from typing import Optional
import os
from e2b_code_interpreter import Sandbox as CodeInterpreter
from pydantic import BaseModel
from utils.logger import logger

# Load E2B API key from environment
E2B_API_KEY = os.getenv("E2B_API_KEY")
if not E2B_API_KEY:
    logger.warning("E2B_API_KEY not found in environment variables")

# Model for code execution request
class CodeExecutionRequest(BaseModel):
    code: str
    language: str
    timeout: Optional[int] = 30000  # Default 30 seconds

# Model for code execution response
class CodeExecutionResponse(BaseModel):
    output: str
    error: Optional[str] = None
    exitCode: int = 0
    images: Optional[list] = None  # List of base64 encoded images

async def execute_code(request: CodeExecutionRequest) -> CodeExecutionResponse:
    """
    Execute code using e2b code interpreter
    """
    try:
        # Create code interpreter session
        interpreter = CodeInterpreter(api_key=E2B_API_KEY)

        # Set timeout
        timeout_seconds = min(request.timeout / 1000, 60)  # Max 60 seconds

        # Execute code based on language
        if request.language.lower() in ["python", "py"]:
            # Modify code to automatically save matplotlib plots
            modified_code = request.code

            # Check if the code contains matplotlib plotting
            if any(keyword in modified_code.lower() for keyword in ['plt.', 'matplotlib', 'pyplot']):
                # Add automatic plot saving
                plot_save_code = """
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import base64
import io

# Store original show function
_original_show = plt.show

def _custom_show():
    # Save the current figure to a bytes buffer
    buf = io.BytesIO()
    plt.savefig(buf, format='png', dpi=150, bbox_inches='tight')
    buf.seek(0)

    # Encode as base64
    img_base64 = base64.b64encode(buf.getvalue()).decode('utf-8')
    buf.close()

    # Print the base64 image with a special marker
    print(f"__E2B_IMAGE_START__{img_base64}__E2B_IMAGE_END__")

    # Clear the figure
    plt.clf()

# Replace plt.show with our custom function
plt.show = _custom_show

"""
                modified_code = plot_save_code + "\n" + modified_code

            # Execute Python code
            execution = interpreter.run_code(modified_code, timeout=timeout_seconds)

            # Extract results
            output = ""
            error = None
            exit_code = 0
            images = []

            # Check for errors
            if execution.error:
                error = f"{execution.error.name}: {execution.error.value}"
                exit_code = 1
            else:
                # Collect output from logs and results
                output_parts = []

                # Get stdout logs
                if execution.logs and execution.logs.stdout:
                    for log_line in execution.logs.stdout:
                        # Check for embedded images
                        if "__E2B_IMAGE_START__" in log_line and "__E2B_IMAGE_END__" in log_line:
                            # Extract base64 image
                            start_marker = "__E2B_IMAGE_START__"
                            end_marker = "__E2B_IMAGE_END__"
                            start_idx = log_line.find(start_marker) + len(start_marker)
                            end_idx = log_line.find(end_marker)
                            if start_idx > len(start_marker) - 1 and end_idx > start_idx:
                                image_b64 = log_line[start_idx:end_idx]
                                images.append(image_b64)
                        else:
                            output_parts.append(log_line)

                # Get results (for things like plots, dataframes, etc.)
                if execution.results:
                    for result in execution.results:
                        if hasattr(result, 'text') and result.text:
                            output_parts.append(result.text)

                # Use the text property as fallback
                if not output_parts and execution.text:
                    output = execution.text
                else:
                    output = "".join(output_parts)

                # If we found images, add them to the output
                if images:
                    output += f"\n\n[Generated {len(images)} plot(s)]"
        else:
            return CodeExecutionResponse(
                output="",
                error=f"Unsupported language: {request.language}",
                exitCode=1,
                images=None
            )

        # Return the result
        return CodeExecutionResponse(
            output=output,
            error=error,
            exitCode=exit_code,
            images=images if images else None
        )
    except Exception as e:
        logger.error(f"Error executing code: {str(e)}")
        # Handle any errors
        return CodeExecutionResponse(
            output="",
            error=str(e),
            exitCode=1,
            images=None
        )
