from typing import Optional, List, Dict, Any
import os
import re
import json
from e2b_code_interpreter import Sandbox as CodeInterpreter
from pydantic import BaseModel
from utils.logger import logger

# Load E2B API key from environment
E2B_API_KEY = os.getenv("E2B_API_KEY")
if not E2B_API_KEY:
    logger.warning("E2B_API_KEY not found in environment variables")

# Model for code execution request
class CodeExecutionRequest(BaseModel):
    code: str
    language: str
    timeout: Optional[int] = 30000  # Default 30 seconds

# Model for code execution response
class CodeExecutionResponse(BaseModel):
    output: str
    error: Optional[str] = None
    exitCode: int = 0
    images: Optional[List[str]] = None  # List of base64 encoded images
    files: Optional[List[Dict[str, Any]]] = None  # List of generated files
    execution_time: Optional[float] = None  # Execution time in seconds

async def execute_code(request: CodeExecutionRequest) -> CodeExecutionResponse:
    """
    Execute code using e2b code interpreter with enhanced image and output handling
    """
    import time
    start_time = time.time()

    try:
        # Create code interpreter session
        interpreter = CodeInterpreter(api_key=E2B_API_KEY)

        # Set timeout
        timeout_seconds = min(request.timeout / 1000, 60)  # Max 60 seconds

        # Execute code based on language
        if request.language.lower() in ["python", "py"]:
            # Enhanced code modification for better matplotlib and output handling
            modified_code = _prepare_python_code(request.code)

            # Execute Python code
            execution = interpreter.run_code(modified_code, timeout=timeout_seconds)

            # Process execution results
            result = _process_execution_results(execution, time.time() - start_time)
            return result
        else:
            return CodeExecutionResponse(
                output="",
                error=f"Unsupported language: {request.language}",
                exitCode=1,
                images=None,
                files=None,
                execution_time=time.time() - start_time
            )

    except Exception as e:
        logger.error(f"Error executing code: {str(e)}", exc_info=True)
        return CodeExecutionResponse(
            output="",
            error=f"Execution failed: {str(e)}",
            exitCode=1,
            images=None,
            files=None,
            execution_time=time.time() - start_time
        )


def _prepare_python_code(code: str) -> str:
    """
    Prepare Python code with enhanced matplotlib and output handling
    """
    # Enhanced plot saving code with better error handling
    plot_save_code = '''
import sys
import os
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import base64
import io
import traceback

# Global list to store images
_generated_images = []

# Store original show function
_original_show = plt.show

def _custom_show(*args, **kwargs):
    """Custom show function that captures matplotlib plots"""
    try:
        # Get current figure
        fig = plt.gcf()
        if fig.get_axes():  # Only save if there are axes (actual plot)
            # Save the current figure to a bytes buffer
            buf = io.BytesIO()
            plt.savefig(buf, format='png', dpi=150, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            buf.seek(0)

            # Encode as base64
            img_base64 = base64.b64encode(buf.getvalue()).decode('utf-8')
            buf.close()

            # Store image and print marker
            _generated_images.append(img_base64)
            print(f"__E2B_IMAGE_START__{img_base64}__E2B_IMAGE_END__")

        # Clear the figure
        plt.clf()
    except Exception as e:
        print(f"Error saving plot: {e}")

# Replace plt.show with our custom function
plt.show = _custom_show

# Also handle automatic plot display for Jupyter-style environments
def _auto_display_plots():
    """Automatically display any remaining plots"""
    try:
        if plt.get_fignums():  # If there are open figures
            _custom_show()
    except:
        pass

# Register cleanup function
import atexit
atexit.register(_auto_display_plots)

'''

    # Check if the code contains matplotlib plotting or other visualization libraries
    needs_plot_handling = any(keyword in code.lower() for keyword in [
        'plt.', 'matplotlib', 'pyplot', 'seaborn', 'sns.', 'plotly', 'bokeh'
    ])

    if needs_plot_handling:
        return plot_save_code + "\n" + code
    else:
        return code


def _process_execution_results(execution, execution_time: float) -> CodeExecutionResponse:
    """
    Process E2B execution results with enhanced error handling and output parsing
    """
    output = ""
    error = None
    exit_code = 0
    images = []
    files = []

    try:
        # Check for errors
        if execution.error:
            error = f"{execution.error.name}: {execution.error.value}"
            exit_code = 1
            logger.warning(f"Code execution error: {error}")
        else:
            # Collect output from various sources
            output_parts = []

            # Process stdout logs
            if execution.logs and execution.logs.stdout:
                for log_line in execution.logs.stdout:
                    if "__E2B_IMAGE_START__" in log_line and "__E2B_IMAGE_END__" in log_line:
                        # Extract base64 image
                        image_b64 = _extract_image_from_log(log_line)
                        if image_b64:
                            images.append(image_b64)
                            logger.info(f"Extracted image of size: {len(image_b64)} characters")
                    else:
                        output_parts.append(log_line)

            # Process stderr logs for warnings (not errors)
            if execution.logs and execution.logs.stderr:
                stderr_content = "\n".join(execution.logs.stderr)
                if stderr_content.strip():
                    # Only include stderr if it's not just matplotlib warnings
                    if not all(any(warning in line.lower() for warning in [
                        'matplotlib', 'warning', 'deprecated', 'futurewarning'
                    ]) for line in execution.logs.stderr):
                        output_parts.append(f"\nWarnings/Errors:\n{stderr_content}")

            # Process execution results (for rich outputs like dataframes, plots, etc.)
            if execution.results:
                for result in execution.results:
                    if hasattr(result, 'text') and result.text:
                        output_parts.append(result.text)
                    elif hasattr(result, 'html') and result.html:
                        # Handle HTML outputs (like pandas dataframes)
                        output_parts.append(f"[HTML Output Generated - {len(result.html)} characters]")
                    elif hasattr(result, 'png') and result.png:
                        # Handle direct PNG outputs
                        images.append(result.png)
                        logger.info("Extracted PNG result")

            # Combine all output
            if output_parts:
                output = "\n".join(output_parts).strip()
            elif execution.text:
                output = execution.text.strip()
            else:
                output = "Code executed successfully (no output)"

            # Add image summary to output
            if images:
                output += f"\n\n✅ Generated {len(images)} visualization(s)"

    except Exception as e:
        logger.error(f"Error processing execution results: {str(e)}", exc_info=True)
        error = f"Error processing results: {str(e)}"
        exit_code = 1

    return CodeExecutionResponse(
        output=output,
        error=error,
        exitCode=exit_code,
        images=images if images else None,
        files=files if files else None,
        execution_time=execution_time
    )


def _extract_image_from_log(log_line: str) -> Optional[str]:
    """
    Extract base64 image from log line with error handling
    """
    try:
        start_marker = "__E2B_IMAGE_START__"
        end_marker = "__E2B_IMAGE_END__"
        start_idx = log_line.find(start_marker)
        end_idx = log_line.find(end_marker)

        if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
            start_idx += len(start_marker)
            image_b64 = log_line[start_idx:end_idx]

            # Validate base64 (basic check)
            if len(image_b64) > 100 and image_b64.replace('+', '').replace('/', '').replace('=', '').isalnum():
                return image_b64

    except Exception as e:
        logger.warning(f"Error extracting image from log: {str(e)}")

    return None
