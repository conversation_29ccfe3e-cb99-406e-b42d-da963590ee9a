"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/color-bits@1.1.0";
exports.ids = ["vendor-chunks/color-bits@1.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/bit.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/bit.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Bitwise functions\n//\n// The color representation would ideally be 32-bits unsigned, but JS bitwise\n// operators only work as 32-bits signed. The range of Smi values on V8 is also\n// 32-bits signed. Those two factors make it that it's much more efficient to just\n// use signed integers to represent the data.\n//\n// Colors with a R channel >= 0x80 will be a negative number, but that's not really\n// an issue at any point because the bits for signed and unsigned integers are always\n// the same, only their interpretation changes.\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.cast = cast;\nexports.get = get;\nexports.set = set;\nconst INT32_TO_UINT32_OFFSET = 2 ** 32;\nfunction cast(n) {\n    if (n < 0) {\n        return n + INT32_TO_UINT32_OFFSET;\n    }\n    return n;\n}\nfunction get(n, offset) {\n    return (n >> offset) & 0xff;\n}\nfunction set(n, offset, byte) {\n    return n ^ ((n ^ (byte << offset)) & (0xff << offset));\n}\n//# sourceMappingURL=bit.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/bit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/convert.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/convert.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n// Copyright 2022 The Chromium Authors. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n//\n// Source: https://github.com/ChromeDevTools/devtools-frontend/blob/c51201e6ee70370f7f1ac8a1a49dca7d4561aeaa/front_end/core/common/ColorConverter.ts\n// License: https://github.com/ChromeDevTools/devtools-frontend/blob/c51201e6ee70370f7f1ac8a1a49dca7d4561aeaa/LICENSE\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.labToXyzd50 = labToXyzd50;\nexports.xyzd50ToLab = xyzd50ToLab;\nexports.oklabToXyzd65 = oklabToXyzd65;\nexports.xyzd65ToOklab = xyzd65ToOklab;\nexports.lchToLab = lchToLab;\nexports.labToLch = labToLch;\nexports.displayP3ToXyzd50 = displayP3ToXyzd50;\nexports.xyzd50ToDisplayP3 = xyzd50ToDisplayP3;\nexports.proPhotoToXyzd50 = proPhotoToXyzd50;\nexports.xyzd50ToProPhoto = xyzd50ToProPhoto;\nexports.adobeRGBToXyzd50 = adobeRGBToXyzd50;\nexports.xyzd50ToAdobeRGB = xyzd50ToAdobeRGB;\nexports.rec2020ToXyzd50 = rec2020ToXyzd50;\nexports.xyzd50ToRec2020 = xyzd50ToRec2020;\nexports.xyzd50ToD65 = xyzd50ToD65;\nexports.xyzd65ToD50 = xyzd65ToD50;\nexports.xyzd65TosRGBLinear = xyzd65TosRGBLinear;\nexports.xyzd50TosRGBLinear = xyzd50TosRGBLinear;\nexports.srgbLinearToXyzd50 = srgbLinearToXyzd50;\nexports.srgbToXyzd50 = srgbToXyzd50;\nexports.xyzd50ToSrgb = xyzd50ToSrgb;\nexports.oklchToXyzd50 = oklchToXyzd50;\nexports.xyzd50ToOklch = xyzd50ToOklch;\n/**\n * Implementation of this module and all the tests are heavily influenced by\n * https://source.chromium.org/chromium/chromium/src/+/main:ui/gfx/color_conversions.cc\n */\n// https://en.wikipedia.org/wiki/CIELAB_color_space#Converting_between_CIELAB_and_CIEXYZ_coordinates\nconst D50_X = 0.9642;\nconst D50_Y = 1.0;\nconst D50_Z = 0.8251;\nfunction multiply(matrix, other) {\n    const dst = [0, 0, 0];\n    for (let row = 0; row < 3; ++row) {\n        dst[row] = matrix[row][0] * other[0] + matrix[row][1] * other[1] +\n            matrix[row][2] * other[2];\n    }\n    return dst;\n}\n// A transfer function mapping encoded values to linear values,\n// represented by this 7-parameter piecewise function:\n//\n//   linear = sign(encoded) *  (c*|encoded| + f)       , 0 <= |encoded| < d\n//          = sign(encoded) * ((a*|encoded| + b)^g + e), d <= |encoded|\n//\n// (A simple gamma transfer function sets g to gamma and a to 1.)\nclass TransferFunction {\n    g;\n    a;\n    b;\n    c;\n    d;\n    e;\n    f;\n    constructor(g, a, b = 0, c = 0, d = 0, e = 0, f = 0) {\n        this.g = g;\n        this.a = a;\n        this.b = b;\n        this.c = c;\n        this.d = d;\n        this.e = e;\n        this.f = f;\n    }\n    eval(val) {\n        const sign = val < 0 ? -1.0 : 1.0;\n        const abs = val * sign;\n        // 0 <= |encoded| < d path\n        if (abs < this.d) {\n            return sign * (this.c * abs + this.f);\n        }\n        // d <= |encoded| path\n        return sign * (Math.pow(this.a * abs + this.b, this.g) + this.e);\n    }\n}\nconst NAMED_TRANSFER_FN = {\n    sRGB: new TransferFunction(2.4, (1 / 1.055), (0.055 / 1.055), (1 / 12.92), 0.04045, 0.0, 0.0),\n    sRGB_INVERSE: new TransferFunction(0.416667, 1.13728, -0, 12.92, 0.0031308, -0.0549698, -0),\n    proPhotoRGB: new TransferFunction(1.8, 1),\n    proPhotoRGB_INVERSE: new TransferFunction(0.555556, 1, -0, 0, 0, 0, 0),\n    k2Dot2: new TransferFunction(2.2, 1.0),\n    k2Dot2_INVERSE: new TransferFunction(0.454545, 1),\n    rec2020: new TransferFunction(2.22222, 0.909672, 0.0903276, 0.222222, 0.0812429, 0, 0),\n    rec2020_INVERSE: new TransferFunction(0.45, 1.23439, -0, 4.5, 0.018054, -0.0993195, -0),\n};\nconst NAMED_GAMUTS = {\n    sRGB: [\n        [0.436065674, 0.385147095, 0.143066406],\n        [0.222488403, 0.716873169, 0.060607910],\n        [0.013916016, 0.097076416, 0.714096069],\n    ],\n    sRGB_INVERSE: [\n        [3.134112151374599, -1.6173924597114966, -0.4906334036481285],\n        [-0.9787872938826594, 1.9162795854799963, 0.0334547139520088],\n        [0.07198304248352326, -0.2289858493321844, 1.4053851325241447],\n    ],\n    displayP3: [\n        [0.515102, 0.291965, 0.157153],\n        [0.241182, 0.692236, 0.0665819],\n        [-0.00104941, 0.0418818, 0.784378],\n    ],\n    displayP3_INVERSE: [\n        [2.404045155982687, -0.9898986932663839, -0.3976317191366333],\n        [-0.8422283799266768, 1.7988505115115485, 0.016048170293157416],\n        [0.04818705979712955, -0.09737385156228891, 1.2735066448052303],\n    ],\n    adobeRGB: [\n        [0.60974, 0.20528, 0.14919],\n        [0.31111, 0.62567, 0.06322],\n        [0.01947, 0.06087, 0.74457],\n    ],\n    adobeRGB_INVERSE: [\n        [1.9625385510109137, -0.6106892546501431, -0.3413827467482388],\n        [-0.9787580455521, 1.9161624707082339, 0.03341676594241408],\n        [0.028696263137883395, -0.1406807819331586, 1.349252109991369],\n    ],\n    rec2020: [\n        [0.673459, 0.165661, 0.125100],\n        [0.279033, 0.675338, 0.0456288],\n        [-0.00193139, 0.0299794, 0.797162],\n    ],\n    rec2020_INVERSE: [\n        [1.647275201661012, -0.3936024771460771, -0.23598028884792507],\n        [-0.6826176165196962, 1.647617775014935, 0.01281626807852422],\n        [0.029662725298529837, -0.06291668721366285, 1.2533964313435522],\n    ],\n    xyz: [\n        [1.0, 0.0, 0.0],\n        [0.0, 1.0, 0.0],\n        [0.0, 0.0, 1.0],\n    ],\n};\nfunction degToRad(deg) {\n    return deg * (Math.PI / 180);\n}\nfunction radToDeg(rad) {\n    return rad * (180 / Math.PI);\n}\nfunction applyTransferFns(fn, r, g, b) {\n    return [fn.eval(r), fn.eval(g), fn.eval(b)];\n}\nconst OKLAB_TO_LMS_MATRIX = [\n    [0.99999999845051981432, 0.39633779217376785678, 0.21580375806075880339],\n    [1.0000000088817607767, -0.1055613423236563494, -0.063854174771705903402],\n    [1.0000000546724109177, -0.089484182094965759684, -1.2914855378640917399],\n];\n// Inverse of the OKLAB_TO_LMS_MATRIX\nconst LMS_TO_OKLAB_MATRIX = [\n    [0.2104542553, 0.7936177849999999, -0.0040720468],\n    [1.9779984951000003, -2.4285922049999997, 0.4505937099000001],\n    [0.025904037099999982, 0.7827717662, -0.8086757660000001],\n];\nconst XYZ_TO_LMS_MATRIX = [\n    [0.8190224432164319, 0.3619062562801221, -0.12887378261216414],\n    [0.0329836671980271, 0.9292868468965546, 0.03614466816999844],\n    [0.048177199566046255, 0.26423952494422764, 0.6335478258136937],\n];\n// Inverse of XYZ_TO_LMS_MATRIX\nconst LMS_TO_XYZ_MATRIX = [\n    [1.226879873374156, -0.5578149965554814, 0.2813910501772159],\n    [-0.040575762624313734, 1.1122868293970596, -0.07171106666151703],\n    [-0.07637294974672144, -0.4214933239627915, 1.586924024427242],\n];\nconst PRO_PHOTO_TO_XYZD50_MATRIX = [\n    [0.7976700747153241, 0.13519395152800417, 0.03135596341127167],\n    [0.28803902352472205, 0.7118744007923554, 0.00008661179538844252],\n    [2.739876695467402e-7, -0.0000014405226518969991, 0.825211112593861],\n];\n// Inverse of PRO_PHOTO_TO_XYZD50_MATRIX\nconst XYZD50_TO_PRO_PHOTO_MATRIX = [\n    [1.3459533710138858, -0.25561367037652133, -0.051116041522131374],\n    [-0.544600415668951, 1.5081687311475767, 0.020535163968720935],\n    [-0.0000013975622054109725, 0.000002717590904589903, 1.2118111696814942],\n];\nconst XYZD65_TO_XYZD50_MATRIX = [\n    [1.0478573189120088, 0.022907374491829943, -0.050162247377152525],\n    [0.029570500050499514, 0.9904755577034089, -0.017061518194840468],\n    [-0.00924047197558879, 0.015052921526981566, 0.7519708530777581],\n];\n// Inverse of XYZD65_TO_XYZD50_MATRIX\nconst XYZD50_TO_XYZD65_MATRIX = [\n    [0.9555366447632887, -0.02306009252137888, 0.06321844147263304],\n    [-0.028315378228764922, 1.009951351591575, 0.021026001591792402],\n    [0.012308773293784308, -0.02050053471777469, 1.3301947294775631],\n];\nconst XYZD65_TO_SRGB_MATRIX = [\n    [3.2408089365140573, -1.5375788839307314, -0.4985609572551541],\n    [-0.9692732213205414, 1.876110235238969, 0.041560501141251774],\n    [0.05567030990267439, -0.2040007921971802, 1.0571046720577026],\n];\nfunction labToXyzd50(l, a, b) {\n    let y = (l + 16.0) / 116.0;\n    let x = y + a / 500.0;\n    let z = y - b / 200.0;\n    function labInverseTransferFunction(t) {\n        const delta = (24.0 / 116.0);\n        if (t <= delta) {\n            return (108.0 / 841.0) * (t - (16.0 / 116.0));\n        }\n        return t * t * t;\n    }\n    x = labInverseTransferFunction(x) * D50_X;\n    y = labInverseTransferFunction(y) * D50_Y;\n    z = labInverseTransferFunction(z) * D50_Z;\n    return [x, y, z];\n}\nfunction xyzd50ToLab(x, y, z) {\n    function labTransferFunction(t) {\n        const deltaLimit = (24.0 / 116.0) * (24.0 / 116.0) * (24.0 / 116.0);\n        if (t <= deltaLimit) {\n            return (841.0 / 108.0) * t + (16.0 / 116.0);\n        }\n        return Math.pow(t, 1.0 / 3.0);\n    }\n    x = labTransferFunction(x / D50_X);\n    y = labTransferFunction(y / D50_Y);\n    z = labTransferFunction(z / D50_Z);\n    const l = 116.0 * y - 16.0;\n    const a = 500.0 * (x - y);\n    const b = 200.0 * (y - z);\n    return [l, a, b];\n}\nfunction oklabToXyzd65(l, a, b) {\n    const labInput = [l, a, b];\n    const lmsIntermediate = multiply(OKLAB_TO_LMS_MATRIX, labInput);\n    lmsIntermediate[0] = lmsIntermediate[0] * lmsIntermediate[0] * lmsIntermediate[0];\n    lmsIntermediate[1] = lmsIntermediate[1] * lmsIntermediate[1] * lmsIntermediate[1];\n    lmsIntermediate[2] = lmsIntermediate[2] * lmsIntermediate[2] * lmsIntermediate[2];\n    const xyzOutput = multiply(LMS_TO_XYZ_MATRIX, lmsIntermediate);\n    return xyzOutput;\n}\nfunction xyzd65ToOklab(x, y, z) {\n    const xyzInput = [x, y, z];\n    const lmsIntermediate = multiply(XYZ_TO_LMS_MATRIX, xyzInput);\n    lmsIntermediate[0] = Math.pow(lmsIntermediate[0], 1.0 / 3.0);\n    lmsIntermediate[1] = Math.pow(lmsIntermediate[1], 1.0 / 3.0);\n    lmsIntermediate[2] = Math.pow(lmsIntermediate[2], 1.0 / 3.0);\n    const labOutput = multiply(LMS_TO_OKLAB_MATRIX, lmsIntermediate);\n    return [labOutput[0], labOutput[1], labOutput[2]];\n}\nfunction lchToLab(l, c, h) {\n    if (h === undefined) {\n        return [l, 0, 0];\n    }\n    return [l, c * Math.cos(degToRad(h)), c * Math.sin(degToRad(h))];\n}\nfunction labToLch(l, a, b) {\n    return [l, Math.sqrt(a * a + b * b), radToDeg(Math.atan2(b, a))];\n}\nfunction displayP3ToXyzd50(r, g, b) {\n    const [mappedR, mappedG, mappedB] = applyTransferFns(NAMED_TRANSFER_FN.sRGB, r, g, b);\n    const rgbInput = [mappedR, mappedG, mappedB];\n    const xyzOutput = multiply(NAMED_GAMUTS.displayP3, rgbInput);\n    return xyzOutput;\n}\nfunction xyzd50ToDisplayP3(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbOutput = multiply(NAMED_GAMUTS.displayP3_INVERSE, xyzInput);\n    return applyTransferFns(NAMED_TRANSFER_FN.sRGB_INVERSE, rgbOutput[0], rgbOutput[1], rgbOutput[2]);\n}\nfunction proPhotoToXyzd50(r, g, b) {\n    const [mappedR, mappedG, mappedB] = applyTransferFns(NAMED_TRANSFER_FN.proPhotoRGB, r, g, b);\n    const rgbInput = [mappedR, mappedG, mappedB];\n    const xyzOutput = multiply(PRO_PHOTO_TO_XYZD50_MATRIX, rgbInput);\n    return xyzOutput;\n}\nfunction xyzd50ToProPhoto(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbOutput = multiply(XYZD50_TO_PRO_PHOTO_MATRIX, xyzInput);\n    return applyTransferFns(NAMED_TRANSFER_FN.proPhotoRGB_INVERSE, rgbOutput[0], rgbOutput[1], rgbOutput[2]);\n}\nfunction adobeRGBToXyzd50(r, g, b) {\n    const [mappedR, mappedG, mappedB] = applyTransferFns(NAMED_TRANSFER_FN.k2Dot2, r, g, b);\n    const rgbInput = [mappedR, mappedG, mappedB];\n    const xyzOutput = multiply(NAMED_GAMUTS.adobeRGB, rgbInput);\n    return xyzOutput;\n}\nfunction xyzd50ToAdobeRGB(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbOutput = multiply(NAMED_GAMUTS.adobeRGB_INVERSE, xyzInput);\n    return applyTransferFns(NAMED_TRANSFER_FN.k2Dot2_INVERSE, rgbOutput[0], rgbOutput[1], rgbOutput[2]);\n}\nfunction rec2020ToXyzd50(r, g, b) {\n    const [mappedR, mappedG, mappedB] = applyTransferFns(NAMED_TRANSFER_FN.rec2020, r, g, b);\n    const rgbInput = [mappedR, mappedG, mappedB];\n    const xyzOutput = multiply(NAMED_GAMUTS.rec2020, rgbInput);\n    return xyzOutput;\n}\nfunction xyzd50ToRec2020(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbOutput = multiply(NAMED_GAMUTS.rec2020_INVERSE, xyzInput);\n    return applyTransferFns(NAMED_TRANSFER_FN.rec2020_INVERSE, rgbOutput[0], rgbOutput[1], rgbOutput[2]);\n}\nfunction xyzd50ToD65(x, y, z) {\n    const xyzInput = [x, y, z];\n    const xyzOutput = multiply(XYZD50_TO_XYZD65_MATRIX, xyzInput);\n    return xyzOutput;\n}\nfunction xyzd65ToD50(x, y, z) {\n    const xyzInput = [x, y, z];\n    const xyzOutput = multiply(XYZD65_TO_XYZD50_MATRIX, xyzInput);\n    return xyzOutput;\n}\nfunction xyzd65TosRGBLinear(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbResult = multiply(XYZD65_TO_SRGB_MATRIX, xyzInput);\n    return rgbResult;\n}\nfunction xyzd50TosRGBLinear(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbResult = multiply(NAMED_GAMUTS.sRGB_INVERSE, xyzInput);\n    return rgbResult;\n}\nfunction srgbLinearToXyzd50(r, g, b) {\n    const rgbInput = [r, g, b];\n    const xyzOutput = multiply(NAMED_GAMUTS.sRGB, rgbInput);\n    return xyzOutput;\n}\nfunction srgbToXyzd50(r, g, b) {\n    const [mappedR, mappedG, mappedB] = applyTransferFns(NAMED_TRANSFER_FN.sRGB, r, g, b);\n    const rgbInput = [mappedR, mappedG, mappedB];\n    const xyzOutput = multiply(NAMED_GAMUTS.sRGB, rgbInput);\n    return xyzOutput;\n}\nfunction xyzd50ToSrgb(x, y, z) {\n    const xyzInput = [x, y, z];\n    const rgbOutput = multiply(NAMED_GAMUTS.sRGB_INVERSE, xyzInput);\n    return applyTransferFns(NAMED_TRANSFER_FN.sRGB_INVERSE, rgbOutput[0], rgbOutput[1], rgbOutput[2]);\n}\nfunction oklchToXyzd50(lInput, c, h) {\n    const [l, a, b] = lchToLab(lInput, c, h);\n    const [x65, y65, z65] = oklabToXyzd65(l, a, b);\n    return xyzd65ToD50(x65, y65, z65);\n}\nfunction xyzd50ToOklch(x, y, z) {\n    const [x65, y65, z65] = xyzd50ToD65(x, y, z);\n    const [l, a, b] = xyzd65ToOklab(x65, y65, z65);\n    return labToLch(l, a, b);\n}\n//# sourceMappingURL=convert.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vY29sb3ItYml0c0AxLjEuMC9ub2RlX21vZHVsZXMvY29sb3ItYml0cy9idWlsZC9jb252ZXJ0LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELG1CQUFtQjtBQUNuQixtQkFBbUI7QUFDbkIscUJBQXFCO0FBQ3JCLHFCQUFxQjtBQUNyQixnQkFBZ0I7QUFDaEIsZ0JBQWdCO0FBQ2hCLHlCQUF5QjtBQUN6Qix5QkFBeUI7QUFDekIsd0JBQXdCO0FBQ3hCLHdCQUF3QjtBQUN4Qix3QkFBd0I7QUFDeEIsd0JBQXdCO0FBQ3hCLHVCQUF1QjtBQUN2Qix1QkFBdUI7QUFDdkIsbUJBQW1CO0FBQ25CLG1CQUFtQjtBQUNuQiwwQkFBMEI7QUFDMUIsMEJBQTBCO0FBQzFCLDBCQUEwQjtBQUMxQixvQkFBb0I7QUFDcEIsb0JBQW9CO0FBQ3BCLHFCQUFxQjtBQUNyQixxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsU0FBUztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFuZHJlXFxPbmVEcml2ZVxcRGVza3RvcFxcR2l0aHViIFJlcG9zaXRvcmllc1xcZGVtb1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGNvbG9yLWJpdHNAMS4xLjBcXG5vZGVfbW9kdWxlc1xcY29sb3ItYml0c1xcYnVpbGRcXGNvbnZlcnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vLyBDb3B5cmlnaHQgMjAyMiBUaGUgQ2hyb21pdW0gQXV0aG9ycy4gQWxsIHJpZ2h0cyByZXNlcnZlZC5cbi8vIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGEgQlNELXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbi8vIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUuXG4vL1xuLy8gU291cmNlOiBodHRwczovL2dpdGh1Yi5jb20vQ2hyb21lRGV2VG9vbHMvZGV2dG9vbHMtZnJvbnRlbmQvYmxvYi9jNTEyMDFlNmVlNzAzNzBmN2YxYWM4YTFhNDlkY2E3ZDQ1NjFhZWFhL2Zyb250X2VuZC9jb3JlL2NvbW1vbi9Db2xvckNvbnZlcnRlci50c1xuLy8gTGljZW5zZTogaHR0cHM6Ly9naXRodWIuY29tL0Nocm9tZURldlRvb2xzL2RldnRvb2xzLWZyb250ZW5kL2Jsb2IvYzUxMjAxZTZlZTcwMzcwZjdmMWFjOGExYTQ5ZGNhN2Q0NTYxYWVhYS9MSUNFTlNFXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmxhYlRvWHl6ZDUwID0gbGFiVG9YeXpkNTA7XG5leHBvcnRzLnh5emQ1MFRvTGFiID0geHl6ZDUwVG9MYWI7XG5leHBvcnRzLm9rbGFiVG9YeXpkNjUgPSBva2xhYlRvWHl6ZDY1O1xuZXhwb3J0cy54eXpkNjVUb09rbGFiID0geHl6ZDY1VG9Pa2xhYjtcbmV4cG9ydHMubGNoVG9MYWIgPSBsY2hUb0xhYjtcbmV4cG9ydHMubGFiVG9MY2ggPSBsYWJUb0xjaDtcbmV4cG9ydHMuZGlzcGxheVAzVG9YeXpkNTAgPSBkaXNwbGF5UDNUb1h5emQ1MDtcbmV4cG9ydHMueHl6ZDUwVG9EaXNwbGF5UDMgPSB4eXpkNTBUb0Rpc3BsYXlQMztcbmV4cG9ydHMucHJvUGhvdG9Ub1h5emQ1MCA9IHByb1Bob3RvVG9YeXpkNTA7XG5leHBvcnRzLnh5emQ1MFRvUHJvUGhvdG8gPSB4eXpkNTBUb1Byb1Bob3RvO1xuZXhwb3J0cy5hZG9iZVJHQlRvWHl6ZDUwID0gYWRvYmVSR0JUb1h5emQ1MDtcbmV4cG9ydHMueHl6ZDUwVG9BZG9iZVJHQiA9IHh5emQ1MFRvQWRvYmVSR0I7XG5leHBvcnRzLnJlYzIwMjBUb1h5emQ1MCA9IHJlYzIwMjBUb1h5emQ1MDtcbmV4cG9ydHMueHl6ZDUwVG9SZWMyMDIwID0geHl6ZDUwVG9SZWMyMDIwO1xuZXhwb3J0cy54eXpkNTBUb0Q2NSA9IHh5emQ1MFRvRDY1O1xuZXhwb3J0cy54eXpkNjVUb0Q1MCA9IHh5emQ2NVRvRDUwO1xuZXhwb3J0cy54eXpkNjVUb3NSR0JMaW5lYXIgPSB4eXpkNjVUb3NSR0JMaW5lYXI7XG5leHBvcnRzLnh5emQ1MFRvc1JHQkxpbmVhciA9IHh5emQ1MFRvc1JHQkxpbmVhcjtcbmV4cG9ydHMuc3JnYkxpbmVhclRvWHl6ZDUwID0gc3JnYkxpbmVhclRvWHl6ZDUwO1xuZXhwb3J0cy5zcmdiVG9YeXpkNTAgPSBzcmdiVG9YeXpkNTA7XG5leHBvcnRzLnh5emQ1MFRvU3JnYiA9IHh5emQ1MFRvU3JnYjtcbmV4cG9ydHMub2tsY2hUb1h5emQ1MCA9IG9rbGNoVG9YeXpkNTA7XG5leHBvcnRzLnh5emQ1MFRvT2tsY2ggPSB4eXpkNTBUb09rbGNoO1xuLyoqXG4gKiBJbXBsZW1lbnRhdGlvbiBvZiB0aGlzIG1vZHVsZSBhbmQgYWxsIHRoZSB0ZXN0cyBhcmUgaGVhdmlseSBpbmZsdWVuY2VkIGJ5XG4gKiBodHRwczovL3NvdXJjZS5jaHJvbWl1bS5vcmcvY2hyb21pdW0vY2hyb21pdW0vc3JjLysvbWFpbjp1aS9nZngvY29sb3JfY29udmVyc2lvbnMuY2NcbiAqL1xuLy8gaHR0cHM6Ly9lbi53aWtpcGVkaWEub3JnL3dpa2kvQ0lFTEFCX2NvbG9yX3NwYWNlI0NvbnZlcnRpbmdfYmV0d2Vlbl9DSUVMQUJfYW5kX0NJRVhZWl9jb29yZGluYXRlc1xuY29uc3QgRDUwX1ggPSAwLjk2NDI7XG5jb25zdCBENTBfWSA9IDEuMDtcbmNvbnN0IEQ1MF9aID0gMC44MjUxO1xuZnVuY3Rpb24gbXVsdGlwbHkobWF0cml4LCBvdGhlcikge1xuICAgIGNvbnN0IGRzdCA9IFswLCAwLCAwXTtcbiAgICBmb3IgKGxldCByb3cgPSAwOyByb3cgPCAzOyArK3Jvdykge1xuICAgICAgICBkc3Rbcm93XSA9IG1hdHJpeFtyb3ddWzBdICogb3RoZXJbMF0gKyBtYXRyaXhbcm93XVsxXSAqIG90aGVyWzFdICtcbiAgICAgICAgICAgIG1hdHJpeFtyb3ddWzJdICogb3RoZXJbMl07XG4gICAgfVxuICAgIHJldHVybiBkc3Q7XG59XG4vLyBBIHRyYW5zZmVyIGZ1bmN0aW9uIG1hcHBpbmcgZW5jb2RlZCB2YWx1ZXMgdG8gbGluZWFyIHZhbHVlcyxcbi8vIHJlcHJlc2VudGVkIGJ5IHRoaXMgNy1wYXJhbWV0ZXIgcGllY2V3aXNlIGZ1bmN0aW9uOlxuLy9cbi8vICAgbGluZWFyID0gc2lnbihlbmNvZGVkKSAqICAoYyp8ZW5jb2RlZHwgKyBmKSAgICAgICAsIDAgPD0gfGVuY29kZWR8IDwgZFxuLy8gICAgICAgICAgPSBzaWduKGVuY29kZWQpICogKChhKnxlbmNvZGVkfCArIGIpXmcgKyBlKSwgZCA8PSB8ZW5jb2RlZHxcbi8vXG4vLyAoQSBzaW1wbGUgZ2FtbWEgdHJhbnNmZXIgZnVuY3Rpb24gc2V0cyBnIHRvIGdhbW1hIGFuZCBhIHRvIDEuKVxuY2xhc3MgVHJhbnNmZXJGdW5jdGlvbiB7XG4gICAgZztcbiAgICBhO1xuICAgIGI7XG4gICAgYztcbiAgICBkO1xuICAgIGU7XG4gICAgZjtcbiAgICBjb25zdHJ1Y3RvcihnLCBhLCBiID0gMCwgYyA9IDAsIGQgPSAwLCBlID0gMCwgZiA9IDApIHtcbiAgICAgICAgdGhpcy5nID0gZztcbiAgICAgICAgdGhpcy5hID0gYTtcbiAgICAgICAgdGhpcy5iID0gYjtcbiAgICAgICAgdGhpcy5jID0gYztcbiAgICAgICAgdGhpcy5kID0gZDtcbiAgICAgICAgdGhpcy5lID0gZTtcbiAgICAgICAgdGhpcy5mID0gZjtcbiAgICB9XG4gICAgZXZhbCh2YWwpIHtcbiAgICAgICAgY29uc3Qgc2lnbiA9IHZhbCA8IDAgPyAtMS4wIDogMS4wO1xuICAgICAgICBjb25zdCBhYnMgPSB2YWwgKiBzaWduO1xuICAgICAgICAvLyAwIDw9IHxlbmNvZGVkfCA8IGQgcGF0aFxuICAgICAgICBpZiAoYWJzIDwgdGhpcy5kKSB7XG4gICAgICAgICAgICByZXR1cm4gc2lnbiAqICh0aGlzLmMgKiBhYnMgKyB0aGlzLmYpO1xuICAgICAgICB9XG4gICAgICAgIC8vIGQgPD0gfGVuY29kZWR8IHBhdGhcbiAgICAgICAgcmV0dXJuIHNpZ24gKiAoTWF0aC5wb3codGhpcy5hICogYWJzICsgdGhpcy5iLCB0aGlzLmcpICsgdGhpcy5lKTtcbiAgICB9XG59XG5jb25zdCBOQU1FRF9UUkFOU0ZFUl9GTiA9IHtcbiAgICBzUkdCOiBuZXcgVHJhbnNmZXJGdW5jdGlvbigyLjQsICgxIC8gMS4wNTUpLCAoMC4wNTUgLyAxLjA1NSksICgxIC8gMTIuOTIpLCAwLjA0MDQ1LCAwLjAsIDAuMCksXG4gICAgc1JHQl9JTlZFUlNFOiBuZXcgVHJhbnNmZXJGdW5jdGlvbigwLjQxNjY2NywgMS4xMzcyOCwgLTAsIDEyLjkyLCAwLjAwMzEzMDgsIC0wLjA1NDk2OTgsIC0wKSxcbiAgICBwcm9QaG90b1JHQjogbmV3IFRyYW5zZmVyRnVuY3Rpb24oMS44LCAxKSxcbiAgICBwcm9QaG90b1JHQl9JTlZFUlNFOiBuZXcgVHJhbnNmZXJGdW5jdGlvbigwLjU1NTU1NiwgMSwgLTAsIDAsIDAsIDAsIDApLFxuICAgIGsyRG90MjogbmV3IFRyYW5zZmVyRnVuY3Rpb24oMi4yLCAxLjApLFxuICAgIGsyRG90Ml9JTlZFUlNFOiBuZXcgVHJhbnNmZXJGdW5jdGlvbigwLjQ1NDU0NSwgMSksXG4gICAgcmVjMjAyMDogbmV3IFRyYW5zZmVyRnVuY3Rpb24oMi4yMjIyMiwgMC45MDk2NzIsIDAuMDkwMzI3NiwgMC4yMjIyMjIsIDAuMDgxMjQyOSwgMCwgMCksXG4gICAgcmVjMjAyMF9JTlZFUlNFOiBuZXcgVHJhbnNmZXJGdW5jdGlvbigwLjQ1LCAxLjIzNDM5LCAtMCwgNC41LCAwLjAxODA1NCwgLTAuMDk5MzE5NSwgLTApLFxufTtcbmNvbnN0IE5BTUVEX0dBTVVUUyA9IHtcbiAgICBzUkdCOiBbXG4gICAgICAgIFswLjQzNjA2NTY3NCwgMC4zODUxNDcwOTUsIDAuMTQzMDY2NDA2XSxcbiAgICAgICAgWzAuMjIyNDg4NDAzLCAwLjcxNjg3MzE2OSwgMC4wNjA2MDc5MTBdLFxuICAgICAgICBbMC4wMTM5MTYwMTYsIDAuMDk3MDc2NDE2LCAwLjcxNDA5NjA2OV0sXG4gICAgXSxcbiAgICBzUkdCX0lOVkVSU0U6IFtcbiAgICAgICAgWzMuMTM0MTEyMTUxMzc0NTk5LCAtMS42MTczOTI0NTk3MTE0OTY2LCAtMC40OTA2MzM0MDM2NDgxMjg1XSxcbiAgICAgICAgWy0wLjk3ODc4NzI5Mzg4MjY1OTQsIDEuOTE2Mjc5NTg1NDc5OTk2MywgMC4wMzM0NTQ3MTM5NTIwMDg4XSxcbiAgICAgICAgWzAuMDcxOTgzMDQyNDgzNTIzMjYsIC0wLjIyODk4NTg0OTMzMjE4NDQsIDEuNDA1Mzg1MTMyNTI0MTQ0N10sXG4gICAgXSxcbiAgICBkaXNwbGF5UDM6IFtcbiAgICAgICAgWzAuNTE1MTAyLCAwLjI5MTk2NSwgMC4xNTcxNTNdLFxuICAgICAgICBbMC4yNDExODIsIDAuNjkyMjM2LCAwLjA2NjU4MTldLFxuICAgICAgICBbLTAuMDAxMDQ5NDEsIDAuMDQxODgxOCwgMC43ODQzNzhdLFxuICAgIF0sXG4gICAgZGlzcGxheVAzX0lOVkVSU0U6IFtcbiAgICAgICAgWzIuNDA0MDQ1MTU1OTgyNjg3LCAtMC45ODk4OTg2OTMyNjYzODM5LCAtMC4zOTc2MzE3MTkxMzY2MzMzXSxcbiAgICAgICAgWy0wLjg0MjIyODM3OTkyNjY3NjgsIDEuNzk4ODUwNTExNTExNTQ4NSwgMC4wMTYwNDgxNzAyOTMxNTc0MTZdLFxuICAgICAgICBbMC4wNDgxODcwNTk3OTcxMjk1NSwgLTAuMDk3MzczODUxNTYyMjg4OTEsIDEuMjczNTA2NjQ0ODA1MjMwM10sXG4gICAgXSxcbiAgICBhZG9iZVJHQjogW1xuICAgICAgICBbMC42MDk3NCwgMC4yMDUyOCwgMC4xNDkxOV0sXG4gICAgICAgIFswLjMxMTExLCAwLjYyNTY3LCAwLjA2MzIyXSxcbiAgICAgICAgWzAuMDE5NDcsIDAuMDYwODcsIDAuNzQ0NTddLFxuICAgIF0sXG4gICAgYWRvYmVSR0JfSU5WRVJTRTogW1xuICAgICAgICBbMS45NjI1Mzg1NTEwMTA5MTM3LCAtMC42MTA2ODkyNTQ2NTAxNDMxLCAtMC4zNDEzODI3NDY3NDgyMzg4XSxcbiAgICAgICAgWy0wLjk3ODc1ODA0NTU1MjEsIDEuOTE2MTYyNDcwNzA4MjMzOSwgMC4wMzM0MTY3NjU5NDI0MTQwOF0sXG4gICAgICAgIFswLjAyODY5NjI2MzEzNzg4MzM5NSwgLTAuMTQwNjgwNzgxOTMzMTU4NiwgMS4zNDkyNTIxMDk5OTEzNjldLFxuICAgIF0sXG4gICAgcmVjMjAyMDogW1xuICAgICAgICBbMC42NzM0NTksIDAuMTY1NjYxLCAwLjEyNTEwMF0sXG4gICAgICAgIFswLjI3OTAzMywgMC42NzUzMzgsIDAuMDQ1NjI4OF0sXG4gICAgICAgIFstMC4wMDE5MzEzOSwgMC4wMjk5Nzk0LCAwLjc5NzE2Ml0sXG4gICAgXSxcbiAgICByZWMyMDIwX0lOVkVSU0U6IFtcbiAgICAgICAgWzEuNjQ3Mjc1MjAxNjYxMDEyLCAtMC4zOTM2MDI0NzcxNDYwNzcxLCAtMC4yMzU5ODAyODg4NDc5MjUwN10sXG4gICAgICAgIFstMC42ODI2MTc2MTY1MTk2OTYyLCAxLjY0NzYxNzc3NTAxNDkzNSwgMC4wMTI4MTYyNjgwNzg1MjQyMl0sXG4gICAgICAgIFswLjAyOTY2MjcyNTI5ODUyOTgzNywgLTAuMDYyOTE2Njg3MjEzNjYyODUsIDEuMjUzMzk2NDMxMzQzNTUyMl0sXG4gICAgXSxcbiAgICB4eXo6IFtcbiAgICAgICAgWzEuMCwgMC4wLCAwLjBdLFxuICAgICAgICBbMC4wLCAxLjAsIDAuMF0sXG4gICAgICAgIFswLjAsIDAuMCwgMS4wXSxcbiAgICBdLFxufTtcbmZ1bmN0aW9uIGRlZ1RvUmFkKGRlZykge1xuICAgIHJldHVybiBkZWcgKiAoTWF0aC5QSSAvIDE4MCk7XG59XG5mdW5jdGlvbiByYWRUb0RlZyhyYWQpIHtcbiAgICByZXR1cm4gcmFkICogKDE4MCAvIE1hdGguUEkpO1xufVxuZnVuY3Rpb24gYXBwbHlUcmFuc2ZlckZucyhmbiwgciwgZywgYikge1xuICAgIHJldHVybiBbZm4uZXZhbChyKSwgZm4uZXZhbChnKSwgZm4uZXZhbChiKV07XG59XG5jb25zdCBPS0xBQl9UT19MTVNfTUFUUklYID0gW1xuICAgIFswLjk5OTk5OTk5ODQ1MDUxOTgxNDMyLCAwLjM5NjMzNzc5MjE3Mzc2Nzg1Njc4LCAwLjIxNTgwMzc1ODA2MDc1ODgwMzM5XSxcbiAgICBbMS4wMDAwMDAwMDg4ODE3NjA3NzY3LCAtMC4xMDU1NjEzNDIzMjM2NTYzNDk0LCAtMC4wNjM4NTQxNzQ3NzE3MDU5MDM0MDJdLFxuICAgIFsxLjAwMDAwMDA1NDY3MjQxMDkxNzcsIC0wLjA4OTQ4NDE4MjA5NDk2NTc1OTY4NCwgLTEuMjkxNDg1NTM3ODY0MDkxNzM5OV0sXG5dO1xuLy8gSW52ZXJzZSBvZiB0aGUgT0tMQUJfVE9fTE1TX01BVFJJWFxuY29uc3QgTE1TX1RPX09LTEFCX01BVFJJWCA9IFtcbiAgICBbMC4yMTA0NTQyNTUzLCAwLjc5MzYxNzc4NDk5OTk5OTksIC0wLjAwNDA3MjA0NjhdLFxuICAgIFsxLjk3Nzk5ODQ5NTEwMDAwMDMsIC0yLjQyODU5MjIwNDk5OTk5OTcsIDAuNDUwNTkzNzA5OTAwMDAwMV0sXG4gICAgWzAuMDI1OTA0MDM3MDk5OTk5OTgyLCAwLjc4Mjc3MTc2NjIsIC0wLjgwODY3NTc2NjAwMDAwMDFdLFxuXTtcbmNvbnN0IFhZWl9UT19MTVNfTUFUUklYID0gW1xuICAgIFswLjgxOTAyMjQ0MzIxNjQzMTksIDAuMzYxOTA2MjU2MjgwMTIyMSwgLTAuMTI4ODczNzgyNjEyMTY0MTRdLFxuICAgIFswLjAzMjk4MzY2NzE5ODAyNzEsIDAuOTI5Mjg2ODQ2ODk2NTU0NiwgMC4wMzYxNDQ2NjgxNjk5OTg0NF0sXG4gICAgWzAuMDQ4MTc3MTk5NTY2MDQ2MjU1LCAwLjI2NDIzOTUyNDk0NDIyNzY0LCAwLjYzMzU0NzgyNTgxMzY5MzddLFxuXTtcbi8vIEludmVyc2Ugb2YgWFlaX1RPX0xNU19NQVRSSVhcbmNvbnN0IExNU19UT19YWVpfTUFUUklYID0gW1xuICAgIFsxLjIyNjg3OTg3MzM3NDE1NiwgLTAuNTU3ODE0OTk2NTU1NDgxNCwgMC4yODEzOTEwNTAxNzcyMTU5XSxcbiAgICBbLTAuMDQwNTc1NzYyNjI0MzEzNzM0LCAxLjExMjI4NjgyOTM5NzA1OTYsIC0wLjA3MTcxMTA2NjY2MTUxNzAzXSxcbiAgICBbLTAuMDc2MzcyOTQ5NzQ2NzIxNDQsIC0wLjQyMTQ5MzMyMzk2Mjc5MTUsIDEuNTg2OTI0MDI0NDI3MjQyXSxcbl07XG5jb25zdCBQUk9fUEhPVE9fVE9fWFlaRDUwX01BVFJJWCA9IFtcbiAgICBbMC43OTc2NzAwNzQ3MTUzMjQxLCAwLjEzNTE5Mzk1MTUyODAwNDE3LCAwLjAzMTM1NTk2MzQxMTI3MTY3XSxcbiAgICBbMC4yODgwMzkwMjM1MjQ3MjIwNSwgMC43MTE4NzQ0MDA3OTIzNTU0LCAwLjAwMDA4NjYxMTc5NTM4ODQ0MjUyXSxcbiAgICBbMi43Mzk4NzY2OTU0Njc0MDJlLTcsIC0wLjAwMDAwMTQ0MDUyMjY1MTg5Njk5OTEsIDAuODI1MjExMTEyNTkzODYxXSxcbl07XG4vLyBJbnZlcnNlIG9mIFBST19QSE9UT19UT19YWVpENTBfTUFUUklYXG5jb25zdCBYWVpENTBfVE9fUFJPX1BIT1RPX01BVFJJWCA9IFtcbiAgICBbMS4zNDU5NTMzNzEwMTM4ODU4LCAtMC4yNTU2MTM2NzAzNzY1MjEzMywgLTAuMDUxMTE2MDQxNTIyMTMxMzc0XSxcbiAgICBbLTAuNTQ0NjAwNDE1NjY4OTUxLCAxLjUwODE2ODczMTE0NzU3NjcsIDAuMDIwNTM1MTYzOTY4NzIwOTM1XSxcbiAgICBbLTAuMDAwMDAxMzk3NTYyMjA1NDEwOTcyNSwgMC4wMDAwMDI3MTc1OTA5MDQ1ODk5MDMsIDEuMjExODExMTY5NjgxNDk0Ml0sXG5dO1xuY29uc3QgWFlaRDY1X1RPX1hZWkQ1MF9NQVRSSVggPSBbXG4gICAgWzEuMDQ3ODU3MzE4OTEyMDA4OCwgMC4wMjI5MDczNzQ0OTE4Mjk5NDMsIC0wLjA1MDE2MjI0NzM3NzE1MjUyNV0sXG4gICAgWzAuMDI5NTcwNTAwMDUwNDk5NTE0LCAwLjk5MDQ3NTU1NzcwMzQwODksIC0wLjAxNzA2MTUxODE5NDg0MDQ2OF0sXG4gICAgWy0wLjAwOTI0MDQ3MTk3NTU4ODc5LCAwLjAxNTA1MjkyMTUyNjk4MTU2NiwgMC43NTE5NzA4NTMwNzc3NTgxXSxcbl07XG4vLyBJbnZlcnNlIG9mIFhZWkQ2NV9UT19YWVpENTBfTUFUUklYXG5jb25zdCBYWVpENTBfVE9fWFlaRDY1X01BVFJJWCA9IFtcbiAgICBbMC45NTU1MzY2NDQ3NjMyODg3LCAtMC4wMjMwNjAwOTI1MjEzNzg4OCwgMC4wNjMyMTg0NDE0NzI2MzMwNF0sXG4gICAgWy0wLjAyODMxNTM3ODIyODc2NDkyMiwgMS4wMDk5NTEzNTE1OTE1NzUsIDAuMDIxMDI2MDAxNTkxNzkyNDAyXSxcbiAgICBbMC4wMTIzMDg3NzMyOTM3ODQzMDgsIC0wLjAyMDUwMDUzNDcxNzc3NDY5LCAxLjMzMDE5NDcyOTQ3NzU2MzFdLFxuXTtcbmNvbnN0IFhZWkQ2NV9UT19TUkdCX01BVFJJWCA9IFtcbiAgICBbMy4yNDA4MDg5MzY1MTQwNTczLCAtMS41Mzc1Nzg4ODM5MzA3MzE0LCAtMC40OTg1NjA5NTcyNTUxNTQxXSxcbiAgICBbLTAuOTY5MjczMjIxMzIwNTQxNCwgMS44NzYxMTAyMzUyMzg5NjksIDAuMDQxNTYwNTAxMTQxMjUxNzc0XSxcbiAgICBbMC4wNTU2NzAzMDk5MDI2NzQzOSwgLTAuMjA0MDAwNzkyMTk3MTgwMiwgMS4wNTcxMDQ2NzIwNTc3MDI2XSxcbl07XG5mdW5jdGlvbiBsYWJUb1h5emQ1MChsLCBhLCBiKSB7XG4gICAgbGV0IHkgPSAobCArIDE2LjApIC8gMTE2LjA7XG4gICAgbGV0IHggPSB5ICsgYSAvIDUwMC4wO1xuICAgIGxldCB6ID0geSAtIGIgLyAyMDAuMDtcbiAgICBmdW5jdGlvbiBsYWJJbnZlcnNlVHJhbnNmZXJGdW5jdGlvbih0KSB7XG4gICAgICAgIGNvbnN0IGRlbHRhID0gKDI0LjAgLyAxMTYuMCk7XG4gICAgICAgIGlmICh0IDw9IGRlbHRhKSB7XG4gICAgICAgICAgICByZXR1cm4gKDEwOC4wIC8gODQxLjApICogKHQgLSAoMTYuMCAvIDExNi4wKSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHQgKiB0ICogdDtcbiAgICB9XG4gICAgeCA9IGxhYkludmVyc2VUcmFuc2ZlckZ1bmN0aW9uKHgpICogRDUwX1g7XG4gICAgeSA9IGxhYkludmVyc2VUcmFuc2ZlckZ1bmN0aW9uKHkpICogRDUwX1k7XG4gICAgeiA9IGxhYkludmVyc2VUcmFuc2ZlckZ1bmN0aW9uKHopICogRDUwX1o7XG4gICAgcmV0dXJuIFt4LCB5LCB6XTtcbn1cbmZ1bmN0aW9uIHh5emQ1MFRvTGFiKHgsIHksIHopIHtcbiAgICBmdW5jdGlvbiBsYWJUcmFuc2ZlckZ1bmN0aW9uKHQpIHtcbiAgICAgICAgY29uc3QgZGVsdGFMaW1pdCA9ICgyNC4wIC8gMTE2LjApICogKDI0LjAgLyAxMTYuMCkgKiAoMjQuMCAvIDExNi4wKTtcbiAgICAgICAgaWYgKHQgPD0gZGVsdGFMaW1pdCkge1xuICAgICAgICAgICAgcmV0dXJuICg4NDEuMCAvIDEwOC4wKSAqIHQgKyAoMTYuMCAvIDExNi4wKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gTWF0aC5wb3codCwgMS4wIC8gMy4wKTtcbiAgICB9XG4gICAgeCA9IGxhYlRyYW5zZmVyRnVuY3Rpb24oeCAvIEQ1MF9YKTtcbiAgICB5ID0gbGFiVHJhbnNmZXJGdW5jdGlvbih5IC8gRDUwX1kpO1xuICAgIHogPSBsYWJUcmFuc2ZlckZ1bmN0aW9uKHogLyBENTBfWik7XG4gICAgY29uc3QgbCA9IDExNi4wICogeSAtIDE2LjA7XG4gICAgY29uc3QgYSA9IDUwMC4wICogKHggLSB5KTtcbiAgICBjb25zdCBiID0gMjAwLjAgKiAoeSAtIHopO1xuICAgIHJldHVybiBbbCwgYSwgYl07XG59XG5mdW5jdGlvbiBva2xhYlRvWHl6ZDY1KGwsIGEsIGIpIHtcbiAgICBjb25zdCBsYWJJbnB1dCA9IFtsLCBhLCBiXTtcbiAgICBjb25zdCBsbXNJbnRlcm1lZGlhdGUgPSBtdWx0aXBseShPS0xBQl9UT19MTVNfTUFUUklYLCBsYWJJbnB1dCk7XG4gICAgbG1zSW50ZXJtZWRpYXRlWzBdID0gbG1zSW50ZXJtZWRpYXRlWzBdICogbG1zSW50ZXJtZWRpYXRlWzBdICogbG1zSW50ZXJtZWRpYXRlWzBdO1xuICAgIGxtc0ludGVybWVkaWF0ZVsxXSA9IGxtc0ludGVybWVkaWF0ZVsxXSAqIGxtc0ludGVybWVkaWF0ZVsxXSAqIGxtc0ludGVybWVkaWF0ZVsxXTtcbiAgICBsbXNJbnRlcm1lZGlhdGVbMl0gPSBsbXNJbnRlcm1lZGlhdGVbMl0gKiBsbXNJbnRlcm1lZGlhdGVbMl0gKiBsbXNJbnRlcm1lZGlhdGVbMl07XG4gICAgY29uc3QgeHl6T3V0cHV0ID0gbXVsdGlwbHkoTE1TX1RPX1hZWl9NQVRSSVgsIGxtc0ludGVybWVkaWF0ZSk7XG4gICAgcmV0dXJuIHh5ek91dHB1dDtcbn1cbmZ1bmN0aW9uIHh5emQ2NVRvT2tsYWIoeCwgeSwgeikge1xuICAgIGNvbnN0IHh5eklucHV0ID0gW3gsIHksIHpdO1xuICAgIGNvbnN0IGxtc0ludGVybWVkaWF0ZSA9IG11bHRpcGx5KFhZWl9UT19MTVNfTUFUUklYLCB4eXpJbnB1dCk7XG4gICAgbG1zSW50ZXJtZWRpYXRlWzBdID0gTWF0aC5wb3cobG1zSW50ZXJtZWRpYXRlWzBdLCAxLjAgLyAzLjApO1xuICAgIGxtc0ludGVybWVkaWF0ZVsxXSA9IE1hdGgucG93KGxtc0ludGVybWVkaWF0ZVsxXSwgMS4wIC8gMy4wKTtcbiAgICBsbXNJbnRlcm1lZGlhdGVbMl0gPSBNYXRoLnBvdyhsbXNJbnRlcm1lZGlhdGVbMl0sIDEuMCAvIDMuMCk7XG4gICAgY29uc3QgbGFiT3V0cHV0ID0gbXVsdGlwbHkoTE1TX1RPX09LTEFCX01BVFJJWCwgbG1zSW50ZXJtZWRpYXRlKTtcbiAgICByZXR1cm4gW2xhYk91dHB1dFswXSwgbGFiT3V0cHV0WzFdLCBsYWJPdXRwdXRbMl1dO1xufVxuZnVuY3Rpb24gbGNoVG9MYWIobCwgYywgaCkge1xuICAgIGlmIChoID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcmV0dXJuIFtsLCAwLCAwXTtcbiAgICB9XG4gICAgcmV0dXJuIFtsLCBjICogTWF0aC5jb3MoZGVnVG9SYWQoaCkpLCBjICogTWF0aC5zaW4oZGVnVG9SYWQoaCkpXTtcbn1cbmZ1bmN0aW9uIGxhYlRvTGNoKGwsIGEsIGIpIHtcbiAgICByZXR1cm4gW2wsIE1hdGguc3FydChhICogYSArIGIgKiBiKSwgcmFkVG9EZWcoTWF0aC5hdGFuMihiLCBhKSldO1xufVxuZnVuY3Rpb24gZGlzcGxheVAzVG9YeXpkNTAociwgZywgYikge1xuICAgIGNvbnN0IFttYXBwZWRSLCBtYXBwZWRHLCBtYXBwZWRCXSA9IGFwcGx5VHJhbnNmZXJGbnMoTkFNRURfVFJBTlNGRVJfRk4uc1JHQiwgciwgZywgYik7XG4gICAgY29uc3QgcmdiSW5wdXQgPSBbbWFwcGVkUiwgbWFwcGVkRywgbWFwcGVkQl07XG4gICAgY29uc3QgeHl6T3V0cHV0ID0gbXVsdGlwbHkoTkFNRURfR0FNVVRTLmRpc3BsYXlQMywgcmdiSW5wdXQpO1xuICAgIHJldHVybiB4eXpPdXRwdXQ7XG59XG5mdW5jdGlvbiB4eXpkNTBUb0Rpc3BsYXlQMyh4LCB5LCB6KSB7XG4gICAgY29uc3QgeHl6SW5wdXQgPSBbeCwgeSwgel07XG4gICAgY29uc3QgcmdiT3V0cHV0ID0gbXVsdGlwbHkoTkFNRURfR0FNVVRTLmRpc3BsYXlQM19JTlZFUlNFLCB4eXpJbnB1dCk7XG4gICAgcmV0dXJuIGFwcGx5VHJhbnNmZXJGbnMoTkFNRURfVFJBTlNGRVJfRk4uc1JHQl9JTlZFUlNFLCByZ2JPdXRwdXRbMF0sIHJnYk91dHB1dFsxXSwgcmdiT3V0cHV0WzJdKTtcbn1cbmZ1bmN0aW9uIHByb1Bob3RvVG9YeXpkNTAociwgZywgYikge1xuICAgIGNvbnN0IFttYXBwZWRSLCBtYXBwZWRHLCBtYXBwZWRCXSA9IGFwcGx5VHJhbnNmZXJGbnMoTkFNRURfVFJBTlNGRVJfRk4ucHJvUGhvdG9SR0IsIHIsIGcsIGIpO1xuICAgIGNvbnN0IHJnYklucHV0ID0gW21hcHBlZFIsIG1hcHBlZEcsIG1hcHBlZEJdO1xuICAgIGNvbnN0IHh5ek91dHB1dCA9IG11bHRpcGx5KFBST19QSE9UT19UT19YWVpENTBfTUFUUklYLCByZ2JJbnB1dCk7XG4gICAgcmV0dXJuIHh5ek91dHB1dDtcbn1cbmZ1bmN0aW9uIHh5emQ1MFRvUHJvUGhvdG8oeCwgeSwgeikge1xuICAgIGNvbnN0IHh5eklucHV0ID0gW3gsIHksIHpdO1xuICAgIGNvbnN0IHJnYk91dHB1dCA9IG11bHRpcGx5KFhZWkQ1MF9UT19QUk9fUEhPVE9fTUFUUklYLCB4eXpJbnB1dCk7XG4gICAgcmV0dXJuIGFwcGx5VHJhbnNmZXJGbnMoTkFNRURfVFJBTlNGRVJfRk4ucHJvUGhvdG9SR0JfSU5WRVJTRSwgcmdiT3V0cHV0WzBdLCByZ2JPdXRwdXRbMV0sIHJnYk91dHB1dFsyXSk7XG59XG5mdW5jdGlvbiBhZG9iZVJHQlRvWHl6ZDUwKHIsIGcsIGIpIHtcbiAgICBjb25zdCBbbWFwcGVkUiwgbWFwcGVkRywgbWFwcGVkQl0gPSBhcHBseVRyYW5zZmVyRm5zKE5BTUVEX1RSQU5TRkVSX0ZOLmsyRG90MiwgciwgZywgYik7XG4gICAgY29uc3QgcmdiSW5wdXQgPSBbbWFwcGVkUiwgbWFwcGVkRywgbWFwcGVkQl07XG4gICAgY29uc3QgeHl6T3V0cHV0ID0gbXVsdGlwbHkoTkFNRURfR0FNVVRTLmFkb2JlUkdCLCByZ2JJbnB1dCk7XG4gICAgcmV0dXJuIHh5ek91dHB1dDtcbn1cbmZ1bmN0aW9uIHh5emQ1MFRvQWRvYmVSR0IoeCwgeSwgeikge1xuICAgIGNvbnN0IHh5eklucHV0ID0gW3gsIHksIHpdO1xuICAgIGNvbnN0IHJnYk91dHB1dCA9IG11bHRpcGx5KE5BTUVEX0dBTVVUUy5hZG9iZVJHQl9JTlZFUlNFLCB4eXpJbnB1dCk7XG4gICAgcmV0dXJuIGFwcGx5VHJhbnNmZXJGbnMoTkFNRURfVFJBTlNGRVJfRk4uazJEb3QyX0lOVkVSU0UsIHJnYk91dHB1dFswXSwgcmdiT3V0cHV0WzFdLCByZ2JPdXRwdXRbMl0pO1xufVxuZnVuY3Rpb24gcmVjMjAyMFRvWHl6ZDUwKHIsIGcsIGIpIHtcbiAgICBjb25zdCBbbWFwcGVkUiwgbWFwcGVkRywgbWFwcGVkQl0gPSBhcHBseVRyYW5zZmVyRm5zKE5BTUVEX1RSQU5TRkVSX0ZOLnJlYzIwMjAsIHIsIGcsIGIpO1xuICAgIGNvbnN0IHJnYklucHV0ID0gW21hcHBlZFIsIG1hcHBlZEcsIG1hcHBlZEJdO1xuICAgIGNvbnN0IHh5ek91dHB1dCA9IG11bHRpcGx5KE5BTUVEX0dBTVVUUy5yZWMyMDIwLCByZ2JJbnB1dCk7XG4gICAgcmV0dXJuIHh5ek91dHB1dDtcbn1cbmZ1bmN0aW9uIHh5emQ1MFRvUmVjMjAyMCh4LCB5LCB6KSB7XG4gICAgY29uc3QgeHl6SW5wdXQgPSBbeCwgeSwgel07XG4gICAgY29uc3QgcmdiT3V0cHV0ID0gbXVsdGlwbHkoTkFNRURfR0FNVVRTLnJlYzIwMjBfSU5WRVJTRSwgeHl6SW5wdXQpO1xuICAgIHJldHVybiBhcHBseVRyYW5zZmVyRm5zKE5BTUVEX1RSQU5TRkVSX0ZOLnJlYzIwMjBfSU5WRVJTRSwgcmdiT3V0cHV0WzBdLCByZ2JPdXRwdXRbMV0sIHJnYk91dHB1dFsyXSk7XG59XG5mdW5jdGlvbiB4eXpkNTBUb0Q2NSh4LCB5LCB6KSB7XG4gICAgY29uc3QgeHl6SW5wdXQgPSBbeCwgeSwgel07XG4gICAgY29uc3QgeHl6T3V0cHV0ID0gbXVsdGlwbHkoWFlaRDUwX1RPX1hZWkQ2NV9NQVRSSVgsIHh5eklucHV0KTtcbiAgICByZXR1cm4geHl6T3V0cHV0O1xufVxuZnVuY3Rpb24geHl6ZDY1VG9ENTAoeCwgeSwgeikge1xuICAgIGNvbnN0IHh5eklucHV0ID0gW3gsIHksIHpdO1xuICAgIGNvbnN0IHh5ek91dHB1dCA9IG11bHRpcGx5KFhZWkQ2NV9UT19YWVpENTBfTUFUUklYLCB4eXpJbnB1dCk7XG4gICAgcmV0dXJuIHh5ek91dHB1dDtcbn1cbmZ1bmN0aW9uIHh5emQ2NVRvc1JHQkxpbmVhcih4LCB5LCB6KSB7XG4gICAgY29uc3QgeHl6SW5wdXQgPSBbeCwgeSwgel07XG4gICAgY29uc3QgcmdiUmVzdWx0ID0gbXVsdGlwbHkoWFlaRDY1X1RPX1NSR0JfTUFUUklYLCB4eXpJbnB1dCk7XG4gICAgcmV0dXJuIHJnYlJlc3VsdDtcbn1cbmZ1bmN0aW9uIHh5emQ1MFRvc1JHQkxpbmVhcih4LCB5LCB6KSB7XG4gICAgY29uc3QgeHl6SW5wdXQgPSBbeCwgeSwgel07XG4gICAgY29uc3QgcmdiUmVzdWx0ID0gbXVsdGlwbHkoTkFNRURfR0FNVVRTLnNSR0JfSU5WRVJTRSwgeHl6SW5wdXQpO1xuICAgIHJldHVybiByZ2JSZXN1bHQ7XG59XG5mdW5jdGlvbiBzcmdiTGluZWFyVG9YeXpkNTAociwgZywgYikge1xuICAgIGNvbnN0IHJnYklucHV0ID0gW3IsIGcsIGJdO1xuICAgIGNvbnN0IHh5ek91dHB1dCA9IG11bHRpcGx5KE5BTUVEX0dBTVVUUy5zUkdCLCByZ2JJbnB1dCk7XG4gICAgcmV0dXJuIHh5ek91dHB1dDtcbn1cbmZ1bmN0aW9uIHNyZ2JUb1h5emQ1MChyLCBnLCBiKSB7XG4gICAgY29uc3QgW21hcHBlZFIsIG1hcHBlZEcsIG1hcHBlZEJdID0gYXBwbHlUcmFuc2ZlckZucyhOQU1FRF9UUkFOU0ZFUl9GTi5zUkdCLCByLCBnLCBiKTtcbiAgICBjb25zdCByZ2JJbnB1dCA9IFttYXBwZWRSLCBtYXBwZWRHLCBtYXBwZWRCXTtcbiAgICBjb25zdCB4eXpPdXRwdXQgPSBtdWx0aXBseShOQU1FRF9HQU1VVFMuc1JHQiwgcmdiSW5wdXQpO1xuICAgIHJldHVybiB4eXpPdXRwdXQ7XG59XG5mdW5jdGlvbiB4eXpkNTBUb1NyZ2IoeCwgeSwgeikge1xuICAgIGNvbnN0IHh5eklucHV0ID0gW3gsIHksIHpdO1xuICAgIGNvbnN0IHJnYk91dHB1dCA9IG11bHRpcGx5KE5BTUVEX0dBTVVUUy5zUkdCX0lOVkVSU0UsIHh5eklucHV0KTtcbiAgICByZXR1cm4gYXBwbHlUcmFuc2ZlckZucyhOQU1FRF9UUkFOU0ZFUl9GTi5zUkdCX0lOVkVSU0UsIHJnYk91dHB1dFswXSwgcmdiT3V0cHV0WzFdLCByZ2JPdXRwdXRbMl0pO1xufVxuZnVuY3Rpb24gb2tsY2hUb1h5emQ1MChsSW5wdXQsIGMsIGgpIHtcbiAgICBjb25zdCBbbCwgYSwgYl0gPSBsY2hUb0xhYihsSW5wdXQsIGMsIGgpO1xuICAgIGNvbnN0IFt4NjUsIHk2NSwgejY1XSA9IG9rbGFiVG9YeXpkNjUobCwgYSwgYik7XG4gICAgcmV0dXJuIHh5emQ2NVRvRDUwKHg2NSwgeTY1LCB6NjUpO1xufVxuZnVuY3Rpb24geHl6ZDUwVG9Pa2xjaCh4LCB5LCB6KSB7XG4gICAgY29uc3QgW3g2NSwgeTY1LCB6NjVdID0geHl6ZDUwVG9ENjUoeCwgeSwgeik7XG4gICAgY29uc3QgW2wsIGEsIGJdID0geHl6ZDY1VG9Pa2xhYih4NjUsIHk2NSwgejY1KTtcbiAgICByZXR1cm4gbGFiVG9MY2gobCwgYSwgYik7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb252ZXJ0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/convert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/core.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/core.js ***!
  \***********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.OFFSET_A = exports.OFFSET_B = exports.OFFSET_G = exports.OFFSET_R = void 0;\nexports.newColor = newColor;\nexports.from = from;\nexports.toNumber = toNumber;\nexports.getRed = getRed;\nexports.getGreen = getGreen;\nexports.getBlue = getBlue;\nexports.getAlpha = getAlpha;\nexports.setRed = setRed;\nexports.setGreen = setGreen;\nexports.setBlue = setBlue;\nexports.setAlpha = setAlpha;\nconst bit = __importStar(__webpack_require__(/*! ./bit */ \"(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/bit.js\"));\nconst { cast, get, set } = bit;\nexports.OFFSET_R = 24;\nexports.OFFSET_G = 16;\nexports.OFFSET_B = 8;\nexports.OFFSET_A = 0;\n/**\n * Creates a new color from the given RGBA components.\n * Every component should be in the [0, 255] range.\n */\nfunction newColor(r, g, b, a) {\n    return ((r << exports.OFFSET_R) +\n        (g << exports.OFFSET_G) +\n        (b << exports.OFFSET_B) +\n        (a << exports.OFFSET_A));\n}\n/**\n * Creates a new color from the given number value, e.g. 0x599eff.\n */\nfunction from(color) {\n    return newColor(get(color, exports.OFFSET_R), get(color, exports.OFFSET_G), get(color, exports.OFFSET_B), get(color, exports.OFFSET_A));\n}\n/**\n * Turns the color into its equivalent number representation.\n * This is essentially a cast from int32 to uint32.\n */\nfunction toNumber(color) {\n    return cast(color);\n}\nfunction getRed(c) { return get(c, exports.OFFSET_R); }\nfunction getGreen(c) { return get(c, exports.OFFSET_G); }\nfunction getBlue(c) { return get(c, exports.OFFSET_B); }\nfunction getAlpha(c) { return get(c, exports.OFFSET_A); }\nfunction setRed(c, value) { return set(c, exports.OFFSET_R, value); }\nfunction setGreen(c, value) { return set(c, exports.OFFSET_G, value); }\nfunction setBlue(c, value) { return set(c, exports.OFFSET_B, value); }\nfunction setAlpha(c, value) { return set(c, exports.OFFSET_A, value); }\n//# sourceMappingURL=core.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/format.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/format.js ***!
  \*************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.format = void 0;\nexports.formatHEXA = formatHEXA;\nexports.formatHEX = formatHEX;\nexports.formatRGBA = formatRGBA;\nexports.toRGBA = toRGBA;\nexports.formatHSLA = formatHSLA;\nexports.toHSLA = toHSLA;\nexports.formatHWBA = formatHWBA;\nexports.toHWBA = toHWBA;\nconst core = __importStar(__webpack_require__(/*! ./core */ \"(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/core.js\"));\nconst { getRed, getGreen, getBlue, getAlpha } = core;\n// Return buffer, avoid allocations\nconst buffer = [0, 0, 0];\n/**\n * Map 8-bits value to its hexadecimal representation\n * ['00', '01', '02', ..., 'fe', 'ff']\n */\nconst FORMAT_HEX = Array.from({ length: 256 })\n    .map((_, byte) => byte.toString(16).padStart(2, '0'));\n/** Format to a #RRGGBBAA string */\nexports.format = formatHEXA;\n/** Format to a #RRGGBBAA string */\nfunction formatHEXA(color) {\n    return ('#' +\n        FORMAT_HEX[getRed(color)] +\n        FORMAT_HEX[getGreen(color)] +\n        FORMAT_HEX[getBlue(color)] +\n        FORMAT_HEX[getAlpha(color)]);\n}\nfunction formatHEX(color) {\n    return ('#' +\n        FORMAT_HEX[getRed(color)] +\n        FORMAT_HEX[getGreen(color)] +\n        FORMAT_HEX[getBlue(color)]);\n}\nfunction formatRGBA(color) {\n    return `rgba(${getRed(color)} ${getGreen(color)} ${getBlue(color)} / ${getAlpha(color) / 255})`;\n}\nfunction toRGBA(color) {\n    return {\n        r: getRed(color),\n        g: getGreen(color),\n        b: getBlue(color),\n        a: getAlpha(color),\n    };\n}\nfunction formatHSLA(color) {\n    rgbToHSL(getRed(color), getGreen(color), getBlue(color));\n    const h = buffer[0];\n    const s = buffer[1];\n    const l = buffer[2];\n    const a = getAlpha(color) / 255;\n    return `hsla(${h} ${s}% ${l}% / ${a})`;\n}\nfunction toHSLA(color) {\n    rgbToHSL(getRed(color), getGreen(color), getBlue(color));\n    const h = buffer[0];\n    const s = buffer[1];\n    const l = buffer[2];\n    const a = getAlpha(color) / 255;\n    return { h, s, l, a };\n}\nfunction formatHWBA(color) {\n    rgbToHWB(getRed(color), getGreen(color), getBlue(color));\n    const h = buffer[0];\n    const w = buffer[1];\n    const b = buffer[2];\n    const a = getAlpha(color) / 255;\n    return `hsla(${h} ${w}% ${b}% / ${a})`;\n}\nfunction toHWBA(color) {\n    rgbToHWB(getRed(color), getGreen(color), getBlue(color));\n    const h = buffer[0];\n    const w = buffer[1];\n    const b = buffer[2];\n    const a = getAlpha(color) / 255;\n    return { h, w, b, a };\n}\n// Conversion functions\n// https://www.30secondsofcode.org/js/s/rgb-hex-hsl-hsb-color-format-conversion/\nfunction rgbToHSL(r, g, b) {\n    r /= 255;\n    g /= 255;\n    b /= 255;\n    const l = Math.max(r, g, b);\n    const s = l - Math.min(r, g, b);\n    const h = s\n        ? l === r\n            ? (g - b) / s\n            : l === g\n                ? 2 + (b - r) / s\n                : 4 + (r - g) / s\n        : 0;\n    buffer[0] = 60 * h < 0 ? 60 * h + 360 : 60 * h;\n    buffer[1] = 100 * (s ? (l <= 0.5 ? s / (2 * l - s) : s / (2 - (2 * l - s))) : 0);\n    buffer[2] = (100 * (2 * l - s)) / 2;\n}\n// https://stackoverflow.com/a/29463581/3112706\nfunction rgbToHWB(r, g, b) {\n    r /= 255;\n    g /= 255;\n    b /= 255;\n    const w = Math.min(r, g, b);\n    const v = Math.max(r, g, b);\n    const black = 1 - v;\n    if (v === w) {\n        buffer[0] = 0;\n        buffer[1] = w;\n        buffer[2] = black;\n        return;\n    }\n    let f = r === w ? g - b : (g === w ? b - r : r - g);\n    let i = r === w ? 3 : (g === w ? 5 : 1);\n    buffer[0] = (i - f / (v - w)) / 6;\n    buffer[1] = w;\n    buffer[2] = black;\n}\n//# sourceMappingURL=format.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/format.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/functions.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/functions.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.alpha = alpha;\nexports.darken = darken;\nexports.lighten = lighten;\nexports.blend = blend;\nexports.getLuminance = getLuminance;\nconst core = __importStar(__webpack_require__(/*! ./core */ \"(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/core.js\"));\nconst { getRed, getGreen, getBlue, getAlpha, setAlpha, newColor } = core;\n/**\n * Modifies color alpha channel.\n * @param color - Color\n * @param value - Value in the range [0, 1]\n */\nfunction alpha(color, value) {\n    return setAlpha(color, Math.round(value * 255));\n}\n/**\n * Darkens a color.\n * @param color - Color\n * @param coefficient - Multiplier in the range [0, 1]\n */\nfunction darken(color, coefficient) {\n    const r = getRed(color);\n    const g = getGreen(color);\n    const b = getBlue(color);\n    const a = getAlpha(color);\n    const factor = 1 - coefficient;\n    return newColor(r * factor, g * factor, b * factor, a);\n}\n/**\n * Lighten a color.\n * @param color - Color\n * @param coefficient - Multiplier in the range [0, 1]\n */\nfunction lighten(color, coefficient) {\n    const r = getRed(color);\n    const g = getGreen(color);\n    const b = getBlue(color);\n    const a = getAlpha(color);\n    return newColor(r + (255 - r) * coefficient, g + (255 - g) * coefficient, b + (255 - b) * coefficient, a);\n}\n/**\n * Blend (aka mix) two colors together.\n * @param background The background color\n * @param overlay The overlay color that is affected by @opacity\n * @param opacity Opacity (alpha) for @overlay\n * @param [gamma=1.0] Gamma correction coefficient. `1.0` to match browser behavior, `2.2` for gamma-corrected blending.\n */\nfunction blend(background, overlay, opacity, gamma = 1.0) {\n    const blendChannel = (b, o) => Math.round((b ** (1 / gamma) * (1 - opacity) + o ** (1 / gamma) * opacity) ** gamma);\n    const r = blendChannel(getRed(background), getRed(overlay));\n    const g = blendChannel(getGreen(background), getGreen(overlay));\n    const b = blendChannel(getBlue(background), getBlue(overlay));\n    return newColor(r, g, b, 255);\n}\n/**\n * The relative brightness of any point in a color space, normalized to 0 for\n * darkest black and 1 for lightest white.\n * @returns The relative brightness of the color in the range 0 - 1, with 3 digits precision\n */\nfunction getLuminance(color) {\n    const r = getRed(color) / 255;\n    const g = getGreen(color) / 255;\n    const b = getBlue(color) / 255;\n    const apply = (v) => v <= 0.03928 ? v / 12.92 : ((v + 0.055) / 1.055) ** 2.4;\n    const r1 = apply(r);\n    const g1 = apply(g);\n    const b1 = apply(b);\n    return Math.round((0.2126 * r1 + 0.7152 * g1 + 0.0722 * b1) * 1000) / 1000;\n}\n//# sourceMappingURL=functions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/functions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/index.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/index.js ***!
  \************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./core */ \"(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/core.js\"), exports);\n__exportStar(__webpack_require__(/*! ./parse */ \"(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/parse.js\"), exports);\n__exportStar(__webpack_require__(/*! ./format */ \"(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/format.js\"), exports);\n__exportStar(__webpack_require__(/*! ./functions */ \"(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/functions.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/parse.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/parse.js ***!
  \************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parse = parse;\nexports.parseHex = parseHex;\nexports.parseColor = parseColor;\nconst core_1 = __webpack_require__(/*! ./core */ \"(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/core.js\");\nconst convert = __importStar(__webpack_require__(/*! ./convert */ \"(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/convert.js\"));\nconst HASH = '#'.charCodeAt(0);\nconst PERCENT = '%'.charCodeAt(0);\nconst G = 'g'.charCodeAt(0);\nconst N = 'n'.charCodeAt(0);\nconst D = 'd'.charCodeAt(0);\nconst E = 'e'.charCodeAt(0);\n/**\n * Approximative CSS colorspace string pattern, e.g. rgb(), color()\n */\nconst PATTERN = (() => {\n    const NAME = '(\\\\w+)';\n    const SEPARATOR = '[\\\\s,\\\\/]';\n    const VALUE = '([^\\\\s,\\\\/]+)';\n    const SEPARATOR_THEN_VALUE = `(?:${SEPARATOR}+${VALUE})`;\n    return new RegExp(`${NAME}\\\\(\n      ${SEPARATOR}*\n      ${VALUE}\n      ${SEPARATOR_THEN_VALUE}\n      ${SEPARATOR_THEN_VALUE}\n      ${SEPARATOR_THEN_VALUE}?\n      ${SEPARATOR_THEN_VALUE}?\n      ${SEPARATOR}*\n    \\\\)`.replace(/\\s/g, ''));\n})();\n/**\n * Parse CSS color\n * @param color CSS color string: #xxx, #xxxxxx, #xxxxxxxx, rgb(), rgba(), hsl(), hsla(), color()\n */\nfunction parse(color) {\n    if (color.charCodeAt(0) === HASH) {\n        return parseHex(color);\n    }\n    else {\n        return parseColor(color);\n    }\n}\n/**\n * Parse hexadecimal CSS color\n * @param color Hex color string: #xxx, #xxxxxx, #xxxxxxxx\n */\nfunction parseHex(color) {\n    let r = 0x00;\n    let g = 0x00;\n    let b = 0x00;\n    let a = 0xff;\n    switch (color.length) {\n        // #59f\n        case 4: {\n            r = (hexValue(color.charCodeAt(1)) << 4) + hexValue(color.charCodeAt(1));\n            g = (hexValue(color.charCodeAt(2)) << 4) + hexValue(color.charCodeAt(2));\n            b = (hexValue(color.charCodeAt(3)) << 4) + hexValue(color.charCodeAt(3));\n            break;\n        }\n        // #5599ff\n        case 7: {\n            r = (hexValue(color.charCodeAt(1)) << 4) + hexValue(color.charCodeAt(2));\n            g = (hexValue(color.charCodeAt(3)) << 4) + hexValue(color.charCodeAt(4));\n            b = (hexValue(color.charCodeAt(5)) << 4) + hexValue(color.charCodeAt(6));\n            break;\n        }\n        // #5599ff88\n        case 9: {\n            r = (hexValue(color.charCodeAt(1)) << 4) + hexValue(color.charCodeAt(2));\n            g = (hexValue(color.charCodeAt(3)) << 4) + hexValue(color.charCodeAt(4));\n            b = (hexValue(color.charCodeAt(5)) << 4) + hexValue(color.charCodeAt(6));\n            a = (hexValue(color.charCodeAt(7)) << 4) + hexValue(color.charCodeAt(8));\n            break;\n        }\n        default: {\n            break;\n        }\n    }\n    return (0, core_1.newColor)(r, g, b, a);\n}\n// https://lemire.me/blog/2019/04/17/parsing-short-hexadecimal-strings-efficiently/\nfunction hexValue(c) {\n    return (c & 0xF) + 9 * (c >> 6);\n}\n/**\n * Parse CSS color\n * https://developer.mozilla.org/en-US/docs/Web/CSS/color_value\n * @param color CSS color string: rgb(), rgba(), hsl(), hsla(), color()\n */\nfunction parseColor(color) {\n    const match = PATTERN.exec(color);\n    if (match === null) {\n        throw new Error(`Color.parse(): invalid CSS color: \"${color}\"`);\n    }\n    const format = match[1];\n    const p1 = match[2];\n    const p2 = match[3];\n    const p3 = match[4];\n    const p4 = match[5];\n    const p5 = match[6];\n    switch (format) {\n        case 'rgb':\n        case 'rgba': {\n            const r = parseColorChannel(p1);\n            const g = parseColorChannel(p2);\n            const b = parseColorChannel(p3);\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            return (0, core_1.newColor)(r, g, b, a);\n        }\n        case 'hsl':\n        case 'hsla': {\n            const h = parseAngle(p1);\n            const s = parsePercentage(p2);\n            const l = parsePercentage(p3);\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            // https://stackoverflow.com/a/9493060/3112706\n            let r, g, b;\n            if (s === 0) {\n                r = g = b = Math.round(l * 255); // achromatic\n            }\n            else {\n                const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n                const p = 2 * l - q;\n                r = Math.round(hueToRGB(p, q, h + 1 / 3) * 255);\n                g = Math.round(hueToRGB(p, q, h) * 255);\n                b = Math.round(hueToRGB(p, q, h - 1 / 3) * 255);\n            }\n            return (0, core_1.newColor)(r, g, b, a);\n        }\n        case 'hwb': {\n            const h = parseAngle(p1);\n            const w = parsePercentage(p2);\n            const bl = parsePercentage(p3);\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            /* https://drafts.csswg.org/css-color/#hwb-to-rgb */\n            const s = 1.0;\n            const l = 0.5;\n            // Same as HSL to RGB\n            const q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n            const p = 2 * l - q;\n            let r = Math.round(hueToRGB(p, q, h + 1 / 3) * 255);\n            let g = Math.round(hueToRGB(p, q, h) * 255);\n            let b = Math.round(hueToRGB(p, q, h - 1 / 3) * 255);\n            // Then HWB\n            r = hwbApply(r, w, bl);\n            g = hwbApply(g, w, bl);\n            b = hwbApply(b, w, bl);\n            return (0, core_1.newColor)(r, g, b, a);\n        }\n        case 'lab': {\n            const l = parsePercentageFor(p1, 100);\n            const aa = parsePercentageFor(p2, 125);\n            const b = parsePercentageFor(p3, 125);\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.labToXyzd50(l, aa, b)));\n        }\n        case 'lch': {\n            const l = parsePercentageFor(p1, 100);\n            const c = parsePercentageFor(p2, 150);\n            const h = parseAngle(p3) * 360;\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.labToXyzd50(...convert.lchToLab(l, c, h))));\n        }\n        case 'oklab': {\n            const l = parsePercentageFor(p1, 1);\n            const aa = parsePercentageFor(p2, 0.4);\n            const b = parsePercentageFor(p3, 0.4);\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.xyzd65ToD50(...convert.oklabToXyzd65(l, aa, b))));\n        }\n        case 'oklch': {\n            const l = parsePercentageOrValue(p1);\n            const c = parsePercentageOrValue(p2);\n            const h = parsePercentageOrValue(p3);\n            const a = p4 ? parseAlphaChannel(p4) : 255;\n            return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.oklchToXyzd50(l, c, h)));\n        }\n        case 'color': {\n            // https://drafts.csswg.org/css-color-4/#color-function\n            const colorspace = p1;\n            const c1 = parsePercentageOrValue(p2);\n            const c2 = parsePercentageOrValue(p3);\n            const c3 = parsePercentageOrValue(p4);\n            const a = p5 ? parseAlphaChannel(p5) : 255;\n            switch (colorspace) {\n                // RGB color spaces\n                case 'srgb': {\n                    return newColorFromArray(a, [c1, c2, c3]);\n                }\n                case 'srgb-linear': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.srgbLinearToXyzd50(c1, c2, c3)));\n                }\n                case 'display-p3': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.displayP3ToXyzd50(c1, c2, c3)));\n                }\n                case 'a98-rgb': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.adobeRGBToXyzd50(c1, c2, c3)));\n                }\n                case 'prophoto-rgb': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.proPhotoToXyzd50(c1, c2, c3)));\n                }\n                case 'rec2020': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.rec2020ToXyzd50(c1, c2, c3)));\n                }\n                // XYZ color spaces\n                case 'xyz':\n                case 'xyz-d65': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(...convert.xyzd65ToD50(c1, c2, c3)));\n                }\n                case 'xyz-d50': {\n                    return newColorFromArray(a, convert.xyzd50ToSrgb(c1, c2, c3));\n                }\n                default:\n            }\n        }\n        default:\n    }\n    throw new Error(`Color.parse(): invalid CSS color: \"${color}\"`);\n}\n/**\n * Accepts: \"50%\", \"128\"\n * https://developer.mozilla.org/en-US/docs/Web/CSS/color_value/rgb#values\n * @returns a value in the 0 to 255 range\n */\nfunction parseColorChannel(channel) {\n    if (channel.charCodeAt(channel.length - 1) === PERCENT) {\n        return Math.round((parseFloat(channel) / 100) * 255);\n    }\n    return Math.round(parseFloat(channel));\n}\n/**\n * Accepts: \"50%\", \".5\", \"0.5\"\n * https://developer.mozilla.org/en-US/docs/Web/CSS/alpha-value\n * @returns a value in the [0, 255] range\n */\nfunction parseAlphaChannel(channel) {\n    return Math.round(parseAlphaValue(channel) * 255);\n}\n/**\n * Accepts: \"50%\", \".5\", \"0.5\"\n * https://developer.mozilla.org/en-US/docs/Web/CSS/alpha-value\n * @returns a value in the [0, 1] range\n */\nfunction parseAlphaValue(channel) {\n    if (channel.charCodeAt(0) === N) {\n        return 0;\n    }\n    if (channel.charCodeAt(channel.length - 1) === PERCENT) {\n        return parseFloat(channel) / 100;\n    }\n    return parseFloat(channel);\n}\n/**\n * Accepts: \"360\", \"360deg\", \"400grad\", \"6.28rad\", \"1turn\", \"none\"\n * https://developer.mozilla.org/en-US/docs/Web/CSS/angle\n * @returns a value in the 0.0 to 1.0 range\n */\nfunction parseAngle(angle) {\n    let factor = 1;\n    switch (angle.charCodeAt(angle.length - 1)) {\n        case E: {\n            // 'none'\n            return 0;\n        }\n        case D: {\n            // 'rad', 'grad'\n            if (angle.charCodeAt(Math.max(0, angle.length - 4)) === G) {\n                // 'grad'\n                factor = 400;\n            }\n            else {\n                // 'rad'\n                factor = 2 * Math.PI; // TAU\n            }\n            break;\n        }\n        case N: {\n            // 'turn'\n            factor = 1;\n            break;\n        }\n        // case G: // 'deg', but no need to check as it's also the default\n        default: {\n            factor = 360;\n        }\n    }\n    return parseFloat(angle) / factor;\n}\n/**\n * Accepts: \"100%\", \"none\"\n * @returns a value in the 0.0 to 1.0 range\n */\nfunction parsePercentage(value) {\n    if (value.charCodeAt(0) === N) {\n        return 0;\n    }\n    return parseFloat(value) / 100;\n}\n/**\n * Accepts: \"1.0\", \"100%\", \"none\"\n * @returns a value in the 0.0 to 1.0 range\n */\nfunction parsePercentageOrValue(value) {\n    if (value.charCodeAt(0) === N) {\n        return 0;\n    }\n    if (value.charCodeAt(value.length - 1) === PERCENT) {\n        return parseFloat(value) / 100;\n    }\n    return parseFloat(value);\n}\n/**\n * Accepts: \"100\", \"100%\", \"none\"\n * @returns a value in the -@range to @range range\n */\nfunction parsePercentageFor(value, range) {\n    if (value.charCodeAt(0) === N) {\n        return 0;\n    }\n    if (value.charCodeAt(value.length - 1) === PERCENT) {\n        return parseFloat(value) / 100 * range;\n    }\n    return parseFloat(value);\n}\n// HSL functions\nfunction hueToRGB(p, q, t) {\n    if (t < 0) {\n        t += 1;\n    }\n    ;\n    if (t > 1) {\n        t -= 1;\n    }\n    ;\n    if (t < 1 / 6) {\n        return p + (q - p) * 6 * t;\n    }\n    ;\n    if (t < 1 / 2) {\n        return q;\n    }\n    ;\n    if (t < 2 / 3) {\n        return p + (q - p) * (2 / 3 - t) * 6;\n    }\n    ;\n    {\n        return p;\n    }\n    ;\n}\n// HWB functions\nfunction hwbApply(channel, w, b) {\n    let result = channel / 255;\n    result *= 1 - w - b;\n    result += w;\n    return Math.round(result * 255);\n}\nfunction clamp(value) {\n    return Math.max(0, Math.min(255, value));\n}\nfunction newColorFromArray(a, rgb) {\n    const r = clamp(Math.round(rgb[0] * 255));\n    const g = clamp(Math.round(rgb[1] * 255));\n    const b = clamp(Math.round(rgb[2] * 255));\n    return (0, core_1.newColor)(r, g, b, a);\n}\n//# sourceMappingURL=parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vY29sb3ItYml0c0AxLjEuMC9ub2RlX21vZHVsZXMvY29sb3ItYml0cy9idWlsZC9wYXJzZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxvQ0FBb0M7QUFDbkQ7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0EsMENBQTBDLDRCQUE0QjtBQUN0RSxDQUFDO0FBQ0Q7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYTtBQUNiLGdCQUFnQjtBQUNoQixrQkFBa0I7QUFDbEIsZUFBZSxtQkFBTyxDQUFDLGlHQUFRO0FBQy9CLDZCQUE2QixtQkFBTyxDQUFDLHVHQUFXO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLFVBQVUsR0FBRyxNQUFNO0FBQzFELHlCQUF5QixLQUFLO0FBQzlCLFFBQVEsVUFBVTtBQUNsQixRQUFRO0FBQ1IsUUFBUTtBQUNSLFFBQVE7QUFDUixRQUFRLHFCQUFxQjtBQUM3QixRQUFRLHFCQUFxQjtBQUM3QixRQUFRLFVBQVU7QUFDbEI7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhEQUE4RCxNQUFNO0FBQ3BFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlEO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwREFBMEQsTUFBTTtBQUNoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0M7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbmRyZVxcT25lRHJpdmVcXERlc2t0b3BcXEdpdGh1YiBSZXBvc2l0b3JpZXNcXGRlbW9cXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxjb2xvci1iaXRzQDEuMS4wXFxub2RlX21vZHVsZXNcXGNvbG9yLWJpdHNcXGJ1aWxkXFxwYXJzZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2NyZWF0ZUJpbmRpbmcgPSAodGhpcyAmJiB0aGlzLl9fY3JlYXRlQmluZGluZykgfHwgKE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIHZhciBkZXNjID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihtLCBrKTtcbiAgICBpZiAoIWRlc2MgfHwgKFwiZ2V0XCIgaW4gZGVzYyA/ICFtLl9fZXNNb2R1bGUgOiBkZXNjLndyaXRhYmxlIHx8IGRlc2MuY29uZmlndXJhYmxlKSkge1xuICAgICAgZGVzYyA9IHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfTtcbiAgICB9XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIGsyLCBkZXNjKTtcbn0pIDogKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICBvW2syXSA9IG1ba107XG59KSk7XG52YXIgX19zZXRNb2R1bGVEZWZhdWx0ID0gKHRoaXMgJiYgdGhpcy5fX3NldE1vZHVsZURlZmF1bHQpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIHYpIHtcbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgXCJkZWZhdWx0XCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgdmFsdWU6IHYgfSk7XG59KSA6IGZ1bmN0aW9uKG8sIHYpIHtcbiAgICBvW1wiZGVmYXVsdFwiXSA9IHY7XG59KTtcbnZhciBfX2ltcG9ydFN0YXIgPSAodGhpcyAmJiB0aGlzLl9faW1wb3J0U3RhcikgfHwgZnVuY3Rpb24gKG1vZCkge1xuICAgIGlmIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpIHJldHVybiBtb2Q7XG4gICAgdmFyIHJlc3VsdCA9IHt9O1xuICAgIGlmIChtb2QgIT0gbnVsbCkgZm9yICh2YXIgayBpbiBtb2QpIGlmIChrICE9PSBcImRlZmF1bHRcIiAmJiBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwobW9kLCBrKSkgX19jcmVhdGVCaW5kaW5nKHJlc3VsdCwgbW9kLCBrKTtcbiAgICBfX3NldE1vZHVsZURlZmF1bHQocmVzdWx0LCBtb2QpO1xuICAgIHJldHVybiByZXN1bHQ7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5wYXJzZSA9IHBhcnNlO1xuZXhwb3J0cy5wYXJzZUhleCA9IHBhcnNlSGV4O1xuZXhwb3J0cy5wYXJzZUNvbG9yID0gcGFyc2VDb2xvcjtcbmNvbnN0IGNvcmVfMSA9IHJlcXVpcmUoXCIuL2NvcmVcIik7XG5jb25zdCBjb252ZXJ0ID0gX19pbXBvcnRTdGFyKHJlcXVpcmUoXCIuL2NvbnZlcnRcIikpO1xuY29uc3QgSEFTSCA9ICcjJy5jaGFyQ29kZUF0KDApO1xuY29uc3QgUEVSQ0VOVCA9ICclJy5jaGFyQ29kZUF0KDApO1xuY29uc3QgRyA9ICdnJy5jaGFyQ29kZUF0KDApO1xuY29uc3QgTiA9ICduJy5jaGFyQ29kZUF0KDApO1xuY29uc3QgRCA9ICdkJy5jaGFyQ29kZUF0KDApO1xuY29uc3QgRSA9ICdlJy5jaGFyQ29kZUF0KDApO1xuLyoqXG4gKiBBcHByb3hpbWF0aXZlIENTUyBjb2xvcnNwYWNlIHN0cmluZyBwYXR0ZXJuLCBlLmcuIHJnYigpLCBjb2xvcigpXG4gKi9cbmNvbnN0IFBBVFRFUk4gPSAoKCkgPT4ge1xuICAgIGNvbnN0IE5BTUUgPSAnKFxcXFx3KyknO1xuICAgIGNvbnN0IFNFUEFSQVRPUiA9ICdbXFxcXHMsXFxcXC9dJztcbiAgICBjb25zdCBWQUxVRSA9ICcoW15cXFxccyxcXFxcL10rKSc7XG4gICAgY29uc3QgU0VQQVJBVE9SX1RIRU5fVkFMVUUgPSBgKD86JHtTRVBBUkFUT1J9KyR7VkFMVUV9KWA7XG4gICAgcmV0dXJuIG5ldyBSZWdFeHAoYCR7TkFNRX1cXFxcKFxuICAgICAgJHtTRVBBUkFUT1J9KlxuICAgICAgJHtWQUxVRX1cbiAgICAgICR7U0VQQVJBVE9SX1RIRU5fVkFMVUV9XG4gICAgICAke1NFUEFSQVRPUl9USEVOX1ZBTFVFfVxuICAgICAgJHtTRVBBUkFUT1JfVEhFTl9WQUxVRX0/XG4gICAgICAke1NFUEFSQVRPUl9USEVOX1ZBTFVFfT9cbiAgICAgICR7U0VQQVJBVE9SfSpcbiAgICBcXFxcKWAucmVwbGFjZSgvXFxzL2csICcnKSk7XG59KSgpO1xuLyoqXG4gKiBQYXJzZSBDU1MgY29sb3JcbiAqIEBwYXJhbSBjb2xvciBDU1MgY29sb3Igc3RyaW5nOiAjeHh4LCAjeHh4eHh4LCAjeHh4eHh4eHgsIHJnYigpLCByZ2JhKCksIGhzbCgpLCBoc2xhKCksIGNvbG9yKClcbiAqL1xuZnVuY3Rpb24gcGFyc2UoY29sb3IpIHtcbiAgICBpZiAoY29sb3IuY2hhckNvZGVBdCgwKSA9PT0gSEFTSCkge1xuICAgICAgICByZXR1cm4gcGFyc2VIZXgoY29sb3IpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIHBhcnNlQ29sb3IoY29sb3IpO1xuICAgIH1cbn1cbi8qKlxuICogUGFyc2UgaGV4YWRlY2ltYWwgQ1NTIGNvbG9yXG4gKiBAcGFyYW0gY29sb3IgSGV4IGNvbG9yIHN0cmluZzogI3h4eCwgI3h4eHh4eCwgI3h4eHh4eHh4XG4gKi9cbmZ1bmN0aW9uIHBhcnNlSGV4KGNvbG9yKSB7XG4gICAgbGV0IHIgPSAweDAwO1xuICAgIGxldCBnID0gMHgwMDtcbiAgICBsZXQgYiA9IDB4MDA7XG4gICAgbGV0IGEgPSAweGZmO1xuICAgIHN3aXRjaCAoY29sb3IubGVuZ3RoKSB7XG4gICAgICAgIC8vICM1OWZcbiAgICAgICAgY2FzZSA0OiB7XG4gICAgICAgICAgICByID0gKGhleFZhbHVlKGNvbG9yLmNoYXJDb2RlQXQoMSkpIDw8IDQpICsgaGV4VmFsdWUoY29sb3IuY2hhckNvZGVBdCgxKSk7XG4gICAgICAgICAgICBnID0gKGhleFZhbHVlKGNvbG9yLmNoYXJDb2RlQXQoMikpIDw8IDQpICsgaGV4VmFsdWUoY29sb3IuY2hhckNvZGVBdCgyKSk7XG4gICAgICAgICAgICBiID0gKGhleFZhbHVlKGNvbG9yLmNoYXJDb2RlQXQoMykpIDw8IDQpICsgaGV4VmFsdWUoY29sb3IuY2hhckNvZGVBdCgzKSk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICAvLyAjNTU5OWZmXG4gICAgICAgIGNhc2UgNzoge1xuICAgICAgICAgICAgciA9IChoZXhWYWx1ZShjb2xvci5jaGFyQ29kZUF0KDEpKSA8PCA0KSArIGhleFZhbHVlKGNvbG9yLmNoYXJDb2RlQXQoMikpO1xuICAgICAgICAgICAgZyA9IChoZXhWYWx1ZShjb2xvci5jaGFyQ29kZUF0KDMpKSA8PCA0KSArIGhleFZhbHVlKGNvbG9yLmNoYXJDb2RlQXQoNCkpO1xuICAgICAgICAgICAgYiA9IChoZXhWYWx1ZShjb2xvci5jaGFyQ29kZUF0KDUpKSA8PCA0KSArIGhleFZhbHVlKGNvbG9yLmNoYXJDb2RlQXQoNikpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgLy8gIzU1OTlmZjg4XG4gICAgICAgIGNhc2UgOToge1xuICAgICAgICAgICAgciA9IChoZXhWYWx1ZShjb2xvci5jaGFyQ29kZUF0KDEpKSA8PCA0KSArIGhleFZhbHVlKGNvbG9yLmNoYXJDb2RlQXQoMikpO1xuICAgICAgICAgICAgZyA9IChoZXhWYWx1ZShjb2xvci5jaGFyQ29kZUF0KDMpKSA8PCA0KSArIGhleFZhbHVlKGNvbG9yLmNoYXJDb2RlQXQoNCkpO1xuICAgICAgICAgICAgYiA9IChoZXhWYWx1ZShjb2xvci5jaGFyQ29kZUF0KDUpKSA8PCA0KSArIGhleFZhbHVlKGNvbG9yLmNoYXJDb2RlQXQoNikpO1xuICAgICAgICAgICAgYSA9IChoZXhWYWx1ZShjb2xvci5jaGFyQ29kZUF0KDcpKSA8PCA0KSArIGhleFZhbHVlKGNvbG9yLmNoYXJDb2RlQXQoOCkpO1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgZGVmYXVsdDoge1xuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuICgwLCBjb3JlXzEubmV3Q29sb3IpKHIsIGcsIGIsIGEpO1xufVxuLy8gaHR0cHM6Ly9sZW1pcmUubWUvYmxvZy8yMDE5LzA0LzE3L3BhcnNpbmctc2hvcnQtaGV4YWRlY2ltYWwtc3RyaW5ncy1lZmZpY2llbnRseS9cbmZ1bmN0aW9uIGhleFZhbHVlKGMpIHtcbiAgICByZXR1cm4gKGMgJiAweEYpICsgOSAqIChjID4+IDYpO1xufVxuLyoqXG4gKiBQYXJzZSBDU1MgY29sb3JcbiAqIGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0NTUy9jb2xvcl92YWx1ZVxuICogQHBhcmFtIGNvbG9yIENTUyBjb2xvciBzdHJpbmc6IHJnYigpLCByZ2JhKCksIGhzbCgpLCBoc2xhKCksIGNvbG9yKClcbiAqL1xuZnVuY3Rpb24gcGFyc2VDb2xvcihjb2xvcikge1xuICAgIGNvbnN0IG1hdGNoID0gUEFUVEVSTi5leGVjKGNvbG9yKTtcbiAgICBpZiAobWF0Y2ggPT09IG51bGwpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBDb2xvci5wYXJzZSgpOiBpbnZhbGlkIENTUyBjb2xvcjogXCIke2NvbG9yfVwiYCk7XG4gICAgfVxuICAgIGNvbnN0IGZvcm1hdCA9IG1hdGNoWzFdO1xuICAgIGNvbnN0IHAxID0gbWF0Y2hbMl07XG4gICAgY29uc3QgcDIgPSBtYXRjaFszXTtcbiAgICBjb25zdCBwMyA9IG1hdGNoWzRdO1xuICAgIGNvbnN0IHA0ID0gbWF0Y2hbNV07XG4gICAgY29uc3QgcDUgPSBtYXRjaFs2XTtcbiAgICBzd2l0Y2ggKGZvcm1hdCkge1xuICAgICAgICBjYXNlICdyZ2InOlxuICAgICAgICBjYXNlICdyZ2JhJzoge1xuICAgICAgICAgICAgY29uc3QgciA9IHBhcnNlQ29sb3JDaGFubmVsKHAxKTtcbiAgICAgICAgICAgIGNvbnN0IGcgPSBwYXJzZUNvbG9yQ2hhbm5lbChwMik7XG4gICAgICAgICAgICBjb25zdCBiID0gcGFyc2VDb2xvckNoYW5uZWwocDMpO1xuICAgICAgICAgICAgY29uc3QgYSA9IHA0ID8gcGFyc2VBbHBoYUNoYW5uZWwocDQpIDogMjU1O1xuICAgICAgICAgICAgcmV0dXJuICgwLCBjb3JlXzEubmV3Q29sb3IpKHIsIGcsIGIsIGEpO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgJ2hzbCc6XG4gICAgICAgIGNhc2UgJ2hzbGEnOiB7XG4gICAgICAgICAgICBjb25zdCBoID0gcGFyc2VBbmdsZShwMSk7XG4gICAgICAgICAgICBjb25zdCBzID0gcGFyc2VQZXJjZW50YWdlKHAyKTtcbiAgICAgICAgICAgIGNvbnN0IGwgPSBwYXJzZVBlcmNlbnRhZ2UocDMpO1xuICAgICAgICAgICAgY29uc3QgYSA9IHA0ID8gcGFyc2VBbHBoYUNoYW5uZWwocDQpIDogMjU1O1xuICAgICAgICAgICAgLy8gaHR0cHM6Ly9zdGFja292ZXJmbG93LmNvbS9hLzk0OTMwNjAvMzExMjcwNlxuICAgICAgICAgICAgbGV0IHIsIGcsIGI7XG4gICAgICAgICAgICBpZiAocyA9PT0gMCkge1xuICAgICAgICAgICAgICAgIHIgPSBnID0gYiA9IE1hdGgucm91bmQobCAqIDI1NSk7IC8vIGFjaHJvbWF0aWNcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IHEgPSBsIDwgMC41ID8gbCAqICgxICsgcykgOiBsICsgcyAtIGwgKiBzO1xuICAgICAgICAgICAgICAgIGNvbnN0IHAgPSAyICogbCAtIHE7XG4gICAgICAgICAgICAgICAgciA9IE1hdGgucm91bmQoaHVlVG9SR0IocCwgcSwgaCArIDEgLyAzKSAqIDI1NSk7XG4gICAgICAgICAgICAgICAgZyA9IE1hdGgucm91bmQoaHVlVG9SR0IocCwgcSwgaCkgKiAyNTUpO1xuICAgICAgICAgICAgICAgIGIgPSBNYXRoLnJvdW5kKGh1ZVRvUkdCKHAsIHEsIGggLSAxIC8gMykgKiAyNTUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuICgwLCBjb3JlXzEubmV3Q29sb3IpKHIsIGcsIGIsIGEpO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgJ2h3Yic6IHtcbiAgICAgICAgICAgIGNvbnN0IGggPSBwYXJzZUFuZ2xlKHAxKTtcbiAgICAgICAgICAgIGNvbnN0IHcgPSBwYXJzZVBlcmNlbnRhZ2UocDIpO1xuICAgICAgICAgICAgY29uc3QgYmwgPSBwYXJzZVBlcmNlbnRhZ2UocDMpO1xuICAgICAgICAgICAgY29uc3QgYSA9IHA0ID8gcGFyc2VBbHBoYUNoYW5uZWwocDQpIDogMjU1O1xuICAgICAgICAgICAgLyogaHR0cHM6Ly9kcmFmdHMuY3Nzd2cub3JnL2Nzcy1jb2xvci8jaHdiLXRvLXJnYiAqL1xuICAgICAgICAgICAgY29uc3QgcyA9IDEuMDtcbiAgICAgICAgICAgIGNvbnN0IGwgPSAwLjU7XG4gICAgICAgICAgICAvLyBTYW1lIGFzIEhTTCB0byBSR0JcbiAgICAgICAgICAgIGNvbnN0IHEgPSBsIDwgMC41ID8gbCAqICgxICsgcykgOiBsICsgcyAtIGwgKiBzO1xuICAgICAgICAgICAgY29uc3QgcCA9IDIgKiBsIC0gcTtcbiAgICAgICAgICAgIGxldCByID0gTWF0aC5yb3VuZChodWVUb1JHQihwLCBxLCBoICsgMSAvIDMpICogMjU1KTtcbiAgICAgICAgICAgIGxldCBnID0gTWF0aC5yb3VuZChodWVUb1JHQihwLCBxLCBoKSAqIDI1NSk7XG4gICAgICAgICAgICBsZXQgYiA9IE1hdGgucm91bmQoaHVlVG9SR0IocCwgcSwgaCAtIDEgLyAzKSAqIDI1NSk7XG4gICAgICAgICAgICAvLyBUaGVuIEhXQlxuICAgICAgICAgICAgciA9IGh3YkFwcGx5KHIsIHcsIGJsKTtcbiAgICAgICAgICAgIGcgPSBod2JBcHBseShnLCB3LCBibCk7XG4gICAgICAgICAgICBiID0gaHdiQXBwbHkoYiwgdywgYmwpO1xuICAgICAgICAgICAgcmV0dXJuICgwLCBjb3JlXzEubmV3Q29sb3IpKHIsIGcsIGIsIGEpO1xuICAgICAgICB9XG4gICAgICAgIGNhc2UgJ2xhYic6IHtcbiAgICAgICAgICAgIGNvbnN0IGwgPSBwYXJzZVBlcmNlbnRhZ2VGb3IocDEsIDEwMCk7XG4gICAgICAgICAgICBjb25zdCBhYSA9IHBhcnNlUGVyY2VudGFnZUZvcihwMiwgMTI1KTtcbiAgICAgICAgICAgIGNvbnN0IGIgPSBwYXJzZVBlcmNlbnRhZ2VGb3IocDMsIDEyNSk7XG4gICAgICAgICAgICBjb25zdCBhID0gcDQgPyBwYXJzZUFscGhhQ2hhbm5lbChwNCkgOiAyNTU7XG4gICAgICAgICAgICByZXR1cm4gbmV3Q29sb3JGcm9tQXJyYXkoYSwgY29udmVydC54eXpkNTBUb1NyZ2IoLi4uY29udmVydC5sYWJUb1h5emQ1MChsLCBhYSwgYikpKTtcbiAgICAgICAgfVxuICAgICAgICBjYXNlICdsY2gnOiB7XG4gICAgICAgICAgICBjb25zdCBsID0gcGFyc2VQZXJjZW50YWdlRm9yKHAxLCAxMDApO1xuICAgICAgICAgICAgY29uc3QgYyA9IHBhcnNlUGVyY2VudGFnZUZvcihwMiwgMTUwKTtcbiAgICAgICAgICAgIGNvbnN0IGggPSBwYXJzZUFuZ2xlKHAzKSAqIDM2MDtcbiAgICAgICAgICAgIGNvbnN0IGEgPSBwNCA/IHBhcnNlQWxwaGFDaGFubmVsKHA0KSA6IDI1NTtcbiAgICAgICAgICAgIHJldHVybiBuZXdDb2xvckZyb21BcnJheShhLCBjb252ZXJ0Lnh5emQ1MFRvU3JnYiguLi5jb252ZXJ0LmxhYlRvWHl6ZDUwKC4uLmNvbnZlcnQubGNoVG9MYWIobCwgYywgaCkpKSk7XG4gICAgICAgIH1cbiAgICAgICAgY2FzZSAnb2tsYWInOiB7XG4gICAgICAgICAgICBjb25zdCBsID0gcGFyc2VQZXJjZW50YWdlRm9yKHAxLCAxKTtcbiAgICAgICAgICAgIGNvbnN0IGFhID0gcGFyc2VQZXJjZW50YWdlRm9yKHAyLCAwLjQpO1xuICAgICAgICAgICAgY29uc3QgYiA9IHBhcnNlUGVyY2VudGFnZUZvcihwMywgMC40KTtcbiAgICAgICAgICAgIGNvbnN0IGEgPSBwNCA/IHBhcnNlQWxwaGFDaGFubmVsKHA0KSA6IDI1NTtcbiAgICAgICAgICAgIHJldHVybiBuZXdDb2xvckZyb21BcnJheShhLCBjb252ZXJ0Lnh5emQ1MFRvU3JnYiguLi5jb252ZXJ0Lnh5emQ2NVRvRDUwKC4uLmNvbnZlcnQub2tsYWJUb1h5emQ2NShsLCBhYSwgYikpKSk7XG4gICAgICAgIH1cbiAgICAgICAgY2FzZSAnb2tsY2gnOiB7XG4gICAgICAgICAgICBjb25zdCBsID0gcGFyc2VQZXJjZW50YWdlT3JWYWx1ZShwMSk7XG4gICAgICAgICAgICBjb25zdCBjID0gcGFyc2VQZXJjZW50YWdlT3JWYWx1ZShwMik7XG4gICAgICAgICAgICBjb25zdCBoID0gcGFyc2VQZXJjZW50YWdlT3JWYWx1ZShwMyk7XG4gICAgICAgICAgICBjb25zdCBhID0gcDQgPyBwYXJzZUFscGhhQ2hhbm5lbChwNCkgOiAyNTU7XG4gICAgICAgICAgICByZXR1cm4gbmV3Q29sb3JGcm9tQXJyYXkoYSwgY29udmVydC54eXpkNTBUb1NyZ2IoLi4uY29udmVydC5va2xjaFRvWHl6ZDUwKGwsIGMsIGgpKSk7XG4gICAgICAgIH1cbiAgICAgICAgY2FzZSAnY29sb3InOiB7XG4gICAgICAgICAgICAvLyBodHRwczovL2RyYWZ0cy5jc3N3Zy5vcmcvY3NzLWNvbG9yLTQvI2NvbG9yLWZ1bmN0aW9uXG4gICAgICAgICAgICBjb25zdCBjb2xvcnNwYWNlID0gcDE7XG4gICAgICAgICAgICBjb25zdCBjMSA9IHBhcnNlUGVyY2VudGFnZU9yVmFsdWUocDIpO1xuICAgICAgICAgICAgY29uc3QgYzIgPSBwYXJzZVBlcmNlbnRhZ2VPclZhbHVlKHAzKTtcbiAgICAgICAgICAgIGNvbnN0IGMzID0gcGFyc2VQZXJjZW50YWdlT3JWYWx1ZShwNCk7XG4gICAgICAgICAgICBjb25zdCBhID0gcDUgPyBwYXJzZUFscGhhQ2hhbm5lbChwNSkgOiAyNTU7XG4gICAgICAgICAgICBzd2l0Y2ggKGNvbG9yc3BhY2UpIHtcbiAgICAgICAgICAgICAgICAvLyBSR0IgY29sb3Igc3BhY2VzXG4gICAgICAgICAgICAgICAgY2FzZSAnc3JnYic6IHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ld0NvbG9yRnJvbUFycmF5KGEsIFtjMSwgYzIsIGMzXSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgJ3NyZ2ItbGluZWFyJzoge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbmV3Q29sb3JGcm9tQXJyYXkoYSwgY29udmVydC54eXpkNTBUb1NyZ2IoLi4uY29udmVydC5zcmdiTGluZWFyVG9YeXpkNTAoYzEsIGMyLCBjMykpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSAnZGlzcGxheS1wMyc6IHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ld0NvbG9yRnJvbUFycmF5KGEsIGNvbnZlcnQueHl6ZDUwVG9TcmdiKC4uLmNvbnZlcnQuZGlzcGxheVAzVG9YeXpkNTAoYzEsIGMyLCBjMykpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSAnYTk4LXJnYic6IHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ld0NvbG9yRnJvbUFycmF5KGEsIGNvbnZlcnQueHl6ZDUwVG9TcmdiKC4uLmNvbnZlcnQuYWRvYmVSR0JUb1h5emQ1MChjMSwgYzIsIGMzKSkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXNlICdwcm9waG90by1yZ2InOiB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBuZXdDb2xvckZyb21BcnJheShhLCBjb252ZXJ0Lnh5emQ1MFRvU3JnYiguLi5jb252ZXJ0LnByb1Bob3RvVG9YeXpkNTAoYzEsIGMyLCBjMykpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2FzZSAncmVjMjAyMCc6IHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIG5ld0NvbG9yRnJvbUFycmF5KGEsIGNvbnZlcnQueHl6ZDUwVG9TcmdiKC4uLmNvbnZlcnQucmVjMjAyMFRvWHl6ZDUwKGMxLCBjMiwgYzMpKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIFhZWiBjb2xvciBzcGFjZXNcbiAgICAgICAgICAgICAgICBjYXNlICd4eXonOlxuICAgICAgICAgICAgICAgIGNhc2UgJ3h5ei1kNjUnOiB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBuZXdDb2xvckZyb21BcnJheShhLCBjb252ZXJ0Lnh5emQ1MFRvU3JnYiguLi5jb252ZXJ0Lnh5emQ2NVRvRDUwKGMxLCBjMiwgYzMpKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhc2UgJ3h5ei1kNTAnOiB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBuZXdDb2xvckZyb21BcnJheShhLCBjb252ZXJ0Lnh5emQ1MFRvU3JnYihjMSwgYzIsIGMzKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZGVmYXVsdDpcbiAgICB9XG4gICAgdGhyb3cgbmV3IEVycm9yKGBDb2xvci5wYXJzZSgpOiBpbnZhbGlkIENTUyBjb2xvcjogXCIke2NvbG9yfVwiYCk7XG59XG4vKipcbiAqIEFjY2VwdHM6IFwiNTAlXCIsIFwiMTI4XCJcbiAqIGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0NTUy9jb2xvcl92YWx1ZS9yZ2IjdmFsdWVzXG4gKiBAcmV0dXJucyBhIHZhbHVlIGluIHRoZSAwIHRvIDI1NSByYW5nZVxuICovXG5mdW5jdGlvbiBwYXJzZUNvbG9yQ2hhbm5lbChjaGFubmVsKSB7XG4gICAgaWYgKGNoYW5uZWwuY2hhckNvZGVBdChjaGFubmVsLmxlbmd0aCAtIDEpID09PSBQRVJDRU5UKSB7XG4gICAgICAgIHJldHVybiBNYXRoLnJvdW5kKChwYXJzZUZsb2F0KGNoYW5uZWwpIC8gMTAwKSAqIDI1NSk7XG4gICAgfVxuICAgIHJldHVybiBNYXRoLnJvdW5kKHBhcnNlRmxvYXQoY2hhbm5lbCkpO1xufVxuLyoqXG4gKiBBY2NlcHRzOiBcIjUwJVwiLCBcIi41XCIsIFwiMC41XCJcbiAqIGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0NTUy9hbHBoYS12YWx1ZVxuICogQHJldHVybnMgYSB2YWx1ZSBpbiB0aGUgWzAsIDI1NV0gcmFuZ2VcbiAqL1xuZnVuY3Rpb24gcGFyc2VBbHBoYUNoYW5uZWwoY2hhbm5lbCkge1xuICAgIHJldHVybiBNYXRoLnJvdW5kKHBhcnNlQWxwaGFWYWx1ZShjaGFubmVsKSAqIDI1NSk7XG59XG4vKipcbiAqIEFjY2VwdHM6IFwiNTAlXCIsIFwiLjVcIiwgXCIwLjVcIlxuICogaHR0cHM6Ly9kZXZlbG9wZXIubW96aWxsYS5vcmcvZW4tVVMvZG9jcy9XZWIvQ1NTL2FscGhhLXZhbHVlXG4gKiBAcmV0dXJucyBhIHZhbHVlIGluIHRoZSBbMCwgMV0gcmFuZ2VcbiAqL1xuZnVuY3Rpb24gcGFyc2VBbHBoYVZhbHVlKGNoYW5uZWwpIHtcbiAgICBpZiAoY2hhbm5lbC5jaGFyQ29kZUF0KDApID09PSBOKSB7XG4gICAgICAgIHJldHVybiAwO1xuICAgIH1cbiAgICBpZiAoY2hhbm5lbC5jaGFyQ29kZUF0KGNoYW5uZWwubGVuZ3RoIC0gMSkgPT09IFBFUkNFTlQpIHtcbiAgICAgICAgcmV0dXJuIHBhcnNlRmxvYXQoY2hhbm5lbCkgLyAxMDA7XG4gICAgfVxuICAgIHJldHVybiBwYXJzZUZsb2F0KGNoYW5uZWwpO1xufVxuLyoqXG4gKiBBY2NlcHRzOiBcIjM2MFwiLCBcIjM2MGRlZ1wiLCBcIjQwMGdyYWRcIiwgXCI2LjI4cmFkXCIsIFwiMXR1cm5cIiwgXCJub25lXCJcbiAqIGh0dHBzOi8vZGV2ZWxvcGVyLm1vemlsbGEub3JnL2VuLVVTL2RvY3MvV2ViL0NTUy9hbmdsZVxuICogQHJldHVybnMgYSB2YWx1ZSBpbiB0aGUgMC4wIHRvIDEuMCByYW5nZVxuICovXG5mdW5jdGlvbiBwYXJzZUFuZ2xlKGFuZ2xlKSB7XG4gICAgbGV0IGZhY3RvciA9IDE7XG4gICAgc3dpdGNoIChhbmdsZS5jaGFyQ29kZUF0KGFuZ2xlLmxlbmd0aCAtIDEpKSB7XG4gICAgICAgIGNhc2UgRToge1xuICAgICAgICAgICAgLy8gJ25vbmUnXG4gICAgICAgICAgICByZXR1cm4gMDtcbiAgICAgICAgfVxuICAgICAgICBjYXNlIEQ6IHtcbiAgICAgICAgICAgIC8vICdyYWQnLCAnZ3JhZCdcbiAgICAgICAgICAgIGlmIChhbmdsZS5jaGFyQ29kZUF0KE1hdGgubWF4KDAsIGFuZ2xlLmxlbmd0aCAtIDQpKSA9PT0gRykge1xuICAgICAgICAgICAgICAgIC8vICdncmFkJ1xuICAgICAgICAgICAgICAgIGZhY3RvciA9IDQwMDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIC8vICdyYWQnXG4gICAgICAgICAgICAgICAgZmFjdG9yID0gMiAqIE1hdGguUEk7IC8vIFRBVVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgICAgY2FzZSBOOiB7XG4gICAgICAgICAgICAvLyAndHVybidcbiAgICAgICAgICAgIGZhY3RvciA9IDE7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgfVxuICAgICAgICAvLyBjYXNlIEc6IC8vICdkZWcnLCBidXQgbm8gbmVlZCB0byBjaGVjayBhcyBpdCdzIGFsc28gdGhlIGRlZmF1bHRcbiAgICAgICAgZGVmYXVsdDoge1xuICAgICAgICAgICAgZmFjdG9yID0gMzYwO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBwYXJzZUZsb2F0KGFuZ2xlKSAvIGZhY3Rvcjtcbn1cbi8qKlxuICogQWNjZXB0czogXCIxMDAlXCIsIFwibm9uZVwiXG4gKiBAcmV0dXJucyBhIHZhbHVlIGluIHRoZSAwLjAgdG8gMS4wIHJhbmdlXG4gKi9cbmZ1bmN0aW9uIHBhcnNlUGVyY2VudGFnZSh2YWx1ZSkge1xuICAgIGlmICh2YWx1ZS5jaGFyQ29kZUF0KDApID09PSBOKSB7XG4gICAgICAgIHJldHVybiAwO1xuICAgIH1cbiAgICByZXR1cm4gcGFyc2VGbG9hdCh2YWx1ZSkgLyAxMDA7XG59XG4vKipcbiAqIEFjY2VwdHM6IFwiMS4wXCIsIFwiMTAwJVwiLCBcIm5vbmVcIlxuICogQHJldHVybnMgYSB2YWx1ZSBpbiB0aGUgMC4wIHRvIDEuMCByYW5nZVxuICovXG5mdW5jdGlvbiBwYXJzZVBlcmNlbnRhZ2VPclZhbHVlKHZhbHVlKSB7XG4gICAgaWYgKHZhbHVlLmNoYXJDb2RlQXQoMCkgPT09IE4pIHtcbiAgICAgICAgcmV0dXJuIDA7XG4gICAgfVxuICAgIGlmICh2YWx1ZS5jaGFyQ29kZUF0KHZhbHVlLmxlbmd0aCAtIDEpID09PSBQRVJDRU5UKSB7XG4gICAgICAgIHJldHVybiBwYXJzZUZsb2F0KHZhbHVlKSAvIDEwMDtcbiAgICB9XG4gICAgcmV0dXJuIHBhcnNlRmxvYXQodmFsdWUpO1xufVxuLyoqXG4gKiBBY2NlcHRzOiBcIjEwMFwiLCBcIjEwMCVcIiwgXCJub25lXCJcbiAqIEByZXR1cm5zIGEgdmFsdWUgaW4gdGhlIC1AcmFuZ2UgdG8gQHJhbmdlIHJhbmdlXG4gKi9cbmZ1bmN0aW9uIHBhcnNlUGVyY2VudGFnZUZvcih2YWx1ZSwgcmFuZ2UpIHtcbiAgICBpZiAodmFsdWUuY2hhckNvZGVBdCgwKSA9PT0gTikge1xuICAgICAgICByZXR1cm4gMDtcbiAgICB9XG4gICAgaWYgKHZhbHVlLmNoYXJDb2RlQXQodmFsdWUubGVuZ3RoIC0gMSkgPT09IFBFUkNFTlQpIHtcbiAgICAgICAgcmV0dXJuIHBhcnNlRmxvYXQodmFsdWUpIC8gMTAwICogcmFuZ2U7XG4gICAgfVxuICAgIHJldHVybiBwYXJzZUZsb2F0KHZhbHVlKTtcbn1cbi8vIEhTTCBmdW5jdGlvbnNcbmZ1bmN0aW9uIGh1ZVRvUkdCKHAsIHEsIHQpIHtcbiAgICBpZiAodCA8IDApIHtcbiAgICAgICAgdCArPSAxO1xuICAgIH1cbiAgICA7XG4gICAgaWYgKHQgPiAxKSB7XG4gICAgICAgIHQgLT0gMTtcbiAgICB9XG4gICAgO1xuICAgIGlmICh0IDwgMSAvIDYpIHtcbiAgICAgICAgcmV0dXJuIHAgKyAocSAtIHApICogNiAqIHQ7XG4gICAgfVxuICAgIDtcbiAgICBpZiAodCA8IDEgLyAyKSB7XG4gICAgICAgIHJldHVybiBxO1xuICAgIH1cbiAgICA7XG4gICAgaWYgKHQgPCAyIC8gMykge1xuICAgICAgICByZXR1cm4gcCArIChxIC0gcCkgKiAoMiAvIDMgLSB0KSAqIDY7XG4gICAgfVxuICAgIDtcbiAgICB7XG4gICAgICAgIHJldHVybiBwO1xuICAgIH1cbiAgICA7XG59XG4vLyBIV0IgZnVuY3Rpb25zXG5mdW5jdGlvbiBod2JBcHBseShjaGFubmVsLCB3LCBiKSB7XG4gICAgbGV0IHJlc3VsdCA9IGNoYW5uZWwgLyAyNTU7XG4gICAgcmVzdWx0ICo9IDEgLSB3IC0gYjtcbiAgICByZXN1bHQgKz0gdztcbiAgICByZXR1cm4gTWF0aC5yb3VuZChyZXN1bHQgKiAyNTUpO1xufVxuZnVuY3Rpb24gY2xhbXAodmFsdWUpIHtcbiAgICByZXR1cm4gTWF0aC5tYXgoMCwgTWF0aC5taW4oMjU1LCB2YWx1ZSkpO1xufVxuZnVuY3Rpb24gbmV3Q29sb3JGcm9tQXJyYXkoYSwgcmdiKSB7XG4gICAgY29uc3QgciA9IGNsYW1wKE1hdGgucm91bmQocmdiWzBdICogMjU1KSk7XG4gICAgY29uc3QgZyA9IGNsYW1wKE1hdGgucm91bmQocmdiWzFdICogMjU1KSk7XG4gICAgY29uc3QgYiA9IGNsYW1wKE1hdGgucm91bmQocmdiWzJdICogMjU1KSk7XG4gICAgcmV0dXJuICgwLCBjb3JlXzEubmV3Q29sb3IpKHIsIGcsIGIsIGEpO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFyc2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/color-bits@1.1.0/node_modules/color-bits/build/parse.js\n");

/***/ })

};
;