"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { X, Check<PERSON>ircle, AlertCircle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";

interface ForgotPasswordDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (email: string) => Promise<{ success?: boolean; message?: string }>;
}

export function ForgotPasswordDialog({ open, onOpenChange, onSubmit }: ForgotPasswordDialogProps) {
  const [email, setEmail] = useState("");
  const [status, setStatus] = useState<{ success?: boolean; message?: string }>({});

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setStatus({});

    if (!email || !email.includes('@')) {
      setStatus({
        success: false,
        message: "Please enter a valid email address"
      });
      return;
    }

    const result = await onSubmit(email);
    setStatus(result);
  };

  const handleClose = () => {
    setEmail("");
    setStatus({});
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md rounded-md bg-[#1a1a1a] border border-gray-700 shadow-lg">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-medium text-white">Reset Password</DialogTitle>
            <button
              onClick={handleClose}
              className="rounded-full p-1 hover:bg-gray-800 transition-colors"
            >
              <X className="h-4 w-4 text-gray-400" />
            </button>
          </div>
          <DialogDescription className="text-gray-400">
            Enter your email address and we'll send you a link to reset your password.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <Input
            id="forgot-password-email"
            type="email"
            placeholder="Email address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="h-12 rounded-md bg-[#222222] border-gray-700 text-white"
            required
          />

          {status.message && (
            <div className={`p-4 rounded-md flex items-center gap-3 ${
              status.success
                ? "bg-green-900/20 border border-green-900/30 text-green-400"
                : "bg-red-900/20 border border-red-900/30 text-red-400"
            }`}>
              {status.success ? (
                <CheckCircle className="h-5 w-5 flex-shrink-0" />
              ) : (
                <AlertCircle className="h-5 w-5 flex-shrink-0" />
              )}
              <span className="text-sm">{status.message}</span>
            </div>
          )}

          <DialogFooter className="flex sm:justify-start gap-3 pt-2">
            <button
              type="submit"
              className="h-12 px-6 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-all"
            >
              Send Reset Link
            </button>
            <button
              type="button"
              onClick={handleClose}
              className="h-12 px-6 rounded-md border border-gray-700 bg-[#1a1a1a] hover:bg-gray-800 text-white transition-all"
            >
              Cancel
            </button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
