import { useState } from 'react';
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

const availableAgents = [
  { id: "<PERSON><PERSON>", name: "<PERSON><PERSON>", role: "CEO" },
  { id: "<PERSON>", name: "<PERSON>", role: "<PERSON><PERSON>" },
  { id: "<PERSON>", name: "<PERSON>", role: "Project Manager" },
  { id: "<PERSON>", name: "<PERSON>", role: "<PERSON><PERSON><PERSON>" }
];

export interface AgentSelectorProps {
  selectedAgents: string[];
  onAgentSelectionChange: (agents: string[]) => void;
}

export function AgentSelector({ selectedAgents, onAgentSelectionChange }: AgentSelectorProps) {
  const [open, setOpen] = useState(false);

  const toggleAgent = (agentId: string) => {
    if (selectedAgents.includes(agentId)) {
      onAgentSelectionChange(selectedAgents.filter(id => id !== agentId));
    } else {
      onAgentSelectionChange([...selectedAgents, agentId]);
    }
  };

  return (
    <div className="space-y-4">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {selectedAgents.length === 0 
              ? "Select agents..." 
              : `${selectedAgents.length} agent${selectedAgents.length > 1 ? 's' : ''} selected`}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput placeholder="Search agents..." />
            <CommandEmpty>No agents found.</CommandEmpty>
            <CommandGroup>
              {availableAgents.map((agent) => (
                <CommandItem
                  key={agent.id}
                  value={agent.id}
                  onSelect={() => toggleAgent(agent.id)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedAgents.includes(agent.id) ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <span className="font-medium">{agent.name}</span>
                  <span className="ml-2 text-xs text-muted-foreground">({agent.role})</span>
                </CommandItem>
              ))}
            </CommandGroup>
          </Command>
        </PopoverContent>
      </Popover>
      
      {selectedAgents.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {selectedAgents.map(agentId => {
            const agent = availableAgents.find(a => a.id === agentId);
            return (
              <div key={agentId} className="bg-primary/10 text-primary rounded-full px-3 py-1 text-sm flex items-center">
                {agent?.name || agentId}
                <button 
                  className="ml-2 text-primary/70 hover:text-primary"
                  onClick={() => toggleAgent(agentId)}
                >
                  ×
                </button>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
