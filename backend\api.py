from fastapi import <PERSON><PERSON><PERSON>, Request, HTT<PERSON>Exception, Depends, Body, Header, BackgroundTasks, Response, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional, Union, AsyncGenerator
from contextlib import asynccontextmanager

# AgentPress imports
from agentpress.thread_manager import ThreadManager
from agentpress.agent_communication import AgentCommunicationManager
from agentpress.response_processor import ResponseProcessor, ProcessorConfig
from agentpress.tool import Tool, ToolResult
from agentpress.tool_registry import ToolRegistry

# Agent prompt imports
from agent.prompt import get_system_prompt, SYSTEM_PROMPT
from agent.ceoAgent.prompt import SYSTEM_PROMPT as CEO_PROMPT
from agent.developerAgent.prompt import SYSTEM_PROMPT as DEVELOPER_PROMPT
from agent.marketingAgent.prompt import SYSTEM_PROMPT as MARKETING_PROMPT
from agent.productAgent.prompt import SYSTEM_PROMPT as PRODUCT_PROMPT
from agent.designAgent.prompt import SYSTEM_PROMPT as DESIGN_PROMPT
from agent.financeAgent.prompt import SYSTEM_PROMPT as FINANCE_PROMPT
from agent.researchAgent.prompt import SYSTEM_PROMPT as RESEARCH_PROMPT
from agent.salesAgent.prompt import SYSTEM_PROMPT as SALES_PROMPT

# Service imports
from services.supabase import DBConnection
from services.llm import make_llm_api_call

# Utility imports
from datetime import datetime, timezone, timedelta
from dotenv import load_dotenv
from utils.config import config, EnvMode
import asyncio
from utils.logger import logger
import time
import uuid
import json
import os
from collections import OrderedDict

# Import the API modules
from agent.routes import router as agent_router
from agent import api as agent_api
from sandbox import api as sandbox_api
from services import billing as billing_api
from projects import api as projects_api

# Import agent capabilities manager
from agent_capabilities import AgentCapabilityManager, AGENT_CAPABILITIES

# Load environment variables (these will be available through config)
load_dotenv()

# Initialize managers
db = DBConnection()
thread_manager = None
agent_communication_manager = None
response_processor = None
tool_registry = None
instance_id = "single"

# Model configurations
CLAUDE_3_7_SONNET = {
    "model": "anthropic/claude-3-7-sonnet-20250219",  # Use the correct model name with provider prefix
    "temperature": 0.7,
    "max_tokens": 4000,
    "thinking": True,
    "top_p": 0.9,
    "stream": True
}

GPT_4O = {
    "model": "anthropic/claude-3-7-sonnet-latest",  # Include the provider name 'anthropic/' as required by litellm
    "temperature": 0.7,
    "max_tokens": 4000,
    "top_p": 0.9,
    "stream": True
}

# Rate limiter state
ip_tracker = OrderedDict()
MAX_CONCURRENT_IPS = 25

# Helper function to get system prompts for different agent types
def get_system_prompt_for_agent(agent_name: str, agents: List[str] = None, collaboration_mode: bool = False) -> str:
    """Get the system prompt for a specific agent type or a collaborative prompt for multiple agents.
    
    Args:
        agent_name: The primary agent name (e.g., 'Kenard', 'Alex', etc.)
        agents: Optional list of all agent names to include in collaboration mode
        collaboration_mode: Whether to create a collaborative prompt for multiple agents
        
    Returns:
        The system prompt for the specified agent(s)
    """
    # Direct mapping of agent names to prompts
    agent_prompt_map = {
        "Kenard": CEO_PROMPT,
        "Alex": DEVELOPER_PROMPT,
        "Chloe": MARKETING_PROMPT,
        "Mark": PRODUCT_PROMPT,
        "Maisie": DESIGN_PROMPT,
        "Finley": FINANCE_PROMPT,
        "Garek": RESEARCH_PROMPT,
        "Sam": SALES_PROMPT
    }
    
    # Agent descriptions for collaboration mode
    agent_descriptions = {
        "Kenard": "CEO - Strategic leadership and decision making",
        "Alex": "Developer - Software development and technical implementation",
        "Chloe": "Marketing - Marketing strategy and content creation",
        "Mark": "Product - Product management and user experience",
        "Maisie": "Design - User interface and experience design",
        "Finley": "Finance - Financial planning and analysis",
        "Garek": "Research - Market and user research",
        "Sam": "Sales - Sales strategy and customer relationships"
    }
    
    # If collaboration mode is enabled and multiple agents are specified
    if collaboration_mode and agents and len(agents) > 1:
        # Create a collaborative system prompt
        collaboration_intro = "You are a team of AI agents collaborating to solve problems. "
        collaboration_intro += "Each agent has a specific role and expertise. "
        collaboration_intro += "The following agents are participating in this conversation:\n\n"
        
        # Add each agent's description
        for agent in agents:
            collaboration_intro += f"- {agent}: {agent_descriptions.get(agent, 'Specialist')}\n"
        
        collaboration_intro += "\nWhen responding, clearly indicate which agent is speaking by starting with their name in bold. "
        collaboration_intro += "For example: **Kenard (CEO):** [Your response as Kenard]\n\n**Alex (Developer):** [Your response as Alex]\n\n"
        collaboration_intro += "Each agent should provide their unique perspective based on their role and expertise. "
        collaboration_intro += "Ensure that all relevant agents contribute to the response based on the query's needs.\n\n"
        
        # Get the base prompts for all agents in the conversation
        agent_prompts = []
        
        # Add a brief summary of each agent's role and expertise
        for agent in agents:
            if agent in agent_prompt_map:
                # Extract just the first few paragraphs of each agent's prompt to avoid making the prompt too long
                full_prompt = agent_prompt_map[agent]
                # Extract the first section that defines the agent's identity
                identity_section = full_prompt.split('##')[0] if '##' in full_prompt else full_prompt
                agent_prompts.append(f"\n--- {agent}'s Role ---\n{identity_section}\n")
            else:
                # For agents without specific prompts, add a generic description
                agent_prompts.append(f"\n--- {agent}'s Role ---\nYou are {agent}, a specialist in your field.\n")
        
        # Combine all agent prompts
        combined_prompts = "\n".join(agent_prompts)
        
        # Combine with collaboration instructions
        return collaboration_intro + "\n" + combined_prompts
    
    # For single agent mode, return the specific agent prompt if available
    if agent_name in agent_prompt_map:
        return agent_prompt_map[agent_name]
    
    # Fallback to using the central prompt.py file
    # Map agent names to roles
    agent_role_map = {
        "Kenard": ["CEO"],
        "Alex": ["Developer"],
        "Chloe": ["Marketing"],
        "Mark": ["Product"],
        "Maisie": ["Design"],
        "Finley": ["Finance"],
        "Garek": ["Research"],
        "Sam": ["Sales"]
    }
    
    # Get the roles for the agent
    agent_roles = agent_role_map.get(agent_name, ["Assistant"])
    
    # Get the system prompt using the central prompt.py file
    prompt_data = get_system_prompt(
        agent_name=agent_name,
        agent_roles=agent_roles,
        autonomous_mode=False,
        approval_mode=False,
        real_time_triggers=False,
        sandbox_mode=True,
        collaboration_mode='false'
    )
    
    # Return the prompt content
    return prompt_data["content"]

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global thread_manager, tool_registry, response_processor, agent_communication_manager
    logger.info(f"Starting up FastAPI application with instance ID: {instance_id} in {config.ENV_MODE.value} mode")

    try:
        # Initialize database
        await db.initialize()
        
        # Initialize AgentPress components
        thread_manager = ThreadManager()
        tool_registry = ToolRegistry()
        agent_communication_manager = AgentCommunicationManager()
        response_processor = ResponseProcessor(
            tool_registry=tool_registry,
            add_message_callback=thread_manager.add_message
        )

        # Register tools with the thread manager
        from agent.tools import CodeExecutionTool
        thread_manager.add_tool(CodeExecutionTool)
        logger.info("Code execution tool registered with thread manager")

        # Initialize the agent API with shared resources
        agent_api.initialize(
            db,
            instance_id
        )

        # Initialize the sandbox API with shared resources
        sandbox_api.initialize(db)

        # Initialize Redis connection
        from services import redis
        try:
            await redis.initialize_async()
            logger.info("Redis connection initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Redis connection: {e}")
            # Continue without Redis - the application will handle Redis failures gracefully

        # Start background tasks
        # Commented out as this function doesn't exist in the new agent API structure
        # asyncio.create_task(agent_api.restore_running_agent_runs())

        yield

        # Clean up agent resources
        logger.info("Cleaning up agent resources")
        await agent_api.cleanup()

        # Clean up Redis connection
        try:
            logger.info("Closing Redis connection")
            await redis.close()
            logger.info("Redis connection closed successfully")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")

        # Clean up database connection
        logger.info("Disconnecting from database")
        await db.disconnect()
    except Exception as e:
        logger.error(f"Error during application startup: {e}")
        raise

app = FastAPI(lifespan=lifespan)

@app.middleware("http")
async def log_requests_middleware(request: Request, call_next):
    start_time = time.time()
    client_ip = request.client.host
    method = request.method
    url = str(request.url)
    path = request.url.path
    query_params = str(request.query_params)

    # Log the incoming request
    logger.info(f"Request started: {method} {path} from {client_ip} | Query: {query_params}")

    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        logger.debug(f"Request completed: {method} {path} | Status: {response.status_code} | Time: {process_time:.2f}s")
        return response
    except Exception as e:
        process_time = time.time() - start_time
        logger.error(f"Request failed: {method} {path} | Error: {str(e)} | Time: {process_time:.2f}s")
        raise

# Define allowed origins - allow all origins for development
allowed_origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# Define direct mock implementations of the agent API endpoints
@app.post("/api/agent/initiate")
async def initiate_agent(request: Request):
    """Production implementation of agent initiation using AgentPress framework."""
    try:
        # Parse the request body
        body = await request.json()
        # Support both 'prompt' and 'message' fields for backward compatibility
        prompt = body.get("prompt", body.get("message", ""))
        model_name = body.get("model_name", body.get("model", "claude-3-7-sonnet-latest"))
        
        # Handle different agent formats
        if "agents" in body:
            agents = body.get("agents", ["Kenard"])
        elif "agent_type" in body:
            # Map agent_type to agent name
            agent_type = body.get("agent_type", "ceo")
            agent_map = {
                "ceo": "Kenard",
                "developer": "Alex",
                "marketing": "Chloe",
                "product": "Mark"
            }
            agents = [agent_map.get(agent_type, "Kenard")]
        else:
            agents = ["Kenard"]  # Default to CEO agent
            
        collaboration_mode = body.get("collaboration_mode", False)
        enable_thinking = body.get("enable_thinking", body.get("metadata", {}).get("enable_thinking", False))
        
        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt or message is required")
        
        logger.info(f"Initiating agent conversation: model={model_name}, agents={agents}, collaboration_mode={collaboration_mode}")
        
        # Create a new thread
        client = await db.client
        thread_id = str(uuid.uuid4())
        agent_run_id = str(uuid.uuid4())
        
        # Create thread in database - without metadata column which doesn't exist in schema
        thread_data = {
            'thread_id': thread_id,
            'is_public': True  # Make it public for testing purposes
        }
        
        # Insert the thread
        await client.table('threads').insert(thread_data).execute()
        
        # Add system prompt based on selected agents
        primary_agent = agents[0] if agents else "Kenard"
        system_prompt = get_system_prompt_for_agent(primary_agent)
        
        # Add system message to thread
        await thread_manager.add_message(
            thread_id=thread_id,
            type="system",
            content=system_prompt,
            is_llm_message=False,
            metadata={
                "agent": primary_agent,
                "role": "system"
            }
        )
        
        # Add user message to thread
        await thread_manager.add_message(
            thread_id=thread_id,
            type="user",
            content=prompt,
            is_llm_message=False,
            metadata={
                "role": "user"
            }
        )
        
        # Return a successful response
        return AgentInitiateResponse(
            thread_id=thread_id,
            agent_run_id=agent_run_id,
            agents_used=agents,
            collaboration_mode=collaboration_mode
        )
    except Exception as e:
        logger.error(f"Error in agent initiation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/agent/stream/{thread_id}")
async def stream_agent_response(thread_id: str):
    """Production implementation of agent streaming using AgentPress framework."""
    try:
        logger.info(f"Streaming response for thread {thread_id}")
        
        # Check if thread exists, if not return an error
        client = await db.client
        result = await client.table('threads').select('*').eq('thread_id', thread_id).execute()
        
        if not result.data or len(result.data) == 0:
            return JSONResponse(status_code=404, content={"error": f"Thread {thread_id} not found"})
        
        # Get the last user message to determine context
        messages_result = await client.table('messages').select('*').eq('thread_id', thread_id).order('created_at', desc=True).limit(10).execute()
        
        last_user_message = None
        for msg in messages_result.data:
            if msg.get('type') == 'user':
                last_user_message = msg.get('content')
                break
        
        # Create a message ID for the assistant's response
        message_id = str(uuid.uuid4())
        
        async def generate_response_stream():
            try:
                # Initialize response generator from thread manager
                # Create a basic system prompt dictionary
                system_prompt_dict = {
                    "role": "system",
                    "content": "You are a helpful AI assistant. Respond to the user's questions in a clear and concise manner."
                }
                
                # Create a ProcessorConfig object
                from agentpress.response_processor import ProcessorConfig
                
                processor_config = ProcessorConfig(
                    native_tool_calling=True,  # Enable native tool calling
                    xml_tool_calling=False,     # Disable XML tool calling
                    max_xml_tool_calls=0,      # No XML tool calls
                    execute_tools=True         # Enable tool execution
                )
                
                response_gen = await thread_manager.run_thread(
                    thread_id=thread_id,
                    system_prompt=system_prompt_dict,
                    llm_model=CLAUDE_3_7_SONNET["model"],
                    llm_temperature=CLAUDE_3_7_SONNET["temperature"],
                    llm_max_tokens=CLAUDE_3_7_SONNET["max_tokens"],
                    stream=True,
                    enable_thinking=CLAUDE_3_7_SONNET.get("thinking", False),
                    tool_choice="auto",
                    processor_config=processor_config
                )
                
                # Stream each chunk from the response generator
                full_response = ""
                chunk_count = 0
                
                # Add debug message at the start
                logger.info(f"Starting to process response stream for thread {thread_id}")
                
                # If we don't get any chunks, we'll need to generate a default response
                has_received_chunks = False
                
                async for chunk in response_gen:
                    # Debug: Log each chunk we receive
                    chunk_count += 1
                    logger.info(f"Received chunk {chunk_count} for thread {thread_id}: {str(chunk)[:100]}...")
                    has_received_chunks = True
                    
                    # Process different chunk types
                    if isinstance(chunk, dict):
                        logger.info(f"Processing dict chunk with keys: {chunk.keys()}")

                        # Skip status messages from the response processor
                        if chunk.get("type") == "status":
                            content = chunk.get("content", {})
                            status_type = content.get("status_type", "unknown") if isinstance(content, dict) else "unknown"
                            logger.info(f"Skipping status message type: {status_type}")
                            continue

                        # Also check for status_type in content field
                        content = chunk.get("content", {})
                        if isinstance(content, dict) and content.get("status_type"):
                            logger.info(f"Skipping message with status_type in content: {content.get('status_type')}")
                            continue

                        if chunk.get("type") == "thinking":
                            # Send thinking notification
                            thinking_data = json.dumps({
                                "event": "thinking", 
                                "content": chunk.get("content", "")
                            })
                            yield f"data: {thinking_data}\n\n"
                            logger.info(f"Sent thinking event for thread {thread_id}")
                            
                        elif chunk.get("type") == "content":
                            # Send content chunk and accumulate full response
                            content = chunk.get("content", "")
                            
                            # Clean the content to remove internal prompt analysis
                            # This removes text that starts with analysis and ends with the actual response
                            cleaned_content = content
                            if isinstance(content, str) and "This is a" in content and "test message" in content:
                                # Find where the actual response starts (after the internal analysis)
                                parts = content.split("Officer")
                                if len(parts) > 1:
                                    # Keep only the actual response part (after the last "Officer")
                                    cleaned_content = parts[-1].strip()
                                    if cleaned_content.startswith('"'):
                                        cleaned_content = cleaned_content[1:]
                                    logger.info(f"Cleaned internal prompt analysis from content")
                            
                            full_response += cleaned_content
                            chunk_data = json.dumps({
                                "event": "chunk", 
                                "content": cleaned_content
                            })
                            yield f"data: {chunk_data}\n\n"
                            logger.info(f"Sent content chunk for thread {thread_id}: {cleaned_content[:50]}...")
                            
                        elif chunk.get("type") == "tool_call":
                            # Send tool call notification
                            tool_data = json.dumps({
                                "event": "tool_call", 
                                "tool": chunk.get("tool", {})
                            })
                            yield f"data: {tool_data}\n\n"
                            logger.info(f"Sent tool call event for thread {thread_id}")
                            
                        elif chunk.get("type") == "tool_result":
                            # Send tool result notification
                            result_data = json.dumps({
                                "event": "tool_result", 
                                "result": chunk.get("result", {})
                            })
                            yield f"data: {result_data}\n\n"
                            logger.info(f"Sent tool result event for thread {thread_id}")
                            
                        # Handle content in different format (direct content field)
                        elif "content" in chunk:
                            # Skip the final complete message that has stream_status: 'complete'
                            metadata = chunk.get("metadata", "{}")
                            if isinstance(metadata, str):
                                try:
                                    parsed_metadata = json.loads(metadata)
                                    if parsed_metadata.get("stream_status") == "complete":
                                        logger.info(f"Skipping final complete message to avoid duplication")
                                        continue
                                except (json.JSONDecodeError, TypeError):
                                    pass  # Not JSON, continue processing

                            # Skip status messages - they should not be sent to the frontend
                            content = chunk.get("content", "")

                            # Check if this is a status message by looking at the content structure
                            if isinstance(content, dict) and content.get("status_type"):
                                logger.info(f"Skipping status message: {content.get('status_type')}")
                                continue

                            # Also check if the content is a JSON string containing status_type
                            if isinstance(content, str):
                                try:
                                    parsed_content = json.loads(content)
                                    if isinstance(parsed_content, dict) and parsed_content.get("status_type"):
                                        logger.info(f"Skipping JSON status message: {parsed_content.get('status_type')}")
                                        continue
                                except (json.JSONDecodeError, TypeError):
                                    pass  # Not JSON, continue processing as regular content

                            # Clean the content to remove internal prompt analysis
                            cleaned_content = content
                            if isinstance(content, str) and "This is a" in content and "test message" in content:
                                # Find where the actual response starts (after the internal analysis)
                                if "Officer" in content:
                                    parts = content.split("Officer")
                                    if len(parts) > 1:
                                        # Keep only the actual response part (after the last "Officer")
                                        cleaned_content = parts[-1].strip()
                                        if cleaned_content.startswith('"'):
                                            cleaned_content = cleaned_content[1:]
                                        logger.info(f"Cleaned internal prompt analysis from direct content")

                            # Only send non-empty content
                            if cleaned_content and str(cleaned_content).strip():
                                full_response += str(cleaned_content)
                                chunk_data = json.dumps({
                                    "event": "chunk",
                                    "content": str(cleaned_content)
                                })
                                yield f"data: {chunk_data}\n\n"
                                logger.info(f"Sent direct content chunk for thread {thread_id}: {str(cleaned_content)[:50]}...")
                        
                        # Handle delta format (OpenAI style)
                        elif "delta" in chunk and "content" in chunk["delta"]:
                            content = chunk["delta"]["content"]
                            full_response += content
                            chunk_data = json.dumps({
                                "event": "chunk", 
                                "content": content
                            })
                            yield f"data: {chunk_data}\n\n"
                            logger.info(f"Sent delta content chunk for thread {thread_id}: {content[:50]}...")
                            
                    else:
                        # Handle raw text chunks (fallback)
                        raw_content = str(chunk)
                        
                        # Clean the content to remove internal prompt analysis
                        cleaned_content = raw_content
                        if "This is a" in raw_content and "test message" in raw_content:
                            # Find where the actual response starts (after the internal analysis)
                            if "Officer" in raw_content:
                                parts = raw_content.split("Officer")
                                if len(parts) > 1:
                                    # Keep only the actual response part (after the last "Officer")
                                    cleaned_content = parts[-1].strip()
                                    if cleaned_content.startswith('"'):
                                        cleaned_content = cleaned_content[1:]
                                    logger.info(f"Cleaned internal prompt analysis from raw text")
                        
                        full_response += cleaned_content
                        chunk_data = json.dumps({
                            "event": "chunk", 
                            "content": cleaned_content
                        })
                        yield f"data: {chunk_data}\n\n"
                        logger.info(f"Sent raw text chunk for thread {thread_id}: {cleaned_content[:50]}...")
                
                # If we didn't receive any chunks, send a default response
                if not has_received_chunks or not full_response.strip():
                    default_response = "I'm here to help. What can I assist you with?"
                    full_response = default_response
                    chunk_data = json.dumps({
                        "event": "chunk", 
                        "content": default_response
                    })
                    yield f"data: {chunk_data}\n\n"
                    logger.info(f"Sent default response for thread {thread_id} as no chunks were received")
                    
                # Store the response in the database
                try:
                    await thread_manager.add_message(
                        thread_id=thread_id,
                        type="assistant",
                        content=full_response,
                        is_llm_message=True,
                        metadata={
                            "role": "assistant",
                            "model": CLAUDE_3_7_SONNET["model"],
                            "chunk_count": chunk_count
                        }
                    )
                    logger.info(f"Stored assistant response in database for thread {thread_id}")
                except Exception as e:
                    logger.error(f"Error storing assistant response: {str(e)}")

                
                # Signal completion
                done_data = json.dumps({
                    "event": "done", 
                    "message_id": message_id,
                    "thread_id": thread_id
                })
                yield f"data: {done_data}\n\n"
                
                logger.info(f"Completed streaming response for thread {thread_id}")
                
            except Exception as e:
                logger.error(f"Error in response generator: {str(e)}")
                error_data = json.dumps({"event": "error", "message": str(e)})
                yield f"data: {error_data}\n\n"
        
        # Return a streaming response
        return StreamingResponse(
            generate_response_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
            }
        )
    except Exception as e:
        logger.error(f"Error in stream agent endpoint: {str(e)}")
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.post("/api/agent/message")
async def send_message(request: Request):
    """Production implementation of sending a follow-up message using AgentPress framework."""
    try:
        # Parse the request body
        body = await request.json()
        thread_id = body.get("thread_id")
        message = body.get("message")
        metadata = body.get("metadata", {})
        
        if not thread_id or not message:
            raise HTTPException(status_code=400, detail="thread_id and message are required")
        
        logger.info(f"Sending message to thread: {thread_id}, message: {message}")
        
        # Check if thread exists
        client = await db.client
        result = await client.table('threads').select('*').eq('thread_id', thread_id).execute()
        
        if not result.data or len(result.data) == 0:
            raise HTTPException(status_code=404, detail=f"Thread {thread_id} not found")
        
        # Add user message to thread using ThreadManager
        user_message = await thread_manager.add_message(
            thread_id=thread_id,
            type="user",
            content=message,
            is_llm_message=False,
            metadata={
                "role": "user",
                **metadata
            }
        )
        
        if not user_message:
            raise HTTPException(status_code=500, detail="Failed to add user message to thread")
        
        # Get the message ID from the response
        user_message_id = user_message.get('message_id', str(uuid.uuid4()))
        
        # Return a successful response - the actual assistant response will be streamed
        return {
            "thread_id": thread_id,
            "message_id": user_message_id,
            "status": "message_added",
            "message": "Message added to thread. Use the streaming endpoint to get the response."
        }
    except Exception as e:
        logger.error(f"Error in send message: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# In-memory message store implementation
class MessageStore:
    def __init__(self):
        self.messages = {}
    
    def add_message(self, thread_id, message):
        """Add a message to a thread.
        
        Args:
            thread_id: The ID of the thread
            message: The message to add
        """
        if thread_id not in self.messages:
            self.messages[thread_id] = []
        
        self.messages[thread_id].append(message)
    
    def get_messages(self, thread_id):
        """Get all messages for a thread.
        
        Args:
            thread_id: The ID of the thread
            
        Returns:
            A list of messages for the thread
        """
        return self.messages.get(thread_id, [])

# Initialize the message store
message_store = MessageStore()

# Function to make agent responses more human and conversational
def humanize_response(response_text, agent_type):
    """Make agent responses more human and conversational.
    
    Args:
        response_text: The original response text from the agent
        agent_type: The type of agent (e.g., 'ceo', 'developer', etc.)
        
    Returns:
        A more human and conversational response
    """
    # Map agent types to agent names and personalities
    agent_personalities = {
        "ceo": {
            "name": "Kenard",
            "traits": ["strategic", "visionary", "business-focused", "decisive"]
        },
        "developer": {
            "name": "Alex",
            "traits": ["technical", "precise", "implementation-focused", "analytical"]
        },
        "marketing": {
            "name": "Chloe",
            "traits": ["creative", "audience-focused", "messaging-oriented", "persuasive"]
        },
        "product": {
            "name": "Mark",
            "traits": ["user-centric", "requirements-focused", "experience-oriented", "practical"]
        },
        "design": {
            "name": "Maisie",
            "traits": ["visual", "aesthetic", "user-experience-focused", "creative"]
        },
        "finance": {
            "name": "Finley",
            "traits": ["analytical", "numbers-focused", "risk-aware", "precise"]
        },
        "research": {
            "name": "Garek",
            "traits": ["curious", "data-driven", "methodical", "thorough"]
        },
        "sales": {
            "name": "Sam",
            "traits": ["persuasive", "relationship-focused", "goal-oriented", "enthusiastic"]
        }
    }
    
    # Get agent personality based on agent type
    agent_personality = agent_personalities.get(agent_type.lower(), {
        "name": "Assistant",
        "traits": ["helpful", "informative", "friendly"]
    })
    
    agent_name = agent_personality["name"]
    
    # Remove formal phrases and make it more direct
    response_text = response_text.replace(f"As the {agent_type.upper()}", f"As {agent_name}")
    response_text = response_text.replace(f"As a {agent_type}", f"As {agent_name}")
    response_text = response_text.replace("I would recommend", "I recommend")
    response_text = response_text.replace("I would suggest", "I suggest")
    
    # If the response doesn't already start with a personalized greeting, add one
    common_greetings = ["Hi", "Hello", "Hey", "Greetings"]
    has_greeting = any(response_text.startswith(greeting) for greeting in common_greetings)
    
    if not has_greeting and not response_text.startswith(agent_name):
        # Add a simple greeting with the agent's name for the first message
        response_text = f"Hey there! {agent_name} here. {response_text}"
    
    return response_text

@app.get("/api/messages/{thread_id}")
async def get_messages(thread_id: str):
    """Production-ready implementation for getting messages for a thread."""
    try:
        logger.info(f"Getting messages for thread: {thread_id}")
        
        # Get messages for this thread using the MessageStore class
        messages = message_store.get_messages(thread_id)
        
        # Format the messages for the frontend
        formatted_messages = []
        for msg in messages:
            formatted_msg = {
                "id": msg.get("id", str(uuid.uuid4())),
                "role": msg.get("role", "unknown"),
                "content": msg.get("content", ""),
                "created_at": msg.get("created_at", datetime.now().isoformat())
            }
            formatted_messages.append(formatted_msg)
        
        # Return the messages for this thread
        return {
            "thread_id": thread_id,
            "messages": formatted_messages
        }
    except Exception as e:
        logger.error(f"Error getting messages for thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/agent-status/{agent_run_id}")
async def mock_get_agent_status(agent_run_id: str):
    """Mock implementation of getting agent status for testing."""
    try:
        logger.info(f"Mock get agent status: agent_run_id={agent_run_id}")
        
        # Return a mock status
        return {
            "status": "completed",
            "message": "Agent run completed successfully"
        }
    except Exception as e:
        logger.error(f"Error in mock get agent status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Include the sandbox router with a prefix
app.include_router(sandbox_api.router, prefix="/api")

# Include the billing router with a prefix
app.include_router(billing_api.router, prefix="/api")

# Include the projects router
app.include_router(projects_api.router, prefix="/api/projects")

# Agent collaboration endpoints
@app.post("/api/agent/send-message")
async def send_agent_message(
    request: Request,
    message_data: dict = Body(...)
):
    """Send a message from one agent to another with reliable delivery.
    
    This endpoint uses the message queuing system for reliable delivery with retries.
    """
    try:
        # Extract message data
        from_agent_id = message_data.get("from_agent_id")
        to_agent_id = message_data.get("to_agent_id")
        message_type = message_data.get("message_type")
        content = message_data.get("content")
        thread_id = message_data.get("thread_id")
        reference_id = message_data.get("reference_id")
        metadata = message_data.get("metadata")
        priority = message_data.get("priority", 1)
        retry_count = message_data.get("retry_count", 3)
        
        # Validate required fields
        if not all([from_agent_id, to_agent_id, message_type, content]):
            raise HTTPException(status_code=400, detail="Missing required fields")
        
        # Import here to avoid circular imports
        from agent_collaboration import AgentCollaborationManager, MessageType
        
        # Initialize collaboration manager
        collaboration_manager = AgentCollaborationManager()
        
        # Send message with queuing
        message_id = await collaboration_manager.send_message_with_queue(
            from_agent_id=from_agent_id,
            to_agent_id=to_agent_id,
            message_type=MessageType(message_type),
            content=content,
            thread_id=thread_id,
            reference_id=reference_id,
            metadata=metadata,
            priority=priority,
            retry_count=retry_count
        )
        
        return {
            "status": "queued",
            "message_id": message_id,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except ValueError as e:
        logger.error(f"Value error in send_agent_message: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error in send_agent_message: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/agent/message-status/{message_id}")
async def get_message_status(message_id: str):
    """Get the status of a message sent between agents.
    
    Returns the current status, delivery attempts, and any error information.
    """
    try:
        # Import here to avoid circular imports
        from agent_collaboration import AgentCollaborationManager
        
        # Initialize collaboration manager
        collaboration_manager = AgentCollaborationManager()
        
        # Get message status
        status = await collaboration_manager.get_message_status(message_id)
        
        return status
        
    except Exception as e:
        logger.error(f"Error in get_message_status: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/agent/retry-message/{message_id}")
async def retry_failed_message(message_id: str):
    """Retry a failed message from the dead letter queue.
    
    This endpoint allows manual retry of messages that have failed all automatic retry attempts.
    """
    try:
        # Import here to avoid circular imports
        from agent_collaboration import AgentCollaborationManager
        
        # Initialize collaboration manager
        collaboration_manager = AgentCollaborationManager()
        
        # Retry message
        success = await collaboration_manager.retry_dead_letter(message_id)
        
        if success:
            return {
                "status": "retrying",
                "message_id": message_id,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        else:
            raise HTTPException(
                status_code=404, 
                detail=f"Message {message_id} not found in dead letter queue or retry failed"
            )
        
    except Exception as e:
        logger.error(f"Error in retry_failed_message: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/agent/dead-letter-queue")
async def get_dead_letter_queue(
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0)
):
    """Get messages from the dead letter queue.
    
    Returns messages that have failed all retry attempts and require manual intervention.
    """
    try:
        # Import here to avoid circular imports
        from agent_collaboration import AgentCollaborationManager
        
        # Initialize collaboration manager
        collaboration_manager = AgentCollaborationManager()
        
        # Get dead letter queue
        messages = await collaboration_manager.get_dead_letter_queue(limit, offset)
        
        return {
            "count": len(messages),
            "messages": messages,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in get_dead_letter_queue: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/agent/queue-length/{agent_id}")
async def get_agent_queue_length(agent_id: str):
    """Get the length of an agent's message queue.
    
    Returns the number of messages currently queued for an agent.
    """
    try:
        # Import here to avoid circular imports
        from agent_collaboration import AgentCollaborationManager
        
        # Initialize collaboration manager
        collab_manager = AgentCollaborationManager(db, redis)
        
        # Get queue length
        queue_length = await collab_manager.get_queue_length(agent_id)
        
        return {"agent_id": agent_id, "queue_length": queue_length}
    except Exception as e:
        logger.error(f"Error in get_agent_queue_length: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# Agent Capabilities and Task Delegation Endpoints

class TaskDelegationRequest(BaseModel):
    from_agent_id: str
    task_description: str
    required_capabilities: Optional[List[str]] = None
    to_agent_id: Optional[str] = None
    thread_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class TaskAcceptanceRequest(BaseModel):
    agent_id: str
    task_id: str
    thread_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class TaskCompletionRequest(BaseModel):
    agent_id: str
    task_id: str
    result: str
    thread_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

@app.get("/api/agent/capabilities")
async def get_all_agent_capabilities():
    """Get capabilities of all agent roles.
    
    Returns a dictionary mapping agent types to their capabilities.
    """
    try:
        return {"agent_capabilities": AGENT_CAPABILITIES}
    except Exception as e:
        logger.error(f"Error in get_all_agent_capabilities: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/agent/capabilities/{agent_id}")
async def get_agent_capabilities(agent_id: str):
    """Get capabilities of a specific agent.
    
    Returns the capabilities of the specified agent.
    """
    try:
        # Initialize capability manager
        capability_manager = AgentCapabilityManager(comm_manager)
        
        # Get agent capabilities
        capabilities = await capability_manager.get_agent_capabilities(agent_id)
        
        if not capabilities:
            raise HTTPException(status_code=404, detail=f"Agent {agent_id} not found")
            
        return {"agent_id": agent_id, "capabilities": capabilities}
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error in get_agent_capabilities: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/agent/delegate-task")
async def delegate_task(request: TaskDelegationRequest):
    """Delegate a task from one agent to another.
    
    Returns the result of the task delegation.
    """
    try:
        # Initialize capability manager
        capability_manager = AgentCapabilityManager(comm_manager)
        
        # Delegate task
        success, to_agent_id, message_id = await capability_manager.delegate_task(
            from_agent_id=request.from_agent_id,
            task_description=request.task_description,
            required_capabilities=request.required_capabilities,
            to_agent_id=request.to_agent_id,
            thread_id=request.thread_id,
            metadata=request.metadata
        )
        
        if not success:
            raise HTTPException(status_code=400, detail="Task delegation failed")
            
        return {
            "success": success,
            "from_agent_id": request.from_agent_id,
            "to_agent_id": to_agent_id,
            "message_id": message_id,
            "thread_id": request.thread_id
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error in delegate_task: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/agent/accept-task")
async def accept_task(request: TaskAcceptanceRequest):
    """Accept a delegated task.
    
    Returns the result of the task acceptance.
    """
    try:
        # Initialize capability manager
        capability_manager = AgentCapabilityManager(comm_manager)
        
        # Accept task
        success = await capability_manager.accept_task(
            agent_id=request.agent_id,
            task_id=request.task_id,
            thread_id=request.thread_id,
            metadata=request.metadata
        )
        
        if not success:
            raise HTTPException(status_code=400, detail="Task acceptance failed")
            
        return {
            "success": success,
            "agent_id": request.agent_id,
            "task_id": request.task_id,
            "thread_id": request.thread_id
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error in accept_task: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/agent/complete-task")
async def complete_task(request: TaskCompletionRequest):
    """Complete a delegated task and send results back.
    
    Returns the result of the task completion.
    """
    try:
        # Initialize capability manager
        capability_manager = AgentCapabilityManager(comm_manager)
        
        # Complete task
        success = await capability_manager.complete_task(
            agent_id=request.agent_id,
            task_id=request.task_id,
            result=request.result,
            thread_id=request.thread_id,
            metadata=request.metadata
        )
        
        if not success:
            raise HTTPException(status_code=400, detail="Task completion failed")
            
        return {
            "success": success,
            "agent_id": request.agent_id,
            "task_id": request.task_id,
            "thread_id": request.thread_id
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error in complete_task: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

# Add a health check endpoint
@app.get("/api/health")
async def health_check():
    """Health check endpoint to verify API is working."""
    logger.info("Health check endpoint called")
    return {
        "status": "ok", 
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "instance_id": "api-server"
    }

@app.get("/api/system-health")
async def check_system_health():
    """Comprehensive system health check that verifies connections to critical dependencies."""
    logger.info("System health check endpoint called")
    health_status = {
        "status": "ok",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "instance_id": instance_id,
        "components": {}
    }
    
    # Check database connection
    try:
        # Simple query to verify database connection
        # Check if the db object has the execute_query method
        if hasattr(db, 'execute_query'):
            await db.execute_query("SELECT 1")
        elif hasattr(db, 'query'):
            await db.query("SELECT 1")
        elif hasattr(db, 'execute'):
            await db.execute("SELECT 1")
        else:
            # If no known query method exists, just check if db object exists
            if db is not None:
                logger.info("Database object exists but no standard query method found")
            else:
                raise Exception("Database object is None")
                
        health_status["components"]["database"] = {
            "status": "ok",
            "message": "Database connection successful"
        }
    except Exception as e:
        health_status["status"] = "degraded"
        health_status["components"]["database"] = {
            "status": "error",
            "message": f"Database connection failed: {str(e)}"
        }
    
    # Check Redis connection
    try:
        from services import redis
        # Check if redis module has ping method
        if hasattr(redis, 'ping'):
            await redis.ping()
        elif hasattr(redis, 'client') and hasattr(redis.client, 'ping'):
            await redis.client.ping()
        elif hasattr(redis, 'connection') and hasattr(redis.connection, 'ping'):
            await redis.connection.ping()
        elif hasattr(redis, 'redis_client'):
            # Some implementations use a redis_client attribute
            if hasattr(redis.redis_client, 'ping'):
                await redis.redis_client.ping()
            else:
                # Just check if the client exists
                if redis.redis_client is not None:
                    logger.info("Redis client exists but no ping method found")
                else:
                    raise Exception("Redis client is None")
        else:
            # If no known ping method exists, just check if redis module exists
            logger.info("Redis module exists but no standard ping method found")
            
        health_status["components"]["redis"] = {
            "status": "ok",
            "message": "Redis connection successful"
        }
    except Exception as e:
        health_status["status"] = "degraded"
        health_status["components"]["redis"] = {
            "status": "error",
            "message": f"Redis connection failed: {str(e)}"
        }
    
    # Check LLM service availability
    try:
        # Just check if API keys are configured
        anthropic_key = config.ANTHROPIC_API_KEY
        if anthropic_key and len(anthropic_key) > 10:
            health_status["components"]["llm_service"] = {
                "status": "ok",
                "message": "LLM service credentials configured"
            }
        else:
            health_status["components"]["llm_service"] = {
                "status": "warning",
                "message": "LLM service credentials may not be properly configured"
            }
    except Exception as e:
        health_status["status"] = "degraded"
        health_status["components"]["llm_service"] = {
            "status": "error",
            "message": f"LLM service check failed: {str(e)}"
        }
    
    return health_status

# Include the project agents router with a prefix
# Temporarily commented out to fix circular import
# app.include_router(project_agents_api.router, prefix="/api")

# Define Pydantic models for request/response handling
class AgentInitiateRequest(BaseModel):
    prompt: str
    model_name: str = "claude-3-7-sonnet-latest"  # Default to claude-3-7-sonnet-latest
    agents: List[str] = ["Kenard"]  # Default to CEO agent
    collaboration_mode: bool = False
    enable_thinking: bool = False

class AgentInitiateResponse(BaseModel):
    thread_id: str
    agent_run_id: str
    response: Optional[str] = None
    agents_used: List[str]
    collaboration_mode: bool

# Agent initiation endpoint
@app.post("/agent/initiate", response_model=AgentInitiateResponse)
async def agent_initiate(request: Request):
    try:
        # Parse request body
        body = await request.json()
        
        # Extract parameters from request
        request_data = AgentInitiateRequest(**body)
        prompt = request_data.prompt
        model_name = request_data.model_name
        agents = request_data.agents
        collaboration_mode = request_data.collaboration_mode
        enable_thinking = request_data.enable_thinking
        
        # Log the request details
        logger.info(f"Agent initiate called with prompt: {prompt}")
        logger.info(f"Model: {model_name}")
        logger.info(f"Agents: {agents}")
        logger.info(f"Collaboration mode: {collaboration_mode}")
        logger.info(f"Enable thinking: {enable_thinking}")
        
        # Generate thread ID and agent run ID
        thread_id = str(uuid.uuid4())
        agent_run_id = str(uuid.uuid4())
        
        # Create a thread in the thread manager
        try:
            await thread_manager.create_thread(thread_id)
            logger.info(f"Created new thread with ID: {thread_id}")
            
            # Get the primary agent (first in the list)
            primary_agent = agents[0] if agents else 'Kenard'
            
            # Get the system prompt using our improved function that handles collaboration mode
            system_prompt = get_system_prompt_for_agent(
                agent_name=primary_agent,
                agents=agents,
                collaboration_mode=collaboration_mode
            )
            
            if collaboration_mode and len(agents) > 1:
                logger.info(f"Created collaborative system prompt for {len(agents)} agents: {', '.join(agents)}")
            else:
                logger.info(f"Using system prompt for single agent: {primary_agent}")
            
            # Add system message to thread
            await thread_manager.add_message(thread_id, "system", system_prompt)
            logger.info(f"Added system prompt to thread")
            
            # Add user message to thread
            await thread_manager.add_message(thread_id, "user", prompt)
            logger.info(f"Added user message to thread: {thread_id}")
            
            # Configure the model parameters based on the requested model
            model_config = {
                "model": model_name,
                "temperature": 0.7,
                "max_tokens": 4000,
                "top_p": 0.9,
                "stream": False  # Non-streaming for initial response
            }
            
            # Create processor config with supported parameters only
            processor_config = ProcessorConfig()
            # Note: ProcessorConfig doesn't support 'stream' or 'thinking' parameters
            
            # Run the thread to get initial response
            response_text = ""
            if not collaboration_mode:
                # For single agent mode, get a complete response
                response_generator = await thread_manager.run_thread(
                    thread_id=thread_id,
                    system_prompt={'role': 'system', 'content': system_prompt},
                    stream=False,
                    llm_model=model_config["model"],
                    llm_temperature=model_config["temperature"],
                    llm_max_tokens=model_config["max_tokens"],
                    processor_config=processor_config,
                    enable_thinking=enable_thinking
                )
                
                # Collect the response from the generator
                response_text = ""
                try:
                    # Process the async generator to get the complete response
                    async for chunk in response_generator:
                        if chunk.get('type') == 'content' and 'content' in chunk:
                            response_text += chunk['content']
                except Exception as e:
                    logger.error(f"Error processing response generator: {str(e)}")
                    response_text = "I apologize, but I encountered an error while generating a response."
                    
                logger.info(f"Generated initial response for thread: {thread_id}")
            
            # Prepare the response
            response = AgentInitiateResponse(
                thread_id=thread_id,
                agent_run_id=agent_run_id,
                response=response_text if not collaboration_mode else None,
                agents_used=agents,
                collaboration_mode=collaboration_mode
            )
            
            return response.dict()
            
        except Exception as e:
            logger.error(f"Error in thread operations: {str(e)}")
            return JSONResponse(status_code=500, content={"error": f"Thread operations failed: {str(e)}"})
            
    except Exception as e:
        logger.error(f"Error in agent_initiate: {str(e)}")
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.get("/api/health")
async def health_check():
    """Health check endpoint to verify API is working."""
    logger.info("Health check endpoint called")
    return {
        "status": "ok",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "instance_id": instance_id
    }

# Endpoint to send a follow-up message to an existing thread
@app.post("/agent/message")
async def send_message(request: Request):
    try:
        # Parse request body
        body = await request.json()
        thread_id = body.get('thread_id')
        content = body.get('content')
        model_name = body.get('model_name', 'claude-3-7-sonnet-latest')
        enable_thinking = body.get('enable_thinking', False)
        
        if not thread_id or not content:
            return JSONResponse(status_code=400, content={"error": "thread_id and content are required"})
        
        logger.info(f"Sending message to thread {thread_id}: {content}")
        
        # Check if thread exists
        if not await thread_manager.thread_exists(thread_id):
            return JSONResponse(status_code=404, content={"error": f"Thread {thread_id} not found"})
        
        # Add user message to thread
        await thread_manager.add_message(thread_id, "user", content)
        
        # Configure the model parameters
        model_config = {
            "model": model_name,
            "temperature": 0.7,
            "max_tokens": 4000,
            "top_p": 0.9,
            "stream": False  # Non-streaming for send-message
        }
        
        # Create processor config
        processor_config = ProcessorConfig(
            stream=False,
            thinking=enable_thinking
        )
        
        # Run the thread to get a response
        response_text = await thread_manager.run_thread(
            thread_id=thread_id,
            model=model_config["model"],
            temperature=model_config["temperature"],
            max_tokens=model_config["max_tokens"],
            top_p=model_config["top_p"],
            processor_config=processor_config
        )
        
        # Return the response
        return {
            "thread_id": thread_id,
            "response": response_text
        }
    except Exception as e:
        logger.error(f"Error in send_message: {str(e)}")
        return JSONResponse(status_code=500, content={"error": str(e)})

# Generate AI-powered smart suggestions based on conversation context
@app.post("/api/suggestions/generate")
async def generate_smart_suggestions(request: Request):
    """Generate AI-powered smart suggestions based on conversation context."""
    try:
        # Parse request body
        body = await request.json()
        thread_id = body.get('thread_id')
        last_message = body.get('last_message', '')
        chat_type = body.get('chat_type', 'general')

        if not last_message:
            return JSONResponse(status_code=400, content={"error": "last_message is required"})

        logger.info(f"Generating smart suggestions for chat_type: {chat_type}, message: {last_message[:100]}...")

        # Create a comprehensive prompt for generating contextual suggestions
        suggestion_prompt = f"""You are an AI assistant that generates smart reply suggestions for chat conversations, similar to Apple Messages quick replies.

Analyze this message and generate 3 contextually appropriate response suggestions:

Message: "{last_message}"
Chat context: {chat_type} conversation
Thread ID: {thread_id}

Requirements:
- Each suggestion must be 1-4 words maximum
- Suggestions should be natural, conversational responses
- Consider the tone, content, and context of the message
- Generate responses that a human would naturally want to send
- Avoid generic responses - make them specific to the message content
- Consider the relationship context (professional for CEO/developer, collaborative for team)

Examples of good suggestions:
- For questions: "Yes", "Not sure", "Let me check"
- For updates: "Thanks", "Got it", "When?"
- For requests: "Sure thing", "On it", "Need details"
- For problems: "I'll help", "Try this", "Let's debug"
- For code: "Looks good", "Test it", "Ship it"

Generate exactly 3 suggestions, one per line, no quotes or formatting:"""

        # Use the LLM to generate suggestions
        try:
            import anthropic

            # Initialize Anthropic client
            client = anthropic.Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))

            # Generate suggestions using Claude
            response = client.messages.create(
                model="claude-3-haiku-20240307",  # Use fast model for suggestions
                max_tokens=100,
                temperature=0.7,
                messages=[
                    {
                        "role": "user",
                        "content": suggestion_prompt
                    }
                ]
            )

            # Parse the response to extract suggestions
            suggestions_text = response.content[0].text.strip()
            suggestions = [s.strip() for s in suggestions_text.split('\n') if s.strip()]

            # Limit to 3 suggestions and ensure they're short
            suggestions = suggestions[:3]
            suggestions = [s for s in suggestions if len(s.split()) <= 4]

            # If AI didn't generate any suggestions, try a simpler prompt
            if not suggestions:
                simple_prompt = f"""Generate 3 short reply suggestions (1-4 words each) for this message: "{last_message}"

Just return 3 responses, one per line:"""

                try:
                    simple_response = client.messages.create(
                        model="claude-3-haiku-20240307",
                        max_tokens=50,
                        temperature=0.5,
                        messages=[{"role": "user", "content": simple_prompt}]
                    )

                    simple_suggestions = [s.strip() for s in simple_response.content[0].text.strip().split('\n') if s.strip()]
                    suggestions = simple_suggestions[:3] if simple_suggestions else ["Continue", "Thanks", "Got it"]

                except Exception as simple_error:
                    logger.error(f"Simple prompt also failed: {simple_error}")
                    # Only use minimal fallback as last resort
                    suggestions = ["Continue", "Thanks", "Got it"]

            logger.info(f"Generated suggestions: {suggestions}")

            return {
                "suggestions": suggestions,
                "generated_by": "ai",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(f"Error generating AI suggestions: {e}")

            # Try a backup AI call with a very simple prompt
            try:
                import anthropic
                backup_client = anthropic.Anthropic(api_key=os.getenv("ANTHROPIC_API_KEY"))

                backup_prompt = f"""Reply to: "{last_message[:200]}"

Generate 3 short responses (max 4 words each):"""

                backup_response = backup_client.messages.create(
                    model="claude-3-haiku-20240307",
                    max_tokens=30,
                    temperature=0.3,
                    messages=[{"role": "user", "content": backup_prompt}]
                )

                backup_suggestions = [s.strip() for s in backup_response.content[0].text.strip().split('\n') if s.strip()]
                suggestions = backup_suggestions[:3] if backup_suggestions else ["Got it", "Thanks", "Continue"]

            except Exception as backup_error:
                logger.error(f"Backup AI call failed: {backup_error}")
                # Absolute last resort - minimal generic responses
                suggestions = ["Got it", "Thanks", "Continue"]

            return {
                "suggestions": suggestions,
                "generated_by": "fallback",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    except Exception as e:
        logger.error(f"Error in generate_smart_suggestions: {e}")
        return JSONResponse(status_code=500, content={"error": str(e)})

# Get messages for a thread
@app.get("/messages/{thread_id}")
async def get_messages(thread_id: str):
    try:
        # Check if thread exists
        if not await thread_manager.thread_exists(thread_id):
            return JSONResponse(status_code=404, content={"error": f"Thread {thread_id} not found"})

        # Get messages from thread manager
        messages = await thread_manager.get_messages(thread_id)

        # Format messages for frontend
        formatted_messages = []
        for msg in messages:
            formatted_msg = {
                "message_id": msg.get("id", str(uuid.uuid4())),
                "thread_id": thread_id,
                "type": msg.get("type", "unknown"),
                "content": msg.get("content", ""),
                "created_at": msg.get("created_at", datetime.now(timezone.utc).isoformat())
            }
            formatted_messages.append(formatted_msg)
        
        return {
            "thread_id": thread_id,
            "messages": formatted_messages
        }
    except Exception as e:
        logger.error(f"Error in get_messages: {str(e)}")
        return JSONResponse(status_code=500, content={"error": str(e)})

# Streaming agent response endpoint
@app.post("/agent/stream")

# Simplified streaming implementation for agent responses by thread_id
@app.get("/api/agent/stream/{thread_id}")
async def stream_agent_by_thread_id(thread_id: str):
    try:
        logger.info(f"Streaming agent response for thread {thread_id}")
        
        # Generate a unique message ID for this response
        message_id = str(uuid.uuid4())
        
        # Define a simpler streaming response generator
        async def generate_stream():
            try:
                # Default to CEO agent type
                agent_type = "ceo"
                agent_name = "Kenard"
                
                # Create a simple response
                response_text = "Hey there! Kenard here. I'm focused on helping you achieve your business goals. What can I help you with today?"
                
                # Store the full response
                full_response = response_text
                
                # Split the response into chunks for streaming
                chunks = [response_text[i:i+10] for i in range(0, len(response_text), 10)]
                content_buffer = ""
                
                # Stream each chunk with a delay
                for chunk in chunks:
                    content_buffer += chunk
                    
                    # Create the message data
                    data = {
                        "type": "message",
                        "content": content_buffer,
                        "thread_id": thread_id,
                        "message_id": message_id
                    }
                    
                    # Send the data
                    yield f"data: {json.dumps(data)}\n\n"
                    
                    # Add a small delay
                    await asyncio.sleep(0.1)
                
                # Send a completion message
                complete_data = {
                    "type": "message",
                    "content": full_response,
                    "thread_id": thread_id,
                    "message_id": message_id,
                    "status": "complete"
                }
                yield f"data: {json.dumps(complete_data)}\n\n"
                
                # Store the message
                try:
                    # Create the message
                    message = {
                        "id": message_id,
                        "role": "assistant",
                        "content": full_response,
                        "created_at": datetime.now().isoformat(),
                        "metadata": {"agent_type": agent_type, "agent_name": agent_name}
                    }
                    
                    # Store it using the MessageStore class
                    message_store.add_message(thread_id, message)
                    
                    logger.info(f"Message stored in thread {thread_id}")
                except Exception as e:
                    logger.error(f"Failed to store message: {str(e)}")
                    # Continue anyway
            except Exception as e:
                # Log the error
                logger.error(f"Error in streaming response: {str(e)}")
                
                # Send an error message
                error_data = {
                    "type": "error",
                    "message": str(e)
                }
                yield f"data: {json.dumps(error_data)}\n\n"
        
        # Return a streaming response with proper headers for SSE
        headers = {
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "X-Accel-Buffering": "no"  # Disable buffering for Nginx
        }
        
        # Create the streaming response
        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream",
            headers=headers
        )
    except Exception as e:
        logger.error(f"Error in stream agent endpoint: {str(e)}")
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.post("/agent/stream")
async def stream_agent_response(request: Request):
    try:
        # Parse form data
        form_data = await request.form()
        logger.info(f"Stream agent endpoint called with form data: {form_data}")
        
        # Extract prompt and other parameters
        prompt = form_data.get('prompt', 'No prompt provided')
        model_name = form_data.get('model_name', 'claude-3-7-sonnet-latest')  # Default to claude-3-7-sonnet-latest
        agents_str = form_data.get('agents', '')
        collaboration_mode_str = form_data.get('collaboration_mode', 'false')
        enable_thinking_str = form_data.get('enable_thinking', 'false')
        thread_id = form_data.get('thread_id', str(uuid.uuid4()))
        
        # Convert to appropriate types
        agents = agents_str.split(',') if agents_str else ['Kenard']
        collaboration_mode = collaboration_mode_str.lower() == 'true'
        enable_thinking = enable_thinking_str.lower() == 'true'
        
        # Log the request details
        logger.info(f"Prompt: {prompt}")
        logger.info(f"Model: {model_name}")
        logger.info(f"Agents: {agents}")
        logger.info(f"Thread ID: {thread_id}")
        logger.info(f"Collaboration mode: {collaboration_mode}")
        logger.info(f"Enable thinking: {enable_thinking}")
        
        # Initialize thread if it doesn't exist
        try:
            thread_exists = await thread_manager.thread_exists(thread_id)
            if not thread_exists:
                logger.info(f"Creating new thread with ID: {thread_id}")
                await thread_manager.create_thread(thread_id)
                
                # Ensure we have at least one agent
                if not agents or len(agents) == 0:
                    agents = ['Kenard']
                    logger.info(f"No agents specified, defaulting to: {agents[0]}")
                
                # Get the primary agent (first in the list)
                primary_agent = agents[0]
                
                # Log the agents and collaboration mode
                if collaboration_mode and len(agents) > 1:
                    logger.info(f"Creating collaborative thread with {len(agents)} agents: {', '.join(agents)}")
                else:
                    logger.info(f"Creating thread for single agent: {primary_agent}")
                
                # Get the system prompt using our improved function that handles collaboration mode
                system_prompt = get_system_prompt_for_agent(
                    agent_name=primary_agent,
                    agents=agents,
                    collaboration_mode=collaboration_mode
                )
                
                # Add the system prompt to the thread
                await thread_manager.add_message(thread_id, "system", system_prompt)
                logger.info(f"Added system prompt to thread")
                
                # Store thread metadata including all agents and collaboration mode
                thread_metadata = {
                    "selected_agents": agents,
                    "primary_agent": primary_agent,
                    "collaboration_mode": collaboration_mode
                }
                await thread_manager.set_thread_metadata(thread_id, thread_metadata)
                logger.info(f"Stored thread metadata with agents and collaboration settings")
            else:
                logger.info(f"Using existing thread with ID: {thread_id}")
        except Exception as e:
            logger.error(f"Error initializing thread: {str(e)}")
            return JSONResponse(status_code=500, content={"error": f"Thread initialization failed: {str(e)}"})
        
        # Add user message to thread
        try:
            await thread_manager.add_message(thread_id, "user", prompt)
            logger.info(f"Added user message to thread {thread_id}")
        except Exception as e:
            logger.error(f"Error adding user message: {str(e)}")
            return JSONResponse(status_code=500, content={"error": f"Failed to add user message: {str(e)}"})
        
        async def response_generator():
            try:
                # Configure the model parameters
                model_config = {
                    "model": model_name,
                    "temperature": 0.7,
                    "max_tokens": 4000,
                    "top_p": 0.9,
                    "stream": True
                }
                
                # Create processor config for streaming response
                processor_config = ProcessorConfig(
                    stream=True,
                    thinking=enable_thinking
                )
                
                # Initialize response tracking
                full_response = ""
                message_id = str(uuid.uuid4())
                
                # Send initial event with thread ID
                init_data = json.dumps({
                    "event": "init",
                    "thread_id": thread_id,
                    "message_id": message_id,
                    "agents": agents,
                    "collaboration_mode": collaboration_mode
                })
                yield f"data: {init_data}\n\n"
                
                # Run the thread with streaming response
                async for chunk in thread_manager.run_thread_streaming(
                    thread_id=thread_id,
                    model=model_config["model"],
                    temperature=model_config["temperature"],
                    max_tokens=model_config["max_tokens"],
                    top_p=model_config["top_p"],
                    processor_config=processor_config
                ):
                    # Process the chunk based on its type
                    if "type" in chunk:
                        if chunk["type"] == "thinking" and enable_thinking:
                            # Send thinking chunk
                            thinking_data = json.dumps({
                                "event": "thinking", 
                                "content": chunk.get("content", "")
                            })
                            yield f"data: {thinking_data}\n\n"
                        elif chunk["type"] == "content":
                            # Send content chunk and accumulate full response
                            content = chunk.get("content", "")
                            full_response += content
                            chunk_data = json.dumps({
                                "event": "chunk", 
                                "content": content
                            })
                            yield f"data: {chunk_data}\n\n"
                        elif chunk["type"] == "tool_call":
                            # Send tool call notification
                            tool_data = json.dumps({
                                "event": "tool_call", 
                                "tool": chunk.get("tool", {})
                            })
                            yield f"data: {tool_data}\n\n"
                        elif chunk["type"] == "tool_result":
                            # Send tool result notification
                            result_data = json.dumps({
                                "event": "tool_result", 
                                "result": chunk.get("result", {})
                            })
                            yield f"data: {result_data}\n\n"
                    else:
                        # Handle raw text chunks (fallback)
                        raw_content = str(chunk)
                        full_response += raw_content
                        chunk_data = json.dumps({
                            "event": "chunk", 
                            "content": raw_content
                        })
                        yield f"data: {chunk_data}\n\n"
                
                # Signal completion
                done_data = json.dumps({
                    "event": "done", 
                    "message_id": message_id,
                    "thread_id": thread_id
                })
                yield f"data: {done_data}\n\n"
                
                logger.info(f"Completed streaming response for thread {thread_id}")
                
            except Exception as e:
                logger.error(f"Error in response generator: {str(e)}")
                error_data = json.dumps({"event": "error", "message": str(e)})
                yield f"data: {error_data}\n\n"
        
        # Return a streaming response
        return StreamingResponse(
            response_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
            }
        )
    except Exception as e:
        logger.error(f"Error in stream agent endpoint: {str(e)}")
        return JSONResponse(status_code=500, content={"error": str(e)})

# Direct agent status check endpoint
@app.get("/agent-status/{agent_run_id}")
async def check_agent_status(agent_run_id: str):
    try:
        # In a real implementation, this would check the status of the agent run in a database or Redis
        # For now, we'll return a simple status
        return {
            "status": "completed",
            "agent_run_id": agent_run_id,
            "message": "Agent run completed successfully",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        logger.error(f"Error in check_agent_status: {str(e)}")
        return JSONResponse(status_code=500, content={"error": str(e)})

@app.get("/api/heartbeat")
async def heartbeat():
    """Heartbeat endpoint for frontend to check if API is alive.
    This is a simpler endpoint that doesn't require database access."""
    return {
        "status": "ok",
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

if __name__ == "__main__":
    import uvicorn
    import nest_asyncio
    
    # Apply nest_asyncio to allow nested event loops
    nest_asyncio.apply()
    
    # Start the server directly with the app instance
    logger.info("Starting server on 0.0.0.0:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)