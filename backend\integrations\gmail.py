from typing import Dict, Any, Optional, List, Union
import asyncio
import json
import base64
import httpx
from datetime import datetime

from utils.logger import logger
from integrations.base import BaseIntegration, IntegrationType, IntegrationStatus
from integrations import registry


class GmailIntegration(BaseIntegration):
    name = "gmail"
    display_name = "Gmail"
    description = "Integration with Gmail for email communication and alerts"
    
    def __init__(self, 
                 integration_id: str, 
                 account_id: str, 
                 integration_type: IntegrationType = IntegrationType.USER,
                 config: Optional[Dict[str, Any]] = None):
        super().__init__(integration_id, account_id, integration_type, config)
        self.api_base_url = "https://gmail.googleapis.com/gmail/v1"
        self.access_token = self.config.get("access_token")
        self.refresh_token = self.config.get("refresh_token")
        self.client_id = self.config.get("client_id")
        self.client_secret = self.config.get("client_secret")
        self.user_id = self.config.get("user_id", "me")
        self.headers = {}
        
        if self.access_token:
            self.headers["Authorization"] = f"Bearer {self.access_token}"
    
    async def initialize(self) -> bool:
        if not self.access_token and not self.refresh_token:
            self.last_error = "Gmail access token or refresh token is required"
            self.status = IntegrationStatus.ERROR
            return False
            
        if self.refresh_token and (not self.client_id or not self.client_secret):
            self.last_error = "Client ID and client secret are required for token refresh"
            self.status = IntegrationStatus.ERROR
            return False
            
        try:
            if self.refresh_token and not self.access_token:
                refreshed = await self._refresh_access_token()
                if not refreshed:
                    return False
            
            # Validate the token by making a test API call
            valid = await self.validate_credentials()
            if valid:
                self.status = IntegrationStatus.ACTIVE
                self.last_sync = datetime.now().isoformat()
                return True
            else:
                if self.refresh_token:
                    refreshed = await self._refresh_access_token()
                    if refreshed:
                        valid = await self.validate_credentials()
                        if valid:
                            self.status = IntegrationStatus.ACTIVE
                            self.last_sync = datetime.now().isoformat()
                            return True
                
                self.status = IntegrationStatus.ERROR
                self.last_error = "Invalid Gmail credentials"
                return False
        except Exception as e:
            self.status = IntegrationStatus.ERROR
            self.last_error = str(e)
            logger.error(f"Error initializing Gmail integration: {str(e)}")
            return False
    
    async def _refresh_access_token(self) -> bool:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://oauth2.googleapis.com/token",
                    data={
                        "client_id": self.client_id,
                        "client_secret": self.client_secret,
                        "refresh_token": self.refresh_token,
                        "grant_type": "refresh_token"
                    },
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()
                    self.access_token = data.get("access_token")
                    self.headers["Authorization"] = f"Bearer {self.access_token}"
                    
                    # Update the config with the new access token
                    self.config["access_token"] = self.access_token
                    return True
                else:
                    self.last_error = f"Failed to refresh token: {response.status_code} - {response.text}"
                    return False
        except Exception as e:
            self.last_error = f"Error refreshing token: {str(e)}"
            return False
    
    async def validate_credentials(self) -> bool:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_base_url}/users/{self.user_id}/profile", 
                    headers=self.headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    return True
                else:
                    self.last_error = f"Gmail API error: {response.status_code} - {response.text}"
                    return False
        except Exception as e:
            self.last_error = f"Error validating Gmail credentials: {str(e)}"
            return False
    
    async def get_capabilities(self) -> List[str]:
        return [
            "list_messages",
            "get_message",
            "send_message",
            "create_draft",
            "list_labels",
            "create_label",
            "watch_mailbox"
        ]
    
    async def execute_action(self, action: str, params: Dict[str, Any]) -> Dict[str, Any]:
        if action == "list_messages":
            return await self._list_messages(params)
        elif action == "get_message":
            return await self._get_message(params)
        elif action == "send_message":
            return await self._send_message(params)
        elif action == "create_draft":
            return await self._create_draft(params)
        elif action == "list_labels":
            return await self._list_labels(params)
        elif action == "create_label":
            return await self._create_label(params)
        elif action == "watch_mailbox":
            return await self._watch_mailbox(params)
        else:
            return {"success": False, "error": f"Action {action} not implemented for Gmail integration"}
    
    async def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None, data: Optional[Dict] = None) -> Dict:
        try:
            url = f"{self.api_base_url}{endpoint}" if endpoint.startswith("/") else f"{self.api_base_url}/{endpoint}"
            
            async with httpx.AsyncClient() as client:
                if method.upper() == "GET":
                    response = await client.get(url, headers=self.headers, params=params, timeout=30)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=self.headers, json=data, timeout=30)
                elif method.upper() == "PUT":
                    response = await client.put(url, headers=self.headers, json=data, timeout=30)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=self.headers, timeout=30)
                else:
                    return {"success": False, "error": f"Unsupported method: {method}"}
                
                if response.status_code >= 200 and response.status_code < 300:
                    return {"success": True, "data": response.json() if response.text else {}}
                elif response.status_code == 401 and self.refresh_token:
                    # Token expired, try to refresh
                    refreshed = await self._refresh_access_token()
                    if refreshed:
                        # Retry the request with the new token
                        return await self._make_request(method, endpoint, params, data)
                    else:
                        return {
                            "success": False, 
                            "error": "Failed to refresh access token", 
                            "details": self.last_error
                        }
                else:
                    return {
                        "success": False, 
                        "error": f"Gmail API error: {response.status_code}", 
                        "details": response.text
                    }
                
        except Exception as e:
            logger.error(f"Gmail request error: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _list_messages(self, params: Dict[str, Any]) -> Dict[str, Any]:
        query = params.get("query", "")
        max_results = params.get("max_results", 10)
        label_ids = params.get("label_ids", [])
        
        query_params = {
            "maxResults": max_results,
            "q": query
        }
        
        if label_ids:
            query_params["labelIds"] = label_ids
            
        endpoint = f"users/{self.user_id}/messages"
        
        result = await self._make_request("GET", endpoint, params=query_params)
        
        if result["success"]:
            messages = result["data"].get("messages", [])
            
            if not messages:
                return {"success": True, "messages": []}
                
            detailed_messages = []
            
            # Get details for each message
            for message in messages:
                message_id = message.get("id")
                if message_id:
                    message_result = await self._get_message({"message_id": message_id, "format": "metadata"})
                    if message_result["success"]:
                        detailed_messages.append(message_result["message"])
            
            return {"success": True, "messages": detailed_messages}
        else:
            return result
    
    async def _get_message(self, params: Dict[str, Any]) -> Dict[str, Any]:
        message_id = params.get("message_id")
        format = params.get("format", "full")
        
        if not message_id:
            return {"success": False, "error": "Message ID is required"}
            
        query_params = {
            "format": format
        }
            
        endpoint = f"users/{self.user_id}/messages/{message_id}"
        
        result = await self._make_request("GET", endpoint, params=query_params)
        
        if result["success"]:
            message_data = result["data"]
            
            # Extract headers
            headers = {}
            for header in message_data.get("payload", {}).get("headers", []):
                headers[header.get("name")] = header.get("value")
                
            # Extract body
            body = ""
            if format == "full":
                body_data = message_data.get("payload", {}).get("body", {})
                if body_data.get("data"):
                    body = base64.urlsafe_b64decode(body_data["data"]).decode("utf-8")
                else:
                    # Handle multipart messages
                    parts = message_data.get("payload", {}).get("parts", [])
                    for part in parts:
                        if part.get("mimeType") == "text/plain" and part.get("body", {}).get("data"):
                            body += base64.urlsafe_b64decode(part["body"]["data"]).decode("utf-8")
            
            formatted_message = {
                "id": message_data.get("id"),
                "thread_id": message_data.get("threadId"),
                "label_ids": message_data.get("labelIds", []),
                "snippet": message_data.get("snippet", ""),
                "headers": headers,
                "subject": headers.get("Subject", ""),
                "from": headers.get("From", ""),
                "to": headers.get("To", ""),
                "date": headers.get("Date", ""),
                "body": body,
                "internal_date": message_data.get("internalDate")
            }
            
            return {"success": True, "message": formatted_message}
        else:
            return result
    
    async def _send_message(self, params: Dict[str, Any]) -> Dict[str, Any]:
        to = params.get("to")
        subject = params.get("subject")
        body = params.get("body")
        
        if not to or not subject or not body:
            return {"success": False, "error": "To, subject, and body parameters are required"}
            
        # Create email in RFC 2822 format
        email_content = f"To: {to}\r\n"
        email_content += f"Subject: {subject}\r\n"
        email_content += "Content-Type: text/plain; charset=utf-8\r\n\r\n"
        email_content += body
        
        # Encode as base64url string
        encoded_message = base64.urlsafe_b64encode(email_content.encode("utf-8")).decode("utf-8")
        
        data = {
            "raw": encoded_message
        }
            
        endpoint = f"users/{self.user_id}/messages/send"
        
        return await self._make_request("POST", endpoint, data=data)
    
    async def _create_draft(self, params: Dict[str, Any]) -> Dict[str, Any]:
        to = params.get("to")
        subject = params.get("subject")
        body = params.get("body")
        
        if not to or not subject or not body:
            return {"success": False, "error": "To, subject, and body parameters are required"}
            
        # Create email in RFC 2822 format
        email_content = f"To: {to}\r\n"
        email_content += f"Subject: {subject}\r\n"
        email_content += "Content-Type: text/plain; charset=utf-8\r\n\r\n"
        email_content += body
        
        # Encode as base64url string
        encoded_message = base64.urlsafe_b64encode(email_content.encode("utf-8")).decode("utf-8")
        
        data = {
            "message": {
                "raw": encoded_message
            }
        }
            
        endpoint = f"users/{self.user_id}/drafts"
        
        return await self._make_request("POST", endpoint, data=data)
    
    async def _list_labels(self, params: Dict[str, Any]) -> Dict[str, Any]:
        endpoint = f"users/{self.user_id}/labels"
        
        return await self._make_request("GET", endpoint)
    
    async def _create_label(self, params: Dict[str, Any]) -> Dict[str, Any]:
        name = params.get("name")
        
        if not name:
            return {"success": False, "error": "Label name is required"}
            
        data = {
            "name": name,
            "labelListVisibility": params.get("visibility", "labelShow"),
            "messageListVisibility": params.get("message_visibility", "show")
        }
            
        endpoint = f"users/{self.user_id}/labels"
        
        return await self._make_request("POST", endpoint, data=data)
    
    async def _watch_mailbox(self, params: Dict[str, Any]) -> Dict[str, Any]:
        topic_name = params.get("topic_name")
        
        if not topic_name:
            return {"success": False, "error": "Topic name is required"}
            
        data = {
            "topicName": topic_name,
            "labelIds": params.get("label_ids", [])
        }
            
        endpoint = f"users/{self.user_id}/watch"
        
        return await self._make_request("POST", endpoint, data=data)
    
    async def handle_webhook(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        # Process Gmail push notification
        if "message" in payload and "data" in payload["message"]:
            try:
                # Decode the data
                data = json.loads(base64.b64decode(payload["message"]["data"]).decode("utf-8"))
                
                # Get the email details
                email_data = {
                    "history_id": data.get("historyId"),
                    "email_address": data.get("emailAddress")
                }
                
                # Process the notification based on the history ID
                if email_data["history_id"]:
                    await self._process_history(email_data["history_id"])
                
                return {"success": True, "message": "Gmail notification processed", "data": email_data}
            except Exception as e:
                return {"success": False, "error": f"Error processing Gmail notification: {str(e)}"}
        else:
            return {"success": False, "error": "Invalid Gmail notification format"}
    
    async def _process_history(self, history_id: str) -> Dict[str, Any]:
        # Get the history details
        endpoint = f"users/{self.user_id}/history"
        query_params = {
            "startHistoryId": history_id
        }
        
        result = await self._make_request("GET", endpoint, params=query_params)
        
        if result["success"]:
            history = result["data"]
            
            # Process the history entries
            for history_entry in history.get("history", []):
                # Handle new messages
                if "messagesAdded" in history_entry:
                    for message_added in history_entry["messagesAdded"]:
                        message_id = message_added.get("message", {}).get("id")
                        if message_id:
                            # Process the new message
                            await self._process_new_message(message_id)
            
            return {"success": True, "message": "History processed"}
        else:
            return result
    
    async def _process_new_message(self, message_id: str) -> None:
        # Get the message details
        message_result = await self._get_message({"message_id": message_id})
        
        if message_result["success"]:
            message = message_result["message"]
            
            # TODO: Implement message processing logic based on your requirements
            logger.info(f"New email received: {message['subject']} from {message['from']}")


# Register the integration
registry.register_integration("gmail", GmailIntegration)
