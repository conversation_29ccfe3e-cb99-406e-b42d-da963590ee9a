import { Suspense } from 'react'
import { notFound } from 'next/navigation'
import { Metadata } from 'next'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { PlusCircle } from 'lucide-react'
import LoadingSpinner from '@/components/ui/loading-spinner'

export const metadata: Metadata = {
  title: 'Conversations | Siden',
  description: 'View and manage your AI agent conversations',
}

export default async function ThreadsPage({
  params,
}: {
  params: Promise<{ projectId: string }>
}) {
  const { projectId } = await params;

  if (!projectId) {
    return notFound()
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">Conversations</h2>
        <Button asChild>
          <Link href={`/project/${projectId}/chat`}>
            <PlusCircle className="mr-2 h-4 w-4" />
            New Conversation
          </Link>
        </Button>
      </div>

      <Suspense fallback={<LoadingSpinner />}>
        <ThreadsList projectId={projectId} />
      </Suspense>
    </div>
  )
}

function ThreadsList({ projectId }: { projectId: string }) {
  // This would be replaced with actual data fetching logic
  return (
    <div className="space-y-4">
      <div className="bg-card rounded-lg border p-6">
        <p className="text-muted-foreground text-center">
          No conversations found. Start a new conversation with your AI agent.
        </p>
        <div className="mt-4 flex justify-center">
          <Button asChild>
            <Link href={`/project/${projectId}/chat`}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Start New Conversation
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
