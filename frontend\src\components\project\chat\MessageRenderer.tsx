'use client';

import React, { useState, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { GitCommit, GitPullRequest, GitMerge, CheckCircle2, Video, Phone } from 'lucide-react';
import { FileAttachmentsGrid } from '@/components/thread/file-attachments-grid';
import { getFileTypeFromExtension } from '@/components/file-renderers';
import { CodeArtifact } from '@/components/ui/code-artifact';
import { useCodeExecution, generateExecutionKey } from '@/hooks/use-code-execution';
import { detectCodeGeneration, type CodeBlock } from '@/utils/code-detection';
import { Message, SelectedFile } from './types';
import { formatFileSize, AGENT_ROLES } from './utils';

interface MessageRendererProps {
  message: Message;
  isFirstInGroup?: boolean;
  isInGroup?: boolean;
  onFileClick: (file: SelectedFile) => void;
}

// Component to render code blocks as artifacts
function CodeBlockArtifact({
  codeBlock,
  index
}: {
  codeBlock: CodeBlock;
  index: number;
}) {
  const { executeCode, results } = useCodeExecution();
  const [executionKey, setExecutionKey] = useState<string | null>(null);

  const handleExecute = async (codeToExecute: string, lang: string) => {
    const key = generateExecutionKey(codeToExecute, lang);
    setExecutionKey(key);
    await executeCode(codeToExecute, lang, 30000, key);
  };

  const executionResult = executionKey ? results[executionKey] : undefined;
  const isExecutable = ['python', 'py', 'javascript', 'js', 'typescript', 'ts'].includes(codeBlock.language.toLowerCase());

  return (
    <div className="my-4">
      <CodeArtifact
        code={codeBlock.code}
        language={codeBlock.language}
        title={codeBlock.title || `Code Block ${index + 1}`}
        description={codeBlock.description}
        onExecute={handleExecute}
        executionResult={executionResult}
        showExecuteButton={isExecutable}
        className="max-w-none"
      />
    </div>
  );
}

export function MessageRenderer({
  message,
  isFirstInGroup = true,
  isInGroup = false,
  onFileClick
}: MessageRendererProps) {
  // Detect code generation and extract enhanced code blocks
  const codeGenerationContext = useMemo(() => {
    if (!message.content || message.isFile || message.isCode) {
      return { isCodeGeneration: false, codeBlocks: [], hasExecutableCode: false, confidence: 0 };
    }
    return detectCodeGeneration(message.content);
  }, [message.content, message.isFile, message.isCode]);

  // Check if message should display code as artifacts
  const shouldShowArtifacts = codeGenerationContext.isCodeGeneration &&
                              codeGenerationContext.confidence > 0.6 &&
                              !message.isCode &&
                              !message.isFile;
  const isUser = message.sender === 'user';
  const isSystem = message.sender === 'system';
  const isThinking = message.type === 'thinking';
  
  // Find agent info based on sender ID
  const agentInfo = AGENT_ROLES.find(agent => agent.id === message.sender);

  if (isSystem && message.isGitHub) {
    let icon = <GitCommit className="h-4 w-4" />;
    let bgColor = "bg-[#0078d4]";
    let title = "Git Commit";

    if (message.gitHubAction === 'pull-request') {
      icon = <GitPullRequest className="h-4 w-4" />;
      bgColor = "bg-[#5e2398]";
      title = "Pull Request";
    } else if (message.gitHubAction === 'merge') {
      icon = <GitMerge className="h-4 w-4" />;
      bgColor = "bg-[#107c10]";
      title = "Merge";
    } else if (message.content.includes('Tests passed')) {
      icon = <CheckCircle2 className="h-4 w-4" />;
      bgColor = "bg-[#107c10]";
      title = "Tests";
    }

    return (
      <div key={message.id} className="flex mt-4 justify-start">
        <div className="flex-shrink-0 mr-2">
          <div className={`${bgColor} h-8 w-8 rounded-md flex items-center justify-center text-white`}>
            {icon}
          </div>
        </div>

        <div className="max-w-[70%]">
          <div className="flex items-center mb-1">
            <span className="text-sm font-medium text-white">
              {title}
            </span>
            <span className="text-xs text-[#999999] ml-2">
              {message.timestamp}
            </span>
          </div>

          <div className="bg-[#2a2a2a] text-white border border-[#333333] rounded-md p-2 px-3">
            <div className="text-sm">{message.content}</div>
          </div>
        </div>
      </div>
    );
  }

  // Discord-style call message rendering (left-aligned like regular messages)
  if (isSystem && message.isCall) {
    const isVideoCall = message.callType === 'video';

    const callIcon = isVideoCall ? (
      <Video className="h-4 w-4" />
    ) : (
      <Phone className="h-4 w-4" />
    );

    return (
      <div key={message.id} className="flex justify-start my-2">
        <div className="flex items-center gap-3 bg-[#2a2a2a] border border-[#333333] rounded-lg px-4 py-3">
          <div className="bg-gray-500 h-8 w-8 rounded-full flex items-center justify-center text-white">
            {callIcon}
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-300">
              {message.content}
            </div>
            <div className="text-xs text-[#999999] mt-1">
              {message.timestamp}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Microsoft Teams style message rendering
  const avatarBg = isUser ? "bg-[#6466E9]" :
                   isThinking ? "bg-[#666666]" :
                   isSystem ? "bg-[#8561c5]" :
                   agentInfo ? "bg-[#4f6bed]" : "bg-[#8561c5]";
                   
  const avatarInitial = agentInfo ? agentInfo.name.charAt(0) : isUser ? "Y" : "S";
  const avatarSrc = agentInfo ? agentInfo.avatar : "";

  // Determine message spacing and border radius based on grouping
  const showAvatar = isFirstInGroup || !isInGroup;
  const showSender = isFirstInGroup && !isUser;
  const showTimestamp = !isInGroup || !isFirstInGroup;

  // Adjust border radius based on position in group
  let borderRadius = isUser
    ? 'rounded-tl-md rounded-tr-md rounded-bl-md rounded-br-sm'
    : 'rounded-tl-sm rounded-tr-md rounded-bl-md rounded-br-md';

  if (isInGroup && !isFirstInGroup) {
    borderRadius = isUser
      ? 'rounded-tl-md rounded-tr-md rounded-bl-md rounded-br-sm'
      : 'rounded-md';
  }

  return (
    <div key={message.id} className={`flex ${isUser ? 'justify-end' : 'justify-start'} ${isFirstInGroup ? 'mt-4' : 'mt-1'}`}>
      {!isUser && (
        <div className={`flex-shrink-0 mr-2 ${!showAvatar ? 'invisible' : 'visible'}`} style={{ width: '32px', height: '32px' }}>
          {showAvatar && (
            <div className={`${avatarBg} h-8 w-8 rounded-md overflow-hidden`}>
              <div className="h-8 w-8 flex items-center justify-center text-white font-medium">
                {avatarSrc ? (
                  <img src={avatarSrc} alt={agentInfo ? agentInfo.name : "System"} className="h-full w-full object-cover" />
                ) : (
                  avatarInitial
                )}
              </div>
            </div>
          )}
        </div>
      )}

      <div className={`max-w-[70%] ${isUser ? 'order-1' : 'order-2'}`}>
        {showSender && (
          <div className="flex items-center mb-1">
            <span className="text-sm font-medium text-white">
              {agentInfo ? `${agentInfo.name} (${agentInfo.role})` : isUser ? 'You' : 'System'}
            </span>
            {isThinking && (
              <span className="bg-[#666666] text-white px-2 py-0.5 rounded-full text-xs ml-2">
                typing...
              </span>
            )}
            <span className="text-xs text-[#999999] ml-2">
              {message.timestamp}
            </span>
          </div>
        )}

        {isThinking ? (
          <div className="bg-[#3a3a3a] text-[#cccccc] border border-[#555555] rounded-md p-2 px-3">
            <div className="flex items-center justify-center gap-2 text-[#999999] italic min-h-[20px]">
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-[#999999] rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-[#999999] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-[#999999] rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
              <span>{agentInfo ? `${agentInfo.name} is typing...` : 'typing...'}</span>
            </div>
          </div>
        ) : message.isFile ? (
          <div
            className={`p-2 px-3 bg-[#2a2a2a] text-white border border-[#333333] ${borderRadius}`}
          >
            <FileAttachmentsGrid
              files={[
                (() => {
                  try {
                    const fileData = JSON.parse(message.content);
                    return {
                      name: fileData.name || message.fileName || 'file.txt',
                      type: getFileTypeFromExtension(fileData.name || message.fileName || 'file.txt'),
                      size: typeof fileData.size === 'number' ? formatFileSize(fileData.size) : (message.fileSize || '0 B'),
                      content: fileData.content || ''
                    };
                  } catch (e) {
                    console.error('Error parsing file data:', e);
                    return {
                      name: message.fileName || 'file.txt',
                      type: message.fileType || 'text',
                      size: message.fileSize || '0 B',
                      content: message.content || ''
                    };
                  }
                })()
              ]}
              onFileClick={(file) => {
                onFileClick({
                  name: file.name,
                  content: file.content || '',
                  type: getFileTypeFromExtension(file.name)
                });
              }}
              className="bg-transparent border-0 p-0 m-0"
            />
          </div>
        ) : (
          <div
            className={`p-2 px-3 bg-[#2a2a2a] text-white border border-[#333333] ${borderRadius}`}
          >
            {message.isCode ? (
              <pre className="whitespace-pre-wrap overflow-x-auto bg-[#222222] p-2 rounded text-xs font-mono text-[#cccccc]">{message.content}</pre>
            ) : shouldShowArtifacts ? (
              <div className="space-y-4">
                {/* Render text content without code blocks */}
                <div className="text-sm prose prose-invert prose-sm max-w-none [&>*:first-child]:mt-0 [&>*:last-child]:mb-0">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    components={{
                      // Override code component to prevent rendering code blocks in markdown
                      code: ({ className, children, ...props }) => {
                        const isInline = !className || !className.includes('language-');
                        if (isInline) {
                          return <code className={className} {...props}>{children}</code>;
                        }
                        // Return empty for block code - will be rendered as artifacts
                        return null;
                      }
                    }}
                  >
                    {message.content}
                  </ReactMarkdown>
                </div>

                {/* Render code blocks as artifacts */}
                {codeGenerationContext.codeBlocks.map((block, index) => (
                  <CodeBlockArtifact
                    key={`${message.id}-code-${index}`}
                    codeBlock={block}
                    index={index}
                  />
                ))}
              </div>
            ) : (
              <div className="text-sm prose prose-invert prose-sm max-w-none [&>*:first-child]:mt-0 [&>*:last-child]:mb-0">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {message.content}
                </ReactMarkdown>
              </div>
            )}
          </div>
        )}

        {/* Only show timestamp at the bottom for user messages that are not files */}
        {isUser && showTimestamp && !message.isFile && (
          <div className="text-xs text-[#999999] ml-1 mt-0.5 text-right">
            {message.timestamp}
          </div>
        )}
      </div>
    </div>
  );
}
