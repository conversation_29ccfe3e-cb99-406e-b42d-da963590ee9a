'use client';

import React from 'react';
import Image from 'next/image';
import { Video, Phone } from 'lucide-react';
import { CustomGroup } from './types';
import { GroupAvatar } from './GroupAvatar';
import { AGENT_ROLES } from './utils';

interface ChatHeaderProps {
  activeChat: string;
  customGroups: CustomGroup[];
  startCall: (type: 'audio' | 'video') => void;
}

export function ChatHeader({ activeChat, customGroups, startCall }: ChatHeaderProps) {
  const renderChatAvatar = () => {
    // Check if it's a standard agent from AGENT_ROLES
    const agent = AGENT_ROLES.find(agent => agent.id === activeChat);
    if (agent) {
      return (
        <div className="h-10 w-10 flex items-center justify-center bg-[#4f6bed]">
          <Image 
            src={agent.avatar} 
            alt={agent.name} 
            width={40} 
            height={40} 
            className="h-full w-full object-cover" 
            onError={(e) => {
              // Fallback to first letter if image fails to load
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              const parent = target.parentNode as HTMLElement;
              if (parent) {
                const textElement = document.createElement('span');
                textElement.textContent = agent.name.charAt(0);
                textElement.className = 'text-white text-lg font-medium';
                parent.appendChild(textElement);
              }
            }}
          />
        </div>
      );
    } else if (activeChat === 'group') {
      return (
        <div className="h-10 w-10 flex items-center justify-center bg-[#8561c5]">TC</div>
      );
    } else {
      const customGroup = customGroups.find(group => group.id === activeChat);
      return customGroup ? (
        <GroupAvatar participants={customGroup.participants} size="large" />
      ) : (
        <div className="h-10 w-10 flex items-center justify-center bg-[#8561c5]">?</div>
      );
    }
  };

  const getChatTitle = () => {
    // Check if it's a standard agent from AGENT_ROLES
    const agent = AGENT_ROLES.find(agent => agent.id === activeChat);
    if (agent) {
      return `${agent.name} (${agent.role})`;
    }
    
    if (activeChat === 'group') return 'Team Chat';
    
    const customGroup = customGroups.find(group => group.id === activeChat);
    return customGroup ? customGroup.name : 'Unknown Chat';
  };

  const getChatSubtitle = () => {
    if (activeChat === 'group') return '3 participants';
    if (activeChat === 'ceo' || activeChat === 'developer') return 'Available';
    
    const customGroup = customGroups.find(group => group.id === activeChat);
    return customGroup ? `${customGroup.participants.length} participants` : 'Available';
  };

  return (
    <div className="bg-sidebar-accent h-[60px] px-4 flex items-center justify-between border-b border-border">
      <div className="flex items-center">
        <div className="flex-shrink-0 h-10 w-10 rounded-md overflow-hidden mr-3 text-white font-medium">
          {renderChatAvatar()}
        </div>
        <div className="min-w-0 flex-1">
          <h2 className="font-semibold text-white truncate">
            {getChatTitle()}
          </h2>
          <p className="text-xs text-[#999999]">
            {getChatSubtitle()}
          </p>
        </div>
      </div>

      <div className="flex items-center">
        <div className="flex border border-[#333333] rounded-md overflow-hidden">
          <button
            className="h-8 w-8 flex items-center justify-center bg-[#222222] hover:bg-[#333333] text-[#999999] hover:text-white border-r border-[#333333]"
            onClick={() => startCall('video')}
          >
            <Video className="h-4 w-4" />
          </button>
          <button
            className="h-8 w-8 flex items-center justify-center bg-[#222222] hover:bg-[#333333] text-[#999999] hover:text-white"
            onClick={() => startCall('audio')}
          >
            <Phone className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
