import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import Image from 'next/image';

interface IntegrationCardProps {
  id: string;
  name: string;
  description: string;
  iconSrc?: string;
  useCustomIcon?: boolean;
  enabled?: boolean;
  integrationType?: 'connect' | 'create' | 'coming-soon';
  onToggle?: (enabled: boolean) => void;
  onConnect?: () => void;
  onCreate?: () => void;
}

export function IntegrationCard({
  id,
  name,
  description,
  iconSrc,
  useCustomIcon = false,
  enabled = false,
  integrationType = 'coming-soon',
  onToggle,
  onConnect,
  onCreate
}: IntegrationCardProps) {
  return (
    <Card className="border-border">
      <CardContent className="p-6">
        <div className="flex justify-between items-start">
          <div className="flex items-start gap-4">
            <div className="h-10 w-10 flex items-center justify-center">
              {useCustomIcon && id === 'github' ? (
                <div className="h-10 w-10 flex items-center justify-center">
                  <svg
                    width={40}
                    height={40}
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="dark:fill-white fill-black w-10 h-10"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M12 0C5.37 0 0 5.37 0 12C0 17.31 3.435 21.795 8.205 23.385C8.805 23.49 9.03 23.13 9.03 22.815C9.03 22.53 9.015 21.585 9.015 20.58C6 21.135 5.22 19.845 4.98 19.17C4.845 18.825 4.26 17.76 3.75 17.475C3.33 17.25 2.73 16.695 3.735 16.68C4.68 16.665 5.355 17.55 5.58 17.91C6.66 19.725 8.385 19.215 9.075 18.9C9.18 18.12 9.495 17.595 9.84 17.295C7.17 16.995 4.38 15.96 4.38 11.37C4.38 10.065 4.845 8.985 5.61 8.145C5.49 7.845 5.07 6.615 5.73 4.965C5.73 4.965 6.735 4.65 9.03 6.195C9.99 5.925 11.01 5.79 12.03 5.79C13.05 5.79 14.07 5.925 15.03 6.195C17.325 4.635 18.33 4.965 18.33 4.965C18.99 6.615 18.57 7.845 18.45 8.145C19.215 8.985 19.68 10.05 19.68 11.37C19.68 15.975 16.875 16.995 14.205 17.295C14.64 17.67 15.015 18.39 15.015 19.515C15.015 21.12 15 22.41 15 22.815C15 23.13 15.225 23.505 15.825 23.385C18.2072 22.5807 20.2772 21.0497 21.7437 19.0074C23.2101 16.965 23.9993 14.5143 24 12C24 5.37 18.63 0 12 0Z"
                    />
                  </svg>
                </div>
              ) : id === 'database' ? (
                <div className="h-10 w-10 flex items-center justify-center">
                  <svg
                    width={40}
                    height={40}
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="text-primary w-10 h-10"
                  >
                    <path
                      d="M12 8C16.4183 8 20 6.65685 20 5C20 3.34315 16.4183 2 12 2C7.58172 2 4 3.34315 4 5C4 6.65685 7.58172 8 12 8Z"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M4 5V19C4 20.6569 7.58172 22 12 22C16.4183 22 20 20.6569 20 19V5"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M4 12C4 13.6569 7.58172 15 12 15C16.4183 15 20 13.6569 20 12"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              ) : iconSrc ? (
                <Image
                  src={iconSrc}
                  alt={`${name} icon`}
                  width={40}
                  height={40}
                  className="object-contain w-10 h-10"
                />
              ) : id === 'custom' ? (
                <div className="h-10 w-10 bg-primary/10 rounded-md flex items-center justify-center text-primary">
                  <svg
                    width={24}
                    height={24}
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="text-primary"
                  >
                    <rect
                      x="2"
                      y="2"
                      width="9"
                      height="9"
                      rx="2"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <rect
                      x="13"
                      y="2"
                      width="9"
                      height="9"
                      rx="2"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <rect
                      x="13"
                      y="13"
                      width="9"
                      height="9"
                      rx="2"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M6.5 13V17.5H11"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M17.5 6.5L17.5 6.51"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M17.5 17.5L17.5 17.51"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M6.5 6.5L6.5 6.51"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              ) : (
                <div className="h-10 w-10 bg-primary/10 rounded-md flex items-center justify-center text-primary">
                  {name.charAt(0)}
                </div>
              )}
            </div>

            <div className="space-y-1">
              <h3 className="font-medium text-foreground">{name}</h3>
              <p className="text-sm text-muted-foreground">{description}</p>
            </div>
          </div>

          {integrationType === 'coming-soon' ? (
            <span className="text-xs text-muted-foreground">Coming Soon</span>
          ) : integrationType === 'connect' ? (
            <Button
              variant="outline"
              size="sm"
              onClick={onConnect}
              disabled={enabled}
            >
              {enabled ? 'Connected' : 'Connect'}
            </Button>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={onCreate}
              disabled={enabled}
            >
              {enabled ? 'Created' : 'Create'}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
