// API client for agent interactions
import { createClient } from '@/lib/supabase/client';

// Define the backend URL
const getBackendUrl = (): string => {
  // Get the backend URL from environment variable
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';
  // Remove trailing /api if present to ensure consistent URL format
  return backendUrl.endsWith('/api') ? backendUrl : `${backendUrl}/api`;
};

// Helper function to get auth headers
const getAuthHeaders = async (): Promise<Record<string, string>> => {
  const supabase = createClient();
  const { data: { session } } = await supabase.auth.getSession();
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  if (session?.access_token) {
    headers['Authorization'] = `Bearer ${session.access_token}`;
  }
  
  return headers;
};

// Interface for agent initiation request
export interface AgentInitiateRequest {
  agent_type: string;
  model?: string;
  message: string;
  project_id?: string;
  metadata?: Record<string, any>;
}

// Interface for agent initiation response
export interface AgentInitiateResponse {
  thread_id: string;
  agent_run_id: string;
  message: string;
}

// Interface for sending a message to an existing thread
export interface SendMessageRequest {
  thread_id: string;
  message: string;
  metadata?: Record<string, any>;
}

// Interface for streaming agent response
export interface StreamingAgentResponse {
  type: string;
  content: string;
  tool_calls?: any[];
  thread_id?: string;
  message_id?: string;
}

// Interface for streaming agent callback
export interface StreamingAgentCallback {
  onMessage: (data: any) => void;
  onError: (error: any) => void;
  onComplete: () => void;
}

/**
 * Initiate a new agent conversation
 */
export const initiateAgent = async (request: AgentInitiateRequest): Promise<AgentInitiateResponse> => {
  try {
    const response = await fetch(`${getBackendUrl()}/agent/initiate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to initiate agent: ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error initiating agent:', error);
    throw error;
  }
};

/**
 * Send a follow-up message to an existing thread
 */
export const sendMessage = async (request: SendMessageRequest): Promise<any> => {
  try {
    const response = await fetch(`${getBackendUrl()}/agent/message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to send message: ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
};

/**
 * Get all messages for a thread
 */
export const getMessages = async (threadId: string): Promise<any> => {
  try {
    const response = await fetch(`${getBackendUrl()}/messages/${threadId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to get messages: ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting messages:', error);
    throw error;
  }
};

/**
 * Get the status of an agent run
 */
export const getAgentStatus = async (agentRunId: string): Promise<any> => {
  try {
    const response = await fetch(`${getBackendUrl()}/agent-status/${agentRunId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to get agent status: ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting agent status:', error);
    throw error;
  }
};

/**
 * Stream agent responses using EventSource
 */
export const streamAgentWithFormData = (
  threadIdOrFormData: string | FormData,
  callback: StreamingAgentCallback
): (() => void) => {
  try {
    let url;
    let threadId;
    
    if (typeof threadIdOrFormData === 'string') {
      // If a string is provided, it's the thread ID
      threadId = threadIdOrFormData;
      // Use the correct endpoint path with /api already included by getBackendUrl()
      url = `${getBackendUrl()}/agent/stream/${threadId}`;
      console.log('Streaming URL:', url);
    } else {
      // If FormData is provided, extract the thread ID from it
      threadId = threadIdOrFormData.get('thread_id') as string;
      
      if (!threadId) {
        throw new Error('thread_id is required in FormData');
      }
      
      // Use the correct endpoint path with /api already included by getBackendUrl()
      url = `${getBackendUrl()}/agent/stream/${threadId}`;
      console.log('Streaming URL (FormData):', url);
    }
    
    const eventSource = new EventSource(url);

    eventSource.onmessage = (event) => {
      try {
        console.log('Received SSE event:', event.data);

        // Handle the case where the data might be a direct JSON object from the backend
        let data;
        try {
          data = JSON.parse(event.data);
        } catch (parseError) {
          console.log('Failed to parse as JSON, treating as raw data:', event.data);
          // If it's not JSON, treat it as raw content
          callback.onMessage({
            content: event.data,
            type: 'content'
          });
          return;
        }

        console.log('Parsed event data:', data);

        // Filter out status messages that shouldn't be shown to the user
        if (data.status_type) {
          console.log('Filtering out status message:', data.status_type);

          // Handle completion status
          if (data.status_type === 'finish' || data.status_type === 'thread_run_end') {
            console.log('Stream completed');
            callback.onMessage({
              type: 'done'
            });
            eventSource.close();
            callback.onComplete();
          }
          return; // Don't show status messages to the user
        }

        // Handle the new backend format where content comes directly in the data
        if (data.role === 'assistant' && data.content) {
          console.log('Processing assistant content:', data.content);
          // Only process actual content, not metadata
          callback.onMessage({
            content: data.content,
            type: 'content',
            role: data.role
          });
          return;
        }

        // Handle different event types from our backend
        if (data.event === 'chunk') {
          // Check if content is a JSON string that needs parsing
          let processedContent = data.content;
          if (typeof data.content === 'string') {
            try {
              // Try to parse as JSON if it looks like JSON
              if (data.content.trim().startsWith('{') && data.content.includes('role')) {
                const parsedContent = JSON.parse(data.content);
                // If it has a content field, use that
                if (parsedContent.content) {
                  processedContent = parsedContent.content;
                }
                // Skip status messages even in chunk events
                if (parsedContent.status_type) {
                  console.log('Skipping status message in chunk:', parsedContent.status_type);
                  return;
                }
              }
            } catch (e) {
              console.log('Content is not valid JSON, using as-is');
              // Keep the original content if parsing fails
            }
          }

          // Only send content if it's not empty and not a status message
          if (processedContent && processedContent.trim()) {
            callback.onMessage({
              content: processedContent,
              type: 'content'
            });
          }
        } else if (data.event === 'thinking') {
          callback.onMessage({
            content: data.content,
            type: 'thinking'
          });
        } else if (data.event === 'done' || data.status_type === 'finish' || data.status_type === 'thread_run_end') {
          callback.onMessage({
            thread_id: data.thread_id,
            message_id: data.message_id,
            type: 'done'
          });
          // Close the EventSource when done
          eventSource.close();
          callback.onComplete();
        } else if (data.event === 'error') {
          console.error('Error from server:', data.message);
          callback.onError(new Error(data.message));
        } else if (data.status_type === 'thread_run_start' || data.status_type === 'assistant_response_start') {
          // Handle start events but don't close the stream
          console.log('Stream started:', data.status_type);
        } else {
          // Log unhandled events but don't display them to avoid showing raw JSON
          console.log('Unhandled event data (not displaying to user):', data);
        }
      } catch (error) {
        console.error('Error parsing streaming response:', error, '\nRaw data:', event.data);
        callback.onError(error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('EventSource error:', error);
      console.log('Connection state:', eventSource.readyState);
      
      // Check if the connection was closed abnormally
      if (eventSource.readyState === EventSource.CLOSED) {
        console.log('Connection was closed');
      }
      
      // Close the connection
      eventSource.close();
      
      // Create a more informative error object
      const errorInfo = {
        message: 'Connection error with streaming endpoint',
        originalError: error,
        url: url,
        readyState: eventSource.readyState
      };
      
      callback.onError(errorInfo);
    };

    // Return a cleanup function
    return () => {
      eventSource.close();
      callback.onComplete();
    };
  } catch (error) {
    console.error('Error setting up streaming:', error);
    callback.onError(error);
    return () => {};
  }
};

/**
 * Initiate a new agent conversation with FormData
 */
export type AgentType = 'ceo' | 'developer' | 'marketing' | 'product' | 'design' | 'finance' | 'research' | 'sales';

export interface Project {
  id: string;
  project_id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at?: string;
  owner_id?: string;
  account_id?: string;
  status?: string;
  metadata?: Record<string, any>;
  selectedAgents?: string[];
  agent_names?: string[];
  companyInfo?: string;
  settings?: {
    knowledgeAccess?: boolean;
    memoryPersistence?: boolean;
    autonomousMode?: boolean;
    sandboxMode?: boolean;
    collaborationMode?: boolean;
  };
  sandbox?: {
    sandbox_url?: string;
    vnc_preview?: string;
    selectedAgents?: string[];
    agent_names?: string[];
    companyInfo?: string;
    settings?: Record<string, any>;
    metadata?: Record<string, any>;
    pass?: string;
  };
}

export interface Message {
  id: string;
  type: string;
  content: string;
  created_at: string;
  metadata?: Record<string, any>;
}

export interface FileInfo {
  name: string;
  path: string;
  size: number;
  type: string;
  modified: string;
}

export interface AgentRun {
  id: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface BillingError {
  message: string;
  code: string;
}

export interface SubscriptionStatus {
  active: boolean;
  plan: string;
  usage: number;
  limit: number;
}

export interface Integration {
  id: string;
  name: string;
  type: string;
  config: Record<string, any>;
}

export interface AvailableIntegration {
  id: string;
  name: string;
  description: string;
  type: string;
}

export const initiateAgentWithFormData = async (formData: FormData): Promise<AgentInitiateResponse> => {
  try {
    // Extract agent_type and message from FormData
    const agent_type = formData.get('agent_type') as string || 'developer';
    const message = formData.get('message') as string;
    const model = formData.get('model_name') as string;
    const project_id = formData.get('project_id') as string;
    
    if (!message) {
      throw new Error('message is required in FormData');
    }
    
    // Create a request object from FormData
    const request: AgentInitiateRequest = {
      agent_type,
      message,
      model,
      project_id,
      metadata: {}
    };
    
    // Call the regular initiateAgent function
    return await initiateAgent(request);
  } catch (error) {
    console.error('Error initiating agent with FormData:', error);
    throw error;
  }
};

/**
 * Generate AI-powered smart suggestions based on conversation context
 */
export const generateSmartSuggestions = async (
  lastMessage: string,
  chatType: string,
  threadId?: string
): Promise<string[]> => {
  try {
    const response = await fetch(`${getBackendUrl()}/suggestions/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        last_message: lastMessage,
        chat_type: chatType,
        thread_id: threadId
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to generate suggestions: ${errorText}`);
    }

    const data = await response.json();
    return data.suggestions || [];
  } catch (error) {
    console.error('Error generating smart suggestions:', error);

    // Return empty array if AI fails - let the backend handle all fallbacks
    // This ensures we don't show any hardcoded suggestions on the frontend
    return [];
  }
};

/**
 * Test the backend connection with a simple health check
 */
export const testBackendConnection = async (): Promise<any> => {
  try {
    const response = await fetch(`${getBackendUrl()}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Health check failed: ${errorText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error testing backend connection:', error);
    throw error;
  }
};

/**
 * Check API health status with comprehensive system health check
 * This checks all critical components of the system including database, Redis, and LLM services
 */
export const checkApiHealth = async (): Promise<any> => {
  try {
    const response = await fetch(`${getBackendUrl()}/system-health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`System health check failed: ${errorText}`);
    }

    const healthData = await response.json();
    
    // If overall status is not 'ok', throw an error
    if (healthData.status !== 'ok') {
      throw new Error(`System health degraded: ${JSON.stringify(healthData.components)}`);
    }
    
    return healthData;
  } catch (error) {
    console.error('Error checking API health:', error);
    throw error;
  }
};

/**
 * Get all projects for the current user
 */
export async function getProjects(): Promise<Project[]> {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${getBackendUrl()}/projects`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      throw new Error(`Error fetching projects: ${response.status}`);
    }

    const data = await response.json();
    return data.projects;
  } catch (error) {
    console.error('Error fetching projects:', error);
    throw error;
  }
}

/**
 * Create a new project
 */
export async function createProject(projectData: Partial<Project>): Promise<Project> {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${getBackendUrl()}/projects`, {
      method: 'POST',
      headers,
      body: JSON.stringify(projectData),
    });

    if (!response.ok) {
      throw new Error(`Error creating project: ${response.status}`);
    }

    const data = await response.json();
    return data.project;
  } catch (error) {
    console.error('Error creating project:', error);
    throw error;
  }
}

/**
 * Update a project
 */
export async function updateProject(projectId: string, projectData: Partial<Project>): Promise<Project> {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${getBackendUrl()}/projects/${projectId}`, {
      method: 'PUT',
      headers,
      body: JSON.stringify(projectData),
    });

    if (!response.ok) {
      throw new Error(`Error updating project: ${response.status}`);
    }

    const data = await response.json();
    return data.project;
  } catch (error) {
    console.error('Error updating project:', error);
    throw error;
  }
}

/**
 * Get a single project by ID
 */
export async function getProject(projectId: string): Promise<Project> {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${getBackendUrl()}/projects/${projectId}`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      throw new Error(`Error fetching project: ${response.status}`);
    }

    const data = await response.json();
    return data.project;
  } catch (error) {
    console.error('Error fetching project:', error);
    throw error;
  }
}

/**
 * Get threads for a project
 */
export async function getThreads(projectId: string): Promise<any[]> {
  try {
    const response = await fetch(`${getBackendUrl()}/projects/${projectId}/threads`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error fetching threads: ${response.status}`);
    }

    const data = await response.json();
    return data.threads;
  } catch (error) {
    console.error('Error fetching threads:', error);
    throw error;
  }
}

/**
 * Get a thread by ID
 */
export async function getThread(threadId: string): Promise<any> {
  try {
    const response = await fetch(`${getBackendUrl()}threads/${threadId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error fetching thread: ${response.status}`);
    }

    const data = await response.json();
    return data.thread;
  } catch (error) {
    console.error('Error fetching thread:', error);
    throw error;
  }
}

/**
 * Create a new thread
 */
export async function createThread(projectId: string, threadData: any): Promise<any> {
  try {
    const headers = await getAuthHeaders();
    const response = await fetch(`${getBackendUrl()}/projects/${projectId}/threads`, {
      method: 'POST',
      headers,
      body: JSON.stringify(threadData),
    });

    if (!response.ok) {
      throw new Error(`Error creating thread: ${response.status}`);
    }

    const data = await response.json();
    return data.thread;
  } catch (error) {
    console.error('Error creating thread:', error);
    throw error;
  }
}

/**
 * Add a user message to a thread
 */
export async function addUserMessage(threadId: string, message: string): Promise<any> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/threads/${threadId}/messages`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ message, type: 'user' }),
    });

    if (!response.ok) {
      throw new Error(`Error adding message: ${response.status}`);
    }

    const data = await response.json();
    return data.message;
  } catch (error) {
    console.error('Error adding message:', error);
    throw error;
  }
}

/**
 * Start an agent
 */
export async function startAgent(threadId: string, agentType: string): Promise<any> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/threads/${threadId}/start-agent`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ agent_type: agentType }),
    });

    if (!response.ok) {
      throw new Error(`Error starting agent: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error starting agent:', error);
    throw error;
  }
}

/**
 * Stop an agent
 */
export async function stopAgent(agentRunId: string): Promise<any> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/agent-runs/${agentRunId}/stop`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error stopping agent: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error stopping agent:', error);
    throw error;
  }
}

/**
 * Get agent runs for a thread
 */
export async function getAgentRuns(threadId: string): Promise<AgentRun[]> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/threads/${threadId}/agent-runs`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error fetching agent runs: ${response.status}`);
    }

    const data = await response.json();
    return data.agent_runs;
  } catch (error) {
    console.error('Error fetching agent runs:', error);
    throw error;
  }
}

/**
 * Stream agent responses
 */
export async function streamAgent(threadId: string, callback: StreamingAgentCallback): Promise<() => void> {
  return streamAgentWithFormData(threadId, callback);
}

/**
 * Check billing status
 */
export async function checkBillingStatus(accountId: string): Promise<any> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/billing/status/${accountId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error checking billing status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error checking billing status:', error);
    throw error;
  }
}

/**
 * Get subscription status
 */
export async function getSubscription(accountId: string): Promise<SubscriptionStatus> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/billing/subscription/${accountId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error fetching subscription: ${response.status}`);
    }

    const data = await response.json();
    return data.subscription;
  } catch (error) {
    console.error('Error fetching subscription:', error);
    throw error;
  }
}

/**
 * Create portal session
 */
export async function createPortalSession(accountId: string): Promise<{ url: string }> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/billing/portal/${accountId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error creating portal session: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error creating portal session:', error);
    throw error;
  }
}

/**
 * List sandbox files
 */
export async function listSandboxFiles(projectId: string, path: string = '/'): Promise<FileInfo[]> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/projects/${projectId}/sandbox/files?path=${encodeURIComponent(path)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error listing files: ${response.status}`);
    }

    const data = await response.json();
    return data.files;
  } catch (error) {
    console.error('Error listing files:', error);
    throw error;
  }
}

/**
 * Get sandbox file content
 */
export async function getSandboxFileContent(projectId: string, filePath: string): Promise<string> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/projects/${projectId}/sandbox/files/content?path=${encodeURIComponent(filePath)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error getting file content: ${response.status}`);
    }

    const data = await response.json();
    return data.content;
  } catch (error) {
    console.error('Error getting file content:', error);
    throw error;
  }
}

/**
 * Get account integrations
 */
export async function getAccountIntegrations(accountId: string): Promise<Integration[]> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/accounts/${accountId}/integrations`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error fetching integrations: ${response.status}`);
    }

    const data = await response.json();
    return data.integrations;
  } catch (error) {
    console.error('Error fetching integrations:', error);
    throw error;
  }
}

/**
 * Get available integrations
 */
export async function getAvailableIntegrations(): Promise<AvailableIntegration[]> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/integrations/available`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error fetching available integrations: ${response.status}`);
    }

    const data = await response.json();
    return data.integrations;
  } catch (error) {
    console.error('Error fetching available integrations:', error);
    throw error;
  }
}

/**
 * Create integration
 */
export async function createIntegration(accountId: string, integrationData: Partial<Integration>): Promise<Integration> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/accounts/${accountId}/integrations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(integrationData),
    });

    if (!response.ok) {
      throw new Error(`Error creating integration: ${response.status}`);
    }

    const data = await response.json();
    return data.integration;
  } catch (error) {
    console.error('Error creating integration:', error);
    throw error;
  }
}

/**
 * Delete integration
 */
export async function deleteIntegration(accountId: string, integrationId: string): Promise<void> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/accounts/${accountId}/integrations/${integrationId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Error deleting integration: ${response.status}`);
    }
  } catch (error) {
    console.error('Error deleting integration:', error);
    throw error;
  }
}

/**
 * Execute integration action
 */
export async function executeIntegrationAction(accountId: string, integrationId: string, action: string, params: any): Promise<any> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/accounts/${accountId}/integrations/${integrationId}/execute`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ action, params }),
    });

    if (!response.ok) {
      throw new Error(`Error executing integration action: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error executing integration action:', error);
    throw error;
  }
}

// Agent Capabilities and Task Delegation Interfaces

export interface AgentCapability {
  name: string;
  role: string;
  capabilities: Record<string, number>;
  can_delegate_to: string[];
  description: string;
}

export interface TaskDelegationRequest {
  from_agent_id: string;
  task_description: string;
  required_capabilities?: string[];
  to_agent_id?: string;
  thread_id?: string;
  metadata?: Record<string, any>;
}

export interface TaskAcceptanceRequest {
  agent_id: string;
  task_id: string;
  thread_id?: string;
  metadata?: Record<string, any>;
}

export interface TaskCompletionRequest {
  agent_id: string;
  task_id: string;
  result: string;
  thread_id?: string;
  metadata?: Record<string, any>;
}

export interface TaskDelegationResponse {
  success: boolean;
  from_agent_id: string;
  to_agent_id: string;
  message_id: string;
  thread_id?: string;
}

export interface TaskAcceptanceResponse {
  success: boolean;
  agent_id: string;
  task_id: string;
  thread_id?: string;
}

export interface TaskCompletionResponse {
  success: boolean;
  agent_id: string;
  task_id: string;
  thread_id?: string;
}

/**
 * Get capabilities of all agent roles
 */
export async function getAllAgentCapabilities(): Promise<Record<string, AgentCapability>> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/agent/capabilities`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Error getting agent capabilities: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting agent capabilities:', error);
    throw error;
  }
}

/**
 * Get capabilities of a specific agent
 */
export async function getAgentCapabilities(agentId: string): Promise<AgentCapability> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/agent/capabilities/${agentId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Error getting agent capabilities: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting agent capabilities:', error);
    throw error;
  }
}

/**
 * Delegate a task from one agent to another
 */
export async function delegateTask(request: TaskDelegationRequest): Promise<TaskDelegationResponse> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/agent/delegate-task`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Error delegating task: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error delegating task:', error);
    throw error;
  }
}

/**
 * Accept a delegated task
 */
export async function acceptTask(request: TaskAcceptanceRequest): Promise<TaskAcceptanceResponse> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/agent/accept-task`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Error accepting task: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error accepting task:', error);
    throw error;
  }
}

/**
 * Complete a delegated task and send results back
 */
export async function completeTask(request: TaskCompletionRequest): Promise<TaskCompletionResponse> {
  try {
    const response = await fetch(`${getBackendUrl()}/api/agent/complete-task`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || `Error completing task: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error completing task:', error);
    throw error;
  }
}