from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException, Depends, Form, File, UploadFile, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from contextlib import asynccontextmanager
# Import from our local agentpress package
from agentpress.thread_manager import ThreadManager
from agentpress.agent_communication import AgentCommunicationManager
from agentpress.response_processor import ResponseProcessor
from agentpress.tool_registry import ToolRegistry
from services.supabase import DBConnection
from datetime import datetime, timezone
from dotenv import load_dotenv
from utils.config import config, EnvMode
import asyncio
from utils.logger import logger
import uuid
import time
from collections import OrderedDict
from typing import Dict, Any, Optional, List, AsyncGenerator
import json

# Import the routes module
from agent.routes import router

# Import sandbox module (but not the api)
from sandbox.sandbox import get_or_start_sandbox

# Load environment variables (these will be available through config)
load_dotenv()

# Initialize managers
db = DBConnection()
instance_id = "single"

# Rate limiter state
ip_tracker = OrderedDict()
MAX_CONCURRENT_IPS = 25

# Initialize managers
thread_manager = None
agent_communication_manager = None
response_processor = None

# Model configurations
MODEL_CONFIGS = {
    "claude-3-5-sonnet-20240620": {
        "provider": "anthropic",
        "model": "claude-3-5-sonnet-20240620",
        "temperature": 0.7,
        "max_tokens": 4000
    },
    "gpt-4o": {
        "provider": "openai",
        "model": "gpt-4o",
        "temperature": 0.7,
        "max_tokens": 4000
    }
}

# Helper function to get system prompt for different agent types
def get_system_prompt(agent_type: str) -> str:
    """Get the system prompt for a specific agent type."""
    try:
        if agent_type == "developer":
            from agent.developerAgent.prompt import SYSTEM_PROMPT
            return SYSTEM_PROMPT
        elif agent_type == "ceo":
            from agent.ceoAgent.prompt import SYSTEM_PROMPT
            return SYSTEM_PROMPT
        elif agent_type == "design":
            from agent.designAgent.prompt import SYSTEM_PROMPT
            return SYSTEM_PROMPT
        elif agent_type == "product":
            from agent.productAgent.prompt import SYSTEM_PROMPT
            return SYSTEM_PROMPT
        elif agent_type == "marketing":
            from agent.marketingAgent.prompt import SYSTEM_PROMPT
            return SYSTEM_PROMPT
        elif agent_type == "finance":
            from agent.financeAgent.prompt import SYSTEM_PROMPT
            return SYSTEM_PROMPT
        elif agent_type == "sales":
            from agent.salesAgent.prompt import SYSTEM_PROMPT
            return SYSTEM_PROMPT
        elif agent_type == "research":
            from agent.researchAgent.prompt import SYSTEM_PROMPT
            return SYSTEM_PROMPT
        else:
            # Default to developer agent if type not found
            from agent.developerAgent.prompt import SYSTEM_PROMPT
            return SYSTEM_PROMPT
    except ImportError as e:
        logger.error(f"Error importing system prompt for agent type {agent_type}: {e}")
        # Fallback to a generic prompt
        return "You are a helpful AI assistant."

# Initialize function to be called from main api.py
async def initialize(db_connection, instance_id_value):
    """Initialize the agent API with shared resources."""
    global thread_manager, agent_communication_manager, response_processor, db, instance_id
    
    # Store the database connection and instance ID
    db = db_connection
    instance_id = instance_id_value
    
    logger.info(f"Initializing agent API with instance ID: {instance_id}")
    
    try:
        # Initialize tool registry for the response processor
        tool_registry = ToolRegistry()
        logger.info("Tool registry initialized")
        
        # Initialize the thread manager (it creates its own DB connection)
        thread_manager = ThreadManager()
        logger.info("Thread manager initialized")

        # Register tools with the thread manager
        from agent.tools import CodeExecutionTool
        thread_manager.add_tool(CodeExecutionTool)
        logger.info("Code execution tool registered")
        
        # Initialize the agent communication manager
        agent_communication_manager = AgentCommunicationManager()
        logger.info("Agent communication manager initialized")
        
        # Initialize the response processor with the tool registry and thread manager
        response_processor = ResponseProcessor(
            tool_registry=tool_registry,
            add_message_callback=thread_manager.add_message
        )
        logger.info("Response processor initialized")
        
        # Register route implementations
        implement_routes()
        logger.info("Route implementations registered")
        
        logger.info(f"Agent API successfully initialized with instance ID: {instance_id_value}")
    except Exception as e:
        logger.error(f"Error initializing agent API: {e}")
        raise

# Cleanup function to be called during shutdown
async def cleanup():
    """Clean up agent resources."""
    logger.info("Cleaning up agent resources")
    # Add any cleanup logic here

# Function to implement the route handlers
def implement_routes():
    """Implement the route handlers defined in routes.py"""
    # Override the route handlers with actual implementations
    for route in router.routes:
        if route.path == "/agent/initiate":
            route.endpoint = initiate_agent
        elif route.path == "/agent/stream/{thread_id}":
            route.endpoint = stream_agent_response
        elif route.path == "/send-message":
            route.endpoint = send_message
        elif route.path == "/messages/{thread_id}":
            route.endpoint = get_messages
        elif route.path == "/agent-status/{agent_run_id}":
            route.endpoint = get_agent_status

# Import the request and response models from routes
from agent.routes import AgentInitiateRequest, AgentInitiateResponse

# Implement the actual route handlers
async def initiate_agent(request: AgentInitiateRequest):
    """Initiate a new agent conversation."""
    try:
        # Extract data from the request model
        agent_type = request.agent_type
        model = request.model or "claude-3-5-sonnet-20240620"
        message = request.message
        project_id = request.project_id
        metadata = request.metadata or {}
        
        logger.info(f"Initiating agent conversation with agent_type: {agent_type}, model: {model}")
        
        # Get the system prompt for the agent type
        system_prompt = get_system_prompt(agent_type)
        
        # Create a new thread
        thread_id = str(uuid.uuid4())
        agent_run_id = str(uuid.uuid4())
        
        # Initialize the thread with the system prompt
        await thread_manager.create_thread(thread_id)
        await thread_manager.add_message(thread_id, "system", system_prompt)
        
        # Add the user message to the thread
        await thread_manager.add_message(thread_id, "user", message)
        
        # Store metadata about the thread
        await thread_manager.update_thread_metadata(thread_id, {
            "agent_type": agent_type,
            "model": model,
            "project_id": project_id,
            "agent_run_id": agent_run_id,
            "user_metadata": metadata
        })
        
        # Return the thread ID and agent run ID as an AgentInitiateResponse object
        return AgentInitiateResponse(
            thread_id=thread_id,
            agent_run_id=agent_run_id,
            message="Agent conversation initiated successfully"
        )
    except Exception as e:
        logger.error(f"Error initiating agent conversation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def stream_agent_response(thread_id: str):
    """Stream an agent response for a given thread."""
    try:
        # Get the thread metadata
        thread_metadata = await thread_manager.get_thread_metadata(thread_id)
        if not thread_metadata:
            raise HTTPException(status_code=404, detail=f"Thread {thread_id} not found")
        
        # Get the model configuration
        model = thread_metadata.get("model", "claude-3-5-sonnet-20240620")
        model_config = MODEL_CONFIGS.get(model, MODEL_CONFIGS["claude-3-5-sonnet-20240620"])
        
        # Run the thread with the specified model
        llm_response = await thread_manager.run_thread(thread_id, model_config)
        
        # Process the streaming response
        response_stream = process_streaming_response(llm_response, thread_id)
        
        # Return a streaming response
        return StreamingResponse(response_stream, media_type="text/event-stream")
    except Exception as e:
        logger.error(f"Error streaming agent response for thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Helper function to format streaming responses for the frontend
async def process_streaming_response(llm_response, thread_id):
    """Process a streaming LLM response and format it for the frontend."""
    try:
        message_id = str(uuid.uuid4())
        content_buffer = ""
        first_chunk = True
        
        # Get thread metadata to determine which agents are involved
        thread_metadata = await thread_manager.get_thread_metadata(thread_id)
        collaboration_mode = thread_metadata.get("collaboration_mode", False)
        selected_agents = thread_metadata.get("selected_agents", [])
        
        # Default to the first agent if available
        primary_agent = selected_agents[0] if selected_agents else "Chloe"
        
        # Get agent descriptions for proper identification
        agent_descriptions = {
            "Kenard": "CEO",
            "Alex": "Lead Developer",
            "Chloe": "Marketing",
            "Mark": "Product Manager",
            "Maisie": "Designer",
            "Finley": "Finance",
            "Garek": "Research",
            "Hannah": "Sales"
        }
        
        # Get the description for the primary agent
        primary_agent_desc = agent_descriptions.get(primary_agent, "Specialist")
        
        # Prepare the agent identifier prefix based on the primary agent
        agent_prefix = f"**{primary_agent} ({primary_agent_desc}):**"
        
        # For collaboration mode, we don't add a prefix and let the model handle it
        should_add_prefix = not collaboration_mode
        
        async for chunk in response_processor.process_streaming_response(llm_response, thread_id):
            # Process the chunk content
            chunk_content = ""
            if isinstance(chunk, dict) and 'content' in chunk:
                chunk_content = chunk['content']
            elif isinstance(chunk, str):
                chunk_content = chunk
            
            # For the first chunk, ensure it starts with the correct agent identifier in single agent mode
            if first_chunk and should_add_prefix:
                first_chunk = False
                
                # Check if content already has an agent identifier
                has_agent_prefix = False
                for agent_name, agent_desc in agent_descriptions.items():
                    if chunk_content.strip().startswith(f"**{agent_name}") or \
                       chunk_content.strip().startswith(f"**{agent_desc}"):
                        has_agent_prefix = True
                        break
                
                # Add the agent prefix if not already present
                if not has_agent_prefix:
                    if isinstance(chunk, dict) and 'content' in chunk:
                        chunk['content'] = f"{agent_prefix} {chunk['content']}"
                        chunk_content = chunk['content']
                    elif isinstance(chunk, str):
                        chunk = f"{agent_prefix} {chunk}"
                        chunk_content = chunk
            
            # Accumulate content
            if isinstance(chunk, dict) and 'content' in chunk:
                content_buffer += chunk['content']
            elif isinstance(chunk, str):
                content_buffer += chunk
                
            # Prepare the response data with agent metadata
            response_data = {
                "type": "message",
                "content": content_buffer,
                "thread_id": thread_id,
                "message_id": message_id,
                "role": "assistant",
                "metadata": {
                    "collaboration_mode": collaboration_mode,
                    "selected_agents": selected_agents,
                    "primary_agent": primary_agent
                }
            }
            
            # Convert to JSON and yield as SSE data
            yield f"data: {json.dumps(response_data)}\n\n"
    except Exception as e:
        logger.error(f"Error processing streaming response: {e}")
        # Yield an error message
        error_data = {
            "type": "error",
            "content": f"Error processing response: {str(e)}",
            "thread_id": thread_id
        }
        yield f"data: {json.dumps(error_data)}\n\n"

from agent.routes import SendMessageRequest

async def send_message(request: SendMessageRequest):
    """Send a follow-up message to an existing thread."""
    try:
        # Extract data from the request model
        thread_id = request.thread_id
        message = request.message
        metadata = request.metadata or {}
        
        if not thread_id or not message:
            raise HTTPException(status_code=400, detail="thread_id and message are required")
        
        # Check if the thread exists
        if not await thread_manager.thread_exists(thread_id):
            raise HTTPException(status_code=404, detail=f"Thread {thread_id} not found")
        
        # Add the user message to the thread
        await thread_manager.add_message(thread_id, "user", message)
        
        # Update the thread metadata with any new metadata
        if metadata:
            thread_metadata = await thread_manager.get_thread_metadata(thread_id)
            user_metadata = thread_metadata.get("user_metadata", {})
            user_metadata.update(metadata)
            await thread_manager.update_thread_metadata(thread_id, {"user_metadata": user_metadata})
        
        # Return success
        return {
            "status": "success",
            "message": "Message added to thread"
        }
    except Exception as e:
        logger.error(f"Error sending message: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def get_messages(thread_id: str):
    """Get all messages for a thread."""
    try:
        # Check if the thread exists
        if not await thread_manager.thread_exists(thread_id):
            raise HTTPException(status_code=404, detail=f"Thread {thread_id} not found")
        
        # Get the messages for the thread
        messages = await thread_manager.get_messages(thread_id)
        
        # Return the messages
        return {
            "thread_id": thread_id,
            "messages": messages
        }
    except Exception as e:
        logger.error(f"Error getting messages for thread {thread_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def get_agent_status(agent_run_id: str):
    """Get the status of an agent run."""
    try:
        # This is a placeholder implementation
        # In a real implementation, you would check the status of the agent run in the database
        return {
            "status": "completed",
            "message": "Agent run completed successfully"
        }
    except Exception as e:
        logger.error(f"Error getting status for agent run {agent_run_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Add a health check endpoint to the router
@router.get("/health")
async def health_check():
    """Health check endpoint to verify API is working."""
    logger.info("Health check endpoint called")
    return {
        "status": "ok", 
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "instance_id": "agent-api"
    }