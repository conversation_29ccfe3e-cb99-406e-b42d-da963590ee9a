"""
AgentPress: Core framework for Siden.ai's AGI agent system.

This package provides the foundation for creating, managing, and executing
agent-based conversations with tool integration and context management.

Main components:
- ThreadManager: Manages conversation threads and tool execution
- ContextManager: Handles token counting and thread summarization
- ToolRegistry: Maintains tool instances and schemas
- ResponseProcessor: Processes LLM responses and executes tool calls
- Tool: Base class for implementing agent tools
"""

from .thread_manager import ThreadManager
from .context_manager import ContextManager
from .tool_registry import ToolRegistry
from .response_processor import ResponseProcessor, ProcessorConfig
from .tool import (
    Tool, ToolResult, ToolSchema, SchemaType,
    openapi_schema, xml_schema, XMLTagSchema
)
from .agent_communication import (
    AgentCommunicationManager, MessageType
)
from .agent_memory import (
    AgentMemoryManager, MemoryType, MemoryPriority
)

__all__ = [
    # Core components
    'ThreadManager',
    'ContextManager',
    'ToolRegistry',
    'ResponseProcessor',
    'ProcessorConfig',

    # Tool system
    'Tool',
    'ToolResult',
    'ToolSchema',
    'SchemaType',
    'openapi_schema',
    'xml_schema',
    'XMLTagSchema',

    # Agent communication
    'AgentCommunicationManager',
    'MessageType',

    # Agent memory
    'AgentMemoryManager',
    'MemoryType',
    'MemoryPriority'
]