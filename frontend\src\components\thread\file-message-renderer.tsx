"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, Download, ExternalLink, Code, Eye } from "lucide-react";
import { FileRenderer, getFileTypeFromExtension, getLanguageFromExtension } from "@/components/file-renderers";
import { cn } from "@/lib/utils";

interface FileMessageRendererProps {
  fileName: string;
  content: string;
  binaryUrl?: string | null;
  className?: string;
  onViewFull?: () => void;
  project?: {
    sandbox?: {
      sandbox_url?: string;
      vnc_preview?: string;
      pass?: string;
    }
  };
}

export function FileMessageRenderer({
  fileName,
  content,
  binaryUrl = null,
  className,
  onViewFull,
  project
}: FileMessageRendererProps) {
  const [viewMode, setViewMode] = useState<'preview' | 'code'>('preview');
  const fileType = getFileTypeFromExtension(fileName);
  const language = getLanguageFromExtension(fileName);
  
  const isImage = fileType === 'image';
  const isPdf = fileType === 'pdf';
  const isMarkdown = fileType === 'markdown';
  const isCode = fileType === 'code';
  const isText = fileType === 'text';
  const isHtml = fileName.toLowerCase().endsWith('.html');
  
  // For code files, limit the preview to a reasonable number of lines
  const previewContent = React.useMemo(() => {
    if (isCode || isText) {
      const lines = content.split('\n');
      const maxLines = 15;
      if (lines.length > maxLines) {
        return lines.slice(0, maxLines).join('\n') + '\n...';
      }
    }
    return content;
  }, [content, isCode, isText]);

  return (
    <div className={cn("border rounded-md overflow-hidden bg-muted/30", className)}>
      {/* File header */}
      <div className="flex items-center justify-between p-2 bg-muted border-b">
        <div className="flex items-center">
          <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
          <span className="text-sm font-medium">{fileName}</span>
        </div>
        <div className="flex items-center gap-1">
          {(isCode || isText || isMarkdown || isHtml) && (
            <div className="flex border rounded-md overflow-hidden mr-2">
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "h-7 px-2 rounded-none",
                  viewMode === 'preview' && "bg-muted-foreground/10"
                )}
                onClick={() => setViewMode('preview')}
              >
                <Eye className="h-3.5 w-3.5 mr-1" />
                Preview
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "h-7 px-2 rounded-none",
                  viewMode === 'code' && "bg-muted-foreground/10"
                )}
                onClick={() => setViewMode('code')}
              >
                <Code className="h-3.5 w-3.5 mr-1" />
                Code
              </Button>
            </div>
          )}
          {onViewFull && (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 px-2"
              onClick={onViewFull}
            >
              <ExternalLink className="h-3.5 w-3.5 mr-1" />
              Full View
            </Button>
          )}
          {binaryUrl && (
            <Button
              variant="ghost"
              size="sm"
              className="h-7 px-2"
              onClick={() => {
                const a = document.createElement('a');
                a.href = binaryUrl;
                a.download = fileName;
                a.click();
              }}
            >
              <Download className="h-3.5 w-3.5 mr-1" />
              Download
            </Button>
          )}
        </div>
      </div>
      
      {/* File content */}
      <div className="max-h-[300px] overflow-auto">
        {viewMode === 'preview' || !(isCode || isText || isMarkdown || isHtml) ? (
          <div className="h-full">
            <FileRenderer
              content={previewContent}
              binaryUrl={binaryUrl}
              fileName={fileName}
              className="max-h-[300px]"
              project={project}
            />
          </div>
        ) : (
          <pre className="p-3 text-xs font-mono whitespace-pre-wrap overflow-x-auto">
            {content}
          </pre>
        )}
      </div>
    </div>
  );
}
