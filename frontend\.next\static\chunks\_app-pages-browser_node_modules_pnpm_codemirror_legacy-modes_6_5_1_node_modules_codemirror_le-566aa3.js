"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_codemirror_legacy-modes_6_5_1_node_modules_codemirror_le-566aa3"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/sql.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/sql.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cassandra: () => (/* binding */ cassandra),\n/* harmony export */   esper: () => (/* binding */ esper),\n/* harmony export */   gpSQL: () => (/* binding */ gpSQL),\n/* harmony export */   gql: () => (/* binding */ gql),\n/* harmony export */   hive: () => (/* binding */ hive),\n/* harmony export */   mariaDB: () => (/* binding */ mariaDB),\n/* harmony export */   msSQL: () => (/* binding */ msSQL),\n/* harmony export */   mySQL: () => (/* binding */ mySQL),\n/* harmony export */   pgSQL: () => (/* binding */ pgSQL),\n/* harmony export */   plSQL: () => (/* binding */ plSQL),\n/* harmony export */   sparkSQL: () => (/* binding */ sparkSQL),\n/* harmony export */   sql: () => (/* binding */ sql),\n/* harmony export */   sqlite: () => (/* binding */ sqlite),\n/* harmony export */   standardSQL: () => (/* binding */ standardSQL)\n/* harmony export */ });\nfunction sql(parserConfig) {\n  var client         = parserConfig.client || {},\n      atoms          = parserConfig.atoms || {\"false\": true, \"true\": true, \"null\": true},\n      builtin        = parserConfig.builtin || set(defaultBuiltin),\n      keywords       = parserConfig.keywords || set(sqlKeywords),\n      operatorChars  = parserConfig.operatorChars || /^[*+\\-%<>!=&|~^\\/]/,\n      support        = parserConfig.support || {},\n      hooks          = parserConfig.hooks || {},\n      dateSQL        = parserConfig.dateSQL || {\"date\" : true, \"time\" : true, \"timestamp\" : true},\n      backslashStringEscapes = parserConfig.backslashStringEscapes !== false,\n      brackets       = parserConfig.brackets || /^[\\{}\\(\\)\\[\\]]/,\n      punctuation    = parserConfig.punctuation || /^[;.,:]/\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n\n    // call hooks from the mime type\n    if (hooks[ch]) {\n      var result = hooks[ch](stream, state);\n      if (result !== false) return result;\n    }\n\n    if (support.hexNumber &&\n      ((ch == \"0\" && stream.match(/^[xX][0-9a-fA-F]+/))\n      || (ch == \"x\" || ch == \"X\") && stream.match(/^'[0-9a-fA-F]*'/))) {\n      // hex\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/hexadecimal-literals.html\n      return \"number\";\n    } else if (support.binaryNumber &&\n      (((ch == \"b\" || ch == \"B\") && stream.match(/^'[01]+'/))\n      || (ch == \"0\" && stream.match(/^b[01]*/)))) {\n      // bitstring\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/bit-field-literals.html\n      return \"number\";\n    } else if (ch.charCodeAt(0) > 47 && ch.charCodeAt(0) < 58) {\n      // numbers\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/number-literals.html\n      stream.match(/^[0-9]*(\\.[0-9]+)?([eE][-+]?[0-9]+)?/);\n      support.decimallessFloat && stream.match(/^\\.(?!\\.)/);\n      return \"number\";\n    } else if (ch == \"?\" && (stream.eatSpace() || stream.eol() || stream.eat(\";\"))) {\n      // placeholders\n      return \"macroName\";\n    } else if (ch == \"'\" || (ch == '\"' && support.doubleQuote)) {\n      // strings\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/string-literals.html\n      state.tokenize = tokenLiteral(ch);\n      return state.tokenize(stream, state);\n    } else if ((((support.nCharCast && (ch == \"n\" || ch == \"N\"))\n        || (support.charsetCast && ch == \"_\" && stream.match(/[a-z][a-z0-9]*/i)))\n        && (stream.peek() == \"'\" || stream.peek() == '\"'))) {\n      // charset casting: _utf8'str', N'str', n'str'\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/string-literals.html\n      return \"keyword\";\n    } else if (support.escapeConstant && (ch == \"e\" || ch == \"E\")\n        && (stream.peek() == \"'\" || (stream.peek() == '\"' && support.doubleQuote))) {\n      // escape constant: E'str', e'str'\n      // ref: https://www.postgresql.org/docs/current/sql-syntax-lexical.html#SQL-SYNTAX-STRINGS-ESCAPE\n      state.tokenize = function(stream, state) {\n        return (state.tokenize = tokenLiteral(stream.next(), true))(stream, state);\n      }\n      return \"keyword\";\n    } else if (support.commentSlashSlash && ch == \"/\" && stream.eat(\"/\")) {\n      // 1-line comment\n      stream.skipToEnd();\n      return \"comment\";\n    } else if ((support.commentHash && ch == \"#\")\n        || (ch == \"-\" && stream.eat(\"-\") && (!support.commentSpaceRequired || stream.eat(\" \")))) {\n      // 1-line comments\n      // ref: https://kb.askmonty.org/en/comment-syntax/\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (ch == \"/\" && stream.eat(\"*\")) {\n      // multi-line comments\n      // ref: https://kb.askmonty.org/en/comment-syntax/\n      state.tokenize = tokenComment(1);\n      return state.tokenize(stream, state);\n    } else if (ch == \".\") {\n      // .1 for 0.1\n      if (support.zerolessFloat && stream.match(/^(?:\\d+(?:e[+-]?\\d+)?)/i))\n        return \"number\";\n      if (stream.match(/^\\.+/))\n        return null\n      // .table_name (ODBC)\n      // // ref: http://dev.mysql.com/doc/refman/5.6/en/identifier-qualifiers.html\n      if (support.ODBCdotTable && stream.match(/^[\\w\\d_$#]+/))\n        return \"type\";\n    } else if (operatorChars.test(ch)) {\n      // operators\n      stream.eatWhile(operatorChars);\n      return \"operator\";\n    } else if (brackets.test(ch)) {\n      // brackets\n      return \"bracket\";\n    } else if (punctuation.test(ch)) {\n      // punctuation\n      stream.eatWhile(punctuation);\n      return \"punctuation\";\n    } else if (ch == '{' &&\n        (stream.match(/^( )*(d|D|t|T|ts|TS)( )*'[^']*'( )*}/) || stream.match(/^( )*(d|D|t|T|ts|TS)( )*\"[^\"]*\"( )*}/))) {\n      // dates (weird ODBC syntax)\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/date-and-time-literals.html\n      return \"number\";\n    } else {\n      stream.eatWhile(/^[_\\w\\d]/);\n      var word = stream.current().toLowerCase();\n      // dates (standard SQL syntax)\n      // ref: http://dev.mysql.com/doc/refman/5.5/en/date-and-time-literals.html\n      if (dateSQL.hasOwnProperty(word) && (stream.match(/^( )+'[^']*'/) || stream.match(/^( )+\"[^\"]*\"/)))\n        return \"number\";\n      if (atoms.hasOwnProperty(word)) return \"atom\";\n      if (builtin.hasOwnProperty(word)) return \"type\";\n      if (keywords.hasOwnProperty(word)) return \"keyword\";\n      if (client.hasOwnProperty(word)) return \"builtin\";\n      return null;\n    }\n  }\n\n  // 'string', with char specified in quote escaped by '\\'\n  function tokenLiteral(quote, backslashEscapes) {\n    return function(stream, state) {\n      var escaped = false, ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == quote && !escaped) {\n          state.tokenize = tokenBase;\n          break;\n        }\n        escaped = (backslashStringEscapes || backslashEscapes) && !escaped && ch == \"\\\\\";\n      }\n      return \"string\";\n    };\n  }\n  function tokenComment(depth) {\n    return function(stream, state) {\n      var m = stream.match(/^.*?(\\/\\*|\\*\\/)/)\n      if (!m) stream.skipToEnd()\n      else if (m[1] == \"/*\") state.tokenize = tokenComment(depth + 1)\n      else if (depth > 1) state.tokenize = tokenComment(depth - 1)\n      else state.tokenize = tokenBase\n      return \"comment\"\n    }\n  }\n\n  function pushContext(stream, state, type) {\n    state.context = {\n      prev: state.context,\n      indent: stream.indentation(),\n      col: stream.column(),\n      type: type\n    };\n  }\n\n  function popContext(state) {\n    state.indent = state.context.indent;\n    state.context = state.context.prev;\n  }\n\n  return {\n    name: \"sql\",\n\n    startState: function() {\n      return {tokenize: tokenBase, context: null};\n    },\n\n    token: function(stream, state) {\n      if (stream.sol()) {\n        if (state.context && state.context.align == null)\n          state.context.align = false;\n      }\n      if (state.tokenize == tokenBase && stream.eatSpace()) return null;\n\n      var style = state.tokenize(stream, state);\n      if (style == \"comment\") return style;\n\n      if (state.context && state.context.align == null)\n        state.context.align = true;\n\n      var tok = stream.current();\n      if (tok == \"(\")\n        pushContext(stream, state, \")\");\n      else if (tok == \"[\")\n        pushContext(stream, state, \"]\");\n      else if (state.context && state.context.type == tok)\n        popContext(state);\n      return style;\n    },\n\n    indent: function(state, textAfter, iCx) {\n      var cx = state.context;\n      if (!cx) return null;\n      var closing = textAfter.charAt(0) == cx.type;\n      if (cx.align) return cx.col + (closing ? 0 : 1);\n      else return cx.indent + (closing ? 0 : iCx.unit);\n    },\n\n    languageData: {\n      commentTokens: {\n        line: support.commentSlashSlash ? \"//\" : support.commentHash ? \"#\" : \"--\",\n        block: {open: \"/*\", close: \"*/\"}\n      },\n      closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"]}\n    }\n  };\n};\n\n// `identifier`\nfunction hookIdentifier(stream) {\n  // MySQL/MariaDB identifiers\n  // ref: http://dev.mysql.com/doc/refman/5.6/en/identifier-qualifiers.html\n  var ch;\n  while ((ch = stream.next()) != null) {\n    if (ch == \"`\" && !stream.eat(\"`\")) return \"string.special\";\n  }\n  stream.backUp(stream.current().length - 1);\n  return stream.eatWhile(/\\w/) ? \"string.special\" : null;\n}\n\n// \"identifier\"\nfunction hookIdentifierDoublequote(stream) {\n  // Standard SQL /SQLite identifiers\n  // ref: http://web.archive.org/web/20160813185132/http://savage.net.au/SQL/sql-99.bnf.html#delimited%20identifier\n  // ref: http://sqlite.org/lang_keywords.html\n  var ch;\n  while ((ch = stream.next()) != null) {\n    if (ch == \"\\\"\" && !stream.eat(\"\\\"\")) return \"string.special\";\n  }\n  stream.backUp(stream.current().length - 1);\n  return stream.eatWhile(/\\w/) ? \"string.special\" : null;\n}\n\n// variable token\nfunction hookVar(stream) {\n  // variables\n  // @@prefix.varName @varName\n  // varName can be quoted with ` or ' or \"\n  // ref: http://dev.mysql.com/doc/refman/5.5/en/user-variables.html\n  if (stream.eat(\"@\")) {\n    stream.match('session.');\n    stream.match('local.');\n    stream.match('global.');\n  }\n\n  if (stream.eat(\"'\")) {\n    stream.match(/^.*'/);\n    return \"string.special\";\n  } else if (stream.eat('\"')) {\n    stream.match(/^.*\"/);\n    return \"string.special\";\n  } else if (stream.eat(\"`\")) {\n    stream.match(/^.*`/);\n    return \"string.special\";\n  } else if (stream.match(/^[0-9a-zA-Z$\\.\\_]+/)) {\n    return \"string.special\";\n  }\n  return null;\n};\n\n// short client keyword token\nfunction hookClient(stream) {\n  // \\N means NULL\n  // ref: http://dev.mysql.com/doc/refman/5.5/en/null-values.html\n  if (stream.eat(\"N\")) {\n    return \"atom\";\n  }\n  // \\g, etc\n  // ref: http://dev.mysql.com/doc/refman/5.5/en/mysql-commands.html\n  return stream.match(/^[a-zA-Z.#!?]/) ? \"string.special\" : null;\n}\n\n// these keywords are used by all SQL dialects (however, a mode can still overwrite it)\nvar sqlKeywords = \"alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit \";\n\n// turn a space-separated list into an array\nfunction set(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar defaultBuiltin = \"bool boolean bit blob enum long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision real date datetime year unsigned signed decimal numeric\"\n\n// A generic SQL Mode. It's not a standard, it just try to support what is generally supported\nconst standardSQL = sql({\n  keywords: set(sqlKeywords + \"begin\"),\n  builtin: set(defaultBuiltin),\n  atoms: set(\"false true null unknown\"),\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable doubleQuote binaryNumber hexNumber\")\n});\n\nconst msSQL = sql({\n  client: set(\"$partition binary_checksum checksum connectionproperty context_info current_request_id error_line error_message error_number error_procedure error_severity error_state formatmessage get_filestream_transaction_context getansinull host_id host_name isnull isnumeric min_active_rowversion newid newsequentialid rowcount_big xact_state object_id\"),\n  keywords: set(sqlKeywords + \"begin trigger proc view index for add constraint key primary foreign collate clustered nonclustered declare exec go if use index holdlock nolock nowait paglock readcommitted readcommittedlock readpast readuncommitted repeatableread rowlock serializable snapshot tablock tablockx updlock with\"),\n  builtin: set(\"bigint numeric bit smallint decimal smallmoney int tinyint money float real char varchar text nchar nvarchar ntext binary varbinary image cursor timestamp hierarchyid uniqueidentifier sql_variant xml table \"),\n  atoms: set(\"is not null like and or in left right between inner outer join all any some cross unpivot pivot exists\"),\n  operatorChars: /^[*+\\-%<>!=^\\&|\\/]/,\n  brackets: /^[\\{}\\(\\)]/,\n  punctuation: /^[;.,:/]/,\n  backslashStringEscapes: false,\n  dateSQL: set(\"date datetimeoffset datetime2 smalldatetime datetime time\"),\n  hooks: {\n    \"@\":   hookVar\n  }\n});\n\nconst mySQL = sql({\n  client: set(\"charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee\"),\n  keywords: set(sqlKeywords + \"accessible action add after algorithm all analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general get global grant grants group group_concat handler hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show signal slave slow smallint snapshot soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views warnings when while with work write xa xor year_month zerofill begin do then else loop repeat\"),\n  builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=&|^]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired\"),\n  hooks: {\n    \"@\":   hookVar,\n    \"`\":   hookIdentifier,\n    \"\\\\\":  hookClient\n  }\n});\n\nconst mariaDB = sql({\n  client: set(\"charset clear connect edit ego exit go help nopager notee nowarning pager print prompt quit rehash source status system tee\"),\n  keywords: set(sqlKeywords + \"accessible action add after algorithm all always analyze asensitive at authors auto_increment autocommit avg avg_row_length before binary binlog both btree cache call cascade cascaded case catalog_name chain change changed character check checkpoint checksum class_origin client_statistics close coalesce code collate collation collations column columns comment commit committed completion concurrent condition connection consistent constraint contains continue contributors convert cross current current_date current_time current_timestamp current_user cursor data database databases day_hour day_microsecond day_minute day_second deallocate dec declare default delay_key_write delayed delimiter des_key_file describe deterministic dev_pop dev_samp deviance diagnostics directory disable discard distinctrow div dual dumpfile each elseif enable enclosed end ends engine engines enum errors escape escaped even event events every execute exists exit explain extended fast fetch field fields first flush for force foreign found_rows full fulltext function general generated get global grant grants group group_concat handler hard hash help high_priority hosts hour_microsecond hour_minute hour_second if ignore ignore_server_ids import index index_statistics infile inner innodb inout insensitive insert_method install interval invoker isolation iterate key keys kill language last leading leave left level limit linear lines list load local localtime localtimestamp lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters match max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modifies modify mutex mysql_errno natural next no no_write_to_binlog offline offset one online open optimize option optionally out outer outfile pack_keys parser partition partitions password persistent phase plugin plugins prepare preserve prev primary privileges procedure processlist profile profiles purge query quick range read read_write reads real rebuild recover references regexp relaylog release remove rename reorganize repair repeatable replace require resignal restrict resume return returns revoke right rlike rollback rollup row row_format rtree savepoint schedule schema schema_name schemas second_microsecond security sensitive separator serializable server session share show shutdown signal slave slow smallint snapshot soft soname spatial specific sql sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result sqlexception sqlstate sqlwarning ssl start starting starts status std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace temporary terminated to trailing transaction trigger triggers truncate uncommitted undo uninstall unique unlock upgrade usage use use_frm user user_resources user_statistics using utc_date utc_time utc_timestamp value variables varying view views virtual warnings when while with work write xa xor year_month zerofill begin do then else loop repeat\"),\n  builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text bigint int int1 int2 int3 int4 int8 integer float float4 float8 double char varbinary varchar varcharacter precision date datetime year unsigned signed numeric\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=&|^]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber doubleQuote nCharCast charsetCast commentHash commentSpaceRequired\"),\n  hooks: {\n    \"@\":   hookVar,\n    \"`\":   hookIdentifier,\n    \"\\\\\":  hookClient\n  }\n});\n\n// provided by the phpLiteAdmin project - phpliteadmin.org\nconst sqlite = sql({\n  // commands of the official SQLite client, ref: https://www.sqlite.org/cli.html#dotcmd\n  client: set(\"auth backup bail binary changes check clone databases dbinfo dump echo eqp exit explain fullschema headers help import imposter indexes iotrace limit lint load log mode nullvalue once open output print prompt quit read restore save scanstats schema separator session shell show stats system tables testcase timeout timer trace vfsinfo vfslist vfsname width\"),\n  // ref: http://sqlite.org/lang_keywords.html\n  keywords: set(sqlKeywords + \"abort action add after all analyze attach autoincrement before begin cascade case cast check collate column commit conflict constraint cross current_date current_time current_timestamp database default deferrable deferred detach each else end escape except exclusive exists explain fail for foreign full glob if ignore immediate index indexed initially inner instead intersect isnull key left limit match natural no notnull null of offset outer plan pragma primary query raise recursive references regexp reindex release rename replace restrict right rollback row savepoint temp temporary then to transaction trigger unique using vacuum view virtual when with without\"),\n  // SQLite is weakly typed, ref: http://sqlite.org/datatype3.html. This is just a list of some common types.\n  builtin: set(\"bool boolean bit blob decimal double float long longblob longtext medium mediumblob mediumint mediumtext time timestamp tinyblob tinyint tinytext text clob bigint int int2 int8 integer float double char varchar date datetime year unsigned signed numeric real\"),\n  // ref: http://sqlite.org/syntax/literal-value.html\n  atoms: set(\"null current_date current_time current_timestamp\"),\n  // ref: http://sqlite.org/lang_expr.html#binaryops\n  operatorChars: /^[*+\\-%<>!=&|/~]/,\n  // SQLite is weakly typed, ref: http://sqlite.org/datatype3.html. This is just a list of some common types.\n  dateSQL: set(\"date time timestamp datetime\"),\n  support: set(\"decimallessFloat zerolessFloat\"),\n  identifierQuote: \"\\\"\",  //ref: http://sqlite.org/lang_keywords.html\n  hooks: {\n    // bind-parameters ref:http://sqlite.org/lang_expr.html#varparam\n    \"@\":   hookVar,\n    \":\":   hookVar,\n    \"?\":   hookVar,\n    \"$\":   hookVar,\n    // The preferred way to escape Identifiers is using double quotes, ref: http://sqlite.org/lang_keywords.html\n    \"\\\"\":   hookIdentifierDoublequote,\n    // there is also support for backticks, ref: http://sqlite.org/lang_keywords.html\n    \"`\":   hookIdentifier\n  }\n});\n\n// the query language used by Apache Cassandra is called CQL, but this mime type\n// is called Cassandra to avoid confusion with Contextual Query Language\nconst cassandra = sql({\n  client: { },\n  keywords: set(\"add all allow alter and any apply as asc authorize batch begin by clustering columnfamily compact consistency count create custom delete desc distinct drop each_quorum exists filtering from grant if in index insert into key keyspace keyspaces level limit local_one local_quorum modify nan norecursive nosuperuser not of on one order password permission permissions primary quorum rename revoke schema select set storage superuser table three to token truncate ttl two type unlogged update use user users using values where with writetime\"),\n  builtin: set(\"ascii bigint blob boolean counter decimal double float frozen inet int list map static text timestamp timeuuid tuple uuid varchar varint\"),\n  atoms: set(\"false true infinity NaN\"),\n  operatorChars: /^[<>=]/,\n  dateSQL: { },\n  support: set(\"commentSlashSlash decimallessFloat\"),\n  hooks: { }\n});\n\n// this is based on Peter Raganitsch's 'plsql' mode\nconst plSQL = sql({\n  client:     set(\"appinfo arraysize autocommit autoprint autorecovery autotrace blockterminator break btitle cmdsep colsep compatibility compute concat copycommit copytypecheck define describe echo editfile embedded escape exec execute feedback flagger flush heading headsep instance linesize lno loboffset logsource long longchunksize markup native newpage numformat numwidth pagesize pause pno recsep recsepchar release repfooter repheader serveroutput shiftinout show showmode size spool sqlblanklines sqlcase sqlcode sqlcontinue sqlnumber sqlpluscompatibility sqlprefix sqlprompt sqlterminator suffix tab term termout time timing trimout trimspool ttitle underline verify version wrap\"),\n  keywords:   set(\"abort accept access add all alter and any array arraylen as asc assert assign at attributes audit authorization avg base_table begin between binary_integer body boolean by case cast char char_base check close cluster clusters colauth column comment commit compress connect connected constant constraint crash create current currval cursor data_base database date dba deallocate debugoff debugon decimal declare default definition delay delete desc digits dispose distinct do drop else elseif elsif enable end entry escape exception exception_init exchange exclusive exists exit external fast fetch file for force form from function generic goto grant group having identified if immediate in increment index indexes indicator initial initrans insert interface intersect into is key level library like limited local lock log logging long loop master maxextents maxtrans member minextents minus mislabel mode modify multiset new next no noaudit nocompress nologging noparallel not nowait number_base object of off offline on online only open option or order out package parallel partition pctfree pctincrease pctused pls_integer positive positiven pragma primary prior private privileges procedure public raise range raw read rebuild record ref references refresh release rename replace resource restrict return returning returns reverse revoke rollback row rowid rowlabel rownum rows run savepoint schema segment select separate session set share snapshot some space split sql start statement storage subtype successful synonym tabauth table tables tablespace task terminate then to trigger truncate type union unique unlimited unrecoverable unusable update use using validate value values variable view views when whenever where while with work\"),\n  builtin:    set(\"abs acos add_months ascii asin atan atan2 average bfile bfilename bigserial bit blob ceil character chartorowid chr clob concat convert cos cosh count dec decode deref dual dump dup_val_on_index empty error exp false float floor found glb greatest hextoraw initcap instr instrb int integer isopen last_day least length lengthb ln lower lpad ltrim lub make_ref max min mlslabel mod months_between natural naturaln nchar nclob new_time next_day nextval nls_charset_decl_len nls_charset_id nls_charset_name nls_initcap nls_lower nls_sort nls_upper nlssort no_data_found notfound null number numeric nvarchar2 nvl others power rawtohex real reftohex round rowcount rowidtochar rowtype rpad rtrim serial sign signtype sin sinh smallint soundex sqlcode sqlerrm sqrt stddev string substr substrb sum sysdate tan tanh to_char text to_date to_label to_multi_byte to_number to_single_byte translate true trunc uid unlogged upper user userenv varchar varchar2 variance varying vsize xml\"),\n  operatorChars: /^[*\\/+\\-%<>!=~]/,\n  dateSQL:    set(\"date time timestamp\"),\n  support:    set(\"doubleQuote nCharCast zerolessFloat binaryNumber hexNumber\")\n});\n\n// Created to support specific hive keywords\nconst hive = sql({\n  keywords: set(\"select alter $elem$ $key$ $value$ add after all analyze and archive as asc before between binary both bucket buckets by cascade case cast change cluster clustered clusterstatus collection column columns comment compute concatenate continue create cross cursor data database databases dbproperties deferred delete delimited desc describe directory disable distinct distribute drop else enable end escaped exclusive exists explain export extended external fetch fields fileformat first format formatted from full function functions grant group having hold_ddltime idxproperties if import in index indexes inpath inputdriver inputformat insert intersect into is items join keys lateral left like limit lines load local location lock locks mapjoin materialized minus msck no_drop nocompress not of offline on option or order out outer outputdriver outputformat overwrite partition partitioned partitions percent plus preserve procedure purge range rcfile read readonly reads rebuild recordreader recordwriter recover reduce regexp rename repair replace restrict revoke right rlike row schema schemas semi sequencefile serde serdeproperties set shared show show_database sort sorted ssl statistics stored streamtable table tables tablesample tblproperties temporary terminated textfile then tmp to touch transform trigger unarchive undo union uniquejoin unlock update use using utc utc_tmestamp view when where while with admin authorization char compact compactions conf cube current current_date current_timestamp day decimal defined dependency directories elem_type exchange file following for grouping hour ignore inner interval jar less logical macro minute month more none noscan over owner partialscan preceding pretty principals protection reload rewrite role roles rollup rows second server sets skewed transactions truncate unbounded unset uri user values window year\"),\n  builtin: set(\"bool boolean long timestamp tinyint smallint bigint int float double date datetime unsigned string array struct map uniontype key_type utctimestamp value_type varchar\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=]/,\n  dateSQL: set(\"date timestamp\"),\n  support: set(\"ODBCdotTable doubleQuote binaryNumber hexNumber\")\n});\n\nconst pgSQL = sql({\n  client: set(\"source\"),\n  // For PostgreSQL - https://www.postgresql.org/docs/11/sql-keywords-appendix.html\n  // For pl/pgsql lang - https://github.com/postgres/postgres/blob/REL_11_2/src/pl/plpgsql/src/pl_scanner.c\n  keywords: set(sqlKeywords + \"a abort abs absent absolute access according action ada add admin after aggregate alias all allocate also alter always analyse analyze and any are array array_agg array_max_cardinality as asc asensitive assert assertion assignment asymmetric at atomic attach attribute attributes authorization avg backward base64 before begin begin_frame begin_partition bernoulli between bigint binary bit bit_length blob blocked bom boolean both breadth by c cache call called cardinality cascade cascaded case cast catalog catalog_name ceil ceiling chain char char_length character character_length character_set_catalog character_set_name character_set_schema characteristics characters check checkpoint class class_origin clob close cluster coalesce cobol collate collation collation_catalog collation_name collation_schema collect column column_name columns command_function command_function_code comment comments commit committed concurrently condition condition_number configuration conflict connect connection connection_name constant constraint constraint_catalog constraint_name constraint_schema constraints constructor contains content continue control conversion convert copy corr corresponding cost count covar_pop covar_samp create cross csv cube cume_dist current current_catalog current_date current_default_transform_group current_path current_role current_row current_schema current_time current_timestamp current_transform_group_for_type current_user cursor cursor_name cycle data database datalink datatype date datetime_interval_code datetime_interval_precision day db deallocate debug dec decimal declare default defaults deferrable deferred defined definer degree delete delimiter delimiters dense_rank depends depth deref derived desc describe descriptor detach detail deterministic diagnostics dictionary disable discard disconnect dispatch distinct dlnewcopy dlpreviouscopy dlurlcomplete dlurlcompleteonly dlurlcompletewrite dlurlpath dlurlpathonly dlurlpathwrite dlurlscheme dlurlserver dlvalue do document domain double drop dump dynamic dynamic_function dynamic_function_code each element else elseif elsif empty enable encoding encrypted end end_frame end_partition endexec enforced enum equals errcode error escape event every except exception exclude excluding exclusive exec execute exists exit exp explain expression extension external extract false family fetch file filter final first first_value flag float floor following for force foreach foreign fortran forward found frame_row free freeze from fs full function functions fusion g general generated get global go goto grant granted greatest group grouping groups handler having header hex hierarchy hint hold hour id identity if ignore ilike immediate immediately immutable implementation implicit import in include including increment indent index indexes indicator info inherit inherits initially inline inner inout input insensitive insert instance instantiable instead int integer integrity intersect intersection interval into invoker is isnull isolation join k key key_member key_type label lag language large last last_value lateral lead leading leakproof least left length level library like like_regex limit link listen ln load local localtime localtimestamp location locator lock locked log logged loop lower m map mapping match matched materialized max max_cardinality maxvalue member merge message message_length message_octet_length message_text method min minute minvalue mod mode modifies module month more move multiset mumps name names namespace national natural nchar nclob nesting new next nfc nfd nfkc nfkd nil no none normalize normalized not nothing notice notify notnull nowait nth_value ntile null nullable nullif nulls number numeric object occurrences_regex octet_length octets of off offset oids old on only open operator option options or order ordering ordinality others out outer output over overlaps overlay overriding owned owner p pad parallel parameter parameter_mode parameter_name parameter_ordinal_position parameter_specific_catalog parameter_specific_name parameter_specific_schema parser partial partition pascal passing passthrough password path percent percent_rank percentile_cont percentile_disc perform period permission pg_context pg_datatype_name pg_exception_context pg_exception_detail pg_exception_hint placing plans pli policy portion position position_regex power precedes preceding precision prepare prepared preserve primary print_strict_params prior privileges procedural procedure procedures program public publication query quote raise range rank read reads real reassign recheck recovery recursive ref references referencing refresh regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy regr_syy reindex relative release rename repeatable replace replica requiring reset respect restart restore restrict result result_oid return returned_cardinality returned_length returned_octet_length returned_sqlstate returning returns reverse revoke right role rollback rollup routine routine_catalog routine_name routine_schema routines row row_count row_number rows rowtype rule savepoint scale schema schema_name schemas scope scope_catalog scope_name scope_schema scroll search second section security select selective self sensitive sequence sequences serializable server server_name session session_user set setof sets share show similar simple size skip slice smallint snapshot some source space specific specific_name specifictype sql sqlcode sqlerror sqlexception sqlstate sqlwarning sqrt stable stacked standalone start state statement static statistics stddev_pop stddev_samp stdin stdout storage strict strip structure style subclass_origin submultiset subscription substring substring_regex succeeds sum symmetric sysid system system_time system_user t table table_name tables tablesample tablespace temp template temporary text then ties time timestamp timezone_hour timezone_minute to token top_level_count trailing transaction transaction_active transactions_committed transactions_rolled_back transform transforms translate translate_regex translation treat trigger trigger_catalog trigger_name trigger_schema trim trim_array true truncate trusted type types uescape unbounded uncommitted under unencrypted union unique unknown unlink unlisten unlogged unnamed unnest until untyped update upper uri usage use_column use_variable user user_defined_type_catalog user_defined_type_code user_defined_type_name user_defined_type_schema using vacuum valid validate validator value value_of values var_pop var_samp varbinary varchar variable_conflict variadic varying verbose version versioning view views volatile warning when whenever where while whitespace width_bucket window with within without work wrapper write xml xmlagg xmlattributes xmlbinary xmlcast xmlcomment xmlconcat xmldeclaration xmldocument xmlelement xmlexists xmlforest xmliterate xmlnamespaces xmlparse xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltext xmlvalidate year yes zone\"),\n  // https://www.postgresql.org/docs/11/datatype.html\n  builtin: set(\"bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time without zone with timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*\\/+\\-%<>!=&|^\\/#@?~]/,\n  backslashStringEscapes: false,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast escapeConstant\")\n});\n\n// Google's SQL-like query language, GQL\nconst gql = sql({\n  keywords: set(\"ancestor and asc by contains desc descendant distinct from group has in is limit offset on order select superset where\"),\n  atoms: set(\"false true\"),\n  builtin: set(\"blob datetime first key __key__ string integer double boolean null\"),\n  operatorChars: /^[*+\\-%<>!=]/\n});\n\n// Greenplum\nconst gpSQL = sql({\n  client: set(\"source\"),\n  //https://github.com/greenplum-db/gpdb/blob/master/src/include/parser/kwlist.h\n  keywords: set(\"abort absolute access action active add admin after aggregate all also alter always analyse analyze and any array as asc assertion assignment asymmetric at authorization backward before begin between bigint binary bit boolean both by cache called cascade cascaded case cast chain char character characteristics check checkpoint class close cluster coalesce codegen collate column comment commit committed concurrency concurrently configuration connection constraint constraints contains content continue conversion copy cost cpu_rate_limit create createdb createexttable createrole createuser cross csv cube current current_catalog current_date current_role current_schema current_time current_timestamp current_user cursor cycle data database day deallocate dec decimal declare decode default defaults deferrable deferred definer delete delimiter delimiters deny desc dictionary disable discard distinct distributed do document domain double drop dxl each else enable encoding encrypted end enum errors escape every except exchange exclude excluding exclusive execute exists explain extension external extract false family fetch fields filespace fill filter first float following for force foreign format forward freeze from full function global grant granted greatest group group_id grouping handler hash having header hold host hour identity if ignore ilike immediate immutable implicit in including inclusive increment index indexes inherit inherits initially inline inner inout input insensitive insert instead int integer intersect interval into invoker is isnull isolation join key language large last leading least left level like limit list listen load local localtime localtimestamp location lock log login mapping master match maxvalue median merge minute minvalue missing mode modifies modify month move name names national natural nchar new newline next no nocreatedb nocreateexttable nocreaterole nocreateuser noinherit nologin none noovercommit nosuperuser not nothing notify notnull nowait null nullif nulls numeric object of off offset oids old on only operator option options or order ordered others out outer over overcommit overlaps overlay owned owner parser partial partition partitions passing password percent percentile_cont percentile_disc placing plans position preceding precision prepare prepared preserve primary prior privileges procedural procedure protocol queue quote randomly range read readable reads real reassign recheck recursive ref references reindex reject relative release rename repeatable replace replica reset resource restart restrict returning returns revoke right role rollback rollup rootpartition row rows rule savepoint scatter schema scroll search second security segment select sequence serializable session session_user set setof sets share show similar simple smallint some split sql stable standalone start statement statistics stdin stdout storage strict strip subpartition subpartitions substring superuser symmetric sysid system table tablespace temp template temporary text then threshold ties time timestamp to trailing transaction treat trigger trim true truncate trusted type unbounded uncommitted unencrypted union unique unknown unlisten until update user using vacuum valid validation validator value values varchar variadic varying verbose version view volatile web when where whitespace window with within without work writable write xml xmlattributes xmlconcat xmlelement xmlexists xmlforest xmlparse xmlpi xmlroot xmlserialize year yes zone\"),\n  builtin: set(\"bigint int8 bigserial serial8 bit varying varbit boolean bool box bytea character char varchar cidr circle date double precision float float8 inet integer int int4 interval json jsonb line lseg macaddr macaddr8 money numeric decimal path pg_lsn point polygon real float4 smallint int2 smallserial serial2 serial serial4 text time without zone with timetz timestamp timestamptz tsquery tsvector txid_snapshot uuid xml\"),\n  atoms: set(\"false true null unknown\"),\n  operatorChars: /^[*+\\-%<>!=&|^\\/#@?~]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable decimallessFloat zerolessFloat binaryNumber hexNumber nCharCast charsetCast\")\n});\n\n// Spark SQL\nconst sparkSQL = sql({\n  keywords: set(\"add after all alter analyze and anti archive array as asc at between bucket buckets by cache cascade case cast change clear cluster clustered codegen collection column columns comment commit compact compactions compute concatenate cost create cross cube current current_date current_timestamp database databases data dbproperties defined delete delimited deny desc describe dfs directories distinct distribute drop else end escaped except exchange exists explain export extended external false fields fileformat first following for format formatted from full function functions global grant group grouping having if ignore import in index indexes inner inpath inputformat insert intersect interval into is items join keys last lateral lazy left like limit lines list load local location lock locks logical macro map minus msck natural no not null nulls of on optimize option options or order out outer outputformat over overwrite partition partitioned partitions percent preceding principals purge range recordreader recordwriter recover reduce refresh regexp rename repair replace reset restrict revoke right rlike role roles rollback rollup row rows schema schemas select semi separated serde serdeproperties set sets show skewed sort sorted start statistics stored stratify struct table tables tablesample tblproperties temp temporary terminated then to touch transaction transactions transform true truncate unarchive unbounded uncache union unlock unset use using values view when where window with\"),\n  builtin: set(\"tinyint smallint int bigint boolean float double string binary timestamp decimal array map struct uniontype delimited serde sequencefile textfile rcfile inputformat outputformat\"),\n  atoms: set(\"false true null\"),\n  operatorChars: /^[*\\/+\\-%<>!=~&|^]/,\n  dateSQL: set(\"date time timestamp\"),\n  support: set(\"ODBCdotTable doubleQuote zerolessFloat\")\n});\n\n// Esper\nconst esper = sql({\n  client: set(\"source\"),\n  // http://www.espertech.com/esper/release-5.5.0/esper-reference/html/appendix_keywords.html\n  keywords: set(\"alter and as asc between by count create delete desc distinct drop from group having in insert into is join like not on or order select set table union update values where limit after all and as at asc avedev avg between by case cast coalesce count create current_timestamp day days delete define desc distinct else end escape events every exists false first from full group having hour hours in inner insert instanceof into irstream is istream join last lastweekday left limit like max match_recognize matches median measures metadatasql min minute minutes msec millisecond milliseconds not null offset on or order outer output partition pattern prev prior regexp retain-union retain-intersection right rstream sec second seconds select set some snapshot sql stddev sum then true unidirectional until update variable weekday when where window\"),\n  builtin: {},\n  atoms: set(\"false true null\"),\n  operatorChars: /^[*+\\-%<>!=&|^\\/#@?~]/,\n  dateSQL: set(\"time\"),\n  support: set(\"decimallessFloat zerolessFloat binaryNumber hexNumber\")\n});\n\n/*\n  How options are used by SQL Mode\n  =================================================\n\n  keywords:\n    A list of keywords you want to be highlighted.\n  builtin:\n    A list of builtin types you want to be highlighted (if you want types to be of class \"builtin\" instead of \"keyword\").\n  operatorChars:\n    All characters that must be handled as operators.\n  client:\n    Commands parsed and executed by the client (not the server).\n  support:\n    A list of supported syntaxes which are not common, but are supported by more than 1 DBMS.\n    * ODBCdotTable: .tableName\n    * zerolessFloat: .1\n    * doubleQuote\n    * nCharCast: N'string'\n    * charsetCast: _utf8'string'\n    * commentHash: use # char for comments\n    * commentSlashSlash: use // for comments\n    * commentSpaceRequired: require a space after -- for comments\n  atoms:\n    Keywords that must be highlighted as atoms,. Some DBMS's support more atoms than others:\n    UNKNOWN, INFINITY, UNDERFLOW, NaN...\n  dateSQL:\n    Used for date/time SQL standard syntax, because not all DBMS's support same temporal types.\n*/\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/sql.js\n"));

/***/ })

}]);