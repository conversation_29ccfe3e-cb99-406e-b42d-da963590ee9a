import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Activity, CheckCircle, CheckCircle2, AlertTriangle, Clock, GitCommit, GitPullRequest, GitMerge, ListChecks, User } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface StatusDisplayProps {
  isActive: boolean;
  errorTriggered: boolean;
}

export function StatusDisplay({ isActive, errorTriggered }: StatusDisplayProps) {
  const [status, setStatus] = useState<'operational' | 'error' | 'fixing' | 'fixed'>('operational');
  const [progress, setProgress] = useState(0);
  const [deploymentSteps, setDeploymentSteps] = useState<{
    identify: boolean;
    develop: boolean;
    test: boolean;
    deploy: boolean;
  }>({
    identify: false,
    develop: false,
    test: false,
    deploy: false,
  });

  useEffect(() => {
    if (!errorTriggered) {
      setStatus('operational');
      setProgress(0);
      setDeploymentSteps({
        identify: false,
        develop: false,
        test: false,
        deploy: false,
      });
      return;
    }

    // Simulate the error detection and fixing process
    const statusSequence = async () => {
      // Error detected
      setStatus('error');
      setProgress(0);

      // Wait 3 seconds before starting fix
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Identify issue
      setDeploymentSteps(prev => ({ ...prev, identify: true }));
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Developer working on fix
      setStatus('fixing');
      setDeploymentSteps(prev => ({ ...prev, develop: true }));

      // Progress animation
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            return 100;
          }
          return prev + 2;
        });
      }, 500);

      // Testing phase
      await new Promise(resolve => setTimeout(resolve, 10000));
      setDeploymentSteps(prev => ({ ...prev, test: true }));

      // Deployment phase
      await new Promise(resolve => setTimeout(resolve, 8000));
      setDeploymentSteps(prev => ({ ...prev, deploy: true }));

      // Fix completed after ~25 seconds
      await new Promise(resolve => setTimeout(resolve, 4000));
      clearInterval(interval);
      setProgress(100);
      setStatus('fixed');
    };

    if (errorTriggered) {
      statusSequence();
    }
  }, [errorTriggered]);

  return (
    <Card className="h-[600px] bg-[#1a1a1a] border-[#222222]">
      <CardHeader className="pb-2 border-b border-[#222222]">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={cn(
              "h-8 w-8 rounded-md flex items-center justify-center mr-2",
              status === 'operational' && "bg-muted/20",
              status === 'error' && "bg-amber-500/20",
              status === 'fixing' && "bg-blue-500/20",
              status === 'fixed' && "bg-green-500/20",
            )}>
              {status === 'operational' && <Clock className="h-4 w-4 text-muted-foreground" />}
              {status === 'error' && <AlertTriangle className="h-4 w-4 text-amber-500" />}
              {status === 'fixing' && <GitCommit className="h-4 w-4 text-blue-500" />}
              {status === 'fixed' && <CheckCircle className="h-4 w-4 text-green-500" />}
            </div>
            <CardTitle className="text-lg">System Status</CardTitle>
          </div>
          <Badge
            variant="outline"
            className={cn(
              status === 'operational' && "bg-muted/10 text-muted-foreground border-muted/20",
              status === 'error' && "bg-amber-500/10 text-amber-500 border-amber-500/20",
              status === 'fixing' && "bg-blue-500/10 text-blue-500 border-blue-500/20",
              status === 'fixed' && "bg-green-500/10 text-green-500 border-green-500/20",
            )}
          >
            {status === 'operational' && 'Standby'}
            {status === 'error' && 'Investigating'}
            {status === 'fixing' && 'In Progress'}
            {status === 'fixed' && 'Completed'}
          </Badge>
        </div>
        <CardDescription>
          {status === 'operational' && 'Agents ready to respond to notifications'}
          {status === 'error' && 'Agents investigating reported issue'}
          {status === 'fixing' && 'Developer agent implementing solution'}
          {status === 'fixed' && 'Issue successfully resolved by agents'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-start gap-3 p-4 rounded-md border border-[#333333] bg-[#222222]">
              <div className={cn(
                "h-8 w-8 rounded-md flex items-center justify-center flex-shrink-0",
                status === 'operational' && "bg-muted/20",
                status === 'error' && "bg-blue-500/20",
                status === 'fixing' && "bg-blue-500/20",
                status === 'fixed' && "bg-green-500/20",
              )}>
                <span className="text-blue-500 font-medium">K</span>
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div className="font-medium">Kenard (CEO Agent)</div>
                  <Badge variant="outline" className={cn(
                    "font-normal text-xs",
                    status === 'operational' && "bg-muted/10 text-muted-foreground border-muted/20",
                    status === 'error' && "bg-blue-500/10 text-blue-500 border-blue-500/20",
                    status === 'fixing' && "bg-blue-500/10 text-blue-500 border-blue-500/20",
                    status === 'fixed' && "bg-green-500/10 text-green-500 border-green-500/20",
                  )}>
                    {status === 'operational' && 'Idle'}
                    {status === 'error' && 'Notified'}
                    {status === 'fixing' && 'Coordinating'}
                    {status === 'fixed' && 'Completed'}
                  </Badge>
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {status === 'operational' && 'Waiting for notifications'}
                  {status === 'error' && 'Received alert from Vercel and delegated to Developer'}
                  {status === 'fixing' && 'Monitoring Developer agent progress'}
                  {status === 'fixed' && 'Confirmed issue resolution and documented response'}
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3 p-4 rounded-md border border-[#333333] bg-[#222222]">
              <div className={cn(
                "h-8 w-8 rounded-md flex items-center justify-center flex-shrink-0",
                status === 'operational' && "bg-muted/20",
                status === 'error' && "bg-amber-500/20",
                status === 'fixing' && "bg-blue-500/20",
                status === 'fixed' && "bg-green-500/20",
              )}>
                <span className="text-green-500 font-medium">A</span>
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div className="font-medium">Alex (Developer Agent)</div>
                  <Badge variant="outline" className={cn(
                    "font-normal text-xs",
                    status === 'operational' && "bg-muted/10 text-muted-foreground border-muted/20",
                    status === 'error' && "bg-amber-500/10 text-amber-500 border-amber-500/20",
                    status === 'fixing' && "bg-blue-500/10 text-blue-500 border-blue-500/20",
                    status === 'fixed' && "bg-green-500/10 text-green-500 border-green-500/20",
                  )}>
                    {status === 'operational' && 'Idle'}
                    {status === 'error' && 'Investigating'}
                    {status === 'fixing' && 'Implementing Fix'}
                    {status === 'fixed' && 'Completed'}
                  </Badge>
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  {status === 'operational' && 'Waiting for tasks'}
                  {status === 'error' && 'Analyzing error logs and code to identify issue'}
                  {status === 'fixing' && 'Implementing error handling and testing solution'}
                  {status === 'fixed' && 'Deployed fix and reported back to CEO agent'}
                </div>
              </div>
            </div>
          </div>

          {(status === 'fixing' || status === 'fixed') && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-md flex items-center justify-center mr-2 bg-blue-500/20">
                    <Activity className="h-4 w-4 text-blue-500" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className="text-sm font-medium">Fix Progress</h3>
                      <span className="text-xs">{progress}%</span>
                    </div>
                    <Progress value={progress} className="h-2 mt-2" />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-md flex items-center justify-center mr-2 bg-blue-500/20">
                    <Clock className="h-4 w-4 text-blue-500" />
                  </div>
                  <h3 className="text-sm font-medium">Activity Timeline</h3>
                </div>

                <div className="ml-4 border-l border-[#333333] pl-6 space-y-6">
                  <div className="relative">
                    <div className="absolute -left-[29px] top-0 h-6 w-6 rounded-md bg-amber-500/20 flex items-center justify-center">
                      <div className="h-2 w-2 rounded-md bg-amber-500"></div>
                    </div>
                    <div className="pb-6">
                      <div className="flex items-center justify-between">
                        <div className="text-sm font-medium">Error Detected</div>
                        <div className="text-xs text-muted-foreground">2:14 AM</div>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">Vercel alert received about site crash</div>
                    </div>
                  </div>

                  <div className="relative">
                    <div className="absolute -left-[29px] top-0 h-6 w-6 rounded-md bg-blue-500/20 flex items-center justify-center">
                      <div className="h-2 w-2 rounded-md bg-blue-500"></div>
                    </div>
                    <div className="pb-6">
                      <div className="flex items-center justify-between">
                        <div className="text-sm font-medium">CEO Agent Notified</div>
                        <div className="text-xs text-muted-foreground">2:14 AM</div>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">Assigned investigation to Developer Agent</div>
                    </div>
                  </div>

                  <div className="relative">
                    <div className="absolute -left-[29px] top-0 h-6 w-6 rounded-full bg-blue-500/20 flex items-center justify-center">
                      <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                    </div>
                    <div className="pb-6">
                      <div className="flex items-center justify-between">
                        <div className="text-sm font-medium">Developer Investigation</div>
                        <div className="text-xs text-muted-foreground">2:15 AM</div>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">Identified missing null check in UserDashboard.tsx</div>
                    </div>
                  </div>

                  {(status === 'fixing' || status === 'fixed') && (
                    <div className="relative">
                      <div className="absolute -left-[29px] top-0 h-6 w-6 rounded-full bg-blue-500/20 flex items-center justify-center">
                        <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                      </div>
                      <div className="pb-6">
                        <div className="flex items-center justify-between">
                          <div className="text-sm font-medium">Fix Implementation</div>
                          <div className="text-xs text-muted-foreground">2:16 AM</div>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">Added proper error handling and null checks</div>
                      </div>
                    </div>
                  )}

                  {status === 'fixed' && (
                    <div className="relative">
                      <div className="absolute -left-[29px] top-0 h-6 w-6 rounded-md bg-green-500/20 flex items-center justify-center">
                        <div className="h-2 w-2 rounded-md bg-green-500"></div>
                      </div>
                      <div>
                        <div className="flex items-center justify-between">
                          <div className="text-sm font-medium">Issue Resolved</div>
                          <div className="text-xs text-muted-foreground">2:18 AM</div>
                        </div>
                        <div className="text-xs text-muted-foreground mt-1">Fix deployed to production and verified</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-md flex items-center justify-center mr-2 bg-blue-500/20">
                    <ListChecks className="h-4 w-4 text-blue-500" />
                  </div>
                  <h3 className="text-sm font-medium">Agent Tasks</h3>
                </div>

                <div className="grid grid-cols-1 gap-3">
                  <div className={`flex items-start gap-3 p-4 rounded-md border border-[#333333] ${deploymentSteps.identify ? 'bg-[#222222]' : 'bg-[#1e1e1e]'}`}>
                    <div className={`h-8 w-8 rounded-full flex items-center justify-center flex-shrink-0 ${deploymentSteps.identify ? 'bg-amber-500/20' : 'bg-muted/20'}`}>
                      <AlertTriangle className={`h-4 w-4 ${deploymentSteps.identify ? 'text-amber-500' : 'text-muted-foreground'}`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-sm">Receive & Analyze Alert</span>
                        {deploymentSteps.identify ? (
                          <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20 text-xs font-normal">
                            Completed
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-muted/10 text-muted-foreground border-muted/20 text-xs font-normal">
                            Pending
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {deploymentSteps.identify
                          ? 'CEO agent analyzed Vercel alert and delegated to Developer'
                          : 'CEO agent reviewing incoming alert email'}
                      </p>
                      <div className="flex items-center mt-2 text-xs">
                        <div className="flex items-center text-blue-500 mr-3">
                          <User className="h-3 w-3 mr-1" />
                          <span>CEO Agent</span>
                        </div>
                        <div className="flex items-center text-muted-foreground">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>2:14 AM</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className={`flex items-start gap-3 p-4 rounded-md border border-[#333333] ${deploymentSteps.develop ? 'bg-[#222222]' : 'bg-[#1e1e1e]'}`}>
                    <div className={`h-8 w-8 rounded-full flex items-center justify-center flex-shrink-0 ${deploymentSteps.develop ? 'bg-blue-500/20' : 'bg-muted/20'}`}>
                      <GitCommit className={`h-4 w-4 ${deploymentSteps.develop ? 'text-blue-500' : 'text-muted-foreground'}`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-sm">Develop Solution</span>
                        {deploymentSteps.develop ? (
                          <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20 text-xs font-normal">
                            Completed
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-muted/10 text-muted-foreground border-muted/20 text-xs font-normal">
                            Pending
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {deploymentSteps.develop
                          ? 'Developer agent created fix with proper error handling'
                          : 'Developer agent investigating and coding solution'}
                      </p>
                      <div className="flex items-center mt-2 text-xs">
                        <div className="flex items-center text-green-500 mr-3">
                          <User className="h-3 w-3 mr-1" />
                          <span>Developer Agent</span>
                        </div>
                        <div className="flex items-center text-muted-foreground">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>2:15 AM</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className={`flex items-start gap-3 p-4 rounded-md border border-[#333333] ${deploymentSteps.test ? 'bg-[#222222]' : 'bg-[#1e1e1e]'}`}>
                    <div className={`h-8 w-8 rounded-full flex items-center justify-center flex-shrink-0 ${deploymentSteps.test ? 'bg-purple-500/20' : 'bg-muted/20'}`}>
                      <GitPullRequest className={`h-4 w-4 ${deploymentSteps.test ? 'text-purple-500' : 'text-muted-foreground'}`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-sm">Test & Review</span>
                        {deploymentSteps.test ? (
                          <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20 text-xs font-normal">
                            Completed
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-muted/10 text-muted-foreground border-muted/20 text-xs font-normal">
                            Pending
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {deploymentSteps.test
                          ? 'Developer agent verified all 24 tests passing'
                          : 'Developer agent running tests on the solution'}
                      </p>
                      <div className="flex items-center mt-2 text-xs">
                        <div className="flex items-center text-green-500 mr-3">
                          <User className="h-3 w-3 mr-1" />
                          <span>Developer Agent</span>
                        </div>
                        <div className="flex items-center text-muted-foreground">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>2:16 AM</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className={`flex items-start gap-3 p-4 rounded-md border border-[#333333] ${deploymentSteps.deploy ? 'bg-[#222222]' : 'bg-[#1e1e1e]'}`}>
                    <div className={`h-8 w-8 rounded-full flex items-center justify-center flex-shrink-0 ${deploymentSteps.deploy ? 'bg-green-500/20' : 'bg-muted/20'}`}>
                      <GitMerge className={`h-4 w-4 ${deploymentSteps.deploy ? 'text-green-500' : 'text-muted-foreground'}`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-sm">Deploy & Report</span>
                        {deploymentSteps.deploy ? (
                          <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20 text-xs font-normal">
                            Completed
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-muted/10 text-muted-foreground border-muted/20 text-xs font-normal">
                            Pending
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {deploymentSteps.deploy
                          ? 'Developer deployed fix and reported to CEO agent'
                          : 'Developer agent preparing to deploy the solution'}
                      </p>
                      <div className="flex items-center mt-2 text-xs">
                        <div className="flex items-center text-green-500 mr-3">
                          <User className="h-3 w-3 mr-1" />
                          <span>Developer Agent</span>
                        </div>
                        <div className="flex items-center text-muted-foreground">
                          <Clock className="h-3 w-3 mr-1" />
                          <span>2:17 AM</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {status === 'fixed' && (
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="h-8 w-8 rounded-md flex items-center justify-center mr-2 bg-green-500/20">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </div>
                <h3 className="text-sm font-medium">Resolution Summary</h3>
              </div>

              <div className="p-4 rounded-md border border-[#333333] bg-[#222222]">
                <div className="grid grid-cols-2 gap-4 text-xs">
                  <div>
                    <div className="text-muted-foreground mb-1">Alert received</div>
                    <div>2:14 AM (from Vercel)</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground mb-1">Issue resolved</div>
                    <div>2:37 AM</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground mb-1">Response time</div>
                    <div>23 minutes</div>
                  </div>
                  <div>
                    <div className="text-muted-foreground mb-1">Agents involved</div>
                    <div>CEO, Developer</div>
                  </div>
                </div>

                <div className="border-t border-[#333333] mt-4 pt-4">
                  <div className="text-muted-foreground mb-1 text-xs">Root cause</div>
                  <div className="text-sm">Missing error handling in UserDashboard component</div>
                </div>

                <div className="border-t border-[#333333] mt-4 pt-4">
                  <div className="text-muted-foreground mb-1 text-xs">Solution</div>
                  <div className="text-sm">Added proper error handling and loading states</div>
                </div>

                <div className="border-t border-[#333333] mt-4 pt-4">
                  <div className="text-muted-foreground mb-1 text-xs">Preventive measures</div>
                  <div className="text-sm">Developer agent implemented stricter TypeScript typing</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
