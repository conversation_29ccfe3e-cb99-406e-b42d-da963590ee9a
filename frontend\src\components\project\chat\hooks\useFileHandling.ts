import { useState } from 'react';
import { getFileTypeFromExtension } from '@/components/file-renderers';
import { Message, SelectedFile } from '../types';
import { formatFileSize } from '../utils';

export function useFileHandling() {
  // File upload preview state
  const [pendingFiles, setPendingFiles] = useState<File[]>([]);
  const [filePreviewUrls, setFilePreviewUrls] = useState<{[key: string]: string}>({});
  const [processingFiles, setProcessingFiles] = useState(false);
  
  // File viewer state
  const [fileViewerOpen, setFileViewerOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<SelectedFile | null>(null);

  // Process selected files - show preview first
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!event.target.files || event.target.files.length === 0) return;

    const files = Array.from(event.target.files);

    // Filter files by size (50MB limit)
    const filteredFiles = files.filter(file => {
      if (file.size > 50 * 1024 * 1024) {
        // Show error message for files that are too large
        console.error(`File size exceeds 50MB limit: ${file.name}`);
        return false;
      }
      return true;
    });

    if (filteredFiles.length === 0) return;

    // Filter out files that are already pending (by name)
    const existingFileNames = pendingFiles.map(f => f.name);
    const newFiles = filteredFiles.filter(file => !existingFileNames.includes(file.name));

    if (newFiles.length === 0) {
      console.log('All selected files are already pending');
      return;
    }

    // Create preview URLs for new image files
    const newPreviewUrls: {[key: string]: string} = {};
    newFiles.forEach(file => {
      if (file.type.startsWith('image/')) {
        newPreviewUrls[file.name] = URL.createObjectURL(file);
      }
    });

    // Append new files to existing pending files
    setPendingFiles(prev => [...prev, ...newFiles]);
    setFilePreviewUrls(prev => ({ ...prev, ...newPreviewUrls }));

    // Clear the file input to allow selecting the same files again
    event.target.value = '';
  };

  // Process files after user confirms
  const processFiles = async (
    files: File[], 
    additionalMessage?: string,
    setCurrentMessages?: (messages: Message[] | ((prev: Message[]) => Message[])) => void,
    getCurrentThreadId?: () => string | null,
    sendFollowUpMessage?: (content: string) => Promise<void>,
    initiateConversation?: (content: string) => Promise<void>,
    setLoadingSuggestions?: (loading: boolean) => void,
    setSuggestedResponses?: (responses: string[]) => void
  ) => {
    // Prevent multiple calls
    if (processingFiles) {
      return;
    }

    // Start suggestion loading state when processing files
    if (setLoadingSuggestions && setSuggestedResponses) {
      setLoadingSuggestions(true);
      setSuggestedResponses([]);
    }
    setProcessingFiles(true);

    try {
      // Create messages array
      const messages: Message[] = [];

      // Add text message if there's additional text
      if (additionalMessage && additionalMessage.trim()) {
        const textMessage: Message = {
          id: `text-${Date.now()}`,
          sender: 'user',
          content: additionalMessage.trim(),
          timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          role: 'user',
          type: 'user'
        };
        messages.push(textMessage);
      }

      // Process each file and create individual file messages
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        try {
          const content = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target?.result as string || '');
            reader.onerror = () => reject(new Error(`Failed to read file: ${file.name}`));
            reader.readAsText(file);
          });

          // Create file message
          const fileMessage: Message = {
            id: `file-${Date.now()}-${i}`,
            sender: 'user',
            content: JSON.stringify({
              name: file.name,
              content: content,
              size: file.size,
              type: file.type
            }),
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            isFile: true,
            isMultiFile: false,
            fileCount: 1,
            fileName: file.name,
            fileSize: formatFileSize(file.size),
            fileType: getFileTypeFromExtension(file.name),
            role: 'user',
            type: 'user'
          };
          messages.push(fileMessage);
        } catch (error) {
          console.error(`Error processing file ${file.name}:`, error);
        }
      }

      // Add all messages to the current chat
      if (setCurrentMessages) {
        setCurrentMessages(prev => [...prev, ...messages]);
      }

      // Clear preview and send to agent
      clearFilePreview();

      // Create message with files and optional additional text for API
      const fileMessageText = `I've uploaded ${files.length} file${files.length > 1 ? 's' : ''}: ${files.map(f => f.name).join(', ')}`;
      const fullMessage = additionalMessage ? `${additionalMessage}\n\n${fileMessageText}` : fileMessageText;

      // Send to agent
      if (getCurrentThreadId && sendFollowUpMessage && initiateConversation) {
        const threadId = getCurrentThreadId();
        if (threadId) {
          await sendFollowUpMessage(fullMessage);
        } else {
          await initiateConversation(fullMessage);
        }
      }
    } catch (error) {
      console.error('Error processing files:', error);
    } finally {
      setProcessingFiles(false);
    }
  };

  // Clear file preview
  const clearFilePreview = () => {
    // Clean up preview URLs
    Object.values(filePreviewUrls).forEach(url => {
      URL.revokeObjectURL(url);
    });
    setPendingFiles([]);
    setFilePreviewUrls({});
  };

  // Remove individual file from preview
  const removeFileFromPreview = (index: number) => {
    const fileToRemove = pendingFiles[index];
    const newFiles = pendingFiles.filter((_, i) => i !== index);

    // Clean up preview URL if it exists
    if (filePreviewUrls[fileToRemove.name]) {
      URL.revokeObjectURL(filePreviewUrls[fileToRemove.name]);
      const newUrls = { ...filePreviewUrls };
      delete newUrls[fileToRemove.name];
      setFilePreviewUrls(newUrls);
    }

    setPendingFiles(newFiles);
  };

  const handleFileClick = (file: SelectedFile) => {
    setSelectedFile(file);
    setFileViewerOpen(true);
  };

  return {
    pendingFiles,
    filePreviewUrls,
    processingFiles,
    fileViewerOpen,
    setFileViewerOpen,
    selectedFile,
    setSelectedFile,
    handleFileChange,
    processFiles,
    clearFilePreview,
    removeFileFromPreview,
    handleFileClick
  };
}
