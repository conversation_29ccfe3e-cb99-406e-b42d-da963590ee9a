'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChatInputArea } from '@/components/ui/chat/ChatInputArea';
import { CallInterface } from '../demo/calling/CallInterface';

// API imports for live data
import { initiateAgent, sendMessage, streamAgentWithFormData, generateSmartSuggestions } from '@/lib/api';

// Import separated components
import { ChatSidebar } from './chat/ChatSidebar';
import { ChatHeader } from './chat/ChatHeader';
import { ChatMessages } from './chat/ChatMessages';
import { FilePreview } from './chat/FilePreview';
import { FileViewer } from './chat/FileViewer';
import { SuggestedResponses } from './chat/SuggestedResponses';

// Import types and hooks
import { Message, LiveProjectChatInterfaceProps } from './chat/types';
import { useChatLogic } from './chat/hooks/useChatLogic';
import { useFileHandling } from './chat/hooks/useFileHandling';
import { useCallHandling } from './chat/hooks/useCallHandling';
import { useSuggestions } from './chat/hooks/useSuggestions';
import { useCallContext } from './chat/contexts/CallContext';

export function LiveProjectChatInterface({ isActive, project }: LiveProjectChatInterfaceProps) {
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Use custom hooks for separated logic
  const chatLogic = useChatLogic(project);
  const fileHandling = useFileHandling();
  const callHandling = useCallHandling(
    chatLogic.activeChat,
    chatLogic.customGroups,
    chatLogic.setCurrentMessages,
    messagesContainerRef
  );
  const suggestions = useSuggestions(
    chatLogic.activeChat,
    chatLogic.customGroups,
    chatLogic.getCurrentMessages,
    chatLogic.getCurrentThreadId,
    chatLogic.loading
  );

  // Use global call context
  const globalCallContext = useCallContext();

  // Sync local call handling with global context
  useEffect(() => {
    if (callHandling.isCallActive && !globalCallContext.isCallActive) {
      // Local call started, sync to global
      globalCallContext.startCall(callHandling.callType, callHandling.callParticipants);
    } else if (!callHandling.isCallActive && globalCallContext.isCallActive) {
      // Local call ended, sync to global
      globalCallContext.endCall();
    }
  }, [callHandling.isCallActive, callHandling.callType, callHandling.callParticipants, globalCallContext]);

  // Scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatLogic.ceoMessages, chatLogic.developerMessages, chatLogic.groupMessages]);







  // Handle sending messages
  const handleSendMessage = async (e: React.FormEvent, messageContent?: string) => {
    e.preventDefault();
    const content = messageContent || inputValue;

    // Prevent multiple submissions while processing
    if (fileHandling.processingFiles || chatLogic.loading) return;

    // Check if we have files to send
    if (fileHandling.pendingFiles.length > 0) {
      // Process files first, with or without message
      await fileHandling.processFiles(
        fileHandling.pendingFiles,
        content.trim(),
        chatLogic.setCurrentMessages,
        chatLogic.getCurrentThreadId,
        chatLogic.sendFollowUpMessage,
        chatLogic.initiateConversation,
        suggestions.setLoadingSuggestions,
        suggestions.setSuggestedResponses
      );
      return;
    }

    if (!content.trim()) return;

    // Start suggestion loading state immediately when user sends message
    suggestions.setLoadingSuggestions(true);
    suggestions.setSuggestedResponses([]);

    const newMessage: Message = {
      id: `user-${Date.now()}`,
      sender: 'user',
      content: content,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      role: 'user',
      type: 'user'
    };

    chatLogic.setCurrentMessages(prev => [...prev, newMessage]);
    setInputValue('');

    // Send to API
    const threadId = chatLogic.getCurrentThreadId();
    if (threadId) {
      await chatLogic.sendFollowUpMessage(content);
    } else {
      await chatLogic.initiateConversation(content);
    }
  };

















  return (
    <div className="flex h-screen bg-background">
      {/* Full-screen calling UI */}
      {(callHandling.showFullScreenCall || (globalCallContext.isCallActive && !globalCallContext.isCallMinimized)) && (
        <div className="fixed inset-0 z-50 bg-background">
          <CallInterface
            callType={chatLogic.activeChat === 'group' || chatLogic.customGroups.some(g => g.id === chatLogic.activeChat) ? 'group' : 'one-on-one'}
            callName={chatLogic.activeChat === 'ceo' ? 'Kenard (CEO)' : chatLogic.activeChat === 'developer' ? 'Alex (Developer)' : 'Team Chat'}
            participants={callHandling.callParticipants}
            onEndCall={() => {
              callHandling.endCall();
              globalCallContext.endCall();
            }}
            onMinimize={() => {
              callHandling.minimizeCall();
              globalCallContext.minimizeCall();
            }}
          />
        </div>
      )}

      {/* Left sidebar for chats */}
      <ChatSidebar
        activeChat={chatLogic.activeChat}
        setActiveChat={chatLogic.setActiveChat}
        ceoMessages={chatLogic.ceoMessages}
        developerMessages={chatLogic.developerMessages}
        groupMessages={chatLogic.groupMessages}
        customGroups={chatLogic.customGroups}
        selectedEmployees={chatLogic.selectedEmployees}
        setSelectedEmployees={chatLogic.setSelectedEmployees}
        isEmployeePopoverOpen={chatLogic.isEmployeePopoverOpen}
        setIsEmployeePopoverOpen={chatLogic.setIsEmployeePopoverOpen}
        createGroupChat={chatLogic.createGroupChat}
        projectAgents={chatLogic.projectAgents}
        toggleAgentSelection={chatLogic.toggleAgentSelection}
        collaborationMode={chatLogic.collaborationMode}
        setCollaborationMode={chatLogic.setCollaborationMode}
      />



      {/* Main chat area */}
      <div className="flex-1 flex flex-col">
        {/* Chat header */}
        <ChatHeader
          activeChat={chatLogic.activeChat}
          customGroups={chatLogic.customGroups}
          startCall={callHandling.startCall}
        />

        {/* Chat messages */}
        <ChatMessages
          ref={messagesContainerRef}
          activeChat={chatLogic.activeChat}
          ceoMessages={chatLogic.ceoMessages}
          developerMessages={chatLogic.developerMessages}
          groupMessages={chatLogic.groupMessages}
          customGroups={chatLogic.customGroups}
          dynamicAgentMessages={chatLogic.dynamicAgentMessages}
          loading={chatLogic.loading}
          error={chatLogic.error}
          onFileClick={fileHandling.handleFileClick}
        />
        <div ref={messagesEndRef} />

        {/* Suggested responses */}
        <SuggestedResponses
          ceoMessages={chatLogic.ceoMessages}
          developerMessages={chatLogic.developerMessages}
          groupMessages={chatLogic.groupMessages}
          loading={chatLogic.loading}
          loadingSuggestions={suggestions.loadingSuggestions}
          suggestedResponses={suggestions.suggestedResponses}
          setInputValue={setInputValue}
        />

        {/* File Preview */}
        <FilePreview
          pendingFiles={fileHandling.pendingFiles}
          filePreviewUrls={fileHandling.filePreviewUrls}
          processingFiles={fileHandling.processingFiles}
          removeFileFromPreview={fileHandling.removeFileFromPreview}
        />

        {/* Message input */}
        <ChatInputArea
          value={inputValue}
          onValueChange={setInputValue}
          onSendMessage={(message) => {
            if (!chatLogic.loading) {
              handleSendMessage({ preventDefault: () => {} } as React.FormEvent, message);
            }
          }}
          placeholder={`Type a message to ${
            chatLogic.activeChat === 'ceo' ? 'Kenard' :
            chatLogic.activeChat === 'developer' ? 'Alex' :
            chatLogic.activeChat === 'group' ? 'the team' :
            (() => {
              const customGroup = chatLogic.customGroups.find(group => group.id === chatLogic.activeChat);
              return customGroup ? customGroup.participants.map(p => p.name).join(', ') : 'the team';
            })()
          }`}
          onFileSelect={(files) => {
            if (files && !chatLogic.loading && !fileHandling.processingFiles) {
              const event = { target: { files } } as React.ChangeEvent<HTMLInputElement>;
              fileHandling.handleFileChange(event);
            }
          }}
          hasAttachments={fileHandling.pendingFiles.length > 0}
        />
      </div>

      {/* File Viewer Modal */}
      <FileViewer
        fileViewerOpen={fileHandling.fileViewerOpen}
        setFileViewerOpen={fileHandling.setFileViewerOpen}
        selectedFile={fileHandling.selectedFile}
        setSelectedFile={fileHandling.setSelectedFile}
      />



    </div>
  );
}
