"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_codemirror_legacy-modes_6_5_1_node_modules_codemirror_le-89df80"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/javascript.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/javascript.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   javascript: () => (/* binding */ javascript),\n/* harmony export */   json: () => (/* binding */ json),\n/* harmony export */   jsonld: () => (/* binding */ jsonld),\n/* harmony export */   typescript: () => (/* binding */ typescript)\n/* harmony export */ });\nfunction mkJavaScript(parserConfig) {\n  var statementIndent = parserConfig.statementIndent;\n  var jsonldMode = parserConfig.jsonld;\n  var jsonMode = parserConfig.json || jsonldMode;\n  var isTS = parserConfig.typescript;\n  var wordRE = parserConfig.wordCharacters || /[\\w$\\xa1-\\uffff]/;\n\n  // Tokenizer\n\n  var keywords = function(){\n    function kw(type) {return {type: type, style: \"keyword\"};}\n    var A = kw(\"keyword a\"), B = kw(\"keyword b\"), C = kw(\"keyword c\"), D = kw(\"keyword d\");\n    var operator = kw(\"operator\"), atom = {type: \"atom\", style: \"atom\"};\n\n    return {\n      \"if\": kw(\"if\"), \"while\": A, \"with\": A, \"else\": B, \"do\": B, \"try\": B, \"finally\": B,\n      \"return\": D, \"break\": D, \"continue\": D, \"new\": kw(\"new\"), \"delete\": C, \"void\": C, \"throw\": C,\n      \"debugger\": kw(\"debugger\"), \"var\": kw(\"var\"), \"const\": kw(\"var\"), \"let\": kw(\"var\"),\n      \"function\": kw(\"function\"), \"catch\": kw(\"catch\"),\n      \"for\": kw(\"for\"), \"switch\": kw(\"switch\"), \"case\": kw(\"case\"), \"default\": kw(\"default\"),\n      \"in\": operator, \"typeof\": operator, \"instanceof\": operator,\n      \"true\": atom, \"false\": atom, \"null\": atom, \"undefined\": atom, \"NaN\": atom, \"Infinity\": atom,\n      \"this\": kw(\"this\"), \"class\": kw(\"class\"), \"super\": kw(\"atom\"),\n      \"yield\": C, \"export\": kw(\"export\"), \"import\": kw(\"import\"), \"extends\": C,\n      \"await\": C\n    };\n  }();\n\n  var isOperatorChar = /[+\\-*&%=<>!?|~^@]/;\n  var isJsonldKeyword = /^@(context|id|value|language|type|container|list|set|reverse|index|base|vocab|graph)\"/;\n\n  function readRegexp(stream) {\n    var escaped = false, next, inSet = false;\n    while ((next = stream.next()) != null) {\n      if (!escaped) {\n        if (next == \"/\" && !inSet) return;\n        if (next == \"[\") inSet = true;\n        else if (inSet && next == \"]\") inSet = false;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n  }\n\n  // Used as scratch variables to communicate multiple values without\n  // consing up tons of objects.\n  var type, content;\n  function ret(tp, style, cont) {\n    type = tp; content = cont;\n    return style;\n  }\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (ch == '\"' || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    } else if (ch == \".\" && stream.match(/^\\d[\\d_]*(?:[eE][+\\-]?[\\d_]+)?/)) {\n      return ret(\"number\", \"number\");\n    } else if (ch == \".\" && stream.match(\"..\")) {\n      return ret(\"spread\", \"meta\");\n    } else if (/[\\[\\]{}\\(\\),;\\:\\.]/.test(ch)) {\n      return ret(ch);\n    } else if (ch == \"=\" && stream.eat(\">\")) {\n      return ret(\"=>\", \"operator\");\n    } else if (ch == \"0\" && stream.match(/^(?:x[\\dA-Fa-f_]+|o[0-7_]+|b[01_]+)n?/)) {\n      return ret(\"number\", \"number\");\n    } else if (/\\d/.test(ch)) {\n      stream.match(/^[\\d_]*(?:n|(?:\\.[\\d_]*)?(?:[eE][+\\-]?[\\d_]+)?)?/);\n      return ret(\"number\", \"number\");\n    } else if (ch == \"/\") {\n      if (stream.eat(\"*\")) {\n        state.tokenize = tokenComment;\n        return tokenComment(stream, state);\n      } else if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return ret(\"comment\", \"comment\");\n      } else if (expressionAllowed(stream, state, 1)) {\n        readRegexp(stream);\n        stream.match(/^\\b(([gimyus])(?![gimyus]*\\2))+\\b/);\n        return ret(\"regexp\", \"string.special\");\n      } else {\n        stream.eat(\"=\");\n        return ret(\"operator\", \"operator\", stream.current());\n      }\n    } else if (ch == \"`\") {\n      state.tokenize = tokenQuasi;\n      return tokenQuasi(stream, state);\n    } else if (ch == \"#\" && stream.peek() == \"!\") {\n      stream.skipToEnd();\n      return ret(\"meta\", \"meta\");\n    } else if (ch == \"#\" && stream.eatWhile(wordRE)) {\n      return ret(\"variable\", \"property\")\n    } else if (ch == \"<\" && stream.match(\"!--\") ||\n               (ch == \"-\" && stream.match(\"->\") && !/\\S/.test(stream.string.slice(0, stream.start)))) {\n      stream.skipToEnd()\n      return ret(\"comment\", \"comment\")\n    } else if (isOperatorChar.test(ch)) {\n      if (ch != \">\" || !state.lexical || state.lexical.type != \">\") {\n        if (stream.eat(\"=\")) {\n          if (ch == \"!\" || ch == \"=\") stream.eat(\"=\")\n        } else if (/[<>*+\\-|&?]/.test(ch)) {\n          stream.eat(ch)\n          if (ch == \">\") stream.eat(ch)\n        }\n      }\n      if (ch == \"?\" && stream.eat(\".\")) return ret(\".\")\n      return ret(\"operator\", \"operator\", stream.current());\n    } else if (wordRE.test(ch)) {\n      stream.eatWhile(wordRE);\n      var word = stream.current()\n      if (state.lastType != \".\") {\n        if (keywords.propertyIsEnumerable(word)) {\n          var kw = keywords[word]\n          return ret(kw.type, kw.style, word)\n        }\n        if (word == \"async\" && stream.match(/^(\\s|\\/\\*([^*]|\\*(?!\\/))*?\\*\\/)*[\\[\\(\\w]/, false))\n          return ret(\"async\", \"keyword\", word)\n      }\n      return ret(\"variable\", \"variable\", word)\n    }\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, next;\n      if (jsonldMode && stream.peek() == \"@\" && stream.match(isJsonldKeyword)){\n        state.tokenize = tokenBase;\n        return ret(\"jsonld-keyword\", \"meta\");\n      }\n      while ((next = stream.next()) != null) {\n        if (next == quote && !escaped) break;\n        escaped = !escaped && next == \"\\\\\";\n      }\n      if (!escaped) state.tokenize = tokenBase;\n      return ret(\"string\", \"string\");\n    };\n  }\n\n  function tokenComment(stream, state) {\n    var maybeEnd = false, ch;\n    while (ch = stream.next()) {\n      if (ch == \"/\" && maybeEnd) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      maybeEnd = (ch == \"*\");\n    }\n    return ret(\"comment\", \"comment\");\n  }\n\n  function tokenQuasi(stream, state) {\n    var escaped = false, next;\n    while ((next = stream.next()) != null) {\n      if (!escaped && (next == \"`\" || next == \"$\" && stream.eat(\"{\"))) {\n        state.tokenize = tokenBase;\n        break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    return ret(\"quasi\", \"string.special\", stream.current());\n  }\n\n  var brackets = \"([{}])\";\n  // This is a crude lookahead trick to try and notice that we're\n  // parsing the argument patterns for a fat-arrow function before we\n  // actually hit the arrow token. It only works if the arrow is on\n  // the same line as the arguments and there's no strange noise\n  // (comments) in between. Fallback is to only notice when we hit the\n  // arrow, and not declare the arguments as locals for the arrow\n  // body.\n  function findFatArrow(stream, state) {\n    if (state.fatArrowAt) state.fatArrowAt = null;\n    var arrow = stream.string.indexOf(\"=>\", stream.start);\n    if (arrow < 0) return;\n\n    if (isTS) { // Try to skip TypeScript return type declarations after the arguments\n      var m = /:\\s*(?:\\w+(?:<[^>]*>|\\[\\])?|\\{[^}]*\\})\\s*$/.exec(stream.string.slice(stream.start, arrow))\n      if (m) arrow = m.index\n    }\n\n    var depth = 0, sawSomething = false;\n    for (var pos = arrow - 1; pos >= 0; --pos) {\n      var ch = stream.string.charAt(pos);\n      var bracket = brackets.indexOf(ch);\n      if (bracket >= 0 && bracket < 3) {\n        if (!depth) { ++pos; break; }\n        if (--depth == 0) { if (ch == \"(\") sawSomething = true; break; }\n      } else if (bracket >= 3 && bracket < 6) {\n        ++depth;\n      } else if (wordRE.test(ch)) {\n        sawSomething = true;\n      } else if (/[\"'\\/`]/.test(ch)) {\n        for (;; --pos) {\n          if (pos == 0) return\n          var next = stream.string.charAt(pos - 1)\n          if (next == ch && stream.string.charAt(pos - 2) != \"\\\\\") { pos--; break }\n        }\n      } else if (sawSomething && !depth) {\n        ++pos;\n        break;\n      }\n    }\n    if (sawSomething && !depth) state.fatArrowAt = pos;\n  }\n\n  // Parser\n\n  var atomicTypes = {\"atom\": true, \"number\": true, \"variable\": true, \"string\": true,\n                     \"regexp\": true, \"this\": true, \"import\": true, \"jsonld-keyword\": true};\n\n  function JSLexical(indented, column, type, align, prev, info) {\n    this.indented = indented;\n    this.column = column;\n    this.type = type;\n    this.prev = prev;\n    this.info = info;\n    if (align != null) this.align = align;\n  }\n\n  function inScope(state, varname) {\n    for (var v = state.localVars; v; v = v.next)\n      if (v.name == varname) return true;\n    for (var cx = state.context; cx; cx = cx.prev) {\n      for (var v = cx.vars; v; v = v.next)\n        if (v.name == varname) return true;\n    }\n  }\n\n  function parseJS(state, style, type, content, stream) {\n    var cc = state.cc;\n    // Communicate our context to the combinators.\n    // (Less wasteful than consing up a hundred closures on every call.)\n    cx.state = state; cx.stream = stream; cx.marked = null; cx.cc = cc; cx.style = style;\n\n    if (!state.lexical.hasOwnProperty(\"align\"))\n      state.lexical.align = true;\n\n    while(true) {\n      var combinator = cc.length ? cc.pop() : jsonMode ? expression : statement;\n      if (combinator(type, content)) {\n        while(cc.length && cc[cc.length - 1].lex)\n          cc.pop()();\n        if (cx.marked) return cx.marked;\n        if (type == \"variable\" && inScope(state, content)) return \"variableName.local\";\n        return style;\n      }\n    }\n  }\n\n  // Combinator utils\n\n  var cx = {state: null, column: null, marked: null, cc: null};\n  function pass() {\n    for (var i = arguments.length - 1; i >= 0; i--) cx.cc.push(arguments[i]);\n  }\n  function cont() {\n    pass.apply(null, arguments);\n    return true;\n  }\n  function inList(name, list) {\n    for (var v = list; v; v = v.next) if (v.name == name) return true\n    return false;\n  }\n  function register(varname) {\n    var state = cx.state;\n    cx.marked = \"def\";\n    if (state.context) {\n      if (state.lexical.info == \"var\" && state.context && state.context.block) {\n        // FIXME function decls are also not block scoped\n        var newContext = registerVarScoped(varname, state.context)\n        if (newContext != null) {\n          state.context = newContext\n          return\n        }\n      } else if (!inList(varname, state.localVars)) {\n        state.localVars = new Var(varname, state.localVars)\n        return\n      }\n    }\n    // Fall through means this is global\n    if (parserConfig.globalVars && !inList(varname, state.globalVars))\n      state.globalVars = new Var(varname, state.globalVars)\n  }\n  function registerVarScoped(varname, context) {\n    if (!context) {\n      return null\n    } else if (context.block) {\n      var inner = registerVarScoped(varname, context.prev)\n      if (!inner) return null\n      if (inner == context.prev) return context\n      return new Context(inner, context.vars, true)\n    } else if (inList(varname, context.vars)) {\n      return context\n    } else {\n      return new Context(context.prev, new Var(varname, context.vars), false)\n    }\n  }\n\n  function isModifier(name) {\n    return name == \"public\" || name == \"private\" || name == \"protected\" || name == \"abstract\" || name == \"readonly\"\n  }\n\n  // Combinators\n\n  function Context(prev, vars, block) { this.prev = prev; this.vars = vars; this.block = block }\n  function Var(name, next) { this.name = name; this.next = next }\n\n  var defaultVars = new Var(\"this\", new Var(\"arguments\", null))\n  function pushcontext() {\n    cx.state.context = new Context(cx.state.context, cx.state.localVars, false)\n    cx.state.localVars = defaultVars\n  }\n  function pushblockcontext() {\n    cx.state.context = new Context(cx.state.context, cx.state.localVars, true)\n    cx.state.localVars = null\n  }\n  pushcontext.lex = pushblockcontext.lex = true\n  function popcontext() {\n    cx.state.localVars = cx.state.context.vars\n    cx.state.context = cx.state.context.prev\n  }\n  popcontext.lex = true\n  function pushlex(type, info) {\n    var result = function() {\n      var state = cx.state, indent = state.indented;\n      if (state.lexical.type == \"stat\") indent = state.lexical.indented;\n      else for (var outer = state.lexical; outer && outer.type == \")\" && outer.align; outer = outer.prev)\n        indent = outer.indented;\n      state.lexical = new JSLexical(indent, cx.stream.column(), type, null, state.lexical, info);\n    };\n    result.lex = true;\n    return result;\n  }\n  function poplex() {\n    var state = cx.state;\n    if (state.lexical.prev) {\n      if (state.lexical.type == \")\")\n        state.indented = state.lexical.indented;\n      state.lexical = state.lexical.prev;\n    }\n  }\n  poplex.lex = true;\n\n  function expect(wanted) {\n    function exp(type) {\n      if (type == wanted) return cont();\n      else if (wanted == \";\" || type == \"}\" || type == \")\" || type == \"]\") return pass();\n      else return cont(exp);\n    };\n    return exp;\n  }\n\n  function statement(type, value) {\n    if (type == \"var\") return cont(pushlex(\"vardef\", value), vardef, expect(\";\"), poplex);\n    if (type == \"keyword a\") return cont(pushlex(\"form\"), parenExpr, statement, poplex);\n    if (type == \"keyword b\") return cont(pushlex(\"form\"), statement, poplex);\n    if (type == \"keyword d\") return cx.stream.match(/^\\s*$/, false) ? cont() : cont(pushlex(\"stat\"), maybeexpression, expect(\";\"), poplex);\n    if (type == \"debugger\") return cont(expect(\";\"));\n    if (type == \"{\") return cont(pushlex(\"}\"), pushblockcontext, block, poplex, popcontext);\n    if (type == \";\") return cont();\n    if (type == \"if\") {\n      if (cx.state.lexical.info == \"else\" && cx.state.cc[cx.state.cc.length - 1] == poplex)\n        cx.state.cc.pop()();\n      return cont(pushlex(\"form\"), parenExpr, statement, poplex, maybeelse);\n    }\n    if (type == \"function\") return cont(functiondef);\n    if (type == \"for\") return cont(pushlex(\"form\"), pushblockcontext, forspec, statement, popcontext, poplex);\n    if (type == \"class\" || (isTS && value == \"interface\")) {\n      cx.marked = \"keyword\"\n      return cont(pushlex(\"form\", type == \"class\" ? type : value), className, poplex)\n    }\n    if (type == \"variable\") {\n      if (isTS && value == \"declare\") {\n        cx.marked = \"keyword\"\n        return cont(statement)\n      } else if (isTS && (value == \"module\" || value == \"enum\" || value == \"type\") && cx.stream.match(/^\\s*\\w/, false)) {\n        cx.marked = \"keyword\"\n        if (value == \"enum\") return cont(enumdef);\n        else if (value == \"type\") return cont(typename, expect(\"operator\"), typeexpr, expect(\";\"));\n        else return cont(pushlex(\"form\"), pattern, expect(\"{\"), pushlex(\"}\"), block, poplex, poplex)\n      } else if (isTS && value == \"namespace\") {\n        cx.marked = \"keyword\"\n        return cont(pushlex(\"form\"), expression, statement, poplex)\n      } else if (isTS && value == \"abstract\") {\n        cx.marked = \"keyword\"\n        return cont(statement)\n      } else {\n        return cont(pushlex(\"stat\"), maybelabel);\n      }\n    }\n    if (type == \"switch\") return cont(pushlex(\"form\"), parenExpr, expect(\"{\"), pushlex(\"}\", \"switch\"), pushblockcontext,\n                                      block, poplex, poplex, popcontext);\n    if (type == \"case\") return cont(expression, expect(\":\"));\n    if (type == \"default\") return cont(expect(\":\"));\n    if (type == \"catch\") return cont(pushlex(\"form\"), pushcontext, maybeCatchBinding, statement, poplex, popcontext);\n    if (type == \"export\") return cont(pushlex(\"stat\"), afterExport, poplex);\n    if (type == \"import\") return cont(pushlex(\"stat\"), afterImport, poplex);\n    if (type == \"async\") return cont(statement)\n    if (value == \"@\") return cont(expression, statement)\n    return pass(pushlex(\"stat\"), expression, expect(\";\"), poplex);\n  }\n  function maybeCatchBinding(type) {\n    if (type == \"(\") return cont(funarg, expect(\")\"))\n  }\n  function expression(type, value) {\n    return expressionInner(type, value, false);\n  }\n  function expressionNoComma(type, value) {\n    return expressionInner(type, value, true);\n  }\n  function parenExpr(type) {\n    if (type != \"(\") return pass()\n    return cont(pushlex(\")\"), maybeexpression, expect(\")\"), poplex)\n  }\n  function expressionInner(type, value, noComma) {\n    if (cx.state.fatArrowAt == cx.stream.start) {\n      var body = noComma ? arrowBodyNoComma : arrowBody;\n      if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, expect(\"=>\"), body, popcontext);\n      else if (type == \"variable\") return pass(pushcontext, pattern, expect(\"=>\"), body, popcontext);\n    }\n\n    var maybeop = noComma ? maybeoperatorNoComma : maybeoperatorComma;\n    if (atomicTypes.hasOwnProperty(type)) return cont(maybeop);\n    if (type == \"function\") return cont(functiondef, maybeop);\n    if (type == \"class\" || (isTS && value == \"interface\")) { cx.marked = \"keyword\"; return cont(pushlex(\"form\"), classExpression, poplex); }\n    if (type == \"keyword c\" || type == \"async\") return cont(noComma ? expressionNoComma : expression);\n    if (type == \"(\") return cont(pushlex(\")\"), maybeexpression, expect(\")\"), poplex, maybeop);\n    if (type == \"operator\" || type == \"spread\") return cont(noComma ? expressionNoComma : expression);\n    if (type == \"[\") return cont(pushlex(\"]\"), arrayLiteral, poplex, maybeop);\n    if (type == \"{\") return contCommasep(objprop, \"}\", null, maybeop);\n    if (type == \"quasi\") return pass(quasi, maybeop);\n    if (type == \"new\") return cont(maybeTarget(noComma));\n    return cont();\n  }\n  function maybeexpression(type) {\n    if (type.match(/[;\\}\\)\\],]/)) return pass();\n    return pass(expression);\n  }\n\n  function maybeoperatorComma(type, value) {\n    if (type == \",\") return cont(maybeexpression);\n    return maybeoperatorNoComma(type, value, false);\n  }\n  function maybeoperatorNoComma(type, value, noComma) {\n    var me = noComma == false ? maybeoperatorComma : maybeoperatorNoComma;\n    var expr = noComma == false ? expression : expressionNoComma;\n    if (type == \"=>\") return cont(pushcontext, noComma ? arrowBodyNoComma : arrowBody, popcontext);\n    if (type == \"operator\") {\n      if (/\\+\\+|--/.test(value) || isTS && value == \"!\") return cont(me);\n      if (isTS && value == \"<\" && cx.stream.match(/^([^<>]|<[^<>]*>)*>\\s*\\(/, false))\n        return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, me);\n      if (value == \"?\") return cont(expression, expect(\":\"), expr);\n      return cont(expr);\n    }\n    if (type == \"quasi\") { return pass(quasi, me); }\n    if (type == \";\") return;\n    if (type == \"(\") return contCommasep(expressionNoComma, \")\", \"call\", me);\n    if (type == \".\") return cont(property, me);\n    if (type == \"[\") return cont(pushlex(\"]\"), maybeexpression, expect(\"]\"), poplex, me);\n    if (isTS && value == \"as\") { cx.marked = \"keyword\"; return cont(typeexpr, me) }\n    if (type == \"regexp\") {\n      cx.state.lastType = cx.marked = \"operator\"\n      cx.stream.backUp(cx.stream.pos - cx.stream.start - 1)\n      return cont(expr)\n    }\n  }\n  function quasi(type, value) {\n    if (type != \"quasi\") return pass();\n    if (value.slice(value.length - 2) != \"${\") return cont(quasi);\n    return cont(maybeexpression, continueQuasi);\n  }\n  function continueQuasi(type) {\n    if (type == \"}\") {\n      cx.marked = \"string.special\";\n      cx.state.tokenize = tokenQuasi;\n      return cont(quasi);\n    }\n  }\n  function arrowBody(type) {\n    findFatArrow(cx.stream, cx.state);\n    return pass(type == \"{\" ? statement : expression);\n  }\n  function arrowBodyNoComma(type) {\n    findFatArrow(cx.stream, cx.state);\n    return pass(type == \"{\" ? statement : expressionNoComma);\n  }\n  function maybeTarget(noComma) {\n    return function(type) {\n      if (type == \".\") return cont(noComma ? targetNoComma : target);\n      else if (type == \"variable\" && isTS) return cont(maybeTypeArgs, noComma ? maybeoperatorNoComma : maybeoperatorComma)\n      else return pass(noComma ? expressionNoComma : expression);\n    };\n  }\n  function target(_, value) {\n    if (value == \"target\") { cx.marked = \"keyword\"; return cont(maybeoperatorComma); }\n  }\n  function targetNoComma(_, value) {\n    if (value == \"target\") { cx.marked = \"keyword\"; return cont(maybeoperatorNoComma); }\n  }\n  function maybelabel(type) {\n    if (type == \":\") return cont(poplex, statement);\n    return pass(maybeoperatorComma, expect(\";\"), poplex);\n  }\n  function property(type) {\n    if (type == \"variable\") {cx.marked = \"property\"; return cont();}\n  }\n  function objprop(type, value) {\n    if (type == \"async\") {\n      cx.marked = \"property\";\n      return cont(objprop);\n    } else if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\";\n      if (value == \"get\" || value == \"set\") return cont(getterSetter);\n      var m // Work around fat-arrow-detection complication for detecting typescript typed arrow params\n      if (isTS && cx.state.fatArrowAt == cx.stream.start && (m = cx.stream.match(/^\\s*:\\s*/, false)))\n        cx.state.fatArrowAt = cx.stream.pos + m[0].length\n      return cont(afterprop);\n    } else if (type == \"number\" || type == \"string\") {\n      cx.marked = jsonldMode ? \"property\" : (cx.style + \" property\");\n      return cont(afterprop);\n    } else if (type == \"jsonld-keyword\") {\n      return cont(afterprop);\n    } else if (isTS && isModifier(value)) {\n      cx.marked = \"keyword\"\n      return cont(objprop)\n    } else if (type == \"[\") {\n      return cont(expression, maybetype, expect(\"]\"), afterprop);\n    } else if (type == \"spread\") {\n      return cont(expressionNoComma, afterprop);\n    } else if (value == \"*\") {\n      cx.marked = \"keyword\";\n      return cont(objprop);\n    } else if (type == \":\") {\n      return pass(afterprop)\n    }\n  }\n  function getterSetter(type) {\n    if (type != \"variable\") return pass(afterprop);\n    cx.marked = \"property\";\n    return cont(functiondef);\n  }\n  function afterprop(type) {\n    if (type == \":\") return cont(expressionNoComma);\n    if (type == \"(\") return pass(functiondef);\n  }\n  function commasep(what, end, sep) {\n    function proceed(type, value) {\n      if (sep ? sep.indexOf(type) > -1 : type == \",\") {\n        var lex = cx.state.lexical;\n        if (lex.info == \"call\") lex.pos = (lex.pos || 0) + 1;\n        return cont(function(type, value) {\n          if (type == end || value == end) return pass()\n          return pass(what)\n        }, proceed);\n      }\n      if (type == end || value == end) return cont();\n      if (sep && sep.indexOf(\";\") > -1) return pass(what)\n      return cont(expect(end));\n    }\n    return function(type, value) {\n      if (type == end || value == end) return cont();\n      return pass(what, proceed);\n    };\n  }\n  function contCommasep(what, end, info) {\n    for (var i = 3; i < arguments.length; i++)\n      cx.cc.push(arguments[i]);\n    return cont(pushlex(end, info), commasep(what, end), poplex);\n  }\n  function block(type) {\n    if (type == \"}\") return cont();\n    return pass(statement, block);\n  }\n  function maybetype(type, value) {\n    if (isTS) {\n      if (type == \":\") return cont(typeexpr);\n      if (value == \"?\") return cont(maybetype);\n    }\n  }\n  function maybetypeOrIn(type, value) {\n    if (isTS && (type == \":\" || value == \"in\")) return cont(typeexpr)\n  }\n  function mayberettype(type) {\n    if (isTS && type == \":\") {\n      if (cx.stream.match(/^\\s*\\w+\\s+is\\b/, false)) return cont(expression, isKW, typeexpr)\n      else return cont(typeexpr)\n    }\n  }\n  function isKW(_, value) {\n    if (value == \"is\") {\n      cx.marked = \"keyword\"\n      return cont()\n    }\n  }\n  function typeexpr(type, value) {\n    if (value == \"keyof\" || value == \"typeof\" || value == \"infer\" || value == \"readonly\") {\n      cx.marked = \"keyword\"\n      return cont(value == \"typeof\" ? expressionNoComma : typeexpr)\n    }\n    if (type == \"variable\" || value == \"void\") {\n      cx.marked = \"type\"\n      return cont(afterType)\n    }\n    if (value == \"|\" || value == \"&\") return cont(typeexpr)\n    if (type == \"string\" || type == \"number\" || type == \"atom\") return cont(afterType);\n    if (type == \"[\") return cont(pushlex(\"]\"), commasep(typeexpr, \"]\", \",\"), poplex, afterType)\n    if (type == \"{\") return cont(pushlex(\"}\"), typeprops, poplex, afterType)\n    if (type == \"(\") return cont(commasep(typearg, \")\"), maybeReturnType, afterType)\n    if (type == \"<\") return cont(commasep(typeexpr, \">\"), typeexpr)\n    if (type == \"quasi\") return pass(quasiType, afterType)\n  }\n  function maybeReturnType(type) {\n    if (type == \"=>\") return cont(typeexpr)\n  }\n  function typeprops(type) {\n    if (type.match(/[\\}\\)\\]]/)) return cont()\n    if (type == \",\" || type == \";\") return cont(typeprops)\n    return pass(typeprop, typeprops)\n  }\n  function typeprop(type, value) {\n    if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\"\n      return cont(typeprop)\n    } else if (value == \"?\" || type == \"number\" || type == \"string\") {\n      return cont(typeprop)\n    } else if (type == \":\") {\n      return cont(typeexpr)\n    } else if (type == \"[\") {\n      return cont(expect(\"variable\"), maybetypeOrIn, expect(\"]\"), typeprop)\n    } else if (type == \"(\") {\n      return pass(functiondecl, typeprop)\n    } else if (!type.match(/[;\\}\\)\\],]/)) {\n      return cont()\n    }\n  }\n  function quasiType(type, value) {\n    if (type != \"quasi\") return pass();\n    if (value.slice(value.length - 2) != \"${\") return cont(quasiType);\n    return cont(typeexpr, continueQuasiType);\n  }\n  function continueQuasiType(type) {\n   if (type == \"}\") {\n      cx.marked = \"string.special\";\n      cx.state.tokenize = tokenQuasi;\n      return cont(quasiType);\n    }\n  }\n  function typearg(type, value) {\n    if (type == \"variable\" && cx.stream.match(/^\\s*[?:]/, false) || value == \"?\") return cont(typearg)\n    if (type == \":\") return cont(typeexpr)\n    if (type == \"spread\") return cont(typearg)\n    return pass(typeexpr)\n  }\n  function afterType(type, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, afterType)\n    if (value == \"|\" || type == \".\" || value == \"&\") return cont(typeexpr)\n    if (type == \"[\") return cont(typeexpr, expect(\"]\"), afterType)\n    if (value == \"extends\" || value == \"implements\") { cx.marked = \"keyword\"; return cont(typeexpr) }\n    if (value == \"?\") return cont(typeexpr, expect(\":\"), typeexpr)\n  }\n  function maybeTypeArgs(_, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeexpr, \">\"), poplex, afterType)\n  }\n  function typeparam() {\n    return pass(typeexpr, maybeTypeDefault)\n  }\n  function maybeTypeDefault(_, value) {\n    if (value == \"=\") return cont(typeexpr)\n  }\n  function vardef(_, value) {\n    if (value == \"enum\") {cx.marked = \"keyword\"; return cont(enumdef)}\n    return pass(pattern, maybetype, maybeAssign, vardefCont);\n  }\n  function pattern(type, value) {\n    if (isTS && isModifier(value)) { cx.marked = \"keyword\"; return cont(pattern) }\n    if (type == \"variable\") { register(value); return cont(); }\n    if (type == \"spread\") return cont(pattern);\n    if (type == \"[\") return contCommasep(eltpattern, \"]\");\n    if (type == \"{\") return contCommasep(proppattern, \"}\");\n  }\n  function proppattern(type, value) {\n    if (type == \"variable\" && !cx.stream.match(/^\\s*:/, false)) {\n      register(value);\n      return cont(maybeAssign);\n    }\n    if (type == \"variable\") cx.marked = \"property\";\n    if (type == \"spread\") return cont(pattern);\n    if (type == \"}\") return pass();\n    if (type == \"[\") return cont(expression, expect(']'), expect(':'), proppattern);\n    return cont(expect(\":\"), pattern, maybeAssign);\n  }\n  function eltpattern() {\n    return pass(pattern, maybeAssign)\n  }\n  function maybeAssign(_type, value) {\n    if (value == \"=\") return cont(expressionNoComma);\n  }\n  function vardefCont(type) {\n    if (type == \",\") return cont(vardef);\n  }\n  function maybeelse(type, value) {\n    if (type == \"keyword b\" && value == \"else\") return cont(pushlex(\"form\", \"else\"), statement, poplex);\n  }\n  function forspec(type, value) {\n    if (value == \"await\") return cont(forspec);\n    if (type == \"(\") return cont(pushlex(\")\"), forspec1, poplex);\n  }\n  function forspec1(type) {\n    if (type == \"var\") return cont(vardef, forspec2);\n    if (type == \"variable\") return cont(forspec2);\n    return pass(forspec2)\n  }\n  function forspec2(type, value) {\n    if (type == \")\") return cont()\n    if (type == \";\") return cont(forspec2)\n    if (value == \"in\" || value == \"of\") { cx.marked = \"keyword\"; return cont(expression, forspec2) }\n    return pass(expression, forspec2)\n  }\n  function functiondef(type, value) {\n    if (value == \"*\") {cx.marked = \"keyword\"; return cont(functiondef);}\n    if (type == \"variable\") {register(value); return cont(functiondef);}\n    if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, mayberettype, statement, popcontext);\n    if (isTS && value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, functiondef)\n  }\n  function functiondecl(type, value) {\n    if (value == \"*\") {cx.marked = \"keyword\"; return cont(functiondecl);}\n    if (type == \"variable\") {register(value); return cont(functiondecl);}\n    if (type == \"(\") return cont(pushcontext, pushlex(\")\"), commasep(funarg, \")\"), poplex, mayberettype, popcontext);\n    if (isTS && value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, functiondecl)\n  }\n  function typename(type, value) {\n    if (type == \"keyword\" || type == \"variable\") {\n      cx.marked = \"type\"\n      return cont(typename)\n    } else if (value == \"<\") {\n      return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex)\n    }\n  }\n  function funarg(type, value) {\n    if (value == \"@\") cont(expression, funarg)\n    if (type == \"spread\") return cont(funarg);\n    if (isTS && isModifier(value)) { cx.marked = \"keyword\"; return cont(funarg); }\n    if (isTS && type == \"this\") return cont(maybetype, maybeAssign)\n    return pass(pattern, maybetype, maybeAssign);\n  }\n  function classExpression(type, value) {\n    // Class expressions may have an optional name.\n    if (type == \"variable\") return className(type, value);\n    return classNameAfter(type, value);\n  }\n  function className(type, value) {\n    if (type == \"variable\") {register(value); return cont(classNameAfter);}\n  }\n  function classNameAfter(type, value) {\n    if (value == \"<\") return cont(pushlex(\">\"), commasep(typeparam, \">\"), poplex, classNameAfter)\n    if (value == \"extends\" || value == \"implements\" || (isTS && type == \",\")) {\n      if (value == \"implements\") cx.marked = \"keyword\";\n      return cont(isTS ? typeexpr : expression, classNameAfter);\n    }\n    if (type == \"{\") return cont(pushlex(\"}\"), classBody, poplex);\n  }\n  function classBody(type, value) {\n    if (type == \"async\" ||\n        (type == \"variable\" &&\n         (value == \"static\" || value == \"get\" || value == \"set\" || (isTS && isModifier(value))) &&\n         cx.stream.match(/^\\s+#?[\\w$\\xa1-\\uffff]/, false))) {\n      cx.marked = \"keyword\";\n      return cont(classBody);\n    }\n    if (type == \"variable\" || cx.style == \"keyword\") {\n      cx.marked = \"property\";\n      return cont(classfield, classBody);\n    }\n    if (type == \"number\" || type == \"string\") return cont(classfield, classBody);\n    if (type == \"[\")\n      return cont(expression, maybetype, expect(\"]\"), classfield, classBody)\n    if (value == \"*\") {\n      cx.marked = \"keyword\";\n      return cont(classBody);\n    }\n    if (isTS && type == \"(\") return pass(functiondecl, classBody)\n    if (type == \";\" || type == \",\") return cont(classBody);\n    if (type == \"}\") return cont();\n    if (value == \"@\") return cont(expression, classBody)\n  }\n  function classfield(type, value) {\n    if (value == \"!\" || value == \"?\") return cont(classfield)\n    if (type == \":\") return cont(typeexpr, maybeAssign)\n    if (value == \"=\") return cont(expressionNoComma)\n    var context = cx.state.lexical.prev, isInterface = context && context.info == \"interface\"\n    return pass(isInterface ? functiondecl : functiondef)\n  }\n  function afterExport(type, value) {\n    if (value == \"*\") { cx.marked = \"keyword\"; return cont(maybeFrom, expect(\";\")); }\n    if (value == \"default\") { cx.marked = \"keyword\"; return cont(expression, expect(\";\")); }\n    if (type == \"{\") return cont(commasep(exportField, \"}\"), maybeFrom, expect(\";\"));\n    return pass(statement);\n  }\n  function exportField(type, value) {\n    if (value == \"as\") { cx.marked = \"keyword\"; return cont(expect(\"variable\")); }\n    if (type == \"variable\") return pass(expressionNoComma, exportField);\n  }\n  function afterImport(type) {\n    if (type == \"string\") return cont();\n    if (type == \"(\") return pass(expression);\n    if (type == \".\") return pass(maybeoperatorComma);\n    return pass(importSpec, maybeMoreImports, maybeFrom);\n  }\n  function importSpec(type, value) {\n    if (type == \"{\") return contCommasep(importSpec, \"}\");\n    if (type == \"variable\") register(value);\n    if (value == \"*\") cx.marked = \"keyword\";\n    return cont(maybeAs);\n  }\n  function maybeMoreImports(type) {\n    if (type == \",\") return cont(importSpec, maybeMoreImports)\n  }\n  function maybeAs(_type, value) {\n    if (value == \"as\") { cx.marked = \"keyword\"; return cont(importSpec); }\n  }\n  function maybeFrom(_type, value) {\n    if (value == \"from\") { cx.marked = \"keyword\"; return cont(expression); }\n  }\n  function arrayLiteral(type) {\n    if (type == \"]\") return cont();\n    return pass(commasep(expressionNoComma, \"]\"));\n  }\n  function enumdef() {\n    return pass(pushlex(\"form\"), pattern, expect(\"{\"), pushlex(\"}\"), commasep(enummember, \"}\"), poplex, poplex)\n  }\n  function enummember() {\n    return pass(pattern, maybeAssign);\n  }\n\n  function isContinuedStatement(state, textAfter) {\n    return state.lastType == \"operator\" || state.lastType == \",\" ||\n      isOperatorChar.test(textAfter.charAt(0)) ||\n      /[,.]/.test(textAfter.charAt(0));\n  }\n\n  function expressionAllowed(stream, state, backUp) {\n    return state.tokenize == tokenBase &&\n      /^(?:operator|sof|keyword [bcd]|case|new|export|default|spread|[\\[{}\\(,;:]|=>)$/.test(state.lastType) ||\n      (state.lastType == \"quasi\" && /\\{\\s*$/.test(stream.string.slice(0, stream.pos - (backUp || 0))))\n  }\n\n  // Interface\n\n  return {\n    name: parserConfig.name,\n\n    startState: function(indentUnit) {\n      var state = {\n        tokenize: tokenBase,\n        lastType: \"sof\",\n        cc: [],\n        lexical: new JSLexical(-indentUnit, 0, \"block\", false),\n        localVars: parserConfig.localVars,\n        context: parserConfig.localVars && new Context(null, null, false),\n        indented: 0\n      };\n      if (parserConfig.globalVars && typeof parserConfig.globalVars == \"object\")\n        state.globalVars = parserConfig.globalVars;\n      return state;\n    },\n\n    token: function(stream, state) {\n      if (stream.sol()) {\n        if (!state.lexical.hasOwnProperty(\"align\"))\n          state.lexical.align = false;\n        state.indented = stream.indentation();\n        findFatArrow(stream, state);\n      }\n      if (state.tokenize != tokenComment && stream.eatSpace()) return null;\n      var style = state.tokenize(stream, state);\n      if (type == \"comment\") return style;\n      state.lastType = type == \"operator\" && (content == \"++\" || content == \"--\") ? \"incdec\" : type;\n      return parseJS(state, style, type, content, stream);\n    },\n\n    indent: function(state, textAfter, cx) {\n      if (state.tokenize == tokenComment || state.tokenize == tokenQuasi) return null;\n      if (state.tokenize != tokenBase) return 0;\n      var firstChar = textAfter && textAfter.charAt(0), lexical = state.lexical, top\n      // Kludge to prevent 'maybelse' from blocking lexical scope pops\n      if (!/^\\s*else\\b/.test(textAfter)) for (var i = state.cc.length - 1; i >= 0; --i) {\n        var c = state.cc[i];\n        if (c == poplex) lexical = lexical.prev;\n        else if (c != maybeelse && c != popcontext) break;\n      }\n      while ((lexical.type == \"stat\" || lexical.type == \"form\") &&\n             (firstChar == \"}\" || ((top = state.cc[state.cc.length - 1]) &&\n                                   (top == maybeoperatorComma || top == maybeoperatorNoComma) &&\n                                   !/^[,\\.=+\\-*:?[\\(]/.test(textAfter))))\n        lexical = lexical.prev;\n      if (statementIndent && lexical.type == \")\" && lexical.prev.type == \"stat\")\n        lexical = lexical.prev;\n      var type = lexical.type, closing = firstChar == type;\n\n      if (type == \"vardef\") return lexical.indented + (state.lastType == \"operator\" || state.lastType == \",\" ? lexical.info.length + 1 : 0);\n      else if (type == \"form\" && firstChar == \"{\") return lexical.indented;\n      else if (type == \"form\") return lexical.indented + cx.unit;\n      else if (type == \"stat\")\n        return lexical.indented + (isContinuedStatement(state, textAfter) ? statementIndent || cx.unit : 0);\n      else if (lexical.info == \"switch\" && !closing && parserConfig.doubleIndentSwitch != false)\n        return lexical.indented + (/^(?:case|default)\\b/.test(textAfter) ? cx.unit : 2 * cx.unit);\n      else if (lexical.align) return lexical.column + (closing ? 0 : 1);\n      else return lexical.indented + (closing ? 0 : cx.unit);\n    },\n\n    languageData: {\n      indentOnInput: /^\\s*(?:case .*?:|default:|\\{|\\})$/,\n      commentTokens: jsonMode ? undefined : {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n      closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"]},\n      wordChars: \"$\"\n    }\n  };\n};\n\nconst javascript = mkJavaScript({name: \"javascript\"})\nconst json = mkJavaScript({name: \"json\", json: true})\nconst jsonld = mkJavaScript({name: \"json\", jsonld: true})\nconst typescript = mkJavaScript({name: \"typescript\", typescript: true})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/javascript.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/pug.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/pug.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pug: () => (/* binding */ pug)\n/* harmony export */ });\n/* harmony import */ var _javascript_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./javascript.js */ \"(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/javascript.js\");\n\n\nvar ATTRS_NEST = {\n  '{': '}',\n  '(': ')',\n  '[': ']'\n}\n\nfunction defaultCopyState(state) {\n  if (typeof state != \"object\") return state\n  let newState = {}\n  for (let prop in state) {\n    let val = state[prop]\n    newState[prop] = val instanceof Array ? val.slice() : val\n  }\n  return newState\n}\n\nclass State {\n  constructor(indentUnit) {\n    this.indentUnit = indentUnit\n\n    this.javaScriptLine = false\n    this.javaScriptLineExcludesColon = false\n\n    this.javaScriptArguments = false\n    this.javaScriptArgumentsDepth = 0\n\n    this.isInterpolating = false\n    this.interpolationNesting = 0\n\n    this.jsState = _javascript_js__WEBPACK_IMPORTED_MODULE_0__.javascript.startState(indentUnit)\n\n    this.restOfLine = ''\n\n    this.isIncludeFiltered = false\n    this.isEach = false\n\n    this.lastTag = ''\n\n    // Attributes Mode\n    this.isAttrs = false\n    this.attrsNest = []\n    this.inAttributeName = true\n    this.attributeIsType = false\n    this.attrValue = ''\n\n    // Indented Mode\n    this.indentOf = Infinity\n    this.indentToken = ''\n  }\n\n  copy() {\n    var res = new State(this.indentUnit)\n    res.javaScriptLine = this.javaScriptLine\n    res.javaScriptLineExcludesColon = this.javaScriptLineExcludesColon\n    res.javaScriptArguments = this.javaScriptArguments\n    res.javaScriptArgumentsDepth = this.javaScriptArgumentsDepth\n    res.isInterpolating = this.isInterpolating\n    res.interpolationNesting = this.interpolationNesting\n\n    res.jsState = (_javascript_js__WEBPACK_IMPORTED_MODULE_0__.javascript.copyState || defaultCopyState)(this.jsState)\n\n    res.restOfLine = this.restOfLine\n\n    res.isIncludeFiltered = this.isIncludeFiltered\n    res.isEach = this.isEach\n    res.lastTag = this.lastTag\n    res.isAttrs = this.isAttrs\n    res.attrsNest = this.attrsNest.slice()\n    res.inAttributeName = this.inAttributeName\n    res.attributeIsType = this.attributeIsType\n    res.attrValue = this.attrValue\n    res.indentOf = this.indentOf\n    res.indentToken = this.indentToken\n\n    return res\n  }\n}\n\nfunction javaScript(stream, state) {\n  if (stream.sol()) {\n    // if javaScriptLine was set at end of line, ignore it\n    state.javaScriptLine = false\n    state.javaScriptLineExcludesColon = false\n  }\n  if (state.javaScriptLine) {\n    if (state.javaScriptLineExcludesColon && stream.peek() === ':') {\n      state.javaScriptLine = false\n      state.javaScriptLineExcludesColon = false\n      return\n    }\n    var tok = _javascript_js__WEBPACK_IMPORTED_MODULE_0__.javascript.token(stream, state.jsState)\n    if (stream.eol()) state.javaScriptLine = false\n    return tok || true\n  }\n}\nfunction javaScriptArguments(stream, state) {\n  if (state.javaScriptArguments) {\n    if (state.javaScriptArgumentsDepth === 0 && stream.peek() !== '(') {\n      state.javaScriptArguments = false\n      return\n    }\n    if (stream.peek() === '(') {\n      state.javaScriptArgumentsDepth++\n    } else if (stream.peek() === ')') {\n      state.javaScriptArgumentsDepth--\n    }\n    if (state.javaScriptArgumentsDepth === 0) {\n      state.javaScriptArguments = false\n      return\n    }\n\n    var tok = _javascript_js__WEBPACK_IMPORTED_MODULE_0__.javascript.token(stream, state.jsState)\n    return tok || true\n  }\n}\n\nfunction yieldStatement(stream) {\n  if (stream.match(/^yield\\b/)) {\n    return 'keyword'\n  }\n}\n\nfunction doctype(stream) {\n  if (stream.match(/^(?:doctype) *([^\\n]+)?/)) return 'meta'\n}\n\nfunction interpolation(stream, state) {\n  if (stream.match('#{')) {\n    state.isInterpolating = true\n    state.interpolationNesting = 0\n    return 'punctuation'\n  }\n}\n\nfunction interpolationContinued(stream, state) {\n  if (state.isInterpolating) {\n    if (stream.peek() === '}') {\n      state.interpolationNesting--\n      if (state.interpolationNesting < 0) {\n        stream.next()\n        state.isInterpolating = false\n        return 'punctuation'\n      }\n    } else if (stream.peek() === '{') {\n      state.interpolationNesting++\n    }\n    return _javascript_js__WEBPACK_IMPORTED_MODULE_0__.javascript.token(stream, state.jsState) || true\n  }\n}\n\nfunction caseStatement(stream, state) {\n  if (stream.match(/^case\\b/)) {\n    state.javaScriptLine = true\n    return 'keyword'\n  }\n}\n\nfunction when(stream, state) {\n  if (stream.match(/^when\\b/)) {\n    state.javaScriptLine = true\n    state.javaScriptLineExcludesColon = true\n    return 'keyword'\n  }\n}\n\nfunction defaultStatement(stream) {\n  if (stream.match(/^default\\b/)) {\n    return 'keyword'\n  }\n}\n\nfunction extendsStatement(stream, state) {\n  if (stream.match(/^extends?\\b/)) {\n    state.restOfLine = 'string'\n    return 'keyword'\n  }\n}\n\nfunction append(stream, state) {\n  if (stream.match(/^append\\b/)) {\n    state.restOfLine = 'variable'\n    return 'keyword'\n  }\n}\nfunction prepend(stream, state) {\n  if (stream.match(/^prepend\\b/)) {\n    state.restOfLine = 'variable'\n    return 'keyword'\n  }\n}\nfunction block(stream, state) {\n  if (stream.match(/^block\\b *(?:(prepend|append)\\b)?/)) {\n    state.restOfLine = 'variable'\n    return 'keyword'\n  }\n}\n\nfunction include(stream, state) {\n  if (stream.match(/^include\\b/)) {\n    state.restOfLine = 'string'\n    return 'keyword'\n  }\n}\n\nfunction includeFiltered(stream, state) {\n  if (stream.match(/^include:([a-zA-Z0-9\\-]+)/, false) && stream.match('include')) {\n    state.isIncludeFiltered = true\n    return 'keyword'\n  }\n}\n\nfunction includeFilteredContinued(stream, state) {\n  if (state.isIncludeFiltered) {\n    var tok = filter(stream, state)\n    state.isIncludeFiltered = false\n    state.restOfLine = 'string'\n    return tok\n  }\n}\n\nfunction mixin(stream, state) {\n  if (stream.match(/^mixin\\b/)) {\n    state.javaScriptLine = true\n    return 'keyword'\n  }\n}\n\nfunction call(stream, state) {\n  if (stream.match(/^\\+([-\\w]+)/)) {\n    if (!stream.match(/^\\( *[-\\w]+ *=/, false)) {\n      state.javaScriptArguments = true\n      state.javaScriptArgumentsDepth = 0\n    }\n    return 'variable'\n  }\n  if (stream.match('+#{', false)) {\n    stream.next()\n    state.mixinCallAfter = true\n    return interpolation(stream, state)\n  }\n}\nfunction callArguments(stream, state) {\n  if (state.mixinCallAfter) {\n    state.mixinCallAfter = false\n    if (!stream.match(/^\\( *[-\\w]+ *=/, false)) {\n      state.javaScriptArguments = true\n      state.javaScriptArgumentsDepth = 0\n    }\n    return true\n  }\n}\n\nfunction conditional(stream, state) {\n  if (stream.match(/^(if|unless|else if|else)\\b/)) {\n    state.javaScriptLine = true\n    return 'keyword'\n  }\n}\n\nfunction each(stream, state) {\n  if (stream.match(/^(- *)?(each|for)\\b/)) {\n    state.isEach = true\n    return 'keyword'\n  }\n}\nfunction eachContinued(stream, state) {\n  if (state.isEach) {\n    if (stream.match(/^ in\\b/)) {\n      state.javaScriptLine = true\n      state.isEach = false\n      return 'keyword'\n    } else if (stream.sol() || stream.eol()) {\n      state.isEach = false\n    } else if (stream.next()) {\n      while (!stream.match(/^ in\\b/, false) && stream.next()) {}\n      return 'variable'\n    }\n  }\n}\n\nfunction whileStatement(stream, state) {\n  if (stream.match(/^while\\b/)) {\n    state.javaScriptLine = true\n    return 'keyword'\n  }\n}\n\nfunction tag(stream, state) {\n  var captures\n  if (captures = stream.match(/^(\\w(?:[-:\\w]*\\w)?)\\/?/)) {\n    state.lastTag = captures[1].toLowerCase()\n    return 'tag'\n  }\n}\n\nfunction filter(stream, state) {\n  if (stream.match(/^:([\\w\\-]+)/)) {\n    setStringMode(stream, state)\n    return 'atom'\n  }\n}\n\nfunction code(stream, state) {\n  if (stream.match(/^(!?=|-)/)) {\n    state.javaScriptLine = true\n    return 'punctuation'\n  }\n}\n\nfunction id(stream) {\n  if (stream.match(/^#([\\w-]+)/)) {\n    return 'builtin'\n  }\n}\n\nfunction className(stream) {\n  if (stream.match(/^\\.([\\w-]+)/)) {\n    return 'className'\n  }\n}\n\nfunction attrs(stream, state) {\n  if (stream.peek() == '(') {\n    stream.next()\n    state.isAttrs = true\n    state.attrsNest = []\n    state.inAttributeName = true\n    state.attrValue = ''\n    state.attributeIsType = false\n    return 'punctuation'\n  }\n}\n\nfunction attrsContinued(stream, state) {\n  if (state.isAttrs) {\n    if (ATTRS_NEST[stream.peek()]) {\n      state.attrsNest.push(ATTRS_NEST[stream.peek()])\n    }\n    if (state.attrsNest[state.attrsNest.length - 1] === stream.peek()) {\n      state.attrsNest.pop()\n    } else if (stream.eat(')')) {\n      state.isAttrs = false\n      return 'punctuation'\n    }\n    if (state.inAttributeName && stream.match(/^[^=,\\)!]+/)) {\n      if (stream.peek() === '=' || stream.peek() === '!') {\n        state.inAttributeName = false\n        state.jsState = _javascript_js__WEBPACK_IMPORTED_MODULE_0__.javascript.startState(2)\n        if (state.lastTag === 'script' && stream.current().trim().toLowerCase() === 'type') {\n          state.attributeIsType = true\n        } else {\n          state.attributeIsType = false\n        }\n      }\n      return 'attribute'\n    }\n\n    var tok = _javascript_js__WEBPACK_IMPORTED_MODULE_0__.javascript.token(stream, state.jsState)\n    if (state.attrsNest.length === 0 && (tok === 'string' || tok === 'variable' || tok === 'keyword')) {\n      try {\n        Function('', 'var x ' + state.attrValue.replace(/,\\s*$/, '').replace(/^!/, ''))\n        state.inAttributeName = true\n        state.attrValue = ''\n        stream.backUp(stream.current().length)\n        return attrsContinued(stream, state)\n      } catch (ex) {\n        //not the end of an attribute\n      }\n    }\n    state.attrValue += stream.current()\n    return tok || true\n  }\n}\n\nfunction attributesBlock(stream, state) {\n  if (stream.match(/^&attributes\\b/)) {\n    state.javaScriptArguments = true\n    state.javaScriptArgumentsDepth = 0\n    return 'keyword'\n  }\n}\n\nfunction indent(stream) {\n  if (stream.sol() && stream.eatSpace()) {\n    return 'indent'\n  }\n}\n\nfunction comment(stream, state) {\n  if (stream.match(/^ *\\/\\/(-)?([^\\n]*)/)) {\n    state.indentOf = stream.indentation()\n    state.indentToken = 'comment'\n    return 'comment'\n  }\n}\n\nfunction colon(stream) {\n  if (stream.match(/^: */)) {\n    return 'colon'\n  }\n}\n\nfunction text(stream, state) {\n  if (stream.match(/^(?:\\| ?| )([^\\n]+)/)) {\n    return 'string'\n  }\n  if (stream.match(/^(<[^\\n]*)/, false)) {\n    // html string\n    setStringMode(stream, state)\n    stream.skipToEnd()\n    return state.indentToken\n  }\n}\n\nfunction dot(stream, state) {\n  if (stream.eat('.')) {\n    setStringMode(stream, state)\n    return 'dot'\n  }\n}\n\nfunction fail(stream) {\n  stream.next()\n  return null\n}\n\n\nfunction setStringMode(stream, state) {\n  state.indentOf = stream.indentation()\n  state.indentToken = 'string'\n}\nfunction restOfLine(stream, state) {\n  if (stream.sol()) {\n    // if restOfLine was set at end of line, ignore it\n    state.restOfLine = ''\n  }\n  if (state.restOfLine) {\n    stream.skipToEnd()\n    var tok = state.restOfLine\n    state.restOfLine = ''\n    return tok\n  }\n}\n\n\nfunction startState(indentUnit) {\n  return new State(indentUnit)\n}\nfunction copyState(state) {\n  return state.copy()\n}\nfunction nextToken(stream, state) {\n  var tok = restOfLine(stream, state)\n      || interpolationContinued(stream, state)\n      || includeFilteredContinued(stream, state)\n      || eachContinued(stream, state)\n      || attrsContinued(stream, state)\n      || javaScript(stream, state)\n      || javaScriptArguments(stream, state)\n      || callArguments(stream, state)\n\n      || yieldStatement(stream)\n      || doctype(stream)\n      || interpolation(stream, state)\n      || caseStatement(stream, state)\n      || when(stream, state)\n      || defaultStatement(stream)\n      || extendsStatement(stream, state)\n      || append(stream, state)\n      || prepend(stream, state)\n      || block(stream, state)\n      || include(stream, state)\n      || includeFiltered(stream, state)\n      || mixin(stream, state)\n      || call(stream, state)\n      || conditional(stream, state)\n      || each(stream, state)\n      || whileStatement(stream, state)\n      || tag(stream, state)\n      || filter(stream, state)\n      || code(stream, state)\n      || id(stream)\n      || className(stream)\n      || attrs(stream, state)\n      || attributesBlock(stream, state)\n      || indent(stream)\n      || text(stream, state)\n      || comment(stream, state)\n      || colon(stream)\n      || dot(stream, state)\n      || fail(stream)\n\n  return tok === true ? null : tok\n}\n\nconst pug = {\n  startState: startState,\n  copyState: copyState,\n  token: nextToken\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/pug.js\n"));

/***/ })

}]);