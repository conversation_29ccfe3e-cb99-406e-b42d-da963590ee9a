import asyncio
import json
import uuid
import os
import traceback
from typing import Dict, Any, List, Optional, Union, AsyncGenerator
from datetime import datetime, timezone
from pydantic import BaseModel

from agent.prompt import get_system_prompt
from agentpress.thread_manager import ThreadManager
from agentpress.response_processor import ProcessorConfig
from utils.logger import logger
from services import redis
from services.supabase import DBConnection

# Constants
MAX_AUTO_CONTINUES = 25
MAX_XML_TOOL_CALLS = 10
REDIS_RESPONSE_LIST_TTL = 3600 * 24  # 24 hour TTL

async def run_agent(
    agent_run_id: str,
    thread_id: str,
    thread_manager: ThreadManager,
    db: DBConnection,
    user_input: str,
    model_name: str = "anthropic/claude-3-7-sonnet-latest",
    enable_thinking: bool = False,
    reasoning_effort: str = 'low',
    stream: bool = True,
    enable_context_manager: bool = False,
    autonomous_mode: bool = False,
    sandbox_mode: bool = False,
    agents: Optional[str] = None,
    collaboration_mode: str = 'false',
    retry_attempt: int = 0
) -> None:
    """Run an agent with the given parameters.
    
    This function handles the execution of an agent run, including:
    - Setting up the system prompt based on agent configuration
    - Running the thread with appropriate tools and settings
    - Streaming responses to Redis for client consumption
    - Handling errors and cleanup
    
    Args:
        agent_run_id: Unique ID for this agent run
        thread_id: ID of the conversation thread
        thread_manager: ThreadManager instance for handling the conversation
        db: Database connection
        user_input: The user's input message
        model_name: Name of the LLM model to use
        enable_thinking: Whether to enable thinking before making a decision
        reasoning_effort: The effort level for reasoning (low, medium, high)
        stream: Whether to stream the response
        enable_context_manager: Whether to enable automatic context summarization
        autonomous_mode: Whether the agent should operate autonomously
        sandbox_mode: Whether to enable sandbox mode for code execution
        agents: Comma-separated list of agent roles to include
    """
    logger.info(f"Starting agent run {agent_run_id} for thread {thread_id}")
    logger.debug(f"Parameters: model={model_name}, thinking={enable_thinking}, reasoning={reasoning_effort}, stream={stream}")
    logger.debug(f"Modes: context_manager={enable_context_manager}, autonomous={autonomous_mode}, sandbox={sandbox_mode}")
    logger.debug(f"Agents: {agents}")
    
    # Prepare Redis keys for this run
    response_list_key = f"agent:responses:{agent_run_id}"
    pubsub_channel = f"agent:pubsub:{agent_run_id}"
    status_key = f"agent:status:{agent_run_id}"
    
    # Import fallback models from api.py
    from agent.api import FALLBACK_MODELS
    
    try:
        # 1. Set initial status to 'running'
        await redis.set(status_key, json.dumps({
            "status": "running",
            "start_time": datetime.now(timezone.utc).isoformat(),
            "agent_run_id": agent_run_id,
            "thread_id": thread_id,
            "model": model_name
        }), ex=redis.REDIS_KEY_TTL)
        
        # 2. Add user message to thread
        await thread_manager.add_message(
            thread_id=thread_id,
            type="text",
            content=user_input,
            is_llm_message=False
        )
        
        # 3. Generate system prompt based on agent configuration
        agent_roles = []
        if agents:
            agent_roles = [role.strip() for role in agents.split(',')]
        
        # Convert string collaboration_mode to appropriate format
        is_collaboration_mode = collaboration_mode.lower() == 'true'
        
        system_prompt = get_system_prompt(
            agent_name="Siden AI",
            agent_roles=agent_roles,
            autonomous_mode=autonomous_mode,
            approval_mode=not autonomous_mode,
            real_time_triggers=False,
            sandbox_mode=sandbox_mode,
            collaboration_mode=collaboration_mode  # Pass collaboration mode to prompt generation
        )
        
        # 4. Configure processor for the run
        processor_config = ProcessorConfig(
            native_tool_calling=True,
            xml_tool_calling=True,
            execute_tools=True,
            max_xml_tool_calls=MAX_XML_TOOL_CALLS
        )
        
        # 5. Run the thread with the configured settings
        # Set a higher temperature for Anthropic models to encourage more varied responses
        llm_temperature = 0.7 if "claude" in model_name.lower() or "anthropic" in model_name.lower() else 0
        
        response_generator = await thread_manager.run_thread(
            thread_id=thread_id,
            system_prompt=system_prompt,
            llm_model=model_name,
            stream=stream,
            processor_config=processor_config,
            native_max_auto_continues=MAX_AUTO_CONTINUES,
            max_xml_tool_calls=MAX_XML_TOOL_CALLS,
            include_xml_examples=True,
            enable_thinking=enable_thinking,
            reasoning_effort=reasoning_effort,
            enable_context_manager=enable_context_manager  # Use correct parameter name
        )
        
        # 6. Process and store responses
        async for response in response_generator:
            # Convert any non-serializable objects to strings
            response_json = json.dumps(response)
            
            # Add to Redis list for later retrieval
            await redis.rpush(response_list_key, response_json)
            
            # Publish to Redis channel for real-time updates
            await redis.publish(pubsub_channel, response_json)
            
            # Log response type for debugging
            response_type = response.get('type', 'unknown')
            if response_type == 'error':
                logger.error(f"Error in agent run {agent_run_id}: {response.get('message', 'Unknown error')}")
            elif response_type == 'finish':
                logger.info(f"Agent run {agent_run_id} finished with reason: {response.get('finish_reason', 'unknown')}")
        
        # 7. Set final status to 'completed'
        await redis.set(status_key, json.dumps({
            "status": "completed",
            "end_time": datetime.now(timezone.utc).isoformat(),
            "agent_run_id": agent_run_id,
            "thread_id": thread_id
        }), ex=redis.REDIS_KEY_TTL)
        
        # 8. Set TTL on the response list
        await redis.expire(response_list_key, REDIS_RESPONSE_LIST_TTL)
        
        logger.info(f"Agent run {agent_run_id} completed successfully")
        
    except Exception as e:
        # Handle any errors that occur during the run
        error_message = str(e)
        stack_trace = traceback.format_exc()
        logger.error(f"Error in agent run {agent_run_id}: {error_message}\n{stack_trace}")
        
        # Check if this is an overload error and we can retry with a fallback model
        if retry_attempt < 2 and ("overloaded" in error_message.lower() or "rate limit" in error_message.lower() or "capacity" in error_message.lower()):
            # Get fallback model
            fallback_model = FALLBACK_MODELS.get(model_name, FALLBACK_MODELS.get("default"))
            logger.warning(f"Model {model_name} is overloaded. Retrying with fallback model: {fallback_model}")
            
            # Add status message about fallback
            fallback_message = {
                "type": "status",
                "message": f"Primary model {model_name} is currently overloaded. Switching to fallback model {fallback_model}.",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            await redis.rpush(response_list_key, json.dumps(fallback_message))
            await redis.publish(pubsub_channel, json.dumps(fallback_message))
            
            # Retry with fallback model
            return await run_agent(
                agent_run_id=agent_run_id,
                thread_id=thread_id,
                thread_manager=thread_manager,
                db=db,
                user_input=user_input,
                model_name=fallback_model,
                enable_thinking=enable_thinking,
                reasoning_effort=reasoning_effort,
                stream=stream,
                enable_context_manager=enable_context_manager,
                autonomous_mode=autonomous_mode,
                sandbox_mode=sandbox_mode,
                agents=agents,
                retry_attempt=retry_attempt + 1
            )
        
        # Set error status in Redis
        try:
            error_response = {
                "type": "error",
                "message": error_message,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Add error to response list
            await redis.rpush(response_list_key, json.dumps(error_response))
            
            # Publish error to channel
            await redis.publish(pubsub_channel, json.dumps(error_response))
            
            # Update status to 'error'
            await redis.set(status_key, json.dumps({
                "status": "error",
                "error": error_message,
                "end_time": datetime.now(timezone.utc).isoformat(),
                "agent_run_id": agent_run_id,
                "thread_id": thread_id
            }), ex=redis.REDIS_KEY_TTL)
            
            # Set TTL on the response list
            await redis.expire(response_list_key, REDIS_RESPONSE_LIST_TTL)
            
        except Exception as redis_error:
            # If we can't even update Redis with the error, log it
            logger.error(f"Failed to update Redis with error status: {str(redis_error)}")

async def run_agent_background(
    agent_run_id: str,
    thread_id: str,
    thread_manager: ThreadManager,
    db: DBConnection,
    user_input: str,
    model_name: str = "anthropic/claude-3-7-sonnet-latest",
    enable_thinking: bool = False,
    reasoning_effort: str = 'low',
    stream: bool = True,
    enable_context_manager: bool = False,
    autonomous_mode: bool = False,
    sandbox_mode: bool = False,
    agents: Optional[str] = None,
    retry_attempt: int = 0
):
    """Run an agent in the background.
    
    This function creates a background task to run the agent, allowing the API
    to return immediately while the agent runs asynchronously.
    
    Args are the same as run_agent.
    """
    # Create a background task for the agent run
    asyncio.create_task(
        run_agent(
            agent_run_id=agent_run_id,
            thread_id=thread_id,
            thread_manager=thread_manager,
            db=db,
            user_input=user_input,
            model_name=model_name,
            enable_thinking=enable_thinking,
            reasoning_effort=reasoning_effort,
            stream=stream,
            enable_context_manager=enable_context_manager,
            autonomous_mode=autonomous_mode,
            sandbox_mode=sandbox_mode,
            agents=agents
        )
    )
    
    logger.info(f"Started background task for agent run {agent_run_id}")
    return {"agent_run_id": agent_run_id, "status": "started"}