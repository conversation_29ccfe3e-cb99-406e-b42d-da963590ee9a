'use client';

import * as React from 'react';
import Link from 'next/link';
import { ArrowLeft, Menu, MessageSquare, Settings, Users, Wrench, Video } from 'lucide-react';
import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import { But<PERSON> } from '@/components/ui/button';
import { NavUserWithTeams } from '@/components/sidebar/nav-user-with-teams';
import { Sidenlogo } from '@/components/sidebar/sidenlogo';
import { SidebarCallInterface } from '@/components/sidebar/sidebar-call-interface';
import { useCallContext } from '@/components/project/chat/contexts/CallContext';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  SidebarTrigger,
  useSidebar,
} from '@/components/ui/sidebar';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface ProjectSidebarLeftProps {
  activeSection: 'chat' | 'team' | 'tools' | 'settings';
  onSectionChange: (section: 'chat' | 'team' | 'tools' | 'settings') => void;
}

export function ProjectSidebarLeft({
  activeSection,
  onSectionChange,
  ...props
}: ProjectSidebarLeftProps & React.ComponentProps<typeof Sidebar>) {
  const { state, setOpen, setOpenMobile } = useSidebar();
  const isMobile = useIsMobile();
  const router = useRouter();
  const params = useParams();
  const projectId = params.projectId as string;
  const callContext = useCallContext();
  const [user, setUser] = useState<{
    name: string;
    email: string;
    avatar: string;
  }>({
    name: 'Loading...',
    email: '<EMAIL>',
    avatar: '',
  });

  // Fetch user data
  useEffect(() => {
    const fetchUserData = async () => {
      const supabase = createClient();
      const { data } = await supabase.auth.getUser();

      if (data.user) {
        setUser({
          name: data.user.user_metadata?.name || data.user.email?.split('@')[0] || 'User',
          email: data.user.email || '',
          avatar: data.user.user_metadata?.avatar_url || '',
        });
      }
    };

    fetchUserData();
  }, []);

  // Handle keyboard shortcuts (CMD+B) for consistency
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.metaKey || event.ctrlKey) && event.key === 'b') {
        event.preventDefault();
        setOpen(!state.startsWith('expanded'));

        // Broadcast a custom event to notify other components
        window.dispatchEvent(
          new CustomEvent('sidebar-left-toggled', {
            detail: { expanded: !state.startsWith('expanded') },
          })
        );
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [state, setOpen]);

  // Handle section changes (just like demo)
  const handleSectionClick = (section: 'chat' | 'team' | 'tools' | 'settings') => {
    onSectionChange(section);
  };

  const navItems = [
    {
      name: 'Chat',
      icon: <MessageSquare className="h-5 w-5" />,
      value: 'chat' as const,
    },
    {
      name: 'Team Members',
      icon: <Users className="h-5 w-5" />,
      value: 'team' as const,
    },
    {
      name: 'Tools & Integrations',
      icon: <Wrench className="h-5 w-5" />,
      value: 'tools' as const,
    },
    {
      name: 'Settings',
      icon: <Settings className="h-5 w-5" />,
      value: 'settings' as const,
    },
  ];

  return (
    <Sidebar
      collapsible="icon"
      className="border-r-0 bg-sidebar backdrop-blur-sm [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']"
      {...props}
    >
      <SidebarHeader className="px-2 py-2">
        <div className="flex h-[40px] items-center px-1 relative">
          <Link href="/dashboard">
            <Sidenlogo />
          </Link>
          {state !== 'collapsed' && (
            <div className="ml-2 transition-all duration-200 ease-in-out whitespace-nowrap">
            </div>
          )}
          <div className="ml-auto flex items-center gap-2">
            {state !== 'collapsed' && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <SidebarTrigger className="h-8 w-8" />
                </TooltipTrigger>
                <TooltipContent>Toggle sidebar (CMD+B)</TooltipContent>
              </Tooltip>
            )}
            {isMobile && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    onClick={() => setOpenMobile(true)}
                    className="h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent"
                  >
                    <Menu className="h-4 w-4" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>Open menu</TooltipContent>
              </Tooltip>
            )}
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="[&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">

        <SidebarGroup>
          <div className="flex justify-between items-center">
            <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          </div>

          <SidebarMenu>
            {navItems.map((item) => (
              <SidebarMenuItem key={item.value}>
                {state === 'collapsed' ? (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <SidebarMenuButton
                        isActive={activeSection === item.value}
                        onClick={() => handleSectionClick(item.value)}
                      >
                        {item.icon}
                      </SidebarMenuButton>
                    </TooltipTrigger>
                    <TooltipContent side="right">{item.name}</TooltipContent>
                  </Tooltip>
                ) : (
                  <SidebarMenuButton
                    isActive={activeSection === item.value}
                    onClick={() => handleSectionClick(item.value)}
                  >
                    {item.icon}
                    <span>{item.name}</span>
                  </SidebarMenuButton>
                )}
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>

      {state !== 'collapsed' && (
        <div className="px-3 py-2">
        </div>
      )}

      <SidebarFooter>
        {state === 'collapsed' && (
          <div className="mt-2 flex justify-center">
            <Tooltip>
              <TooltipTrigger asChild>
                <SidebarTrigger />
              </TooltipTrigger>
              <TooltipContent>Expand sidebar (CMD+B)</TooltipContent>
            </Tooltip>
          </div>
        )}

        {/* Call Interface */}
        <SidebarCallInterface
          isCallActive={callContext.isCallActive}
          callType={callContext.callType}
          callParticipants={callContext.callParticipants}
          callStartTime={callContext.callStartTime}
          onEndCall={callContext.endCall}
        />

        <div className={cn(
          "mx-2",
          state === 'collapsed' ? "" : (callContext.isCallActive ? "bg-card" : "bg-card rounded-md mb-2")
        )}>
          <SidebarMenu>
            <SidebarMenuItem>
              {state === 'collapsed' ? (
                <div className="flex justify-center py-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="h-8 w-8 rounded-md bg-sidebar-accent flex items-center justify-center">
                        <span className="text-xs font-medium">VS</span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <p>Vercel Site - Production</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              ) : (
                <SidebarMenuButton size="lg" className="bg-transparent hover:bg-accent">
                  <div className="h-8 w-8 rounded-md bg-sidebar-accent flex items-center justify-center ">
                    <span className="text-xs font-medium">VS</span>
                  </div>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate">Vercel Site</span>
                    <span className="truncate text-xs text-muted-foreground">Production</span>
                  </div>
                </SidebarMenuButton>
              )}
            </SidebarMenuItem>
          </SidebarMenu>
        </div>

        <div className={cn(
          "mx-2",
          state === 'collapsed' ? "" : (callContext.isCallActive ? "bg-card rounded-b-md" : "")
        )}>
          <NavUserWithTeams user={user} />
        </div>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
