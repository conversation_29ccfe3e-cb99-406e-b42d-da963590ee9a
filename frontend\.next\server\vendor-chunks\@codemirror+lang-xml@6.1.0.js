"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-xml@6.1.0";
exports.ids = ["vendor-chunks/@codemirror+lang-xml@6.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-xml@6.1.0/node_modules/@codemirror/lang-xml/dist/index.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-xml@6.1.0/node_modules/@codemirror/lang-xml/dist/index.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autoCloseTags: () => (/* binding */ autoCloseTags),\n/* harmony export */   completeFromSchema: () => (/* binding */ completeFromSchema),\n/* harmony export */   xml: () => (/* binding */ xml),\n/* harmony export */   xmlLanguage: () => (/* binding */ xmlLanguage)\n/* harmony export */ });\n/* harmony import */ var _lezer_xml__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/xml */ \"(ssr)/./node_modules/.pnpm/@lezer+xml@1.0.6/node_modules/@lezer/xml/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n\n\n\n\n\nfunction tagName(doc, tag) {\n    let name = tag && tag.getChild(\"TagName\");\n    return name ? doc.sliceString(name.from, name.to) : \"\";\n}\nfunction elementName$1(doc, tree) {\n    let tag = tree && tree.firstChild;\n    return !tag || tag.name != \"OpenTag\" ? \"\" : tagName(doc, tag);\n}\nfunction attrName(doc, tag, pos) {\n    let attr = tag && tag.getChildren(\"Attribute\").find(a => a.from <= pos && a.to >= pos);\n    let name = attr && attr.getChild(\"AttributeName\");\n    return name ? doc.sliceString(name.from, name.to) : \"\";\n}\nfunction findParentElement(tree) {\n    for (let cur = tree && tree.parent; cur; cur = cur.parent)\n        if (cur.name == \"Element\")\n            return cur;\n    return null;\n}\nfunction findLocation(state, pos) {\n    var _a;\n    let at = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.syntaxTree)(state).resolveInner(pos, -1), inTag = null;\n    for (let cur = at; !inTag && cur.parent; cur = cur.parent)\n        if (cur.name == \"OpenTag\" || cur.name == \"CloseTag\" || cur.name == \"SelfClosingTag\" || cur.name == \"MismatchedCloseTag\")\n            inTag = cur;\n    if (inTag && (inTag.to > pos || inTag.lastChild.type.isError)) {\n        let elt = inTag.parent;\n        if (at.name == \"TagName\")\n            return inTag.name == \"CloseTag\" || inTag.name == \"MismatchedCloseTag\"\n                ? { type: \"closeTag\", from: at.from, context: elt }\n                : { type: \"openTag\", from: at.from, context: findParentElement(elt) };\n        if (at.name == \"AttributeName\")\n            return { type: \"attrName\", from: at.from, context: inTag };\n        if (at.name == \"AttributeValue\")\n            return { type: \"attrValue\", from: at.from, context: inTag };\n        let before = at == inTag || at.name == \"Attribute\" ? at.childBefore(pos) : at;\n        if ((before === null || before === void 0 ? void 0 : before.name) == \"StartTag\")\n            return { type: \"openTag\", from: pos, context: findParentElement(elt) };\n        if ((before === null || before === void 0 ? void 0 : before.name) == \"StartCloseTag\" && before.to <= pos)\n            return { type: \"closeTag\", from: pos, context: elt };\n        if ((before === null || before === void 0 ? void 0 : before.name) == \"Is\")\n            return { type: \"attrValue\", from: pos, context: inTag };\n        if (before)\n            return { type: \"attrName\", from: pos, context: inTag };\n        return null;\n    }\n    else if (at.name == \"StartCloseTag\") {\n        return { type: \"closeTag\", from: pos, context: at.parent };\n    }\n    while (at.parent && at.to == pos && !((_a = at.lastChild) === null || _a === void 0 ? void 0 : _a.type.isError))\n        at = at.parent;\n    if (at.name == \"Element\" || at.name == \"Text\" || at.name == \"Document\")\n        return { type: \"tag\", from: pos, context: at.name == \"Element\" ? at : findParentElement(at) };\n    return null;\n}\nclass Element {\n    constructor(spec, attrs, attrValues) {\n        this.attrs = attrs;\n        this.attrValues = attrValues;\n        this.children = [];\n        this.name = spec.name;\n        this.completion = Object.assign(Object.assign({ type: \"type\" }, spec.completion || {}), { label: this.name });\n        this.openCompletion = Object.assign(Object.assign({}, this.completion), { label: \"<\" + this.name });\n        this.closeCompletion = Object.assign(Object.assign({}, this.completion), { label: \"</\" + this.name + \">\", boost: 2 });\n        this.closeNameCompletion = Object.assign(Object.assign({}, this.completion), { label: this.name + \">\" });\n        this.text = spec.textContent ? spec.textContent.map(s => ({ label: s, type: \"text\" })) : [];\n    }\n}\nconst Identifier = /^[:\\-\\.\\w\\u00b7-\\uffff]*$/;\nfunction attrCompletion(spec) {\n    return Object.assign(Object.assign({ type: \"property\" }, spec.completion || {}), { label: spec.name });\n}\nfunction valueCompletion(spec) {\n    return typeof spec == \"string\" ? { label: `\"${spec}\"`, type: \"constant\" }\n        : /^\"/.test(spec.label) ? spec\n            : Object.assign(Object.assign({}, spec), { label: `\"${spec.label}\"` });\n}\n/**\nCreate a completion source for the given schema.\n*/\nfunction completeFromSchema(eltSpecs, attrSpecs) {\n    let allAttrs = [], globalAttrs = [];\n    let attrValues = Object.create(null);\n    for (let s of attrSpecs) {\n        let completion = attrCompletion(s);\n        allAttrs.push(completion);\n        if (s.global)\n            globalAttrs.push(completion);\n        if (s.values)\n            attrValues[s.name] = s.values.map(valueCompletion);\n    }\n    let allElements = [], topElements = [];\n    let byName = Object.create(null);\n    for (let s of eltSpecs) {\n        let attrs = globalAttrs, attrVals = attrValues;\n        if (s.attributes)\n            attrs = attrs.concat(s.attributes.map(s => {\n                if (typeof s == \"string\")\n                    return allAttrs.find(a => a.label == s) || { label: s, type: \"property\" };\n                if (s.values) {\n                    if (attrVals == attrValues)\n                        attrVals = Object.create(attrVals);\n                    attrVals[s.name] = s.values.map(valueCompletion);\n                }\n                return attrCompletion(s);\n            }));\n        let elt = new Element(s, attrs, attrVals);\n        byName[elt.name] = elt;\n        allElements.push(elt);\n        if (s.top)\n            topElements.push(elt);\n    }\n    if (!topElements.length)\n        topElements = allElements;\n    for (let i = 0; i < allElements.length; i++) {\n        let s = eltSpecs[i], elt = allElements[i];\n        if (s.children) {\n            for (let ch of s.children)\n                if (byName[ch])\n                    elt.children.push(byName[ch]);\n        }\n        else {\n            elt.children = allElements;\n        }\n    }\n    return cx => {\n        var _a;\n        let { doc } = cx.state, loc = findLocation(cx.state, cx.pos);\n        if (!loc || (loc.type == \"tag\" && !cx.explicit))\n            return null;\n        let { type, from, context } = loc;\n        if (type == \"openTag\") {\n            let children = topElements;\n            let parentName = elementName$1(doc, context);\n            if (parentName) {\n                let parent = byName[parentName];\n                children = (parent === null || parent === void 0 ? void 0 : parent.children) || allElements;\n            }\n            return {\n                from,\n                options: children.map(ch => ch.completion),\n                validFor: Identifier\n            };\n        }\n        else if (type == \"closeTag\") {\n            let parentName = elementName$1(doc, context);\n            return parentName ? {\n                from,\n                to: cx.pos + (doc.sliceString(cx.pos, cx.pos + 1) == \">\" ? 1 : 0),\n                options: [((_a = byName[parentName]) === null || _a === void 0 ? void 0 : _a.closeNameCompletion) || { label: parentName + \">\", type: \"type\" }],\n                validFor: Identifier\n            } : null;\n        }\n        else if (type == \"attrName\") {\n            let parent = byName[tagName(doc, context)];\n            return {\n                from,\n                options: (parent === null || parent === void 0 ? void 0 : parent.attrs) || globalAttrs,\n                validFor: Identifier\n            };\n        }\n        else if (type == \"attrValue\") {\n            let attr = attrName(doc, context, from);\n            if (!attr)\n                return null;\n            let parent = byName[tagName(doc, context)];\n            let values = ((parent === null || parent === void 0 ? void 0 : parent.attrValues) || attrValues)[attr];\n            if (!values || !values.length)\n                return null;\n            return {\n                from,\n                to: cx.pos + (doc.sliceString(cx.pos, cx.pos + 1) == '\"' ? 1 : 0),\n                options: values,\n                validFor: /^\"[^\"]*\"?$/\n            };\n        }\n        else if (type == \"tag\") {\n            let parentName = elementName$1(doc, context), parent = byName[parentName];\n            let closing = [], last = context && context.lastChild;\n            if (parentName && (!last || last.name != \"CloseTag\" || tagName(doc, last) != parentName))\n                closing.push(parent ? parent.closeCompletion : { label: \"</\" + parentName + \">\", type: \"type\", boost: 2 });\n            let options = closing.concat(((parent === null || parent === void 0 ? void 0 : parent.children) || (context ? allElements : topElements)).map(e => e.openCompletion));\n            if (context && (parent === null || parent === void 0 ? void 0 : parent.text.length)) {\n                let openTag = context.firstChild;\n                if (openTag.to > cx.pos - 20 && !/\\S/.test(cx.state.sliceDoc(openTag.to, cx.pos)))\n                    options = options.concat(parent.text);\n            }\n            return {\n                from,\n                options,\n                validFor: /^<\\/?[:\\-\\.\\w\\u00b7-\\uffff]*$/\n            };\n        }\n        else {\n            return null;\n        }\n    };\n}\n\n/**\nA language provider based on the [Lezer XML\nparser](https://github.com/lezer-parser/xml), extended with\nhighlighting and indentation information.\n*/\nconst xmlLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LRLanguage.define({\n    name: \"xml\",\n    parser: /*@__PURE__*/_lezer_xml__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.indentNodeProp.add({\n                Element(context) {\n                    let closed = /^\\s*<\\//.test(context.textAfter);\n                    return context.lineIndent(context.node.from) + (closed ? 0 : context.unit);\n                },\n                \"OpenTag CloseTag SelfClosingTag\"(context) {\n                    return context.column(context.node.from) + context.unit;\n                }\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldNodeProp.add({\n                Element(subtree) {\n                    let first = subtree.firstChild, last = subtree.lastChild;\n                    if (!first || first.name != \"OpenTag\")\n                        return null;\n                    return { from: first.to, to: last.name == \"CloseTag\" ? last.from : subtree.to };\n                }\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.bracketMatchingHandle.add({\n                \"OpenTag CloseTag\": node => node.getChild(\"TagName\")\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"<!--\", close: \"-->\" } },\n        indentOnInput: /^\\s*<\\/$/\n    }\n});\n/**\nXML language support. Includes schema-based autocompletion when\nconfigured.\n*/\nfunction xml(conf = {}) {\n    let support = [xmlLanguage.data.of({\n            autocomplete: completeFromSchema(conf.elements || [], conf.attributes || [])\n        })];\n    if (conf.autoCloseTags !== false)\n        support.push(autoCloseTags);\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LanguageSupport(xmlLanguage, support);\n}\nfunction elementName(doc, tree, max = doc.length) {\n    if (!tree)\n        return \"\";\n    let tag = tree.firstChild;\n    let name = tag && tag.getChild(\"TagName\");\n    return name ? doc.sliceString(name.from, Math.min(name.to, max)) : \"\";\n}\n/**\nExtension that will automatically insert close tags when a `>` or\n`/` is typed.\n*/\nconst autoCloseTags = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.inputHandler.of((view, from, to, text, insertTransaction) => {\n    if (view.composing || view.state.readOnly || from != to || (text != \">\" && text != \"/\") ||\n        !xmlLanguage.isActiveAt(view.state, from, -1))\n        return false;\n    let base = insertTransaction(), { state } = base;\n    let closeTags = state.changeByRange(range => {\n        var _a, _b, _c;\n        let { head } = range;\n        let didType = state.doc.sliceString(head - 1, head) == text;\n        let after = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.syntaxTree)(state).resolveInner(head, -1), name;\n        if (didType && text == \">\" && after.name == \"EndTag\") {\n            let tag = after.parent;\n            if (((_b = (_a = tag.parent) === null || _a === void 0 ? void 0 : _a.lastChild) === null || _b === void 0 ? void 0 : _b.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, tag.parent, head))) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `</${name}>`;\n                return { range, changes: { from: head, to, insert } };\n            }\n        }\n        else if (didType && text == \"/\" && after.name == \"StartCloseTag\") {\n            let base = after.parent;\n            if (after.from == head - 2 && ((_c = base.lastChild) === null || _c === void 0 ? void 0 : _c.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, base, head))) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `${name}>`;\n                return {\n                    range: _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorSelection.cursor(head + insert.length, -1),\n                    changes: { from: head, to, insert }\n                };\n            }\n        }\n        return { range };\n    });\n    if (closeTags.changes.empty)\n        return false;\n    view.dispatch([\n        base,\n        state.update(closeTags, {\n            userEvent: \"input.complete\",\n            scrollIntoView: true\n        })\n    ]);\n    return true;\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-xml@6.1.0/node_modules/@codemirror/lang-xml/dist/index.js\n");

/***/ })

};
;