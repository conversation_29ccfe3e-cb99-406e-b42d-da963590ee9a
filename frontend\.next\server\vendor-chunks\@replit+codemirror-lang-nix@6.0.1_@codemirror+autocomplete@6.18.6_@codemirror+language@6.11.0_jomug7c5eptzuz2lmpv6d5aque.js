"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@replit+codemirror-lang-nix@6.0.1_@codemirror+autocomplete@6.18.6_@codemirror+language@6.11.0_jomug7c5eptzuz2lmpv6d5aque";
exports.ids = ["vendor-chunks/@replit+codemirror-lang-nix@6.0.1_@codemirror+autocomplete@6.18.6_@codemirror+language@6.11.0_jomug7c5eptzuz2lmpv6d5aque"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@replit+codemirror-lang-nix@6.0.1_@codemirror+autocomplete@6.18.6_@codemirror+language@6.11.0_jomug7c5eptzuz2lmpv6d5aque/node_modules/@replit/codemirror-lang-nix/dist/index.js":
/*!************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@replit+codemirror-lang-nix@6.0.1_@codemirror+autocomplete@6.18.6_@codemirror+language@6.11.0_jomug7c5eptzuz2lmpv6d5aque/node_modules/@replit/codemirror-lang-nix/dist/index.js ***!
  \************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   nix: () => (/* binding */ nix),\n/* harmony export */   nixLanguage: () => (/* binding */ nixLanguage),\n/* harmony export */   parser: () => (/* binding */ parser)\n/* harmony export */ });\n/* harmony import */ var _lezer_lr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/lr */ \"(ssr)/./node_modules/.pnpm/@lezer+lr@1.4.2/node_modules/@lezer/lr/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js\");\n/* harmony import */ var _codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/autocomplete */ \"(ssr)/./node_modules/.pnpm/@codemirror+autocomplete@6.18.6/node_modules/@codemirror/autocomplete/dist/index.js\");\n\n\n\n\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst strContent = 63,\n  strDollarBrace = 64,\n  strEnd = 65,\n  escapeSequence = 66,\n  indStrContent = 67,\n  indStrDollarBrace = 68,\n  indStrEnd = 69,\n  indEscapeSequence = 70;\n\nconst quote = 34, backslack = 92, braceL = 123, dollar = 36, apostrophe = 39;\nconst scanString = /*@__PURE__*/new _lezer_lr__WEBPACK_IMPORTED_MODULE_0__.ExternalTokenizer((input) => {\n    for (let afterDollar = false, i = 0;; i++) {\n        let { next } = input;\n        if (next < 0) {\n            if (i > 0)\n                input.acceptToken(strContent);\n            break;\n        }\n        else if (next === quote) {\n            if (i > 0)\n                input.acceptToken(strContent);\n            else\n                input.acceptToken(strEnd, 1);\n            break;\n        }\n        else if (next === braceL && afterDollar) {\n            if (i == 1)\n                input.acceptToken(strDollarBrace, 1);\n            else\n                input.acceptToken(strContent, -1);\n            break;\n        }\n        else if (next === backslack) {\n            input.advance();\n            input.acceptToken(escapeSequence, 1);\n        }\n        afterDollar = next === dollar;\n        input.advance();\n    }\n});\nconst scanIndString = /*@__PURE__*/new _lezer_lr__WEBPACK_IMPORTED_MODULE_0__.ExternalTokenizer((input) => {\n    for (let afterDollar = false, afterApostrophe = false, i = 0;; i++) {\n        let { next } = input;\n        if (next < 0) {\n            if (i > 0)\n                input.acceptToken(indStrContent);\n            break;\n        }\n        else if (next === apostrophe && afterApostrophe) {\n            if (i > 1)\n                input.acceptToken(indStrContent, -1);\n            else\n                input.acceptToken(indStrEnd, 1);\n            break;\n        }\n        else if (next === braceL && afterDollar) {\n            if (i == 1)\n                input.acceptToken(indStrDollarBrace, 1);\n            else\n                input.acceptToken(indStrContent, -1);\n            break;\n        }\n        else if (next === backslack) {\n            input.advance();\n            input.acceptToken(indEscapeSequence, 1);\n        }\n        afterDollar = next === dollar;\n        afterApostrophe = next === apostrophe;\n        input.advance();\n    }\n});\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_Identifier = {__proto__:null,assert:22, with:26, let:30, inherit:42, in:48, if:52, then:54, else:56, builtins:70, null:214, true:216, false:216, rec:100, or:108};\nconst parser$1 = /*@__PURE__*/_lezer_lr__WEBPACK_IMPORTED_MODULE_0__.LRParser.deserialize({\n  version: 14,\n  states: \"7QO]QSOOO!sQWO'#DyO#XQ`O'#EjO&QQSO'#C`O&YQTO'#CnO'lQWO'#EWO(VQSO'#C|O(VQSO'#C|OOQO'#DQ'#DQOOQO'#DT'#DTO)dQUO'#DUO*yQSO'#DcOOQO'#Ej'#EjO,XQ`O'#EiOOQO'#Ei'#EiO-wQ`O'#EXOOQO'#Eh'#EhOOQO'#EX'#EXOOQO'#EW'#EWOOQO'#Dw'#DwO]QSO'#CfO]QSO'#ChO/dQSO'#D^O]QSO'#CuO]QSO'#D[O/xQSO'#D_QOQSOOO/}QSO'#CdO0`Q`O,5:eO3XQSO,5:eO3aQSO,5:eO4sQSO'#EOOOQO'#Cm'#CmOOQO'#Df'#DfO4}QSO,59wO]QSO'#CpO5`QSO'#ClO5eQSO'#EUO]QSO,58zO5sQSO,58zO5xQSO,58zOOQP'#EQ'#EQOOQP'#Dg'#DgO5}QTO,59YOOQO,59Y,59YO]QSO'#CoO6]QSO,59eO(VQSO,59eO(VQSO,59eO(VQSO,59eO(VQSO,59eO(VQSO,59eO(VQSO,59eO(VQSO,59eO(VQSO,59eO(VQSO,59eO(VQSO,59eO(VQSO,59eO(VQSO,59eO(VQSO,59eO6|QWO,59hO8VQSO'#D]O/xQSO'#D^OOQO,59h,59hOOQQ'#En'#EnOOQQ'#Dj'#DjO8hQUO,59pOOQO,59p,59pO]QSO'#DVOOQO'#Dk'#DkO8vQSO,59}OOQO,59},59}O8}QSO'#EiO6]QSO,59jOOQO,59i,59iO9XQSO,59QO9^QSO,59SO9cQSO,59UO]QSO,59UOOQO,59x,59xO9tQSO,59aO9yQSO,59vOOQO,59y,59yO:OQSO'#DhO;hQSO,5:jO]QSO,59OO;rQWO1G0PO;zQSO1G0POOQO1G0P1G0POOQO-E7d-E7dOOQO1G/c1G/cO<SQSO,59[O]QSO,59WOOQO'#Di'#DiO<XQSO,5:pO]QSO,5:qOOQO1G.f1G.fO<gQWO'#DyO<rQSO1G.fOOQP-E7e-E7eOOQO1G.t1G.tO<wQSO,59ZO<|Q`O'#EOOOQO1G/P1G/PO@PQWO1G/PO@WQWO1G/POAuQWO1G/POA|QWO1G/POBTQWO1G/POC^QWO1G/PODgQWO1G/POEpQWO1G/POFyQWO1G/POHSQWO1G/POI]QWO1G/POJfQWO1G/POKoQWO1G/POOQO,59w,59wOOQQ-E7h-E7hOOQO1G/[1G/[OLYQSO,59qOOQO-E7i-E7iOOQO1G/i1G/iOMfQ`O1G/UO]QSO1G.lO]QSO1G.nO]QSO1G.pOOQO1G.p1G.pO]QSO1G.{OOQO1G/b1G/bOOQO,5:S,5:SOOQO-E7f-E7fOOQO1G.j1G.jO! UQSO'#CdOOQO,5:P,5:PO! aQSO7+%kO! fQWO7+%kOOQO-E7c-E7cOOQO7+%k7+%kOOQO1G.v1G.vO! nQSO1G.rOOQO-E7g-E7gOOQO1G0[1G0[O! sQSO1G0]OOQO,5:e,5:eO]QSO7+$QOOQP1G.u1G.uO! xQ`O,5:jOOQQ1G/]1G/]O!#eQSO1G/UO)rQSO7+$pOOQO7+$W7+$WOOQO7+$Y7+$YOOQO7+$[7+$[O!#oQSO7+$gOOQO<<IV<<IVO!#tQSO<<IVP!#yQSO'#DeOOQO7+$^7+$^O!$OQSO7+%wOOQO<<Gl<<GlOOQO<<H[<<H[O]QSO<<HROOQOAN>qAN>qO!$ZQSO<<IcOOQOAN=mAN=mOOQOAN>}AN>}O!$iQSO,59jO)rQSO7+$p\",\n  stateData: \"!$x~O!jOSPOSQOS~OTQOUPOZdO]eO_fOfhOjgOs[Ou[Ov[Oz[O{[O|[O}[O!SiO!UZO!sSO#QVO#ZUO#_WO#`XO#aYO~OTkOVlOXnOeuO!sSO!usO~O!lvO!pwOT#^XU#^X_#^Xf#^Xn#^Xo#^Xs#^Xu#^Xv#^Xz#^X{#^X|#^X}#^X!S#^X!U#^X!`#^X!n#^X!s#^X!v#^X!|#^X!}#^X#O#^X#P#^X#Q#^X#R#^X#S#^X#T#^X#U#^X#V#^X#W#^X#X#^X#Y#^X#_#^X#`#^X#a#^X!q#^Xk#^Xg#^XV#^X!o#^Xl#^X~O!lvO!pxO~O!ayO!b}O!c|O!dyO~On!TOo!VO!n!OO!|!PO!}!PO#O!QO#P!RO#Q!SO#R!TO#S!UO#T!WO#U!XO#V!YO#W!ZO#X![O#Y!]O~O!`!zX!q!zXk!zXg!zXV!zX!o!zXl!zX~P&hOT[OU!_O_!`OfhOs[Ou[Ov[Oz[O{[O|[O}[O!SiO!UZO!sSO#QVO#ZUO#_WO#`XO#aYO~O!e!bO!f!fO!g!eO!h!bO~OT[OU!_O_!`OfhOs[Ou[Ov[Oz[O{[O|[O}[O!SiO!UZO!sSO#_WO#`XO#aYO~O!T!iO~P)rOT#]XU#]X_#]Xf#]Xs#]Xu#]Xv#]Xz#]X{#]X|#]X}#]X!S#]X!U#]X!s#]X#_#]X#`#]X#a#]X~O!v!kOn#]Xo#]X!`#]X!n#]X!|#]X!}#]X#O#]X#P#]X#Q#]X#R#]X#S#]X#T#]X#U#]X#V#]X#W#]X#X#]X#Y#]X!q#]Xk#]Xg#]XV#]X!o#]Xl#]X~P+QOn!{Xo!{X!`!{X!n!{X!|!{X!}!{X#O!{X#P!{X#Q!{X#R!{X#S!{X#T!{X#U!{X#V!{X#W!{X#X!{X#Y!{X!q!{Xk!{Xg!{XV!{X!o!{Xl!{X~P)rOToOU!_OeuOh!pO!sSO!usO~OU!_O~O!n!wO!v!uOVWX!oWX!w!rX~OT!PaU!Pa_!Paf!Pan!Pao!Pas!Pau!Pav!Paz!Pa{!Pa|!Pa}!Pa!S!Pa!U!Pa!`!Pa!l!ma!n!Pa!p!ma!s!Pa!v!Pa!|!Pa!}!Pa#O!Pa#P!Pa#Q!Pa#R!Pa#S!Pa#T!Pa#U!Pa#V!Pa#W!Pa#X!Pa#Y!Pa#_!Pa#`!Pa#a!Pa!q!Pak!Pag!PaV!Pa!o!Pal!Pa~OV!zO!o!xO~OV!zO~O!v!uOT!rXU!rX_!rXf!rXs!rXu!rXv!rXz!rX{!rX|!rX}!rX!S!rX!U!rX!W!rX!s!rX#_!rX#`!rX#a!rX~O!w!rX!T!rX~P3fOToOV!|OeuO!sSO!usO~O!w#OO~OT#POf#RO!sSO!usO~OU#TO~OT#UO~O!ayO!b}O!c#WO!dyO~OT#YO!sSO!usO~O!n!OO!|!PO!}!PO#O!QO#P!RO#Q!SO~Onpaopa!`pa#Rpa#Spa#Tpa#Upa#Vpa#Wpa#Xpa#Ypa!qpakpagpaVpa!opalpa~P6hOToOV#iOeuO!sSO!usO~O!e!bO!f!fO!g#kO!h!bO~O!T#nO~P)rO!v$mO!T#]X~P+QO!q#pO~O!q#qO~OToOeuOh#rO!sSO!usO~Ok#tO~Og#uO~OT#vO!sSO!usO~O!v!uOT!raU!ra_!raf!ras!rau!rav!raz!ra{!ra|!ra}!ra!S!ra!U!ra!W!ra!s!ra#_!ra#`!ra#a!ra~O!w!ra!T!ra~P:ZOT#yOX#{O~OV$OO!o#|O~OV$PO~OT#PO!q$SO!sSO!usO~OT#yOV$UOXnO~O!l$VO~OV$WO~On!rXo!rX!`!rX!n!rX!|!rX!}!rX#O!rX#P!rX#Q!rX#R!rX#S!rX#T!rX#U!rX#V!rX#W!rX#X!rX#Y!rX!q!rXk!rXg!rXV!rX!o!rXl!rX~P3fO!n!OO!|!POnmiomi!`mi#Omi#Pmi#Qmi#Rmi#Smi#Tmi#Umi#Vmi#Wmi#Xmi#Ymi!qmikmigmiVmi!omilmi~O!}mi~P>iO!}!PO~P>iO!n!OO!|!PO!}!PO#O!QOnmiomi!`mi#Qmi#Rmi#Smi#Tmi#Umi#Vmi#Wmi#Xmi#Ymi!qmikmigmiVmi!omilmi~O#Pmi~P@_O#P!RO~P@_O#R!TOnmiomi!`mi#Smi#Tmi#Umi#Vmi#Wmi#Xmi#Ymi!qmikmigmiVmi!omilmi~P6hOn!TO#R!TOomi!`mi#Smi#Tmi#Umi#Vmi#Wmi#Xmi#Ymi!qmikmigmiVmi!omilmi~P6hOn!TO#R!TO#S!UOomi!`mi#Tmi#Umi#Vmi#Wmi#Xmi#Ymi!qmikmigmiVmi!omilmi~P6hOn!TOo!VO#R!TO#S!UO!`mi#Tmi#Umi#Vmi#Wmi#Xmi#Ymi!qmikmigmiVmi!omilmi~P6hOn!TOo!VO#R!TO#S!UO#T!WO!`mi#Umi#Vmi#Wmi#Xmi#Ymi!qmikmigmiVmi!omilmi~P6hOn!TOo!VO#R!TO#S!UO#T!WO#U!XO!`mi#Vmi#Wmi#Xmi#Ymi!qmikmigmiVmi!omilmi~P6hOn!TOo!VO#R!TO#S!UO#T!WO#U!XO#V!YO!`mi#Wmi#Xmi#Ymi!qmikmigmiVmi!omilmi~P6hOn!TOo!VO#R!TO#S!UO#T!WO#U!XO#V!YO#W!ZO!`mi#Xmi#Ymi!qmikmigmiVmi!omilmi~P6hO!`mi!qmikmigmiVmi!omilmi~P&hOV$YO~OTriUri_rifrisriurivrizri{ri|ri}ri!Sri!Uri!sri#_ri#`ri#ari~O!W$[Onriori!`ri!nri!|ri!}ri#Ori#Pri#Qri#Rri#Sri#Tri#Uri#Vri#Wri#Xri#Yri!qrikrigriVri!orilri~PL_O!n!wOVWX!oWX~OV$aO~OT#yOX$bO~O!q$dO~Og$eO~On!rao!ra!`!ra!n!ra!|!ra!}!ra#O!ra#P!ra#Q!ra#R!ra#S!ra#T!ra#U!ra#V!ra#W!ra#X!ra#Y!ra!q!rak!rag!raV!ra!o!ral!ra~P:ZO!W$nO!Tri~PL_Ol$hO~OV$iO~OT#yO~OT#PO!sSO!usO~OT#PO!q$lO!sSO!usO~OToO!sSO!usO~Oz!v!v~\",\n  goto: \"2g#cPPPP#dPPP#yP#dP#dP#dP$S$Z$k%{%fPPPP&PPPP&fPP&f'[(QP({PP({({)vPPPP({)z({({PPP({P*|+S+_+e+p+z,QPPPPPPPPPPP,WP-cPPPP-{P.VPPP$S$S#d.ZPPPPPPPPPPPPPP/o0e1fPPP2cwcOdeghsv}!f!p!w#O#R#p#q#r#t$V$hSmP#TV#z!x#|$cZqPfr!_!oYtPfr!_!oQ#Z!OQ#o!kR$Z$m!p[OUVZ_deghsv}!P!Q!R!S!T!U!V!W!X!Y!Z![!]!f!h!p!w#O#R#p#q#r#t$V$[$h$n[oPfr!_!o$mW#Pu#Q$e$jS#Y!O!kR#v!uTyS{wbOdeghsv}!f!p!w#O#R#p#q#r#t$V$h!gaOUVdeghsv}!P!Q!R!S!T!U!V!W!X!Y!Z![!]!f!p!w#O#R#p#q#r#t$V$h!g`OUVdeghsv}!P!Q!R!S!T!U!V!W!X!Y!Z![!]!f!p!w#O#R#p#q#r#t$V$h!q^OUVZ_deghsv}!P!Q!R!S!T!U!V!W!X!Y!Z![!]!f!h!p!w#O#R#p#q#r#t$V$[$h$n!q[OUVZ_deghsv}!P!Q!R!S!T!U!V!W!X!Y!Z![!]!f!h!p!w#O#R#p#q#r#t$V$[$h$nT!bY!d!p[OUVZ_deghsv}!P!Q!R!S!T!U!V!W!X!Y!Z![!]!f!h!p!w#O#R#p#q#r#t$V$[$h$nS!qf!`R!tiQ!ymR#}!ySrP!_Q!ofT!{r!oQ{SR#V{S!vkoS#w!v$XR$X#YQ#QuS$R#Q$jR$j$eQ!dYR#j!dQ!hZR#m!hQjOQ!mdQ!neQ!rgQ!shQ!}sQ#SvQ#X}Q#l!fQ#s!pQ#x!wQ$Q#OQ$T#RQ$]#pQ$^#qQ$_#rQ$`#tQ$f$VR$k$hvROdeghsv}!f!p!w#O#R#p#q#r#t$V$hR#UwapPfr!O!_!k!o$mTzS{vTOdeghsv}!f!p!w#O#R#p#q#r#t$V$hQ!^UQ!aVQ#[!PQ#]!QQ#^!RQ#_!SQ#`!TQ#a!UQ#b!VQ#c!WQ#d!XQ#e!YQ#f!ZQ#g![R#h!]!g_OUVdeghsv}!P!Q!R!S!T!U!V!W!X!Y!Z![!]!f!p!w#O#R#p#q#r#t$V$h!f`OUVdeghsv}!P!Q!R!S!T!U!V!W!X!Y!Z![!]!f!p!w#O#R#p#q#r#t$V$hS!gZ!hQ!l_T$g$[$n!j]OUV_deghsv}!P!Q!R!S!T!U!V!W!X!Y!Z![!]!f!p!w#O#R#p#q#r#t$V$[$hV!jZ!h$nT!cY!d\",\n  nodeNames: \"⚠ LineComment BlockComment Program Function Identifier { } Formal Ellipses Assert assert With with Let let Bind AttrPath String Interpolation Interpolation inherit ( ) in IfExpr if then else BinaryExpr < > UnaryExpr App Select builtins Null Integer Float Boolean IndentedString Interpolation Path HPath SPath URI Parenthesized AttrSet LetAttrSet RecAttrSet rec ] [ List or\",\n  maxTerm: 110,\n  nodeProps: [\n    [\"closedBy\", 6,\"}\",22,\")\",52,\"]\"],\n    [\"openedBy\", 7,\"{\",23,\"(\",51,\"[\"]\n  ],\n  skippedNodes: [0,1,2],\n  repeatNodeCount: 7,\n  tokenData: \">P~RtXY#cYZ#c]^#cpq#cqr#trs$Rst$Wtu$ovw$zwx%Vxy%byz%gz{%l{|%q|})]}!O)b!O!P*X!P!Q.r!Q!R0l!R![2e![!]3S!]!^3X!^!_3^!_!`5_!`!a5l!a!b5y!b!c6O!c!}6T!}#O;g#P#Q;l#R#S:u#T#o6T#o#p;q#p#q;v#q#r<R#r#s<W~#hS!j~XY#cYZ#c]^#cpq#cV#yP#ZP!_!`#|U$RO#VU~$WO!s~~$]SP~OY$WZ;'S$W;'S;=`$i<%lO$W~$lP;=`<%l$W~$rP#o#p$u~$zO!u~~$}Pvw%Q~%VO#W~~%YPwx%]~%bO#a~~%gOf~~%lOg~~%qO!}~~%vW#P~{|&`}!O&}!O!P&}!P!Q'j!Q![&}!c!}&}#R#S&}#T#o&}~&eW!|~{|&}}!O&}!O!P&}!P!Q'j!Q![&}!c!}&}#R#S&}#T#o&}T'QW{|&}}!O&}!O!P&}!P!Q'j!Q![&}!c!}&}#R#S&}#T#o&}T'mV{|(S}!O(S!O!P(S!Q![(S!c!}(S#R#S(S#T#o(ST(XWzT{|(S}!O(S!O!P(S!P!Q(q!Q![(S!c!}(S#R#S(S#T#o(ST(vVzT{|(S}!O(S!O!P(S!Q![(S!c!}(S#R#S(S#T#o(S~)bO!o~~)gX#Q~{|&}}!O&}!O!P&}!P!Q'j!Q![&}!`!a*S!c!}&}#R#S&}#T#o&}~*XO#Y~~*^W!vT{|&}}!O&}!O!P*v!P!Q'j!Q![,Q!c!}&}#R#S&}#T#o&}V*yW{|&}}!O&}!O!P+c!P!Q'j!Q![&}!c!}&}#R#S&}#T#o&}V+hWXQ{|&}}!O&}!O!P&}!P!Q'j!Q![&}!c!}&}#R#S&}#T#o&}~,V[v~{|&}}!O&}!O!P&}!P!Q'j!Q![,Q!c!g&}!g!h,{!h!}&}#R#S&}#T#X&}#X#Y,{#Y#o&}~-OW{|-h}!O-h!O!P&}!P!Q'j!Q![.T!c!}&}#R#S&}#T#o&}~-kW{|&}}!O&}!O!P&}!P!Q'j!Q![.T!c!}&}#R#S&}#T#o&}~.YWv~{|&}}!O&}!O!P&}!P!Q'j!Q![.T!c!}&}#R#S&}#T#o&}~.wX#O~z{/d{|(S}!O(S!O!P(S!P!Q0g!Q![(S!c!}(S#R#S(S#T#o(S~/iTQ~Oz/dz{/x{;'S/d;'S;=`0a<%lO/d~/{TO!P/d!P!Q0[!Q;'S/d;'S;=`0a<%lO/d~0aOQ~~0dP;=`<%l/d~0lO#R~~0qWu~{|&}}!O&}!O!P1Z!P!Q'j!Q![1v!c!}&}#R#S&}#T#o&}~1^W{|&}}!O&}!O!P&}!P!Q'j!Q![,Q!c!}&}#R#S&}#T#o&}~1{Wu~{|&}}!O&}!O!P&}!P!Q'j!Q![1v!c!}&}#R#S&}#T#o&}~2jWu~{|&}}!O&}!O!P,Q!P!Q'j!Q![2e!c!}&}#R#S&}#T#o&}~3XO!l~~3^O!q~~3cWn~{|3{}!O3{!O!P3{!Q![3{!_!`5Y!c!}3{#R#S3{#T#o3{~4OX{|3{}!O3{!O!P3{!P!Q4k!Q![3{!`!a5T!c!}3{#R#S3{#T#o3{~4nV{|3{}!O3{!O!P3{!Q![3{!c!}3{#R#S3{#T#o3{~5YO|~~5_O#S~V5dP!wP!_!`5gU5lO#UU~5qPo~!_!`5t~5yO#T~~6OO!n~~6TO!p~~6YYT~wx6x{|7a}!O6T!O!P7a!P!Q'j!Q![6T![!]8P!c!}6T#R#S:u#T#o6T~6}UT~wx6x}!O6x!Q![6x!c!}6x#R#S6x#T#o6x~7dX{|7a}!O7a!O!P7a!P!Q'j!Q![7a![!]8P!c!}7a#R#S&}#T#o7a~8Sdqr9btu9buv9bvw9bwx9bz{9b{|9b|}9b}!O9b!O!P9b!P!Q9b!Q![9b![!]9b!_!`9b!a!b9b!b!c9b!c!}9b#R#S9b#T#o9b#r#s9b~9gd}~qr9btu9buv9bvw9bwx9bz{9b{|9b|}9b}!O9b!O!P9b!P!Q9b!Q![9b![!]9b!_!`9b!a!b9b!b!c9b!c!}9b#R#S9b#T#o9b#r#s9b~:zXT~wx6x{|&}}!O:u!O!P&}!P!Q'j!Q![:u!c!}:u#R#S:u#T#o:u~;lO!U~~;qO!T~~;vOU~~;yP#p#q;|~<RO#X~~<WOV~~<ZP!P!Q<^~<aV{|<v}!O<v!O!P<v!Q![<v!c!}<v#R#S<v#T#o<v~<{W{~{|<v}!O<v!O!P<v!P!Q=e!Q![<v!c!}<v#R#S<v#T#o<v~=jV{~{|<v}!O<v!O!P<v!Q![<v!c!}<v#R#S<v#T#o<v\",\n  tokenizers: [scanString, scanIndString, 0, 1, 2],\n  topRules: {\"Program\":[0,3]},\n  specialized: [{term: 5, get: value => spec_Identifier[value] || -1}],\n  tokenPrec: 2290\n});\n\nconst parser = parser$1;\nconst nixLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LRLanguage.define({\n    name: 'Nix',\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.indentNodeProp.add({\n                Parenthesized: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.delimitedIndent)({ closing: \")\" }),\n                AttrSet: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.delimitedIndent)({ closing: \"}\" }),\n                List: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.delimitedIndent)({ closing: \"]\" }),\n                Let: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.continuedIndent)({ except: /^\\s*in\\b/ }),\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldNodeProp.add({\n                AttrSet: _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldInside,\n                List: _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldInside,\n                Let(node) {\n                    let first = node.getChild(\"let\"), last = node.getChild(\"in\");\n                    if (!first || !last)\n                        return null;\n                    return { from: first.to, to: last.from };\n                },\n            }),\n            /*@__PURE__*/(0,_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.styleTags)({\n                Identifier: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.propertyName,\n                Boolean: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.bool,\n                String: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string,\n                IndentedString: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string,\n                LineComment: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.lineComment,\n                BlockComment: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.blockComment,\n                Float: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.float,\n                Integer: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.integer,\n                Null: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.null,\n                URI: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.url,\n                SPath: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.literal,\n                Path: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.literal,\n                \"( )\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.paren,\n                \"{ }\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.brace,\n                \"[ ]\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.squareBracket,\n                \"if then else\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.controlKeyword,\n                \"import with let in rec builtins inherit assert or\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.keyword,\n            }),\n        ],\n    }),\n    languageData: {\n        commentTokens: { line: \"#\", block: { open: \"/*\", close: \"*/\" } },\n        closeBrackets: { brackets: [\"(\", \"[\", \"{\", \"''\", '\"'] },\n        indentOnInput: /^\\s*(in|\\}|\\)|\\])$/,\n    },\n});\nconst snippets = [\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.snippetCompletion)(\"let ${binds} in ${expression}\", {\n        label: \"let\",\n        detail: \"Let ... in statement\",\n        type: \"keyword\",\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.snippetCompletion)(\"with ${expression}; ${expression}\", {\n        label: \"with\",\n        detail: \"With statement\",\n        type: \"keyword\",\n    }),\n];\nfunction nix() {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LanguageSupport(nixLanguage, nixLanguage.data.of({\n        autocomplete: (0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.ifNotIn)([\"LineComment\", \"BlockComment\", \"String\", \"IndentedString\"], (0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.completeFromList)(snippets)),\n    }));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@replit+codemirror-lang-nix@6.0.1_@codemirror+autocomplete@6.18.6_@codemirror+language@6.11.0_jomug7c5eptzuz2lmpv6d5aque/node_modules/@replit/codemirror-lang-nix/dist/index.js\n");

/***/ })

};
;