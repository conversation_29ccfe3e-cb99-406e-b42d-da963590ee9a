<div align="center">

# Siden AI Agent Platform

</div>

## Overview

Internal development platform for Siden's AI agent technology. This repository contains the core components needed to run and develop our agent infrastructure.

## Architecture

The platform consists of four main components:

- **Backend API**: FastAPI service for endpoints, thread management, and LLM integration
- **Frontend**: Next.js app with chat interface and dashboard
- **Agent Docker**: Isolated execution environments with browser automation and tool integration
- **Database**: Supabase for persistence, auth, and real-time features

## Setup Requirements

- Supabase project
- Redis database
- E2B sandbox
- Python 3.11
- API keys: Anthropic, OpenAI

## Quick Setup

1. **Services Setup**:
   - Supabase: Create project and get credentials
   - Redis: Use local install or Docker Compose setup
   - API Keys: Anthropic (primary), OpenAI

2. **Environment Setup**:
   ```bash
   # Backend config (.env)
   NEXT_PUBLIC_URL="http://localhost:3000"
   SUPABASE_URL=your_supabase_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   REDIS_HOST=your_redis_host
   REDIS_PORT=6379
   REDIS_PASSWORD=your_redis_password
   REDIS_SSL=True
   ANTHROPIC_API_KEY=your_anthropic_key
   OPENAI_API_KEY=your_openai_api_key
   ```

   ```bash
   # Frontend config (.env.local)
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   NEXT_PUBLIC_BACKEND_URL="http://localhost:8000/api"
   NEXT_PUBLIC_URL="http://localhost:3000"
   ```

3. **Launch Options**:
   - Standard: Run frontend with `npm run dev` and backend with `python api.py`

4. **Access**: Open `http://localhost:3000` and sign in with Supabase auth


## Tech Stack
- E2B - Agent execution
- Supabase - Database & auth
- Playwright - Browser automation
- Anthropic/OpenAI - LLM providers