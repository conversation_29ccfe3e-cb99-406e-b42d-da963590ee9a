import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import Image from 'next/image';

interface AgentRoleCardProps {
  id: string;
  name: string;
  role: string;
  description: string;
  skills: string[];
  imageSrc?: string;
  selected?: boolean;
  onClick?: () => void;
}

export function AgentRoleCard({
  id,
  name,
  role,
  description,
  skills = [],
  imageSrc,
  selected = false,
  onClick
}: AgentRoleCardProps) {
  return (
    <Card
      className={cn(
        "relative cursor-pointer transition-all duration-200 hover:border-primary/70 overflow-hidden h-full",
        selected ? "border-primary" : "border-border hover:bg-muted/10",
        "rounded-md" // Match the 6px border radius preference
      )}
      onClick={onClick}
    >
      <CardContent className="p-0 flex flex-col h-full">
        {selected && (
          <div className="absolute top-3 right-3">
            <div className="h-6 w-6 rounded-full bg-primary flex items-center justify-center text-primary-foreground">
              <Check className="h-3.5 w-3.5" />
            </div>
          </div>
        )}

        <div className="p-5 flex items-start gap-4">
          <div className="h-14 w-14 rounded-md overflow-hidden flex-shrink-0">
            {imageSrc ? (
              <Image
                src={imageSrc}
                alt={`${name} avatar`}
                width={56}
                height={56}
                className="object-cover"
              />
            ) : (
              <div className="h-full w-full bg-primary/10 flex items-center justify-center text-primary">
                {name.charAt(0)}
              </div>
            )}
          </div>

          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg text-foreground">{name}</h3>
            <p className="text-sm text-primary font-medium mb-1">{role}</p>
            <p className="text-sm text-muted-foreground leading-relaxed">{description}</p>
          </div>
        </div>

        <div className="mt-auto px-4 py-3 border-t border-border">
          <div className="flex items-center gap-2 overflow-hidden h-6">
            {skills.slice(0, 2).map((skill, index) => (
              <span
                key={index}
                className="text-xs px-2.5 py-0.5 bg-primary/10 rounded-md text-primary font-medium whitespace-nowrap flex-shrink-0"
                style={{ maxWidth: "120px", overflow: "hidden", textOverflow: "ellipsis", display: "inline-block" }}
              >
                {skill}
              </span>
            ))}
            {skills.length > 2 && (
              <span className="text-xs px-2.5 py-0.5 bg-muted rounded-full text-muted-foreground font-medium whitespace-nowrap flex-shrink-0">
                ...
              </span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
