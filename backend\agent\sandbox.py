import os
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Optional, Dict, Any
from e2b_code_interpreter import Sandbox as CodeInterpreter

router = APIRouter()

# Load E2B API key from environment
E2B_API_KEY = os.getenv("E2B_API_KEY")
if not E2B_API_KEY:
    raise ValueError("E2B_API_KEY not found in environment variables")

# Model for code execution request
class CodeExecutionRequest(BaseModel):
    code: str
    language: str
    timeout: Optional[int] = 30000  # Default 30 seconds

# Model for code execution response
class CodeExecutionResponse(BaseModel):
    output: str
    error: Optional[str] = None
    exitCode: int = 0

@router.post("/sandbox/execute", response_model=CodeExecutionResponse)
async def execute_code(request: CodeExecutionRequest):
    """
    Execute code using e2b code interpreter
    """
    try:
        # Create code interpreter session
        interpreter = CodeInterpreter(api_key=E2B_API_KEY)

        # Set timeout
        timeout_seconds = min(request.timeout / 1000, 60)  # Max 60 seconds

        # Execute code based on language
        if request.language.lower() in ["python", "py"]:
            # Execute Python code
            execution = interpreter.run_code(request.code, timeout=timeout_seconds)

            # Extract results
            output = ""
            error = None
            exit_code = 0

            # Check for errors
            if execution.error:
                error = f"{execution.error.name}: {execution.error.value}"
                exit_code = 1
            else:
                # Collect output from logs and results
                output_parts = []

                # Get stdout logs
                if execution.logs and execution.logs.stdout:
                    output_parts.extend(execution.logs.stdout)

                # Get results (for things like plots, dataframes, etc.)
                if execution.results:
                    for result in execution.results:
                        if hasattr(result, 'text') and result.text:
                            output_parts.append(result.text)

                # Use the text property as fallback
                if not output_parts and execution.text:
                    output = execution.text
                else:
                    output = "".join(output_parts)
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported language: {request.language}")

        # Return the result
        return CodeExecutionResponse(
            output=output,
            error=error,
            exitCode=exit_code
        )
    except Exception as e:
        # Handle any errors
        return CodeExecutionResponse(
            output="",
            error=str(e),
            exitCode=1
        )