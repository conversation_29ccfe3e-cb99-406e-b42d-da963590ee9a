"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-yaml@6.1.2";
exports.ids = ["vendor-chunks/@codemirror+lang-yaml@6.1.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-yaml@6.1.2/node_modules/@codemirror/lang-yaml/dist/index.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-yaml@6.1.2/node_modules/@codemirror/lang-yaml/dist/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   yaml: () => (/* binding */ yaml),\n/* harmony export */   yamlFrontmatter: () => (/* binding */ yamlFrontmatter),\n/* harmony export */   yamlLanguage: () => (/* binding */ yamlLanguage)\n/* harmony export */ });\n/* harmony import */ var _lezer_yaml__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/yaml */ \"(ssr)/./node_modules/.pnpm/@lezer+yaml@1.0.3/node_modules/@lezer/yaml/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js\");\n/* harmony import */ var _lezer_lr__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lezer/lr */ \"(ssr)/./node_modules/.pnpm/@lezer+lr@1.4.2/node_modules/@lezer/lr/dist/index.js\");\n\n\n\n\n\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = /*@__PURE__*/_lezer_lr__WEBPACK_IMPORTED_MODULE_3__.LRParser.deserialize({\n  version: 14,\n  states: \"!vOQOPOOO]OPO'#C_OhOPO'#C^OOOO'#Cc'#CcOpOPO'#CaQOOOOOO{OPOOOOOO'#Cb'#CbO!WOPO'#C`O!`OPO,58xOOOO-E6a-E6aOOOO-E6`-E6`OOOO'#C_'#C_OOOO1G.d1G.d\",\n  stateData: \"!h~OXPOYROWTP~OWVXXRXYRX~OYVOXSP~OXROYROWTX~OXROYROWTP~OYVOXSX~OX[O~OXY~\",\n  goto: \"vWPPX[beioRUOQQOR]XRXQTTOUQWQRZWSSOURYS\",\n  nodeNames: \"⚠ Document Frontmatter DashLine FrontmatterContent Body\",\n  maxTerm: 10,\n  skippedNodes: [0],\n  repeatNodeCount: 2,\n  tokenData: \"$z~RXOYnYZ!^Z]n]^!^^}n}!O!i!O;'Sn;'S;=`!c<%lOn~qXOYnYZ!^Z]n]^!^^;'Sn;'S;=`!c<%l~n~On~~!^~!cOY~~!fP;=`<%ln~!lZOYnYZ!^Z]n]^!^^}n}!O#_!O;'Sn;'S;=`!c<%l~n~On~~!^~#bZOYnYZ!^Z]n]^!^^}n}!O$T!O;'Sn;'S;=`!c<%l~n~On~~!^~$WXOYnYZ$sZ]n]^$s^;'Sn;'S;=`!c<%l~n~On~~$s~$zOX~Y~\",\n  tokenizers: [0],\n  topRules: {\"Document\":[0,1]},\n  tokenPrec: 67\n});\n\n/**\nA language provider based on the [Lezer YAML\nparser](https://github.com/lezer-parser/yaml), extended with\nhighlighting and indentation information.\n*/\nconst yamlLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_4__.LRLanguage.define({\n    name: \"yaml\",\n    parser: /*@__PURE__*/_lezer_yaml__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_4__.indentNodeProp.add({\n                Stream: cx => {\n                    for (let before = cx.node.resolve(cx.pos, -1); before && before.to >= cx.pos; before = before.parent) {\n                        if (before.name == \"BlockLiteralContent\" && before.from < before.to)\n                            return cx.baseIndentFor(before);\n                        if (before.name == \"BlockLiteral\")\n                            return cx.baseIndentFor(before) + cx.unit;\n                        if (before.name == \"BlockSequence\" || before.name == \"BlockMapping\")\n                            return cx.column(before.from, 1);\n                        if (before.name == \"QuotedLiteral\")\n                            return null;\n                        if (before.name == \"Literal\") {\n                            let col = cx.column(before.from, 1);\n                            if (col == cx.lineIndent(before.from, 1))\n                                return col; // Start on own line\n                            if (before.to > cx.pos)\n                                return null;\n                        }\n                    }\n                    return null;\n                },\n                FlowMapping: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_4__.delimitedIndent)({ closing: \"}\" }),\n                FlowSequence: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_4__.delimitedIndent)({ closing: \"]\" }),\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_4__.foldNodeProp.add({\n                \"FlowMapping FlowSequence\": _codemirror_language__WEBPACK_IMPORTED_MODULE_4__.foldInside,\n                \"Item Pair BlockLiteral\": (node, state) => ({ from: state.doc.lineAt(node.from).to, to: node.to })\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { line: \"#\" },\n        indentOnInput: /^\\s*[\\]\\}]$/,\n    }\n});\n/**\nLanguage support for YAML.\n*/\nfunction yaml() {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_4__.LanguageSupport(yamlLanguage);\n}\nconst frontmatterLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_4__.LRLanguage.define({\n    name: \"yaml-frontmatter\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [/*@__PURE__*/(0,_lezer_highlight__WEBPACK_IMPORTED_MODULE_2__.styleTags)({ DashLine: _lezer_highlight__WEBPACK_IMPORTED_MODULE_2__.tags.meta })]\n    })\n});\n/**\nReturns language support for a document parsed as `config.content`\nwith an optional YAML \"frontmatter\" delimited by lines that\ncontain three dashes.\n*/\nfunction yamlFrontmatter(config) {\n    let { language, support } = config.content instanceof _codemirror_language__WEBPACK_IMPORTED_MODULE_4__.LanguageSupport ? config.content\n        : { language: config.content, support: [] };\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_4__.LanguageSupport(frontmatterLanguage.configure({\n        wrap: (0,_lezer_common__WEBPACK_IMPORTED_MODULE_1__.parseMixed)(node => {\n            return node.name == \"FrontmatterContent\" ? { parser: yamlLanguage.parser }\n                : node.name == \"Body\" ? { parser: language.parser }\n                    : null;\n        })\n    }), support);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-yaml@6.1.2/node_modules/@codemirror/lang-yaml/dist/index.js\n");

/***/ })

};
;