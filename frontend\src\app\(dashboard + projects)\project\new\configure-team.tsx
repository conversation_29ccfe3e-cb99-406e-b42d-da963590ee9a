"use client";

import React, { useState } from 'react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Pencil, ChevronDown, ChevronUp } from 'lucide-react';
import Image from 'next/image';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { DatabaseOptions } from '@/components/project/database-options';
import { CustomIntegration, CustomIntegrationData } from '@/components/project/custom-integration';

interface ConfigureTeamProps {
  data: {
    selectedAgents: string[];
    integrations: string[];
    settings: {
      knowledgeAccess: boolean;
      memoryPersistence: boolean;
      autonomousMode: boolean;
      sandboxMode: boolean;
    };
  };
  updateData: (data: Partial<{
    integrations: string[];
    settings: {
      knowledgeAccess: boolean;
      memoryPersistence: boolean;
      autonomousMode: boolean;
      sandboxMode: boolean;
    };
  }>) => void;
}

// Agent roles data for display
const agentRoles = {
  'ceo': { name: '<PERSON><PERSON>', role: 'CEO', imageSrc: '/roles/kenard.png', skills: ['Strategic planning', 'Team leadership'] },
  'developer': { name: 'Alex', role: 'Developer', imageSrc: '/roles/alex.png', skills: ['Full-stack development', 'Code architecture'] },
  'marketing': { name: 'Chloe', role: 'Marketing Officer', imageSrc: '/roles/chloe.png', skills: ['Content creation', 'Campaign planning'] },
  'product': { name: 'Mark', role: 'Product Manager', imageSrc: '/roles/mark.png', skills: ['Feature prioritization', 'User research'] },
  'sales': { name: 'Hannah', role: 'Sales Representative', imageSrc: '/roles/hannah.png', skills: ['Lead qualification', 'Demos and pitches'] },
  'finance': { name: 'Jenna', role: 'Finance Advisor', imageSrc: '/roles/jenna.png', skills: ['Budget planning', 'Financial analysis'] },
  'designer': { name: 'Maisie', role: 'Designer', imageSrc: '/roles/maisie.png', skills: ['UI/UX design', 'Brand identity'] },
  'research': { name: 'Garek', role: 'Research Analyst', imageSrc: '/roles/garek.png', skills: ['Competitive analysis', 'Market trends'] },
};

// Define integration type
type IntegrationType = 'connect' | 'create' | 'coming-soon';

// Integrations data
const integrations = [
  // Connect Integrations
  {
    id: 'google-workspace',
    name: 'Google Workspace',
    description: 'Allow agents to access your Docs, Sheets, and Gmail',
    iconSrc: '/logoicons/google.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'google-meet',
    name: 'Google Meet',
    description: 'Allow agents to schedule and manage video meetings',
    iconSrc: '/logoicons/google-meet.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'outlook',
    name: 'Outlook',
    description: 'Allow agents to manage emails and calendar events',
    iconSrc: '/logoicons/outlook.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'zoom',
    name: 'Zoom',
    description: 'Allow agents to schedule and manage video meetings',
    iconSrc: '/logoicons/zoom.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'teams',
    name: 'Microsoft Teams',
    description: 'Allow agents to collaborate with your team',
    iconSrc: '/logoicons/microsoft-teams.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'sharepoint',
    name: 'Microsoft SharePoint',
    description: 'Allow agents to manage documents and collaborate',
    iconSrc: '/logoicons/microsoft-sharepoint.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'salesforce',
    name: 'Salesforce',
    description: 'Allow agents to manage your CRM and sales pipeline',
    iconSrc: '/logoicons/salesforce.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'gitlab',
    name: 'GitLab',
    description: 'Allow agents to access repositories and create merge requests',
    iconSrc: '/logoicons/gitlab.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'github',
    name: 'GitHub',
    description: 'Allow agents to access repositories and create pull requests',
    iconSrc: null, // We'll use a custom icon component for GitHub
    useCustomIcon: true,
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'jira',
    name: 'Jira',
    description: 'Allow agents to track issues and manage projects',
    iconSrc: '/logoicons/jira.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'notion',
    name: 'Notion',
    description: 'Allow agents to manage knowledge and documentation',
    iconSrc: '/logoicons/notion.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'slack',
    name: 'Slack',
    description: 'Allow agents to send messages to your Slack channels',
    iconSrc: '/logoicons/slack.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'discord',
    name: 'Discord',
    description: 'Allow agents to manage your community and channels',
    iconSrc: '/logoicons/discord.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'dropbox',
    name: 'Dropbox',
    description: 'Allow agents to access and manage your files',
    iconSrc: '/logoicons/dropbox.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'calendly',
    name: 'Calendly',
    description: 'Allow agents to schedule appointments and meetings',
    iconSrc: '/logoicons/calendly.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'airtable',
    name: 'Airtable',
    description: 'Allow agents to manage flexible databases',
    iconSrc: '/logoicons/airtable.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'stripe',
    name: 'Stripe',
    description: 'Allow agents to process payments and manage subscriptions',
    iconSrc: '/logoicons/stripe.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'meta',
    name: 'Meta Business Suite',
    description: 'Allow agents to manage Facebook and Instagram presence',
    iconSrc: '/logoicons/meta.svg',
    integrationType: 'connect' as IntegrationType,
  },
  {
    id: 'hubspot',
    name: 'HubSpot',
    description: 'Allow agents to manage contacts and deals',
    iconSrc: '/logoicons/hubspot.svg',
    integrationType: 'connect' as IntegrationType,
  },

  // Create Integrations
  {
    id: 'database',
    name: 'Database Access',
    description: 'Allow agents to query your databases securely',
    iconSrc: null, // Using custom database icon
    integrationType: 'create' as IntegrationType,
  },
  {
    id: 'custom',
    name: 'Custom Integration',
    description: 'Connect a custom API endpoint for your agents',
    iconSrc: null,
    integrationType: 'create' as IntegrationType,
  },
];

export function ConfigureTeam({ data, updateData }: ConfigureTeamProps) {
  const [showDatabaseOptions, setShowDatabaseOptions] = useState(false);
  const [showCustomIntegration, setShowCustomIntegration] = useState(false);
  const [recentlyConnected, setRecentlyConnected] = useState<string | null>(null);
  const [showAllConnectIntegrations, setShowAllConnectIntegrations] = useState(false);
  const [showAllCreateIntegrations, setShowAllCreateIntegrations] = useState(false);

  const toggleIntegration = (integrationId: string, enabled: boolean) => {
    const currentIntegrations = [...(data.integrations || [])];

    if (enabled) {
      // For database, show options dialog
      if (integrationId === 'database') {
        setShowDatabaseOptions(true);
        return;
      }

      // For custom integration, show custom dialog
      if (integrationId === 'custom') {
        setShowCustomIntegration(true);
        return;
      }

      // Add integration
      updateData({
        integrations: [...currentIntegrations, integrationId]
      });

      // Set recently connected integration
      setRecentlyConnected(integrationId);
    } else {
      // Remove integration
      updateData({
        integrations: currentIntegrations.filter(id => id !== integrationId)
      });
    }
  };

  const handleDatabaseSelect = (databaseId: string) => {
    const currentIntegrations = [...(data.integrations || [])];
    const integrationId = `database-${databaseId}`;
    updateData({
      integrations: [...currentIntegrations, integrationId]
    });
    setShowDatabaseOptions(false);
    setRecentlyConnected('database');
  };

  const handleCustomIntegrationSave = (integrationData: CustomIntegrationData) => {
    const currentIntegrations = [...(data.integrations || [])];
    // Store the custom integration data in a format that can be retrieved later
    // In a real app, you'd likely store this in a database
    updateData({
      integrations: [...currentIntegrations, `custom-${integrationData.name}`]
    });
    setShowCustomIntegration(false);
  };

  const updateSettings = (setting: string, value: boolean) => {
    updateData({
      settings: {
        ...data.settings,
        [setting]: value
      }
    });
  };

  return (
    <div className="space-y-6">
      {/* Database Options Dialog */}
      <Dialog open={showDatabaseOptions} onOpenChange={setShowDatabaseOptions}>
        <DialogContent className="sm:max-w-[500px]">
          <DatabaseOptions
            onSelect={handleDatabaseSelect}
            onCancel={() => setShowDatabaseOptions(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Custom Integration Dialog */}
      <Dialog open={showCustomIntegration} onOpenChange={setShowCustomIntegration}>
        <DialogContent className="sm:max-w-[600px]">
          <CustomIntegration
            onSave={handleCustomIntegrationSave}
            onCancel={() => setShowCustomIntegration(false)}
          />
        </DialogContent>
      </Dialog>


      <div className="flex flex-col md:flex-row gap-10 relative">
        <div className="absolute top-0 left-0 w-full h-[1px] bg-accent/10"></div>

        <div className="md:w-8/12 pt-6">


          {/* Connect Integrations */}
          <div className="space-y-5 mb-8">
            <div className="flex items-center justify-between">
              <h4 className="text-lg font-medium text-foreground">Connect Intergrations</h4>
              <span className="text-xs text-muted-foreground">
                {integrations.filter(i => i.integrationType === 'connect').length} Available
              </span>
            </div>
            <div className="grid gap-3">
              {integrations
                .filter(integration => integration.integrationType === 'connect')
                .slice(0, showAllConnectIntegrations ? undefined : 7)
                .map((integration) => (
                  <div key={integration.id} className="flex items-center gap-3 p-4 border rounded-[6px] hover:border-accent/50 transition-all duration-200 bg-card">
                    <div className="flex-shrink-0 h-12 w-12 bg-background rounded-md flex items-center justify-center">
                      {integration.id === 'github' ? (
                        <div className="h-12 w-12 flex items-center justify-center">
                          <svg
                            width={24}
                            height={24}
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="dark:fill-white fill-black"
                          >
                            <path
                              fillRule="evenodd"
                              clipRule="evenodd"
                              d="M12 0C5.37 0 0 5.37 0 12C0 17.31 3.435 21.795 8.205 23.385C8.805 23.49 9.03 23.13 9.03 22.815C9.03 22.53 9.015 21.585 9.015 20.58C6 21.135 5.22 19.845 4.98 19.17C4.845 18.825 4.26 17.76 3.75 17.475C3.33 17.25 2.73 16.695 3.735 16.68C4.68 16.665 5.355 17.55 5.58 17.91C6.66 19.725 8.385 19.215 9.075 18.9C9.18 18.12 9.495 17.595 9.84 17.295C7.17 16.995 4.38 15.96 4.38 11.37C4.38 10.065 4.845 8.985 5.61 8.145C5.49 7.845 5.07 6.615 5.73 4.965C5.73 4.965 6.735 4.65 9.03 6.195C9.99 5.925 11.01 5.79 12.03 5.79C13.05 5.79 14.07 5.925 15.03 6.195C17.325 4.635 18.33 4.965 18.33 4.965C18.99 6.615 18.57 7.845 18.45 8.145C19.215 8.985 19.68 10.05 19.68 11.37C19.68 15.975 16.875 16.995 14.205 17.295C14.64 17.67 15.015 18.39 15.015 19.515C15.015 21.12 15 22.41 15 22.815C15 23.13 15.225 23.505 15.825 23.385C18.2072 22.5807 20.2772 21.0497 21.7437 19.0074C23.2101 16.965 23.9993 14.5143 24 12C24 5.37 18.63 0 12 0Z"
                            />
                          </svg>
                        </div>
                      ) : (
                        <div className="h-12 w-12 flex items-center justify-center">
                          <Image
                            src={integration.iconSrc}
                            alt={`${integration.name} icon`}
                            width={28}
                            height={28}
                            className="object-contain"
                          />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h5 className="font-medium text-sm">{integration.name}</h5>
                      <p className="text-xs text-muted-foreground mb-1 truncate">{integration.description}</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="ml-auto h-8"
                      onClick={() => toggleIntegration(integration.id, !data.integrations?.includes(integration.id))}
                    >
                      Connect
                    </Button>
                  </div>
                ))}
              {integrations.filter(i => i.integrationType === 'connect').length > 6 && (
                <Button
                  variant="ghost"
                  className="w-full mt-1 border border-dashed border-accent/20 hover:border-accent/40 bg-transparent"
                  onClick={() => setShowAllConnectIntegrations(!showAllConnectIntegrations)}
                >
                  {showAllConnectIntegrations ? (
                    <span className="flex items-center gap-1">Show Less <ChevronUp className="h-4 w-4" /></span>
                  ) : (
                    <span className="flex items-center gap-1">Show {integrations.filter(i => i.integrationType === 'connect').length - 6} More <ChevronDown className="h-4 w-4" /></span>
                  )}
                </Button>
              )}
            </div>
          </div>

          {/* Create Integrations */}
          <div className="space-y-5">
            <div className="flex items-center justify-between">
              <h4 className="text-lg font-medium text-foreground">Create New Accounts</h4>
              <span className="text-xs text-muted-foreground">
                {integrations.filter(i => i.integrationType === 'create').length} Available
              </span>
            </div>
            <div className="grid gap-3">
              {integrations
                .filter(integration => integration.integrationType === 'create')
                .slice(0, showAllCreateIntegrations ? undefined : 2)
                .map((integration) => (
                  <div key={integration.id} className="flex items-center gap-3 p-4 border rounded-[6px] hover:border-accent/50 transition-all duration-200 bg-card">
                    <div className="flex-shrink-0 h-12 w-12 bg-background rounded-md flex items-center justify-center">
                      {integration.id === 'database' ? (
                        <div className="h-12 w-12 flex items-center justify-center text-accent">
                          <svg
                            width={28}
                            height={28}
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M12 8C16.4183 8 20 6.65685 20 5C20 3.34315 16.4183 2 12 2C7.58172 2 4 3.34315 4 5C4 6.65685 7.58172 8 12 8Z"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M4 5V19C4 20.6569 7.58172 22 12 22C16.4183 22 20 20.6569 20 19V5"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                      ) : integration.id === 'custom' ? (
                        <div className="h-12 w-12 flex items-center justify-center text-accent">
                          <svg
                            width={28}
                            height={28}
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <rect
                              x="2"
                              y="2"
                              width="9"
                              height="9"
                              rx="2"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <rect
                              x="13"
                              y="2"
                              width="9"
                              height="9"
                              rx="2"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <rect
                              x="13"
                              y="13"
                              width="9"
                              height="9"
                              rx="2"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                      ) : (
                        <div className="h-10 w-10 flex items-center justify-center">
                          <Image
                            src={integration.iconSrc}
                            alt={`${integration.name} icon`}
                            width={28}
                            height={28}
                            className="object-contain"
                          />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h5 className="font-medium text-sm">{integration.name}</h5>
                      <p className="text-xs text-muted-foreground mb-1 truncate">{integration.description}</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="ml-auto h-8"
                      onClick={() => toggleIntegration(integration.id, !data.integrations?.includes(integration.id))}
                    >
                      Create
                    </Button>
                  </div>
                ))}
              {integrations.filter(i => i.integrationType === 'create').length > 2 && (
                <Button
                  variant="ghost"
                  className="w-full mt-1 border border-dashed border-accent/20 hover:border-accent/40 bg-transparent"
                  onClick={() => setShowAllCreateIntegrations(!showAllCreateIntegrations)}
                >
                  {showAllCreateIntegrations ? (
                    <span className="flex items-center gap-1">Show Less <ChevronUp className="h-4 w-4" /></span>
                  ) : (
                    <span className="flex items-center gap-1">Show More <ChevronDown className="h-4 w-4" /></span>
                  )}
                </Button>
              )}
            </div>
          </div>


        </div>

        <div className="md:w-4/12 pt-6">
          <div className="mb-6 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h3 className="text-xl font-semibold">Your Team</h3>
              <span className="text-xs bg-accent/20 text-gray-200 px-2 py-1 rounded-md">
                {data.selectedAgents?.length || 0} Agents
              </span>
            </div>
            <Button variant="outline" size="sm" className="h-8">
              <Pencil className="h-3.5 w-3.5 mr-1" />
              Edit Team
            </Button>
          </div>

          <div className="grid gap-3">
            {data.selectedAgents?.map((agentId) => {
              const agent = agentRoles[agentId];
              if (!agent) return null;

              return (
                <div key={agentId} className="flex items-center gap-3 p-4 border rounded-[6px] hover:border-accent/50 transition-all duration-200 bg-card">
                  <div className="h-12 w-12 rounded-md overflow-hidden flex-shrink-0">
                    {agent.imageSrc ? (
                      <Image
                        src={agent.imageSrc}
                        alt={`${agent.name} avatar`}
                        width={48}
                        height={48}
                        className="object-cover"
                      />
                    ) : (
                      <div className="h-full w-full bg-accent/10 flex items-center justify-center text-accent">
                        {agent.name.charAt(0)}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm">{agent.name}</h4>
                    <p className="text-xs text-primary mb-1">{agent.role}</p>
                    <div className="flex items-center gap-1.5 overflow-hidden h-5">
                      {agent.skills?.slice(0, 1).map((skill: string, index: number) => (
                        <span
                          key={index}
                          className="text-xs px-2.5 py-0.5 bg-muted/80 rounded-full text-muted-foreground font-medium whitespace-nowrap flex-shrink-0"
                          style={{ maxWidth: "120px", overflow: "hidden", textOverflow: "ellipsis", display: "inline-block" }}
                        >
                          {skill}
                        </span>
                      ))}
                      {agent.skills?.length > 1 && (
                        <span className="text-xs px-2.5 py-0.5 bg-muted rounded-full text-muted-foreground font-medium whitespace-nowrap flex-shrink-0">
                          ...
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}

            {(!data.selectedAgents || data.selectedAgents.length === 0) && (
              <div className="text-center py-8 text-muted-foreground border rounded-[6px] border-dashed">
                No agents selected
              </div>
            )}
          </div>


          <div className="mt-10">
            <h3 className="text-xl font-semibold mb-6">Advanced Settings</h3>

            <div className="bg-card border rounded-[6px] p-5 mb-6">
              <h4 className="font-medium text-white mb-2">Agent Operation Modes</h4>
              <p className="text-muted-foreground mb-5">
                Configure how your AI agents operate and make decisions within your project.
              </p>

              <div className="space-y-6">
                <div className="flex items-center justify-between border-b pb-5">
                  <div>
                    <Label htmlFor="autonomous-mode" className="font-medium text-foreground">Autonomous Mode</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      Allows agents to operate independently, making decisions and taking actions without constant human oversight.
                    </p>
                    {data.settings?.autonomousMode && (
                      <div className="mt-3 bg-yellow-100/10 border border-yellow-200/20 rounded-md p-3">
                        <p className="text-sm text-yellow-300">
                          <strong>Note:</strong> In autonomous mode, agents will execute tasks without waiting for approval.
                        </p>
                      </div>
                    )}
                  </div>
                  <Switch
                    id="autonomous-mode"
                    checked={data.settings?.autonomousMode}
                    onCheckedChange={(checked) => updateSettings('autonomousMode', checked)}
                    className="data-[state=checked]:bg-accent"
                  />
                </div>

                <div className="flex items-center justify-between border-b pb-5">
                  <div>
                    <Label htmlFor="sandbox-mode" className="font-medium text-foreground">Sandbox Mode</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      Enables secure, isolated environments for agents to execute code, run builds, and perform tasks safely.
                    </p>
                  </div>
                  <Switch
                    id="sandbox-mode"
                    checked={data.settings?.sandboxMode}
                    onCheckedChange={(checked) => updateSettings('sandboxMode', checked)}
                    className="data-[state=checked]:bg-accent"
                  />
                </div>
              </div>
            </div>

            <div className="bg-card border rounded-[6px] p-5">
              <h4 className="font-medium text-white mb-2">Knowledge & Memory</h4>
              <p className="text-muted-foreground mb-5">
                Configure how your agents access and retain information.
              </p>
              <div className="space-y-6">
                <div className="flex items-center justify-between border-b pb-5">
                  <div>
                    <Label htmlFor="knowledge-access" className="font-medium text-foreground">Knowledge Access</Label>
                    <p className="text-sm text-muted-foreground mt-1">Allow agents to access internet for research and gather information</p>
                  </div>
                  <Switch
                    id="knowledge-access"
                    checked={data.settings?.knowledgeAccess}
                    onCheckedChange={(checked) => updateSettings('knowledgeAccess', checked)}
                    className="data-[state=checked]:bg-accent"
                  />
                </div>

                <div className="flex items-center justify-between pb-4">
                  <div>
                    <Label htmlFor="memory-persistence" className="font-medium text-foreground">Memory Persistence</Label>
                    <p className="text-sm text-muted-foreground mt-1">Allow agents to remember past conversations, decisions, and context across sessions</p>
                  </div>
                  <Switch
                    id="memory-persistence"
                    checked={data.settings?.memoryPersistence}
                    onCheckedChange={(checked) => updateSettings('memoryPersistence', checked)}
                    className="data-[state=checked]:bg-accent"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
