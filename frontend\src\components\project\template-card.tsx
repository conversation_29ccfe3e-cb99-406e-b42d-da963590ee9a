import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TemplateCardProps {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  selected?: boolean;
  onClick?: () => void;
}

export function TemplateCard({
  id,
  name,
  description,
  icon,
  selected = false,
  onClick
}: TemplateCardProps) {
  return (
    <Card
      className={cn(
        "relative cursor-pointer transition-all duration-200 overflow-hidden h-[100px]", // Fixed height for all cards
        selected ? "border-primary bg-accent/10" : "border-border hover:bg-muted/5",
        "rounded-md"
      )}
      onClick={onClick}
    >
      <CardContent className="p-4 flex flex-row items-center h-full">
        {selected && (
          <div className="absolute top-3 right-3">
            <div className="h-5 w-5 rounded-full bg-primary flex items-center justify-center text-primary-foreground">
              <Check className="h-3 w-3" />
            </div>
          </div>
        )}

        <div className="p-2 rounded-md bg-primary/5 mr-4">
          <div className="h-6 w-6 flex items-center justify-center">
            {icon}
          </div>
        </div>

        <div className="flex flex-col items-start text-left">
          <h3 className="font-medium text-base">{name}</h3>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
      </CardContent>
    </Card>
  );
}
