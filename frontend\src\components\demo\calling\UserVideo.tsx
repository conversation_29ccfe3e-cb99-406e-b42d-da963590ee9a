"use client";

import React, { RefObject } from 'react';
import Image from 'next/image';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MicOff } from 'lucide-react';

interface UserVideoProps {
  isVideoOn: boolean;
  isScreenSharing: boolean;
  isMuted: boolean;
  userVideoRef: RefObject<HTMLVideoElement>;
}

export function UserVideo({
  isVideoOn,
  isScreenSharing,
  isMuted,
  userVideoRef
}: UserVideoProps) {
  return (
    <div className="relative aspect-video bg-black rounded-lg overflow-hidden w-full h-full max-w-full max-h-full">
      {isScreenSharing ? (
        <div className="absolute inset-0 flex items-center justify-center bg-zinc-900">
          <Image 
            src="/demo-assets/screen-share.png" 
            alt="Screen sharing" 
            className="w-full h-full object-contain"
            width={1920}
            height={1080}
          />
          <div className="absolute bottom-3 right-3 bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-medium">
            Screen Share
          </div>
        </div>
      ) : isVideoOn ? (
        <div className="absolute inset-0 flex items-center justify-center">
          <video 
            ref={userVideoRef}
            className="w-full h-full object-cover mirror-mode"
            autoPlay
            muted
            playsInline
          />
        </div>
      ) : (
        <div className="absolute inset-0 flex items-center justify-center bg-muted">
          <Avatar className="h-24 w-24">
            <AvatarImage src="/demo-assets/user-avatar.png" alt="You" />
            <AvatarFallback>You</AvatarFallback>
          </Avatar>
        </div>
      )}
      
      {/* User label */}
      <div className="absolute bottom-3 left-3 bg-black/50 text-white px-2 py-1 rounded text-sm flex items-center gap-2">
        {isMuted && <MicOff className="h-3 w-3" />}
        You
      </div>
    </div>
  );
}