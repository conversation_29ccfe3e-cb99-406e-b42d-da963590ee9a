'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Message } from './types';

interface SuggestedResponsesProps {
  ceoMessages: Message[];
  developerMessages: Message[];
  groupMessages: Message[];
  loading: boolean;
  loadingSuggestions: boolean;
  suggestedResponses: string[];
  setInputValue: (value: string) => void;
}

export function SuggestedResponses({
  ceoMessages,
  developerMessages,
  groupMessages,
  loading,
  loadingSuggestions,
  suggestedResponses,
  setInputValue
}: SuggestedResponsesProps) {
  const hasMessages = ceoMessages.length > 0 || developerMessages.length > 0 || groupMessages.length > 0;

  if (!hasMessages) {
    return null;
  }

  return (
    <div className="flex gap-2 py-2 bg-background" style={{ paddingLeft: '64px', paddingRight: '16px' }}>
      {(loading || loadingSuggestions) ? (
        // Single clean loading state - shows from user message send until suggestions are ready
        <div className="h-8 px-4 bg-muted border border-border rounded-md flex items-center">
          <div className="w-16 h-2 bg-muted-foreground/30 rounded animate-pulse"></div>
        </div>
      ) : (
        // Actual suggestions
        suggestedResponses.map((response, index) => (
          <Button
            key={index}
            variant="outline"
            className="bg-muted text-foreground border-border hover:bg-muted/80 hover:text-foreground"
            onClick={() => {
              setInputValue(response);
            }}
          >
            {response}
          </Button>
        ))
      )}
    </div>
  );
}
