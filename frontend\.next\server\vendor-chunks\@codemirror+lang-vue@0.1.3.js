"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-vue@0.1.3";
exports.ids = ["vendor-chunks/@codemirror+lang-vue@0.1.3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-vue@0.1.3/node_modules/@codemirror/lang-vue/dist/index.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-vue@0.1.3/node_modules/@codemirror/lang-vue/dist/index.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   vue: () => (/* binding */ vue),\n/* harmony export */   vueLanguage: () => (/* binding */ vueLanguage)\n/* harmony export */ });\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _codemirror_lang_html__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/lang-html */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-html@6.4.9/node_modules/@codemirror/lang-html/dist/index.js\");\n/* harmony import */ var _codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/lang-javascript */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-javascript@6.2.3/node_modules/@codemirror/lang-javascript/dist/index.js\");\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js\");\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n/* harmony import */ var _lezer_lr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lezer/lr */ \"(ssr)/./node_modules/.pnpm/@lezer+lr@1.4.2/node_modules/@lezer/lr/dist/index.js\");\n\n\n\n\n\n\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = /*@__PURE__*/_lezer_lr__WEBPACK_IMPORTED_MODULE_2__.LRParser.deserialize({\n  version: 14,\n  states: \"%pOVOWOOObQPOOOpOSO'#C_OOOO'#Cp'#CpQVOWOOQxQPOOO!TQQOOQ!YQPOOOOOO,58y,58yO!_OSO,58yOOOO-E6n-E6nO!dQQO'#CqQ{QPOOO!iQPOOQ{QPOOO!qQPOOOOOO1G.e1G.eOOQO,59],59]OOQO-E6o-E6oO!yOpO'#CiO#RO`O'#CiQOQPOOO#ZO#tO'#CmO#fO!bO'#CmOOQO,59T,59TO#qOpO,59TO#vO`O,59TOOOO'#Cr'#CrO#{O#tO,59XOOQO,59X,59XOOOO'#Cs'#CsO$WO!bO,59XOOQO1G.o1G.oOOOO-E6p-E6pOOQO1G.s1G.sOOOO-E6q-E6q\",\n  stateData: \"$g~OjOS~OQROUROkQO~OWTOXUOZUO`VO~OSXOTWO~OXUO[]OlZO~OY^O~O[_O~OT`O~OYaO~OmcOodO~OmfOogO~O^iOnhO~O_jOphO~ObkOqkOrmO~OcnOsnOtmO~OnpO~OppO~ObkOqkOrrO~OcnOsnOtrO~OWX`~\",\n  goto: \"!^hPPPiPPPPPPPPPmPPPpPPsy!Q!WTROSRe]Re_QSORYSS[T^Rb[QlfRqlQogRso\",\n  nodeNames: \"⚠ Content Text Interpolation InterpolationContent }} Entity Attribute VueAttributeName : Identifier @ Is ScriptAttributeValue AttributeScript AttributeScript AttributeName AttributeValue Entity Entity\",\n  maxTerm: 36,\n  nodeProps: [\n    [\"isolate\", -3,3,13,17,\"\"]\n  ],\n  skippedNodes: [0],\n  repeatNodeCount: 4,\n  tokenData: \"'y~RdXY!aYZ!a]^!apq!ars!rwx!w}!O!|!O!P#t!Q![#y![!]$s!_!`%g!b!c%l!c!}#y#R#S#y#T#j#y#j#k%q#k#o#y%W;'S#y;'S;:j$m<%lO#y~!fSj~XY!aYZ!a]^!apq!a~!wOm~~!|Oo~!b#RX`!b}!O!|!Q![!|![!]!|!c!}!|#R#S!|#T#o!|%W;'S!|;'S;:j#n<%lO!|!b#qP;=`<%l!|~#yOl~%W$QXY#t`!b}!O!|!Q![#y![!]!|!c!}#y#R#S#y#T#o#y%W;'S#y;'S;:j$m<%lO#y%W$pP;=`<%l#y~$zXX~`!b}!O!|!Q![!|![!]!|!c!}!|#R#S!|#T#o!|%W;'S!|;'S;:j#n<%lO!|~%lO[~~%qOZ~%W%xXY#t`!b}!O&e!Q![#y![!]!|!c!}#y#R#S#y#T#o#y%W;'S#y;'S;:j$m<%lO#y!b&jX`!b}!O!|!Q![!|![!]!|!c!}'V#R#S!|#T#o'V%W;'S!|;'S;:j#n<%lO!|!b'^XW!b`!b}!O!|!Q![!|![!]!|!c!}'V#R#S!|#T#o'V%W;'S!|;'S;:j#n<%lO!|\",\n  tokenizers: [6, 7, /*@__PURE__*/new _lezer_lr__WEBPACK_IMPORTED_MODULE_2__.LocalTokenGroup(\"b~RP#q#rU~XP#q#r[~aOT~~\", 17, 4), /*@__PURE__*/new _lezer_lr__WEBPACK_IMPORTED_MODULE_2__.LocalTokenGroup(\"!k~RQvwX#o#p!_~^TU~Opmq!]m!^;'Sm;'S;=`!X<%lOm~pUOpmq!]m!]!^!S!^;'Sm;'S;=`!X<%lOm~!XOU~~![P;=`<%lm~!bP#o#p!e~!jOk~~\", 72, 2), /*@__PURE__*/new _lezer_lr__WEBPACK_IMPORTED_MODULE_2__.LocalTokenGroup(\"[~RPwxU~ZOp~~\", 11, 15), /*@__PURE__*/new _lezer_lr__WEBPACK_IMPORTED_MODULE_2__.LocalTokenGroup(\"[~RPrsU~ZOn~~\", 11, 14), /*@__PURE__*/new _lezer_lr__WEBPACK_IMPORTED_MODULE_2__.LocalTokenGroup(\"!e~RQvwXwx!_~^Tc~Opmq!]m!^;'Sm;'S;=`!X<%lOm~pUOpmq!]m!]!^!S!^;'Sm;'S;=`!X<%lOm~!XOc~~![P;=`<%lm~!dOt~~\", 66, 35), /*@__PURE__*/new _lezer_lr__WEBPACK_IMPORTED_MODULE_2__.LocalTokenGroup(\"!e~RQrsXvw^~^Or~~cTb~Oprq!]r!^;'Sr;'S;=`!^<%lOr~uUOprq!]r!]!^!X!^;'Sr;'S;=`!^<%lOr~!^Ob~~!aP;=`<%lr~\", 66, 33)],\n  topRules: {\"Content\":[0,1],\"Attribute\":[1,7]},\n  tokenPrec: 157\n});\n\nconst exprParser = /*@__PURE__*/_codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_3__.javascriptLanguage.parser.configure({\n    top: \"SingleExpression\"\n});\nconst baseParser = /*@__PURE__*/parser.configure({\n    props: [\n        /*@__PURE__*/(0,_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.styleTags)({\n            Text: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.content,\n            Is: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.definitionOperator,\n            AttributeName: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.attributeName,\n            VueAttributeName: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.keyword,\n            Identifier: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.variableName,\n            \"AttributeValue ScriptAttributeValue\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.attributeValue,\n            Entity: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.character,\n            \"{{ }}\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.brace,\n            \"@ :\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.punctuation\n        })\n    ]\n});\nconst exprMixed = { parser: exprParser };\nconst textParser = /*@__PURE__*/baseParser.configure({\n    wrap: /*@__PURE__*/(0,_lezer_common__WEBPACK_IMPORTED_MODULE_1__.parseMixed)((node, input) => node.name == \"InterpolationContent\" ? exprMixed : null),\n});\nconst attrParser = /*@__PURE__*/baseParser.configure({\n    wrap: /*@__PURE__*/(0,_lezer_common__WEBPACK_IMPORTED_MODULE_1__.parseMixed)((node, input) => node.name == \"AttributeScript\" ? exprMixed : null),\n    top: \"Attribute\"\n});\nconst textMixed = { parser: textParser }, attrMixed = { parser: attrParser };\nconst baseHTML = /*@__PURE__*/(0,_codemirror_lang_html__WEBPACK_IMPORTED_MODULE_4__.html)();\nfunction makeVue(base) {\n    return base.configure({\n        dialect: \"selfClosing\",\n        wrap: (0,_lezer_common__WEBPACK_IMPORTED_MODULE_1__.parseMixed)(mixVue)\n    }, \"vue\");\n}\n/**\nA language provider for Vue templates.\n*/\nconst vueLanguage = /*@__PURE__*/makeVue(baseHTML.language);\nfunction mixVue(node, input) {\n    switch (node.name) {\n        case \"Attribute\":\n            return /^(@|:|v-)/.test(input.read(node.from, node.from + 2)) ? attrMixed : null;\n        case \"Text\":\n            return textMixed;\n    }\n    return null;\n}\n/**\nVue template support.\n*/\nfunction vue(config = {}) {\n    let base = baseHTML;\n    if (config.base) {\n        if (config.base.language.name != \"html\" || !(config.base.language instanceof _codemirror_language__WEBPACK_IMPORTED_MODULE_5__.LRLanguage))\n            throw new RangeError(\"The base option must be the result of calling html(...)\");\n        base = config.base;\n    }\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_5__.LanguageSupport(base.language == baseHTML.language ? vueLanguage : makeVue(base.language), [\n        base.support,\n        base.language.data.of({ closeBrackets: { brackets: [\"{\", '\"'] } })\n    ]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-vue@0.1.3/node_modules/@codemirror/lang-vue/dist/index.js\n");

/***/ })

};
;