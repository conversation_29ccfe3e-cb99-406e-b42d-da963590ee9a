/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4";
exports.ids = ["vendor-chunks/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4"];
exports.modules = {

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/cookies.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/cookies.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyServerStorage: () => (/* binding */ applyServerStorage),\n/* harmony export */   createStorageFromOptions: () => (/* binding */ createStorageFromOptions)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"(action-browser)/./node_modules/.pnpm/cookie@1.0.2/node_modules/cookie/dist/index.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/index.js\");\n\n\nconst BASE64_PREFIX = \"base64-\";\n/**\n * Creates a storage client that handles cookies correctly for browser and\n * server clients with or without properly provided cookie methods.\n *\n * @param options The options passed to createBrowserClient or createServer client.\n *\n * @param isServerClient Whether it's called from createServerClient.\n */\nfunction createStorageFromOptions(options, isServerClient) {\n    const cookies = options.cookies ?? null;\n    const cookieEncoding = options.cookieEncoding;\n    const setItems = {};\n    const removedItems = {};\n    let getAll;\n    let setAll;\n    if (cookies) {\n        if (\"get\" in cookies) {\n            // Just get is not enough, because the client needs to see what cookies\n            // are already set and unset them if necessary. To attempt to fix this\n            // behavior for most use cases, we pass \"hints\" which is the keys of the\n            // storage items. They are then converted to their corresponding cookie\n            // chunk names and are fetched with get. Only 5 chunks are fetched, which\n            // should be enough for the majority of use cases, but does not solve\n            // those with very large sessions.\n            const getWithHints = async (keyHints) => {\n                // optimistically find the first 5 potential chunks for the specified key\n                const chunkNames = keyHints.flatMap((keyHint) => [\n                    keyHint,\n                    ...Array.from({ length: 5 }).map((_, i) => `${keyHint}.${i}`),\n                ]);\n                const chunks = [];\n                for (let i = 0; i < chunkNames.length; i += 1) {\n                    const value = await cookies.get(chunkNames[i]);\n                    if (!value && typeof value !== \"string\") {\n                        continue;\n                    }\n                    chunks.push({ name: chunkNames[i], value });\n                }\n                // TODO: detect and log stale chunks error\n                return chunks;\n            };\n            getAll = async (keyHints) => await getWithHints(keyHints);\n            if (\"set\" in cookies && \"remove\" in cookies) {\n                setAll = async (setCookies) => {\n                    for (let i = 0; i < setCookies.length; i += 1) {\n                        const { name, value, options } = setCookies[i];\n                        if (value) {\n                            await cookies.set(name, value, options);\n                        }\n                        else {\n                            await cookies.remove(name, options);\n                        }\n                    }\n                };\n            }\n            else if (isServerClient) {\n                setAll = async () => {\n                    console.warn(\"@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.\");\n                };\n            }\n            else {\n                throw new Error(\"@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)\");\n            }\n        }\n        else if (\"getAll\" in cookies) {\n            getAll = async () => await cookies.getAll();\n            if (\"setAll\" in cookies) {\n                setAll = cookies.setAll;\n            }\n            else if (isServerClient) {\n                setAll = async () => {\n                    console.warn(\"@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.\");\n                };\n            }\n            else {\n                throw new Error(\"@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)\");\n            }\n        }\n        else {\n            // neither get nor getAll is present on cookies, only will occur if pure JavaScript is used, but cookies is an object\n            throw new Error(`@supabase/ssr: ${isServerClient ? \"createServerClient\" : \"createBrowserClient\"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${(0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)() ? \" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.\" : \"\"}`);\n        }\n    }\n    else if (!isServerClient && (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)()) {\n        // The environment is browser, so use the document.cookie API to implement getAll and setAll.\n        const noHintGetAll = () => {\n            const parsed = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.parse)(document.cookie);\n            return Object.keys(parsed).map((name) => ({\n                name,\n                value: parsed[name] ?? \"\",\n            }));\n        };\n        getAll = () => noHintGetAll();\n        setAll = (setCookies) => {\n            setCookies.forEach(({ name, value, options }) => {\n                document.cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.serialize)(name, value, options);\n            });\n        };\n    }\n    else if (isServerClient) {\n        throw new Error(\"@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)\");\n    }\n    else {\n        // getting cookies when there's no window but we're in browser mode can be OK, because the developer probably is not using auth functions\n        getAll = () => {\n            return [];\n        };\n        // this is NOT OK because the developer is using auth functions that require setting some state, so that must error out\n        setAll = () => {\n            throw new Error(\"@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed\");\n        };\n    }\n    if (!isServerClient) {\n        // This is the storage client to be used in browsers. It only\n        // works on the cookies abstraction, unlike the server client\n        // which only uses cookies to read the initial state. When an\n        // item is set, cookies are both cleared and set to values so\n        // that stale chunks are not left remaining.\n        return {\n            getAll, // for type consistency\n            setAll, // for type consistency\n            setItems, // for type consistency\n            removedItems, // for type consistency\n            storage: {\n                isServer: false,\n                getItem: async (key) => {\n                    const allCookies = await getAll([key]);\n                    const chunkedCookie = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.combineChunks)(key, async (chunkName) => {\n                        const cookie = allCookies?.find(({ name }) => name === chunkName) || null;\n                        if (!cookie) {\n                            return null;\n                        }\n                        return cookie.value;\n                    });\n                    if (!chunkedCookie) {\n                        return null;\n                    }\n                    let decoded = chunkedCookie;\n                    if (chunkedCookie.startsWith(BASE64_PREFIX)) {\n                        decoded = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringFromBase64URL)(chunkedCookie.substring(BASE64_PREFIX.length));\n                    }\n                    return decoded;\n                },\n                setItem: async (key, value) => {\n                    const allCookies = await getAll([key]);\n                    const cookieNames = allCookies?.map(({ name }) => name) || [];\n                    const removeCookies = new Set(cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, key)));\n                    let encoded = value;\n                    if (cookieEncoding === \"base64url\") {\n                        encoded = BASE64_PREFIX + (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringToBase64URL)(value);\n                    }\n                    const setCookies = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.createChunks)(key, encoded);\n                    setCookies.forEach(({ name }) => {\n                        removeCookies.delete(name);\n                    });\n                    const removeCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: 0,\n                    };\n                    const setCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: _utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS.maxAge,\n                    };\n                    // the NextJS cookieStore API can get confused if the `name` from\n                    // options.cookieOptions leaks\n                    delete removeCookieOptions.name;\n                    delete setCookieOptions.name;\n                    const allToSet = [\n                        ...[...removeCookies].map((name) => ({\n                            name,\n                            value: \"\",\n                            options: removeCookieOptions,\n                        })),\n                        ...setCookies.map(({ name, value }) => ({\n                            name,\n                            value,\n                            options: setCookieOptions,\n                        })),\n                    ];\n                    if (allToSet.length > 0) {\n                        await setAll(allToSet);\n                    }\n                },\n                removeItem: async (key) => {\n                    const allCookies = await getAll([key]);\n                    const cookieNames = allCookies?.map(({ name }) => name) || [];\n                    const removeCookies = cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, key));\n                    const removeCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: 0,\n                    };\n                    // the NextJS cookieStore API can get confused if the `name` from\n                    // options.cookieOptions leaks\n                    delete removeCookieOptions.name;\n                    if (removeCookies.length > 0) {\n                        await setAll(removeCookies.map((name) => ({\n                            name,\n                            value: \"\",\n                            options: removeCookieOptions,\n                        })));\n                    }\n                },\n            },\n        };\n    }\n    // This is the server client. It only uses getAll to read the initial\n    // state. Any subsequent changes to the items is persisted in the\n    // setItems and removedItems objects. createServerClient *must* use\n    // getAll, setAll and the values in setItems and removedItems to\n    // persist the changes *at once* when appropriate (usually only when\n    // the TOKEN_REFRESHED, USER_UPDATED or SIGNED_OUT events are fired by\n    // the Supabase Auth client).\n    return {\n        getAll,\n        setAll,\n        setItems,\n        removedItems,\n        storage: {\n            // to signal to the libraries that these cookies are\n            // coming from a server environment and their value\n            // should not be trusted\n            isServer: true,\n            getItem: async (key) => {\n                if (typeof setItems[key] === \"string\") {\n                    return setItems[key];\n                }\n                if (removedItems[key]) {\n                    return null;\n                }\n                const allCookies = await getAll([key]);\n                const chunkedCookie = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.combineChunks)(key, async (chunkName) => {\n                    const cookie = allCookies?.find(({ name }) => name === chunkName) || null;\n                    if (!cookie) {\n                        return null;\n                    }\n                    return cookie.value;\n                });\n                if (!chunkedCookie) {\n                    return null;\n                }\n                let decoded = chunkedCookie;\n                if (typeof chunkedCookie === \"string\" &&\n                    chunkedCookie.startsWith(BASE64_PREFIX)) {\n                    decoded = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringFromBase64URL)(chunkedCookie.substring(BASE64_PREFIX.length));\n                }\n                return decoded;\n            },\n            setItem: async (key, value) => {\n                // We don't have an `onAuthStateChange` event that can let us know that\n                // the PKCE code verifier is being set. Therefore, if we see it being\n                // set, we need to apply the storage (call `setAll` so the cookie is\n                // set properly).\n                if (key.endsWith(\"-code-verifier\")) {\n                    await applyServerStorage({\n                        getAll,\n                        setAll,\n                        // pretend only that the code verifier was set\n                        setItems: { [key]: value },\n                        // pretend that nothing was removed\n                        removedItems: {},\n                    }, {\n                        cookieOptions: options?.cookieOptions ?? null,\n                        cookieEncoding,\n                    });\n                }\n                setItems[key] = value;\n                delete removedItems[key];\n            },\n            removeItem: async (key) => {\n                // Intentionally not applying the storage when the key is the PKCE code\n                // verifier, as usually right after it's removed other items are set,\n                // so application of the storage will be handled by the\n                // `onAuthStateChange` callback that follows removal -- usually as part\n                // of the `exchangeCodeForSession` call.\n                delete setItems[key];\n                removedItems[key] = true;\n            },\n        },\n    };\n}\n/**\n * When createServerClient needs to apply the created storage to cookies, it\n * should call this function which handles correcly setting cookies for stored\n * and removed items in the storage.\n */\nasync function applyServerStorage({ getAll, setAll, setItems, removedItems, }, options) {\n    const cookieEncoding = options.cookieEncoding;\n    const cookieOptions = options.cookieOptions ?? null;\n    const allCookies = await getAll([\n        ...(setItems ? Object.keys(setItems) : []),\n        ...(removedItems ? Object.keys(removedItems) : []),\n    ]);\n    const cookieNames = allCookies?.map(({ name }) => name) || [];\n    const removeCookies = Object.keys(removedItems).flatMap((itemName) => {\n        return cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, itemName));\n    });\n    const setCookies = Object.keys(setItems).flatMap((itemName) => {\n        const removeExistingCookiesForItem = new Set(cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, itemName)));\n        let encoded = setItems[itemName];\n        if (cookieEncoding === \"base64url\") {\n            encoded = BASE64_PREFIX + (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringToBase64URL)(encoded);\n        }\n        const chunks = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.createChunks)(itemName, encoded);\n        chunks.forEach((chunk) => {\n            removeExistingCookiesForItem.delete(chunk.name);\n        });\n        removeCookies.push(...removeExistingCookiesForItem);\n        return chunks;\n    });\n    const removeCookieOptions = {\n        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n        ...cookieOptions,\n        maxAge: 0,\n    };\n    const setCookieOptions = {\n        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n        ...cookieOptions,\n        maxAge: _utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS.maxAge,\n    };\n    // the NextJS cookieStore API can get confused if the `name` from\n    // options.cookieOptions leaks\n    delete removeCookieOptions.name;\n    delete setCookieOptions.name;\n    await setAll([\n        ...removeCookies.map((name) => ({\n            name,\n            value: \"\",\n            options: removeCookieOptions,\n        })),\n        ...setCookies.map(({ name, value }) => ({\n            name,\n            value,\n            options: setCookieOptions,\n        })),\n    ]);\n}\n//# sourceMappingURL=cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/cookies.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createBrowserClient.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createBrowserClient.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBrowserClient: () => (/* binding */ createBrowserClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/supabase-js */ \"(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.49.4/node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/version.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/index.js\");\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cookies */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/cookies.js\");\n\n\n\n\nlet cachedBrowserClient;\nfunction createBrowserClient(supabaseUrl, supabaseKey, options) {\n    // singleton client is created only if isSingleton is set to true, or if isSingleton is not defined and we detect a browser\n    const shouldUseSingleton = options?.isSingleton === true ||\n        ((!options || !(\"isSingleton\" in options)) && (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)());\n    if (shouldUseSingleton && cachedBrowserClient) {\n        return cachedBrowserClient;\n    }\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    const { storage } = (0,_cookies__WEBPACK_IMPORTED_MODULE_2__.createStorageFromOptions)({\n        ...options,\n        cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n    }, false);\n    const client = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options?.global,\n            headers: {\n                ...options?.global?.headers,\n                \"X-Client-Info\": `supabase-ssr/${_version__WEBPACK_IMPORTED_MODULE_0__.VERSION} createBrowserClient`,\n            },\n        },\n        auth: {\n            ...options?.auth,\n            ...(options?.cookieOptions?.name\n                ? { storageKey: options.cookieOptions.name }\n                : null),\n            flowType: \"pkce\",\n            autoRefreshToken: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)(),\n            detectSessionInUrl: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)(),\n            persistSession: true,\n            storage,\n        },\n    });\n    if (shouldUseSingleton) {\n        cachedBrowserClient = client;\n    }\n    return client;\n}\n//# sourceMappingURL=createBrowserClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createBrowserClient.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createServerClient.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createServerClient.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(action-browser)/./node_modules/.pnpm/@supabase+supabase-js@2.49.4/node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/version.js\");\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cookies */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/cookies.js\");\n\n\n\nfunction createServerClient(supabaseUrl, supabaseKey, options) {\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`Your project's URL and Key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    const { storage, getAll, setAll, setItems, removedItems } = (0,_cookies__WEBPACK_IMPORTED_MODULE_1__.createStorageFromOptions)({\n        ...options,\n        cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n    }, true);\n    const client = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options?.global,\n            headers: {\n                ...options?.global?.headers,\n                \"X-Client-Info\": `supabase-ssr/${_version__WEBPACK_IMPORTED_MODULE_0__.VERSION} createServerClient`,\n            },\n        },\n        auth: {\n            ...(options?.cookieOptions?.name\n                ? { storageKey: options.cookieOptions.name }\n                : null),\n            ...options?.auth,\n            flowType: \"pkce\",\n            autoRefreshToken: false,\n            detectSessionInUrl: false,\n            persistSession: true,\n            storage,\n        },\n    });\n    client.auth.onAuthStateChange(async (event) => {\n        // The SIGNED_IN event is fired very often, but we don't need to\n        // apply the storage each time it fires, only if there are changes\n        // that need to be set -- which is if setItems / removeItems have\n        // data.\n        const hasStorageChanges = Object.keys(setItems).length > 0 || Object.keys(removedItems).length > 0;\n        if (hasStorageChanges &&\n            (event === \"SIGNED_IN\" ||\n                event === \"TOKEN_REFRESHED\" ||\n                event === \"USER_UPDATED\" ||\n                event === \"PASSWORD_RECOVERY\" ||\n                event === \"SIGNED_OUT\" ||\n                event === \"MFA_CHALLENGE_VERIFIED\")) {\n            await (0,_cookies__WEBPACK_IMPORTED_MODULE_1__.applyServerStorage)({ getAll, setAll, setItems, removedItems }, {\n                cookieOptions: options?.cookieOptions ?? null,\n                cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n            });\n        }\n    });\n    return client;\n}\n//# sourceMappingURL=createServerClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createServerClient.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/index.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/index.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.MAX_CHUNK_SIZE),\n/* harmony export */   codepointToUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.codepointToUTF8),\n/* harmony export */   combineChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.combineChunks),\n/* harmony export */   createBrowserClient: () => (/* reexport safe */ _createBrowserClient__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient),\n/* harmony export */   createChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.createChunks),\n/* harmony export */   createServerClient: () => (/* reexport safe */ _createServerClient__WEBPACK_IMPORTED_MODULE_1__.createServerClient),\n/* harmony export */   deleteChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.deleteChunks),\n/* harmony export */   isBrowser: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.isBrowser),\n/* harmony export */   isChunkLike: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.isChunkLike),\n/* harmony export */   parse: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.parse),\n/* harmony export */   parseCookieHeader: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.parseCookieHeader),\n/* harmony export */   serialize: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.serialize),\n/* harmony export */   serializeCookieHeader: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.serializeCookieHeader),\n/* harmony export */   stringFromBase64URL: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringToUTF8)\n/* harmony export */ });\n/* harmony import */ var _createBrowserClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createBrowserClient */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createBrowserClient.js\");\n/* harmony import */ var _createServerClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createServerClient */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createServerClient.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/types.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_types__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_2__) if([\"default\",\"createBrowserClient\",\"createServerClient\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/index.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2Urc3NyQDAuNi4xX0BzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjQ5LjQvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zc3IvZGlzdC9tb2R1bGUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBc0M7QUFDRDtBQUNiO0FBQ0E7QUFDeEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW5kcmVcXE9uZURyaXZlXFxEZXNrdG9wXFxHaXRodWIgUmVwb3NpdG9yaWVzXFxkZW1vXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3NzckAwLjYuMV9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi40OS40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3NyXFxkaXN0XFxtb2R1bGVcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuL2NyZWF0ZUJyb3dzZXJDbGllbnRcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2NyZWF0ZVNlcnZlckNsaWVudFwiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHlwZXNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3V0aWxzXCI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/types.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/types.js ***!
  \*****************************************************************************************************************************/
/***/ (() => {

eval("//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2Urc3NyQDAuNi4xX0BzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjQ5LjQvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zc3IvZGlzdC9tb2R1bGUvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW5kcmVcXE9uZURyaXZlXFxEZXNrdG9wXFxHaXRodWIgUmVwb3NpdG9yaWVzXFxkZW1vXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3NzckAwLjYuMV9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi40OS40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3NyXFxkaXN0XFxtb2R1bGVcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/types.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/base64url.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/base64url.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codepointToUTF8: () => (/* binding */ codepointToUTF8),\n/* harmony export */   stringFromBase64URL: () => (/* binding */ stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* binding */ stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* binding */ stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* binding */ stringToUTF8)\n/* harmony export */ });\n/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_\".split(\"\");\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = \" \\t\\n\\r=\".split(\"\");\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n    const charMap = new Array(128);\n    for (let i = 0; i < charMap.length; i += 1) {\n        charMap[i] = -1;\n    }\n    for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n        charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2;\n    }\n    for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n        charMap[TO_BASE64URL[i].charCodeAt(0)] = i;\n    }\n    return charMap;\n})();\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nfunction stringToBase64URL(str) {\n    const base64 = [];\n    let queue = 0;\n    let queuedBits = 0;\n    const emitter = (byte) => {\n        queue = (queue << 8) | byte;\n        queuedBits += 8;\n        while (queuedBits >= 6) {\n            const pos = (queue >> (queuedBits - 6)) & 63;\n            base64.push(TO_BASE64URL[pos]);\n            queuedBits -= 6;\n        }\n    };\n    stringToUTF8(str, emitter);\n    if (queuedBits > 0) {\n        queue = queue << (6 - queuedBits);\n        queuedBits = 6;\n        while (queuedBits >= 6) {\n            const pos = (queue >> (queuedBits - 6)) & 63;\n            base64.push(TO_BASE64URL[pos]);\n            queuedBits -= 6;\n        }\n    }\n    return base64.join(\"\");\n}\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nfunction stringFromBase64URL(str) {\n    const conv = [];\n    const emit = (codepoint) => {\n        conv.push(String.fromCodePoint(codepoint));\n    };\n    const state = {\n        utf8seq: 0,\n        codepoint: 0,\n    };\n    let queue = 0;\n    let queuedBits = 0;\n    for (let i = 0; i < str.length; i += 1) {\n        const codepoint = str.charCodeAt(i);\n        const bits = FROM_BASE64URL[codepoint];\n        if (bits > -1) {\n            // valid Base64-URL character\n            queue = (queue << 6) | bits;\n            queuedBits += 6;\n            while (queuedBits >= 8) {\n                stringFromUTF8((queue >> (queuedBits - 8)) & 0xff, state, emit);\n                queuedBits -= 8;\n            }\n        }\n        else if (bits === -2) {\n            // ignore spaces, tabs, newlines, =\n            continue;\n        }\n        else {\n            throw new Error(`Invalid Base64-URL character \"${str.at(i)}\" at position ${i}`);\n        }\n    }\n    return conv.join(\"\");\n}\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nfunction codepointToUTF8(codepoint, emit) {\n    if (codepoint <= 0x7f) {\n        emit(codepoint);\n        return;\n    }\n    else if (codepoint <= 0x7ff) {\n        emit(0xc0 | (codepoint >> 6));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0xffff) {\n        emit(0xe0 | (codepoint >> 12));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0x10ffff) {\n        emit(0xf0 | (codepoint >> 18));\n        emit(0x80 | ((codepoint >> 12) & 0x3f));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`);\n}\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nfunction stringToUTF8(str, emit) {\n    for (let i = 0; i < str.length; i += 1) {\n        let codepoint = str.charCodeAt(i);\n        if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n            // most UTF-16 codepoints are Unicode codepoints, except values in this\n            // range where the next UTF-16 codepoint needs to be combined with the\n            // current one to get the Unicode codepoint\n            const highSurrogate = ((codepoint - 0xd800) * 0x400) & 0xffff;\n            const lowSurrogate = (str.charCodeAt(i + 1) - 0xdc00) & 0xffff;\n            codepoint = (lowSurrogate | highSurrogate) + 0x10000;\n            i += 1;\n        }\n        codepointToUTF8(codepoint, emit);\n    }\n}\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nfunction stringFromUTF8(byte, state, emit) {\n    if (state.utf8seq === 0) {\n        if (byte <= 0x7f) {\n            emit(byte);\n            return;\n        }\n        // count the number of 1 leading bits until you reach 0\n        for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n            if (((byte >> (7 - leadingBit)) & 1) === 0) {\n                state.utf8seq = leadingBit;\n                break;\n            }\n        }\n        if (state.utf8seq === 2) {\n            state.codepoint = byte & 31;\n        }\n        else if (state.utf8seq === 3) {\n            state.codepoint = byte & 15;\n        }\n        else if (state.utf8seq === 4) {\n            state.codepoint = byte & 7;\n        }\n        else {\n            throw new Error(\"Invalid UTF-8 sequence\");\n        }\n        state.utf8seq -= 1;\n    }\n    else if (state.utf8seq > 0) {\n        if (byte <= 0x7f) {\n            throw new Error(\"Invalid UTF-8 sequence\");\n        }\n        state.codepoint = (state.codepoint << 6) | (byte & 63);\n        state.utf8seq -= 1;\n        if (state.utf8seq === 0) {\n            emit(state.codepoint);\n        }\n    }\n}\n//# sourceMappingURL=base64url.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/base64url.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/chunker.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/chunker.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* binding */ MAX_CHUNK_SIZE),\n/* harmony export */   combineChunks: () => (/* binding */ combineChunks),\n/* harmony export */   createChunks: () => (/* binding */ createChunks),\n/* harmony export */   deleteChunks: () => (/* binding */ deleteChunks),\n/* harmony export */   isChunkLike: () => (/* binding */ isChunkLike)\n/* harmony export */ });\nconst MAX_CHUNK_SIZE = 3180;\nconst CHUNK_LIKE_REGEX = /^(.*)[.](0|[1-9][0-9]*)$/;\nfunction isChunkLike(cookieName, key) {\n    if (cookieName === key) {\n        return true;\n    }\n    const chunkLike = cookieName.match(CHUNK_LIKE_REGEX);\n    if (chunkLike && chunkLike[1] === key) {\n        return true;\n    }\n    return false;\n}\n/**\n * create chunks from a string and return an array of object\n */\nfunction createChunks(key, value, chunkSize) {\n    const resolvedChunkSize = chunkSize ?? MAX_CHUNK_SIZE;\n    let encodedValue = encodeURIComponent(value);\n    if (encodedValue.length <= resolvedChunkSize) {\n        return [{ name: key, value }];\n    }\n    const chunks = [];\n    while (encodedValue.length > 0) {\n        let encodedChunkHead = encodedValue.slice(0, resolvedChunkSize);\n        const lastEscapePos = encodedChunkHead.lastIndexOf(\"%\");\n        // Check if the last escaped character is truncated.\n        if (lastEscapePos > resolvedChunkSize - 3) {\n            // If so, reslice the string to exclude the whole escape sequence.\n            // We only reduce the size of the string as the chunk must\n            // be smaller than the chunk size.\n            encodedChunkHead = encodedChunkHead.slice(0, lastEscapePos);\n        }\n        let valueHead = \"\";\n        // Check if the chunk was split along a valid unicode boundary.\n        while (encodedChunkHead.length > 0) {\n            try {\n                // Try to decode the chunk back and see if it is valid.\n                // Stop when the chunk is valid.\n                valueHead = decodeURIComponent(encodedChunkHead);\n                break;\n            }\n            catch (error) {\n                if (error instanceof URIError &&\n                    encodedChunkHead.at(-3) === \"%\" &&\n                    encodedChunkHead.length > 3) {\n                    encodedChunkHead = encodedChunkHead.slice(0, encodedChunkHead.length - 3);\n                }\n                else {\n                    throw error;\n                }\n            }\n        }\n        chunks.push(valueHead);\n        encodedValue = encodedValue.slice(encodedChunkHead.length);\n    }\n    return chunks.map((value, i) => ({ name: `${key}.${i}`, value }));\n}\n// Get fully constructed chunks\nasync function combineChunks(key, retrieveChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        return value;\n    }\n    let values = [];\n    for (let i = 0;; i++) {\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        values.push(chunk);\n    }\n    if (values.length > 0) {\n        return values.join(\"\");\n    }\n    return null;\n}\nasync function deleteChunks(key, retrieveChunk, removeChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        await removeChunk(key);\n    }\n    for (let i = 0;; i++) {\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        await removeChunk(chunkName);\n    }\n}\n//# sourceMappingURL=chunker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/chunker.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/constants.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/constants.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* binding */ DEFAULT_COOKIE_OPTIONS)\n/* harmony export */ });\nconst DEFAULT_COOKIE_OPTIONS = {\n    path: \"/\",\n    sameSite: \"lax\",\n    httpOnly: false,\n    // https://developer.chrome.com/blog/cookie-max-age-expires\n    // https://httpwg.org/http-extensions/draft-ietf-httpbis-rfc6265bis.html#name-cookie-lifetime-limits\n    maxAge: 400 * 24 * 60 * 60,\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2Urc3NyQDAuNi4xX0BzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjQ5LjQvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zc3IvZGlzdC9tb2R1bGUvdXRpbHMvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW5kcmVcXE9uZURyaXZlXFxEZXNrdG9wXFxHaXRodWIgUmVwb3NpdG9yaWVzXFxkZW1vXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3NzckAwLjYuMV9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi40OS40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3NyXFxkaXN0XFxtb2R1bGVcXHV0aWxzXFxjb25zdGFudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IERFRkFVTFRfQ09PS0lFX09QVElPTlMgPSB7XG4gICAgcGF0aDogXCIvXCIsXG4gICAgc2FtZVNpdGU6IFwibGF4XCIsXG4gICAgaHR0cE9ubHk6IGZhbHNlLFxuICAgIC8vIGh0dHBzOi8vZGV2ZWxvcGVyLmNocm9tZS5jb20vYmxvZy9jb29raWUtbWF4LWFnZS1leHBpcmVzXG4gICAgLy8gaHR0cHM6Ly9odHRwd2cub3JnL2h0dHAtZXh0ZW5zaW9ucy9kcmFmdC1pZXRmLWh0dHBiaXMtcmZjNjI2NWJpcy5odG1sI25hbWUtY29va2llLWxpZmV0aW1lLWxpbWl0c1xuICAgIG1heEFnZTogNDAwICogMjQgKiA2MCAqIDYwLFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnN0YW50cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/constants.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/helpers.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/helpers.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseCookieHeader: () => (/* binding */ parseCookieHeader),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   serializeCookieHeader: () => (/* binding */ serializeCookieHeader)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"(action-browser)/./node_modules/.pnpm/cookie@1.0.2/node_modules/cookie/dist/index.js\");\n\n/**\n * @deprecated Since v0.4.0: Please use {@link parseCookieHeader}. `parse` will\n * not be available for import starting v1.0.0 of `@supabase/ssr`.\n */\nconst parse = cookie__WEBPACK_IMPORTED_MODULE_0__.parse;\n/**\n * @deprecated Since v0.4.0: Please use {@link serializeCookieHeader}.\n * `serialize` will not be available for import starting v1.0.0 of\n * `@supabase/ssr`.\n */\nconst serialize = cookie__WEBPACK_IMPORTED_MODULE_0__.serialize;\n/**\n * Parses the `Cookie` HTTP header into an array of cookie name-value objects.\n *\n * @param header The `Cookie` HTTP header. Decodes cookie names and values from\n * URI encoding first.\n */\nfunction parseCookieHeader(header) {\n    const parsed = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.parse)(header);\n    return Object.keys(parsed ?? {}).map((name) => ({\n        name,\n        value: parsed[name],\n    }));\n}\n/**\n * Converts the arguments to a valid `Set-Cookie` header. Non US-ASCII chars\n * and other forbidden cookie chars will be URI encoded.\n *\n * @param name Name of cookie.\n * @param value Value of cookie.\n */\nfunction serializeCookieHeader(name, value, options) {\n    return (0,cookie__WEBPACK_IMPORTED_MODULE_0__.serialize)(name, value, options);\n}\nfunction isBrowser() {\n    return (typeof window !== \"undefined\" && typeof window.document !== \"undefined\");\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/helpers.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/index.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/index.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.MAX_CHUNK_SIZE),\n/* harmony export */   codepointToUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.codepointToUTF8),\n/* harmony export */   combineChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.combineChunks),\n/* harmony export */   createChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.createChunks),\n/* harmony export */   deleteChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.deleteChunks),\n/* harmony export */   isBrowser: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.isBrowser),\n/* harmony export */   isChunkLike: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.isChunkLike),\n/* harmony export */   parse: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.parse),\n/* harmony export */   parseCookieHeader: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.parseCookieHeader),\n/* harmony export */   serialize: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.serialize),\n/* harmony export */   serializeCookieHeader: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.serializeCookieHeader),\n/* harmony export */   stringFromBase64URL: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringToUTF8)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/helpers.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/constants.js\");\n/* harmony import */ var _chunker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunker */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/chunker.js\");\n/* harmony import */ var _base64url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base64url */ \"(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/base64url.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2Urc3NyQDAuNi4xX0BzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjQ5LjQvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zc3IvZGlzdC9tb2R1bGUvdXRpbHMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBMEI7QUFDRTtBQUNGO0FBQ0U7QUFDNUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW5kcmVcXE9uZURyaXZlXFxEZXNrdG9wXFxHaXRodWIgUmVwb3NpdG9yaWVzXFxkZW1vXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3NzckAwLjYuMV9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi40OS40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3NyXFxkaXN0XFxtb2R1bGVcXHV0aWxzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9oZWxwZXJzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jb25zdGFudHNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2NodW5rZXJcIjtcbmV4cG9ydCAqIGZyb20gXCIuL2Jhc2U2NHVybFwiO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/version.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/version.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VERSION: () => (/* binding */ VERSION)\n/* harmony export */ });\nconst VERSION = '0.6.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2Urc3NyQDAuNi4xX0BzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjQ5LjQvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zc3IvZGlzdC9tb2R1bGUvdmVyc2lvbi5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbmRyZVxcT25lRHJpdmVcXERlc2t0b3BcXEdpdGh1YiBSZXBvc2l0b3JpZXNcXGRlbW9cXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAc3VwYWJhc2Urc3NyQDAuNi4xX0BzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjQ5LjRcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzc3JcXGRpc3RcXG1vZHVsZVxcdmVyc2lvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgVkVSU0lPTiA9ICcwLjYuMSc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD12ZXJzaW9uLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/version.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/cookies.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/cookies.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyServerStorage: () => (/* binding */ applyServerStorage),\n/* harmony export */   createStorageFromOptions: () => (/* binding */ createStorageFromOptions)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"(ssr)/./node_modules/.pnpm/cookie@1.0.2/node_modules/cookie/dist/index.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/index.js\");\n\n\nconst BASE64_PREFIX = \"base64-\";\n/**\n * Creates a storage client that handles cookies correctly for browser and\n * server clients with or without properly provided cookie methods.\n *\n * @param options The options passed to createBrowserClient or createServer client.\n *\n * @param isServerClient Whether it's called from createServerClient.\n */\nfunction createStorageFromOptions(options, isServerClient) {\n    const cookies = options.cookies ?? null;\n    const cookieEncoding = options.cookieEncoding;\n    const setItems = {};\n    const removedItems = {};\n    let getAll;\n    let setAll;\n    if (cookies) {\n        if (\"get\" in cookies) {\n            // Just get is not enough, because the client needs to see what cookies\n            // are already set and unset them if necessary. To attempt to fix this\n            // behavior for most use cases, we pass \"hints\" which is the keys of the\n            // storage items. They are then converted to their corresponding cookie\n            // chunk names and are fetched with get. Only 5 chunks are fetched, which\n            // should be enough for the majority of use cases, but does not solve\n            // those with very large sessions.\n            const getWithHints = async (keyHints) => {\n                // optimistically find the first 5 potential chunks for the specified key\n                const chunkNames = keyHints.flatMap((keyHint) => [\n                    keyHint,\n                    ...Array.from({ length: 5 }).map((_, i) => `${keyHint}.${i}`),\n                ]);\n                const chunks = [];\n                for (let i = 0; i < chunkNames.length; i += 1) {\n                    const value = await cookies.get(chunkNames[i]);\n                    if (!value && typeof value !== \"string\") {\n                        continue;\n                    }\n                    chunks.push({ name: chunkNames[i], value });\n                }\n                // TODO: detect and log stale chunks error\n                return chunks;\n            };\n            getAll = async (keyHints) => await getWithHints(keyHints);\n            if (\"set\" in cookies && \"remove\" in cookies) {\n                setAll = async (setCookies) => {\n                    for (let i = 0; i < setCookies.length; i += 1) {\n                        const { name, value, options } = setCookies[i];\n                        if (value) {\n                            await cookies.set(name, value, options);\n                        }\n                        else {\n                            await cookies.remove(name, options);\n                        }\n                    }\n                };\n            }\n            else if (isServerClient) {\n                setAll = async () => {\n                    console.warn(\"@supabase/ssr: createServerClient was configured without set and remove cookie methods, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness. Consider switching to the getAll and setAll cookie methods instead of get, set and remove which are deprecated and can be difficult to use correctly.\");\n                };\n            }\n            else {\n                throw new Error(\"@supabase/ssr: createBrowserClient requires configuring a getAll and setAll cookie method (deprecated: alternatively both get, set and remove can be used)\");\n            }\n        }\n        else if (\"getAll\" in cookies) {\n            getAll = async () => await cookies.getAll();\n            if (\"setAll\" in cookies) {\n                setAll = cookies.setAll;\n            }\n            else if (isServerClient) {\n                setAll = async () => {\n                    console.warn(\"@supabase/ssr: createServerClient was configured without the setAll cookie method, but the client needs to set cookies. This can lead to issues such as random logouts, early session termination or increased token refresh requests. If in NextJS, check your middleware.ts file, route handlers and server actions for correctness.\");\n                };\n            }\n            else {\n                throw new Error(\"@supabase/ssr: createBrowserClient requires configuring both getAll and setAll cookie methods (deprecated: alternatively both get, set and remove can be used)\");\n            }\n        }\n        else {\n            // neither get nor getAll is present on cookies, only will occur if pure JavaScript is used, but cookies is an object\n            throw new Error(`@supabase/ssr: ${isServerClient ? \"createServerClient\" : \"createBrowserClient\"} requires configuring getAll and setAll cookie methods (deprecated: alternatively use get, set and remove).${(0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)() ? \" As this is called in a browser runtime, consider removing the cookies option object to use the document.cookie API automatically.\" : \"\"}`);\n        }\n    }\n    else if (!isServerClient && (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)()) {\n        // The environment is browser, so use the document.cookie API to implement getAll and setAll.\n        const noHintGetAll = () => {\n            const parsed = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.parse)(document.cookie);\n            return Object.keys(parsed).map((name) => ({\n                name,\n                value: parsed[name] ?? \"\",\n            }));\n        };\n        getAll = () => noHintGetAll();\n        setAll = (setCookies) => {\n            setCookies.forEach(({ name, value, options }) => {\n                document.cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.serialize)(name, value, options);\n            });\n        };\n    }\n    else if (isServerClient) {\n        throw new Error(\"@supabase/ssr: createServerClient must be initialized with cookie options that specify getAll and setAll functions (deprecated, not recommended: alternatively use get, set and remove)\");\n    }\n    else {\n        // getting cookies when there's no window but we're in browser mode can be OK, because the developer probably is not using auth functions\n        getAll = () => {\n            return [];\n        };\n        // this is NOT OK because the developer is using auth functions that require setting some state, so that must error out\n        setAll = () => {\n            throw new Error(\"@supabase/ssr: createBrowserClient in non-browser runtimes (including Next.js pre-rendering mode) was not initialized cookie options that specify getAll and setAll functions (deprecated: alternatively use get, set and remove), but they were needed\");\n        };\n    }\n    if (!isServerClient) {\n        // This is the storage client to be used in browsers. It only\n        // works on the cookies abstraction, unlike the server client\n        // which only uses cookies to read the initial state. When an\n        // item is set, cookies are both cleared and set to values so\n        // that stale chunks are not left remaining.\n        return {\n            getAll, // for type consistency\n            setAll, // for type consistency\n            setItems, // for type consistency\n            removedItems, // for type consistency\n            storage: {\n                isServer: false,\n                getItem: async (key) => {\n                    const allCookies = await getAll([key]);\n                    const chunkedCookie = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.combineChunks)(key, async (chunkName) => {\n                        const cookie = allCookies?.find(({ name }) => name === chunkName) || null;\n                        if (!cookie) {\n                            return null;\n                        }\n                        return cookie.value;\n                    });\n                    if (!chunkedCookie) {\n                        return null;\n                    }\n                    let decoded = chunkedCookie;\n                    if (chunkedCookie.startsWith(BASE64_PREFIX)) {\n                        decoded = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringFromBase64URL)(chunkedCookie.substring(BASE64_PREFIX.length));\n                    }\n                    return decoded;\n                },\n                setItem: async (key, value) => {\n                    const allCookies = await getAll([key]);\n                    const cookieNames = allCookies?.map(({ name }) => name) || [];\n                    const removeCookies = new Set(cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, key)));\n                    let encoded = value;\n                    if (cookieEncoding === \"base64url\") {\n                        encoded = BASE64_PREFIX + (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringToBase64URL)(value);\n                    }\n                    const setCookies = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.createChunks)(key, encoded);\n                    setCookies.forEach(({ name }) => {\n                        removeCookies.delete(name);\n                    });\n                    const removeCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: 0,\n                    };\n                    const setCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: _utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS.maxAge,\n                    };\n                    // the NextJS cookieStore API can get confused if the `name` from\n                    // options.cookieOptions leaks\n                    delete removeCookieOptions.name;\n                    delete setCookieOptions.name;\n                    const allToSet = [\n                        ...[...removeCookies].map((name) => ({\n                            name,\n                            value: \"\",\n                            options: removeCookieOptions,\n                        })),\n                        ...setCookies.map(({ name, value }) => ({\n                            name,\n                            value,\n                            options: setCookieOptions,\n                        })),\n                    ];\n                    if (allToSet.length > 0) {\n                        await setAll(allToSet);\n                    }\n                },\n                removeItem: async (key) => {\n                    const allCookies = await getAll([key]);\n                    const cookieNames = allCookies?.map(({ name }) => name) || [];\n                    const removeCookies = cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, key));\n                    const removeCookieOptions = {\n                        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n                        ...options?.cookieOptions,\n                        maxAge: 0,\n                    };\n                    // the NextJS cookieStore API can get confused if the `name` from\n                    // options.cookieOptions leaks\n                    delete removeCookieOptions.name;\n                    if (removeCookies.length > 0) {\n                        await setAll(removeCookies.map((name) => ({\n                            name,\n                            value: \"\",\n                            options: removeCookieOptions,\n                        })));\n                    }\n                },\n            },\n        };\n    }\n    // This is the server client. It only uses getAll to read the initial\n    // state. Any subsequent changes to the items is persisted in the\n    // setItems and removedItems objects. createServerClient *must* use\n    // getAll, setAll and the values in setItems and removedItems to\n    // persist the changes *at once* when appropriate (usually only when\n    // the TOKEN_REFRESHED, USER_UPDATED or SIGNED_OUT events are fired by\n    // the Supabase Auth client).\n    return {\n        getAll,\n        setAll,\n        setItems,\n        removedItems,\n        storage: {\n            // to signal to the libraries that these cookies are\n            // coming from a server environment and their value\n            // should not be trusted\n            isServer: true,\n            getItem: async (key) => {\n                if (typeof setItems[key] === \"string\") {\n                    return setItems[key];\n                }\n                if (removedItems[key]) {\n                    return null;\n                }\n                const allCookies = await getAll([key]);\n                const chunkedCookie = await (0,_utils__WEBPACK_IMPORTED_MODULE_1__.combineChunks)(key, async (chunkName) => {\n                    const cookie = allCookies?.find(({ name }) => name === chunkName) || null;\n                    if (!cookie) {\n                        return null;\n                    }\n                    return cookie.value;\n                });\n                if (!chunkedCookie) {\n                    return null;\n                }\n                let decoded = chunkedCookie;\n                if (typeof chunkedCookie === \"string\" &&\n                    chunkedCookie.startsWith(BASE64_PREFIX)) {\n                    decoded = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringFromBase64URL)(chunkedCookie.substring(BASE64_PREFIX.length));\n                }\n                return decoded;\n            },\n            setItem: async (key, value) => {\n                // We don't have an `onAuthStateChange` event that can let us know that\n                // the PKCE code verifier is being set. Therefore, if we see it being\n                // set, we need to apply the storage (call `setAll` so the cookie is\n                // set properly).\n                if (key.endsWith(\"-code-verifier\")) {\n                    await applyServerStorage({\n                        getAll,\n                        setAll,\n                        // pretend only that the code verifier was set\n                        setItems: { [key]: value },\n                        // pretend that nothing was removed\n                        removedItems: {},\n                    }, {\n                        cookieOptions: options?.cookieOptions ?? null,\n                        cookieEncoding,\n                    });\n                }\n                setItems[key] = value;\n                delete removedItems[key];\n            },\n            removeItem: async (key) => {\n                // Intentionally not applying the storage when the key is the PKCE code\n                // verifier, as usually right after it's removed other items are set,\n                // so application of the storage will be handled by the\n                // `onAuthStateChange` callback that follows removal -- usually as part\n                // of the `exchangeCodeForSession` call.\n                delete setItems[key];\n                removedItems[key] = true;\n            },\n        },\n    };\n}\n/**\n * When createServerClient needs to apply the created storage to cookies, it\n * should call this function which handles correcly setting cookies for stored\n * and removed items in the storage.\n */\nasync function applyServerStorage({ getAll, setAll, setItems, removedItems, }, options) {\n    const cookieEncoding = options.cookieEncoding;\n    const cookieOptions = options.cookieOptions ?? null;\n    const allCookies = await getAll([\n        ...(setItems ? Object.keys(setItems) : []),\n        ...(removedItems ? Object.keys(removedItems) : []),\n    ]);\n    const cookieNames = allCookies?.map(({ name }) => name) || [];\n    const removeCookies = Object.keys(removedItems).flatMap((itemName) => {\n        return cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, itemName));\n    });\n    const setCookies = Object.keys(setItems).flatMap((itemName) => {\n        const removeExistingCookiesForItem = new Set(cookieNames.filter((name) => (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isChunkLike)(name, itemName)));\n        let encoded = setItems[itemName];\n        if (cookieEncoding === \"base64url\") {\n            encoded = BASE64_PREFIX + (0,_utils__WEBPACK_IMPORTED_MODULE_1__.stringToBase64URL)(encoded);\n        }\n        const chunks = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.createChunks)(itemName, encoded);\n        chunks.forEach((chunk) => {\n            removeExistingCookiesForItem.delete(chunk.name);\n        });\n        removeCookies.push(...removeExistingCookiesForItem);\n        return chunks;\n    });\n    const removeCookieOptions = {\n        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n        ...cookieOptions,\n        maxAge: 0,\n    };\n    const setCookieOptions = {\n        ..._utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS,\n        ...cookieOptions,\n        maxAge: _utils__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS.maxAge,\n    };\n    // the NextJS cookieStore API can get confused if the `name` from\n    // options.cookieOptions leaks\n    delete removeCookieOptions.name;\n    delete setCookieOptions.name;\n    await setAll([\n        ...removeCookies.map((name) => ({\n            name,\n            value: \"\",\n            options: removeCookieOptions,\n        })),\n        ...setCookies.map(({ name, value }) => ({\n            name,\n            value,\n            options: setCookieOptions,\n        })),\n    ]);\n}\n//# sourceMappingURL=cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/cookies.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createBrowserClient.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createBrowserClient.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBrowserClient: () => (/* binding */ createBrowserClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.49.4/node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/version.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/index.js\");\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cookies */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/cookies.js\");\n\n\n\n\nlet cachedBrowserClient;\nfunction createBrowserClient(supabaseUrl, supabaseKey, options) {\n    // singleton client is created only if isSingleton is set to true, or if isSingleton is not defined and we detect a browser\n    const shouldUseSingleton = options?.isSingleton === true ||\n        ((!options || !(\"isSingleton\" in options)) && (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)());\n    if (shouldUseSingleton && cachedBrowserClient) {\n        return cachedBrowserClient;\n    }\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`@supabase/ssr: Your project's URL and API key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    const { storage } = (0,_cookies__WEBPACK_IMPORTED_MODULE_2__.createStorageFromOptions)({\n        ...options,\n        cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n    }, false);\n    const client = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options?.global,\n            headers: {\n                ...options?.global?.headers,\n                \"X-Client-Info\": `supabase-ssr/${_version__WEBPACK_IMPORTED_MODULE_0__.VERSION} createBrowserClient`,\n            },\n        },\n        auth: {\n            ...options?.auth,\n            ...(options?.cookieOptions?.name\n                ? { storageKey: options.cookieOptions.name }\n                : null),\n            flowType: \"pkce\",\n            autoRefreshToken: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)(),\n            detectSessionInUrl: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.isBrowser)(),\n            persistSession: true,\n            storage,\n        },\n    });\n    if (shouldUseSingleton) {\n        cachedBrowserClient = client;\n    }\n    return client;\n}\n//# sourceMappingURL=createBrowserClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createBrowserClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createServerClient.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createServerClient.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.49.4/node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/version.js\");\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cookies */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/cookies.js\");\n\n\n\nfunction createServerClient(supabaseUrl, supabaseKey, options) {\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`Your project's URL and Key are required to create a Supabase client!\\n\\nCheck your Supabase project's API settings to find these values\\n\\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    const { storage, getAll, setAll, setItems, removedItems } = (0,_cookies__WEBPACK_IMPORTED_MODULE_1__.createStorageFromOptions)({\n        ...options,\n        cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n    }, true);\n    const client = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseKey, {\n        ...options,\n        global: {\n            ...options?.global,\n            headers: {\n                ...options?.global?.headers,\n                \"X-Client-Info\": `supabase-ssr/${_version__WEBPACK_IMPORTED_MODULE_0__.VERSION} createServerClient`,\n            },\n        },\n        auth: {\n            ...(options?.cookieOptions?.name\n                ? { storageKey: options.cookieOptions.name }\n                : null),\n            ...options?.auth,\n            flowType: \"pkce\",\n            autoRefreshToken: false,\n            detectSessionInUrl: false,\n            persistSession: true,\n            storage,\n        },\n    });\n    client.auth.onAuthStateChange(async (event) => {\n        // The SIGNED_IN event is fired very often, but we don't need to\n        // apply the storage each time it fires, only if there are changes\n        // that need to be set -- which is if setItems / removeItems have\n        // data.\n        const hasStorageChanges = Object.keys(setItems).length > 0 || Object.keys(removedItems).length > 0;\n        if (hasStorageChanges &&\n            (event === \"SIGNED_IN\" ||\n                event === \"TOKEN_REFRESHED\" ||\n                event === \"USER_UPDATED\" ||\n                event === \"PASSWORD_RECOVERY\" ||\n                event === \"SIGNED_OUT\" ||\n                event === \"MFA_CHALLENGE_VERIFIED\")) {\n            await (0,_cookies__WEBPACK_IMPORTED_MODULE_1__.applyServerStorage)({ getAll, setAll, setItems, removedItems }, {\n                cookieOptions: options?.cookieOptions ?? null,\n                cookieEncoding: options?.cookieEncoding ?? \"base64url\",\n            });\n        }\n    });\n    return client;\n}\n//# sourceMappingURL=createServerClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createServerClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/index.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/index.js ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.MAX_CHUNK_SIZE),\n/* harmony export */   codepointToUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.codepointToUTF8),\n/* harmony export */   combineChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.combineChunks),\n/* harmony export */   createBrowserClient: () => (/* reexport safe */ _createBrowserClient__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient),\n/* harmony export */   createChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.createChunks),\n/* harmony export */   createServerClient: () => (/* reexport safe */ _createServerClient__WEBPACK_IMPORTED_MODULE_1__.createServerClient),\n/* harmony export */   deleteChunks: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.deleteChunks),\n/* harmony export */   isBrowser: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.isBrowser),\n/* harmony export */   isChunkLike: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.isChunkLike),\n/* harmony export */   parse: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.parse),\n/* harmony export */   parseCookieHeader: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.parseCookieHeader),\n/* harmony export */   serialize: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.serialize),\n/* harmony export */   serializeCookieHeader: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.serializeCookieHeader),\n/* harmony export */   stringFromBase64URL: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_3__.stringToUTF8)\n/* harmony export */ });\n/* harmony import */ var _createBrowserClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./createBrowserClient */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createBrowserClient.js\");\n/* harmony import */ var _createServerClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./createServerClient */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/createServerClient.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/types.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_types__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _types__WEBPACK_IMPORTED_MODULE_2__) if([\"default\",\"createBrowserClient\",\"createServerClient\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _types__WEBPACK_IMPORTED_MODULE_2__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/index.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3NzckAwLjYuMV9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi40OS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2Uvc3NyL2Rpc3QvbW9kdWxlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXNDO0FBQ0Q7QUFDYjtBQUNBO0FBQ3hCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFuZHJlXFxPbmVEcml2ZVxcRGVza3RvcFxcR2l0aHViIFJlcG9zaXRvcmllc1xcZGVtb1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzc3JAMC42LjFfQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNDkuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHNzclxcZGlzdFxcbW9kdWxlXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9jcmVhdGVCcm93c2VyQ2xpZW50XCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jcmVhdGVTZXJ2ZXJDbGllbnRcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3R5cGVzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi91dGlsc1wiO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/types.js":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/types.js ***!
  \*****************************************************************************************************************************/
/***/ (() => {

eval("//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3NzckAwLjYuMV9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi40OS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2Uvc3NyL2Rpc3QvbW9kdWxlL3R5cGVzLmpzIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFuZHJlXFxPbmVEcml2ZVxcRGVza3RvcFxcR2l0aHViIFJlcG9zaXRvcmllc1xcZGVtb1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzc3JAMC42LjFfQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNDkuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHNzclxcZGlzdFxcbW9kdWxlXFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyMgc291cmNlTWFwcGluZ1VSTD10eXBlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/base64url.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/base64url.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codepointToUTF8: () => (/* binding */ codepointToUTF8),\n/* harmony export */   stringFromBase64URL: () => (/* binding */ stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* binding */ stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* binding */ stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* binding */ stringToUTF8)\n/* harmony export */ });\n/**\n * Avoid modifying this file. It's part of\n * https://github.com/supabase-community/base64url-js.  Submit all fixes on\n * that repo!\n */\n/**\n * An array of characters that encode 6 bits into a Base64-URL alphabet\n * character.\n */\nconst TO_BASE64URL = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_\".split(\"\");\n/**\n * An array of characters that can appear in a Base64-URL encoded string but\n * should be ignored.\n */\nconst IGNORE_BASE64URL = \" \\t\\n\\r=\".split(\"\");\n/**\n * An array of 128 numbers that map a Base64-URL character to 6 bits, or if -2\n * used to skip the character, or if -1 used to error out.\n */\nconst FROM_BASE64URL = (() => {\n    const charMap = new Array(128);\n    for (let i = 0; i < charMap.length; i += 1) {\n        charMap[i] = -1;\n    }\n    for (let i = 0; i < IGNORE_BASE64URL.length; i += 1) {\n        charMap[IGNORE_BASE64URL[i].charCodeAt(0)] = -2;\n    }\n    for (let i = 0; i < TO_BASE64URL.length; i += 1) {\n        charMap[TO_BASE64URL[i].charCodeAt(0)] = i;\n    }\n    return charMap;\n})();\n/**\n * Converts a JavaScript string (which may include any valid character) into a\n * Base64-URL encoded string. The string is first encoded in UTF-8 which is\n * then encoded as Base64-URL.\n *\n * @param str The string to convert.\n */\nfunction stringToBase64URL(str) {\n    const base64 = [];\n    let queue = 0;\n    let queuedBits = 0;\n    const emitter = (byte) => {\n        queue = (queue << 8) | byte;\n        queuedBits += 8;\n        while (queuedBits >= 6) {\n            const pos = (queue >> (queuedBits - 6)) & 63;\n            base64.push(TO_BASE64URL[pos]);\n            queuedBits -= 6;\n        }\n    };\n    stringToUTF8(str, emitter);\n    if (queuedBits > 0) {\n        queue = queue << (6 - queuedBits);\n        queuedBits = 6;\n        while (queuedBits >= 6) {\n            const pos = (queue >> (queuedBits - 6)) & 63;\n            base64.push(TO_BASE64URL[pos]);\n            queuedBits -= 6;\n        }\n    }\n    return base64.join(\"\");\n}\n/**\n * Converts a Base64-URL encoded string into a JavaScript string. It is assumed\n * that the underlying string has been encoded as UTF-8.\n *\n * @param str The Base64-URL encoded string.\n */\nfunction stringFromBase64URL(str) {\n    const conv = [];\n    const emit = (codepoint) => {\n        conv.push(String.fromCodePoint(codepoint));\n    };\n    const state = {\n        utf8seq: 0,\n        codepoint: 0,\n    };\n    let queue = 0;\n    let queuedBits = 0;\n    for (let i = 0; i < str.length; i += 1) {\n        const codepoint = str.charCodeAt(i);\n        const bits = FROM_BASE64URL[codepoint];\n        if (bits > -1) {\n            // valid Base64-URL character\n            queue = (queue << 6) | bits;\n            queuedBits += 6;\n            while (queuedBits >= 8) {\n                stringFromUTF8((queue >> (queuedBits - 8)) & 0xff, state, emit);\n                queuedBits -= 8;\n            }\n        }\n        else if (bits === -2) {\n            // ignore spaces, tabs, newlines, =\n            continue;\n        }\n        else {\n            throw new Error(`Invalid Base64-URL character \"${str.at(i)}\" at position ${i}`);\n        }\n    }\n    return conv.join(\"\");\n}\n/**\n * Converts a Unicode codepoint to a multi-byte UTF-8 sequence.\n *\n * @param codepoint The Unicode codepoint.\n * @param emit      Function which will be called for each UTF-8 byte that represents the codepoint.\n */\nfunction codepointToUTF8(codepoint, emit) {\n    if (codepoint <= 0x7f) {\n        emit(codepoint);\n        return;\n    }\n    else if (codepoint <= 0x7ff) {\n        emit(0xc0 | (codepoint >> 6));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0xffff) {\n        emit(0xe0 | (codepoint >> 12));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    else if (codepoint <= 0x10ffff) {\n        emit(0xf0 | (codepoint >> 18));\n        emit(0x80 | ((codepoint >> 12) & 0x3f));\n        emit(0x80 | ((codepoint >> 6) & 0x3f));\n        emit(0x80 | (codepoint & 0x3f));\n        return;\n    }\n    throw new Error(`Unrecognized Unicode codepoint: ${codepoint.toString(16)}`);\n}\n/**\n * Converts a JavaScript string to a sequence of UTF-8 bytes.\n *\n * @param str  The string to convert to UTF-8.\n * @param emit Function which will be called for each UTF-8 byte of the string.\n */\nfunction stringToUTF8(str, emit) {\n    for (let i = 0; i < str.length; i += 1) {\n        let codepoint = str.charCodeAt(i);\n        if (codepoint > 0xd7ff && codepoint <= 0xdbff) {\n            // most UTF-16 codepoints are Unicode codepoints, except values in this\n            // range where the next UTF-16 codepoint needs to be combined with the\n            // current one to get the Unicode codepoint\n            const highSurrogate = ((codepoint - 0xd800) * 0x400) & 0xffff;\n            const lowSurrogate = (str.charCodeAt(i + 1) - 0xdc00) & 0xffff;\n            codepoint = (lowSurrogate | highSurrogate) + 0x10000;\n            i += 1;\n        }\n        codepointToUTF8(codepoint, emit);\n    }\n}\n/**\n * Converts a UTF-8 byte to a Unicode codepoint.\n *\n * @param byte  The UTF-8 byte next in the sequence.\n * @param state The shared state between consecutive UTF-8 bytes in the\n *              sequence, an object with the shape `{ utf8seq: 0, codepoint: 0 }`.\n * @param emit  Function which will be called for each codepoint.\n */\nfunction stringFromUTF8(byte, state, emit) {\n    if (state.utf8seq === 0) {\n        if (byte <= 0x7f) {\n            emit(byte);\n            return;\n        }\n        // count the number of 1 leading bits until you reach 0\n        for (let leadingBit = 1; leadingBit < 6; leadingBit += 1) {\n            if (((byte >> (7 - leadingBit)) & 1) === 0) {\n                state.utf8seq = leadingBit;\n                break;\n            }\n        }\n        if (state.utf8seq === 2) {\n            state.codepoint = byte & 31;\n        }\n        else if (state.utf8seq === 3) {\n            state.codepoint = byte & 15;\n        }\n        else if (state.utf8seq === 4) {\n            state.codepoint = byte & 7;\n        }\n        else {\n            throw new Error(\"Invalid UTF-8 sequence\");\n        }\n        state.utf8seq -= 1;\n    }\n    else if (state.utf8seq > 0) {\n        if (byte <= 0x7f) {\n            throw new Error(\"Invalid UTF-8 sequence\");\n        }\n        state.codepoint = (state.codepoint << 6) | (byte & 63);\n        state.utf8seq -= 1;\n        if (state.utf8seq === 0) {\n            emit(state.codepoint);\n        }\n    }\n}\n//# sourceMappingURL=base64url.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/base64url.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/chunker.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/chunker.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* binding */ MAX_CHUNK_SIZE),\n/* harmony export */   combineChunks: () => (/* binding */ combineChunks),\n/* harmony export */   createChunks: () => (/* binding */ createChunks),\n/* harmony export */   deleteChunks: () => (/* binding */ deleteChunks),\n/* harmony export */   isChunkLike: () => (/* binding */ isChunkLike)\n/* harmony export */ });\nconst MAX_CHUNK_SIZE = 3180;\nconst CHUNK_LIKE_REGEX = /^(.*)[.](0|[1-9][0-9]*)$/;\nfunction isChunkLike(cookieName, key) {\n    if (cookieName === key) {\n        return true;\n    }\n    const chunkLike = cookieName.match(CHUNK_LIKE_REGEX);\n    if (chunkLike && chunkLike[1] === key) {\n        return true;\n    }\n    return false;\n}\n/**\n * create chunks from a string and return an array of object\n */\nfunction createChunks(key, value, chunkSize) {\n    const resolvedChunkSize = chunkSize ?? MAX_CHUNK_SIZE;\n    let encodedValue = encodeURIComponent(value);\n    if (encodedValue.length <= resolvedChunkSize) {\n        return [{ name: key, value }];\n    }\n    const chunks = [];\n    while (encodedValue.length > 0) {\n        let encodedChunkHead = encodedValue.slice(0, resolvedChunkSize);\n        const lastEscapePos = encodedChunkHead.lastIndexOf(\"%\");\n        // Check if the last escaped character is truncated.\n        if (lastEscapePos > resolvedChunkSize - 3) {\n            // If so, reslice the string to exclude the whole escape sequence.\n            // We only reduce the size of the string as the chunk must\n            // be smaller than the chunk size.\n            encodedChunkHead = encodedChunkHead.slice(0, lastEscapePos);\n        }\n        let valueHead = \"\";\n        // Check if the chunk was split along a valid unicode boundary.\n        while (encodedChunkHead.length > 0) {\n            try {\n                // Try to decode the chunk back and see if it is valid.\n                // Stop when the chunk is valid.\n                valueHead = decodeURIComponent(encodedChunkHead);\n                break;\n            }\n            catch (error) {\n                if (error instanceof URIError &&\n                    encodedChunkHead.at(-3) === \"%\" &&\n                    encodedChunkHead.length > 3) {\n                    encodedChunkHead = encodedChunkHead.slice(0, encodedChunkHead.length - 3);\n                }\n                else {\n                    throw error;\n                }\n            }\n        }\n        chunks.push(valueHead);\n        encodedValue = encodedValue.slice(encodedChunkHead.length);\n    }\n    return chunks.map((value, i) => ({ name: `${key}.${i}`, value }));\n}\n// Get fully constructed chunks\nasync function combineChunks(key, retrieveChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        return value;\n    }\n    let values = [];\n    for (let i = 0;; i++) {\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        values.push(chunk);\n    }\n    if (values.length > 0) {\n        return values.join(\"\");\n    }\n    return null;\n}\nasync function deleteChunks(key, retrieveChunk, removeChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        await removeChunk(key);\n    }\n    for (let i = 0;; i++) {\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        await removeChunk(chunkName);\n    }\n}\n//# sourceMappingURL=chunker.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/chunker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/constants.js":
/*!***************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/constants.js ***!
  \***************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* binding */ DEFAULT_COOKIE_OPTIONS)\n/* harmony export */ });\nconst DEFAULT_COOKIE_OPTIONS = {\n    path: \"/\",\n    sameSite: \"lax\",\n    httpOnly: false,\n    // https://developer.chrome.com/blog/cookie-max-age-expires\n    // https://httpwg.org/http-extensions/draft-ietf-httpbis-rfc6265bis.html#name-cookie-lifetime-limits\n    maxAge: 400 * 24 * 60 * 60,\n};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3NzckAwLjYuMV9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi40OS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2Uvc3NyL2Rpc3QvbW9kdWxlL3V0aWxzL2NvbnN0YW50cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFuZHJlXFxPbmVEcml2ZVxcRGVza3RvcFxcR2l0aHViIFJlcG9zaXRvcmllc1xcZGVtb1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzc3JAMC42LjFfQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNDkuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHNzclxcZGlzdFxcbW9kdWxlXFx1dGlsc1xcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBERUZBVUxUX0NPT0tJRV9PUFRJT05TID0ge1xuICAgIHBhdGg6IFwiL1wiLFxuICAgIHNhbWVTaXRlOiBcImxheFwiLFxuICAgIGh0dHBPbmx5OiBmYWxzZSxcbiAgICAvLyBodHRwczovL2RldmVsb3Blci5jaHJvbWUuY29tL2Jsb2cvY29va2llLW1heC1hZ2UtZXhwaXJlc1xuICAgIC8vIGh0dHBzOi8vaHR0cHdnLm9yZy9odHRwLWV4dGVuc2lvbnMvZHJhZnQtaWV0Zi1odHRwYmlzLXJmYzYyNjViaXMuaHRtbCNuYW1lLWNvb2tpZS1saWZldGltZS1saW1pdHNcbiAgICBtYXhBZ2U6IDQwMCAqIDI0ICogNjAgKiA2MCxcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb25zdGFudHMuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/helpers.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/helpers.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   parseCookieHeader: () => (/* binding */ parseCookieHeader),\n/* harmony export */   serialize: () => (/* binding */ serialize),\n/* harmony export */   serializeCookieHeader: () => (/* binding */ serializeCookieHeader)\n/* harmony export */ });\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"(ssr)/./node_modules/.pnpm/cookie@1.0.2/node_modules/cookie/dist/index.js\");\n\n/**\n * @deprecated Since v0.4.0: Please use {@link parseCookieHeader}. `parse` will\n * not be available for import starting v1.0.0 of `@supabase/ssr`.\n */\nconst parse = cookie__WEBPACK_IMPORTED_MODULE_0__.parse;\n/**\n * @deprecated Since v0.4.0: Please use {@link serializeCookieHeader}.\n * `serialize` will not be available for import starting v1.0.0 of\n * `@supabase/ssr`.\n */\nconst serialize = cookie__WEBPACK_IMPORTED_MODULE_0__.serialize;\n/**\n * Parses the `Cookie` HTTP header into an array of cookie name-value objects.\n *\n * @param header The `Cookie` HTTP header. Decodes cookie names and values from\n * URI encoding first.\n */\nfunction parseCookieHeader(header) {\n    const parsed = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.parse)(header);\n    return Object.keys(parsed ?? {}).map((name) => ({\n        name,\n        value: parsed[name],\n    }));\n}\n/**\n * Converts the arguments to a valid `Set-Cookie` header. Non US-ASCII chars\n * and other forbidden cookie chars will be URI encoded.\n *\n * @param name Name of cookie.\n * @param value Value of cookie.\n */\nfunction serializeCookieHeader(name, value, options) {\n    return (0,cookie__WEBPACK_IMPORTED_MODULE_0__.serialize)(name, value, options);\n}\nfunction isBrowser() {\n    return (typeof window !== \"undefined\" && typeof window.document !== \"undefined\");\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/index.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/index.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* reexport safe */ _constants__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   MAX_CHUNK_SIZE: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.MAX_CHUNK_SIZE),\n/* harmony export */   codepointToUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.codepointToUTF8),\n/* harmony export */   combineChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.combineChunks),\n/* harmony export */   createChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.createChunks),\n/* harmony export */   deleteChunks: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.deleteChunks),\n/* harmony export */   isBrowser: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.isBrowser),\n/* harmony export */   isChunkLike: () => (/* reexport safe */ _chunker__WEBPACK_IMPORTED_MODULE_2__.isChunkLike),\n/* harmony export */   parse: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.parse),\n/* harmony export */   parseCookieHeader: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.parseCookieHeader),\n/* harmony export */   serialize: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.serialize),\n/* harmony export */   serializeCookieHeader: () => (/* reexport safe */ _helpers__WEBPACK_IMPORTED_MODULE_0__.serializeCookieHeader),\n/* harmony export */   stringFromBase64URL: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringFromBase64URL),\n/* harmony export */   stringFromUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringFromUTF8),\n/* harmony export */   stringToBase64URL: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringToBase64URL),\n/* harmony export */   stringToUTF8: () => (/* reexport safe */ _base64url__WEBPACK_IMPORTED_MODULE_3__.stringToUTF8)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/helpers.js\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/constants.js\");\n/* harmony import */ var _chunker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunker */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/chunker.js\");\n/* harmony import */ var _base64url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./base64url */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/base64url.js\");\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3NzckAwLjYuMV9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi40OS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2Uvc3NyL2Rpc3QvbW9kdWxlL3V0aWxzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTBCO0FBQ0U7QUFDRjtBQUNFO0FBQzVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFuZHJlXFxPbmVEcml2ZVxcRGVza3RvcFxcR2l0aHViIFJlcG9zaXRvcmllc1xcZGVtb1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzc3JAMC42LjFfQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNDkuNFxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHNzclxcZGlzdFxcbW9kdWxlXFx1dGlsc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vaGVscGVyc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vY29uc3RhbnRzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jaHVua2VyXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9iYXNlNjR1cmxcIjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/utils/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/version.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/version.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VERSION: () => (/* binding */ VERSION)\n/* harmony export */ });\nconst VERSION = '0.6.1';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3NzckAwLjYuMV9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi40OS40L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2Uvc3NyL2Rpc3QvbW9kdWxlL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW5kcmVcXE9uZURyaXZlXFxEZXNrdG9wXFxHaXRodWIgUmVwb3NpdG9yaWVzXFxkZW1vXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3NzckAwLjYuMV9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi40OS40XFxub2RlX21vZHVsZXNcXEBzdXBhYmFzZVxcc3NyXFxkaXN0XFxtb2R1bGVcXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IFZFUlNJT04gPSAnMC42LjEnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dmVyc2lvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/version.js\n");

/***/ })

};
;