"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_codemirror_legacy-modes_6_5_1_node_modules_codemirror_le-0a512f"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/python.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/python.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cython: () => (/* binding */ cython),\n/* harmony export */   mkPython: () => (/* binding */ mkPython),\n/* harmony export */   python: () => (/* binding */ python)\n/* harmony export */ });\nfunction wordRegexp(words) {\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n}\n\nvar wordOperators = wordRegexp([\"and\", \"or\", \"not\", \"is\"]);\nvar commonKeywords = [\"as\", \"assert\", \"break\", \"class\", \"continue\",\n                      \"def\", \"del\", \"elif\", \"else\", \"except\", \"finally\",\n                      \"for\", \"from\", \"global\", \"if\", \"import\",\n                      \"lambda\", \"pass\", \"raise\", \"return\",\n                      \"try\", \"while\", \"with\", \"yield\", \"in\", \"False\", \"True\"];\nvar commonBuiltins = [\"abs\", \"all\", \"any\", \"bin\", \"bool\", \"bytearray\", \"callable\", \"chr\",\n                      \"classmethod\", \"compile\", \"complex\", \"delattr\", \"dict\", \"dir\", \"divmod\",\n                      \"enumerate\", \"eval\", \"filter\", \"float\", \"format\", \"frozenset\",\n                      \"getattr\", \"globals\", \"hasattr\", \"hash\", \"help\", \"hex\", \"id\",\n                      \"input\", \"int\", \"isinstance\", \"issubclass\", \"iter\", \"len\",\n                      \"list\", \"locals\", \"map\", \"max\", \"memoryview\", \"min\", \"next\",\n                      \"object\", \"oct\", \"open\", \"ord\", \"pow\", \"property\", \"range\",\n                      \"repr\", \"reversed\", \"round\", \"set\", \"setattr\", \"slice\",\n                      \"sorted\", \"staticmethod\", \"str\", \"sum\", \"super\", \"tuple\",\n                      \"type\", \"vars\", \"zip\", \"__import__\", \"NotImplemented\",\n                      \"Ellipsis\", \"__debug__\"];\n\nfunction top(state) {\n  return state.scopes[state.scopes.length - 1];\n}\n\nfunction mkPython(parserConf) {\n  var ERRORCLASS = \"error\";\n\n  var delimiters = parserConf.delimiters || parserConf.singleDelimiters || /^[\\(\\)\\[\\]\\{\\}@,:`=;\\.\\\\]/;\n  //               (Backwards-compatibility with old, cumbersome config system)\n  var operators = [parserConf.singleOperators, parserConf.doubleOperators, parserConf.doubleDelimiters, parserConf.tripleDelimiters,\n                   parserConf.operators || /^([-+*/%\\/&|^]=?|[<>=]+|\\/\\/=?|\\*\\*=?|!=|[~!@]|\\.\\.\\.)/]\n  for (var i = 0; i < operators.length; i++) if (!operators[i]) operators.splice(i--, 1)\n\n  var hangingIndent = parserConf.hangingIndent;\n\n  var myKeywords = commonKeywords, myBuiltins = commonBuiltins;\n  if (parserConf.extra_keywords != undefined)\n    myKeywords = myKeywords.concat(parserConf.extra_keywords);\n\n  if (parserConf.extra_builtins != undefined)\n    myBuiltins = myBuiltins.concat(parserConf.extra_builtins);\n\n  var py3 = !(parserConf.version && Number(parserConf.version) < 3)\n  if (py3) {\n    // since http://legacy.python.org/dev/peps/pep-0465/ @ is also an operator\n    var identifiers = parserConf.identifiers|| /^[_A-Za-z\\u00A1-\\uFFFF][_A-Za-z0-9\\u00A1-\\uFFFF]*/;\n    myKeywords = myKeywords.concat([\"nonlocal\", \"None\", \"aiter\", \"anext\", \"async\", \"await\", \"breakpoint\", \"match\", \"case\"]);\n    myBuiltins = myBuiltins.concat([\"ascii\", \"bytes\", \"exec\", \"print\"]);\n    var stringPrefixes = new RegExp(\"^(([rbuf]|(br)|(rb)|(fr)|(rf))?('{3}|\\\"{3}|['\\\"]))\", \"i\");\n  } else {\n    var identifiers = parserConf.identifiers|| /^[_A-Za-z][_A-Za-z0-9]*/;\n    myKeywords = myKeywords.concat([\"exec\", \"print\"]);\n    myBuiltins = myBuiltins.concat([\"apply\", \"basestring\", \"buffer\", \"cmp\", \"coerce\", \"execfile\",\n                                    \"file\", \"intern\", \"long\", \"raw_input\", \"reduce\", \"reload\",\n                                    \"unichr\", \"unicode\", \"xrange\", \"None\"]);\n    var stringPrefixes = new RegExp(\"^(([rubf]|(ur)|(br))?('{3}|\\\"{3}|['\\\"]))\", \"i\");\n  }\n  var keywords = wordRegexp(myKeywords);\n  var builtins = wordRegexp(myBuiltins);\n\n  // tokenizers\n  function tokenBase(stream, state) {\n    var sol = stream.sol() && state.lastToken != \"\\\\\"\n    if (sol) state.indent = stream.indentation()\n    // Handle scope changes\n    if (sol && top(state).type == \"py\") {\n      var scopeOffset = top(state).offset;\n      if (stream.eatSpace()) {\n        var lineOffset = stream.indentation();\n        if (lineOffset > scopeOffset)\n          pushPyScope(stream, state);\n        else if (lineOffset < scopeOffset && dedent(stream, state) && stream.peek() != \"#\")\n          state.errorToken = true;\n        return null;\n      } else {\n        var style = tokenBaseInner(stream, state);\n        if (scopeOffset > 0 && dedent(stream, state))\n          style += \" \" + ERRORCLASS;\n        return style;\n      }\n    }\n    return tokenBaseInner(stream, state);\n  }\n\n  function tokenBaseInner(stream, state, inFormat) {\n    if (stream.eatSpace()) return null;\n\n    // Handle Comments\n    if (!inFormat && stream.match(/^#.*/)) return \"comment\";\n\n    // Handle Number Literals\n    if (stream.match(/^[0-9\\.]/, false)) {\n      var floatLiteral = false;\n      // Floats\n      if (stream.match(/^[\\d_]*\\.\\d+(e[\\+\\-]?\\d+)?/i)) { floatLiteral = true; }\n      if (stream.match(/^[\\d_]+\\.\\d*/)) { floatLiteral = true; }\n      if (stream.match(/^\\.\\d+/)) { floatLiteral = true; }\n      if (floatLiteral) {\n        // Float literals may be \"imaginary\"\n        stream.eat(/J/i);\n        return \"number\";\n      }\n      // Integers\n      var intLiteral = false;\n      // Hex\n      if (stream.match(/^0x[0-9a-f_]+/i)) intLiteral = true;\n      // Binary\n      if (stream.match(/^0b[01_]+/i)) intLiteral = true;\n      // Octal\n      if (stream.match(/^0o[0-7_]+/i)) intLiteral = true;\n      // Decimal\n      if (stream.match(/^[1-9][\\d_]*(e[\\+\\-]?[\\d_]+)?/)) {\n        // Decimal literals may be \"imaginary\"\n        stream.eat(/J/i);\n        // TODO - Can you have imaginary longs?\n        intLiteral = true;\n      }\n      // Zero by itself with no other piece of number.\n      if (stream.match(/^0(?![\\dx])/i)) intLiteral = true;\n      if (intLiteral) {\n        // Integer literals may be \"long\"\n        stream.eat(/L/i);\n        return \"number\";\n      }\n    }\n\n    // Handle Strings\n    if (stream.match(stringPrefixes)) {\n      var isFmtString = stream.current().toLowerCase().indexOf('f') !== -1;\n      if (!isFmtString) {\n        state.tokenize = tokenStringFactory(stream.current(), state.tokenize);\n        return state.tokenize(stream, state);\n      } else {\n        state.tokenize = formatStringFactory(stream.current(), state.tokenize);\n        return state.tokenize(stream, state);\n      }\n    }\n\n    for (var i = 0; i < operators.length; i++)\n      if (stream.match(operators[i])) return \"operator\"\n\n    if (stream.match(delimiters)) return \"punctuation\";\n\n    if (state.lastToken == \".\" && stream.match(identifiers))\n      return \"property\";\n\n    if (stream.match(keywords) || stream.match(wordOperators))\n      return \"keyword\";\n\n    if (stream.match(builtins))\n      return \"builtin\";\n\n    if (stream.match(/^(self|cls)\\b/))\n      return \"self\";\n\n    if (stream.match(identifiers)) {\n      if (state.lastToken == \"def\" || state.lastToken == \"class\")\n        return \"def\";\n      return \"variable\";\n    }\n\n    // Handle non-detected items\n    stream.next();\n    return inFormat ? null :ERRORCLASS;\n  }\n\n  function formatStringFactory(delimiter, tokenOuter) {\n    while (\"rubf\".indexOf(delimiter.charAt(0).toLowerCase()) >= 0)\n      delimiter = delimiter.substr(1);\n\n    var singleline = delimiter.length == 1;\n    var OUTCLASS = \"string\";\n\n    function tokenNestedExpr(depth) {\n      return function(stream, state) {\n        var inner = tokenBaseInner(stream, state, true)\n        if (inner == \"punctuation\") {\n          if (stream.current() == \"{\") {\n            state.tokenize = tokenNestedExpr(depth + 1)\n          } else if (stream.current() == \"}\") {\n            if (depth > 1) state.tokenize = tokenNestedExpr(depth - 1)\n            else state.tokenize = tokenString\n          }\n        }\n        return inner\n      }\n    }\n\n    function tokenString(stream, state) {\n      while (!stream.eol()) {\n        stream.eatWhile(/[^'\"\\{\\}\\\\]/);\n        if (stream.eat(\"\\\\\")) {\n          stream.next();\n          if (singleline && stream.eol())\n            return OUTCLASS;\n        } else if (stream.match(delimiter)) {\n          state.tokenize = tokenOuter;\n          return OUTCLASS;\n        } else if (stream.match('{{')) {\n          // ignore {{ in f-str\n          return OUTCLASS;\n        } else if (stream.match('{', false)) {\n          // switch to nested mode\n          state.tokenize = tokenNestedExpr(0)\n          if (stream.current()) return OUTCLASS;\n          else return state.tokenize(stream, state)\n        } else if (stream.match('}}')) {\n          return OUTCLASS;\n        } else if (stream.match('}')) {\n          // single } in f-string is an error\n          return ERRORCLASS;\n        } else {\n          stream.eat(/['\"]/);\n        }\n      }\n      if (singleline) {\n        if (parserConf.singleLineStringErrors)\n          return ERRORCLASS;\n        else\n          state.tokenize = tokenOuter;\n      }\n      return OUTCLASS;\n    }\n    tokenString.isString = true;\n    return tokenString;\n  }\n\n  function tokenStringFactory(delimiter, tokenOuter) {\n    while (\"rubf\".indexOf(delimiter.charAt(0).toLowerCase()) >= 0)\n      delimiter = delimiter.substr(1);\n\n    var singleline = delimiter.length == 1;\n    var OUTCLASS = \"string\";\n\n    function tokenString(stream, state) {\n      while (!stream.eol()) {\n        stream.eatWhile(/[^'\"\\\\]/);\n        if (stream.eat(\"\\\\\")) {\n          stream.next();\n          if (singleline && stream.eol())\n            return OUTCLASS;\n        } else if (stream.match(delimiter)) {\n          state.tokenize = tokenOuter;\n          return OUTCLASS;\n        } else {\n          stream.eat(/['\"]/);\n        }\n      }\n      if (singleline) {\n        if (parserConf.singleLineStringErrors)\n          return ERRORCLASS;\n        else\n          state.tokenize = tokenOuter;\n      }\n      return OUTCLASS;\n    }\n    tokenString.isString = true;\n    return tokenString;\n  }\n\n  function pushPyScope(stream, state) {\n    while (top(state).type != \"py\") state.scopes.pop()\n    state.scopes.push({offset: top(state).offset + stream.indentUnit,\n                       type: \"py\",\n                       align: null})\n  }\n\n  function pushBracketScope(stream, state, type) {\n    var align = stream.match(/^[\\s\\[\\{\\(]*(?:#|$)/, false) ? null : stream.column() + 1\n    state.scopes.push({offset: state.indent + (hangingIndent || stream.indentUnit),\n                       type: type,\n                       align: align})\n  }\n\n  function dedent(stream, state) {\n    var indented = stream.indentation();\n    while (state.scopes.length > 1 && top(state).offset > indented) {\n      if (top(state).type != \"py\") return true;\n      state.scopes.pop();\n    }\n    return top(state).offset != indented;\n  }\n\n  function tokenLexer(stream, state) {\n    if (stream.sol()) {\n      state.beginningOfLine = true;\n      state.dedent = false;\n    }\n\n    var style = state.tokenize(stream, state);\n    var current = stream.current();\n\n    // Handle decorators\n    if (state.beginningOfLine && current == \"@\")\n      return stream.match(identifiers, false) ? \"meta\" : py3 ? \"operator\" : ERRORCLASS;\n\n    if (/\\S/.test(current)) state.beginningOfLine = false;\n\n    if ((style == \"variable\" || style == \"builtin\")\n        && state.lastToken == \"meta\")\n      style = \"meta\";\n\n    // Handle scope changes.\n    if (current == \"pass\" || current == \"return\")\n      state.dedent = true;\n\n    if (current == \"lambda\") state.lambda = true;\n    if (current == \":\" && !state.lambda && top(state).type == \"py\" && stream.match(/^\\s*(?:#|$)/, false))\n      pushPyScope(stream, state);\n\n    if (current.length == 1 && !/string|comment/.test(style)) {\n      var delimiter_index = \"[({\".indexOf(current);\n      if (delimiter_index != -1)\n        pushBracketScope(stream, state, \"])}\".slice(delimiter_index, delimiter_index+1));\n\n      delimiter_index = \"])}\".indexOf(current);\n      if (delimiter_index != -1) {\n        if (top(state).type == current) state.indent = state.scopes.pop().offset - (hangingIndent || stream.indentUnit)\n        else return ERRORCLASS;\n      }\n    }\n    if (state.dedent && stream.eol() && top(state).type == \"py\" && state.scopes.length > 1)\n      state.scopes.pop();\n\n    return style;\n  }\n\n  return {\n    name: \"python\",\n\n    startState: function() {\n      return {\n        tokenize: tokenBase,\n        scopes: [{offset: 0, type: \"py\", align: null}],\n        indent: 0,\n        lastToken: null,\n        lambda: false,\n        dedent: 0\n      };\n    },\n\n    token: function(stream, state) {\n      var addErr = state.errorToken;\n      if (addErr) state.errorToken = false;\n      var style = tokenLexer(stream, state);\n\n      if (style && style != \"comment\")\n        state.lastToken = (style == \"keyword\" || style == \"punctuation\") ? stream.current() : style;\n      if (style == \"punctuation\") style = null;\n\n      if (stream.eol() && state.lambda)\n        state.lambda = false;\n      return addErr ? ERRORCLASS : style;\n    },\n\n    indent: function(state, textAfter, cx) {\n      if (state.tokenize != tokenBase)\n        return state.tokenize.isString ? null : 0;\n\n      var scope = top(state)\n      var closing = scope.type == textAfter.charAt(0) ||\n          scope.type == \"py\" && !state.dedent && /^(else:|elif |except |finally:)/.test(textAfter)\n      if (scope.align != null)\n        return scope.align - (closing ? 1 : 0)\n      else\n        return scope.offset - (closing ? hangingIndent || cx.unit : 0)\n    },\n\n    languageData: {\n      autocomplete: commonKeywords.concat(commonBuiltins).concat([\"exec\", \"print\"]),\n      indentOnInput: /^\\s*([\\}\\]\\)]|else:|elif |except |finally:)$/,\n      commentTokens: {line: \"#\"},\n      closeBrackets: {brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"'''\", '\"\"\"']}\n    }\n  };\n};\n\nvar words = function(str) { return str.split(\" \"); };\n\nconst python = mkPython({})\n\nconst cython = mkPython({\n  extra_keywords: words(\"by cdef cimport cpdef ctypedef enum except \"+\n                        \"extern gil include nogil property public \"+\n                        \"readonly struct union DEF IF ELIF ELSE\")\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/python.js\n"));

/***/ })

}]);