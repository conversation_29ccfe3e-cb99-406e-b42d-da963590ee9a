"""
Agent Memory Module for AgentPress.

This module provides functionality for agent memory and learning, enabling
agents to recall past interactions and improve over time.

Features:
- Long-term memory storage and retrieval
- Memory categorization and prioritization
- Memory search and relevance scoring
- Learning from past interactions
"""

import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from enum import Enum

from utils.logger import logger
from services.supabase import DBConnection


class MemoryType(Enum):
    """Types of memories that can be stored."""
    FACT = "fact"             # Factual information
    INTERACTION = "interaction"  # Record of an interaction
    PREFERENCE = "preference"  # User preference
    SKILL = "skill"           # Learned skill or capability
    FEEDBACK = "feedback"     # Feedback on agent performance
    GOAL = "goal"             # Goal or objective
    PLAN = "plan"             # Plan or strategy


class MemoryPriority(Enum):
    """Priority levels for memories."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AgentMemoryManager:
    """Manages memory and learning for agents.

    This class provides methods for storing, retrieving, and searching memories,
    as well as learning from past interactions.

    Attributes:
        db: Database connection for persisting memories
    """

    def __init__(self):
        """Initialize the AgentMemoryManager."""
        self.db = DBConnection()
        logger.debug("Initialized AgentMemoryManager")

    async def store_memory(
        self,
        agent_id: str,
        content: str,
        memory_type: MemoryType,
        priority: MemoryPriority = MemoryPriority.MEDIUM,
        context: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        expiration: Optional[datetime] = None
    ) -> str:
        """Store a new memory for an agent.

        Args:
            agent_id: ID of the agent to store the memory for
            content: Content of the memory
            memory_type: Type of memory
            priority: Priority of the memory
            context: Optional context for the memory
            metadata: Optional additional metadata
            expiration: Optional expiration date for the memory

        Returns:
            ID of the created memory
        """
        try:
            # Generate a new memory ID
            memory_id = str(uuid.uuid4())

            # Prepare memory data
            memory_data = {
                "memory_id": memory_id,
                "agent_id": agent_id,
                "content": content,
                "memory_type": memory_type.value,
                "priority": priority.value,
                "context": context or {},
                "metadata": metadata or {},
                "created_at": datetime.now(timezone.utc).isoformat(),
                "last_accessed": None,
                "access_count": 0,
                "expiration": expiration.isoformat() if expiration else None
            }

            # Save memory to database
            client = await self.db.client
            await client.from_("agent_memories").insert(memory_data).execute()

            logger.info(f"Stored memory for agent {agent_id} with type {memory_type.value}")
            return memory_id

        except Exception as e:
            logger.error(f"Error storing memory: {str(e)}", exc_info=True)
            raise

    async def retrieve_memory(self, memory_id: str) -> Optional[Dict[str, Any]]:
        """Retrieve a specific memory by ID.

        Args:
            memory_id: ID of the memory to retrieve

        Returns:
            Memory object or None if not found
        """
        try:
            client = await self.db.client
            result = await client.from_("agent_memories").select("*").eq("memory_id", memory_id).execute()

            if result.data and len(result.data) > 0:
                memory = result.data[0]

                # Update access statistics
                await self._update_memory_access(memory_id)

                logger.debug(f"Retrieved memory {memory_id}")
                return memory
            else:
                logger.warning(f"Memory {memory_id} not found")
                return None

        except Exception as e:
            logger.error(f"Error retrieving memory: {str(e)}", exc_info=True)
            return None

    async def search_memories(
        self,
        agent_id: str,
        query: str,
        memory_type: Optional[MemoryType] = None,
        min_priority: Optional[MemoryPriority] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Search for memories based on content and criteria.

        Args:
            agent_id: ID of the agent to search memories for
            query: Search query
            memory_type: Optional type of memories to search
            min_priority: Optional minimum priority level
            limit: Maximum number of memories to return

        Returns:
            List of matching memory objects
        """
        try:
            client = await self.db.client

            # Start with basic query
            db_query = client.from_("agent_memories").select("*").eq("agent_id", agent_id)

            # Apply filters
            if memory_type:
                db_query = db_query.eq("memory_type", memory_type.value)

            if min_priority:
                # This is a simplified approach - in a real implementation,
                # you would need a more sophisticated query to handle priority levels
                db_query = db_query.eq("priority", min_priority.value)

            # Execute query
            result = await db_query.execute()
            memories = result.data

            # Filter and rank results based on relevance to query
            # This is a simplified approach - in a real implementation,
            # you would use a more sophisticated search algorithm
            scored_memories = []
            for memory in memories:
                content = memory.get("content", "")
                score = self._calculate_relevance(content, query)
                if score > 0:
                    scored_memories.append((memory, score))

            # Sort by relevance score
            scored_memories.sort(key=lambda x: x[1], reverse=True)

            # Take top results
            top_memories = [memory for memory, _ in scored_memories[:limit]]

            # Update access statistics for retrieved memories
            for memory in top_memories:
                await self._update_memory_access(memory["memory_id"])

            logger.debug(f"Found {len(top_memories)} memories for agent {agent_id} matching query '{query}'")
            return top_memories

        except Exception as e:
            logger.error(f"Error searching memories: {str(e)}", exc_info=True)
            return []

    async def update_memory(
        self,
        memory_id: str,
        content: Optional[str] = None,
        priority: Optional[MemoryPriority] = None,
        context: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        expiration: Optional[datetime] = None
    ) -> bool:
        """Update an existing memory.

        Args:
            memory_id: ID of the memory to update
            content: Optional new content
            priority: Optional new priority
            context: Optional new context
            metadata: Optional new metadata
            expiration: Optional new expiration date

        Returns:
            Whether the update was successful
        """
        try:
            # Prepare update data
            update_data = {}
            if content is not None:
                update_data["content"] = content
            if priority is not None:
                update_data["priority"] = priority.value
            if context is not None:
                update_data["context"] = context
            if metadata is not None:
                update_data["metadata"] = metadata
            if expiration is not None:
                update_data["expiration"] = expiration.isoformat()

            # If no updates, return early
            if not update_data:
                return True

            # Update memory in database
            client = await self.db.client
            await client.from_("agent_memories").update(update_data).eq("memory_id", memory_id).execute()

            logger.info(f"Updated memory {memory_id}")
            return True

        except Exception as e:
            logger.error(f"Error updating memory: {str(e)}", exc_info=True)
            return False

    async def delete_memory(self, memory_id: str) -> bool:
        """Delete a memory.

        Args:
            memory_id: ID of the memory to delete

        Returns:
            Whether the deletion was successful
        """
        try:
            client = await self.db.client
            await client.from_("agent_memories").delete().eq("memory_id", memory_id).execute()

            logger.info(f"Deleted memory {memory_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting memory: {str(e)}", exc_info=True)
            return False

    async def get_recent_memories(
        self,
        agent_id: str,
        memory_type: Optional[MemoryType] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get recent memories for an agent.

        Args:
            agent_id: ID of the agent to get memories for
            memory_type: Optional type of memories to get
            limit: Maximum number of memories to return

        Returns:
            List of memory objects
        """
        try:
            client = await self.db.client

            # Start with basic query
            query = client.from_("agent_memories").select("*").eq("agent_id", agent_id)

            # Apply filters
            if memory_type:
                query = query.eq("memory_type", memory_type.value)

            # Apply sorting and pagination
            query = query.order("created_at", desc=True).limit(limit)

            # Execute query
            result = await query.execute()
            memories = result.data

            logger.debug(f"Retrieved {len(memories)} recent memories for agent {agent_id}")
            return memories

        except Exception as e:
            logger.error(f"Error getting recent memories: {str(e)}", exc_info=True)
            return []

    async def get_important_memories(
        self,
        agent_id: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get important memories for an agent based on priority.

        Args:
            agent_id: ID of the agent to get memories for
            limit: Maximum number of memories to return

        Returns:
            List of memory objects
        """
        try:
            client = await self.db.client

            # Get high and critical priority memories
            query = client.from_("agent_memories").select("*").eq("agent_id", agent_id).in_("priority", ["high", "critical"])

            # Apply sorting and pagination
            query = query.order("priority", desc=True).order("created_at", desc=True).limit(limit)

            # Execute query
            result = await query.execute()
            memories = result.data

            logger.debug(f"Retrieved {len(memories)} important memories for agent {agent_id}")
            return memories

        except Exception as e:
            logger.error(f"Error getting important memories: {str(e)}", exc_info=True)
            return []

    async def get_frequently_accessed_memories(
        self,
        agent_id: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Get frequently accessed memories for an agent.

        Args:
            agent_id: ID of the agent to get memories for
            limit: Maximum number of memories to return

        Returns:
            List of memory objects
        """
        try:
            client = await self.db.client

            # Get memories with high access count
            query = client.from_("agent_memories").select("*").eq("agent_id", agent_id).gt("access_count", 0)

            # Apply sorting and pagination
            query = query.order("access_count", desc=True).limit(limit)

            # Execute query
            result = await query.execute()
            memories = result.data

            logger.debug(f"Retrieved {len(memories)} frequently accessed memories for agent {agent_id}")
            return memories

        except Exception as e:
            logger.error(f"Error getting frequently accessed memories: {str(e)}", exc_info=True)
            return []

    async def _update_memory_access(self, memory_id: str) -> None:
        """Update access statistics for a memory.

        Args:
            memory_id: ID of the memory to update
        """
        try:
            client = await self.db.client

            # Get current access count
            result = await client.from_("agent_memories").select("access_count").eq("memory_id", memory_id).execute()

            if result.data and len(result.data) > 0:
                current_count = result.data[0].get("access_count", 0)

                # Update access count and last accessed time
                update_data = {
                    "access_count": current_count + 1,
                    "last_accessed": datetime.now(timezone.utc).isoformat()
                }

                await client.from_("agent_memories").update(update_data).eq("memory_id", memory_id).execute()

        except Exception as e:
            logger.error(f"Error updating memory access: {str(e)}", exc_info=True)

    def _calculate_relevance(self, content: str, query: str) -> float:
        """Calculate relevance score between content and query.

        This is a simplified implementation. In a real system, you would use
        more sophisticated techniques like embeddings or semantic search.

        Args:
            content: Content to score
            query: Query to match against

        Returns:
            Relevance score between 0 and 1
        """
        # Simple word matching for demonstration
        content_lower = content.lower()
        query_lower = query.lower()

        # Split query into words
        query_words = query_lower.split()

        # Count matching words
        matching_words = sum(1 for word in query_words if word in content_lower)

        # Calculate score
        if not query_words:
            return 0

        return matching_words / len(query_words)
