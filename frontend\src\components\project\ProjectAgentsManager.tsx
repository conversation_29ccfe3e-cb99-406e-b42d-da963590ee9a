import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { UserCircle, BrainCircuit, X } from 'lucide-react';

interface AgentType {
  agent_type_id: string;
  name: string;
  role: string;
  description: string;
  capabilities: Record<string, boolean>;
}

interface ProjectAgent {
  agent_id: string;
  agent_type_id: string;
  name: string;
  role: string;
  description: string;
  capabilities: Record<string, boolean>;
  is_active: boolean;
}

interface ProjectAgentsManagerProps {
  projectId: string;
}

export function ProjectAgentsManager({ projectId }: ProjectAgentsManagerProps) {
  const [agentTypes, setAgentTypes] = useState<AgentType[]>([]);
  const [projectAgents, setProjectAgents] = useState<ProjectAgent[]>([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  // Fetch agent types and project agents
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch agent types
        const typesResponse = await fetch('/api/project-agents/agent-types');
        const typesData = await typesResponse.json();

        // Fetch project agents
        const agentsResponse = await fetch(`/api/project-agents/${projectId}`);
        const agentsData = await agentsResponse.json();

        setAgentTypes(typesData.agent_types || []);
        setProjectAgents(agentsData.agents || []);
      } catch (error) {
        console.error('Error fetching agent data:', error);
        toast.error('Failed to load agent data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [projectId]);

  // Add an agent to the project
  const addAgent = async (agentTypeId: string) => {
    setUpdating(true);
    try {
      const response = await fetch(`/api/project-agents/${projectId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ agent_type_id: agentTypeId }),
      });

      if (!response.ok) {
        throw new Error('Failed to add agent');
      }

      // Refresh project agents
      const agentsResponse = await fetch(`/api/project-agents/${projectId}`);
      const agentsData = await agentsResponse.json();
      setProjectAgents(agentsData.agents || []);

      toast.success('Agent added to project');
    } catch (error) {
      console.error('Error adding agent:', error);
      toast.error('Failed to add agent to project');
    } finally {
      setUpdating(false);
    }
  };

  // Remove an agent from the project
  const removeAgent = async (agentTypeId: string) => {
    setUpdating(true);
    try {
      const response = await fetch(`/api/project-agents/${projectId}/${agentTypeId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to remove agent');
      }

      // Refresh project agents
      const agentsResponse = await fetch(`/api/project-agents/${projectId}`);
      const agentsData = await agentsResponse.json();
      setProjectAgents(agentsData.agents || []);

      toast.success('Agent removed from project');
    } catch (error) {
      console.error('Error removing agent:', error);
      toast.error('Failed to remove agent from project');
    } finally {
      setUpdating(false);
    }
  };

  // Check if an agent type is already added to the project
  const isAgentAdded = (agentTypeId: string) => {
    return projectAgents.some(agent =>
      agent.agent_type_id === agentTypeId && agent.is_active
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-full" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3, 4].map(i => (
            <Skeleton key={i} className="h-40 w-full" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Project Agents</h2>
        <p className="text-muted-foreground">
          Select which agents you want to include in your project
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {agentTypes.map(agentType => {
          const isAdded = isAgentAdded(agentType.agent_type_id);

          return (
            <Card key={agentType.agent_type_id} className={isAdded ? 'border-primary' : ''}>
              <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-2">
                <div>
                  <CardTitle className="text-lg">{agentType.name}</CardTitle>
                  <CardDescription>{agentType.role}</CardDescription>
                </div>
                {isAdded ? (
                  <BrainCircuit className="h-5 w-5 text-primary" />
                ) : (
                  <UserCircle className="h-5 w-5 text-muted-foreground" />
                )}
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {agentType.description}
                </p>
              </CardContent>
              <CardFooter>
                {isAdded ? (
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => removeAgent(agentType.agent_type_id)}
                    disabled={updating}
                  >
                    <X className="mr-2 h-4 w-4" />
                    Remove Agent
                  </Button>
                ) : (
                  <Button
                    variant="default"
                    size="sm"
                    className="w-full"
                    onClick={() => addAgent(agentType.agent_type_id)}
                    disabled={updating}
                  >
                    <UserCircle className="mr-2 h-4 w-4" />
                    Add Agent
                  </Button>
                )}
              </CardFooter>
            </Card>
          );
        })}
      </div>

      {projectAgents.length > 0 && (
        <div className="mt-8">
          <h3 className="text-xl font-semibold mb-4">Active Project Agents</h3>
          <ul className="space-y-2">
            {projectAgents.filter(agent => agent.is_active).map(agent => (
              <li key={agent.agent_id} className="flex items-center space-x-2">
                <BrainCircuit className="h-5 w-5 text-primary" />
                <span className="font-medium">{agent.name}</span>
                <span className="text-muted-foreground">({agent.role})</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
