"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+language-data@6.5.1";
exports.ids = ["vendor-chunks/@codemirror+language-data@6.5.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+language-data@6.5.1/node_modules/@codemirror/language-data/dist/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+language-data@6.5.1/node_modules/@codemirror/language-data/dist/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   languages: () => (/* binding */ languages)\n/* harmony export */ });\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n\n\nfunction legacy(parser) {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageSupport(_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.StreamLanguage.define(parser));\n}\nfunction sql(dialectName) {\n    return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-sql */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-sql@6.8.0/node_modules/@codemirror/lang-sql/dist/index.js\")).then(m => m.sql({ dialect: m[dialectName] }));\n}\n/**\nAn array of language descriptions for known language packages.\n*/\nconst languages = [\n    // New-style language modes\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"C\",\n        extensions: [\"c\", \"h\", \"ino\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-cpp */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-cpp@6.0.2/node_modules/@codemirror/lang-cpp/dist/index.js\")).then(m => m.cpp());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"C++\",\n        alias: [\"cpp\"],\n        extensions: [\"cpp\", \"c++\", \"cc\", \"cxx\", \"hpp\", \"h++\", \"hh\", \"hxx\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-cpp */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-cpp@6.0.2/node_modules/@codemirror/lang-cpp/dist/index.js\")).then(m => m.cpp());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"CQL\",\n        alias: [\"cassandra\"],\n        extensions: [\"cql\"],\n        load() { return sql(\"Cassandra\"); }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"CSS\",\n        extensions: [\"css\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-css */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-css@6.3.1/node_modules/@codemirror/lang-css/dist/index.js\")).then(m => m.css());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Go\",\n        extensions: [\"go\"],\n        load() {\n            return Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@lezer+go@1.0.0\"), __webpack_require__.e(\"vendor-chunks/@codemirror+lang-go@6.0.1\")]).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-go */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-go@6.0.1/node_modules/@codemirror/lang-go/dist/index.js\")).then(m => m.go());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"HTML\",\n        alias: [\"xhtml\"],\n        extensions: [\"html\", \"htm\", \"handlebars\", \"hbs\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-html */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-html@6.4.9/node_modules/@codemirror/lang-html/dist/index.js\")).then(m => m.html());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Java\",\n        extensions: [\"java\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-java */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-java@6.0.1/node_modules/@codemirror/lang-java/dist/index.js\")).then(m => m.java());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"JavaScript\",\n        alias: [\"ecmascript\", \"js\", \"node\"],\n        extensions: [\"js\", \"mjs\", \"cjs\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-javascript */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-javascript@6.2.3/node_modules/@codemirror/lang-javascript/dist/index.js\")).then(m => m.javascript());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"JSON\",\n        alias: [\"json5\"],\n        extensions: [\"json\", \"map\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-json */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-json@6.0.1/node_modules/@codemirror/lang-json/dist/index.js\")).then(m => m.json());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"JSX\",\n        extensions: [\"jsx\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-javascript */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-javascript@6.2.3/node_modules/@codemirror/lang-javascript/dist/index.js\")).then(m => m.javascript({ jsx: true }));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"LESS\",\n        extensions: [\"less\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-less */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-less@6.0.2/node_modules/@codemirror/lang-less/dist/index.js\")).then(m => m.less());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Liquid\",\n        extensions: [\"liquid\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-liquid */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-liquid@6.2.3/node_modules/@codemirror/lang-liquid/dist/index.js\")).then(m => m.liquid());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"MariaDB SQL\",\n        load() { return sql(\"MariaSQL\"); }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Markdown\",\n        extensions: [\"md\", \"markdown\", \"mkd\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-markdown */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-markdown@6.3.2/node_modules/@codemirror/lang-markdown/dist/index.js\")).then(m => m.markdown());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"MS SQL\",\n        load() { return sql(\"MSSQL\"); }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"MySQL\",\n        load() { return sql(\"MySQL\"); }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"PHP\",\n        extensions: [\"php\", \"php3\", \"php4\", \"php5\", \"php7\", \"phtml\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-php */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-php@6.0.1/node_modules/@codemirror/lang-php/dist/index.js\")).then(m => m.php());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"PLSQL\",\n        extensions: [\"pls\"],\n        load() { return sql(\"PLSQL\"); }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"PostgreSQL\",\n        load() { return sql(\"PostgreSQL\"); }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Python\",\n        extensions: [\"BUILD\", \"bzl\", \"py\", \"pyw\"],\n        filename: /^(BUCK|BUILD)$/,\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-python */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-python@6.2.0/node_modules/@codemirror/lang-python/dist/index.js\")).then(m => m.python());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Rust\",\n        extensions: [\"rs\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-rust */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-rust@6.0.1/node_modules/@codemirror/lang-rust/dist/index.js\")).then(m => m.rust());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Sass\",\n        extensions: [\"sass\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-sass */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-sass@6.0.2/node_modules/@codemirror/lang-sass/dist/index.js\")).then(m => m.sass({ indented: true }));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"SCSS\",\n        extensions: [\"scss\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-sass */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-sass@6.0.2/node_modules/@codemirror/lang-sass/dist/index.js\")).then(m => m.sass());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"SQL\",\n        extensions: [\"sql\"],\n        load() { return sql(\"StandardSQL\"); }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"SQLite\",\n        load() { return sql(\"SQLite\"); }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"TSX\",\n        extensions: [\"tsx\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-javascript */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-javascript@6.2.3/node_modules/@codemirror/lang-javascript/dist/index.js\")).then(m => m.javascript({ jsx: true, typescript: true }));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"TypeScript\",\n        alias: [\"ts\"],\n        extensions: [\"ts\", \"mts\", \"cts\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-javascript */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-javascript@6.2.3/node_modules/@codemirror/lang-javascript/dist/index.js\")).then(m => m.javascript({ typescript: true }));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"WebAssembly\",\n        extensions: [\"wat\", \"wast\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-wast */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-wast@6.0.2/node_modules/@codemirror/lang-wast/dist/index.js\")).then(m => m.wast());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"XML\",\n        alias: [\"rss\", \"wsdl\", \"xsd\"],\n        extensions: [\"xml\", \"xsl\", \"xsd\", \"svg\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-xml */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-xml@6.1.0/node_modules/@codemirror/lang-xml/dist/index.js\")).then(m => m.xml());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"YAML\",\n        alias: [\"yml\"],\n        extensions: [\"yaml\", \"yml\"],\n        load() {\n            return Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/@lezer+yaml@1.0.3\"), __webpack_require__.e(\"vendor-chunks/@codemirror+lang-yaml@6.1.2\")]).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-yaml */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-yaml@6.1.2/node_modules/@codemirror/lang-yaml/dist/index.js\")).then(m => m.yaml());\n        }\n    }),\n    // Legacy modes ported from CodeMirror 5\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"APL\",\n        extensions: [\"dyalog\", \"apl\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/apl */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/apl.js\")).then(m => legacy(m.apl));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"PGP\",\n        alias: [\"asciiarmor\"],\n        extensions: [\"asc\", \"pgp\", \"sig\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/asciiarmor */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/asciiarmor.js\")).then(m => legacy(m.asciiArmor));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"ASN.1\",\n        extensions: [\"asn\", \"asn1\"],\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/asn1 */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/asn1.js\")).then(m => legacy(m.asn1({})));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Asterisk\",\n        filename: /^extensions\\.conf$/i,\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/asterisk */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/asterisk.js\")).then(m => legacy(m.asterisk));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Brainfuck\",\n        extensions: [\"b\", \"bf\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/brainfuck */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/brainfuck.js\")).then(m => legacy(m.brainfuck));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Cobol\",\n        extensions: [\"cob\", \"cpy\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/cobol */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/cobol.js\")).then(m => legacy(m.cobol));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"C#\",\n        alias: [\"csharp\", \"cs\"],\n        extensions: [\"cs\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/clike */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/clike.js\")).then(m => legacy(m.csharp));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Clojure\",\n        extensions: [\"clj\", \"cljc\", \"cljx\"],\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/clojure */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/clojure.js\")).then(m => legacy(m.clojure));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"ClojureScript\",\n        extensions: [\"cljs\"],\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/clojure */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/clojure.js\")).then(m => legacy(m.clojure));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Closure Stylesheets (GSS)\",\n        extensions: [\"gss\"],\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/css */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/css.js\")).then(m => legacy(m.gss));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"CMake\",\n        extensions: [\"cmake\", \"cmake.in\"],\n        filename: /^CMakeLists\\.txt$/,\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/cmake */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/cmake.js\")).then(m => legacy(m.cmake));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"CoffeeScript\",\n        alias: [\"coffee\", \"coffee-script\"],\n        extensions: [\"coffee\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/coffeescript */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/coffeescript.js\")).then(m => legacy(m.coffeeScript));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Common Lisp\",\n        alias: [\"lisp\"],\n        extensions: [\"cl\", \"lisp\", \"el\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/commonlisp */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/commonlisp.js\")).then(m => legacy(m.commonLisp));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Cypher\",\n        extensions: [\"cyp\", \"cypher\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/cypher */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/cypher.js\")).then(m => legacy(m.cypher));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Cython\",\n        extensions: [\"pyx\", \"pxd\", \"pxi\"],\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/python */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/python.js\")).then(m => legacy(m.cython));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Crystal\",\n        extensions: [\"cr\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/crystal */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/crystal.js\")).then(m => legacy(m.crystal));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"D\",\n        extensions: [\"d\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/d */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/d.js\")).then(m => legacy(m.d));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Dart\",\n        extensions: [\"dart\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/clike */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/clike.js\")).then(m => legacy(m.dart));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"diff\",\n        extensions: [\"diff\", \"patch\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/diff */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/diff.js\")).then(m => legacy(m.diff));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Dockerfile\",\n        filename: /^Dockerfile$/,\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/dockerfile */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/dockerfile.js\")).then(m => legacy(m.dockerFile));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"DTD\",\n        extensions: [\"dtd\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/dtd */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/dtd.js\")).then(m => legacy(m.dtd));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Dylan\",\n        extensions: [\"dylan\", \"dyl\", \"intr\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/dylan */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/dylan.js\")).then(m => legacy(m.dylan));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"EBNF\",\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/ebnf */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/ebnf.js\")).then(m => legacy(m.ebnf));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"ECL\",\n        extensions: [\"ecl\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/ecl */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/ecl.js\")).then(m => legacy(m.ecl));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"edn\",\n        extensions: [\"edn\"],\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/clojure */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/clojure.js\")).then(m => legacy(m.clojure));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Eiffel\",\n        extensions: [\"e\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/eiffel */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/eiffel.js\")).then(m => legacy(m.eiffel));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Elm\",\n        extensions: [\"elm\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/elm */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/elm.js\")).then(m => legacy(m.elm));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Erlang\",\n        extensions: [\"erl\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/erlang */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/erlang.js\")).then(m => legacy(m.erlang));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Esper\",\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/sql */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/sql.js\")).then(m => legacy(m.esper));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Factor\",\n        extensions: [\"factor\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/factor */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/factor.js\")).then(m => legacy(m.factor));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"FCL\",\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/fcl */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/fcl.js\")).then(m => legacy(m.fcl));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Forth\",\n        extensions: [\"forth\", \"fth\", \"4th\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/forth */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/forth.js\")).then(m => legacy(m.forth));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Fortran\",\n        extensions: [\"f\", \"for\", \"f77\", \"f90\", \"f95\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/fortran */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/fortran.js\")).then(m => legacy(m.fortran));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"F#\",\n        alias: [\"fsharp\"],\n        extensions: [\"fs\"],\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/mllike */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/mllike.js\")).then(m => legacy(m.fSharp));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Gas\",\n        extensions: [\"s\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/gas */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/gas.js\")).then(m => legacy(m.gas));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Gherkin\",\n        extensions: [\"feature\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/gherkin */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/gherkin.js\")).then(m => legacy(m.gherkin));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Groovy\",\n        extensions: [\"groovy\", \"gradle\"],\n        filename: /^Jenkinsfile$/,\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/groovy */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/groovy.js\")).then(m => legacy(m.groovy));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Haskell\",\n        extensions: [\"hs\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/haskell */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/haskell.js\")).then(m => legacy(m.haskell));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Haxe\",\n        extensions: [\"hx\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/haxe */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/haxe.js\")).then(m => legacy(m.haxe));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"HXML\",\n        extensions: [\"hxml\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/haxe */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/haxe.js\")).then(m => legacy(m.hxml));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"HTTP\",\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/http */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/http.js\")).then(m => legacy(m.http));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"IDL\",\n        extensions: [\"pro\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/idl */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/idl.js\")).then(m => legacy(m.idl));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"JSON-LD\",\n        alias: [\"jsonld\"],\n        extensions: [\"jsonld\"],\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/javascript */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/javascript.js\")).then(m => legacy(m.jsonld));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Jinja2\",\n        extensions: [\"j2\", \"jinja\", \"jinja2\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/jinja2 */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/jinja2.js\")).then(m => legacy(m.jinja2));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Julia\",\n        extensions: [\"jl\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/julia */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/julia.js\")).then(m => legacy(m.julia));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Kotlin\",\n        extensions: [\"kt\", \"kts\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/clike */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/clike.js\")).then(m => legacy(m.kotlin));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"LiveScript\",\n        alias: [\"ls\"],\n        extensions: [\"ls\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/livescript */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/livescript.js\")).then(m => legacy(m.liveScript));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Lua\",\n        extensions: [\"lua\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/lua */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/lua.js\")).then(m => legacy(m.lua));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"mIRC\",\n        extensions: [\"mrc\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/mirc */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/mirc.js\")).then(m => legacy(m.mirc));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Mathematica\",\n        extensions: [\"m\", \"nb\", \"wl\", \"wls\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/mathematica */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/mathematica.js\")).then(m => legacy(m.mathematica));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Modelica\",\n        extensions: [\"mo\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/modelica */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/modelica.js\")).then(m => legacy(m.modelica));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"MUMPS\",\n        extensions: [\"mps\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/mumps */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/mumps.js\")).then(m => legacy(m.mumps));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Mbox\",\n        extensions: [\"mbox\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/mbox */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/mbox.js\")).then(m => legacy(m.mbox));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Nginx\",\n        filename: /nginx.*\\.conf$/i,\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/nginx */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/nginx.js\")).then(m => legacy(m.nginx));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"NSIS\",\n        extensions: [\"nsh\", \"nsi\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/nsis */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/nsis.js\")).then(m => legacy(m.nsis));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"NTriples\",\n        extensions: [\"nt\", \"nq\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/ntriples */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/ntriples.js\")).then(m => legacy(m.ntriples));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Objective-C\",\n        alias: [\"objective-c\", \"objc\"],\n        extensions: [\"m\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/clike */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/clike.js\")).then(m => legacy(m.objectiveC));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Objective-C++\",\n        alias: [\"objective-c++\", \"objc++\"],\n        extensions: [\"mm\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/clike */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/clike.js\")).then(m => legacy(m.objectiveCpp));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"OCaml\",\n        extensions: [\"ml\", \"mli\", \"mll\", \"mly\"],\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/mllike */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/mllike.js\")).then(m => legacy(m.oCaml));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Octave\",\n        extensions: [\"m\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/octave */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/octave.js\")).then(m => legacy(m.octave));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Oz\",\n        extensions: [\"oz\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/oz */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/oz.js\")).then(m => legacy(m.oz));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Pascal\",\n        extensions: [\"p\", \"pas\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/pascal */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/pascal.js\")).then(m => legacy(m.pascal));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Perl\",\n        extensions: [\"pl\", \"pm\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/perl */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/perl.js\")).then(m => legacy(m.perl));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Pig\",\n        extensions: [\"pig\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/pig */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/pig.js\")).then(m => legacy(m.pig));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"PowerShell\",\n        extensions: [\"ps1\", \"psd1\", \"psm1\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/powershell */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/powershell.js\")).then(m => legacy(m.powerShell));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Properties files\",\n        alias: [\"ini\", \"properties\"],\n        extensions: [\"properties\", \"ini\", \"in\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/properties */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/properties.js\")).then(m => legacy(m.properties));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"ProtoBuf\",\n        extensions: [\"proto\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/protobuf */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/protobuf.js\")).then(m => legacy(m.protobuf));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Pug\",\n        alias: [\"jade\"],\n        extensions: [\"pug\", \"jade\"],\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/pug */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/pug.js\")).then(m => legacy(m.pug));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Puppet\",\n        extensions: [\"pp\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/puppet */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/puppet.js\")).then(m => legacy(m.puppet));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Q\",\n        extensions: [\"q\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/q */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/q.js\")).then(m => legacy(m.q));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"R\",\n        alias: [\"rscript\"],\n        extensions: [\"r\", \"R\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/r */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/r.js\")).then(m => legacy(m.r));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"RPM Changes\",\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/rpm */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/rpm.js\")).then(m => legacy(m.rpmChanges));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"RPM Spec\",\n        extensions: [\"spec\"],\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/rpm */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/rpm.js\")).then(m => legacy(m.rpmSpec));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Ruby\",\n        alias: [\"jruby\", \"macruby\", \"rake\", \"rb\", \"rbx\"],\n        extensions: [\"rb\"],\n        filename: /^(Gemfile|Rakefile)$/,\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/ruby */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/ruby.js\")).then(m => legacy(m.ruby));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"SAS\",\n        extensions: [\"sas\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/sas */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/sas.js\")).then(m => legacy(m.sas));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Scala\",\n        extensions: [\"scala\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/clike */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/clike.js\")).then(m => legacy(m.scala));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Scheme\",\n        extensions: [\"scm\", \"ss\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/scheme */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/scheme.js\")).then(m => legacy(m.scheme));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Shell\",\n        alias: [\"bash\", \"sh\", \"zsh\"],\n        extensions: [\"sh\", \"ksh\", \"bash\"],\n        filename: /^PKGBUILD$/,\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/shell */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/shell.js\")).then(m => legacy(m.shell));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Sieve\",\n        extensions: [\"siv\", \"sieve\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/sieve */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/sieve.js\")).then(m => legacy(m.sieve));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Smalltalk\",\n        extensions: [\"st\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/smalltalk */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/smalltalk.js\")).then(m => legacy(m.smalltalk));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Solr\",\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/solr */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/solr.js\")).then(m => legacy(m.solr));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"SML\",\n        extensions: [\"sml\", \"sig\", \"fun\", \"smackspec\"],\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/mllike */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/mllike.js\")).then(m => legacy(m.sml));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"SPARQL\",\n        alias: [\"sparul\"],\n        extensions: [\"rq\", \"sparql\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/sparql */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/sparql.js\")).then(m => legacy(m.sparql));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Spreadsheet\",\n        alias: [\"excel\", \"formula\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/spreadsheet */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/spreadsheet.js\")).then(m => legacy(m.spreadsheet));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Squirrel\",\n        extensions: [\"nut\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/clike */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/clike.js\")).then(m => legacy(m.squirrel));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Stylus\",\n        extensions: [\"styl\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/stylus */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/stylus.js\")).then(m => legacy(m.stylus));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Swift\",\n        extensions: [\"swift\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/swift */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/swift.js\")).then(m => legacy(m.swift));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"sTeX\",\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/stex */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/stex.js\")).then(m => legacy(m.stex));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"LaTeX\",\n        alias: [\"tex\"],\n        extensions: [\"text\", \"ltx\", \"tex\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/stex */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/stex.js\")).then(m => legacy(m.stex));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"SystemVerilog\",\n        extensions: [\"v\", \"sv\", \"svh\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/verilog */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/verilog.js\")).then(m => legacy(m.verilog));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Tcl\",\n        extensions: [\"tcl\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/tcl */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/tcl.js\")).then(m => legacy(m.tcl));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Textile\",\n        extensions: [\"textile\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/textile */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/textile.js\")).then(m => legacy(m.textile));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"TiddlyWiki\",\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/tiddlywiki */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/tiddlywiki.js\")).then(m => legacy(m.tiddlyWiki));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Tiki wiki\",\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/tiki */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/tiki.js\")).then(m => legacy(m.tiki));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"TOML\",\n        extensions: [\"toml\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/toml */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/toml.js\")).then(m => legacy(m.toml));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Troff\",\n        extensions: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/troff */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/troff.js\")).then(m => legacy(m.troff));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"TTCN\",\n        extensions: [\"ttcn\", \"ttcn3\", \"ttcnpp\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/ttcn */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/ttcn.js\")).then(m => legacy(m.ttcn));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"TTCN_CFG\",\n        extensions: [\"cfg\"],\n        load() {\n            return __webpack_require__.e(/*! import() */ \"vendor-chunks/@codemirror+legacy-modes@6.5.1\").then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/ttcn-cfg */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/ttcn-cfg.js\")).then(m => legacy(m.ttcnCfg));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Turtle\",\n        extensions: [\"ttl\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/turtle */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/turtle.js\")).then(m => legacy(m.turtle));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Web IDL\",\n        extensions: [\"webidl\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/webidl */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/webidl.js\")).then(m => legacy(m.webIDL));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"VB.NET\",\n        extensions: [\"vb\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/vb */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/vb.js\")).then(m => legacy(m.vb));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"VBScript\",\n        extensions: [\"vbs\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/vbscript */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/vbscript.js\")).then(m => legacy(m.vbScript));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Velocity\",\n        extensions: [\"vtl\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/velocity */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/velocity.js\")).then(m => legacy(m.velocity));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Verilog\",\n        extensions: [\"v\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/verilog */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/verilog.js\")).then(m => legacy(m.verilog));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"VHDL\",\n        extensions: [\"vhd\", \"vhdl\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/vhdl */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/vhdl.js\")).then(m => legacy(m.vhdl));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"XQuery\",\n        extensions: [\"xy\", \"xquery\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/xquery */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/xquery.js\")).then(m => legacy(m.xQuery));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Yacas\",\n        extensions: [\"ys\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/yacas */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/yacas.js\")).then(m => legacy(m.yacas));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Z80\",\n        extensions: [\"z80\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/z80 */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/z80.js\")).then(m => legacy(m.z80));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"MscGen\",\n        extensions: [\"mscgen\", \"mscin\", \"msc\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/mscgen */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/mscgen.js\")).then(m => legacy(m.mscgen));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Xù\",\n        extensions: [\"xu\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/mscgen */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/mscgen.js\")).then(m => legacy(m.xu));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"MsGenny\",\n        extensions: [\"msgenny\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/legacy-modes/mode/mscgen */ \"(ssr)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/mscgen.js\")).then(m => legacy(m.msgenny));\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Vue\",\n        extensions: [\"vue\"],\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-vue */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-vue@0.1.3/node_modules/@codemirror/lang-vue/dist/index.js\")).then(m => m.vue());\n        }\n    }),\n    /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.LanguageDescription.of({\n        name: \"Angular Template\",\n        load() {\n            return Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @codemirror/lang-angular */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-angular@0.1.4/node_modules/@codemirror/lang-angular/dist/index.js\")).then(m => m.angular());\n        }\n    })\n];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvZGVtaXJyb3IrbGFuZ3VhZ2UtZGF0YUA2LjUuMS9ub2RlX21vZHVsZXMvQGNvZGVtaXJyb3IvbGFuZ3VhZ2UtZGF0YS9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTRGOztBQUU1RjtBQUNBLGVBQWUsaUVBQWUsQ0FBQyxnRUFBYztBQUM3QztBQUNBO0FBQ0EsV0FBVyx5TkFBOEIsbUJBQW1CLHlCQUF5QjtBQUNyRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix5TkFBOEI7QUFDakQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix5TkFBOEI7QUFDakQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHlOQUE4QjtBQUNqRDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw0VUFBNkI7QUFDaEQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw0TkFBK0I7QUFDbEQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNE5BQStCO0FBQ2xEO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsOE9BQXFDO0FBQ3hEO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNE5BQStCO0FBQ2xEO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDhPQUFxQywwQkFBMEIsV0FBVztBQUM3RjtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw0TkFBK0I7QUFDbEQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsa09BQWlDO0FBQ3BEO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQSxpQkFBaUI7QUFDakIsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHdPQUFtQztBQUN0RDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0EsaUJBQWlCO0FBQ2pCLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0EsaUJBQWlCO0FBQ2pCLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix5TkFBOEI7QUFDakQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0EsaUJBQWlCO0FBQ2pCLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGtPQUFpQztBQUNwRDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw0TkFBK0I7QUFDbEQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNE5BQStCLG9CQUFvQixnQkFBZ0I7QUFDdEY7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNE5BQStCO0FBQ2xEO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQixLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBLGlCQUFpQjtBQUNqQixLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsOE9BQXFDLDBCQUEwQiw2QkFBNkI7QUFDL0c7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw4T0FBcUMsMEJBQTBCLGtCQUFrQjtBQUNwRztBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw0TkFBK0I7QUFDbEQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix5TkFBOEI7QUFDakQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixzVkFBK0I7QUFDbEQ7QUFDQSxLQUFLO0FBQ0w7QUFDQSxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw0T0FBMkM7QUFDOUQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiwwUEFBa0Q7QUFDckU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsbVNBQTRDLDJCQUEyQjtBQUMxRjtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixzUEFBZ0Q7QUFDbkU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsd1BBQWlEO0FBQ3BFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGdQQUE2QztBQUNoRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGdQQUE2QztBQUNoRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix5U0FBK0M7QUFDbEU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIseVNBQStDO0FBQ2xFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGlTQUEyQztBQUM5RDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGdQQUE2QztBQUNoRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDhQQUFvRDtBQUN2RTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDBQQUFrRDtBQUNyRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixrUEFBOEM7QUFDakU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsdVNBQThDO0FBQ2pFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLG9QQUErQztBQUNsRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix3T0FBeUM7QUFDNUQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsZ1BBQTZDO0FBQ2hFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDhPQUE0QztBQUMvRDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiwwUEFBa0Q7QUFDckU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNE9BQTJDO0FBQzlEO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGdQQUE2QztBQUNoRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQSxtQkFBbUIsOE9BQTRDO0FBQy9EO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDRPQUEyQztBQUM5RDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix5U0FBK0M7QUFDbEU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsa1BBQThDO0FBQ2pFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDRPQUEyQztBQUM5RDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixrUEFBOEM7QUFDakU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0EsbUJBQW1CLGlTQUEyQztBQUM5RDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixrUEFBOEM7QUFDakU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0EsbUJBQW1CLDRPQUEyQztBQUM5RDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixnUEFBNkM7QUFDaEU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsb1BBQStDO0FBQ2xFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsdVNBQThDO0FBQ2pFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDRPQUEyQztBQUM5RDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixvUEFBK0M7QUFDbEU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixrUEFBOEM7QUFDakU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsb1BBQStDO0FBQ2xFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDhPQUE0QztBQUMvRDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw4T0FBNEM7QUFDL0Q7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0EsbUJBQW1CLDhPQUE0QztBQUMvRDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw0T0FBMkM7QUFDOUQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiwrU0FBa0Q7QUFDckU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsa1BBQThDO0FBQ2pFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGdQQUE2QztBQUNoRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixnUEFBNkM7QUFDaEU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiwwUEFBa0Q7QUFDckU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNE9BQTJDO0FBQzlEO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDhPQUE0QztBQUMvRDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw0UEFBbUQ7QUFDdEU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsc1BBQWdEO0FBQ25FO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGdQQUE2QztBQUNoRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw4T0FBNEM7QUFDL0Q7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsZ1BBQTZDO0FBQ2hFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDhPQUE0QztBQUMvRDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixzUEFBZ0Q7QUFDbkU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixnUEFBNkM7QUFDaEU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixnUEFBNkM7QUFDaEU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsdVNBQThDO0FBQ2pFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGtQQUE4QztBQUNqRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiwwT0FBMEM7QUFDN0Q7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsa1BBQThDO0FBQ2pFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDhPQUE0QztBQUMvRDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw0T0FBMkM7QUFDOUQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsMFBBQWtEO0FBQ3JFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsMFBBQWtEO0FBQ3JFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHNQQUFnRDtBQUNuRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGlTQUEyQztBQUM5RDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixrUEFBOEM7QUFDakU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsd09BQXlDO0FBQzVEO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsd09BQXlDO0FBQzVEO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBLG1CQUFtQixpU0FBMkM7QUFDOUQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsaVNBQTJDO0FBQzlEO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw4T0FBNEM7QUFDL0Q7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNE9BQTJDO0FBQzlEO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGdQQUE2QztBQUNoRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixrUEFBOEM7QUFDakU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGdQQUE2QztBQUNoRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixnUEFBNkM7QUFDaEU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsd1BBQWlEO0FBQ3BFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBLG1CQUFtQiw4T0FBNEM7QUFDL0Q7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsdVNBQThDO0FBQ2pFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsa1BBQThDO0FBQ2pFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDRQQUFtRDtBQUN0RTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixnUEFBNkM7QUFDaEU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsa1BBQThDO0FBQ2pFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGdQQUE2QztBQUNoRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQSxtQkFBbUIsOE9BQTRDO0FBQy9EO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsOE9BQTRDO0FBQy9EO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLG9QQUErQztBQUNsRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw0T0FBMkM7QUFDOUQ7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsb1BBQStDO0FBQ2xFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBLG1CQUFtQiwwUEFBa0Q7QUFDckU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0EsbUJBQW1CLDhPQUE0QztBQUMvRDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiw4T0FBNEM7QUFDL0Q7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsZ1BBQTZDO0FBQ2hFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLDhPQUE0QztBQUMvRDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiwyU0FBZ0Q7QUFDbkU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsa1BBQThDO0FBQ2pFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGtQQUE4QztBQUNqRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQiwwT0FBMEM7QUFDN0Q7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsc1BBQWdEO0FBQ25FO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHNQQUFnRDtBQUNuRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixvUEFBK0M7QUFDbEU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsOE9BQTRDO0FBQy9EO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGtQQUE4QztBQUNqRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixnUEFBNkM7QUFDaEU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsNE9BQTJDO0FBQzlEO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGtQQUE4QztBQUNqRTtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixrUEFBOEM7QUFDakU7QUFDQSxLQUFLO0FBQ0wsaUJBQWlCLHFFQUFtQjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsa1BBQThDO0FBQ2pFO0FBQ0EsS0FBSztBQUNMLGlCQUFpQixxRUFBbUI7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHlOQUE4QjtBQUNqRDtBQUNBLEtBQUs7QUFDTCxpQkFBaUIscUVBQW1CO0FBQ3BDO0FBQ0E7QUFDQSxtQkFBbUIscU9BQWtDO0FBQ3JEO0FBQ0EsS0FBSztBQUNMOztBQUVxQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbmRyZVxcT25lRHJpdmVcXERlc2t0b3BcXEdpdGh1YiBSZXBvc2l0b3JpZXNcXGRlbW9cXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAY29kZW1pcnJvcitsYW5ndWFnZS1kYXRhQDYuNS4xXFxub2RlX21vZHVsZXNcXEBjb2RlbWlycm9yXFxsYW5ndWFnZS1kYXRhXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMYW5ndWFnZURlc2NyaXB0aW9uLCBMYW5ndWFnZVN1cHBvcnQsIFN0cmVhbUxhbmd1YWdlIH0gZnJvbSAnQGNvZGVtaXJyb3IvbGFuZ3VhZ2UnO1xuXG5mdW5jdGlvbiBsZWdhY3kocGFyc2VyKSB7XG4gICAgcmV0dXJuIG5ldyBMYW5ndWFnZVN1cHBvcnQoU3RyZWFtTGFuZ3VhZ2UuZGVmaW5lKHBhcnNlcikpO1xufVxuZnVuY3Rpb24gc3FsKGRpYWxlY3ROYW1lKSB7XG4gICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy1zcWwnKS50aGVuKG0gPT4gbS5zcWwoeyBkaWFsZWN0OiBtW2RpYWxlY3ROYW1lXSB9KSk7XG59XG4vKipcbkFuIGFycmF5IG9mIGxhbmd1YWdlIGRlc2NyaXB0aW9ucyBmb3Iga25vd24gbGFuZ3VhZ2UgcGFja2FnZXMuXG4qL1xuY29uc3QgbGFuZ3VhZ2VzID0gW1xuICAgIC8vIE5ldy1zdHlsZSBsYW5ndWFnZSBtb2Rlc1xuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJDXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImNcIiwgXCJoXCIsIFwiaW5vXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy1jcHAnKS50aGVuKG0gPT4gbS5jcHAoKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiQysrXCIsXG4gICAgICAgIGFsaWFzOiBbXCJjcHBcIl0sXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImNwcFwiLCBcImMrK1wiLCBcImNjXCIsIFwiY3h4XCIsIFwiaHBwXCIsIFwiaCsrXCIsIFwiaGhcIiwgXCJoeHhcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sYW5nLWNwcCcpLnRoZW4obSA9PiBtLmNwcCgpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJDUUxcIixcbiAgICAgICAgYWxpYXM6IFtcImNhc3NhbmRyYVwiXSxcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiY3FsXCJdLFxuICAgICAgICBsb2FkKCkgeyByZXR1cm4gc3FsKFwiQ2Fzc2FuZHJhXCIpOyB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkNTU1wiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJjc3NcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sYW5nLWNzcycpLnRoZW4obSA9PiBtLmNzcygpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJHb1wiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJnb1wiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xhbmctZ28nKS50aGVuKG0gPT4gbS5nbygpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJIVE1MXCIsXG4gICAgICAgIGFsaWFzOiBbXCJ4aHRtbFwiXSxcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiaHRtbFwiLCBcImh0bVwiLCBcImhhbmRsZWJhcnNcIiwgXCJoYnNcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sYW5nLWh0bWwnKS50aGVuKG0gPT4gbS5odG1sKCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkphdmFcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiamF2YVwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xhbmctamF2YScpLnRoZW4obSA9PiBtLmphdmEoKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiSmF2YVNjcmlwdFwiLFxuICAgICAgICBhbGlhczogW1wiZWNtYXNjcmlwdFwiLCBcImpzXCIsIFwibm9kZVwiXSxcbiAgICAgICAgZXh0ZW5zaW9uczogW1wianNcIiwgXCJtanNcIiwgXCJjanNcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sYW5nLWphdmFzY3JpcHQnKS50aGVuKG0gPT4gbS5qYXZhc2NyaXB0KCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkpTT05cIixcbiAgICAgICAgYWxpYXM6IFtcImpzb241XCJdLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJqc29uXCIsIFwibWFwXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy1qc29uJykudGhlbihtID0+IG0uanNvbigpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJKU1hcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wianN4XCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy1qYXZhc2NyaXB0JykudGhlbihtID0+IG0uamF2YXNjcmlwdCh7IGpzeDogdHJ1ZSB9KSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiTEVTU1wiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJsZXNzXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy1sZXNzJykudGhlbihtID0+IG0ubGVzcygpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJMaXF1aWRcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wibGlxdWlkXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy1saXF1aWQnKS50aGVuKG0gPT4gbS5saXF1aWQoKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiTWFyaWFEQiBTUUxcIixcbiAgICAgICAgbG9hZCgpIHsgcmV0dXJuIHNxbChcIk1hcmlhU1FMXCIpOyB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIk1hcmtkb3duXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcIm1kXCIsIFwibWFya2Rvd25cIiwgXCJta2RcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sYW5nLW1hcmtkb3duJykudGhlbihtID0+IG0ubWFya2Rvd24oKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiTVMgU1FMXCIsXG4gICAgICAgIGxvYWQoKSB7IHJldHVybiBzcWwoXCJNU1NRTFwiKTsgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJNeVNRTFwiLFxuICAgICAgICBsb2FkKCkgeyByZXR1cm4gc3FsKFwiTXlTUUxcIik7IH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiUEhQXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcInBocFwiLCBcInBocDNcIiwgXCJwaHA0XCIsIFwicGhwNVwiLCBcInBocDdcIiwgXCJwaHRtbFwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xhbmctcGhwJykudGhlbihtID0+IG0ucGhwKCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlBMU1FMXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcInBsc1wiXSxcbiAgICAgICAgbG9hZCgpIHsgcmV0dXJuIHNxbChcIlBMU1FMXCIpOyB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlBvc3RncmVTUUxcIixcbiAgICAgICAgbG9hZCgpIHsgcmV0dXJuIHNxbChcIlBvc3RncmVTUUxcIik7IH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiUHl0aG9uXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcIkJVSUxEXCIsIFwiYnpsXCIsIFwicHlcIiwgXCJweXdcIl0sXG4gICAgICAgIGZpbGVuYW1lOiAvXihCVUNLfEJVSUxEKSQvLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy1weXRob24nKS50aGVuKG0gPT4gbS5weXRob24oKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiUnVzdFwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJyc1wiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xhbmctcnVzdCcpLnRoZW4obSA9PiBtLnJ1c3QoKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiU2Fzc1wiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJzYXNzXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy1zYXNzJykudGhlbihtID0+IG0uc2Fzcyh7IGluZGVudGVkOiB0cnVlIH0pKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJTQ1NTXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcInNjc3NcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sYW5nLXNhc3MnKS50aGVuKG0gPT4gbS5zYXNzKCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlNRTFwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJzcWxcIl0sXG4gICAgICAgIGxvYWQoKSB7IHJldHVybiBzcWwoXCJTdGFuZGFyZFNRTFwiKTsgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJTUUxpdGVcIixcbiAgICAgICAgbG9hZCgpIHsgcmV0dXJuIHNxbChcIlNRTGl0ZVwiKTsgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJUU1hcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1widHN4XCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy1qYXZhc2NyaXB0JykudGhlbihtID0+IG0uamF2YXNjcmlwdCh7IGpzeDogdHJ1ZSwgdHlwZXNjcmlwdDogdHJ1ZSB9KSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiVHlwZVNjcmlwdFwiLFxuICAgICAgICBhbGlhczogW1widHNcIl0sXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcInRzXCIsIFwibXRzXCIsIFwiY3RzXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy1qYXZhc2NyaXB0JykudGhlbihtID0+IG0uamF2YXNjcmlwdCh7IHR5cGVzY3JpcHQ6IHRydWUgfSkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIldlYkFzc2VtYmx5XCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcIndhdFwiLCBcIndhc3RcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sYW5nLXdhc3QnKS50aGVuKG0gPT4gbS53YXN0KCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlhNTFwiLFxuICAgICAgICBhbGlhczogW1wicnNzXCIsIFwid3NkbFwiLCBcInhzZFwiXSxcbiAgICAgICAgZXh0ZW5zaW9uczogW1wieG1sXCIsIFwieHNsXCIsIFwieHNkXCIsIFwic3ZnXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy14bWwnKS50aGVuKG0gPT4gbS54bWwoKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiWUFNTFwiLFxuICAgICAgICBhbGlhczogW1wieW1sXCJdLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJ5YW1sXCIsIFwieW1sXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy15YW1sJykudGhlbihtID0+IG0ueWFtbCgpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8vIExlZ2FjeSBtb2RlcyBwb3J0ZWQgZnJvbSBDb2RlTWlycm9yIDVcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiQVBMXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImR5YWxvZ1wiLCBcImFwbFwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2FwbCcpLnRoZW4obSA9PiBsZWdhY3kobS5hcGwpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJQR1BcIixcbiAgICAgICAgYWxpYXM6IFtcImFzY2lpYXJtb3JcIl0sXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImFzY1wiLCBcInBncFwiLCBcInNpZ1wiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2FzY2lpYXJtb3InKS50aGVuKG0gPT4gbGVnYWN5KG0uYXNjaWlBcm1vcikpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkFTTi4xXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImFzblwiLCBcImFzbjFcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9hc24xJykudGhlbihtID0+IGxlZ2FjeShtLmFzbjEoe30pKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiQXN0ZXJpc2tcIixcbiAgICAgICAgZmlsZW5hbWU6IC9eZXh0ZW5zaW9uc1xcLmNvbmYkL2ksXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9hc3RlcmlzaycpLnRoZW4obSA9PiBsZWdhY3kobS5hc3RlcmlzaykpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkJyYWluZnVja1wiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJiXCIsIFwiYmZcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9icmFpbmZ1Y2snKS50aGVuKG0gPT4gbGVnYWN5KG0uYnJhaW5mdWNrKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiQ29ib2xcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiY29iXCIsIFwiY3B5XCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvY29ib2wnKS50aGVuKG0gPT4gbGVnYWN5KG0uY29ib2wpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJDI1wiLFxuICAgICAgICBhbGlhczogW1wiY3NoYXJwXCIsIFwiY3NcIl0sXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImNzXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvY2xpa2UnKS50aGVuKG0gPT4gbGVnYWN5KG0uY3NoYXJwKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiQ2xvanVyZVwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJjbGpcIiwgXCJjbGpjXCIsIFwiY2xqeFwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2Nsb2p1cmUnKS50aGVuKG0gPT4gbGVnYWN5KG0uY2xvanVyZSkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkNsb2p1cmVTY3JpcHRcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiY2xqc1wiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2Nsb2p1cmUnKS50aGVuKG0gPT4gbGVnYWN5KG0uY2xvanVyZSkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkNsb3N1cmUgU3R5bGVzaGVldHMgKEdTUylcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiZ3NzXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvY3NzJykudGhlbihtID0+IGxlZ2FjeShtLmdzcykpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkNNYWtlXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImNtYWtlXCIsIFwiY21ha2UuaW5cIl0sXG4gICAgICAgIGZpbGVuYW1lOiAvXkNNYWtlTGlzdHNcXC50eHQkLyxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2NtYWtlJykudGhlbihtID0+IGxlZ2FjeShtLmNtYWtlKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiQ29mZmVlU2NyaXB0XCIsXG4gICAgICAgIGFsaWFzOiBbXCJjb2ZmZWVcIiwgXCJjb2ZmZWUtc2NyaXB0XCJdLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJjb2ZmZWVcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9jb2ZmZWVzY3JpcHQnKS50aGVuKG0gPT4gbGVnYWN5KG0uY29mZmVlU2NyaXB0KSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiQ29tbW9uIExpc3BcIixcbiAgICAgICAgYWxpYXM6IFtcImxpc3BcIl0sXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImNsXCIsIFwibGlzcFwiLCBcImVsXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvY29tbW9ubGlzcCcpLnRoZW4obSA9PiBsZWdhY3kobS5jb21tb25MaXNwKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiQ3lwaGVyXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImN5cFwiLCBcImN5cGhlclwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2N5cGhlcicpLnRoZW4obSA9PiBsZWdhY3kobS5jeXBoZXIpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJDeXRob25cIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wicHl4XCIsIFwicHhkXCIsIFwicHhpXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvcHl0aG9uJykudGhlbihtID0+IGxlZ2FjeShtLmN5dGhvbikpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkNyeXN0YWxcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiY3JcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9jcnlzdGFsJykudGhlbihtID0+IGxlZ2FjeShtLmNyeXN0YWwpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJEXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImRcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9kJykudGhlbihtID0+IGxlZ2FjeShtLmQpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJEYXJ0XCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImRhcnRcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9jbGlrZScpLnRoZW4obSA9PiBsZWdhY3kobS5kYXJ0KSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiZGlmZlwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJkaWZmXCIsIFwicGF0Y2hcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9kaWZmJykudGhlbihtID0+IGxlZ2FjeShtLmRpZmYpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJEb2NrZXJmaWxlXCIsXG4gICAgICAgIGZpbGVuYW1lOiAvXkRvY2tlcmZpbGUkLyxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2RvY2tlcmZpbGUnKS50aGVuKG0gPT4gbGVnYWN5KG0uZG9ja2VyRmlsZSkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkRURFwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJkdGRcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9kdGQnKS50aGVuKG0gPT4gbGVnYWN5KG0uZHRkKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiRHlsYW5cIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiZHlsYW5cIiwgXCJkeWxcIiwgXCJpbnRyXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvZHlsYW4nKS50aGVuKG0gPT4gbGVnYWN5KG0uZHlsYW4pKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJFQk5GXCIsXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9lYm5mJykudGhlbihtID0+IGxlZ2FjeShtLmVibmYpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJFQ0xcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiZWNsXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvZWNsJykudGhlbihtID0+IGxlZ2FjeShtLmVjbCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcImVkblwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJlZG5cIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9jbG9qdXJlJykudGhlbihtID0+IGxlZ2FjeShtLmNsb2p1cmUpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJFaWZmZWxcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiZVwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2VpZmZlbCcpLnRoZW4obSA9PiBsZWdhY3kobS5laWZmZWwpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJFbG1cIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiZWxtXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvZWxtJykudGhlbihtID0+IGxlZ2FjeShtLmVsbSkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkVybGFuZ1wiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJlcmxcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9lcmxhbmcnKS50aGVuKG0gPT4gbGVnYWN5KG0uZXJsYW5nKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiRXNwZXJcIixcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3NxbCcpLnRoZW4obSA9PiBsZWdhY3kobS5lc3BlcikpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkZhY3RvclwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJmYWN0b3JcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9mYWN0b3InKS50aGVuKG0gPT4gbGVnYWN5KG0uZmFjdG9yKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiRkNMXCIsXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9mY2wnKS50aGVuKG0gPT4gbGVnYWN5KG0uZmNsKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiRm9ydGhcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiZm9ydGhcIiwgXCJmdGhcIiwgXCI0dGhcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9mb3J0aCcpLnRoZW4obSA9PiBsZWdhY3kobS5mb3J0aCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkZvcnRyYW5cIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiZlwiLCBcImZvclwiLCBcImY3N1wiLCBcImY5MFwiLCBcImY5NVwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2ZvcnRyYW4nKS50aGVuKG0gPT4gbGVnYWN5KG0uZm9ydHJhbikpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkYjXCIsXG4gICAgICAgIGFsaWFzOiBbXCJmc2hhcnBcIl0sXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImZzXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvbWxsaWtlJykudGhlbihtID0+IGxlZ2FjeShtLmZTaGFycCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkdhc1wiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJzXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvZ2FzJykudGhlbihtID0+IGxlZ2FjeShtLmdhcykpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkdoZXJraW5cIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiZmVhdHVyZVwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2doZXJraW4nKS50aGVuKG0gPT4gbGVnYWN5KG0uZ2hlcmtpbikpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkdyb292eVwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJncm9vdnlcIiwgXCJncmFkbGVcIl0sXG4gICAgICAgIGZpbGVuYW1lOiAvXkplbmtpbnNmaWxlJC8sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9ncm9vdnknKS50aGVuKG0gPT4gbGVnYWN5KG0uZ3Jvb3Z5KSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiSGFza2VsbFwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJoc1wiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2hhc2tlbGwnKS50aGVuKG0gPT4gbGVnYWN5KG0uaGFza2VsbCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkhheGVcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiaHhcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9oYXhlJykudGhlbihtID0+IGxlZ2FjeShtLmhheGUpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJIWE1MXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImh4bWxcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9oYXhlJykudGhlbihtID0+IGxlZ2FjeShtLmh4bWwpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJIVFRQXCIsXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9odHRwJykudGhlbihtID0+IGxlZ2FjeShtLmh0dHApKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJJRExcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wicHJvXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvaWRsJykudGhlbihtID0+IGxlZ2FjeShtLmlkbCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIkpTT04tTERcIixcbiAgICAgICAgYWxpYXM6IFtcImpzb25sZFwiXSxcbiAgICAgICAgZXh0ZW5zaW9uczogW1wianNvbmxkXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvamF2YXNjcmlwdCcpLnRoZW4obSA9PiBsZWdhY3kobS5qc29ubGQpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJKaW5qYTJcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiajJcIiwgXCJqaW5qYVwiLCBcImppbmphMlwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2ppbmphMicpLnRoZW4obSA9PiBsZWdhY3kobS5qaW5qYTIpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJKdWxpYVwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJqbFwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2p1bGlhJykudGhlbihtID0+IGxlZ2FjeShtLmp1bGlhKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiS290bGluXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImt0XCIsIFwia3RzXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvY2xpa2UnKS50aGVuKG0gPT4gbGVnYWN5KG0ua290bGluKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiTGl2ZVNjcmlwdFwiLFxuICAgICAgICBhbGlhczogW1wibHNcIl0sXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImxzXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvbGl2ZXNjcmlwdCcpLnRoZW4obSA9PiBsZWdhY3kobS5saXZlU2NyaXB0KSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiTHVhXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImx1YVwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2x1YScpLnRoZW4obSA9PiBsZWdhY3kobS5sdWEpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJtSVJDXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcIm1yY1wiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL21pcmMnKS50aGVuKG0gPT4gbGVnYWN5KG0ubWlyYykpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIk1hdGhlbWF0aWNhXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcIm1cIiwgXCJuYlwiLCBcIndsXCIsIFwid2xzXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvbWF0aGVtYXRpY2EnKS50aGVuKG0gPT4gbGVnYWN5KG0ubWF0aGVtYXRpY2EpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJNb2RlbGljYVwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJtb1wiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL21vZGVsaWNhJykudGhlbihtID0+IGxlZ2FjeShtLm1vZGVsaWNhKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiTVVNUFNcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wibXBzXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvbXVtcHMnKS50aGVuKG0gPT4gbGVnYWN5KG0ubXVtcHMpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJNYm94XCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcIm1ib3hcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9tYm94JykudGhlbihtID0+IGxlZ2FjeShtLm1ib3gpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJOZ2lueFwiLFxuICAgICAgICBmaWxlbmFtZTogL25naW54LipcXC5jb25mJC9pLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvbmdpbngnKS50aGVuKG0gPT4gbGVnYWN5KG0ubmdpbngpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJOU0lTXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcIm5zaFwiLCBcIm5zaVwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL25zaXMnKS50aGVuKG0gPT4gbGVnYWN5KG0ubnNpcykpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIk5UcmlwbGVzXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcIm50XCIsIFwibnFcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9udHJpcGxlcycpLnRoZW4obSA9PiBsZWdhY3kobS5udHJpcGxlcykpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIk9iamVjdGl2ZS1DXCIsXG4gICAgICAgIGFsaWFzOiBbXCJvYmplY3RpdmUtY1wiLCBcIm9iamNcIl0sXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcIm1cIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9jbGlrZScpLnRoZW4obSA9PiBsZWdhY3kobS5vYmplY3RpdmVDKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiT2JqZWN0aXZlLUMrK1wiLFxuICAgICAgICBhbGlhczogW1wib2JqZWN0aXZlLWMrK1wiLCBcIm9iamMrK1wiXSxcbiAgICAgICAgZXh0ZW5zaW9uczogW1wibW1cIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9jbGlrZScpLnRoZW4obSA9PiBsZWdhY3kobS5vYmplY3RpdmVDcHApKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJPQ2FtbFwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJtbFwiLCBcIm1saVwiLCBcIm1sbFwiLCBcIm1seVwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL21sbGlrZScpLnRoZW4obSA9PiBsZWdhY3kobS5vQ2FtbCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIk9jdGF2ZVwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJtXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvb2N0YXZlJykudGhlbihtID0+IGxlZ2FjeShtLm9jdGF2ZSkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIk96XCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcIm96XCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvb3onKS50aGVuKG0gPT4gbGVnYWN5KG0ub3opKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJQYXNjYWxcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wicFwiLCBcInBhc1wiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3Bhc2NhbCcpLnRoZW4obSA9PiBsZWdhY3kobS5wYXNjYWwpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJQZXJsXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcInBsXCIsIFwicG1cIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9wZXJsJykudGhlbihtID0+IGxlZ2FjeShtLnBlcmwpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJQaWdcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wicGlnXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvcGlnJykudGhlbihtID0+IGxlZ2FjeShtLnBpZykpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlBvd2VyU2hlbGxcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wicHMxXCIsIFwicHNkMVwiLCBcInBzbTFcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9wb3dlcnNoZWxsJykudGhlbihtID0+IGxlZ2FjeShtLnBvd2VyU2hlbGwpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJQcm9wZXJ0aWVzIGZpbGVzXCIsXG4gICAgICAgIGFsaWFzOiBbXCJpbmlcIiwgXCJwcm9wZXJ0aWVzXCJdLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJwcm9wZXJ0aWVzXCIsIFwiaW5pXCIsIFwiaW5cIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9wcm9wZXJ0aWVzJykudGhlbihtID0+IGxlZ2FjeShtLnByb3BlcnRpZXMpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJQcm90b0J1ZlwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJwcm90b1wiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3Byb3RvYnVmJykudGhlbihtID0+IGxlZ2FjeShtLnByb3RvYnVmKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiUHVnXCIsXG4gICAgICAgIGFsaWFzOiBbXCJqYWRlXCJdLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJwdWdcIiwgXCJqYWRlXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvcHVnJykudGhlbihtID0+IGxlZ2FjeShtLnB1ZykpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlB1cHBldFwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJwcFwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3B1cHBldCcpLnRoZW4obSA9PiBsZWdhY3kobS5wdXBwZXQpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJRXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcInFcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9xJykudGhlbihtID0+IGxlZ2FjeShtLnEpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJSXCIsXG4gICAgICAgIGFsaWFzOiBbXCJyc2NyaXB0XCJdLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJyXCIsIFwiUlwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3InKS50aGVuKG0gPT4gbGVnYWN5KG0ucikpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlJQTSBDaGFuZ2VzXCIsXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9ycG0nKS50aGVuKG0gPT4gbGVnYWN5KG0ucnBtQ2hhbmdlcykpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlJQTSBTcGVjXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcInNwZWNcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9ycG0nKS50aGVuKG0gPT4gbGVnYWN5KG0ucnBtU3BlYykpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlJ1YnlcIixcbiAgICAgICAgYWxpYXM6IFtcImpydWJ5XCIsIFwibWFjcnVieVwiLCBcInJha2VcIiwgXCJyYlwiLCBcInJieFwiXSxcbiAgICAgICAgZXh0ZW5zaW9uczogW1wicmJcIl0sXG4gICAgICAgIGZpbGVuYW1lOiAvXihHZW1maWxlfFJha2VmaWxlKSQvLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvcnVieScpLnRoZW4obSA9PiBsZWdhY3kobS5ydWJ5KSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiU0FTXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcInNhc1wiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3NhcycpLnRoZW4obSA9PiBsZWdhY3kobS5zYXMpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJTY2FsYVwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJzY2FsYVwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL2NsaWtlJykudGhlbihtID0+IGxlZ2FjeShtLnNjYWxhKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiU2NoZW1lXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcInNjbVwiLCBcInNzXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvc2NoZW1lJykudGhlbihtID0+IGxlZ2FjeShtLnNjaGVtZSkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlNoZWxsXCIsXG4gICAgICAgIGFsaWFzOiBbXCJiYXNoXCIsIFwic2hcIiwgXCJ6c2hcIl0sXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcInNoXCIsIFwia3NoXCIsIFwiYmFzaFwiXSxcbiAgICAgICAgZmlsZW5hbWU6IC9eUEtHQlVJTEQkLyxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3NoZWxsJykudGhlbihtID0+IGxlZ2FjeShtLnNoZWxsKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiU2lldmVcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wic2l2XCIsIFwic2lldmVcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9zaWV2ZScpLnRoZW4obSA9PiBsZWdhY3kobS5zaWV2ZSkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlNtYWxsdGFsa1wiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJzdFwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3NtYWxsdGFsaycpLnRoZW4obSA9PiBsZWdhY3kobS5zbWFsbHRhbGspKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJTb2xyXCIsXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9zb2xyJykudGhlbihtID0+IGxlZ2FjeShtLnNvbHIpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJTTUxcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wic21sXCIsIFwic2lnXCIsIFwiZnVuXCIsIFwic21hY2tzcGVjXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvbWxsaWtlJykudGhlbihtID0+IGxlZ2FjeShtLnNtbCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlNQQVJRTFwiLFxuICAgICAgICBhbGlhczogW1wic3BhcnVsXCJdLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJycVwiLCBcInNwYXJxbFwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3NwYXJxbCcpLnRoZW4obSA9PiBsZWdhY3kobS5zcGFycWwpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJTcHJlYWRzaGVldFwiLFxuICAgICAgICBhbGlhczogW1wiZXhjZWxcIiwgXCJmb3JtdWxhXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvc3ByZWFkc2hlZXQnKS50aGVuKG0gPT4gbGVnYWN5KG0uc3ByZWFkc2hlZXQpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJTcXVpcnJlbFwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJudXRcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9jbGlrZScpLnRoZW4obSA9PiBsZWdhY3kobS5zcXVpcnJlbCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlN0eWx1c1wiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJzdHlsXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvc3R5bHVzJykudGhlbihtID0+IGxlZ2FjeShtLnN0eWx1cykpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlN3aWZ0XCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcInN3aWZ0XCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvc3dpZnQnKS50aGVuKG0gPT4gbGVnYWN5KG0uc3dpZnQpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJzVGVYXCIsXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9zdGV4JykudGhlbihtID0+IGxlZ2FjeShtLnN0ZXgpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJMYVRlWFwiLFxuICAgICAgICBhbGlhczogW1widGV4XCJdLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJ0ZXh0XCIsIFwibHR4XCIsIFwidGV4XCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvc3RleCcpLnRoZW4obSA9PiBsZWdhY3kobS5zdGV4KSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiU3lzdGVtVmVyaWxvZ1wiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJ2XCIsIFwic3ZcIiwgXCJzdmhcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS92ZXJpbG9nJykudGhlbihtID0+IGxlZ2FjeShtLnZlcmlsb2cpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJUY2xcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1widGNsXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvdGNsJykudGhlbihtID0+IGxlZ2FjeShtLnRjbCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlRleHRpbGVcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1widGV4dGlsZVwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3RleHRpbGUnKS50aGVuKG0gPT4gbGVnYWN5KG0udGV4dGlsZSkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlRpZGRseVdpa2lcIixcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3RpZGRseXdpa2knKS50aGVuKG0gPT4gbGVnYWN5KG0udGlkZGx5V2lraSkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlRpa2kgd2lraVwiLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvdGlraScpLnRoZW4obSA9PiBsZWdhY3kobS50aWtpKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiVE9NTFwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJ0b21sXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvdG9tbCcpLnRoZW4obSA9PiBsZWdhY3kobS50b21sKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiVHJvZmZcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wiMVwiLCBcIjJcIiwgXCIzXCIsIFwiNFwiLCBcIjVcIiwgXCI2XCIsIFwiN1wiLCBcIjhcIiwgXCI5XCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvdHJvZmYnKS50aGVuKG0gPT4gbGVnYWN5KG0udHJvZmYpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJUVENOXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcInR0Y25cIiwgXCJ0dGNuM1wiLCBcInR0Y25wcFwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3R0Y24nKS50aGVuKG0gPT4gbGVnYWN5KG0udHRjbikpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlRUQ05fQ0ZHXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcImNmZ1wiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3R0Y24tY2ZnJykudGhlbihtID0+IGxlZ2FjeShtLnR0Y25DZmcpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJUdXJ0bGVcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1widHRsXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvdHVydGxlJykudGhlbihtID0+IGxlZ2FjeShtLnR1cnRsZSkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIldlYiBJRExcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wid2ViaWRsXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvd2ViaWRsJykudGhlbihtID0+IGxlZ2FjeShtLndlYklETCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlZCLk5FVFwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJ2YlwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3ZiJykudGhlbihtID0+IGxlZ2FjeShtLnZiKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiVkJTY3JpcHRcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1widmJzXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvdmJzY3JpcHQnKS50aGVuKG0gPT4gbGVnYWN5KG0udmJTY3JpcHQpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJWZWxvY2l0eVwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJ2dGxcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS92ZWxvY2l0eScpLnRoZW4obSA9PiBsZWdhY3kobS52ZWxvY2l0eSkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlZlcmlsb2dcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1widlwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3Zlcmlsb2cnKS50aGVuKG0gPT4gbGVnYWN5KG0udmVyaWxvZykpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlZIRExcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1widmhkXCIsIFwidmhkbFwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3ZoZGwnKS50aGVuKG0gPT4gbGVnYWN5KG0udmhkbCkpO1xuICAgICAgICB9XG4gICAgfSksXG4gICAgLypAX19QVVJFX18qL0xhbmd1YWdlRGVzY3JpcHRpb24ub2Yoe1xuICAgICAgICBuYW1lOiBcIlhRdWVyeVwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJ4eVwiLCBcInhxdWVyeVwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3hxdWVyeScpLnRoZW4obSA9PiBsZWdhY3kobS54UXVlcnkpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJZYWNhc1wiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJ5c1wiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3lhY2FzJykudGhlbihtID0+IGxlZ2FjeShtLnlhY2FzKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiWjgwXCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcIno4MFwiXSxcbiAgICAgICAgbG9hZCgpIHtcbiAgICAgICAgICAgIHJldHVybiBpbXBvcnQoJ0Bjb2RlbWlycm9yL2xlZ2FjeS1tb2Rlcy9tb2RlL3o4MCcpLnRoZW4obSA9PiBsZWdhY3kobS56ODApKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJNc2NHZW5cIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1wibXNjZ2VuXCIsIFwibXNjaW5cIiwgXCJtc2NcIl0sXG4gICAgICAgIGxvYWQoKSB7XG4gICAgICAgICAgICByZXR1cm4gaW1wb3J0KCdAY29kZW1pcnJvci9sZWdhY3ktbW9kZXMvbW9kZS9tc2NnZW4nKS50aGVuKG0gPT4gbGVnYWN5KG0ubXNjZ2VuKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiWMO5XCIsXG4gICAgICAgIGV4dGVuc2lvbnM6IFtcInh1XCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvbXNjZ2VuJykudGhlbihtID0+IGxlZ2FjeShtLnh1KSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiTXNHZW5ueVwiLFxuICAgICAgICBleHRlbnNpb25zOiBbXCJtc2dlbm55XCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGVnYWN5LW1vZGVzL21vZGUvbXNjZ2VuJykudGhlbihtID0+IGxlZ2FjeShtLm1zZ2VubnkpKTtcbiAgICAgICAgfVxuICAgIH0pLFxuICAgIC8qQF9fUFVSRV9fKi9MYW5ndWFnZURlc2NyaXB0aW9uLm9mKHtcbiAgICAgICAgbmFtZTogXCJWdWVcIixcbiAgICAgICAgZXh0ZW5zaW9uczogW1widnVlXCJdLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy12dWUnKS50aGVuKG0gPT4gbS52dWUoKSk7XG4gICAgICAgIH1cbiAgICB9KSxcbiAgICAvKkBfX1BVUkVfXyovTGFuZ3VhZ2VEZXNjcmlwdGlvbi5vZih7XG4gICAgICAgIG5hbWU6IFwiQW5ndWxhciBUZW1wbGF0ZVwiLFxuICAgICAgICBsb2FkKCkge1xuICAgICAgICAgICAgcmV0dXJuIGltcG9ydCgnQGNvZGVtaXJyb3IvbGFuZy1hbmd1bGFyJykudGhlbihtID0+IG0uYW5ndWxhcigpKTtcbiAgICAgICAgfVxuICAgIH0pXG5dO1xuXG5leHBvcnQgeyBsYW5ndWFnZXMgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+language-data@6.5.1/node_modules/@codemirror/language-data/dist/index.js\n");

/***/ })

};
;