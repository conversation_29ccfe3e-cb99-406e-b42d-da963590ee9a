


"use client";

import React, { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MicOff } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ParticipantVideoProps {
  participant: {
    id: string;
    name: string;
    avatar: string;
    role: string;
  };
}

export function ParticipantVideo({ participant }: ParticipantVideoProps) {
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [avatarLoaded, setAvatarLoaded] = useState(false);
  const [avatarError, setAvatarError] = useState(false);

  // Simulate random mute/unmute and speaking states for demo purposes
  useEffect(() => {
    // Random initial state
    setIsMuted(Math.random() > 0.7);
    setIsVideoEnabled(Math.random() > 0.2);
    
    // Simulate speaking activity
    const speakingInterval = setInterval(() => {
      if (!isMuted) {
        setIsSpeaking(Math.random() > 0.6);
      } else {
        setIsSpeaking(false);
      }
    }, 2000 + Math.random() * 3000);
    
    return () => clearInterval(speakingInterval);
  }, [isMuted]);
  
  // Preload avatar immediately
  useEffect(() => {
    if (participant.avatar) {
      const img = new Image();
      img.src = participant.avatar;
      img.onload = () => setAvatarLoaded(true);
      img.onerror = () => setAvatarError(true);
    }
  }, [participant.avatar]);

  // Get first name for display
  const firstName = participant.name.split(' ')[0];

  return (
    <div className={cn(
      "relative aspect-video bg-black rounded-lg overflow-hidden w-full h-full max-w-full max-h-full",
      isSpeaking && !isMuted && "ring-2 ring-primary ring-offset-0"
    )}>
      {/* Always show avatar regardless of video state */}
      <div className="absolute inset-0 flex items-center justify-center bg-muted">
        <Avatar className="h-24 w-24">
          <AvatarImage 
            src={participant.avatar} 
            alt={participant.name} 
            className="object-cover"
            onError={(e) => {
              console.log(`Avatar load error for ${participant.name}:`, e);
              // Force fallback to show
              (e.target as HTMLImageElement).style.display = 'none';
            }}
          />
          <AvatarFallback>{firstName[0]}</AvatarFallback>
        </Avatar>
      </div>
      
      {/* Video overlay when enabled */}
      {isVideoEnabled && (
        <div className="absolute inset-0 flex items-center justify-center">
          <video 
            className="w-full h-full object-cover"
            autoPlay
            loop
            muted
            playsInline
            src={`/demo-assets/agent-${(parseInt(participant.id) % 3) + 1}.mp4`}
          />
        </div>
      )}
      
      {/* Participant label */}
      <div className="absolute bottom-3 left-3 bg-black/50 text-white px-2 py-1 rounded text-sm flex items-center gap-2">
        {isMuted && <MicOff className="h-3 w-3" />}
        {firstName} ({participant.role})
      </div>
      
    </div>
  );
}