"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Users, PlusCircle } from "lucide-react";

interface Project {
  id: string;
  name: string;
  description?: string;
  agentCount?: number;
  lastActivity?: string;
  active?: boolean;
}

interface ProjectCardProps {
  project?: Project;
  isCreateCard?: boolean;
}

export function ProjectCard({ project, isCreateCard }: ProjectCardProps) {
  if (isCreateCard) {
    return (
      <div className="bg-card border border-dashed rounded-md p-6 flex flex-col items-center justify-center text-center hover:border-accent/50 transition-all duration-200">
        <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center mb-4">
          <PlusCircle className="h-5 w-5 text-primary" />
        </div>
        <h3 className="font-medium mb-1">New project</h3>
        <p className="text-xs text-muted-foreground mb-4">Build with AI-powered assistance</p>
        <Button variant="outline" size="sm" asChild>
          <Link href="/project/new">
            Create
          </Link>
        </Button>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="bg-[#1a1a1a] border border-[#222222] rounded-md p-6 flex flex-col hover:border-primary/50 transition-all duration-200">
        <div className="mb-4">
          <h3 className="font-medium text-lg mb-1">Siden.ai Demo</h3>
          <p className="text-sm text-muted-foreground line-clamp-2">
            See how agents automatically handle site crashes and errors without human intervention
          </p>
        </div>
        <div className="mt-auto">
          <div className="flex items-center gap-4 text-xs text-muted-foreground mb-4">
            <div className="flex items-center gap-1">
              <Users className="h-3.5 w-3.5" />
              <span>2 agents</span>
            </div>
            <div className="flex items-center gap-1">
              <span>CEO + Developer</span>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs px-2 py-0.5 bg-primary/10 rounded-full text-primary">
              featured
            </span>
            <Button variant="ghost" size="sm" asChild className="text-xs font-normal">
              <Link href="/projects" className="flex items-center gap-1">
                View Project
                <ArrowRight className="h-3.5 w-3.5 ml-1" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-card border rounded-md p-6 flex flex-col hover:border-accent/50 transition-all duration-200">
      <div className="mb-4">
        <h3 className="font-medium text-lg mb-1">{project.name}</h3>
        {project.description ? (
          <p className="text-sm text-muted-foreground line-clamp-1">{project.description}</p>
        ) : (
          <p className="text-sm text-muted-foreground line-clamp-1">No description</p>
        )}
      </div>

      <div className="mt-auto">
        <div className="flex items-center gap-4 text-xs text-muted-foreground mb-4">
          <div className="flex items-center gap-1">
            <Users className="h-3.5 w-3.5" />
            <span>{project.agentCount || 0} agents</span>
          </div>

          {project.lastActivity && (
            <div className="flex items-center gap-1">
              <span>{project.lastActivity}</span>
            </div>
          )}
        </div>

        <div className="flex items-center justify-between">
          <span className="text-xs px-2 py-0.5 bg-accent/10 rounded-full text-primary">
            active
          </span>

          <Button variant="ghost" size="sm" asChild className="text-xs font-normal">
            <Link href={`/project/${project.id}`} className="flex items-center gap-1">
              View Project
              <ArrowRight className="h-3.5 w-3.5 ml-1" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
