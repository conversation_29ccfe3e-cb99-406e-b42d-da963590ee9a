"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-sql@6.8.0";
exports.ids = ["vendor-chunks/@codemirror+lang-sql@6.8.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-sql@6.8.0/node_modules/@codemirror/lang-sql/dist/index.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-sql@6.8.0/node_modules/@codemirror/lang-sql/dist/index.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cassandra: () => (/* binding */ Cassandra),\n/* harmony export */   MSSQL: () => (/* binding */ MSSQL),\n/* harmony export */   MariaSQL: () => (/* binding */ MariaSQL),\n/* harmony export */   MySQL: () => (/* binding */ MySQL),\n/* harmony export */   PLSQL: () => (/* binding */ PLSQL),\n/* harmony export */   PostgreSQL: () => (/* binding */ PostgreSQL),\n/* harmony export */   SQLDialect: () => (/* binding */ SQLDialect),\n/* harmony export */   SQLite: () => (/* binding */ SQLite),\n/* harmony export */   StandardSQL: () => (/* binding */ StandardSQL),\n/* harmony export */   keywordCompletionSource: () => (/* binding */ keywordCompletionSource),\n/* harmony export */   schemaCompletionSource: () => (/* binding */ schemaCompletionSource),\n/* harmony export */   sql: () => (/* binding */ sql)\n/* harmony export */ });\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js\");\n/* harmony import */ var _lezer_lr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/lr */ \"(ssr)/./node_modules/.pnpm/@lezer+lr@1.4.2/node_modules/@lezer/lr/dist/index.js\");\n/* harmony import */ var _codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/autocomplete */ \"(ssr)/./node_modules/.pnpm/@codemirror+autocomplete@6.18.6/node_modules/@codemirror/autocomplete/dist/index.js\");\n\n\n\n\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst whitespace = 36,\n  LineComment = 1,\n  BlockComment = 2,\n  String$1 = 3,\n  Number = 4,\n  Bool = 5,\n  Null = 6,\n  ParenL = 7,\n  ParenR = 8,\n  BraceL = 9,\n  BraceR = 10,\n  BracketL = 11,\n  BracketR = 12,\n  Semi = 13,\n  Dot = 14,\n  Operator = 15,\n  Punctuation = 16,\n  SpecialVar = 17,\n  Identifier = 18,\n  QuotedIdentifier = 19,\n  Keyword = 20,\n  Type = 21,\n  Bits = 22,\n  Bytes = 23,\n  Builtin = 24;\n\nfunction isAlpha(ch) {\n    return ch >= 65 /* Ch.A */ && ch <= 90 /* Ch.Z */ || ch >= 97 /* Ch.a */ && ch <= 122 /* Ch.z */ || ch >= 48 /* Ch._0 */ && ch <= 57 /* Ch._9 */;\n}\nfunction isHexDigit(ch) {\n    return ch >= 48 /* Ch._0 */ && ch <= 57 /* Ch._9 */ || ch >= 97 /* Ch.a */ && ch <= 102 /* Ch.f */ || ch >= 65 /* Ch.A */ && ch <= 70 /* Ch.F */;\n}\nfunction readLiteral(input, endQuote, backslashEscapes) {\n    for (let escaped = false;;) {\n        if (input.next < 0)\n            return;\n        if (input.next == endQuote && !escaped) {\n            input.advance();\n            return;\n        }\n        escaped = backslashEscapes && !escaped && input.next == 92 /* Ch.Backslash */;\n        input.advance();\n    }\n}\nfunction readDoubleDollarLiteral(input, tag) {\n    scan: for (;;) {\n        if (input.next < 0)\n            return;\n        if (input.next == 36 /* Ch.Dollar */) {\n            input.advance();\n            for (let i = 0; i < tag.length; i++) {\n                if (input.next != tag.charCodeAt(i))\n                    continue scan;\n                input.advance();\n            }\n            if (input.next == 36 /* Ch.Dollar */) {\n                input.advance();\n                return;\n            }\n        }\n        else {\n            input.advance();\n        }\n    }\n}\nfunction readPLSQLQuotedLiteral(input, openDelim) {\n    let matchingDelim = \"[{<(\".indexOf(String.fromCharCode(openDelim));\n    let closeDelim = matchingDelim < 0 ? openDelim : \"]}>)\".charCodeAt(matchingDelim);\n    for (;;) {\n        if (input.next < 0)\n            return;\n        if (input.next == closeDelim && input.peek(1) == 39 /* Ch.SingleQuote */) {\n            input.advance(2);\n            return;\n        }\n        input.advance();\n    }\n}\nfunction readWord(input, result) {\n    for (;;) {\n        if (input.next != 95 /* Ch.Underscore */ && !isAlpha(input.next))\n            break;\n        if (result != null)\n            result += String.fromCharCode(input.next);\n        input.advance();\n    }\n    return result;\n}\nfunction readWordOrQuoted(input) {\n    if (input.next == 39 /* Ch.SingleQuote */ || input.next == 34 /* Ch.DoubleQuote */ || input.next == 96 /* Ch.Backtick */) {\n        let quote = input.next;\n        input.advance();\n        readLiteral(input, quote, false);\n    }\n    else {\n        readWord(input);\n    }\n}\nfunction readBits(input, endQuote) {\n    while (input.next == 48 /* Ch._0 */ || input.next == 49 /* Ch._1 */)\n        input.advance();\n    if (endQuote && input.next == endQuote)\n        input.advance();\n}\nfunction readNumber(input, sawDot) {\n    for (;;) {\n        if (input.next == 46 /* Ch.Dot */) {\n            if (sawDot)\n                break;\n            sawDot = true;\n        }\n        else if (input.next < 48 /* Ch._0 */ || input.next > 57 /* Ch._9 */) {\n            break;\n        }\n        input.advance();\n    }\n    if (input.next == 69 /* Ch.E */ || input.next == 101 /* Ch.e */) {\n        input.advance();\n        if (input.next == 43 /* Ch.Plus */ || input.next == 45 /* Ch.Dash */)\n            input.advance();\n        while (input.next >= 48 /* Ch._0 */ && input.next <= 57 /* Ch._9 */)\n            input.advance();\n    }\n}\nfunction eol(input) {\n    while (!(input.next < 0 || input.next == 10 /* Ch.Newline */))\n        input.advance();\n}\nfunction inString(ch, str) {\n    for (let i = 0; i < str.length; i++)\n        if (str.charCodeAt(i) == ch)\n            return true;\n    return false;\n}\nconst Space = \" \\t\\r\\n\";\nfunction keywords(keywords, types, builtin) {\n    let result = Object.create(null);\n    result[\"true\"] = result[\"false\"] = Bool;\n    result[\"null\"] = result[\"unknown\"] = Null;\n    for (let kw of keywords.split(\" \"))\n        if (kw)\n            result[kw] = Keyword;\n    for (let tp of types.split(\" \"))\n        if (tp)\n            result[tp] = Type;\n    for (let kw of (builtin || \"\").split(\" \"))\n        if (kw)\n            result[kw] = Builtin;\n    return result;\n}\nconst SQLTypes = \"array binary bit boolean char character clob date decimal double float int integer interval large national nchar nclob numeric object precision real smallint time timestamp varchar varying \";\nconst SQLKeywords = \"absolute action add after all allocate alter and any are as asc assertion at authorization before begin between both breadth by call cascade cascaded case cast catalog check close collate collation column commit condition connect connection constraint constraints constructor continue corresponding count create cross cube current current_date current_default_transform_group current_transform_group_for_type current_path current_role current_time current_timestamp current_user cursor cycle data day deallocate declare default deferrable deferred delete depth deref desc describe descriptor deterministic diagnostics disconnect distinct do domain drop dynamic each else elseif end end-exec equals escape except exception exec execute exists exit external fetch first for foreign found from free full function general get global go goto grant group grouping handle having hold hour identity if immediate in indicator initially inner inout input insert intersect into is isolation join key language last lateral leading leave left level like limit local localtime localtimestamp locator loop map match method minute modifies module month names natural nesting new next no none not of old on only open option or order ordinality out outer output overlaps pad parameter partial path prepare preserve primary prior privileges procedure public read reads recursive redo ref references referencing relative release repeat resignal restrict result return returns revoke right role rollback rollup routine row rows savepoint schema scroll search second section select session session_user set sets signal similar size some space specific specifictype sql sqlexception sqlstate sqlwarning start state static system_user table temporary then timezone_hour timezone_minute to trailing transaction translation treat trigger under undo union unique unnest until update usage user using value values view when whenever where while with without work write year zone \";\nconst defaults = {\n    backslashEscapes: false,\n    hashComments: false,\n    spaceAfterDashes: false,\n    slashComments: false,\n    doubleQuotedStrings: false,\n    doubleDollarQuotedStrings: false,\n    unquotedBitLiterals: false,\n    treatBitsAsBytes: false,\n    charSetCasts: false,\n    plsqlQuotingMechanism: false,\n    operatorChars: \"*+\\-%<>!=&|~^/\",\n    specialVar: \"?\",\n    identifierQuotes: '\"',\n    caseInsensitiveIdentifiers: false,\n    words: /*@__PURE__*/keywords(SQLKeywords, SQLTypes)\n};\nfunction dialect(spec, kws, types, builtin) {\n    let dialect = {};\n    for (let prop in defaults)\n        dialect[prop] = (spec.hasOwnProperty(prop) ? spec : defaults)[prop];\n    if (kws)\n        dialect.words = keywords(kws, types || \"\", builtin);\n    return dialect;\n}\nfunction tokensFor(d) {\n    return new _lezer_lr__WEBPACK_IMPORTED_MODULE_1__.ExternalTokenizer(input => {\n        var _a;\n        let { next } = input;\n        input.advance();\n        if (inString(next, Space)) {\n            while (inString(input.next, Space))\n                input.advance();\n            input.acceptToken(whitespace);\n        }\n        else if (next == 36 /* Ch.Dollar */ && d.doubleDollarQuotedStrings) {\n            let tag = readWord(input, \"\");\n            if (input.next == 36 /* Ch.Dollar */) {\n                input.advance();\n                readDoubleDollarLiteral(input, tag);\n                input.acceptToken(String$1);\n            }\n        }\n        else if (next == 39 /* Ch.SingleQuote */ || next == 34 /* Ch.DoubleQuote */ && d.doubleQuotedStrings) {\n            readLiteral(input, next, d.backslashEscapes);\n            input.acceptToken(String$1);\n        }\n        else if (next == 35 /* Ch.Hash */ && d.hashComments ||\n            next == 47 /* Ch.Slash */ && input.next == 47 /* Ch.Slash */ && d.slashComments) {\n            eol(input);\n            input.acceptToken(LineComment);\n        }\n        else if (next == 45 /* Ch.Dash */ && input.next == 45 /* Ch.Dash */ &&\n            (!d.spaceAfterDashes || input.peek(1) == 32 /* Ch.Space */)) {\n            eol(input);\n            input.acceptToken(LineComment);\n        }\n        else if (next == 47 /* Ch.Slash */ && input.next == 42 /* Ch.Star */) {\n            input.advance();\n            for (let depth = 1;;) {\n                let cur = input.next;\n                if (input.next < 0)\n                    break;\n                input.advance();\n                if (cur == 42 /* Ch.Star */ && input.next == 47 /* Ch.Slash */) {\n                    depth--;\n                    input.advance();\n                    if (!depth)\n                        break;\n                }\n                else if (cur == 47 /* Ch.Slash */ && input.next == 42 /* Ch.Star */) {\n                    depth++;\n                    input.advance();\n                }\n            }\n            input.acceptToken(BlockComment);\n        }\n        else if ((next == 101 /* Ch.e */ || next == 69 /* Ch.E */) && input.next == 39 /* Ch.SingleQuote */) {\n            input.advance();\n            readLiteral(input, 39 /* Ch.SingleQuote */, true);\n            input.acceptToken(String$1);\n        }\n        else if ((next == 110 /* Ch.n */ || next == 78 /* Ch.N */) && input.next == 39 /* Ch.SingleQuote */ &&\n            d.charSetCasts) {\n            input.advance();\n            readLiteral(input, 39 /* Ch.SingleQuote */, d.backslashEscapes);\n            input.acceptToken(String$1);\n        }\n        else if (next == 95 /* Ch.Underscore */ && d.charSetCasts) {\n            for (let i = 0;; i++) {\n                if (input.next == 39 /* Ch.SingleQuote */ && i > 1) {\n                    input.advance();\n                    readLiteral(input, 39 /* Ch.SingleQuote */, d.backslashEscapes);\n                    input.acceptToken(String$1);\n                    break;\n                }\n                if (!isAlpha(input.next))\n                    break;\n                input.advance();\n            }\n        }\n        else if (d.plsqlQuotingMechanism &&\n            (next == 113 /* Ch.q */ || next == 81 /* Ch.Q */) && input.next == 39 /* Ch.SingleQuote */ &&\n            input.peek(1) > 0 && !inString(input.peek(1), Space)) {\n            let openDelim = input.peek(1);\n            input.advance(2);\n            readPLSQLQuotedLiteral(input, openDelim);\n            input.acceptToken(String$1);\n        }\n        else if (next == 40 /* Ch.ParenL */) {\n            input.acceptToken(ParenL);\n        }\n        else if (next == 41 /* Ch.ParenR */) {\n            input.acceptToken(ParenR);\n        }\n        else if (next == 123 /* Ch.BraceL */) {\n            input.acceptToken(BraceL);\n        }\n        else if (next == 125 /* Ch.BraceR */) {\n            input.acceptToken(BraceR);\n        }\n        else if (next == 91 /* Ch.BracketL */) {\n            input.acceptToken(BracketL);\n        }\n        else if (next == 93 /* Ch.BracketR */) {\n            input.acceptToken(BracketR);\n        }\n        else if (next == 59 /* Ch.Semi */) {\n            input.acceptToken(Semi);\n        }\n        else if (d.unquotedBitLiterals && next == 48 /* Ch._0 */ && input.next == 98 /* Ch.b */) {\n            input.advance();\n            readBits(input);\n            input.acceptToken(Bits);\n        }\n        else if ((next == 98 /* Ch.b */ || next == 66 /* Ch.B */) && (input.next == 39 /* Ch.SingleQuote */ || input.next == 34 /* Ch.DoubleQuote */)) {\n            const quoteStyle = input.next;\n            input.advance();\n            if (d.treatBitsAsBytes) {\n                readLiteral(input, quoteStyle, d.backslashEscapes);\n                input.acceptToken(Bytes);\n            }\n            else {\n                readBits(input, quoteStyle);\n                input.acceptToken(Bits);\n            }\n        }\n        else if (next == 48 /* Ch._0 */ && (input.next == 120 /* Ch.x */ || input.next == 88 /* Ch.X */) ||\n            (next == 120 /* Ch.x */ || next == 88 /* Ch.X */) && input.next == 39 /* Ch.SingleQuote */) {\n            let quoted = input.next == 39 /* Ch.SingleQuote */;\n            input.advance();\n            while (isHexDigit(input.next))\n                input.advance();\n            if (quoted && input.next == 39 /* Ch.SingleQuote */)\n                input.advance();\n            input.acceptToken(Number);\n        }\n        else if (next == 46 /* Ch.Dot */ && input.next >= 48 /* Ch._0 */ && input.next <= 57 /* Ch._9 */) {\n            readNumber(input, true);\n            input.acceptToken(Number);\n        }\n        else if (next == 46 /* Ch.Dot */) {\n            input.acceptToken(Dot);\n        }\n        else if (next >= 48 /* Ch._0 */ && next <= 57 /* Ch._9 */) {\n            readNumber(input, false);\n            input.acceptToken(Number);\n        }\n        else if (inString(next, d.operatorChars)) {\n            while (inString(input.next, d.operatorChars))\n                input.advance();\n            input.acceptToken(Operator);\n        }\n        else if (inString(next, d.specialVar)) {\n            if (input.next == next)\n                input.advance();\n            readWordOrQuoted(input);\n            input.acceptToken(SpecialVar);\n        }\n        else if (inString(next, d.identifierQuotes)) {\n            readLiteral(input, next, false);\n            input.acceptToken(QuotedIdentifier);\n        }\n        else if (next == 58 /* Ch.Colon */ || next == 44 /* Ch.Comma */) {\n            input.acceptToken(Punctuation);\n        }\n        else if (isAlpha(next)) {\n            let word = readWord(input, String.fromCharCode(next));\n            input.acceptToken(input.next == 46 /* Ch.Dot */ || input.peek(-word.length - 1) == 46 /* Ch.Dot */\n                ? Identifier : (_a = d.words[word.toLowerCase()]) !== null && _a !== void 0 ? _a : Identifier);\n        }\n    });\n}\nconst tokens = /*@__PURE__*/tokensFor(defaults);\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser$1 = /*@__PURE__*/_lezer_lr__WEBPACK_IMPORTED_MODULE_1__.LRParser.deserialize({\n  version: 14,\n  states: \"%vQ]QQOOO#wQRO'#DSO$OQQO'#CwO%eQQO'#CxO%lQQO'#CyO%sQQO'#CzOOQQ'#DS'#DSOOQQ'#C}'#C}O'UQRO'#C{OOQQ'#Cv'#CvOOQQ'#C|'#C|Q]QQOOQOQQOOO'`QQO'#DOO(xQRO,59cO)PQQO,59cO)UQQO'#DSOOQQ,59d,59dO)cQQO,59dOOQQ,59e,59eO)jQQO,59eOOQQ,59f,59fO)qQQO,59fOOQQ-E6{-E6{OOQQ,59b,59bOOQQ-E6z-E6zOOQQ,59j,59jOOQQ-E6|-E6|O+VQRO1G.}O+^QQO,59cOOQQ1G/O1G/OOOQQ1G/P1G/POOQQ1G/Q1G/QP+kQQO'#C}O+rQQO1G.}O)PQQO,59cO,PQQO'#Cw\",\n  stateData: \",[~OtOSPOSQOS~ORUOSUOTUOUUOVROXSOZTO]XO^QO_UO`UOaPObPOcPOdUOeUOfUOgUOhUO~O^]ORvXSvXTvXUvXVvXXvXZvX]vX_vX`vXavXbvXcvXdvXevXfvXgvXhvX~OsvX~P!jOa_Ob_Oc_O~ORUOSUOTUOUUOVROXSOZTO^tO_UO`UOa`Ob`Oc`OdUOeUOfUOgUOhUO~OWaO~P$ZOYcO~P$ZO[eO~P$ZORUOSUOTUOUUOVROXSOZTO^QO_UO`UOaPObPOcPOdUOeUOfUOgUOhUO~O]hOsoX~P%zOajObjOcjO~O^]ORkaSkaTkaUkaVkaXkaZka]ka_ka`kaakabkackadkaekafkagkahka~Oska~P'kO^]O~OWvXYvX[vX~P!jOWnO~P$ZOYoO~P$ZO[pO~P$ZO^]ORkiSkiTkiUkiVkiXkiZki]ki_ki`kiakibkickidkiekifkigkihki~Oski~P)xOWkaYka[ka~P'kO]hO~P$ZOWkiYki[ki~P)xOasObsOcsO~O\",\n  goto: \"#hwPPPPPPPPPPPPPPPPPPPPPPPPPPx||||!Y!^!d!xPPP#[TYOZeUORSTWZbdfqT[OZQZORiZSWOZQbRQdSQfTZgWbdfqQ^PWk^lmrQl_Qm`RrseVORSTWZbdfq\",\n  nodeNames: \"⚠ LineComment BlockComment String Number Bool Null ( ) { } [ ] ; . Operator Punctuation SpecialVar Identifier QuotedIdentifier Keyword Type Bits Bytes Builtin Script Statement CompositeIdentifier Parens Braces Brackets Statement\",\n  maxTerm: 38,\n  nodeProps: [\n    [\"isolate\", -4,1,2,3,19,\"\"]\n  ],\n  skippedNodes: [0,1,2],\n  repeatNodeCount: 3,\n  tokenData: \"RORO\",\n  tokenizers: [0, tokens],\n  topRules: {\"Script\":[0,25]},\n  tokenPrec: 0\n});\n\nfunction tokenBefore(tree) {\n    let cursor = tree.cursor().moveTo(tree.from, -1);\n    while (/Comment/.test(cursor.name))\n        cursor.moveTo(cursor.from, -1);\n    return cursor.node;\n}\nfunction idName(doc, node) {\n    let text = doc.sliceString(node.from, node.to);\n    let quoted = /^([`'\"])(.*)\\1$/.exec(text);\n    return quoted ? quoted[2] : text;\n}\nfunction plainID(node) {\n    return node && (node.name == \"Identifier\" || node.name == \"QuotedIdentifier\");\n}\nfunction pathFor(doc, id) {\n    if (id.name == \"CompositeIdentifier\") {\n        let path = [];\n        for (let ch = id.firstChild; ch; ch = ch.nextSibling)\n            if (plainID(ch))\n                path.push(idName(doc, ch));\n        return path;\n    }\n    return [idName(doc, id)];\n}\nfunction parentsFor(doc, node) {\n    for (let path = [];;) {\n        if (!node || node.name != \".\")\n            return path;\n        let name = tokenBefore(node);\n        if (!plainID(name))\n            return path;\n        path.unshift(idName(doc, name));\n        node = tokenBefore(name);\n    }\n}\nfunction sourceContext(state, startPos) {\n    let pos = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.syntaxTree)(state).resolveInner(startPos, -1);\n    let aliases = getAliases(state.doc, pos);\n    if (pos.name == \"Identifier\" || pos.name == \"QuotedIdentifier\" || pos.name == \"Keyword\") {\n        return { from: pos.from,\n            quoted: pos.name == \"QuotedIdentifier\" ? state.doc.sliceString(pos.from, pos.from + 1) : null,\n            parents: parentsFor(state.doc, tokenBefore(pos)),\n            aliases };\n    }\n    if (pos.name == \".\") {\n        return { from: startPos, quoted: null, parents: parentsFor(state.doc, pos), aliases };\n    }\n    else {\n        return { from: startPos, quoted: null, parents: [], empty: true, aliases };\n    }\n}\nconst EndFrom = /*@__PURE__*/new Set(/*@__PURE__*/\"where group having order union intersect except all distinct limit offset fetch for\".split(\" \"));\nfunction getAliases(doc, at) {\n    let statement;\n    for (let parent = at; !statement; parent = parent.parent) {\n        if (!parent)\n            return null;\n        if (parent.name == \"Statement\")\n            statement = parent;\n    }\n    let aliases = null;\n    for (let scan = statement.firstChild, sawFrom = false, prevID = null; scan; scan = scan.nextSibling) {\n        let kw = scan.name == \"Keyword\" ? doc.sliceString(scan.from, scan.to).toLowerCase() : null;\n        let alias = null;\n        if (!sawFrom) {\n            sawFrom = kw == \"from\";\n        }\n        else if (kw == \"as\" && prevID && plainID(scan.nextSibling)) {\n            alias = idName(doc, scan.nextSibling);\n        }\n        else if (kw && EndFrom.has(kw)) {\n            break;\n        }\n        else if (prevID && plainID(scan)) {\n            alias = idName(doc, scan);\n        }\n        if (alias) {\n            if (!aliases)\n                aliases = Object.create(null);\n            aliases[alias] = pathFor(doc, prevID);\n        }\n        prevID = /Identifier$/.test(scan.name) ? scan : null;\n    }\n    return aliases;\n}\nfunction maybeQuoteCompletions(quote, completions) {\n    if (!quote)\n        return completions;\n    return completions.map(c => (Object.assign(Object.assign({}, c), { label: c.label[0] == quote ? c.label : quote + c.label + quote, apply: undefined })));\n}\nconst Span = /^\\w*$/, QuotedSpan = /^[`'\"]?\\w*[`'\"]?$/;\nfunction isSelfTag(namespace) {\n    return namespace.self && typeof namespace.self.label == \"string\";\n}\nclass CompletionLevel {\n    constructor(idQuote, idCaseInsensitive) {\n        this.idQuote = idQuote;\n        this.idCaseInsensitive = idCaseInsensitive;\n        this.list = [];\n        this.children = undefined;\n    }\n    child(name) {\n        let children = this.children || (this.children = Object.create(null));\n        let found = children[name];\n        if (found)\n            return found;\n        if (name && !this.list.some(c => c.label == name))\n            this.list.push(nameCompletion(name, \"type\", this.idQuote, this.idCaseInsensitive));\n        return (children[name] = new CompletionLevel(this.idQuote, this.idCaseInsensitive));\n    }\n    maybeChild(name) {\n        return this.children ? this.children[name] : null;\n    }\n    addCompletion(option) {\n        let found = this.list.findIndex(o => o.label == option.label);\n        if (found > -1)\n            this.list[found] = option;\n        else\n            this.list.push(option);\n    }\n    addCompletions(completions) {\n        for (let option of completions)\n            this.addCompletion(typeof option == \"string\" ? nameCompletion(option, \"property\", this.idQuote, this.idCaseInsensitive) : option);\n    }\n    addNamespace(namespace) {\n        if (Array.isArray(namespace)) {\n            this.addCompletions(namespace);\n        }\n        else if (isSelfTag(namespace)) {\n            this.addNamespace(namespace.children);\n        }\n        else {\n            this.addNamespaceObject(namespace);\n        }\n    }\n    addNamespaceObject(namespace) {\n        for (let name of Object.keys(namespace)) {\n            let children = namespace[name], self = null;\n            let parts = name.replace(/\\\\?\\./g, p => p == \".\" ? \"\\0\" : p).split(\"\\0\");\n            let scope = this;\n            if (isSelfTag(children)) {\n                self = children.self;\n                children = children.children;\n            }\n            for (let i = 0; i < parts.length; i++) {\n                if (self && i == parts.length - 1)\n                    scope.addCompletion(self);\n                scope = scope.child(parts[i].replace(/\\\\\\./g, \".\"));\n            }\n            scope.addNamespace(children);\n        }\n    }\n}\nfunction nameCompletion(label, type, idQuote, idCaseInsensitive) {\n    if ((new RegExp(\"^[a-z_][a-z_\\\\d]*$\", idCaseInsensitive ? \"i\" : \"\")).test(label))\n        return { label, type };\n    return { label, type, apply: idQuote + label + idQuote };\n}\n// Some of this is more gnarly than it has to be because we're also\n// supporting the deprecated, not-so-well-considered style of\n// supplying the schema (dotted property names for schemas, separate\n// `tables` and `schemas` completions).\nfunction completeFromSchema(schema, tables, schemas, defaultTableName, defaultSchemaName, dialect) {\n    var _a;\n    let idQuote = ((_a = dialect === null || dialect === void 0 ? void 0 : dialect.spec.identifierQuotes) === null || _a === void 0 ? void 0 : _a[0]) || '\"';\n    let top = new CompletionLevel(idQuote, !!(dialect === null || dialect === void 0 ? void 0 : dialect.spec.caseInsensitiveIdentifiers));\n    let defaultSchema = defaultSchemaName ? top.child(defaultSchemaName) : null;\n    top.addNamespace(schema);\n    if (tables)\n        (defaultSchema || top).addCompletions(tables);\n    if (schemas)\n        top.addCompletions(schemas);\n    if (defaultSchema)\n        top.addCompletions(defaultSchema.list);\n    if (defaultTableName)\n        top.addCompletions((defaultSchema || top).child(defaultTableName).list);\n    return (context) => {\n        let { parents, from, quoted, empty, aliases } = sourceContext(context.state, context.pos);\n        if (empty && !context.explicit)\n            return null;\n        if (aliases && parents.length == 1)\n            parents = aliases[parents[0]] || parents;\n        let level = top;\n        for (let name of parents) {\n            while (!level.children || !level.children[name]) {\n                if (level == top && defaultSchema)\n                    level = defaultSchema;\n                else if (level == defaultSchema && defaultTableName)\n                    level = level.child(defaultTableName);\n                else\n                    return null;\n            }\n            let next = level.maybeChild(name);\n            if (!next)\n                return null;\n            level = next;\n        }\n        let quoteAfter = quoted && context.state.sliceDoc(context.pos, context.pos + 1) == quoted;\n        let options = level.list;\n        if (level == top && aliases)\n            options = options.concat(Object.keys(aliases).map(name => ({ label: name, type: \"constant\" })));\n        return {\n            from,\n            to: quoteAfter ? context.pos + 1 : undefined,\n            options: maybeQuoteCompletions(quoted, options),\n            validFor: quoted ? QuotedSpan : Span\n        };\n    };\n}\nfunction completionType(tokenType) {\n    return tokenType == Type ? \"type\" : tokenType == Keyword ? \"keyword\" : \"variable\";\n}\nfunction completeKeywords(keywords, upperCase, build) {\n    let completions = Object.keys(keywords)\n        .map(keyword => build(upperCase ? keyword.toUpperCase() : keyword, completionType(keywords[keyword])));\n    return (0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.ifNotIn)([\"QuotedIdentifier\", \"SpecialVar\", \"String\", \"LineComment\", \"BlockComment\", \".\"], (0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.completeFromList)(completions));\n}\n\nlet parser = /*@__PURE__*/parser$1.configure({\n    props: [\n        /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.indentNodeProp.add({\n            Statement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.continuedIndent)()\n        }),\n        /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldNodeProp.add({\n            Statement(tree, state) { return { from: Math.min(tree.from + 100, state.doc.lineAt(tree.from).to), to: tree.to }; },\n            BlockComment(tree) { return { from: tree.from + 2, to: tree.to - 2 }; }\n        }),\n        /*@__PURE__*/(0,_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.styleTags)({\n            Keyword: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.keyword,\n            Type: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.typeName,\n            Builtin: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.standard(_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.name),\n            Bits: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.number,\n            Bytes: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.string,\n            Bool: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.bool,\n            Null: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.null,\n            Number: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.number,\n            String: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.string,\n            Identifier: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.name,\n            QuotedIdentifier: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.string),\n            SpecialVar: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.name),\n            LineComment: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.lineComment,\n            BlockComment: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.blockComment,\n            Operator: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.operator,\n            \"Semi Punctuation\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.punctuation,\n            \"( )\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.paren,\n            \"{ }\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.brace,\n            \"[ ]\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.squareBracket\n        })\n    ]\n});\n/**\nRepresents an SQL dialect.\n*/\nclass SQLDialect {\n    constructor(\n    /**\n    @internal\n    */\n    dialect, \n    /**\n    The language for this dialect.\n    */\n    language, \n    /**\n    The spec used to define this dialect.\n    */\n    spec) {\n        this.dialect = dialect;\n        this.language = language;\n        this.spec = spec;\n    }\n    /**\n    Returns the language for this dialect as an extension.\n    */\n    get extension() { return this.language.extension; }\n    /**\n    Define a new dialect.\n    */\n    static define(spec) {\n        let d = dialect(spec, spec.keywords, spec.types, spec.builtin);\n        let language = _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LRLanguage.define({\n            name: \"sql\",\n            parser: parser.configure({\n                tokenizers: [{ from: tokens, to: tokensFor(d) }]\n            }),\n            languageData: {\n                commentTokens: { line: \"--\", block: { open: \"/*\", close: \"*/\" } },\n                closeBrackets: { brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"] }\n            }\n        });\n        return new SQLDialect(d, language, spec);\n    }\n}\nfunction defaultKeyword(label, type) { return { label, type, boost: -1 }; }\n/**\nReturns a completion source that provides keyword completion for\nthe given SQL dialect.\n*/\nfunction keywordCompletionSource(dialect, upperCase = false, build) {\n    return completeKeywords(dialect.dialect.words, upperCase, build || defaultKeyword);\n}\n/**\nReturns a completion sources that provides schema-based completion\nfor the given configuration.\n*/\nfunction schemaCompletionSource(config) {\n    return config.schema ? completeFromSchema(config.schema, config.tables, config.schemas, config.defaultTable, config.defaultSchema, config.dialect || StandardSQL)\n        : () => null;\n}\nfunction schemaCompletion(config) {\n    return config.schema ? (config.dialect || StandardSQL).language.data.of({\n        autocomplete: schemaCompletionSource(config)\n    }) : [];\n}\n/**\nSQL language support for the given SQL dialect, with keyword\ncompletion, and, if provided, schema-based completion as extra\nextensions.\n*/\nfunction sql(config = {}) {\n    let lang = config.dialect || StandardSQL;\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LanguageSupport(lang.language, [\n        schemaCompletion(config),\n        lang.language.data.of({\n            autocomplete: keywordCompletionSource(lang, config.upperCaseKeywords, config.keywordCompletion)\n        })\n    ]);\n}\n/**\nThe standard SQL dialect.\n*/\nconst StandardSQL = /*@__PURE__*/SQLDialect.define({});\n/**\nDialect for [PostgreSQL](https://www.postgresql.org).\n*/\nconst PostgreSQL = /*@__PURE__*/SQLDialect.define({\n    charSetCasts: true,\n    doubleDollarQuotedStrings: true,\n    operatorChars: \"+-*/<>=~!@#%^&|`?\",\n    specialVar: \"\",\n    keywords: SQLKeywords + \"abort abs absent access according ada admin aggregate alias also always analyse analyze array_agg array_max_cardinality asensitive assert assignment asymmetric atomic attach attribute attributes avg backward base64 begin_frame begin_partition bernoulli bit_length blocked bom cache called cardinality catalog_name ceil ceiling chain char_length character_length character_set_catalog character_set_name character_set_schema characteristics characters checkpoint class class_origin cluster coalesce cobol collation_catalog collation_name collation_schema collect column_name columns command_function command_function_code comment comments committed concurrently condition_number configuration conflict connection_name constant constraint_catalog constraint_name constraint_schema contains content control conversion convert copy corr cost covar_pop covar_samp csv cume_dist current_catalog current_row current_schema cursor_name database datalink datatype datetime_interval_code datetime_interval_precision db debug defaults defined definer degree delimiter delimiters dense_rank depends derived detach detail dictionary disable discard dispatch dlnewcopy dlpreviouscopy dlurlcomplete dlurlcompleteonly dlurlcompletewrite dlurlpath dlurlpathonly dlurlpathwrite dlurlscheme dlurlserver dlvalue document dump dynamic_function dynamic_function_code element elsif empty enable encoding encrypted end_frame end_partition endexec enforced enum errcode error event every exclude excluding exclusive exp explain expression extension extract family file filter final first_value flag floor following force foreach fortran forward frame_row freeze fs functions fusion generated granted greatest groups handler header hex hierarchy hint id ignore ilike immediately immutable implementation implicit import include including increment indent index indexes info inherit inherits inline insensitive instance instantiable instead integrity intersection invoker isnull key_member key_type label lag last_value lead leakproof least length library like_regex link listen ln load location lock locked log logged lower mapping matched materialized max max_cardinality maxvalue member merge message message_length message_octet_length message_text min minvalue mod mode more move multiset mumps name namespace nfc nfd nfkc nfkd nil normalize normalized nothing notice notify notnull nowait nth_value ntile nullable nullif nulls number occurrences_regex octet_length octets off offset oids operator options ordering others over overlay overriding owned owner parallel parameter_mode parameter_name parameter_ordinal_position parameter_specific_catalog parameter_specific_name parameter_specific_schema parser partition pascal passing passthrough password percent percent_rank percentile_cont percentile_disc perform period permission pg_context pg_datatype_name pg_exception_context pg_exception_detail pg_exception_hint placing plans pli policy portion position position_regex power precedes preceding prepared print_strict_params procedural procedures program publication query quote raise range rank reassign recheck recovery refresh regr_avgx regr_avgy regr_count regr_intercept regr_r2 regr_slope regr_sxx regr_sxy regr_syy reindex rename repeatable replace replica requiring reset respect restart restore result_oid returned_cardinality returned_length returned_octet_length returned_sqlstate returning reverse routine_catalog routine_name routine_schema routines row_count row_number rowtype rule scale schema_name schemas scope scope_catalog scope_name scope_schema security selective self sensitive sequence sequences serializable server server_name setof share show simple skip slice snapshot source specific_name sqlcode sqlerror sqrt stable stacked standalone statement statistics stddev_pop stddev_samp stdin stdout storage strict strip structure style subclass_origin submultiset subscription substring substring_regex succeeds sum symmetric sysid system system_time table_name tables tablesample tablespace temp template ties token top_level_count transaction_active transactions_committed transactions_rolled_back transform transforms translate translate_regex trigger_catalog trigger_name trigger_schema trim trim_array truncate trusted type types uescape unbounded uncommitted unencrypted unlink unlisten unlogged unnamed untyped upper uri use_column use_variable user_defined_type_catalog user_defined_type_code user_defined_type_name user_defined_type_schema vacuum valid validate validator value_of var_pop var_samp varbinary variable_conflict variadic verbose version versioning views volatile warning whitespace width_bucket window within wrapper xmlagg xmlattributes xmlbinary xmlcast xmlcomment xmlconcat xmldeclaration xmldocument xmlelement xmlexists xmlforest xmliterate xmlnamespaces xmlparse xmlpi xmlquery xmlroot xmlschema xmlserialize xmltable xmltext xmlvalidate yes\",\n    types: SQLTypes + \"bigint int8 bigserial serial8 varbit bool box bytea cidr circle precision float8 inet int4 json jsonb line lseg macaddr macaddr8 money numeric pg_lsn point polygon float4 int2 smallserial serial2 serial serial4 text timetz timestamptz tsquery tsvector txid_snapshot uuid xml\"\n});\nconst MySQLKeywords = \"accessible algorithm analyze asensitive authors auto_increment autocommit avg avg_row_length binlog btree cache catalog_name chain change changed checkpoint checksum class_origin client_statistics coalesce code collations columns comment committed completion concurrent consistent contains contributors convert database databases day_hour day_microsecond day_minute day_second delay_key_write delayed delimiter des_key_file dev_pop dev_samp deviance directory disable discard distinctrow div dual dumpfile enable enclosed ends engine engines enum errors escaped even event events every explain extended fast field fields flush force found_rows fulltext grants handler hash high_priority hosts hour_microsecond hour_minute hour_second ignore ignore_server_ids import index index_statistics infile innodb insensitive insert_method install invoker iterate keys kill linear lines list load lock logs low_priority master master_heartbeat_period master_ssl_verify_server_cert masters max max_rows maxvalue message_text middleint migrate min min_rows minute_microsecond minute_second mod mode modify mutex mysql_errno no_write_to_binlog offline offset one online optimize optionally outfile pack_keys parser partition partitions password phase plugin plugins prev processlist profile profiles purge query quick range read_write rebuild recover regexp relaylog remove rename reorganize repair repeatable replace require resume rlike row_format rtree schedule schema_name schemas second_microsecond security sensitive separator serializable server share show slave slow snapshot soname spatial sql_big_result sql_buffer_result sql_cache sql_calc_found_rows sql_no_cache sql_small_result ssl starting starts std stddev stddev_pop stddev_samp storage straight_join subclass_origin sum suspend table_name table_statistics tables tablespace terminated triggers truncate uncommitted uninstall unlock upgrade use use_frm user_resources user_statistics utc_date utc_time utc_timestamp variables views warnings xa xor year_month zerofill\";\nconst MySQLTypes = SQLTypes + \"bool blob long longblob longtext medium mediumblob mediumint mediumtext tinyblob tinyint tinytext text bigint int1 int2 int3 int4 int8 float4 float8 varbinary varcharacter precision datetime unsigned signed\";\nconst MySQLBuiltin = \"charset clear edit ego help nopager notee nowarning pager print prompt quit rehash source status system tee\";\n/**\n[MySQL](https://dev.mysql.com/) dialect.\n*/\nconst MySQL = /*@__PURE__*/SQLDialect.define({\n    operatorChars: \"*+-%<>!=&|^\",\n    charSetCasts: true,\n    doubleQuotedStrings: true,\n    unquotedBitLiterals: true,\n    hashComments: true,\n    spaceAfterDashes: true,\n    specialVar: \"@?\",\n    identifierQuotes: \"`\",\n    keywords: SQLKeywords + \"group_concat \" + MySQLKeywords,\n    types: MySQLTypes,\n    builtin: MySQLBuiltin\n});\n/**\nVariant of [`MySQL`](https://codemirror.net/6/docs/ref/#lang-sql.MySQL) for\n[MariaDB](https://mariadb.org/).\n*/\nconst MariaSQL = /*@__PURE__*/SQLDialect.define({\n    operatorChars: \"*+-%<>!=&|^\",\n    charSetCasts: true,\n    doubleQuotedStrings: true,\n    unquotedBitLiterals: true,\n    hashComments: true,\n    spaceAfterDashes: true,\n    specialVar: \"@?\",\n    identifierQuotes: \"`\",\n    keywords: SQLKeywords + \"always generated groupby_concat hard persistent shutdown soft virtual \" + MySQLKeywords,\n    types: MySQLTypes,\n    builtin: MySQLBuiltin\n});\n/**\nSQL dialect for Microsoft [SQL\nServer](https://www.microsoft.com/en-us/sql-server).\n*/\nconst MSSQL = /*@__PURE__*/SQLDialect.define({\n    keywords: SQLKeywords + \"trigger proc view index for add constraint key primary foreign collate clustered nonclustered declare exec go if use index holdlock nolock nowait paglock pivot readcommitted readcommittedlock readpast readuncommitted repeatableread rowlock serializable snapshot tablock tablockx unpivot updlock with\",\n    types: SQLTypes + \"bigint smallint smallmoney tinyint money real text nvarchar ntext varbinary image hierarchyid uniqueidentifier sql_variant xml\",\n    builtin: \"binary_checksum checksum connectionproperty context_info current_request_id error_line error_message error_number error_procedure error_severity error_state formatmessage get_filestream_transaction_context getansinull host_id host_name isnull isnumeric min_active_rowversion newid newsequentialid rowcount_big xact_state object_id\",\n    operatorChars: \"*+-%<>!=^&|/\",\n    specialVar: \"@\"\n});\n/**\n[SQLite](https://sqlite.org/) dialect.\n*/\nconst SQLite = /*@__PURE__*/SQLDialect.define({\n    keywords: SQLKeywords + \"abort analyze attach autoincrement conflict database detach exclusive fail glob ignore index indexed instead isnull notnull offset plan pragma query raise regexp reindex rename replace temp vacuum virtual\",\n    types: SQLTypes + \"bool blob long longblob longtext medium mediumblob mediumint mediumtext tinyblob tinyint tinytext text bigint int2 int8 unsigned signed real\",\n    builtin: \"auth backup bail changes clone databases dbinfo dump echo eqp explain fullschema headers help import imposter indexes iotrace lint load log mode nullvalue once print prompt quit restore save scanstats separator shell show stats system tables testcase timeout timer trace vfsinfo vfslist vfsname width\",\n    operatorChars: \"*+-%<>!=&|/~\",\n    identifierQuotes: \"`\\\"\",\n    specialVar: \"@:?$\"\n});\n/**\nDialect for [Cassandra](https://cassandra.apache.org/)'s SQL-ish query language.\n*/\nconst Cassandra = /*@__PURE__*/SQLDialect.define({\n    keywords: \"add all allow alter and any apply as asc authorize batch begin by clustering columnfamily compact consistency count create custom delete desc distinct drop each_quorum exists filtering from grant if in index insert into key keyspace keyspaces level limit local_one local_quorum modify nan norecursive nosuperuser not of on one order password permission permissions primary quorum rename revoke schema select set storage superuser table three to token truncate ttl two type unlogged update use user users using values where with writetime infinity NaN\",\n    types: SQLTypes + \"ascii bigint blob counter frozen inet list map static text timeuuid tuple uuid varint\",\n    slashComments: true\n});\n/**\n[PL/SQL](https://en.wikipedia.org/wiki/PL/SQL) dialect.\n*/\nconst PLSQL = /*@__PURE__*/SQLDialect.define({\n    keywords: SQLKeywords + \"abort accept access add all alter and any arraylen as asc assert assign at attributes audit authorization avg base_table begin between binary_integer body by case cast char_base check close cluster clusters colauth column comment commit compress connected constant constraint crash create current currval cursor data_base database dba deallocate debugoff debugon declare default definition delay delete desc digits dispose distinct do drop else elseif elsif enable end entry exception exception_init exchange exclusive exists external fast fetch file for force form from function generic goto grant group having identified if immediate in increment index indexes indicator initial initrans insert interface intersect into is key level library like limited local lock log logging loop master maxextents maxtrans member minextents minus mislabel mode modify multiset new next no noaudit nocompress nologging noparallel not nowait number_base of off offline on online only option or order out package parallel partition pctfree pctincrease pctused pls_integer positive positiven pragma primary prior private privileges procedure public raise range raw rebuild record ref references refresh rename replace resource restrict return returning returns reverse revoke rollback row rowid rowlabel rownum rows run savepoint schema segment select separate set share snapshot some space split sql start statement storage subtype successful synonym tabauth table tables tablespace task terminate then to trigger truncate type union unique unlimited unrecoverable unusable update use using validate value values variable view views when whenever where while with work\",\n    builtin: \"appinfo arraysize autocommit autoprint autorecovery autotrace blockterminator break btitle cmdsep colsep compatibility compute concat copycommit copytypecheck define echo editfile embedded feedback flagger flush heading headsep instance linesize lno loboffset logsource longchunksize markup native newpage numformat numwidth pagesize pause pno recsep recsepchar repfooter repheader serveroutput shiftinout show showmode spool sqlblanklines sqlcase sqlcode sqlcontinue sqlnumber sqlpluscompatibility sqlprefix sqlprompt sqlterminator suffix tab term termout timing trimout trimspool ttitle underline verify version wrap\",\n    types: SQLTypes + \"ascii bfile bfilename bigserial bit blob dec long number nvarchar nvarchar2 serial smallint string text uid varchar2 xml\",\n    operatorChars: \"*/+-%<>!=~\",\n    doubleQuotedStrings: true,\n    charSetCasts: true,\n    plsqlQuotingMechanism: true\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-sql@6.8.0/node_modules/@codemirror/lang-sql/dist/index.js\n");

/***/ })

};
;