"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  File,
  Folder,
  FolderOpen,
  Download,
  Home,
  ChevronLeft,
  Loader,
  AlertTriangle,
  FileText,
  Terminal,
  Monitor,
  Clock,
  RefreshCw
} from "lucide-react";
import { FileRenderer, getFileTypeFromExtension } from "@/components/file-renderers";
import { type FileInfo, Project } from "@/lib/api";
import { mockListSandboxFiles, mockGetSandboxFileContent } from "@/lib/mock-sandbox-api";
import { toast } from "sonner";
import { cn } from "@/lib/utils";

interface AgentDesktopViewProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  sandboxId: string;
  project?: Project;
  agentName?: string;
}

export function AgentDesktopView({
  open,
  onOpenChange,
  sandboxId,
  project,
  agentName = "Agent"
}: AgentDesktopViewProps) {
  // File navigation state
  const [currentPath, setCurrentPath] = useState("/workspace");
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [isLoadingFiles, setIsLoadingFiles] = useState(false);

  // File content state
  const [selectedFilePath, setSelectedFilePath] = useState<string | null>(null);
  const [textContentForRenderer, setTextContentForRenderer] = useState<string | null>(null);
  const [blobUrlForRenderer, setBlobUrlForRenderer] = useState<string | null>(null);
  const [isLoadingContent, setIsLoadingContent] = useState(false);
  const [contentError, setContentError] = useState<string | null>(null);

  // Activity log state
  const [activityLog, setActivityLog] = useState<{
    timestamp: string;
    action: string;
    details: string;
  }[]>([]);

  // Active tab state
  const [activeTab, setActiveTab] = useState("files");

  // Add a ref to track current loading operation
  const loadingFileRef = useRef<string | null>(null);
  const markdownRef = useRef<HTMLDivElement>(null);

  // Auto-refresh timer
  const [autoRefresh, setAutoRefresh] = useState(true);
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Function to ensure a path starts with /workspace
  const normalizePath = useCallback((path: unknown): string => {
    if (typeof path !== 'string' || !path) {
      return '/workspace';
    }
    return path.startsWith('/workspace') ? path : `/workspace/${path.replace(/^\//, '')}`;
  }, []);

  // Helper function to clear the selected file
  const clearSelectedFile = useCallback(() => {
    setSelectedFilePath(null);
    setTextContentForRenderer(null);
    setBlobUrlForRenderer(null);
    setContentError(null);
    setIsLoadingContent(false);
    loadingFileRef.current = null;
  }, []);

  // Helper function to navigate to a folder
  const navigateToFolder = useCallback((folder: FileInfo) => {
    if (!folder.is_dir) return;

    const normalizedPath = normalizePath(folder.path);

    // Clear selected file when navigating
    clearSelectedFile();

    // Update path state
    setCurrentPath(normalizedPath);

    // Log activity
    addActivityLogEntry("Navigated", `to folder ${folder.name}`);
  }, [normalizePath, clearSelectedFile]);

  // Helper function to navigate to home
  const navigateHome = useCallback(() => {
    clearSelectedFile();
    setCurrentPath('/workspace');
    addActivityLogEntry("Navigated", "to workspace root");
  }, [clearSelectedFile]);

  // Helper function to navigate up one level
  const navigateUp = useCallback(() => {
    if (currentPath === '/workspace') return;

    const pathParts = currentPath.split('/').filter(Boolean);
    pathParts.pop(); // Remove the last part
    const parentPath = pathParts.length > 1
      ? `/${pathParts.join('/')}`
      : '/workspace';

    clearSelectedFile();
    setCurrentPath(parentPath);
    addActivityLogEntry("Navigated", `up to ${parentPath}`);
  }, [currentPath, clearSelectedFile]);

  // Helper function to check if file is markdown
  const isMarkdownFile = useCallback((filePath: string | null) => {
    return filePath ? filePath.toLowerCase().endsWith('.md') : false;
  }, []);

  // Helper to add activity log entry
  const addActivityLogEntry = useCallback((action: string, details: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setActivityLog(prev => [{ timestamp, action, details }, ...prev].slice(0, 100));
  }, []);

  // Core file opening function
  const openFile = useCallback(async (file: FileInfo) => {
    if (file.is_dir) {
      navigateToFolder(file);
      return;
    }

    // Skip if already selected and content exists
    if (selectedFilePath === file.path && textContentForRenderer) {
      return;
    }

    // Clear previous state FIRST
    clearSelectedFile();

    // Set loading state and selected file path immediately
    setIsLoadingContent(true);
    setSelectedFilePath(file.path);

    // Set the loading ref to track current operation
    loadingFileRef.current = file.path;

    try {
      // Fetch content using mock API for demo purposes
      const content = await mockGetSandboxFileContent(sandboxId, file.path);

      // Critical check: Ensure the file we just loaded is still the one selected
      if (loadingFileRef.current !== file.path) {
        return; // Abort state update
      }

      // Determine how to prepare content for the renderer
      if (typeof content === 'string') {
        setTextContentForRenderer(content);
        setBlobUrlForRenderer(null);
      } else if (content instanceof Blob) {
        // For binary content like images, create a blob URL
        const url = URL.createObjectURL(content);
        setBlobUrlForRenderer(url);
        setTextContentForRenderer(null);
      } else {
        setContentError("Received unexpected content type.");
      }

      setIsLoadingContent(false);
      addActivityLogEntry("Opened", `file ${file.name}`);
    } catch (error) {
      // Only update error if this file is still the one being loaded
      if (loadingFileRef.current === file.path) {
        setContentError(`Failed to load file: ${error instanceof Error ? error.message : String(error)}`);
        setIsLoadingContent(false);
        addActivityLogEntry("Error", `loading file ${file.name}`);
      }
    } finally {
      // Clear the loading ref if it matches the current operation
      if (loadingFileRef.current === file.path) {
        loadingFileRef.current = null;
      }
    }
  }, [sandboxId, selectedFilePath, navigateToFolder, clearSelectedFile, addActivityLogEntry]);

  // Load files when modal opens or path changes
  const loadFiles = useCallback(async () => {
    if (!sandboxId) return;

    setIsLoadingFiles(true);
    try {
      // Use mock API for demo purposes
      const filesData = await mockListSandboxFiles(sandboxId, currentPath);
      setFiles(filesData);
      addActivityLogEntry("Refreshed", `files in ${currentPath}`);
    } catch (error) {
      toast.error("Failed to load files");
      setFiles([]);
      addActivityLogEntry("Error", "loading files");
    } finally {
      setIsLoadingFiles(false);
    }
  }, [sandboxId, currentPath, addActivityLogEntry]);

  // Effect to load files when path changes
  useEffect(() => {
    if (open) {
      loadFiles();
    }
  }, [open, currentPath, loadFiles]);

  // Effect to handle auto-refresh
  useEffect(() => {
    if (open && autoRefresh) {
      refreshTimerRef.current = setInterval(() => {
        loadFiles();
      }, 5000); // Refresh every 5 seconds
    }

    return () => {
      if (refreshTimerRef.current) {
        clearInterval(refreshTimerRef.current);
        refreshTimerRef.current = null;
      }
    };
  }, [open, autoRefresh, loadFiles]);

  // Effect to clean up when modal closes
  useEffect(() => {
    if (!open) {
      clearSelectedFile();
      setCurrentPath('/workspace');
    }
  }, [open, clearSelectedFile]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl h-[85vh] p-0 flex flex-col">
        <div className="p-4 border-b border-border flex items-center justify-between bg-muted/30">
          <div className="flex items-center">
            <Monitor className="h-5 w-5 mr-2 text-primary" />
            <h2 className="text-lg font-semibold">{agentName}'s Computer</h2>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setAutoRefresh(!autoRefresh)}
              className={cn(autoRefresh ? "text-green-500" : "text-muted-foreground")}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              {autoRefresh ? "Auto-refresh On" : "Auto-refresh Off"}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loadFiles}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="ghost" size="sm" onClick={() => onOpenChange(false)}>
              Close
            </Button>
          </div>
        </div>

        <Tabs defaultValue="files" value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
          <TabsList className="mx-4 mt-2 justify-start">
            <TabsTrigger value="files" className="flex items-center gap-1">
              <Folder className="h-4 w-4" />
              Files
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              Activity Log
            </TabsTrigger>
          </TabsList>

          <TabsContent value="files" className="flex-1 flex overflow-hidden p-0 pt-2">
            <div className="flex flex-col w-64 border-r border-border p-2 overflow-hidden">
              {/* File navigation controls */}
              <div className="flex items-center gap-1 mb-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={navigateHome}
                  className="h-8 w-8 p-0"
                >
                  <Home className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={navigateUp}
                  disabled={currentPath === '/workspace'}
                  className="h-8 w-8 p-0"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="text-sm truncate flex-1 px-2 py-1 bg-muted/50 rounded-md">
                  {currentPath}
                </div>
              </div>

              {/* File list */}
              <ScrollArea className="flex-1">
                {isLoadingFiles ? (
                  <div className="flex items-center justify-center h-20">
                    <Loader className="h-5 w-5 animate-spin text-muted-foreground" />
                  </div>
                ) : files.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-20 text-muted-foreground">
                    <Folder className="h-8 w-8 mb-2 opacity-50" />
                    <p className="text-sm">No files found</p>
                  </div>
                ) : (
                  <div className="space-y-0.5">
                    {files.map(file => (
                      <Button
                        key={file.path}
                        variant="ghost"
                        size="sm"
                        className={cn(
                          "w-full justify-start text-sm h-8",
                          selectedFilePath === file.path && "bg-muted"
                        )}
                        onClick={() => openFile(file)}
                      >
                        {file.is_dir ? (
                          <Folder className="h-4 w-4 mr-2 text-blue-500" />
                        ) : (
                          <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                        )}
                        <span className="truncate">{file.name}</span>
                      </Button>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </div>

            {/* File content view */}
            <div className="flex-1 overflow-hidden">
              {selectedFilePath ? (
                isLoadingContent ? (
                  <div className="h-full w-full flex items-center justify-center">
                    <Loader className="h-6 w-6 animate-spin text-primary" />
                  </div>
                ) : contentError ? (
                  <div className="h-full w-full flex flex-col items-center justify-center p-4">
                    <AlertTriangle className="h-12 w-12 text-amber-500 mb-4" />
                    <h3 className="text-lg font-medium mb-2">Error Loading File</h3>
                    <p className="text-sm text-muted-foreground text-center mb-4">{contentError}</p>
                    <div className="flex justify-center gap-3">
                      <Button
                        onClick={() => {
                          setContentError(null);
                          setIsLoadingContent(true);
                          openFile({
                            path: selectedFilePath,
                            name: selectedFilePath.split('/').pop() || '',
                            is_dir: false,
                            size: 0,
                            mod_time: new Date().toISOString()
                          } as FileInfo);
                        }}
                      >
                        Retry
                      </Button>
                      <Button
                        variant="outline"
                        onClick={clearSelectedFile}
                      >
                        Back to Files
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="h-full w-full relative">
                    <div className="border-b border-border p-2 bg-muted/30 flex items-center justify-between">
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-sm font-medium">{selectedFilePath.split('/').pop()}</span>
                      </div>
                      {blobUrlForRenderer && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            if (blobUrlForRenderer) {
                              const a = document.createElement('a');
                              a.href = blobUrlForRenderer;
                              a.download = selectedFilePath.split('/').pop() || 'download';
                              a.click();
                            }
                          }}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Download
                        </Button>
                      )}
                    </div>
                    <div className="h-[calc(100%-36px)]">
                      <FileRenderer
                        key={selectedFilePath}
                        content={textContentForRenderer}
                        binaryUrl={blobUrlForRenderer}
                        fileName={selectedFilePath}
                        className="h-full w-full"
                        project={project}
                        markdownRef={isMarkdownFile(selectedFilePath) ? markdownRef : undefined}
                      />
                    </div>
                  </div>
                )
              ) : (
                <div className="h-full w-full flex flex-col items-center justify-center text-muted-foreground">
                  <Monitor className="h-16 w-16 mb-4 opacity-20" />
                  <p className="text-lg mb-1">No file selected</p>
                  <p className="text-sm">Select a file from the sidebar to view its contents</p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="activity" className="flex-1 overflow-hidden p-0 pt-2">
            <div className="p-4 h-full">
              <h3 className="text-lg font-medium mb-4">Activity Log</h3>
              {activityLog.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-40 text-muted-foreground">
                  <Clock className="h-8 w-8 mb-2 opacity-50" />
                  <p className="text-sm">No activity recorded yet</p>
                </div>
              ) : (
                <ScrollArea className="h-[calc(100%-40px)]">
                  <div className="space-y-2">
                    {activityLog.map((entry, index) => (
                      <div
                        key={index}
                        className="flex items-start p-2 rounded-md border border-border bg-muted/30"
                      >
                        <div className="w-20 flex-shrink-0 text-xs text-muted-foreground">
                          {entry.timestamp}
                        </div>
                        <div className="flex-1">
                          <span className="font-medium">{entry.action}:</span> {entry.details}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
