"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-rust@6.0.1";
exports.ids = ["vendor-chunks/@codemirror+lang-rust@6.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-rust@6.0.1/node_modules/@codemirror/lang-rust/dist/index.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-rust@6.0.1/node_modules/@codemirror/lang-rust/dist/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rust: () => (/* binding */ rust),\n/* harmony export */   rustLanguage: () => (/* binding */ rustLanguage)\n/* harmony export */ });\n/* harmony import */ var _lezer_rust__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/rust */ \"(ssr)/./node_modules/.pnpm/@lezer+rust@1.0.2/node_modules/@lezer/rust/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n\n\n\n/**\nA syntax provider based on the [Lezer Rust\nparser](https://github.com/lezer-parser/rust), extended with\nhighlighting and indentation information.\n*/\nconst rustLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LRLanguage.define({\n    name: \"rust\",\n    parser: /*@__PURE__*/_lezer_rust__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.indentNodeProp.add({\n                IfExpression: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.continuedIndent)({ except: /^\\s*({|else\\b)/ }),\n                \"String BlockComment\": () => null,\n                \"AttributeItem\": cx => cx.continue(),\n                \"Statement MatchArm\": /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.continuedIndent)()\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldNodeProp.add(type => {\n                if (/(Block|edTokens|List)$/.test(type.name))\n                    return _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldInside;\n                if (type.name == \"BlockComment\")\n                    return tree => ({ from: tree.from + 2, to: tree.to - 2 });\n                return undefined;\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { line: \"//\", block: { open: \"/*\", close: \"*/\" } },\n        indentOnInput: /^\\s*(?:\\{|\\})$/,\n        closeBrackets: { stringPrefixes: [\"b\", \"r\", \"br\"] }\n    }\n});\n/**\nRust language support\n*/\nfunction rust() {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LanguageSupport(rustLanguage);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-rust@6.0.1/node_modules/@codemirror/lang-rust/dist/index.js\n");

/***/ })

};
;