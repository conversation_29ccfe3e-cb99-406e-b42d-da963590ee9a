"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-wast@6.0.2";
exports.ids = ["vendor-chunks/@codemirror+lang-wast@6.0.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-wast@6.0.2/node_modules/@codemirror/lang-wast/dist/index.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-wast@6.0.2/node_modules/@codemirror/lang-wast/dist/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   wast: () => (/* binding */ wast),\n/* harmony export */   wastLanguage: () => (/* binding */ wastLanguage)\n/* harmony export */ });\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js\");\n/* harmony import */ var _lezer_lr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/lr */ \"(ssr)/./node_modules/.pnpm/@lezer+lr@1.4.2/node_modules/@lezer/lr/dist/index.js\");\n\n\n\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_Keyword = {__proto__:null,anyref:34, dataref:34, eqref:34, externref:34, i31ref:34, funcref:34, i8:34, i16:34, i32:34, i64:34, f32:34, f64:34};\nconst parser = /*@__PURE__*/_lezer_lr__WEBPACK_IMPORTED_MODULE_1__.LRParser.deserialize({\n  version: 14,\n  states: \"!^Q]QPOOOqQPO'#CbOOQO'#Cd'#CdOOQO'#Cl'#ClOOQO'#Ch'#ChQ]QPOOOOQO,58|,58|OxQPO,58|OOQO-E6f-E6fOOQO1G.h1G.h\",\n  stateData: \"!P~O_OSPOSQOS~OTPOVROXROYROZROaQO~OSUO~P]OSXO~P]O\",\n  goto: \"xaPPPPPPbPbPPPhPPPrXROPTVQTOQVPTWTVXSOPTV\",\n  nodeNames: \"⚠ LineComment BlockComment Module ) ( App Identifier Type Keyword Number String\",\n  maxTerm: 17,\n  nodeProps: [\n    [\"isolate\", -3,1,2,11,\"\"],\n    [\"openedBy\", 4,\"(\"],\n    [\"closedBy\", 5,\")\"],\n    [\"group\", -6,6,7,8,9,10,11,\"Expression\"]\n  ],\n  skippedNodes: [0,1,2],\n  repeatNodeCount: 1,\n  tokenData: \"0o~R^XY}YZ}]^}pq}rs!Stu#pxy'Uyz(e{|(j}!O(j!Q!R(s!R![*p!]!^.^#T#o.{~!SO_~~!VVOr!Srs!ls#O!S#O#P!q#P;'S!S;'S;=`#j<%lO!S~!qOZ~~!tRO;'S!S;'S;=`!};=`O!S~#QWOr!Srs!ls#O!S#O#P!q#P;'S!S;'S;=`#j;=`<%l!S<%lO!S~#mP;=`<%l!S~#siqr%bst%btu%buv%bvw%bwx%bz{%b{|%b}!O%b!O!P%b!P!Q%b!Q![%b![!]%b!^!_%b!_!`%b!`!a%b!a!b%b!b!c%b!c!}%b#Q#R%b#R#S%b#S#T%b#T#o%b#p#q%b#r#s%b~%giV~qr%bst%btu%buv%bvw%bwx%bz{%b{|%b}!O%b!O!P%b!P!Q%b!Q![%b![!]%b!^!_%b!_!`%b!`!a%b!a!b%b!b!c%b!c!}%b#Q#R%b#R#S%b#S#T%b#T#o%b#p#q%b#r#s%b~'ZPT~!]!^'^~'aTO!]'^!]!^'p!^;'S'^;'S;=`(_<%lO'^~'sVOy'^yz(Yz!]'^!]!^'p!^;'S'^;'S;=`(_<%lO'^~(_OQ~~(bP;=`<%l'^~(jOS~~(mQ!Q!R(s!R![*p~(xUY~!O!P)[!Q![*p!g!h){#R#S+U#X#Y){#l#m+[~)aRY~!Q![)j!g!h){#X#Y){~)oSY~!Q![)j!g!h){#R#S*j#X#Y){~*OR{|*X}!O*X!Q![*_~*[P!Q![*_~*dQY~!Q![*_#R#S*X~*mP!Q![)j~*uTY~!O!P)[!Q![*p!g!h){#R#S+U#X#Y){~+XP!Q![*p~+_R!Q![+h!c!i+h#T#Z+h~+mVY~!O!P,S!Q![+h!c!i+h!r!s-P#R#S+[#T#Z+h#d#e-P~,XTY~!Q![,h!c!i,h!r!s-P#T#Z,h#d#e-P~,mUY~!Q![,h!c!i,h!r!s-P#R#S.Q#T#Z,h#d#e-P~-ST{|-c}!O-c!Q![-o!c!i-o#T#Z-o~-fR!Q![-o!c!i-o#T#Z-o~-tSY~!Q![-o!c!i-o#R#S-c#T#Z-o~.TR!Q![,h!c!i,h#T#Z,h~.aP!]!^.d~.iSP~OY.dZ;'S.d;'S;=`.u<%lO.d~.xP;=`<%l.d~/QiX~qr.{st.{tu.{uv.{vw.{wx.{z{.{{|.{}!O.{!O!P.{!P!Q.{!Q![.{![!].{!^!_.{!_!`.{!`!a.{!a!b.{!b!c.{!c!}.{#Q#R.{#R#S.{#S#T.{#T#o.{#p#q.{#r#s.{\",\n  tokenizers: [0],\n  topRules: {\"Module\":[0,3]},\n  specialized: [{term: 9, get: (value) => spec_Keyword[value] || -1}],\n  tokenPrec: 0\n});\n\nconst wastLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LRLanguage.define({\n    name: \"wast\",\n    parser: /*@__PURE__*/parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.indentNodeProp.add({\n                App: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.delimitedIndent)({ closing: \")\", align: false })\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldNodeProp.add({\n                App: _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldInside,\n                BlockComment(tree) { return { from: tree.from + 2, to: tree.to - 2 }; }\n            }),\n            /*@__PURE__*/(0,_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.styleTags)({\n                Keyword: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.keyword,\n                Type: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.typeName,\n                Number: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.number,\n                String: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.string,\n                Identifier: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.variableName,\n                LineComment: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.lineComment,\n                BlockComment: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.blockComment,\n                \"( )\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.paren\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { line: \";;\", block: { open: \"(;\", close: \";)\" } },\n        closeBrackets: { brackets: [\"(\", '\"'] }\n    }\n});\nfunction wast() {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LanguageSupport(wastLanguage);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-wast@6.0.2/node_modules/@codemirror/lang-wast/dist/index.js\n");

/***/ })

};
;