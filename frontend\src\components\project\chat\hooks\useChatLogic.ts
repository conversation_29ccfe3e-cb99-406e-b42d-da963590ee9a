import { useState, useEffect } from 'react';
import { Message, CustomGroup } from '../types';
import { initiateAgent, sendMessage, streamAgentWithFormData } from '@/lib/api';
import { FIXED_MODEL, THINKING_ENABLED, COLLABORATION_ENABLED, AGENT_ROLES, ALL_EMPLOYEES } from '../utils';

export function useChatLogic(project?: any) {
  // Initialize selected agents from project or default to CEO
  const getInitialAgents = () => {
    if (project?.selectedAgents && project.selectedAgents.length > 0) {
      console.log('Using project selectedAgents from DB:', project.selectedAgents);
      return project.selectedAgents;
    }
    console.log('Using default agent: Kenard');
    return ['Kenard']; // Default to CEO
  };

  // Initialize activeChat to the first selected agent or default to 'ceo'
  const [activeChat, setActiveChat] = useState<string>(() => {
    if (project?.selectedAgents && project.selectedAgents.length > 0) {
      return project.selectedAgents[0];
    }
    return 'ceo';
  });
  
  // Custom setActiveChat function to ensure consistent chat keys and proper UI updates
  const setActiveChatWithLogging = (chatId: string) => {
    console.log(`Setting activeChat from ${activeChat} to ${chatId}`);
    setActiveChat(chatId);
  };
  // State for messages
  const [ceoMessages, setCeoMessages] = useState<Message[]>([]);
  const [developerMessages, setDeveloperMessages] = useState<Message[]>([]);
  const [groupMessages, setGroupMessages] = useState<Message[]>([]);
  const [customGroups, setCustomGroups] = useState<CustomGroup[]>([]);
  
  // Dynamic agent messages - store messages for all other agent types
  const [dynamicAgentMessages, setDynamicAgentMessages] = useState<Record<string, Message[]>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Thread management for each chat
  const [ceoThreadId, setCeoThreadId] = useState<string | null>(null);
  const [developerThreadId, setDeveloperThreadId] = useState<string | null>(null);
  const [groupThreadId, setGroupThreadId] = useState<string | null>(null);
  
  // Custom group chats state
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([]);
  const [isEmployeePopoverOpen, setIsEmployeePopoverOpen] = useState(false);
  const [projectAgents, setProjectAgents] = useState<string[]>(getInitialAgents());
  const [collaborationMode, setCollaborationMode] = useState<boolean>(project?.settings?.collaborationMode ?? true);

  // Update project agents when project changes
  useEffect(() => {
    console.log('Project changed:', project);
    if (project?.selectedAgents && project.selectedAgents.length > 0) {
      console.log('Setting project agents from DB:', project.selectedAgents);
      setProjectAgents(project.selectedAgents);
      
      // Also update collaboration mode from project settings if available
      if (project.settings?.collaborationMode !== undefined) {
        setCollaborationMode(project.settings.collaborationMode);
      }
    } else {
      console.log('No agents found in project data, using default: [Kenard]');
      setProjectAgents(['Kenard']);
    }
  }, [project]);

  // Function to toggle agent selection
  const toggleAgentSelection = (agentId: string) => {
    setProjectAgents(prev => {
      // If the agent is already selected, remove it (unless it's the last one)
      if (prev.includes(agentId)) {
        // Don't allow removing the last agent
        if (prev.length === 1) {
          return prev;
        }
        return prev.filter(id => id !== agentId);
      }
      
      // If collaboration mode is off, replace the current selection
      if (!collaborationMode) {
        return [agentId];
      }
      
      // Otherwise, add the agent to the selection
      return [...prev, agentId];
    });
  };

  // Get current thread ID based on active chat
  const getCurrentThreadId = () => {
    switch (activeChat) {
      case 'ceo': return ceoThreadId;
      case 'developer': return developerThreadId;
      case 'group': return groupThreadId;
      default: {
        // Check if it's a custom group
        const customGroup = customGroups.find(group => group.id === activeChat);
        if (customGroup) return customGroup.threadId;
        
        // For other agent types (marketing, product, etc.), we don't have specific thread IDs yet
        // In a full implementation, we would store thread IDs for each agent type
        return null;
      }
    }
  };

  // Set thread ID for current chat
  const setCurrentThreadId = (threadId: string) => {
    switch (activeChat) {
      case 'ceo': setCeoThreadId(threadId); break;
      case 'developer': setDeveloperThreadId(threadId); break;
      case 'group': setGroupThreadId(threadId); break;
      default: {
        // Check if it's a custom group
        const customGroup = customGroups.find(group => group.id === activeChat);
        if (customGroup) {
          setCustomGroups(prev => prev.map(group =>
            group.id === activeChat
              ? { ...group, threadId }
              : group
          ));
        }
        // For other agent types (marketing, product, etc.), we would need to store their thread IDs
        // In a full implementation, we would have state variables for each agent type
        console.log(`Setting thread ID for agent ${activeChat}: ${threadId}`);
        // For now, we'll just log it since we don't have specific state for other agents
      }
    }
  };

  // Get current messages based on active chat
  const getCurrentMessages = () => {
    switch (activeChat) {
      case 'ceo': return ceoMessages;
      case 'developer': return developerMessages;
      case 'group': return groupMessages;
      default: {
        // Check if it's a custom group
        const customGroup = customGroups.find(group => group.id === activeChat);
        if (customGroup) return customGroup.messages;
        
        // Check if it's a dynamic agent (marketing, product, etc.)
        return dynamicAgentMessages[activeChat] || [];
      }
    }
  };

  // Set messages for current chat
  const setCurrentMessages = (messages: Message[] | ((prev: Message[]) => Message[])) => {
    switch (activeChat) {
      case 'ceo': setCeoMessages(messages); break;
      case 'developer': setDeveloperMessages(messages); break;
      case 'group': setGroupMessages(messages); break;
      default: {
        // Check if it's a custom group
        const customGroup = customGroups.find(group => group.id === activeChat);
        if (customGroup) {
          setCustomGroups(prev => prev.map(group =>
            group.id === activeChat
              ? { ...group, messages: typeof messages === 'function' ? messages(group.messages) : messages }
              : group
          ));
        } else {
          // Handle dynamic agent messages (marketing, product, etc.)
          setDynamicAgentMessages(prev => {
            const prevMessages = prev[activeChat] || [];
            const newMessages = typeof messages === 'function' ? messages(prevMessages) : messages;
            return { ...prev, [activeChat]: newMessages };
          });
        }
        break;
      }
    }
  };



  // Effect to automatically remove thinking messages when actual content arrives
  useEffect(() => {
    const currentMessages = getCurrentMessages();
    const hasThinkingMessages = currentMessages.some(m => m.type === 'thinking');
    const hasActualContent = currentMessages.some(m => m.role === 'assistant' && m.type !== 'thinking' && m.content && m.content.trim() !== '');

    if (hasThinkingMessages && hasActualContent) {
      setCurrentMessages(prev => prev.filter(m => m.type !== 'thinking'));
    }
  }, [ceoMessages, developerMessages, groupMessages, activeChat]);

  // Create a new group chat
  const createGroupChat = () => {
    if (selectedEmployees.length === 0) {
      return;
    }

    // Get selected employee details
    const selectedEmployeeDetails = ALL_EMPLOYEES.filter(emp =>
      selectedEmployees.includes(emp.id)
    );

    // Generate group name
    const groupName = selectedEmployeeDetails.length <= 2
      ? selectedEmployeeDetails.map(emp => emp.name).join(' & ')
      : `${selectedEmployeeDetails.slice(0, 2).map(emp => emp.name).join(', ')} +${selectedEmployeeDetails.length - 2}`;

    // Create new group
    const newGroup = {
      id: `group-${Date.now()}`,
      name: groupName,
      participants: selectedEmployeeDetails,
      messages: [],
      threadId: null
    };

    // Add to custom groups
    setCustomGroups(prev => [...prev, newGroup]);

    // Clear selection and close popover
    setSelectedEmployees([]);
    setIsEmployeePopoverOpen(false);

    // Switch to the new group
    setActiveChat(newGroup.id);
  };

  // Initiate new conversation with agent
  const initiateConversation = async (content: string) => {
    try {
      setLoading(true);
      setError(null);

      // Determine agents based on chat type
      const selectedAgents = projectAgents;

      // Map the first selected agent to agent_type
      const agentTypeMap: Record<string, string> = {
        'Kenard': 'ceo',
        'Alex': 'developer',
        'Chloe': 'marketing'
      };

      const primaryAgent = selectedAgents.length > 0 ? selectedAgents[0] : 'Alex';
      const agent_type = agentTypeMap[primaryAgent] || 'developer';

      const initiateRequest = {
        agent_type,
        model: FIXED_MODEL,
        message: content,
        metadata: {
          enable_thinking: THINKING_ENABLED,
          agents: selectedAgents,
          collaboration_mode: collaborationMode,
          timestamp: new Date().toISOString()
        }
      };

      const response = await initiateAgent(initiateRequest);
      setCurrentThreadId(response.thread_id);

      // Start streaming response
      await streamResponse(response.thread_id);
    } catch (error) {
      console.error('Error initiating conversation:', error);
      setError(error instanceof Error ? error.message : 'Failed to start conversation');
    } finally {
      setLoading(false);
    }
  };

  // Send follow-up message
  const sendFollowUpMessage = async (content: string) => {
    const threadId = getCurrentThreadId();
    if (!threadId) return;

    try {
      setLoading(true);
      setError(null);

      // Determine agents based on chat type
      const selectedAgents = projectAgents;

      const sendMessageRequest = {
        thread_id: threadId,
        message: content,
        metadata: {
          enable_thinking: THINKING_ENABLED,
          model: FIXED_MODEL,
          selected_agents: selectedAgents,
          collaboration_mode: collaborationMode,
          is_group_chat: activeChat === 'group',
          timestamp: new Date().toISOString()
        }
      };

      await sendMessage(sendMessageRequest);
      await streamResponse(threadId);
    } catch (error) {
      console.error('Error sending message:', error);
      setError(error instanceof Error ? error.message : 'Failed to send message');
    } finally {
      setLoading(false);
    }
  };

  // Stream agent response
  const streamResponse = async (threadId: string) => {
    const aiMessageId = `ai-${Date.now()}`;
    const thinkingMessageId = `thinking-${Date.now()}`;

    // Determine responding agent based on chat type
    let respondingAgent = activeChat;
    
    // For group chats or custom groups, use 'system' as the sender
    if (activeChat === 'group' || customGroups.find(group => group.id === activeChat)) {
      respondingAgent = 'system';
    }
    
    console.log(`Responding agent for ${activeChat} chat is: ${respondingAgent}`);

    // Keep track of accumulated content
    let accumulatedContent = '';
    let messageCreated = false; // Track if we've created the message bubble yet
    let thinkingMessageCreated = false; // Track if we've created a thinking message
    let thinkingMessageRemoved = false; // Track if we've already removed the thinking message
    let hasReceivedContent = false; // Track if we've received any actual content
    let detectedSender = null; // Track if we've detected a different sender in the content

    return new Promise<void>((resolve, reject) => {
      streamAgentWithFormData(threadId, {
        onMessage: (data: any) => {
          console.log('Received streaming data:', data);

          // Handle thinking messages
          if (data.type === 'thinking' && data.content && !hasReceivedContent) {
            if (!thinkingMessageCreated && !messageCreated && !thinkingMessageRemoved) {
              // Create a thinking message bubble
              const thinkingMessage: Message = {
                id: thinkingMessageId,
                sender: data.metadata?.primary_agent || respondingAgent as any,
                content: 'Thinking...',
                timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                role: 'assistant',
                type: 'thinking'
              };

              setCurrentMessages(prev => [...prev, thinkingMessage]);
              thinkingMessageCreated = true;
            }
            return; // Don't process thinking content further
          }

          // Handle actual content - check for any content that's not thinking
          if (data.content && data.type !== 'thinking' && data.type !== 'unknown') {
            hasReceivedContent = true;
            console.log('Processing content chunk:', data.content);

            // Remove thinking message immediately when actual content starts
            if (thinkingMessageCreated && !thinkingMessageRemoved) {
              setCurrentMessages(prev => prev.filter(m => m.id !== thinkingMessageId));
              thinkingMessageCreated = false;
              thinkingMessageRemoved = true;
            }

            // Check for agent identification in the content
            let currentContent = data.content;
            let agentDetected = false;

            // Try to detect agent from the content using regex patterns
            // First try to match format "**Name (Role):**" with improved pattern for names with spaces
            let agentMatch = currentContent.match(/^\*\*([\w\s]+?)\s*\(([^)]+)\):\*\*/);
            if (agentMatch && agentMatch[1]) {
              const agentName = agentMatch[1];
              const agentRole = agentMatch[2];
              console.log(`Detected agent name in content: ${agentName}, role: ${agentRole}`);

              // Try to find by name first
              let agent = AGENT_ROLES.find(a => a.name === agentName);

              // If not found by name, try by role
              if (!agent) {
                agent = AGENT_ROLES.find(a => a.role.toLowerCase().includes(agentRole.toLowerCase()));
              }

              if (agent) {
                detectedSender = agent.id;
                console.log(`Mapped agent name/role to ID: ${detectedSender}`);
                agentDetected = true;
              }
            } else {
              // Try to match just the role or name format "**Role:**" or "**Name:**"
              agentMatch = currentContent.match(/^\*\*([\w\s]+?):\*\*/);
              if (agentMatch && agentMatch[1]) {
                const roleName = agentMatch[1].trim();
                console.log(`Detected role name in content: ${roleName}`);

                // Find the corresponding agent ID from AGENT_ROLES
                const agent = AGENT_ROLES.find(a =>
                  a.role.toLowerCase().includes(roleName.toLowerCase()) ||
                  a.name.toLowerCase() === roleName.toLowerCase()
                );

                if (agent) {
                  detectedSender = agent.id;
                  console.log(`Mapped role name to ID: ${detectedSender}`);
                  agentDetected = true;
                }
              }
            }

            // If no agent detected from content, check if metadata contains agent info
            if (!agentDetected && data.metadata) {
              const metadata = data.metadata;
              if (metadata.primary_agent) {
                // Convert agent name to agent ID
                const agentName = metadata.primary_agent;
                const agent = AGENT_ROLES.find(a =>
                  a.name.toLowerCase() === agentName.toLowerCase() ||
                  a.id.toLowerCase() === agentName.toLowerCase()
                );

                if (agent) {
                  detectedSender = agent.id;
                  console.log(`Mapped primary agent from metadata: ${agentName} to ID: ${detectedSender}`);
                } else {
                  detectedSender = metadata.primary_agent;
                  console.log(`Using primary agent directly from metadata: ${detectedSender}`);
                }
                agentDetected = true;
              }
            }

            // Accumulate the content instead of replacing it
            accumulatedContent += data.content;
            console.log('Accumulated content:', accumulatedContent);

            // Only create the message when we receive the first actual content
            if (!messageCreated) {
              // Set loading to false when we start displaying the message
              setLoading(false);

              const finalSender = detectedSender || respondingAgent;

              const aiMessage: Message = {
                id: aiMessageId,
                sender: finalSender as any,
                content: accumulatedContent,
                timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                role: 'assistant',
                type: 'assistant'
              };

              setCurrentMessages(prev => [...prev, aiMessage]);
              messageCreated = true;
            } else {
              // Update existing message with accumulated content
              setCurrentMessages(prev => {
                const newMessages = [...prev];
                const aiMessageIndex = newMessages.findIndex(m => m.id === aiMessageId);
                if (aiMessageIndex !== -1) {
                  const finalSender = detectedSender || respondingAgent;

                  newMessages[aiMessageIndex] = {
                    ...newMessages[aiMessageIndex],
                    sender: finalSender as any,
                    content: accumulatedContent,
                    type: 'assistant'
                  };
                } else {
                  console.warn('Could not find message with ID:', aiMessageId);
                }
                return newMessages;
              });
            }
          }

          // Handle completion
          if (data.type === 'done' || data.event === 'done') {
            console.log('Stream completed');
            // Final cleanup: ensure thinking message is removed
            if (thinkingMessageCreated && !thinkingMessageRemoved) {
              setCurrentMessages(prev => prev.filter(m => m.id !== thinkingMessageId));
              thinkingMessageCreated = false;
              thinkingMessageRemoved = true;
            }
            resolve();
            return;
          }
        },
        onError: (error: any) => {
          console.error('Streaming error:', error);
          setError('Failed to receive response');
          reject(error);
        },
        onComplete: () => {
          // Final cleanup: ensure thinking message is removed
          if (thinkingMessageCreated && !thinkingMessageRemoved) {
            setCurrentMessages(prev => prev.filter(m => m.id !== thinkingMessageId));
            thinkingMessageCreated = false;
            thinkingMessageRemoved = true;
          }
          resolve();
        }
      });
    });
  };

  return {
    activeChat,
    setActiveChat: setActiveChatWithLogging,
    ceoMessages,
    developerMessages,
    groupMessages,
    customGroups,
    dynamicAgentMessages,
    selectedEmployees,
    setSelectedEmployees,
    isEmployeePopoverOpen,
    setIsEmployeePopoverOpen,
    loading,
    error,
    sendMessage: initiateConversation,
    createGroupChat,
    projectAgents,
    toggleAgentSelection,
    collaborationMode,
    setCollaborationMode,
    getCurrentMessages,
    setCurrentMessages,
    getCurrentThreadId,
    setCurrentThreadId,
    initiateConversation,
    sendFollowUpMessage
  };
}