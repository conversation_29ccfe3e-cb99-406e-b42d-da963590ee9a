"use client";

import React, { useRef } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Mic, 
  MicOff, 
  Video, 
  VideoOff, 
  PhoneOff, 
  MonitorUp, 
  MessageSquare, 
  Users, 
  Volume2, 
  VolumeX 
} from 'lucide-react';
import { CallControlButton } from "./CallControlButton";
import { EndCallButton } from "./EndCallButton";

interface CallControlsProps {
  isMuted: boolean;
  isVideoOn: boolean;
  isScreenSharing: boolean;
  isSpeakerMuted: boolean;
  isChatOpen: boolean;
  isParticipantsOpen: boolean;
  videoDevices: MediaDeviceInfo[];
  selectedDeviceId: string;
  isDeviceMenuOpen: boolean;
  toggleMute: () => void;
  toggleVideo: () => void;
  toggleScreenShare: () => void;
  toggleSpeaker: () => void;
  toggleChat: () => void;
  toggleParticipants: () => void;
  handleVideoRightClick: (e: React.MouseEvent) => void;
  selectCamera: (deviceId: string) => void;
  onEndCall: () => void;
}

export function CallControls({
  isMuted,
  isVideoOn,
  isScreenSharing,
  isSpeakerMuted,
  isChatOpen,
  isParticipantsOpen,
  videoDevices,
  selectedDeviceId,
  isDeviceMenuOpen,
  toggleMute,
  toggleVideo,
  toggleScreenShare,
  toggleSpeaker,
  toggleChat,
  toggleParticipants,
  handleVideoRightClick,
  selectCamera,
  onEndCall
}: CallControlsProps) {
  const deviceMenuRef = useRef<HTMLDivElement>(null);

  return (
    <div className="border-t border-border h-[60px] px-4 flex items-center bg-[#222222] w-full">
      <div className="flex items-center gap-2 w-1/3">
        <CallControlButton
          icon={isMuted ? MicOff : Mic}
          tooltip={isMuted ? "Unmute" : "Mute"}
          onClick={toggleMute}
          isRed={isMuted}
        />
        
        {/* Video toggle with right-click camera selection */}
        <div className="relative" ref={deviceMenuRef}>
          <CallControlButton
            icon={isVideoOn ? Video : VideoOff}
            tooltip={isVideoOn ? "Turn off camera" : "Turn on camera"}
            onClick={toggleVideo}
            onContextMenu={handleVideoRightClick}
          />
          
          {/* Camera selection dropdown - cleaner and more compact */}
          {isDeviceMenuOpen && videoDevices.length > 0 && (
            <div className="absolute bottom-full mb-2 left-0 bg-background/95 backdrop-blur-sm border border-border rounded-md shadow-lg w-56 overflow-hidden z-50">
              {/* Simple list of cameras without header */}
              <div className="py-1">
                {videoDevices.map((device) => (
                  <button
                    key={device.deviceId}
                    onClick={() => selectCamera(device.deviceId)}
                    className="flex items-center justify-between w-full px-2.5 py-1.5 text-sm text-left hover:bg-accent"
                  >
                    <span className="truncate">{device.label || `Camera ${videoDevices.indexOf(device) + 1}`}</span>
                    {selectedDeviceId === device.deviceId && (
                      <div className="h-3 w-3 rounded-full bg-primary ml-2"></div>
                    )}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
        
        <CallControlButton
          icon={MonitorUp}
          tooltip={isScreenSharing ? "Stop sharing" : "Share screen"}
          onClick={toggleScreenShare}
          isActive={isScreenSharing}
        />
        
        <CallControlButton
          icon={isSpeakerMuted ? VolumeX : Volume2}
          tooltip={isSpeakerMuted ? "Unmute speaker" : "Mute speaker"}
          onClick={toggleSpeaker}
          isRed={isSpeakerMuted}
        />
      </div>
      
      {/* Center the End Call button */}
      <div className="flex justify-center items-center w-1/3">
        <EndCallButton onClick={onEndCall} />
      </div>
      
      {/* Right side buttons */}
      <div className="flex items-center gap-2 w-1/3 justify-end">
        <CallControlButton
          icon={MessageSquare}
          tooltip="Chat"
          onClick={toggleChat}
          isActive={isChatOpen}
        />
        
        <CallControlButton
          icon={Users}
          tooltip="Participants"
          onClick={toggleParticipants}
          isActive={isParticipantsOpen}
        />
      </div>
    </div>
  );
}