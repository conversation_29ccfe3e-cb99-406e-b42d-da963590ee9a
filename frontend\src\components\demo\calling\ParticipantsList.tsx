"use client";

import React, { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Mi<PERSON>, MicOff, Video, VideoOff, MoreVertical, Pin } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';

interface ParticipantsListProps {
  participants: Array<{
    id: string;
    name: string;
    avatar: string;
    role: string;
  }>;
}

export function ParticipantsList({ participants }: ParticipantsListProps) {
  const [pinnedParticipant, setPinnedParticipant] = useState<string | null>(null);

  const togglePin = (id: string) => {
    if (pinnedParticipant === id) {
      setPinnedParticipant(null);
    } else {
      setPinnedParticipant(id);
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b">
        <h2 className="text-lg font-semibold">Participants ({participants.length + 1})</h2>
      </div>
      
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full p-2">
          <div className="space-y-1">
          {/* Current user */}
          <div className="p-2 rounded-md hover:bg-muted flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/demo-assets/user-avatar.png" alt="You" />
                <AvatarFallback>You</AvatarFallback>
              </Avatar>
              <div>
                <p className="text-sm font-medium">You (Host)</p>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <Mic className="h-4 w-4 text-green-500" />
              <Video className="h-4 w-4 text-green-500" />
            </div>
          </div>
          
          {/* Participants */}
          {participants.map(participant => {
            const isMuted = participant.id === '3' || participant.id === '5';
            const isVideoOff = participant.id === '4';
            const isPinned = pinnedParticipant === participant.id;
            
            return (
              <div 
                key={participant.id}
                className="p-2 rounded-md hover:bg-muted flex items-center justify-between"
              >
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={participant.avatar} alt={participant.name} />
                    <AvatarFallback>{participant.name[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">{participant.name}</p>
                    <p className="text-xs text-muted-foreground">{participant.role}</p>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  {isMuted ? (
                    <MicOff className="h-4 w-4 text-destructive" />
                  ) : (
                    <Mic className="h-4 w-4 text-green-500" />
                  )}
                  
                  {isVideoOff ? (
                    <VideoOff className="h-4 w-4 text-destructive" />
                  ) : (
                    <Video className="h-4 w-4 text-green-500" />
                  )}
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => togglePin(participant.id)}>
                        <Pin className="h-4 w-4 mr-2" />
                        {isPinned ? 'Unpin' : 'Pin'} participant
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <MicOff className="h-4 w-4 mr-2" />
                        Mute participant
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <VideoOff className="h-4 w-4 mr-2" />
                        Turn off video
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            );
          })}
          </div>
        </ScrollArea>
      </div>
    </div>
  );
}