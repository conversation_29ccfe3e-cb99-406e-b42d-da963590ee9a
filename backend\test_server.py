#!/usr/bin/env python3
"""
Simple test server for code execution functionality
"""

import sys
import os
sys.path.append('.')

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import our code execution functionality
from sandbox.code_executor import execute_code, CodeExecutionRequest, CodeExecutionResponse

app = FastAPI(title="Code Execution Test Server")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Code Execution Test Server is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "e2b_api_key_set": bool(os.getenv("E2B_API_KEY"))}

@app.post("/api/sandbox/execute", response_model=CodeExecutionResponse)
async def execute_code_endpoint(request: CodeExecutionRequest):
    """
    Execute code using E2B code interpreter
    """
    try:
        print(f"Received code execution request: {request.language}")
        print(f"Code length: {len(request.code)} characters")
        
        result = await execute_code(request)
        
        print(f"Execution completed: success={result.error is None}")
        if result.images:
            print(f"Generated {len(result.images)} images")
        
        return result
    except Exception as e:
        print(f"Error executing code: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/test/simple")
async def test_simple_execution():
    """Test endpoint for simple code execution"""
    request = CodeExecutionRequest(
        code="print('Hello from E2B!')\nprint(f'2 + 2 = {2 + 2}')",
        language="python",
        timeout=10000
    )
    
    try:
        result = await execute_code(request)
        return {
            "success": result.error is None,
            "output": result.output,
            "error": result.error,
            "execution_time": result.execution_time
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

@app.post("/test/matplotlib")
async def test_matplotlib_execution():
    """Test endpoint for matplotlib code execution"""
    code = '''
import matplotlib.pyplot as plt
import numpy as np

# Create sample data
x = np.linspace(0, 10, 100)
y1 = np.sin(x)
y2 = np.cos(x)

# Create the plot
plt.figure(figsize=(10, 6))
plt.plot(x, y1, 'b-', linewidth=2, label='sin(x)')
plt.plot(x, y2, 'r--', linewidth=2, label='cos(x)')
plt.xlabel('x')
plt.ylabel('y')
plt.title('Sine and Cosine Functions')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

print("Matplotlib plot generated successfully!")
'''
    
    request = CodeExecutionRequest(
        code=code,
        language="python",
        timeout=30000
    )
    
    try:
        result = await execute_code(request)
        return {
            "success": result.error is None,
            "output": result.output,
            "error": result.error,
            "execution_time": result.execution_time,
            "image_count": len(result.images) if result.images else 0,
            "has_images": bool(result.images)
        }
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    import uvicorn
    print("Starting Code Execution Test Server...")
    print("E2B API Key set:", bool(os.getenv("E2B_API_KEY")))
    uvicorn.run(app, host="127.0.0.1", port=8002, log_level="info")
