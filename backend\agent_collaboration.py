"""
Agent Collaboration Module for Siden.

This module provides functionality for agent-to-agent communication with message queuing
and robust error handling, enabling teams of AI agents to collaborate effectively.

Features:
- Message queuing for reliable agent-to-agent communication
- Robust error handling for failed communications
- Retry mechanisms for failed message deliveries
- Dead letter queue for undeliverable messages
- Message status tracking and monitoring
"""

import asyncio
import json
import uuid
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime, timezone, timedelta
from enum import Enum

from utils.logger import logger
from services import redis
from services.supabase import DBConnection
from agentpress.agent_communication import AgentCommunicationManager, MessageType


# Constants
QUEUE_PREFIX = "agent:queue:"
DEAD_LETTER_QUEUE = "agent:dead_letter_queue"
MAX_RETRY_COUNT = 3
RETRY_DELAY_SECONDS = 5
MESSAGE_EXPIRY = 3600 * 24  # 24 hours
PROCESSING_TIMEOUT = 60  # 60 seconds


class MessageStatus(Enum):
    """Status of a message in the queue."""
    PENDING = "pending"       # Message is waiting to be processed
    PROCESSING = "processing" # Message is being processed
    DELIVERED = "delivered"   # Message was successfully delivered
    FAILED = "failed"         # Message delivery failed
    RETRYING = "retrying"     # Message is being retried
    DEAD = "dead"             # Message moved to dead letter queue


class AgentCollaborationManager:
    """Manages collaboration between agents with message queuing and error handling.
    
    This class extends the functionality of AgentCommunicationManager by adding
    message queuing, retry mechanisms, and robust error handling.
    """
    
    def __init__(self, project_id: Optional[str] = None):
        """Initialize the AgentCollaborationManager.
        
        Args:
            project_id: Optional ID of the project this manager is associated with
        """
        self.db = DBConnection()
        self.project_id = project_id
        self.agent_communication = AgentCommunicationManager(project_id)
        self._processing_tasks = {}
        logger.debug(f"Initialized AgentCollaborationManager for project {project_id}")
    
    async def send_message_with_queue(
        self,
        from_agent_id: str,
        to_agent_id: str,
        message_type: MessageType,
        content: Dict[str, Any],
        thread_id: Optional[str] = None,
        reference_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        priority: int = 1,
        retry_count: int = MAX_RETRY_COUNT
    ) -> str:
        """Send a message from one agent to another using a queue for reliability.
        
        Args:
            from_agent_id: ID of the sending agent
            to_agent_id: ID of the receiving agent
            message_type: Type of message being sent
            content: Message content
            thread_id: Optional thread ID for conversation tracking
            reference_id: Optional reference to another message
            metadata: Optional additional metadata
            priority: Message priority (1-5, with 1 being highest)
            retry_count: Number of times to retry delivery if it fails
            
        Returns:
            ID of the created message
            
        Raises:
            ValueError: If the agents cannot communicate with each other
        """
        try:
            # Generate a new message ID
            message_id = str(uuid.uuid4())
            
            # Create thread ID if not provided
            if not thread_id:
                thread_id = str(uuid.uuid4())
            
            # Prepare message data
            message_data = {
                "message_id": message_id,
                "thread_id": thread_id,
                "from_agent_id": from_agent_id,
                "to_agent_id": to_agent_id,
                "message_type": message_type.value,
                "content": content,
                "reference_id": reference_id,
                "metadata": metadata or {},
                "created_at": datetime.now(timezone.utc).isoformat(),
                "status": MessageStatus.PENDING.value,
                "priority": priority,
                "retry_count": retry_count,
                "attempts": 0,
                "last_attempt": None,
                "error": None
            }
            
            # Add message to the agent's queue
            queue_key = f"{QUEUE_PREFIX}{to_agent_id}"
            await redis.rpush(queue_key, json.dumps(message_data))
            
            # Set expiry on the queue key as a safety mechanism
            await redis.expire(queue_key, MESSAGE_EXPIRY)
            
            # Store message status in Redis for tracking
            status_key = f"agent:message:{message_id}"
            await redis.set(status_key, json.dumps({
                "message_id": message_id,
                "status": MessageStatus.PENDING.value,
                "created_at": message_data["created_at"],
                "updated_at": message_data["created_at"]
            }), ex=MESSAGE_EXPIRY)
            
            logger.info(f"Queued message {message_id} from {from_agent_id} to {to_agent_id} with type {message_type.value}")
            
            # Start processing the queue if not already processing
            await self._ensure_queue_processing(to_agent_id)
            
            return message_id
            
        except Exception as e:
            logger.error(f"Error queuing message: {str(e)}", exc_info=True)
            raise
    
    async def _ensure_queue_processing(self, agent_id: str):
        """Ensure that the agent's message queue is being processed.
        
        Args:
            agent_id: ID of the agent whose queue should be processed
        """
        if agent_id not in self._processing_tasks or self._processing_tasks[agent_id].done():
            # Start a new task to process the queue
            self._processing_tasks[agent_id] = asyncio.create_task(
                self._process_agent_queue(agent_id)
            )
            logger.debug(f"Started queue processing task for agent {agent_id}")
    
    async def _process_agent_queue(self, agent_id: str):
        """Process messages in an agent's queue.
        
        Args:
            agent_id: ID of the agent whose queue to process
        """
        queue_key = f"{QUEUE_PREFIX}{agent_id}"
        processing_lock_key = f"agent:queue:lock:{agent_id}"
        
        try:
            # Try to acquire a lock to process this queue
            # This prevents multiple instances from processing the same queue
            lock_acquired = await redis.set(
                processing_lock_key, 
                "1", 
                ex=PROCESSING_TIMEOUT,
                nx=True  # Only set if key doesn't exist
            )
            
            if not lock_acquired:
                logger.debug(f"Queue for agent {agent_id} is already being processed by another instance")
                return
            
            logger.debug(f"Starting to process queue for agent {agent_id}")
            
            while True:
                # Extend the lock to prevent it from expiring while we're processing
                await redis.expire(processing_lock_key, PROCESSING_TIMEOUT)
                
                # Get the next message from the queue (without removing it)
                message_json = await redis.lindex(queue_key, 0)
                
                if not message_json:
                    # Queue is empty
                    logger.debug(f"Queue for agent {agent_id} is empty")
                    break
                
                # Parse the message
                message = json.loads(message_json)
                message_id = message.get("message_id")
                
                try:
                    # Update message status to processing
                    await self._update_message_status(
                        message_id, 
                        MessageStatus.PROCESSING,
                        {"attempts": message.get("attempts", 0) + 1}
                    )
                    
                    # Try to deliver the message
                    await self.agent_communication.send_message(
                        from_agent_id=message.get("from_agent_id"),
                        to_agent_id=message.get("to_agent_id"),
                        message_type=MessageType(message.get("message_type")),
                        content=message.get("content"),
                        thread_id=message.get("thread_id"),
                        reference_id=message.get("reference_id"),
                        metadata=message.get("metadata")
                    )
                    
                    # Message delivered successfully
                    await self._update_message_status(message_id, MessageStatus.DELIVERED)
                    
                    # Remove the message from the queue
                    await redis.lrem(queue_key, 1, message_json)
                    
                    logger.info(f"Successfully delivered message {message_id} to agent {agent_id}")
                    
                except Exception as e:
                    # Message delivery failed
                    error_message = str(e)
                    logger.error(f"Failed to deliver message {message_id} to agent {agent_id}: {error_message}")
                    
                    # Update message with error
                    message["error"] = error_message
                    message["last_attempt"] = datetime.now(timezone.utc).isoformat()
                    message["attempts"] = message.get("attempts", 0) + 1
                    
                    # Check if we should retry
                    if message.get("attempts", 0) < message.get("retry_count", MAX_RETRY_COUNT):
                        # Update status to retrying
                        await self._update_message_status(
                            message_id, 
                            MessageStatus.RETRYING,
                            {
                                "error": error_message,
                                "attempts": message["attempts"],
                                "last_attempt": message["last_attempt"]
                            }
                        )
                        
                        # Remove the message from the queue
                        await redis.lrem(queue_key, 1, message_json)
                        
                        # Add it back with updated information after a delay
                        await asyncio.sleep(RETRY_DELAY_SECONDS)
                        await redis.rpush(queue_key, json.dumps(message))
                        
                        logger.info(f"Scheduled retry for message {message_id} to agent {agent_id} (attempt {message['attempts']})")
                        
                    else:
                        # Move to dead letter queue
                        await self._update_message_status(
                            message_id, 
                            MessageStatus.DEAD,
                            {
                                "error": error_message,
                                "attempts": message["attempts"],
                                "last_attempt": message["last_attempt"]
                            }
                        )
                        
                        # Remove from the original queue
                        await redis.lrem(queue_key, 1, message_json)
                        
                        # Add to dead letter queue
                        await redis.rpush(DEAD_LETTER_QUEUE, json.dumps(message))
                        await redis.expire(DEAD_LETTER_QUEUE, MESSAGE_EXPIRY)
                        
                        logger.warning(f"Moved message {message_id} to dead letter queue after {message['attempts']} failed attempts")
        
        except Exception as e:
            logger.error(f"Error processing queue for agent {agent_id}: {str(e)}", exc_info=True)
        
        finally:
            # Release the lock
            await redis.delete(processing_lock_key)
            logger.debug(f"Released processing lock for agent {agent_id}")
    
    async def _update_message_status(
        self, 
        message_id: str, 
        status: MessageStatus,
        additional_data: Optional[Dict[str, Any]] = None
    ):
        """Update the status of a message.
        
        Args:
            message_id: ID of the message to update
            status: New status for the message
            additional_data: Optional additional data to include in the status update
        """
        status_key = f"agent:message:{message_id}"
        
        try:
            # Get current status
            current_status_json = await redis.get(status_key)
            
            if current_status_json:
                current_status = json.loads(current_status_json)
            else:
                current_status = {
                    "message_id": message_id,
                    "created_at": datetime.now(timezone.utc).isoformat()
                }
            
            # Update status
            current_status["status"] = status.value
            current_status["updated_at"] = datetime.now(timezone.utc).isoformat()
            
            # Add additional data if provided
            if additional_data:
                current_status.update(additional_data)
            
            # Save updated status
            await redis.set(status_key, json.dumps(current_status), ex=MESSAGE_EXPIRY)
            
            logger.debug(f"Updated status of message {message_id} to {status.value}")
            
        except Exception as e:
            logger.error(f"Error updating message status: {str(e)}", exc_info=True)
    
    async def get_message_status(self, message_id: str) -> Dict[str, Any]:
        """Get the status of a message.
        
        Args:
            message_id: ID of the message to get status for
            
        Returns:
            Message status information
        """
        status_key = f"agent:message:{message_id}"
        
        try:
            status_json = await redis.get(status_key)
            
            if status_json:
                return json.loads(status_json)
            else:
                logger.warning(f"No status found for message {message_id}")
                return {
                    "message_id": message_id,
                    "status": "unknown",
                    "error": "Message status not found"
                }
                
        except Exception as e:
            logger.error(f"Error getting message status: {str(e)}", exc_info=True)
            return {
                "message_id": message_id,
                "status": "error",
                "error": f"Error retrieving status: {str(e)}"
            }
    
    async def retry_dead_letter(self, message_id: str) -> bool:
        """Retry a message from the dead letter queue.
        
        Args:
            message_id: ID of the message to retry
            
        Returns:
            Whether the operation was successful
        """
        try:
            # Get all messages from the dead letter queue
            dead_letters_json = await redis.lrange(DEAD_LETTER_QUEUE, 0, -1)
            
            for i, message_json in enumerate(dead_letters_json):
                message = json.loads(message_json)
                
                if message.get("message_id") == message_id:
                    # Reset retry information
                    message["attempts"] = 0
                    message["last_attempt"] = None
                    message["error"] = None
                    
                    # Update status
                    await self._update_message_status(
                        message_id, 
                        MessageStatus.PENDING,
                        {"attempts": 0, "error": None}
                    )
                    
                    # Remove from dead letter queue
                    await redis.lrem(DEAD_LETTER_QUEUE, 1, message_json)
                    
                    # Add back to the original queue
                    queue_key = f"{QUEUE_PREFIX}{message.get('to_agent_id')}"
                    await redis.rpush(queue_key, json.dumps(message))
                    await redis.expire(queue_key, MESSAGE_EXPIRY)
                    
                    # Ensure queue processing
                    await self._ensure_queue_processing(message.get("to_agent_id"))
                    
                    logger.info(f"Retrying message {message_id} from dead letter queue")
                    return True
            
            logger.warning(f"Message {message_id} not found in dead letter queue")
            return False
            
        except Exception as e:
            logger.error(f"Error retrying message from dead letter queue: {str(e)}", exc_info=True)
            return False
    
    async def get_dead_letter_queue(self, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """Get messages from the dead letter queue.
        
        Args:
            limit: Maximum number of messages to return
            offset: Offset for pagination
            
        Returns:
            List of messages in the dead letter queue
        """
        try:
            # Get messages from the dead letter queue
            end_index = offset + limit - 1
            dead_letters_json = await redis.lrange(DEAD_LETTER_QUEUE, offset, end_index)
            
            # Parse messages
            dead_letters = []
            for message_json in dead_letters_json:
                dead_letters.append(json.loads(message_json))
            
            logger.debug(f"Retrieved {len(dead_letters)} messages from dead letter queue")
            return dead_letters
            
        except Exception as e:
            logger.error(f"Error getting dead letter queue: {str(e)}", exc_info=True)
            return []
    
    async def get_agent_queue_length(self, agent_id: str) -> int:
        """Get the number of messages in an agent's queue.
        
        Args:
            agent_id: ID of the agent
            
        Returns:
            Number of messages in the queue
        """
        try:
            queue_key = f"{QUEUE_PREFIX}{agent_id}"
            return await redis.llen(queue_key)
            
        except Exception as e:
            logger.error(f"Error getting queue length for agent {agent_id}: {str(e)}", exc_info=True)
            return 0
    
    async def clear_agent_queue(self, agent_id: str) -> bool:
        """Clear an agent's message queue.
        
        Args:
            agent_id: ID of the agent
            
        Returns:
            Whether the operation was successful
        """
        try:
            queue_key = f"{QUEUE_PREFIX}{agent_id}"
            await redis.delete(queue_key)
            logger.info(f"Cleared queue for agent {agent_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing queue for agent {agent_id}: {str(e)}", exc_info=True)
            return False