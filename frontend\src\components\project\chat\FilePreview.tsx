'use client';

import React from 'react';
import { FileText, X } from 'lucide-react';
import { formatFileSize } from './utils';

interface FilePreviewProps {
  pendingFiles: File[];
  filePreviewUrls: {[key: string]: string};
  processingFiles: boolean;
  removeFileFromPreview: (index: number) => void;
}

export function FilePreview({
  pendingFiles,
  filePreviewUrls,
  processingFiles,
  removeFileFromPreview
}: FilePreviewProps) {
  if (pendingFiles.length === 0) {
    return null;
  }

  return (
    <div className="px-4 py-3 border-t border-[#333333] bg-[#1a1a1a]">
      <div className="flex items-center gap-3 overflow-x-auto pb-2 pt-1">
        {pendingFiles.map((file, index) => (
          <div key={index} className="flex-shrink-0 relative group">
            <div className="flex items-center gap-3 bg-[#2a2a2a] border border-[#333333] rounded-lg p-3 pr-12 min-w-[180px]">
              {file.type.startsWith('image/') && filePreviewUrls[file.name] ? (
                <img
                  src={filePreviewUrls[file.name]}
                  alt={file.name}
                  className="w-10 h-10 object-cover rounded-md"
                />
              ) : (
                <div className="w-10 h-10 bg-[#444444] rounded-md flex items-center justify-center">
                  <FileText className="w-5 h-5 text-[#999999]" />
                </div>
              )}
              <div className="min-w-0 flex-1">
                <p className="text-sm text-white truncate">{file.name}</p>
                <p className="text-xs text-[#999999]">{formatFileSize(file.size)}</p>
              </div>
              {processingFiles && (
                <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
                  <div className="w-4 h-4 border-2 border-[#999999] border-t-transparent rounded-full animate-spin"></div>
                </div>
              )}
            </div>
            <button
              onClick={() => removeFileFromPreview(index)}
              className="absolute top-1 right-1 w-6 h-6 bg-red-500 hover:bg-red-600 rounded-md flex items-center justify-center text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity shadow-lg z-10"
              disabled={processingFiles}
            >
              <X className="w-3 h-3" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
