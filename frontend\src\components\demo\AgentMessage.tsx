import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';

export interface AgentMessageProps {
  agent: 'ceo' | 'developer';
  content: string;
  timestamp: string;
  isCode?: boolean;
}

export function AgentMessage({ agent, content, timestamp, isCode = false }: AgentMessageProps) {
  return (
    <div className={cn(
      "flex gap-3 mb-4",
      agent === 'ceo' ? "flex-row" : "flex-row-reverse"
    )}>
      <Avatar className="h-8 w-8">
        <AvatarImage
          src={agent === 'ceo' ? '/roles/kenard.svg' : '/roles/alex.svg'}
          alt={agent === 'ceo' ? 'CEO' : 'Developer'}
        />
        <AvatarFallback>{agent === 'ceo' ? 'CEO' : 'DEV'}</AvatarFallback>
      </Avatar>
      <div className={cn(
        "max-w-[80%] rounded-lg p-3",
        agent === 'ceo'
          ? "bg-primary text-primary-foreground"
          : "bg-muted text-muted-foreground",
        isCode && "font-mono text-xs"
      )}>
        <div className="font-semibold text-xs mb-1">
          {agent === 'ceo' ? 'Kenard (CEO)' : 'Alex (Developer)'}
        </div>
        {isCode ? (
          <pre className="whitespace-pre-wrap overflow-x-auto">{content}</pre>
        ) : (
          <p className="text-sm">{content}</p>
        )}
        <div className="text-xs opacity-70 mt-1 text-right">{timestamp}</div>
      </div>
    </div>
  );
}
