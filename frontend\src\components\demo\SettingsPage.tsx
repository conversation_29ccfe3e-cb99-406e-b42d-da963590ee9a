'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings, Shield, Bell, Save, Trash2, Globe, CheckCircle, AlertCircle, Copy } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SettingsPageProps {
  project?: any;
}

export function SettingsPage({ project }: SettingsPageProps) {
  const [projectName, setProjectName] = useState(project?.name || 'Siden.ai Demo');
  const [projectDescription, setProjectDescription] = useState(project?.description || 'AI agents that automatically handle site crashes and errors without human intervention');
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [slackNotifications, setSlackNotifications] = useState(true);
  const [smsNotifications, setSmsNotifications] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [requireApproval, setRequireApproval] = useState(false);
  const [timezone, setTimezone] = useState('UTC');
  const [customDomain, setCustomDomain] = useState('');
  const [domainVerified, setDomainVerified] = useState(false);

  // Update state when project changes
  useEffect(() => {
    if (project) {
      setProjectName(project.name || 'Siden.ai Demo');
      setProjectDescription(project.description || 'AI agents that automatically handle site crashes and errors without human intervention');
    }
  }, [project]);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="h-[60px] px-6 flex items-center border-b border-border">
        <div className="flex items-center gap-4">
          <h1 className="text-white font-semibold text-lg">Project Settings</h1>
          <div className="text-sm text-[#999999]">
            Configure your project preferences and notifications
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="p-6">
        <div className="relative bg-card border border-border rounded-md py-12 px-6 overflow-hidden">
          {/* Subtle gradient background */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/3"></div>

          <div className="relative max-w-3xl mx-auto text-center">
            <h1 className="text-3xl md:text-4xl font-semibold text-foreground mb-3 tracking-tight">
              Project Settings
            </h1>
            <p className="text-muted-foreground text-base leading-relaxed">
              Configure your project preferences and notifications
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">

        {/* Settings Sections */}
        <div className="space-y-8">

          {/* General Settings */}
          <div className="bg-card border border-border rounded-md p-6 shadow-sm">
            <div className="flex items-center gap-3 mb-6">
              <Settings className="h-5 w-5 text-muted-foreground" />
              <div>
                <h2 className="text-lg font-semibold text-foreground">General</h2>
                <p className="text-sm text-muted-foreground">Basic project information and settings</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="project-name" className="text-sm font-medium">Project Name</Label>
                  <Input
                    id="project-name"
                    value={projectName}
                    onChange={(e) => setProjectName(e.target.value)}
                    className="h-9"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timezone" className="text-sm font-medium">Timezone</Label>
                  <Select value={timezone} onValueChange={setTimezone}>
                    <SelectTrigger className="h-9">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="UTC">UTC</SelectItem>
                      <SelectItem value="America/New_York">Eastern Time</SelectItem>
                      <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                      <SelectItem value="Europe/London">London</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="project-description" className="text-sm font-medium">Project Description</Label>
                  <Textarea
                    id="project-description"
                    value={projectDescription}
                    onChange={(e) => setProjectDescription(e.target.value)}
                    rows={4}
                    className="resize-none"
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3 mt-6 pt-4 border-t border-border">
              <Button size="sm" className="h-8">
                <Save className="h-3 w-3 mr-2" />
                Save Changes
              </Button>
              <Button variant="outline" size="sm" className="h-8 text-destructive hover:text-destructive">
                <Trash2 className="h-3 w-3 mr-2" />
                Delete Project
              </Button>
            </div>
          </div>

          {/* Domain Setup */}
          <div className="bg-card border border-border rounded-md p-6 shadow-sm">
            <div className="flex items-center gap-3 mb-6">
              <Globe className="h-5 w-5 text-muted-foreground" />
              <div>
                <h2 className="text-lg font-semibold text-foreground">Domain Setup</h2>
                <p className="text-sm text-muted-foreground">Configure custom domain for agent emails and services</p>
              </div>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="custom-domain" className="text-sm font-medium">Custom Domain</Label>
                    <Input
                      id="custom-domain"
                      value={customDomain}
                      onChange={(e) => setCustomDomain(e.target.value)}
                      placeholder="yourdomain.com"
                      className="h-9"
                    />
                    <p className="text-xs text-muted-foreground">Connect your custom domain for agent emails and services</p>
                  </div>

                  <div className="p-3 bg-muted/30 rounded-lg border">
                    <p className="text-xs text-muted-foreground mb-2">Once verified, you can:</p>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      <li>• Use custom email addresses for agents</li>
                      <li>• Brand agent communications with your domain</li>
                      <li>• Configure agent emails in Team Members</li>
                    </ul>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="p-4 border border-border rounded-lg bg-muted/30">
                    <div className="flex items-center gap-2 mb-3">
                      {domainVerified ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-amber-500" />
                      )}
                      <span className="text-sm font-medium">
                        {domainVerified ? 'Domain Verified' : 'Domain Not Verified'}
                      </span>
                    </div>

                    {!domainVerified && customDomain && (
                      <div className="space-y-3">
                        <p className="text-xs text-muted-foreground">Add these DNS records to verify your domain:</p>

                        <div className="space-y-2">
                          <div className="flex items-center justify-between p-2 bg-background rounded border">
                            <div className="text-xs font-mono">
                              <div>Type: <span className="text-foreground">TXT</span></div>
                              <div>Name: <span className="text-foreground">_sidena-verify</span></div>
                              <div>Value: <span className="text-foreground">sidena-verify-{Math.random().toString(36).substring(7)}</span></div>
                            </div>
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>

                          <div className="flex items-center justify-between p-2 bg-background rounded border">
                            <div className="text-xs font-mono">
                              <div>Type: <span className="text-foreground">MX</span></div>
                              <div>Name: <span className="text-foreground">@</span></div>
                              <div>Value: <span className="text-foreground">10 mail.sidena.ai</span></div>
                            </div>
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex items-center justify-between mt-6 pt-4 border-t border-border">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border border-border rounded flex items-center justify-center">
                  <svg className="w-2 h-2 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <span className="text-sm text-muted-foreground">Domain Setup Guide</span>
              </div>
              <div className="flex items-center gap-3">
                {customDomain && !domainVerified && (
                  <Button variant="outline" size="sm" className="h-8">
                    Verify Domain
                  </Button>
                )}
                <Button size="sm" className="h-8">
                  <Save className="h-3 w-3 mr-2" />
                  Save Domain
                </Button>
              </div>
            </div>
          </div>

          {/* Notifications */}
          <div className="bg-card border border-border rounded-md p-6 shadow-sm">
            <div className="flex items-center gap-3 mb-6">
              <Bell className="h-5 w-5 text-muted-foreground" />
              <div>
                <h2 className="text-lg font-semibold text-foreground">Notifications</h2>
                <p className="text-sm text-muted-foreground">Configure how and when you receive notifications</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium">Enable Notifications</Label>
                  <p className="text-xs text-muted-foreground">Receive notifications about project activities</p>
                </div>
                <Switch
                  checked={notificationsEnabled}
                  onCheckedChange={setNotificationsEnabled}
                />
              </div>

              {notificationsEnabled && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-border">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Email</Label>
                      <p className="text-xs text-muted-foreground">Get notified via email</p>
                    </div>
                    <Switch
                      checked={emailNotifications}
                      onCheckedChange={setEmailNotifications}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Slack</Label>
                      <p className="text-xs text-muted-foreground">Get notified via Slack</p>
                    </div>
                    <Switch
                      checked={slackNotifications}
                      onCheckedChange={setSlackNotifications}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">SMS</Label>
                      <p className="text-xs text-muted-foreground">Get notified via SMS</p>
                    </div>
                    <Switch
                      checked={smsNotifications}
                      onCheckedChange={setSmsNotifications}
                    />
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center justify-between mt-6 pt-4 border-t border-border">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border border-border rounded flex items-center justify-center">
                  <svg className="w-2 h-2 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <span className="text-sm text-muted-foreground">Documentation</span>
              </div>
              <Button size="sm" className="h-8">
                <Save className="h-3 w-3 mr-2" />
                Save Changes
              </Button>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
}
