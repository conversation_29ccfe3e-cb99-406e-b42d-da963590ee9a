import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AgentMessage } from './AgentMessage';
import { Loader2 } from 'lucide-react';

interface AgentCommunicationProps {
  isActive: boolean;
  errorTriggered: boolean;
}

export function AgentCommunication({ isActive, errorTriggered }: AgentCommunicationProps) {
  const [messages, setMessages] = useState<React.ComponentProps<typeof AgentMessage>[]>([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Demo conversation flow
  const conversationSteps = useMemo(() => [
    {
      agent: 'ceo' as const,
      content: 'ALERT: Our website is down! I just received an error notification. <PERSON>, can you investigate this issue immediately?',
      timestamp: '2:14 AM',
    },
    {
      agent: 'developer' as const,
      content: 'I\'m on it, <PERSON><PERSON>. Let me check the logs and identify the source of the error.',
      timestamp: '2:15 AM',
    },
    {
      agent: 'developer' as const,
      content: 'I\'ve identified the issue. There\'s an error in the UserDashboard component. It\'s trying to access a property of an undefined object.',
      timestamp: '2:17 AM',
    },
    {
      agent: 'ceo' as const,
      content: 'How critical is this? What\'s our estimated downtime?',
      timestamp: '2:18 AM',
    },
    {
      agent: 'developer' as const,
      content: 'It\'s affecting all users trying to access their dashboard. I can fix this quickly - estimated fix time is 5-10 minutes.',
      timestamp: '2:19 AM',
    },
    {
      agent: 'ceo' as const,
      content: 'Proceed with the fix. Keep me updated on your progress.',
      timestamp: '2:20 AM',
    },
    {
      agent: 'developer' as const,
      content: 'I\'m accessing the codebase now. Here\'s the problematic code:',
      timestamp: '2:22 AM',
      isCode: true,
    },
    {
      agent: 'developer' as const,
      content: `// The bug is in this section
const UserDashboard = ({ userId }) => {
  const { data } = useUserData(userId);

  // Error happens here - data is undefined when API call fails
  const userMetrics = data.metrics;

  return (
    <div>
      <h2>Welcome, {data.name}</h2>
      <UserMetricsDisplay metrics={userMetrics} />
    </div>
  );
};`,
      timestamp: '2:23 AM',
      isCode: true,
    },
    {
      agent: 'developer' as const,
      content: 'I\'m implementing a fix with proper error handling and loading states:',
      timestamp: '2:25 AM',
    },
    {
      agent: 'developer' as const,
      content: `// Fixed version with proper error handling
const UserDashboard = ({ userId }) => {
  const { data, isLoading, error } = useUserData(userId);

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorDisplay message={error.message} />;
  if (!data) return <ErrorDisplay message="Failed to load user data" />;

  const userMetrics = data.metrics || {};

  return (
    <div>
      <h2>Welcome, {data.name}</h2>
      <UserMetricsDisplay metrics={userMetrics} />
    </div>
  );
};`,
      timestamp: '2:28 AM',
      isCode: true,
    },
    {
      agent: 'developer' as const,
      content: 'I\'ve tested the fix locally and it works correctly. I\'m deploying the changes to production now.',
      timestamp: '2:32 AM',
    },
    {
      agent: 'ceo' as const,
      content: 'Excellent work. How can we prevent this type of issue in the future?',
      timestamp: '2:33 AM',
    },
    {
      agent: 'developer' as const,
      content: 'I recommend implementing stricter TypeScript typing, adding more comprehensive error handling throughout the app, and setting up better monitoring for these types of errors.',
      timestamp: '2:35 AM',
    },
    {
      agent: 'developer' as const,
      content: 'The fix has been deployed successfully. The site is back online and functioning correctly.',
      timestamp: '2:37 AM',
    },
    {
      agent: 'ceo' as const,
      content: 'Great job, Alex. Please implement your recommendations for preventing similar issues and schedule a brief review with the team tomorrow.',
      timestamp: '2:38 AM',
    },
  ], []);

  // Scroll to bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Progress through conversation steps when active
  useEffect(() => {
    if (!isActive || !errorTriggered || currentStep >= conversationSteps.length || isComplete) {
      return;
    }

    const timer = setTimeout(() => {
      setMessages(prev => [...prev, conversationSteps[currentStep]]);

      if (currentStep === conversationSteps.length - 1) {
        setIsComplete(true);
      } else {
        setCurrentStep(prev => prev + 1);
      }
    }, Math.random() * 1000 + 1000); // Random delay between 1-2 seconds

    return () => clearTimeout(timer);
  }, [isActive, errorTriggered, currentStep, isComplete, conversationSteps]);

  // Reset demo when triggered
  useEffect(() => {
    if (errorTriggered) {
      setMessages([]);
      setCurrentStep(0);
      setIsComplete(false);
    }
  }, [errorTriggered]);

  return (
    <Card className="h-[500px] flex flex-col">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Agent Communication</CardTitle>
        <CardDescription>
          {!errorTriggered
            ? 'Trigger an error to see agents in action'
            : isComplete
              ? 'Issue resolved successfully'
              : 'Agents are working on resolving the issue...'}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 overflow-y-auto">
        {messages.length === 0 && errorTriggered && (
          <div className="h-full flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        )}

        {messages.map((msg, index) => (
          <AgentMessage
            key={index}
            agent={msg.agent}
            content={msg.content}
            timestamp={msg.timestamp}
            isCode={msg.isCode}
          />
        ))}
        <div ref={messagesEndRef} />
      </CardContent>
    </Card>
  );
}
