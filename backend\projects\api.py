"""
Project management API for Siden AI.
Handles project creation, updates, and management.
"""

import uuid
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from services.supabase import DBConnection
from services.accounts import AccountService
from utils.logger import logger
from utils.auth_utils import get_current_user_id_from_jwt as get_current_user_id

router = APIRouter()


class ProjectCreate(BaseModel):
    name: str
    description: Optional[str] = None
    account_id: Optional[str] = None  # If not provided, uses user's personal account
    metadata: Optional[Dict[str, Any]] = None
    selectedAgents: Optional[List[str]] = ["Kenard"]  # Selected agent IDs
    agent_names: Optional[List[str]] = ["Kenard"]  # Default to CEO agent (legacy)
    companyInfo: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None
    knowledgeAccess: Optional[bool] = False
    memoryPersistence: Optional[bool] = False
    autonomousMode: Optional[bool] = False
    sandboxMode: Optional[bool] = True
    integrations: Optional[List[str]] = []


class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    selectedAgents: Optional[List[str]] = None
    agent_names: Optional[List[str]] = None
    companyInfo: Optional[str] = None
    settings: Optional[Dict[str, Any]] = None


async def get_user_account_id(user_id: str, requested_account_id: Optional[str] = None) -> str:
    """Get the account ID to use for a project. Creates personal account if needed."""
    account_service = AccountService()
    
    # If specific account requested, verify user has access
    if requested_account_id:
        account = await account_service.get_account_by_id(requested_account_id, user_id)
        if not account:
            raise HTTPException(status_code=403, detail="Access denied to requested account")
        return requested_account_id
    
    # Get user's personal account
    accounts = await account_service.get_user_accounts(user_id)
    personal_accounts = [acc for acc in accounts if acc.get("type") == "personal"]
    
    if personal_accounts:
        return personal_accounts[0]["id"]
    
    # Create personal account if none exists
    try:
        account = await account_service.create_personal_account(user_id, "Personal Account")
        return account["id"]
    except ValueError:
        # Account already exists, try to get it again
        accounts = await account_service.get_user_accounts(user_id)
        personal_accounts = [acc for acc in accounts if acc.get("type") == "personal"]
        if personal_accounts:
            return personal_accounts[0]["id"]
        raise HTTPException(status_code=500, detail="Failed to get or create personal account")


@router.post("/")
@router.post("")
async def create_project(
    project_data: ProjectCreate,
    user_id: str = Depends(get_current_user_id)
):
    """Create a new project."""
    try:
        logger.info(f"Creating project '{project_data.name}' for user {user_id}")
        
        # Get the account ID to use
        account_id = await get_user_account_id(user_id, project_data.account_id)
        
        # Generate project ID
        project_id = str(uuid.uuid4())
        
        # Create sandbox settings including all project metadata
        sandbox_settings = {
            "knowledgeAccess": project_data.knowledgeAccess,
            "memoryPersistence": project_data.memoryPersistence,
            "autonomousMode": project_data.autonomousMode,
            "sandboxMode": project_data.sandboxMode,
            "integrations": project_data.integrations,
            # Store agent selection and other metadata in sandbox
            "selectedAgents": project_data.selectedAgents or ["Kenard"],
            "agent_names": project_data.agent_names or ["Kenard"],
            "companyInfo": project_data.companyInfo,
            "settings": project_data.settings or {},
            "metadata": project_data.metadata or {}
        }
        
        # Create project data matching database schema
        project = {
            "project_id": project_id,
            "name": project_data.name,
            "description": project_data.description,
            "account_id": account_id,
            "sandbox": sandbox_settings,
            "is_public": False
        }
        
        # Insert into database
        db = DBConnection()
        client = await db.client
        
        result = await client.from_("projects").insert(project).execute()
        
        if not result.data:
            raise HTTPException(status_code=500, detail="Failed to create project")
        
        created_project = result.data[0]
        logger.info(f"Successfully created project {project_id}")
        
        # Add id field and extract agent data from sandbox for frontend compatibility
        created_project["id"] = created_project["project_id"]
        if created_project.get("sandbox"):
            sandbox = created_project["sandbox"]
            created_project["selectedAgents"] = sandbox.get("selectedAgents", ["Kenard"])
            created_project["agent_names"] = sandbox.get("agent_names", ["Kenard"])
            created_project["companyInfo"] = sandbox.get("companyInfo")
            created_project["settings"] = sandbox.get("settings", {})
            created_project["metadata"] = sandbox.get("metadata", {})
        
        # Initialize agents if specified
        if project_data.agent_names:
            try:
                # TODO: Initialize project agents
                logger.info(f"Would initialize agents: {project_data.agent_names}")
            except Exception as agent_error:
                logger.warning(f"Error initializing agents: {str(agent_error)}")
        
        return {"project": created_project}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating project: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/")
@router.get("")
async def get_projects(user_id: str = Depends(get_current_user_id)):
    """Get all projects the user has access to."""
    try:
        db = DBConnection()
        client = await db.client
        
        # Get user's accounts
        account_service = AccountService()
        accounts = await account_service.get_user_accounts(user_id)
        account_ids = [acc["id"] for acc in accounts]
        
        if not account_ids:
            return {"projects": []}
        
        # Get projects from all accessible accounts
        result = await client.from_("projects").select("*").in_("account_id", account_ids).execute()
        
        # Add id field and extract agent data from sandbox for frontend compatibility
        projects = result.data or []
        for project in projects:
            if "project_id" in project and "id" not in project:
                project["id"] = project["project_id"]
            # Extract agent data from sandbox for frontend compatibility
            if project.get("sandbox"):
                sandbox = project["sandbox"]
                project["selectedAgents"] = sandbox.get("selectedAgents", ["Kenard"])
                project["agent_names"] = sandbox.get("agent_names", ["Kenard"])
                project["companyInfo"] = sandbox.get("companyInfo")
                project["settings"] = sandbox.get("settings", {})
                project["metadata"] = sandbox.get("metadata", {})
        
        return {"projects": projects}
        
    except Exception as e:
        logger.error(f"Error getting projects: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{project_id}")
async def get_project(project_id: str, user_id: str = Depends(get_current_user_id)):
    """Get a specific project."""
    try:
        db = DBConnection()
        client = await db.client
        
        # Get user's accounts
        account_service = AccountService()
        accounts = await account_service.get_user_accounts(user_id)
        account_ids = [acc["id"] for acc in accounts]
        
        if not account_ids:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Get project if user has access
        result = await client.from_("projects").select("*").eq("project_id", project_id).in_("account_id", account_ids).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Project not found")
        
        project = result.data[0]
        # Add id field and extract agent data from sandbox for frontend compatibility
        if "project_id" in project and "id" not in project:
            project["id"] = project["project_id"]
        # Extract agent data from sandbox for frontend compatibility
        if project.get("sandbox"):
            sandbox = project["sandbox"]
            project["selectedAgents"] = sandbox.get("selectedAgents", ["Kenard"])
            project["agent_names"] = sandbox.get("agent_names", ["Kenard"])
            project["companyInfo"] = sandbox.get("companyInfo")
            project["settings"] = sandbox.get("settings", {})
            project["metadata"] = sandbox.get("metadata", {})
        
        return {"project": project}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting project: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{project_id}")
async def update_project(
    project_id: str,
    project_data: ProjectUpdate,
    user_id: str = Depends(get_current_user_id)
):
    """Update a project."""
    try:
        db = DBConnection()
        client = await db.client
        
        # Get user's accounts with admin/owner access
        account_service = AccountService()
        accounts = await account_service.get_user_accounts(user_id)
        admin_account_ids = [acc["id"] for acc in accounts if acc.get("user_role") in ["owner", "admin"]]
        
        if not admin_account_ids:
            raise HTTPException(status_code=403, detail="No admin access to any accounts")
        
        # Check if project exists and user has admin access
        existing = await client.from_("projects").select("*").eq("project_id", project_id).in_("account_id", admin_account_ids).execute()
        
        if not existing.data:
            raise HTTPException(status_code=404, detail="Project not found or access denied")
        
        # Prepare update data
        update_data = {
            "updated_at": datetime.now(timezone.utc).isoformat()
        }
        
        if project_data.name is not None:
            update_data["name"] = project_data.name
        if project_data.description is not None:
            update_data["description"] = project_data.description
        
        # Get existing project data to update sandbox
        existing_project = existing.data[0]
        sandbox = existing_project.get("sandbox", {}) or {}
        
        # Update sandbox with new agent selections and settings
        if project_data.selectedAgents is not None:
            sandbox["selectedAgents"] = project_data.selectedAgents
        if project_data.agent_names is not None:
            sandbox["agent_names"] = project_data.agent_names
        if project_data.companyInfo is not None:
            sandbox["companyInfo"] = project_data.companyInfo
        if project_data.settings is not None:
            sandbox["settings"] = project_data.settings
        if project_data.metadata is not None:
            sandbox["metadata"] = project_data.metadata
            
        # Update the sandbox field
        update_data["sandbox"] = sandbox
        
        # Update project
        result = await client.from_("projects").update(update_data).eq("project_id", project_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=500, detail="Failed to update project")
        
        return {"project": result.data[0]}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating project: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{project_id}")
async def delete_project(project_id: str, user_id: str = Depends(get_current_user_id)):
    """Delete a project."""
    try:
        db = DBConnection()
        client = await db.client
        
        # Get user's accounts with owner access
        account_service = AccountService()
        accounts = await account_service.get_user_accounts(user_id)
        owner_account_ids = [acc["id"] for acc in accounts if acc.get("user_role") == "owner"]
        
        if not owner_account_ids:
            raise HTTPException(status_code=403, detail="No owner access to any accounts")
        
        # Check if project exists and user has owner access
        existing = await client.from_("projects").select("*").eq("project_id", project_id).in_("account_id", owner_account_ids).execute()
        
        if not existing.data:
            raise HTTPException(status_code=404, detail="Project not found or access denied")
        
        # Delete project
        result = await client.from_("projects").delete().eq("project_id", project_id).execute()
        
        return {"message": "Project deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting project: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/{project_id}/threads")
async def create_thread(project_id: str, thread_data: dict, user_id: str = Depends(get_current_user_id)):
    """Create a new thread for a project."""
    try:
        db = DBConnection()
        client = await db.client
        
        # Verify user has access to the project
        account_service = AccountService()
        accounts = await account_service.get_user_accounts(user_id)
        account_ids = [acc["id"] for acc in accounts]
        
        if not account_ids:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Check if project exists and user has access
        project_result = await client.from_("projects").select("*").eq("project_id", project_id).in_("account_id", account_ids).execute()
        
        if not project_result.data:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Generate thread ID
        thread_id = str(uuid.uuid4())
        
        # Get account_id from project
        project = project_result.data[0]
        account_id = project["account_id"]
        
        # Note: Thread initialization is handled in the main API
        
        # Create thread data
        thread = {
            "thread_id": thread_id,
            "account_id": account_id,
            "project_id": project_id,
            "is_public": False
        }
        
        # Insert thread into database
        result = await client.from_("threads").insert(thread).execute()
        
        if not result.data:
            raise HTTPException(status_code=500, detail="Failed to create thread")
        
        created_thread = result.data[0]
        logger.info(f"Successfully created thread {thread_id} for project {project_id}")
        
        # Add id field for frontend compatibility
        created_thread["id"] = created_thread["thread_id"]
        
        return {"thread": created_thread}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating thread: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")
