import React from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface IconButtonProps {
  icon: React.ReactNode;
  onClick?: () => void;
  className?: string;
  type?: "button" | "submit" | "reset";
  variant?: "default" | "ghost" | "outline";
  disabled?: boolean;
  ariaLabel?: string;
}

export function IconButton({
  icon,
  onClick,
  className,
  type = "button",
  variant = "ghost",
  disabled = false,
  ariaLabel,
}: IconButtonProps) {
  return (
    <Button
      type={type}
      size="icon"
      variant={variant}
      onClick={onClick}
      disabled={disabled}
      aria-label={ariaLabel}
      className={cn(
        "h-10 w-10 flex items-center justify-center text-[#999999] hover:text-white hover:bg-[#2a2a2a]",
        className
      )}
    >
      {icon}
    </Button>
  );
}
