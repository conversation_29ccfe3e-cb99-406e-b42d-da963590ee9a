"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+commands@6.8.1";
exports.ids = ["vendor-chunks/@codemirror+commands@6.8.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+commands@6.8.1/node_modules/@codemirror/commands/dist/index.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+commands@6.8.1/node_modules/@codemirror/commands/dist/index.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blockComment: () => (/* binding */ blockComment),\n/* harmony export */   blockUncomment: () => (/* binding */ blockUncomment),\n/* harmony export */   copyLineDown: () => (/* binding */ copyLineDown),\n/* harmony export */   copyLineUp: () => (/* binding */ copyLineUp),\n/* harmony export */   cursorCharBackward: () => (/* binding */ cursorCharBackward),\n/* harmony export */   cursorCharBackwardLogical: () => (/* binding */ cursorCharBackwardLogical),\n/* harmony export */   cursorCharForward: () => (/* binding */ cursorCharForward),\n/* harmony export */   cursorCharForwardLogical: () => (/* binding */ cursorCharForwardLogical),\n/* harmony export */   cursorCharLeft: () => (/* binding */ cursorCharLeft),\n/* harmony export */   cursorCharRight: () => (/* binding */ cursorCharRight),\n/* harmony export */   cursorDocEnd: () => (/* binding */ cursorDocEnd),\n/* harmony export */   cursorDocStart: () => (/* binding */ cursorDocStart),\n/* harmony export */   cursorGroupBackward: () => (/* binding */ cursorGroupBackward),\n/* harmony export */   cursorGroupForward: () => (/* binding */ cursorGroupForward),\n/* harmony export */   cursorGroupForwardWin: () => (/* binding */ cursorGroupForwardWin),\n/* harmony export */   cursorGroupLeft: () => (/* binding */ cursorGroupLeft),\n/* harmony export */   cursorGroupRight: () => (/* binding */ cursorGroupRight),\n/* harmony export */   cursorLineBoundaryBackward: () => (/* binding */ cursorLineBoundaryBackward),\n/* harmony export */   cursorLineBoundaryForward: () => (/* binding */ cursorLineBoundaryForward),\n/* harmony export */   cursorLineBoundaryLeft: () => (/* binding */ cursorLineBoundaryLeft),\n/* harmony export */   cursorLineBoundaryRight: () => (/* binding */ cursorLineBoundaryRight),\n/* harmony export */   cursorLineDown: () => (/* binding */ cursorLineDown),\n/* harmony export */   cursorLineEnd: () => (/* binding */ cursorLineEnd),\n/* harmony export */   cursorLineStart: () => (/* binding */ cursorLineStart),\n/* harmony export */   cursorLineUp: () => (/* binding */ cursorLineUp),\n/* harmony export */   cursorMatchingBracket: () => (/* binding */ cursorMatchingBracket),\n/* harmony export */   cursorPageDown: () => (/* binding */ cursorPageDown),\n/* harmony export */   cursorPageUp: () => (/* binding */ cursorPageUp),\n/* harmony export */   cursorSubwordBackward: () => (/* binding */ cursorSubwordBackward),\n/* harmony export */   cursorSubwordForward: () => (/* binding */ cursorSubwordForward),\n/* harmony export */   cursorSyntaxLeft: () => (/* binding */ cursorSyntaxLeft),\n/* harmony export */   cursorSyntaxRight: () => (/* binding */ cursorSyntaxRight),\n/* harmony export */   defaultKeymap: () => (/* binding */ defaultKeymap),\n/* harmony export */   deleteCharBackward: () => (/* binding */ deleteCharBackward),\n/* harmony export */   deleteCharBackwardStrict: () => (/* binding */ deleteCharBackwardStrict),\n/* harmony export */   deleteCharForward: () => (/* binding */ deleteCharForward),\n/* harmony export */   deleteGroupBackward: () => (/* binding */ deleteGroupBackward),\n/* harmony export */   deleteGroupForward: () => (/* binding */ deleteGroupForward),\n/* harmony export */   deleteLine: () => (/* binding */ deleteLine),\n/* harmony export */   deleteLineBoundaryBackward: () => (/* binding */ deleteLineBoundaryBackward),\n/* harmony export */   deleteLineBoundaryForward: () => (/* binding */ deleteLineBoundaryForward),\n/* harmony export */   deleteToLineEnd: () => (/* binding */ deleteToLineEnd),\n/* harmony export */   deleteToLineStart: () => (/* binding */ deleteToLineStart),\n/* harmony export */   deleteTrailingWhitespace: () => (/* binding */ deleteTrailingWhitespace),\n/* harmony export */   emacsStyleKeymap: () => (/* binding */ emacsStyleKeymap),\n/* harmony export */   history: () => (/* binding */ history),\n/* harmony export */   historyField: () => (/* binding */ historyField),\n/* harmony export */   historyKeymap: () => (/* binding */ historyKeymap),\n/* harmony export */   indentLess: () => (/* binding */ indentLess),\n/* harmony export */   indentMore: () => (/* binding */ indentMore),\n/* harmony export */   indentSelection: () => (/* binding */ indentSelection),\n/* harmony export */   indentWithTab: () => (/* binding */ indentWithTab),\n/* harmony export */   insertBlankLine: () => (/* binding */ insertBlankLine),\n/* harmony export */   insertNewline: () => (/* binding */ insertNewline),\n/* harmony export */   insertNewlineAndIndent: () => (/* binding */ insertNewlineAndIndent),\n/* harmony export */   insertNewlineKeepIndent: () => (/* binding */ insertNewlineKeepIndent),\n/* harmony export */   insertTab: () => (/* binding */ insertTab),\n/* harmony export */   invertedEffects: () => (/* binding */ invertedEffects),\n/* harmony export */   isolateHistory: () => (/* binding */ isolateHistory),\n/* harmony export */   lineComment: () => (/* binding */ lineComment),\n/* harmony export */   lineUncomment: () => (/* binding */ lineUncomment),\n/* harmony export */   moveLineDown: () => (/* binding */ moveLineDown),\n/* harmony export */   moveLineUp: () => (/* binding */ moveLineUp),\n/* harmony export */   redo: () => (/* binding */ redo),\n/* harmony export */   redoDepth: () => (/* binding */ redoDepth),\n/* harmony export */   redoSelection: () => (/* binding */ redoSelection),\n/* harmony export */   selectAll: () => (/* binding */ selectAll),\n/* harmony export */   selectCharBackward: () => (/* binding */ selectCharBackward),\n/* harmony export */   selectCharBackwardLogical: () => (/* binding */ selectCharBackwardLogical),\n/* harmony export */   selectCharForward: () => (/* binding */ selectCharForward),\n/* harmony export */   selectCharForwardLogical: () => (/* binding */ selectCharForwardLogical),\n/* harmony export */   selectCharLeft: () => (/* binding */ selectCharLeft),\n/* harmony export */   selectCharRight: () => (/* binding */ selectCharRight),\n/* harmony export */   selectDocEnd: () => (/* binding */ selectDocEnd),\n/* harmony export */   selectDocStart: () => (/* binding */ selectDocStart),\n/* harmony export */   selectGroupBackward: () => (/* binding */ selectGroupBackward),\n/* harmony export */   selectGroupForward: () => (/* binding */ selectGroupForward),\n/* harmony export */   selectGroupForwardWin: () => (/* binding */ selectGroupForwardWin),\n/* harmony export */   selectGroupLeft: () => (/* binding */ selectGroupLeft),\n/* harmony export */   selectGroupRight: () => (/* binding */ selectGroupRight),\n/* harmony export */   selectLine: () => (/* binding */ selectLine),\n/* harmony export */   selectLineBoundaryBackward: () => (/* binding */ selectLineBoundaryBackward),\n/* harmony export */   selectLineBoundaryForward: () => (/* binding */ selectLineBoundaryForward),\n/* harmony export */   selectLineBoundaryLeft: () => (/* binding */ selectLineBoundaryLeft),\n/* harmony export */   selectLineBoundaryRight: () => (/* binding */ selectLineBoundaryRight),\n/* harmony export */   selectLineDown: () => (/* binding */ selectLineDown),\n/* harmony export */   selectLineEnd: () => (/* binding */ selectLineEnd),\n/* harmony export */   selectLineStart: () => (/* binding */ selectLineStart),\n/* harmony export */   selectLineUp: () => (/* binding */ selectLineUp),\n/* harmony export */   selectMatchingBracket: () => (/* binding */ selectMatchingBracket),\n/* harmony export */   selectPageDown: () => (/* binding */ selectPageDown),\n/* harmony export */   selectPageUp: () => (/* binding */ selectPageUp),\n/* harmony export */   selectParentSyntax: () => (/* binding */ selectParentSyntax),\n/* harmony export */   selectSubwordBackward: () => (/* binding */ selectSubwordBackward),\n/* harmony export */   selectSubwordForward: () => (/* binding */ selectSubwordForward),\n/* harmony export */   selectSyntaxLeft: () => (/* binding */ selectSyntaxLeft),\n/* harmony export */   selectSyntaxRight: () => (/* binding */ selectSyntaxRight),\n/* harmony export */   simplifySelection: () => (/* binding */ simplifySelection),\n/* harmony export */   splitLine: () => (/* binding */ splitLine),\n/* harmony export */   standardKeymap: () => (/* binding */ standardKeymap),\n/* harmony export */   temporarilySetTabFocusMode: () => (/* binding */ temporarilySetTabFocusMode),\n/* harmony export */   toggleBlockComment: () => (/* binding */ toggleBlockComment),\n/* harmony export */   toggleBlockCommentByLine: () => (/* binding */ toggleBlockCommentByLine),\n/* harmony export */   toggleComment: () => (/* binding */ toggleComment),\n/* harmony export */   toggleLineComment: () => (/* binding */ toggleLineComment),\n/* harmony export */   toggleTabFocusMode: () => (/* binding */ toggleTabFocusMode),\n/* harmony export */   transposeChars: () => (/* binding */ transposeChars),\n/* harmony export */   undo: () => (/* binding */ undo),\n/* harmony export */   undoDepth: () => (/* binding */ undoDepth),\n/* harmony export */   undoSelection: () => (/* binding */ undoSelection)\n/* harmony export */ });\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n\n\n\n\n\n/**\nComment or uncomment the current selection. Will use line comments\nif available, otherwise falling back to block comments.\n*/\nconst toggleComment = target => {\n    let { state } = target, line = state.doc.lineAt(state.selection.main.from), config = getConfig(target.state, line.from);\n    return config.line ? toggleLineComment(target) : config.block ? toggleBlockCommentByLine(target) : false;\n};\nfunction command(f, option) {\n    return ({ state, dispatch }) => {\n        if (state.readOnly)\n            return false;\n        let tr = f(option, state);\n        if (!tr)\n            return false;\n        dispatch(state.update(tr));\n        return true;\n    };\n}\n/**\nComment or uncomment the current selection using line comments.\nThe line comment syntax is taken from the\n[`commentTokens`](https://codemirror.net/6/docs/ref/#commands.CommentTokens) [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt).\n*/\nconst toggleLineComment = /*@__PURE__*/command(changeLineComment, 0 /* CommentOption.Toggle */);\n/**\nComment the current selection using line comments.\n*/\nconst lineComment = /*@__PURE__*/command(changeLineComment, 1 /* CommentOption.Comment */);\n/**\nUncomment the current selection using line comments.\n*/\nconst lineUncomment = /*@__PURE__*/command(changeLineComment, 2 /* CommentOption.Uncomment */);\n/**\nComment or uncomment the current selection using block comments.\nThe block comment syntax is taken from the\n[`commentTokens`](https://codemirror.net/6/docs/ref/#commands.CommentTokens) [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt).\n*/\nconst toggleBlockComment = /*@__PURE__*/command(changeBlockComment, 0 /* CommentOption.Toggle */);\n/**\nComment the current selection using block comments.\n*/\nconst blockComment = /*@__PURE__*/command(changeBlockComment, 1 /* CommentOption.Comment */);\n/**\nUncomment the current selection using block comments.\n*/\nconst blockUncomment = /*@__PURE__*/command(changeBlockComment, 2 /* CommentOption.Uncomment */);\n/**\nComment or uncomment the lines around the current selection using\nblock comments.\n*/\nconst toggleBlockCommentByLine = /*@__PURE__*/command((o, s) => changeBlockComment(o, s, selectedLineRanges(s)), 0 /* CommentOption.Toggle */);\nfunction getConfig(state, pos) {\n    let data = state.languageDataAt(\"commentTokens\", pos, 1);\n    return data.length ? data[0] : {};\n}\nconst SearchMargin = 50;\n/**\nDetermines if the given range is block-commented in the given\nstate.\n*/\nfunction findBlockComment(state, { open, close }, from, to) {\n    let textBefore = state.sliceDoc(from - SearchMargin, from);\n    let textAfter = state.sliceDoc(to, to + SearchMargin);\n    let spaceBefore = /\\s*$/.exec(textBefore)[0].length, spaceAfter = /^\\s*/.exec(textAfter)[0].length;\n    let beforeOff = textBefore.length - spaceBefore;\n    if (textBefore.slice(beforeOff - open.length, beforeOff) == open &&\n        textAfter.slice(spaceAfter, spaceAfter + close.length) == close) {\n        return { open: { pos: from - spaceBefore, margin: spaceBefore && 1 },\n            close: { pos: to + spaceAfter, margin: spaceAfter && 1 } };\n    }\n    let startText, endText;\n    if (to - from <= 2 * SearchMargin) {\n        startText = endText = state.sliceDoc(from, to);\n    }\n    else {\n        startText = state.sliceDoc(from, from + SearchMargin);\n        endText = state.sliceDoc(to - SearchMargin, to);\n    }\n    let startSpace = /^\\s*/.exec(startText)[0].length, endSpace = /\\s*$/.exec(endText)[0].length;\n    let endOff = endText.length - endSpace - close.length;\n    if (startText.slice(startSpace, startSpace + open.length) == open &&\n        endText.slice(endOff, endOff + close.length) == close) {\n        return { open: { pos: from + startSpace + open.length,\n                margin: /\\s/.test(startText.charAt(startSpace + open.length)) ? 1 : 0 },\n            close: { pos: to - endSpace - close.length,\n                margin: /\\s/.test(endText.charAt(endOff - 1)) ? 1 : 0 } };\n    }\n    return null;\n}\nfunction selectedLineRanges(state) {\n    let ranges = [];\n    for (let r of state.selection.ranges) {\n        let fromLine = state.doc.lineAt(r.from);\n        let toLine = r.to <= fromLine.to ? fromLine : state.doc.lineAt(r.to);\n        if (toLine.from > fromLine.from && toLine.from == r.to)\n            toLine = r.to == fromLine.to + 1 ? fromLine : state.doc.lineAt(r.to - 1);\n        let last = ranges.length - 1;\n        if (last >= 0 && ranges[last].to > fromLine.from)\n            ranges[last].to = toLine.to;\n        else\n            ranges.push({ from: fromLine.from + /^\\s*/.exec(fromLine.text)[0].length, to: toLine.to });\n    }\n    return ranges;\n}\n// Performs toggle, comment and uncomment of block comments in\n// languages that support them.\nfunction changeBlockComment(option, state, ranges = state.selection.ranges) {\n    let tokens = ranges.map(r => getConfig(state, r.from).block);\n    if (!tokens.every(c => c))\n        return null;\n    let comments = ranges.map((r, i) => findBlockComment(state, tokens[i], r.from, r.to));\n    if (option != 2 /* CommentOption.Uncomment */ && !comments.every(c => c)) {\n        return { changes: state.changes(ranges.map((range, i) => {\n                if (comments[i])\n                    return [];\n                return [{ from: range.from, insert: tokens[i].open + \" \" }, { from: range.to, insert: \" \" + tokens[i].close }];\n            })) };\n    }\n    else if (option != 1 /* CommentOption.Comment */ && comments.some(c => c)) {\n        let changes = [];\n        for (let i = 0, comment; i < comments.length; i++)\n            if (comment = comments[i]) {\n                let token = tokens[i], { open, close } = comment;\n                changes.push({ from: open.pos - token.open.length, to: open.pos + open.margin }, { from: close.pos - close.margin, to: close.pos + token.close.length });\n            }\n        return { changes };\n    }\n    return null;\n}\n// Performs toggle, comment and uncomment of line comments.\nfunction changeLineComment(option, state, ranges = state.selection.ranges) {\n    let lines = [];\n    let prevLine = -1;\n    for (let { from, to } of ranges) {\n        let startI = lines.length, minIndent = 1e9;\n        let token = getConfig(state, from).line;\n        if (!token)\n            continue;\n        for (let pos = from; pos <= to;) {\n            let line = state.doc.lineAt(pos);\n            if (line.from > prevLine && (from == to || to > line.from)) {\n                prevLine = line.from;\n                let indent = /^\\s*/.exec(line.text)[0].length;\n                let empty = indent == line.length;\n                let comment = line.text.slice(indent, indent + token.length) == token ? indent : -1;\n                if (indent < line.text.length && indent < minIndent)\n                    minIndent = indent;\n                lines.push({ line, comment, token, indent, empty, single: false });\n            }\n            pos = line.to + 1;\n        }\n        if (minIndent < 1e9)\n            for (let i = startI; i < lines.length; i++)\n                if (lines[i].indent < lines[i].line.text.length)\n                    lines[i].indent = minIndent;\n        if (lines.length == startI + 1)\n            lines[startI].single = true;\n    }\n    if (option != 2 /* CommentOption.Uncomment */ && lines.some(l => l.comment < 0 && (!l.empty || l.single))) {\n        let changes = [];\n        for (let { line, token, indent, empty, single } of lines)\n            if (single || !empty)\n                changes.push({ from: line.from + indent, insert: token + \" \" });\n        let changeSet = state.changes(changes);\n        return { changes: changeSet, selection: state.selection.map(changeSet, 1) };\n    }\n    else if (option != 1 /* CommentOption.Comment */ && lines.some(l => l.comment >= 0)) {\n        let changes = [];\n        for (let { line, comment, token } of lines)\n            if (comment >= 0) {\n                let from = line.from + comment, to = from + token.length;\n                if (line.text[to - line.from] == \" \")\n                    to++;\n                changes.push({ from, to });\n            }\n        return { changes };\n    }\n    return null;\n}\n\nconst fromHistory = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Annotation.define();\n/**\nTransaction annotation that will prevent that transaction from\nbeing combined with other transactions in the undo history. Given\n`\"before\"`, it'll prevent merging with previous transactions. With\n`\"after\"`, subsequent transactions won't be combined with this\none. With `\"full\"`, the transaction is isolated on both sides.\n*/\nconst isolateHistory = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Annotation.define();\n/**\nThis facet provides a way to register functions that, given a\ntransaction, provide a set of effects that the history should\nstore when inverting the transaction. This can be used to\nintegrate some kinds of effects in the history, so that they can\nbe undone (and redone again).\n*/\nconst invertedEffects = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Facet.define();\nconst historyConfig = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Facet.define({\n    combine(configs) {\n        return (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.combineConfig)(configs, {\n            minDepth: 100,\n            newGroupDelay: 500,\n            joinToEvent: (_t, isAdjacent) => isAdjacent,\n        }, {\n            minDepth: Math.max,\n            newGroupDelay: Math.min,\n            joinToEvent: (a, b) => (tr, adj) => a(tr, adj) || b(tr, adj)\n        });\n    }\n});\nconst historyField_ = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateField.define({\n    create() {\n        return HistoryState.empty;\n    },\n    update(state, tr) {\n        let config = tr.state.facet(historyConfig);\n        let fromHist = tr.annotation(fromHistory);\n        if (fromHist) {\n            let item = HistEvent.fromTransaction(tr, fromHist.selection), from = fromHist.side;\n            let other = from == 0 /* BranchName.Done */ ? state.undone : state.done;\n            if (item)\n                other = updateBranch(other, other.length, config.minDepth, item);\n            else\n                other = addSelection(other, tr.startState.selection);\n            return new HistoryState(from == 0 /* BranchName.Done */ ? fromHist.rest : other, from == 0 /* BranchName.Done */ ? other : fromHist.rest);\n        }\n        let isolate = tr.annotation(isolateHistory);\n        if (isolate == \"full\" || isolate == \"before\")\n            state = state.isolate();\n        if (tr.annotation(_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Transaction.addToHistory) === false)\n            return !tr.changes.empty ? state.addMapping(tr.changes.desc) : state;\n        let event = HistEvent.fromTransaction(tr);\n        let time = tr.annotation(_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Transaction.time), userEvent = tr.annotation(_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Transaction.userEvent);\n        if (event)\n            state = state.addChanges(event, time, userEvent, config, tr);\n        else if (tr.selection)\n            state = state.addSelection(tr.startState.selection, time, userEvent, config.newGroupDelay);\n        if (isolate == \"full\" || isolate == \"after\")\n            state = state.isolate();\n        return state;\n    },\n    toJSON(value) {\n        return { done: value.done.map(e => e.toJSON()), undone: value.undone.map(e => e.toJSON()) };\n    },\n    fromJSON(json) {\n        return new HistoryState(json.done.map(HistEvent.fromJSON), json.undone.map(HistEvent.fromJSON));\n    }\n});\n/**\nCreate a history extension with the given configuration.\n*/\nfunction history(config = {}) {\n    return [\n        historyField_,\n        historyConfig.of(config),\n        _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.domEventHandlers({\n            beforeinput(e, view) {\n                let command = e.inputType == \"historyUndo\" ? undo : e.inputType == \"historyRedo\" ? redo : null;\n                if (!command)\n                    return false;\n                e.preventDefault();\n                return command(view);\n            }\n        })\n    ];\n}\n/**\nThe state field used to store the history data. Should probably\nonly be used when you want to\n[serialize](https://codemirror.net/6/docs/ref/#state.EditorState.toJSON) or\n[deserialize](https://codemirror.net/6/docs/ref/#state.EditorState^fromJSON) state objects in a way\nthat preserves history.\n*/\nconst historyField = historyField_;\nfunction cmd(side, selection) {\n    return function ({ state, dispatch }) {\n        if (!selection && state.readOnly)\n            return false;\n        let historyState = state.field(historyField_, false);\n        if (!historyState)\n            return false;\n        let tr = historyState.pop(side, state, selection);\n        if (!tr)\n            return false;\n        dispatch(tr);\n        return true;\n    };\n}\n/**\nUndo a single group of history events. Returns false if no group\nwas available.\n*/\nconst undo = /*@__PURE__*/cmd(0 /* BranchName.Done */, false);\n/**\nRedo a group of history events. Returns false if no group was\navailable.\n*/\nconst redo = /*@__PURE__*/cmd(1 /* BranchName.Undone */, false);\n/**\nUndo a change or selection change.\n*/\nconst undoSelection = /*@__PURE__*/cmd(0 /* BranchName.Done */, true);\n/**\nRedo a change or selection change.\n*/\nconst redoSelection = /*@__PURE__*/cmd(1 /* BranchName.Undone */, true);\nfunction depth(side) {\n    return function (state) {\n        let histState = state.field(historyField_, false);\n        if (!histState)\n            return 0;\n        let branch = side == 0 /* BranchName.Done */ ? histState.done : histState.undone;\n        return branch.length - (branch.length && !branch[0].changes ? 1 : 0);\n    };\n}\n/**\nThe amount of undoable change events available in a given state.\n*/\nconst undoDepth = /*@__PURE__*/depth(0 /* BranchName.Done */);\n/**\nThe amount of redoable change events available in a given state.\n*/\nconst redoDepth = /*@__PURE__*/depth(1 /* BranchName.Undone */);\n// History events store groups of changes or effects that need to be\n// undone/redone together.\nclass HistEvent {\n    constructor(\n    // The changes in this event. Normal events hold at least one\n    // change or effect. But it may be necessary to store selection\n    // events before the first change, in which case a special type of\n    // instance is created which doesn't hold any changes, with\n    // changes == startSelection == undefined\n    changes, \n    // The effects associated with this event\n    effects, \n    // Accumulated mapping (from addToHistory==false) that should be\n    // applied to events below this one.\n    mapped, \n    // The selection before this event\n    startSelection, \n    // Stores selection changes after this event, to be used for\n    // selection undo/redo.\n    selectionsAfter) {\n        this.changes = changes;\n        this.effects = effects;\n        this.mapped = mapped;\n        this.startSelection = startSelection;\n        this.selectionsAfter = selectionsAfter;\n    }\n    setSelAfter(after) {\n        return new HistEvent(this.changes, this.effects, this.mapped, this.startSelection, after);\n    }\n    toJSON() {\n        var _a, _b, _c;\n        return {\n            changes: (_a = this.changes) === null || _a === void 0 ? void 0 : _a.toJSON(),\n            mapped: (_b = this.mapped) === null || _b === void 0 ? void 0 : _b.toJSON(),\n            startSelection: (_c = this.startSelection) === null || _c === void 0 ? void 0 : _c.toJSON(),\n            selectionsAfter: this.selectionsAfter.map(s => s.toJSON())\n        };\n    }\n    static fromJSON(json) {\n        return new HistEvent(json.changes && _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.ChangeSet.fromJSON(json.changes), [], json.mapped && _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.ChangeDesc.fromJSON(json.mapped), json.startSelection && _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.fromJSON(json.startSelection), json.selectionsAfter.map(_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.fromJSON));\n    }\n    // This does not check `addToHistory` and such, it assumes the\n    // transaction needs to be converted to an item. Returns null when\n    // there are no changes or effects in the transaction.\n    static fromTransaction(tr, selection) {\n        let effects = none;\n        for (let invert of tr.startState.facet(invertedEffects)) {\n            let result = invert(tr);\n            if (result.length)\n                effects = effects.concat(result);\n        }\n        if (!effects.length && tr.changes.empty)\n            return null;\n        return new HistEvent(tr.changes.invert(tr.startState.doc), effects, undefined, selection || tr.startState.selection, none);\n    }\n    static selection(selections) {\n        return new HistEvent(undefined, none, undefined, undefined, selections);\n    }\n}\nfunction updateBranch(branch, to, maxLen, newEvent) {\n    let start = to + 1 > maxLen + 20 ? to - maxLen - 1 : 0;\n    let newBranch = branch.slice(start, to);\n    newBranch.push(newEvent);\n    return newBranch;\n}\nfunction isAdjacent(a, b) {\n    let ranges = [], isAdjacent = false;\n    a.iterChangedRanges((f, t) => ranges.push(f, t));\n    b.iterChangedRanges((_f, _t, f, t) => {\n        for (let i = 0; i < ranges.length;) {\n            let from = ranges[i++], to = ranges[i++];\n            if (t >= from && f <= to)\n                isAdjacent = true;\n        }\n    });\n    return isAdjacent;\n}\nfunction eqSelectionShape(a, b) {\n    return a.ranges.length == b.ranges.length &&\n        a.ranges.filter((r, i) => r.empty != b.ranges[i].empty).length === 0;\n}\nfunction conc(a, b) {\n    return !a.length ? b : !b.length ? a : a.concat(b);\n}\nconst none = [];\nconst MaxSelectionsPerEvent = 200;\nfunction addSelection(branch, selection) {\n    if (!branch.length) {\n        return [HistEvent.selection([selection])];\n    }\n    else {\n        let lastEvent = branch[branch.length - 1];\n        let sels = lastEvent.selectionsAfter.slice(Math.max(0, lastEvent.selectionsAfter.length - MaxSelectionsPerEvent));\n        if (sels.length && sels[sels.length - 1].eq(selection))\n            return branch;\n        sels.push(selection);\n        return updateBranch(branch, branch.length - 1, 1e9, lastEvent.setSelAfter(sels));\n    }\n}\n// Assumes the top item has one or more selectionAfter values\nfunction popSelection(branch) {\n    let last = branch[branch.length - 1];\n    let newBranch = branch.slice();\n    newBranch[branch.length - 1] = last.setSelAfter(last.selectionsAfter.slice(0, last.selectionsAfter.length - 1));\n    return newBranch;\n}\n// Add a mapping to the top event in the given branch. If this maps\n// away all the changes and effects in that item, drop it and\n// propagate the mapping to the next item.\nfunction addMappingToBranch(branch, mapping) {\n    if (!branch.length)\n        return branch;\n    let length = branch.length, selections = none;\n    while (length) {\n        let event = mapEvent(branch[length - 1], mapping, selections);\n        if (event.changes && !event.changes.empty || event.effects.length) { // Event survived mapping\n            let result = branch.slice(0, length);\n            result[length - 1] = event;\n            return result;\n        }\n        else { // Drop this event, since there's no changes or effects left\n            mapping = event.mapped;\n            length--;\n            selections = event.selectionsAfter;\n        }\n    }\n    return selections.length ? [HistEvent.selection(selections)] : none;\n}\nfunction mapEvent(event, mapping, extraSelections) {\n    let selections = conc(event.selectionsAfter.length ? event.selectionsAfter.map(s => s.map(mapping)) : none, extraSelections);\n    // Change-less events don't store mappings (they are always the last event in a branch)\n    if (!event.changes)\n        return HistEvent.selection(selections);\n    let mappedChanges = event.changes.map(mapping), before = mapping.mapDesc(event.changes, true);\n    let fullMapping = event.mapped ? event.mapped.composeDesc(before) : before;\n    return new HistEvent(mappedChanges, _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.mapEffects(event.effects, mapping), fullMapping, event.startSelection.map(before), selections);\n}\nconst joinableUserEvent = /^(input\\.type|delete)($|\\.)/;\nclass HistoryState {\n    constructor(done, undone, prevTime = 0, prevUserEvent = undefined) {\n        this.done = done;\n        this.undone = undone;\n        this.prevTime = prevTime;\n        this.prevUserEvent = prevUserEvent;\n    }\n    isolate() {\n        return this.prevTime ? new HistoryState(this.done, this.undone) : this;\n    }\n    addChanges(event, time, userEvent, config, tr) {\n        let done = this.done, lastEvent = done[done.length - 1];\n        if (lastEvent && lastEvent.changes && !lastEvent.changes.empty && event.changes &&\n            (!userEvent || joinableUserEvent.test(userEvent)) &&\n            ((!lastEvent.selectionsAfter.length &&\n                time - this.prevTime < config.newGroupDelay &&\n                config.joinToEvent(tr, isAdjacent(lastEvent.changes, event.changes))) ||\n                // For compose (but not compose.start) events, always join with previous event\n                userEvent == \"input.type.compose\")) {\n            done = updateBranch(done, done.length - 1, config.minDepth, new HistEvent(event.changes.compose(lastEvent.changes), conc(_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.mapEffects(event.effects, lastEvent.changes), lastEvent.effects), lastEvent.mapped, lastEvent.startSelection, none));\n        }\n        else {\n            done = updateBranch(done, done.length, config.minDepth, event);\n        }\n        return new HistoryState(done, none, time, userEvent);\n    }\n    addSelection(selection, time, userEvent, newGroupDelay) {\n        let last = this.done.length ? this.done[this.done.length - 1].selectionsAfter : none;\n        if (last.length > 0 &&\n            time - this.prevTime < newGroupDelay &&\n            userEvent == this.prevUserEvent && userEvent && /^select($|\\.)/.test(userEvent) &&\n            eqSelectionShape(last[last.length - 1], selection))\n            return this;\n        return new HistoryState(addSelection(this.done, selection), this.undone, time, userEvent);\n    }\n    addMapping(mapping) {\n        return new HistoryState(addMappingToBranch(this.done, mapping), addMappingToBranch(this.undone, mapping), this.prevTime, this.prevUserEvent);\n    }\n    pop(side, state, onlySelection) {\n        let branch = side == 0 /* BranchName.Done */ ? this.done : this.undone;\n        if (branch.length == 0)\n            return null;\n        let event = branch[branch.length - 1], selection = event.selectionsAfter[0] || state.selection;\n        if (onlySelection && event.selectionsAfter.length) {\n            return state.update({\n                selection: event.selectionsAfter[event.selectionsAfter.length - 1],\n                annotations: fromHistory.of({ side, rest: popSelection(branch), selection }),\n                userEvent: side == 0 /* BranchName.Done */ ? \"select.undo\" : \"select.redo\",\n                scrollIntoView: true\n            });\n        }\n        else if (!event.changes) {\n            return null;\n        }\n        else {\n            let rest = branch.length == 1 ? none : branch.slice(0, branch.length - 1);\n            if (event.mapped)\n                rest = addMappingToBranch(rest, event.mapped);\n            return state.update({\n                changes: event.changes,\n                selection: event.startSelection,\n                effects: event.effects,\n                annotations: fromHistory.of({ side, rest, selection }),\n                filter: false,\n                userEvent: side == 0 /* BranchName.Done */ ? \"undo\" : \"redo\",\n                scrollIntoView: true\n            });\n        }\n    }\n}\nHistoryState.empty = /*@__PURE__*/new HistoryState(none, none);\n/**\nDefault key bindings for the undo history.\n\n- Mod-z: [`undo`](https://codemirror.net/6/docs/ref/#commands.undo).\n- Mod-y (Mod-Shift-z on macOS) + Ctrl-Shift-z on Linux: [`redo`](https://codemirror.net/6/docs/ref/#commands.redo).\n- Mod-u: [`undoSelection`](https://codemirror.net/6/docs/ref/#commands.undoSelection).\n- Alt-u (Mod-Shift-u on macOS): [`redoSelection`](https://codemirror.net/6/docs/ref/#commands.redoSelection).\n*/\nconst historyKeymap = [\n    { key: \"Mod-z\", run: undo, preventDefault: true },\n    { key: \"Mod-y\", mac: \"Mod-Shift-z\", run: redo, preventDefault: true },\n    { linux: \"Ctrl-Shift-z\", run: redo, preventDefault: true },\n    { key: \"Mod-u\", run: undoSelection, preventDefault: true },\n    { key: \"Alt-u\", mac: \"Mod-Shift-u\", run: redoSelection, preventDefault: true }\n];\n\nfunction updateSel(sel, by) {\n    return _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.create(sel.ranges.map(by), sel.mainIndex);\n}\nfunction setSel(state, selection) {\n    return state.update({ selection, scrollIntoView: true, userEvent: \"select\" });\n}\nfunction moveSel({ state, dispatch }, how) {\n    let selection = updateSel(state.selection, how);\n    if (selection.eq(state.selection, true))\n        return false;\n    dispatch(setSel(state, selection));\n    return true;\n}\nfunction rangeEnd(range, forward) {\n    return _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(forward ? range.to : range.from);\n}\nfunction cursorByChar(view, forward) {\n    return moveSel(view, range => range.empty ? view.moveByChar(range, forward) : rangeEnd(range, forward));\n}\nfunction ltrAtCursor(view) {\n    return view.textDirectionAt(view.state.selection.main.head) == _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Direction.LTR;\n}\n/**\nMove the selection one character to the left (which is backward in\nleft-to-right text, forward in right-to-left text).\n*/\nconst cursorCharLeft = view => cursorByChar(view, !ltrAtCursor(view));\n/**\nMove the selection one character to the right.\n*/\nconst cursorCharRight = view => cursorByChar(view, ltrAtCursor(view));\n/**\nMove the selection one character forward.\n*/\nconst cursorCharForward = view => cursorByChar(view, true);\n/**\nMove the selection one character backward.\n*/\nconst cursorCharBackward = view => cursorByChar(view, false);\nfunction byCharLogical(state, range, forward) {\n    let pos = range.head, line = state.doc.lineAt(pos);\n    if (pos == (forward ? line.to : line.from))\n        pos = forward ? Math.min(state.doc.length, line.to + 1) : Math.max(0, line.from - 1);\n    else\n        pos = line.from + (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.findClusterBreak)(line.text, pos - line.from, forward);\n    return _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(pos, forward ? -1 : 1);\n}\nfunction moveByCharLogical(target, forward) {\n    return moveSel(target, range => range.empty ? byCharLogical(target.state, range, forward) : rangeEnd(range, forward));\n}\n/**\nMove the selection one character forward, in logical\n(non-text-direction-aware) string index order.\n*/\nconst cursorCharForwardLogical = target => moveByCharLogical(target, true);\n/**\nMove the selection one character backward, in logical string index\norder.\n*/\nconst cursorCharBackwardLogical = target => moveByCharLogical(target, false);\nfunction cursorByGroup(view, forward) {\n    return moveSel(view, range => range.empty ? view.moveByGroup(range, forward) : rangeEnd(range, forward));\n}\n/**\nMove the selection to the left across one group of word or\nnon-word (but also non-space) characters.\n*/\nconst cursorGroupLeft = view => cursorByGroup(view, !ltrAtCursor(view));\n/**\nMove the selection one group to the right.\n*/\nconst cursorGroupRight = view => cursorByGroup(view, ltrAtCursor(view));\n/**\nMove the selection one group forward.\n*/\nconst cursorGroupForward = view => cursorByGroup(view, true);\n/**\nMove the selection one group backward.\n*/\nconst cursorGroupBackward = view => cursorByGroup(view, false);\nfunction toGroupStart(view, pos, start) {\n    let categorize = view.state.charCategorizer(pos);\n    let cat = categorize(start), initial = cat != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Space;\n    return (next) => {\n        let nextCat = categorize(next);\n        if (nextCat != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Space)\n            return initial && nextCat == cat;\n        initial = false;\n        return true;\n    };\n}\n/**\nMove the cursor one group forward in the default Windows style,\nwhere it moves to the start of the next group.\n*/\nconst cursorGroupForwardWin = view => {\n    return moveSel(view, range => range.empty\n        ? view.moveByChar(range, true, start => toGroupStart(view, range.head, start))\n        : rangeEnd(range, true));\n};\nconst segmenter = typeof Intl != \"undefined\" && Intl.Segmenter ?\n    /*@__PURE__*/new (Intl.Segmenter)(undefined, { granularity: \"word\" }) : null;\nfunction moveBySubword(view, range, forward) {\n    let categorize = view.state.charCategorizer(range.from);\n    let cat = _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Space, pos = range.from, steps = 0;\n    let done = false, sawUpper = false, sawLower = false;\n    let step = (next) => {\n        if (done)\n            return false;\n        pos += forward ? next.length : -next.length;\n        let nextCat = categorize(next), ahead;\n        if (nextCat == _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word && next.charCodeAt(0) < 128 && /[\\W_]/.test(next))\n            nextCat = -1; // Treat word punctuation specially\n        if (cat == _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Space)\n            cat = nextCat;\n        if (cat != nextCat)\n            return false;\n        if (cat == _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word) {\n            if (next.toLowerCase() == next) {\n                if (!forward && sawUpper)\n                    return false;\n                sawLower = true;\n            }\n            else if (sawLower) {\n                if (forward)\n                    return false;\n                done = true;\n            }\n            else {\n                if (sawUpper && forward && categorize(ahead = view.state.sliceDoc(pos, pos + 1)) == _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word &&\n                    ahead.toLowerCase() == ahead)\n                    return false;\n                sawUpper = true;\n            }\n        }\n        steps++;\n        return true;\n    };\n    let end = view.moveByChar(range, forward, start => {\n        step(start);\n        return step;\n    });\n    if (segmenter && cat == _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word && end.from == range.from + steps * (forward ? 1 : -1)) {\n        let from = Math.min(range.head, end.head), to = Math.max(range.head, end.head);\n        let skipped = view.state.sliceDoc(from, to);\n        if (skipped.length > 1 && /[\\u4E00-\\uffff]/.test(skipped)) {\n            let segments = Array.from(segmenter.segment(skipped));\n            if (segments.length > 1) {\n                if (forward)\n                    return _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(range.head + segments[1].index, -1);\n                return _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(end.head + segments[segments.length - 1].index, 1);\n            }\n        }\n    }\n    return end;\n}\nfunction cursorBySubword(view, forward) {\n    return moveSel(view, range => range.empty ? moveBySubword(view, range, forward) : rangeEnd(range, forward));\n}\n/**\nMove the selection one group or camel-case subword forward.\n*/\nconst cursorSubwordForward = view => cursorBySubword(view, true);\n/**\nMove the selection one group or camel-case subword backward.\n*/\nconst cursorSubwordBackward = view => cursorBySubword(view, false);\nfunction interestingNode(state, node, bracketProp) {\n    if (node.type.prop(bracketProp))\n        return true;\n    let len = node.to - node.from;\n    return len && (len > 2 || /[^\\s,.;:]/.test(state.sliceDoc(node.from, node.to))) || node.firstChild;\n}\nfunction moveBySyntax(state, start, forward) {\n    let pos = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxTree)(state).resolveInner(start.head);\n    let bracketProp = forward ? _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.closedBy : _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.openedBy;\n    // Scan forward through child nodes to see if there's an interesting\n    // node ahead.\n    for (let at = start.head;;) {\n        let next = forward ? pos.childAfter(at) : pos.childBefore(at);\n        if (!next)\n            break;\n        if (interestingNode(state, next, bracketProp))\n            pos = next;\n        else\n            at = forward ? next.to : next.from;\n    }\n    let bracket = pos.type.prop(bracketProp), match, newPos;\n    if (bracket && (match = forward ? (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.matchBrackets)(state, pos.from, 1) : (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.matchBrackets)(state, pos.to, -1)) && match.matched)\n        newPos = forward ? match.end.to : match.end.from;\n    else\n        newPos = forward ? pos.to : pos.from;\n    return _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(newPos, forward ? -1 : 1);\n}\n/**\nMove the cursor over the next syntactic element to the left.\n*/\nconst cursorSyntaxLeft = view => moveSel(view, range => moveBySyntax(view.state, range, !ltrAtCursor(view)));\n/**\nMove the cursor over the next syntactic element to the right.\n*/\nconst cursorSyntaxRight = view => moveSel(view, range => moveBySyntax(view.state, range, ltrAtCursor(view)));\nfunction cursorByLine(view, forward) {\n    return moveSel(view, range => {\n        if (!range.empty)\n            return rangeEnd(range, forward);\n        let moved = view.moveVertically(range, forward);\n        return moved.head != range.head ? moved : view.moveToLineBoundary(range, forward);\n    });\n}\n/**\nMove the selection one line up.\n*/\nconst cursorLineUp = view => cursorByLine(view, false);\n/**\nMove the selection one line down.\n*/\nconst cursorLineDown = view => cursorByLine(view, true);\nfunction pageInfo(view) {\n    let selfScroll = view.scrollDOM.clientHeight < view.scrollDOM.scrollHeight - 2;\n    let marginTop = 0, marginBottom = 0, height;\n    if (selfScroll) {\n        for (let source of view.state.facet(_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.scrollMargins)) {\n            let margins = source(view);\n            if (margins === null || margins === void 0 ? void 0 : margins.top)\n                marginTop = Math.max(margins === null || margins === void 0 ? void 0 : margins.top, marginTop);\n            if (margins === null || margins === void 0 ? void 0 : margins.bottom)\n                marginBottom = Math.max(margins === null || margins === void 0 ? void 0 : margins.bottom, marginBottom);\n        }\n        height = view.scrollDOM.clientHeight - marginTop - marginBottom;\n    }\n    else {\n        height = (view.dom.ownerDocument.defaultView || window).innerHeight;\n    }\n    return { marginTop, marginBottom, selfScroll,\n        height: Math.max(view.defaultLineHeight, height - 5) };\n}\nfunction cursorByPage(view, forward) {\n    let page = pageInfo(view);\n    let { state } = view, selection = updateSel(state.selection, range => {\n        return range.empty ? view.moveVertically(range, forward, page.height)\n            : rangeEnd(range, forward);\n    });\n    if (selection.eq(state.selection))\n        return false;\n    let effect;\n    if (page.selfScroll) {\n        let startPos = view.coordsAtPos(state.selection.main.head);\n        let scrollRect = view.scrollDOM.getBoundingClientRect();\n        let scrollTop = scrollRect.top + page.marginTop, scrollBottom = scrollRect.bottom - page.marginBottom;\n        if (startPos && startPos.top > scrollTop && startPos.bottom < scrollBottom)\n            effect = _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.scrollIntoView(selection.main.head, { y: \"start\", yMargin: startPos.top - scrollTop });\n    }\n    view.dispatch(setSel(state, selection), { effects: effect });\n    return true;\n}\n/**\nMove the selection one page up.\n*/\nconst cursorPageUp = view => cursorByPage(view, false);\n/**\nMove the selection one page down.\n*/\nconst cursorPageDown = view => cursorByPage(view, true);\nfunction moveByLineBoundary(view, start, forward) {\n    let line = view.lineBlockAt(start.head), moved = view.moveToLineBoundary(start, forward);\n    if (moved.head == start.head && moved.head != (forward ? line.to : line.from))\n        moved = view.moveToLineBoundary(start, forward, false);\n    if (!forward && moved.head == line.from && line.length) {\n        let space = /^\\s*/.exec(view.state.sliceDoc(line.from, Math.min(line.from + 100, line.to)))[0].length;\n        if (space && start.head != line.from + space)\n            moved = _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(line.from + space);\n    }\n    return moved;\n}\n/**\nMove the selection to the next line wrap point, or to the end of\nthe line if there isn't one left on this line.\n*/\nconst cursorLineBoundaryForward = view => moveSel(view, range => moveByLineBoundary(view, range, true));\n/**\nMove the selection to previous line wrap point, or failing that to\nthe start of the line. If the line is indented, and the cursor\nisn't already at the end of the indentation, this will move to the\nend of the indentation instead of the start of the line.\n*/\nconst cursorLineBoundaryBackward = view => moveSel(view, range => moveByLineBoundary(view, range, false));\n/**\nMove the selection one line wrap point to the left.\n*/\nconst cursorLineBoundaryLeft = view => moveSel(view, range => moveByLineBoundary(view, range, !ltrAtCursor(view)));\n/**\nMove the selection one line wrap point to the right.\n*/\nconst cursorLineBoundaryRight = view => moveSel(view, range => moveByLineBoundary(view, range, ltrAtCursor(view)));\n/**\nMove the selection to the start of the line.\n*/\nconst cursorLineStart = view => moveSel(view, range => _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(view.lineBlockAt(range.head).from, 1));\n/**\nMove the selection to the end of the line.\n*/\nconst cursorLineEnd = view => moveSel(view, range => _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(view.lineBlockAt(range.head).to, -1));\nfunction toMatchingBracket(state, dispatch, extend) {\n    let found = false, selection = updateSel(state.selection, range => {\n        let matching = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.matchBrackets)(state, range.head, -1)\n            || (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.matchBrackets)(state, range.head, 1)\n            || (range.head > 0 && (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.matchBrackets)(state, range.head - 1, 1))\n            || (range.head < state.doc.length && (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.matchBrackets)(state, range.head + 1, -1));\n        if (!matching || !matching.end)\n            return range;\n        found = true;\n        let head = matching.start.from == range.head ? matching.end.to : matching.end.from;\n        return extend ? _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.range(range.anchor, head) : _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(head);\n    });\n    if (!found)\n        return false;\n    dispatch(setSel(state, selection));\n    return true;\n}\n/**\nMove the selection to the bracket matching the one it is currently\non, if any.\n*/\nconst cursorMatchingBracket = ({ state, dispatch }) => toMatchingBracket(state, dispatch, false);\n/**\nExtend the selection to the bracket matching the one the selection\nhead is currently on, if any.\n*/\nconst selectMatchingBracket = ({ state, dispatch }) => toMatchingBracket(state, dispatch, true);\nfunction extendSel(target, how) {\n    let selection = updateSel(target.state.selection, range => {\n        let head = how(range);\n        return _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.range(range.anchor, head.head, head.goalColumn, head.bidiLevel || undefined);\n    });\n    if (selection.eq(target.state.selection))\n        return false;\n    target.dispatch(setSel(target.state, selection));\n    return true;\n}\nfunction selectByChar(view, forward) {\n    return extendSel(view, range => view.moveByChar(range, forward));\n}\n/**\nMove the selection head one character to the left, while leaving\nthe anchor in place.\n*/\nconst selectCharLeft = view => selectByChar(view, !ltrAtCursor(view));\n/**\nMove the selection head one character to the right.\n*/\nconst selectCharRight = view => selectByChar(view, ltrAtCursor(view));\n/**\nMove the selection head one character forward.\n*/\nconst selectCharForward = view => selectByChar(view, true);\n/**\nMove the selection head one character backward.\n*/\nconst selectCharBackward = view => selectByChar(view, false);\n/**\nMove the selection head one character forward by logical\n(non-direction aware) string index order.\n*/\nconst selectCharForwardLogical = target => extendSel(target, range => byCharLogical(target.state, range, true));\n/**\nMove the selection head one character backward by logical string\nindex order.\n*/\nconst selectCharBackwardLogical = target => extendSel(target, range => byCharLogical(target.state, range, false));\nfunction selectByGroup(view, forward) {\n    return extendSel(view, range => view.moveByGroup(range, forward));\n}\n/**\nMove the selection head one [group](https://codemirror.net/6/docs/ref/#commands.cursorGroupLeft) to\nthe left.\n*/\nconst selectGroupLeft = view => selectByGroup(view, !ltrAtCursor(view));\n/**\nMove the selection head one group to the right.\n*/\nconst selectGroupRight = view => selectByGroup(view, ltrAtCursor(view));\n/**\nMove the selection head one group forward.\n*/\nconst selectGroupForward = view => selectByGroup(view, true);\n/**\nMove the selection head one group backward.\n*/\nconst selectGroupBackward = view => selectByGroup(view, false);\n/**\nMove the selection head one group forward in the default Windows\nstyle, skipping to the start of the next group.\n*/\nconst selectGroupForwardWin = view => {\n    return extendSel(view, range => view.moveByChar(range, true, start => toGroupStart(view, range.head, start)));\n};\nfunction selectBySubword(view, forward) {\n    return extendSel(view, range => moveBySubword(view, range, forward));\n}\n/**\nMove the selection head one group or camel-case subword forward.\n*/\nconst selectSubwordForward = view => selectBySubword(view, true);\n/**\nMove the selection head one group or subword backward.\n*/\nconst selectSubwordBackward = view => selectBySubword(view, false);\n/**\nMove the selection head over the next syntactic element to the left.\n*/\nconst selectSyntaxLeft = view => extendSel(view, range => moveBySyntax(view.state, range, !ltrAtCursor(view)));\n/**\nMove the selection head over the next syntactic element to the right.\n*/\nconst selectSyntaxRight = view => extendSel(view, range => moveBySyntax(view.state, range, ltrAtCursor(view)));\nfunction selectByLine(view, forward) {\n    return extendSel(view, range => view.moveVertically(range, forward));\n}\n/**\nMove the selection head one line up.\n*/\nconst selectLineUp = view => selectByLine(view, false);\n/**\nMove the selection head one line down.\n*/\nconst selectLineDown = view => selectByLine(view, true);\nfunction selectByPage(view, forward) {\n    return extendSel(view, range => view.moveVertically(range, forward, pageInfo(view).height));\n}\n/**\nMove the selection head one page up.\n*/\nconst selectPageUp = view => selectByPage(view, false);\n/**\nMove the selection head one page down.\n*/\nconst selectPageDown = view => selectByPage(view, true);\n/**\nMove the selection head to the next line boundary.\n*/\nconst selectLineBoundaryForward = view => extendSel(view, range => moveByLineBoundary(view, range, true));\n/**\nMove the selection head to the previous line boundary.\n*/\nconst selectLineBoundaryBackward = view => extendSel(view, range => moveByLineBoundary(view, range, false));\n/**\nMove the selection head one line boundary to the left.\n*/\nconst selectLineBoundaryLeft = view => extendSel(view, range => moveByLineBoundary(view, range, !ltrAtCursor(view)));\n/**\nMove the selection head one line boundary to the right.\n*/\nconst selectLineBoundaryRight = view => extendSel(view, range => moveByLineBoundary(view, range, ltrAtCursor(view)));\n/**\nMove the selection head to the start of the line.\n*/\nconst selectLineStart = view => extendSel(view, range => _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(view.lineBlockAt(range.head).from));\n/**\nMove the selection head to the end of the line.\n*/\nconst selectLineEnd = view => extendSel(view, range => _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(view.lineBlockAt(range.head).to));\n/**\nMove the selection to the start of the document.\n*/\nconst cursorDocStart = ({ state, dispatch }) => {\n    dispatch(setSel(state, { anchor: 0 }));\n    return true;\n};\n/**\nMove the selection to the end of the document.\n*/\nconst cursorDocEnd = ({ state, dispatch }) => {\n    dispatch(setSel(state, { anchor: state.doc.length }));\n    return true;\n};\n/**\nMove the selection head to the start of the document.\n*/\nconst selectDocStart = ({ state, dispatch }) => {\n    dispatch(setSel(state, { anchor: state.selection.main.anchor, head: 0 }));\n    return true;\n};\n/**\nMove the selection head to the end of the document.\n*/\nconst selectDocEnd = ({ state, dispatch }) => {\n    dispatch(setSel(state, { anchor: state.selection.main.anchor, head: state.doc.length }));\n    return true;\n};\n/**\nSelect the entire document.\n*/\nconst selectAll = ({ state, dispatch }) => {\n    dispatch(state.update({ selection: { anchor: 0, head: state.doc.length }, userEvent: \"select\" }));\n    return true;\n};\n/**\nExpand the selection to cover entire lines.\n*/\nconst selectLine = ({ state, dispatch }) => {\n    let ranges = selectedLineBlocks(state).map(({ from, to }) => _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.range(from, Math.min(to + 1, state.doc.length)));\n    dispatch(state.update({ selection: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.create(ranges), userEvent: \"select\" }));\n    return true;\n};\n/**\nSelect the next syntactic construct that is larger than the\nselection. Note that this will only work insofar as the language\n[provider](https://codemirror.net/6/docs/ref/#language.language) you use builds up a full\nsyntax tree.\n*/\nconst selectParentSyntax = ({ state, dispatch }) => {\n    let selection = updateSel(state.selection, range => {\n        let tree = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxTree)(state), stack = tree.resolveStack(range.from, 1);\n        if (range.empty) {\n            let stackBefore = tree.resolveStack(range.from, -1);\n            if (stackBefore.node.from >= stack.node.from && stackBefore.node.to <= stack.node.to)\n                stack = stackBefore;\n        }\n        for (let cur = stack; cur; cur = cur.next) {\n            let { node } = cur;\n            if (((node.from < range.from && node.to >= range.to) ||\n                (node.to > range.to && node.from <= range.from)) &&\n                cur.next)\n                return _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.range(node.to, node.from);\n        }\n        return range;\n    });\n    if (selection.eq(state.selection))\n        return false;\n    dispatch(setSel(state, selection));\n    return true;\n};\n/**\nSimplify the current selection. When multiple ranges are selected,\nreduce it to its main range. Otherwise, if the selection is\nnon-empty, convert it to a cursor selection.\n*/\nconst simplifySelection = ({ state, dispatch }) => {\n    let cur = state.selection, selection = null;\n    if (cur.ranges.length > 1)\n        selection = _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.create([cur.main]);\n    else if (!cur.main.empty)\n        selection = _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.create([_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(cur.main.head)]);\n    if (!selection)\n        return false;\n    dispatch(setSel(state, selection));\n    return true;\n};\nfunction deleteBy(target, by) {\n    if (target.state.readOnly)\n        return false;\n    let event = \"delete.selection\", { state } = target;\n    let changes = state.changeByRange(range => {\n        let { from, to } = range;\n        if (from == to) {\n            let towards = by(range);\n            if (towards < from) {\n                event = \"delete.backward\";\n                towards = skipAtomic(target, towards, false);\n            }\n            else if (towards > from) {\n                event = \"delete.forward\";\n                towards = skipAtomic(target, towards, true);\n            }\n            from = Math.min(from, towards);\n            to = Math.max(to, towards);\n        }\n        else {\n            from = skipAtomic(target, from, false);\n            to = skipAtomic(target, to, true);\n        }\n        return from == to ? { range } : { changes: { from, to }, range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(from, from < range.head ? -1 : 1) };\n    });\n    if (changes.changes.empty)\n        return false;\n    target.dispatch(state.update(changes, {\n        scrollIntoView: true,\n        userEvent: event,\n        effects: event == \"delete.selection\" ? _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.announce.of(state.phrase(\"Selection deleted\")) : undefined\n    }));\n    return true;\n}\nfunction skipAtomic(target, pos, forward) {\n    if (target instanceof _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView)\n        for (let ranges of target.state.facet(_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.atomicRanges).map(f => f(target)))\n            ranges.between(pos, pos, (from, to) => {\n                if (from < pos && to > pos)\n                    pos = forward ? to : from;\n            });\n    return pos;\n}\nconst deleteByChar = (target, forward, byIndentUnit) => deleteBy(target, range => {\n    let pos = range.from, { state } = target, line = state.doc.lineAt(pos), before, targetPos;\n    if (byIndentUnit && !forward && pos > line.from && pos < line.from + 200 &&\n        !/[^ \\t]/.test(before = line.text.slice(0, pos - line.from))) {\n        if (before[before.length - 1] == \"\\t\")\n            return pos - 1;\n        let col = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.countColumn)(before, state.tabSize), drop = col % (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.getIndentUnit)(state) || (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.getIndentUnit)(state);\n        for (let i = 0; i < drop && before[before.length - 1 - i] == \" \"; i++)\n            pos--;\n        targetPos = pos;\n    }\n    else {\n        targetPos = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.findClusterBreak)(line.text, pos - line.from, forward, forward) + line.from;\n        if (targetPos == pos && line.number != (forward ? state.doc.lines : 1))\n            targetPos += forward ? 1 : -1;\n        else if (!forward && /[\\ufe00-\\ufe0f]/.test(line.text.slice(targetPos - line.from, pos - line.from)))\n            targetPos = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.findClusterBreak)(line.text, targetPos - line.from, false, false) + line.from;\n    }\n    return targetPos;\n});\n/**\nDelete the selection, or, for cursor selections, the character or\nindentation unit before the cursor.\n*/\nconst deleteCharBackward = view => deleteByChar(view, false, true);\n/**\nDelete the selection or the character before the cursor. Does not\nimplement any extended behavior like deleting whole indentation\nunits in one go.\n*/\nconst deleteCharBackwardStrict = view => deleteByChar(view, false, false);\n/**\nDelete the selection or the character after the cursor.\n*/\nconst deleteCharForward = view => deleteByChar(view, true, false);\nconst deleteByGroup = (target, forward) => deleteBy(target, range => {\n    let pos = range.head, { state } = target, line = state.doc.lineAt(pos);\n    let categorize = state.charCategorizer(pos);\n    for (let cat = null;;) {\n        if (pos == (forward ? line.to : line.from)) {\n            if (pos == range.head && line.number != (forward ? state.doc.lines : 1))\n                pos += forward ? 1 : -1;\n            break;\n        }\n        let next = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.findClusterBreak)(line.text, pos - line.from, forward) + line.from;\n        let nextChar = line.text.slice(Math.min(pos, next) - line.from, Math.max(pos, next) - line.from);\n        let nextCat = categorize(nextChar);\n        if (cat != null && nextCat != cat)\n            break;\n        if (nextChar != \" \" || pos != range.head)\n            cat = nextCat;\n        pos = next;\n    }\n    return pos;\n});\n/**\nDelete the selection or backward until the end of the next\n[group](https://codemirror.net/6/docs/ref/#view.EditorView.moveByGroup), only skipping groups of\nwhitespace when they consist of a single space.\n*/\nconst deleteGroupBackward = target => deleteByGroup(target, false);\n/**\nDelete the selection or forward until the end of the next group.\n*/\nconst deleteGroupForward = target => deleteByGroup(target, true);\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe end of the line. If the cursor is directly at the end of the\nline, delete the line break after it.\n*/\nconst deleteToLineEnd = view => deleteBy(view, range => {\n    let lineEnd = view.lineBlockAt(range.head).to;\n    return range.head < lineEnd ? lineEnd : Math.min(view.state.doc.length, range.head + 1);\n});\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe start of the line. If the cursor is directly at the start of the\nline, delete the line break before it.\n*/\nconst deleteToLineStart = view => deleteBy(view, range => {\n    let lineStart = view.lineBlockAt(range.head).from;\n    return range.head > lineStart ? lineStart : Math.max(0, range.head - 1);\n});\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe start of the line or the next line wrap before the cursor.\n*/\nconst deleteLineBoundaryBackward = view => deleteBy(view, range => {\n    let lineStart = view.moveToLineBoundary(range, false).head;\n    return range.head > lineStart ? lineStart : Math.max(0, range.head - 1);\n});\n/**\nDelete the selection, or, if it is a cursor selection, delete to\nthe end of the line or the next line wrap after the cursor.\n*/\nconst deleteLineBoundaryForward = view => deleteBy(view, range => {\n    let lineStart = view.moveToLineBoundary(range, true).head;\n    return range.head < lineStart ? lineStart : Math.min(view.state.doc.length, range.head + 1);\n});\n/**\nDelete all whitespace directly before a line end from the\ndocument.\n*/\nconst deleteTrailingWhitespace = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let changes = [];\n    for (let pos = 0, prev = \"\", iter = state.doc.iter();;) {\n        iter.next();\n        if (iter.lineBreak || iter.done) {\n            let trailing = prev.search(/\\s+$/);\n            if (trailing > -1)\n                changes.push({ from: pos - (prev.length - trailing), to: pos });\n            if (iter.done)\n                break;\n            prev = \"\";\n        }\n        else {\n            prev = iter.value;\n        }\n        pos += iter.value.length;\n    }\n    if (!changes.length)\n        return false;\n    dispatch(state.update({ changes, userEvent: \"delete\" }));\n    return true;\n};\n/**\nReplace each selection range with a line break, leaving the cursor\non the line before the break.\n*/\nconst splitLine = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let changes = state.changeByRange(range => {\n        return { changes: { from: range.from, to: range.to, insert: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Text.of([\"\", \"\"]) },\n            range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(range.from) };\n    });\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\n/**\nFlip the characters before and after the cursor(s).\n*/\nconst transposeChars = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let changes = state.changeByRange(range => {\n        if (!range.empty || range.from == 0 || range.from == state.doc.length)\n            return { range };\n        let pos = range.from, line = state.doc.lineAt(pos);\n        let from = pos == line.from ? pos - 1 : (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.findClusterBreak)(line.text, pos - line.from, false) + line.from;\n        let to = pos == line.to ? pos + 1 : (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.findClusterBreak)(line.text, pos - line.from, true) + line.from;\n        return { changes: { from, to, insert: state.doc.slice(pos, to).append(state.doc.slice(from, pos)) },\n            range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(to) };\n    });\n    if (changes.changes.empty)\n        return false;\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"move.character\" }));\n    return true;\n};\nfunction selectedLineBlocks(state) {\n    let blocks = [], upto = -1;\n    for (let range of state.selection.ranges) {\n        let startLine = state.doc.lineAt(range.from), endLine = state.doc.lineAt(range.to);\n        if (!range.empty && range.to == endLine.from)\n            endLine = state.doc.lineAt(range.to - 1);\n        if (upto >= startLine.number) {\n            let prev = blocks[blocks.length - 1];\n            prev.to = endLine.to;\n            prev.ranges.push(range);\n        }\n        else {\n            blocks.push({ from: startLine.from, to: endLine.to, ranges: [range] });\n        }\n        upto = endLine.number + 1;\n    }\n    return blocks;\n}\nfunction moveLine(state, dispatch, forward) {\n    if (state.readOnly)\n        return false;\n    let changes = [], ranges = [];\n    for (let block of selectedLineBlocks(state)) {\n        if (forward ? block.to == state.doc.length : block.from == 0)\n            continue;\n        let nextLine = state.doc.lineAt(forward ? block.to + 1 : block.from - 1);\n        let size = nextLine.length + 1;\n        if (forward) {\n            changes.push({ from: block.to, to: nextLine.to }, { from: block.from, insert: nextLine.text + state.lineBreak });\n            for (let r of block.ranges)\n                ranges.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.range(Math.min(state.doc.length, r.anchor + size), Math.min(state.doc.length, r.head + size)));\n        }\n        else {\n            changes.push({ from: nextLine.from, to: block.from }, { from: block.to, insert: state.lineBreak + nextLine.text });\n            for (let r of block.ranges)\n                ranges.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.range(r.anchor - size, r.head - size));\n        }\n    }\n    if (!changes.length)\n        return false;\n    dispatch(state.update({\n        changes,\n        scrollIntoView: true,\n        selection: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.create(ranges, state.selection.mainIndex),\n        userEvent: \"move.line\"\n    }));\n    return true;\n}\n/**\nMove the selected lines up one line.\n*/\nconst moveLineUp = ({ state, dispatch }) => moveLine(state, dispatch, false);\n/**\nMove the selected lines down one line.\n*/\nconst moveLineDown = ({ state, dispatch }) => moveLine(state, dispatch, true);\nfunction copyLine(state, dispatch, forward) {\n    if (state.readOnly)\n        return false;\n    let changes = [];\n    for (let block of selectedLineBlocks(state)) {\n        if (forward)\n            changes.push({ from: block.from, insert: state.doc.slice(block.from, block.to) + state.lineBreak });\n        else\n            changes.push({ from: block.to, insert: state.lineBreak + state.doc.slice(block.from, block.to) });\n    }\n    dispatch(state.update({ changes, scrollIntoView: true, userEvent: \"input.copyline\" }));\n    return true;\n}\n/**\nCreate a copy of the selected lines. Keep the selection in the top copy.\n*/\nconst copyLineUp = ({ state, dispatch }) => copyLine(state, dispatch, false);\n/**\nCreate a copy of the selected lines. Keep the selection in the bottom copy.\n*/\nconst copyLineDown = ({ state, dispatch }) => copyLine(state, dispatch, true);\n/**\nDelete selected lines.\n*/\nconst deleteLine = view => {\n    if (view.state.readOnly)\n        return false;\n    let { state } = view, changes = state.changes(selectedLineBlocks(state).map(({ from, to }) => {\n        if (from > 0)\n            from--;\n        else if (to < state.doc.length)\n            to++;\n        return { from, to };\n    }));\n    let selection = updateSel(state.selection, range => {\n        let dist = undefined;\n        if (view.lineWrapping) {\n            let block = view.lineBlockAt(range.head), pos = view.coordsAtPos(range.head, range.assoc || 1);\n            if (pos)\n                dist = (block.bottom + view.documentTop) - pos.bottom + view.defaultLineHeight / 2;\n        }\n        return view.moveVertically(range, true, dist);\n    }).map(changes);\n    view.dispatch({ changes, selection, scrollIntoView: true, userEvent: \"delete.line\" });\n    return true;\n};\n/**\nReplace the selection with a newline.\n*/\nconst insertNewline = ({ state, dispatch }) => {\n    dispatch(state.update(state.replaceSelection(state.lineBreak), { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\n/**\nReplace the selection with a newline and the same amount of\nindentation as the line above.\n*/\nconst insertNewlineKeepIndent = ({ state, dispatch }) => {\n    dispatch(state.update(state.changeByRange(range => {\n        let indent = /^\\s*/.exec(state.doc.lineAt(range.from).text)[0];\n        return {\n            changes: { from: range.from, to: range.to, insert: state.lineBreak + indent },\n            range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(range.from + indent.length + 1)\n        };\n    }), { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\nfunction isBetweenBrackets(state, pos) {\n    if (/\\(\\)|\\[\\]|\\{\\}/.test(state.sliceDoc(pos - 1, pos + 1)))\n        return { from: pos, to: pos };\n    let context = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxTree)(state).resolveInner(pos);\n    let before = context.childBefore(pos), after = context.childAfter(pos), closedBy;\n    if (before && after && before.to <= pos && after.from >= pos &&\n        (closedBy = before.type.prop(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.closedBy)) && closedBy.indexOf(after.name) > -1 &&\n        state.doc.lineAt(before.to).from == state.doc.lineAt(after.from).from &&\n        !/\\S/.test(state.sliceDoc(before.to, after.from)))\n        return { from: before.to, to: after.from };\n    return null;\n}\n/**\nReplace the selection with a newline and indent the newly created\nline(s). If the current line consists only of whitespace, this\nwill also delete that whitespace. When the cursor is between\nmatching brackets, an additional newline will be inserted after\nthe cursor.\n*/\nconst insertNewlineAndIndent = /*@__PURE__*/newlineAndIndent(false);\n/**\nCreate a blank, indented line below the current line.\n*/\nconst insertBlankLine = /*@__PURE__*/newlineAndIndent(true);\nfunction newlineAndIndent(atEof) {\n    return ({ state, dispatch }) => {\n        if (state.readOnly)\n            return false;\n        let changes = state.changeByRange(range => {\n            let { from, to } = range, line = state.doc.lineAt(from);\n            let explode = !atEof && from == to && isBetweenBrackets(state, from);\n            if (atEof)\n                from = to = (to <= line.to ? line : state.doc.lineAt(to)).to;\n            let cx = new _codemirror_language__WEBPACK_IMPORTED_MODULE_3__.IndentContext(state, { simulateBreak: from, simulateDoubleBreak: !!explode });\n            let indent = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.getIndentation)(cx, from);\n            if (indent == null)\n                indent = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.countColumn)(/^\\s*/.exec(state.doc.lineAt(from).text)[0], state.tabSize);\n            while (to < line.to && /\\s/.test(line.text[to - line.from]))\n                to++;\n            if (explode)\n                ({ from, to } = explode);\n            else if (from > line.from && from < line.from + 100 && !/\\S/.test(line.text.slice(0, from)))\n                from = line.from;\n            let insert = [\"\", (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentString)(state, indent)];\n            if (explode)\n                insert.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentString)(state, cx.lineIndent(line.from, -1)));\n            return { changes: { from, to, insert: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Text.of(insert) },\n                range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(from + 1 + insert[1].length) };\n        });\n        dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"input\" }));\n        return true;\n    };\n}\nfunction changeBySelectedLine(state, f) {\n    let atLine = -1;\n    return state.changeByRange(range => {\n        let changes = [];\n        for (let pos = range.from; pos <= range.to;) {\n            let line = state.doc.lineAt(pos);\n            if (line.number > atLine && (range.empty || range.to > line.from)) {\n                f(line, changes, range);\n                atLine = line.number;\n            }\n            pos = line.to + 1;\n        }\n        let changeSet = state.changes(changes);\n        return { changes,\n            range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.range(changeSet.mapPos(range.anchor, 1), changeSet.mapPos(range.head, 1)) };\n    });\n}\n/**\nAuto-indent the selected lines. This uses the [indentation service\nfacet](https://codemirror.net/6/docs/ref/#language.indentService) as source for auto-indent\ninformation.\n*/\nconst indentSelection = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let updated = Object.create(null);\n    let context = new _codemirror_language__WEBPACK_IMPORTED_MODULE_3__.IndentContext(state, { overrideIndentation: start => {\n            let found = updated[start];\n            return found == null ? -1 : found;\n        } });\n    let changes = changeBySelectedLine(state, (line, changes, range) => {\n        let indent = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.getIndentation)(context, line.from);\n        if (indent == null)\n            return;\n        if (!/\\S/.test(line.text))\n            indent = 0;\n        let cur = /^\\s*/.exec(line.text)[0];\n        let norm = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentString)(state, indent);\n        if (cur != norm || range.from < line.from + cur.length) {\n            updated[line.from] = indent;\n            changes.push({ from: line.from, to: line.from + cur.length, insert: norm });\n        }\n    });\n    if (!changes.changes.empty)\n        dispatch(state.update(changes, { userEvent: \"indent\" }));\n    return true;\n};\n/**\nAdd a [unit](https://codemirror.net/6/docs/ref/#language.indentUnit) of indentation to all selected\nlines.\n*/\nconst indentMore = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    dispatch(state.update(changeBySelectedLine(state, (line, changes) => {\n        changes.push({ from: line.from, insert: state.facet(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentUnit) });\n    }), { userEvent: \"input.indent\" }));\n    return true;\n};\n/**\nRemove a [unit](https://codemirror.net/6/docs/ref/#language.indentUnit) of indentation from all\nselected lines.\n*/\nconst indentLess = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    dispatch(state.update(changeBySelectedLine(state, (line, changes) => {\n        let space = /^\\s*/.exec(line.text)[0];\n        if (!space)\n            return;\n        let col = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.countColumn)(space, state.tabSize), keep = 0;\n        let insert = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentString)(state, Math.max(0, col - (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.getIndentUnit)(state)));\n        while (keep < space.length && keep < insert.length && space.charCodeAt(keep) == insert.charCodeAt(keep))\n            keep++;\n        changes.push({ from: line.from + keep, to: line.from + space.length, insert: insert.slice(keep) });\n    }), { userEvent: \"delete.dedent\" }));\n    return true;\n};\n/**\nEnables or disables\n[tab-focus mode](https://codemirror.net/6/docs/ref/#view.EditorView.setTabFocusMode). While on, this\nprevents the editor's key bindings from capturing Tab or\nShift-Tab, making it possible for the user to move focus out of\nthe editor with the keyboard.\n*/\nconst toggleTabFocusMode = view => {\n    view.setTabFocusMode();\n    return true;\n};\n/**\nTemporarily enables [tab-focus\nmode](https://codemirror.net/6/docs/ref/#view.EditorView.setTabFocusMode) for two seconds or until\nanother key is pressed.\n*/\nconst temporarilySetTabFocusMode = view => {\n    view.setTabFocusMode(2000);\n    return true;\n};\n/**\nInsert a tab character at the cursor or, if something is selected,\nuse [`indentMore`](https://codemirror.net/6/docs/ref/#commands.indentMore) to indent the entire\nselection.\n*/\nconst insertTab = ({ state, dispatch }) => {\n    if (state.selection.ranges.some(r => !r.empty))\n        return indentMore({ state, dispatch });\n    dispatch(state.update(state.replaceSelection(\"\\t\"), { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\n/**\nArray of key bindings containing the Emacs-style bindings that are\navailable on macOS by default.\n\n - Ctrl-b: [`cursorCharLeft`](https://codemirror.net/6/docs/ref/#commands.cursorCharLeft) ([`selectCharLeft`](https://codemirror.net/6/docs/ref/#commands.selectCharLeft) with Shift)\n - Ctrl-f: [`cursorCharRight`](https://codemirror.net/6/docs/ref/#commands.cursorCharRight) ([`selectCharRight`](https://codemirror.net/6/docs/ref/#commands.selectCharRight) with Shift)\n - Ctrl-p: [`cursorLineUp`](https://codemirror.net/6/docs/ref/#commands.cursorLineUp) ([`selectLineUp`](https://codemirror.net/6/docs/ref/#commands.selectLineUp) with Shift)\n - Ctrl-n: [`cursorLineDown`](https://codemirror.net/6/docs/ref/#commands.cursorLineDown) ([`selectLineDown`](https://codemirror.net/6/docs/ref/#commands.selectLineDown) with Shift)\n - Ctrl-a: [`cursorLineStart`](https://codemirror.net/6/docs/ref/#commands.cursorLineStart) ([`selectLineStart`](https://codemirror.net/6/docs/ref/#commands.selectLineStart) with Shift)\n - Ctrl-e: [`cursorLineEnd`](https://codemirror.net/6/docs/ref/#commands.cursorLineEnd) ([`selectLineEnd`](https://codemirror.net/6/docs/ref/#commands.selectLineEnd) with Shift)\n - Ctrl-d: [`deleteCharForward`](https://codemirror.net/6/docs/ref/#commands.deleteCharForward)\n - Ctrl-h: [`deleteCharBackward`](https://codemirror.net/6/docs/ref/#commands.deleteCharBackward)\n - Ctrl-k: [`deleteToLineEnd`](https://codemirror.net/6/docs/ref/#commands.deleteToLineEnd)\n - Ctrl-Alt-h: [`deleteGroupBackward`](https://codemirror.net/6/docs/ref/#commands.deleteGroupBackward)\n - Ctrl-o: [`splitLine`](https://codemirror.net/6/docs/ref/#commands.splitLine)\n - Ctrl-t: [`transposeChars`](https://codemirror.net/6/docs/ref/#commands.transposeChars)\n - Ctrl-v: [`cursorPageDown`](https://codemirror.net/6/docs/ref/#commands.cursorPageDown)\n - Alt-v: [`cursorPageUp`](https://codemirror.net/6/docs/ref/#commands.cursorPageUp)\n*/\nconst emacsStyleKeymap = [\n    { key: \"Ctrl-b\", run: cursorCharLeft, shift: selectCharLeft, preventDefault: true },\n    { key: \"Ctrl-f\", run: cursorCharRight, shift: selectCharRight },\n    { key: \"Ctrl-p\", run: cursorLineUp, shift: selectLineUp },\n    { key: \"Ctrl-n\", run: cursorLineDown, shift: selectLineDown },\n    { key: \"Ctrl-a\", run: cursorLineStart, shift: selectLineStart },\n    { key: \"Ctrl-e\", run: cursorLineEnd, shift: selectLineEnd },\n    { key: \"Ctrl-d\", run: deleteCharForward },\n    { key: \"Ctrl-h\", run: deleteCharBackward },\n    { key: \"Ctrl-k\", run: deleteToLineEnd },\n    { key: \"Ctrl-Alt-h\", run: deleteGroupBackward },\n    { key: \"Ctrl-o\", run: splitLine },\n    { key: \"Ctrl-t\", run: transposeChars },\n    { key: \"Ctrl-v\", run: cursorPageDown },\n];\n/**\nAn array of key bindings closely sticking to platform-standard or\nwidely used bindings. (This includes the bindings from\n[`emacsStyleKeymap`](https://codemirror.net/6/docs/ref/#commands.emacsStyleKeymap), with their `key`\nproperty changed to `mac`.)\n\n - ArrowLeft: [`cursorCharLeft`](https://codemirror.net/6/docs/ref/#commands.cursorCharLeft) ([`selectCharLeft`](https://codemirror.net/6/docs/ref/#commands.selectCharLeft) with Shift)\n - ArrowRight: [`cursorCharRight`](https://codemirror.net/6/docs/ref/#commands.cursorCharRight) ([`selectCharRight`](https://codemirror.net/6/docs/ref/#commands.selectCharRight) with Shift)\n - Ctrl-ArrowLeft (Alt-ArrowLeft on macOS): [`cursorGroupLeft`](https://codemirror.net/6/docs/ref/#commands.cursorGroupLeft) ([`selectGroupLeft`](https://codemirror.net/6/docs/ref/#commands.selectGroupLeft) with Shift)\n - Ctrl-ArrowRight (Alt-ArrowRight on macOS): [`cursorGroupRight`](https://codemirror.net/6/docs/ref/#commands.cursorGroupRight) ([`selectGroupRight`](https://codemirror.net/6/docs/ref/#commands.selectGroupRight) with Shift)\n - Cmd-ArrowLeft (on macOS): [`cursorLineStart`](https://codemirror.net/6/docs/ref/#commands.cursorLineStart) ([`selectLineStart`](https://codemirror.net/6/docs/ref/#commands.selectLineStart) with Shift)\n - Cmd-ArrowRight (on macOS): [`cursorLineEnd`](https://codemirror.net/6/docs/ref/#commands.cursorLineEnd) ([`selectLineEnd`](https://codemirror.net/6/docs/ref/#commands.selectLineEnd) with Shift)\n - ArrowUp: [`cursorLineUp`](https://codemirror.net/6/docs/ref/#commands.cursorLineUp) ([`selectLineUp`](https://codemirror.net/6/docs/ref/#commands.selectLineUp) with Shift)\n - ArrowDown: [`cursorLineDown`](https://codemirror.net/6/docs/ref/#commands.cursorLineDown) ([`selectLineDown`](https://codemirror.net/6/docs/ref/#commands.selectLineDown) with Shift)\n - Cmd-ArrowUp (on macOS): [`cursorDocStart`](https://codemirror.net/6/docs/ref/#commands.cursorDocStart) ([`selectDocStart`](https://codemirror.net/6/docs/ref/#commands.selectDocStart) with Shift)\n - Cmd-ArrowDown (on macOS): [`cursorDocEnd`](https://codemirror.net/6/docs/ref/#commands.cursorDocEnd) ([`selectDocEnd`](https://codemirror.net/6/docs/ref/#commands.selectDocEnd) with Shift)\n - Ctrl-ArrowUp (on macOS): [`cursorPageUp`](https://codemirror.net/6/docs/ref/#commands.cursorPageUp) ([`selectPageUp`](https://codemirror.net/6/docs/ref/#commands.selectPageUp) with Shift)\n - Ctrl-ArrowDown (on macOS): [`cursorPageDown`](https://codemirror.net/6/docs/ref/#commands.cursorPageDown) ([`selectPageDown`](https://codemirror.net/6/docs/ref/#commands.selectPageDown) with Shift)\n - PageUp: [`cursorPageUp`](https://codemirror.net/6/docs/ref/#commands.cursorPageUp) ([`selectPageUp`](https://codemirror.net/6/docs/ref/#commands.selectPageUp) with Shift)\n - PageDown: [`cursorPageDown`](https://codemirror.net/6/docs/ref/#commands.cursorPageDown) ([`selectPageDown`](https://codemirror.net/6/docs/ref/#commands.selectPageDown) with Shift)\n - Home: [`cursorLineBoundaryBackward`](https://codemirror.net/6/docs/ref/#commands.cursorLineBoundaryBackward) ([`selectLineBoundaryBackward`](https://codemirror.net/6/docs/ref/#commands.selectLineBoundaryBackward) with Shift)\n - End: [`cursorLineBoundaryForward`](https://codemirror.net/6/docs/ref/#commands.cursorLineBoundaryForward) ([`selectLineBoundaryForward`](https://codemirror.net/6/docs/ref/#commands.selectLineBoundaryForward) with Shift)\n - Ctrl-Home (Cmd-Home on macOS): [`cursorDocStart`](https://codemirror.net/6/docs/ref/#commands.cursorDocStart) ([`selectDocStart`](https://codemirror.net/6/docs/ref/#commands.selectDocStart) with Shift)\n - Ctrl-End (Cmd-Home on macOS): [`cursorDocEnd`](https://codemirror.net/6/docs/ref/#commands.cursorDocEnd) ([`selectDocEnd`](https://codemirror.net/6/docs/ref/#commands.selectDocEnd) with Shift)\n - Enter and Shift-Enter: [`insertNewlineAndIndent`](https://codemirror.net/6/docs/ref/#commands.insertNewlineAndIndent)\n - Ctrl-a (Cmd-a on macOS): [`selectAll`](https://codemirror.net/6/docs/ref/#commands.selectAll)\n - Backspace: [`deleteCharBackward`](https://codemirror.net/6/docs/ref/#commands.deleteCharBackward)\n - Delete: [`deleteCharForward`](https://codemirror.net/6/docs/ref/#commands.deleteCharForward)\n - Ctrl-Backspace (Alt-Backspace on macOS): [`deleteGroupBackward`](https://codemirror.net/6/docs/ref/#commands.deleteGroupBackward)\n - Ctrl-Delete (Alt-Delete on macOS): [`deleteGroupForward`](https://codemirror.net/6/docs/ref/#commands.deleteGroupForward)\n - Cmd-Backspace (macOS): [`deleteLineBoundaryBackward`](https://codemirror.net/6/docs/ref/#commands.deleteLineBoundaryBackward).\n - Cmd-Delete (macOS): [`deleteLineBoundaryForward`](https://codemirror.net/6/docs/ref/#commands.deleteLineBoundaryForward).\n*/\nconst standardKeymap = /*@__PURE__*/[\n    { key: \"ArrowLeft\", run: cursorCharLeft, shift: selectCharLeft, preventDefault: true },\n    { key: \"Mod-ArrowLeft\", mac: \"Alt-ArrowLeft\", run: cursorGroupLeft, shift: selectGroupLeft, preventDefault: true },\n    { mac: \"Cmd-ArrowLeft\", run: cursorLineBoundaryLeft, shift: selectLineBoundaryLeft, preventDefault: true },\n    { key: \"ArrowRight\", run: cursorCharRight, shift: selectCharRight, preventDefault: true },\n    { key: \"Mod-ArrowRight\", mac: \"Alt-ArrowRight\", run: cursorGroupRight, shift: selectGroupRight, preventDefault: true },\n    { mac: \"Cmd-ArrowRight\", run: cursorLineBoundaryRight, shift: selectLineBoundaryRight, preventDefault: true },\n    { key: \"ArrowUp\", run: cursorLineUp, shift: selectLineUp, preventDefault: true },\n    { mac: \"Cmd-ArrowUp\", run: cursorDocStart, shift: selectDocStart },\n    { mac: \"Ctrl-ArrowUp\", run: cursorPageUp, shift: selectPageUp },\n    { key: \"ArrowDown\", run: cursorLineDown, shift: selectLineDown, preventDefault: true },\n    { mac: \"Cmd-ArrowDown\", run: cursorDocEnd, shift: selectDocEnd },\n    { mac: \"Ctrl-ArrowDown\", run: cursorPageDown, shift: selectPageDown },\n    { key: \"PageUp\", run: cursorPageUp, shift: selectPageUp },\n    { key: \"PageDown\", run: cursorPageDown, shift: selectPageDown },\n    { key: \"Home\", run: cursorLineBoundaryBackward, shift: selectLineBoundaryBackward, preventDefault: true },\n    { key: \"Mod-Home\", run: cursorDocStart, shift: selectDocStart },\n    { key: \"End\", run: cursorLineBoundaryForward, shift: selectLineBoundaryForward, preventDefault: true },\n    { key: \"Mod-End\", run: cursorDocEnd, shift: selectDocEnd },\n    { key: \"Enter\", run: insertNewlineAndIndent, shift: insertNewlineAndIndent },\n    { key: \"Mod-a\", run: selectAll },\n    { key: \"Backspace\", run: deleteCharBackward, shift: deleteCharBackward },\n    { key: \"Delete\", run: deleteCharForward },\n    { key: \"Mod-Backspace\", mac: \"Alt-Backspace\", run: deleteGroupBackward },\n    { key: \"Mod-Delete\", mac: \"Alt-Delete\", run: deleteGroupForward },\n    { mac: \"Mod-Backspace\", run: deleteLineBoundaryBackward },\n    { mac: \"Mod-Delete\", run: deleteLineBoundaryForward }\n].concat(/*@__PURE__*/emacsStyleKeymap.map(b => ({ mac: b.key, run: b.run, shift: b.shift })));\n/**\nThe default keymap. Includes all bindings from\n[`standardKeymap`](https://codemirror.net/6/docs/ref/#commands.standardKeymap) plus the following:\n\n- Alt-ArrowLeft (Ctrl-ArrowLeft on macOS): [`cursorSyntaxLeft`](https://codemirror.net/6/docs/ref/#commands.cursorSyntaxLeft) ([`selectSyntaxLeft`](https://codemirror.net/6/docs/ref/#commands.selectSyntaxLeft) with Shift)\n- Alt-ArrowRight (Ctrl-ArrowRight on macOS): [`cursorSyntaxRight`](https://codemirror.net/6/docs/ref/#commands.cursorSyntaxRight) ([`selectSyntaxRight`](https://codemirror.net/6/docs/ref/#commands.selectSyntaxRight) with Shift)\n- Alt-ArrowUp: [`moveLineUp`](https://codemirror.net/6/docs/ref/#commands.moveLineUp)\n- Alt-ArrowDown: [`moveLineDown`](https://codemirror.net/6/docs/ref/#commands.moveLineDown)\n- Shift-Alt-ArrowUp: [`copyLineUp`](https://codemirror.net/6/docs/ref/#commands.copyLineUp)\n- Shift-Alt-ArrowDown: [`copyLineDown`](https://codemirror.net/6/docs/ref/#commands.copyLineDown)\n- Escape: [`simplifySelection`](https://codemirror.net/6/docs/ref/#commands.simplifySelection)\n- Ctrl-Enter (Cmd-Enter on macOS): [`insertBlankLine`](https://codemirror.net/6/docs/ref/#commands.insertBlankLine)\n- Alt-l (Ctrl-l on macOS): [`selectLine`](https://codemirror.net/6/docs/ref/#commands.selectLine)\n- Ctrl-i (Cmd-i on macOS): [`selectParentSyntax`](https://codemirror.net/6/docs/ref/#commands.selectParentSyntax)\n- Ctrl-[ (Cmd-[ on macOS): [`indentLess`](https://codemirror.net/6/docs/ref/#commands.indentLess)\n- Ctrl-] (Cmd-] on macOS): [`indentMore`](https://codemirror.net/6/docs/ref/#commands.indentMore)\n- Ctrl-Alt-\\\\ (Cmd-Alt-\\\\ on macOS): [`indentSelection`](https://codemirror.net/6/docs/ref/#commands.indentSelection)\n- Shift-Ctrl-k (Shift-Cmd-k on macOS): [`deleteLine`](https://codemirror.net/6/docs/ref/#commands.deleteLine)\n- Shift-Ctrl-\\\\ (Shift-Cmd-\\\\ on macOS): [`cursorMatchingBracket`](https://codemirror.net/6/docs/ref/#commands.cursorMatchingBracket)\n- Ctrl-/ (Cmd-/ on macOS): [`toggleComment`](https://codemirror.net/6/docs/ref/#commands.toggleComment).\n- Shift-Alt-a: [`toggleBlockComment`](https://codemirror.net/6/docs/ref/#commands.toggleBlockComment).\n- Ctrl-m (Alt-Shift-m on macOS): [`toggleTabFocusMode`](https://codemirror.net/6/docs/ref/#commands.toggleTabFocusMode).\n*/\nconst defaultKeymap = /*@__PURE__*/[\n    { key: \"Alt-ArrowLeft\", mac: \"Ctrl-ArrowLeft\", run: cursorSyntaxLeft, shift: selectSyntaxLeft },\n    { key: \"Alt-ArrowRight\", mac: \"Ctrl-ArrowRight\", run: cursorSyntaxRight, shift: selectSyntaxRight },\n    { key: \"Alt-ArrowUp\", run: moveLineUp },\n    { key: \"Shift-Alt-ArrowUp\", run: copyLineUp },\n    { key: \"Alt-ArrowDown\", run: moveLineDown },\n    { key: \"Shift-Alt-ArrowDown\", run: copyLineDown },\n    { key: \"Escape\", run: simplifySelection },\n    { key: \"Mod-Enter\", run: insertBlankLine },\n    { key: \"Alt-l\", mac: \"Ctrl-l\", run: selectLine },\n    { key: \"Mod-i\", run: selectParentSyntax, preventDefault: true },\n    { key: \"Mod-[\", run: indentLess },\n    { key: \"Mod-]\", run: indentMore },\n    { key: \"Mod-Alt-\\\\\", run: indentSelection },\n    { key: \"Shift-Mod-k\", run: deleteLine },\n    { key: \"Shift-Mod-\\\\\", run: cursorMatchingBracket },\n    { key: \"Mod-/\", run: toggleComment },\n    { key: \"Alt-A\", run: toggleBlockComment },\n    { key: \"Ctrl-m\", mac: \"Shift-Alt-m\", run: toggleTabFocusMode },\n].concat(standardKeymap);\n/**\nA binding that binds Tab to [`indentMore`](https://codemirror.net/6/docs/ref/#commands.indentMore) and\nShift-Tab to [`indentLess`](https://codemirror.net/6/docs/ref/#commands.indentLess).\nPlease see the [Tab example](../../examples/tab/) before using\nthis.\n*/\nconst indentWithTab = { key: \"Tab\", run: indentMore, shift: indentLess };\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+commands@6.8.1/node_modules/@codemirror/commands/dist/index.js\n");

/***/ })

};
;