"use client";

import { useState, useEffect, Suspense } from "react";
import { signIn, signUp, forgotPassword } from "./actions";
import { useSearchParams, useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import { useAuth } from "@/components/AuthProvider";
import { AuthForm, ForgotPasswordDialog, RegistrationSuccessView } from "@/components/auth";

function LoginContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, isLoading } = useAuth();
  const mode = searchParams.get("mode");
  const returnUrl = searchParams.get("returnUrl");
  const message = searchParams.get("message");

  const isSignUp = mode === 'signup';

  // Redirect if user is already logged in, checking isLoading state
  useEffect(() => {
    if (!isLoading && user) {
      router.push(returnUrl || '/dashboard');
    }
  }, [user, isLoading, router, returnUrl]);

  // Determine if message is a success message
  const isSuccessMessage = message && (
    message.includes("Check your email") ||
    message.includes("Account created") ||
    message.includes("success")
  );

  // Registration success state
  const [registrationSuccess, setRegistrationSuccess] = useState(!!isSuccessMessage);
  const [registrationEmail, setRegistrationEmail] = useState("");

  // Forgot password state
  const [forgotPasswordOpen, setForgotPasswordOpen] = useState(false);

  // Set registration success state from URL params
  useEffect(() => {
    if (isSuccessMessage) {
      setRegistrationSuccess(true);
    }
  }, [isSuccessMessage]);

  const handleSignIn = async (prevState: any, formData: FormData) => {
    if (returnUrl) {
      formData.append("returnUrl", returnUrl);
    } else {
      formData.append("returnUrl", "/dashboard");
    }
    const result = await signIn(prevState, formData);

    // Check for success and redirectTo properties
    if (result && typeof result === 'object' && 'success' in result && result.success && 'redirectTo' in result) {
      // Use window.location for hard navigation to avoid stale state
      window.location.href = result.redirectTo as string;
      return null; // Return null to prevent normal form action completion
    }

    return result;
  };

  const handleSignUp = async (prevState: any, formData: FormData) => {
    // Store email for success state
    const email = formData.get("email") as string;
    setRegistrationEmail(email);

    if (returnUrl) {
      formData.append("returnUrl", returnUrl);
    }

    // Add origin for email redirects
    formData.append("origin", window.location.origin);

    const result = await signUp(prevState, formData);

    // Check for success and redirectTo properties (direct login case)
    if (result && typeof result === 'object' && 'success' in result && result.success && 'redirectTo' in result) {
      // Use window.location for hard navigation to avoid stale state
      window.location.href = result.redirectTo as string;
      return null; // Return null to prevent normal form action completion
    }

    // Check if registration was successful but needs email verification
    if (result && typeof result === 'object' && 'message' in result) {
      const resultMessage = result.message as string;
      if (resultMessage.includes("Check your email")) {
        setRegistrationSuccess(true);

        // Update URL without causing a refresh
        const params = new URLSearchParams(window.location.search);
        params.set('message', resultMessage);

        const newUrl =
          window.location.pathname +
          (params.toString() ? '?' + params.toString() : '');

        window.history.pushState({ path: newUrl }, '', newUrl);

        return result;
      }
    }

    return result;
  };

  const handleForgotPassword = async (email: string) => {
    const formData = new FormData();
    formData.append("email", email);
    formData.append("origin", window.location.origin);

    return await forgotPassword(null, formData);
  };

  const resetRegistrationSuccess = () => {
    setRegistrationSuccess(false);
    // Remove message from URL and set mode to signin
    const params = new URLSearchParams(window.location.search);
    params.delete('message');
    params.set('mode', 'signin');

    const newUrl =
      window.location.pathname +
      (params.toString() ? '?' + params.toString() : '');

    window.history.pushState({ path: newUrl }, '', newUrl);

    router.refresh();
  };

  // Show loading spinner while checking auth state
  if (isLoading) {
    return (
      <main className="flex flex-col items-center justify-center min-h-screen w-full bg-[#1a1a1a]">
        <Loader2 className="h-12 w-12 animate-spin text-blue-500" />
      </main>
    );
  }

  // Registration success view
  if (registrationSuccess) {
    return (
      <RegistrationSuccessView
        email={registrationEmail}
        onBackToSignIn={resetRegistrationSuccess}
      />
    );
  }

  return (
    <>
      <AuthForm
        isSignUp={isSignUp}
        returnUrl={returnUrl}
        message={message}
        onSignIn={handleSignIn}
        onSignUp={handleSignUp}
        onForgotPassword={() => setForgotPasswordOpen(true)}
      />

      <ForgotPasswordDialog
        open={forgotPasswordOpen}
        onOpenChange={setForgotPasswordOpen}
        onSubmit={handleForgotPassword}
      />
    </>
  );
}

export default function Login() {
  return (
    <Suspense fallback={
      <main className="flex flex-col items-center justify-center min-h-screen w-full bg-[#1a1a1a]">
        <div className="w-12 h-12 rounded-full border-4 border-blue-500 border-t-transparent animate-spin"></div>
      </main>
    }>
      <LoginContent />
    </Suspense>
  );
}
