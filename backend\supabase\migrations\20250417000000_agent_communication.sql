-- AGENT COMMUNICATION SCHEMA

-- Create agents table to store agent information and capabilities
CREATE TABLE agents (
    agent_id UUID PRIMARY KEY,
    name TEXT,
    description TEXT,
    capabilities JSONB DEFAULT '{}'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create agent_messages table to store messages between agents
CREATE TABLE agent_messages (
    message_id UUID PRIMARY KEY,
    thread_id UUID NOT NULL,
    from_agent_id UUID NOT NULL REFERENCES agents(agent_id) ON DELETE CASCADE,
    to_agent_id UUID NOT NULL REFERENCES agents(agent_id) ON DELETE CASCADE,
    message_type TEXT NOT NULL,
    content JSONB NOT NULL,
    reference_id UUID,
    metadata JSONB DEFAULT '{}'::jsonb,
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create index on thread_id for faster message retrieval
CREATE INDEX agent_messages_thread_id_idx ON agent_messages(thread_id);

-- Create index on to_agent_id for faster message retrieval
CREATE INDEX agent_messages_to_agent_id_idx ON agent_messages(to_agent_id);

-- Create index on from_agent_id for faster message retrieval
CREATE INDEX agent_messages_from_agent_id_idx ON agent_messages(from_agent_id);

-- Create index on read status for faster unread message retrieval
CREATE INDEX agent_messages_read_idx ON agent_messages(read);

-- Create agent_teams table to store team information
CREATE TABLE agent_teams (
    team_id UUID PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create agent_team_members table to store team membership
CREATE TABLE agent_team_members (
    team_id UUID NOT NULL REFERENCES agent_teams(team_id) ON DELETE CASCADE,
    agent_id UUID NOT NULL REFERENCES agents(agent_id) ON DELETE CASCADE,
    role TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    PRIMARY KEY (team_id, agent_id)
);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_agent_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to update updated_at timestamp
CREATE TRIGGER update_agents_updated_at
BEFORE UPDATE ON agents
FOR EACH ROW
EXECUTE FUNCTION update_agent_updated_at();

CREATE TRIGGER update_agent_messages_updated_at
BEFORE UPDATE ON agent_messages
FOR EACH ROW
EXECUTE FUNCTION update_agent_updated_at();

CREATE TRIGGER update_agent_teams_updated_at
BEFORE UPDATE ON agent_teams
FOR EACH ROW
EXECUTE FUNCTION update_agent_updated_at();

-- Create function to get messages for an agent
CREATE OR REPLACE FUNCTION get_agent_messages(
    p_agent_id UUID,
    p_thread_id UUID DEFAULT NULL,
    p_unread_only BOOLEAN DEFAULT FALSE,
    p_limit INTEGER DEFAULT 100,
    p_offset INTEGER DEFAULT 0
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    messages_array JSONB;
BEGIN
    -- Build query based on parameters
    WITH filtered_messages AS (
        SELECT *
        FROM agent_messages
        WHERE to_agent_id = p_agent_id
        AND (p_thread_id IS NULL OR thread_id = p_thread_id)
        AND (p_unread_only = FALSE OR read = FALSE)
        ORDER BY created_at DESC
        LIMIT p_limit
        OFFSET p_offset
    )
    SELECT JSONB_AGG(row_to_json(filtered_messages))
    INTO messages_array
    FROM filtered_messages;
    
    -- Handle the case when no messages are found
    IF messages_array IS NULL THEN
        RETURN '[]'::JSONB;
    END IF;
    
    RETURN messages_array;
END;
$$;
