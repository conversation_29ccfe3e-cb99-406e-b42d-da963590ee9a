"""
Communication patterns for agent-to-agent interactions.

This module defines structured communication patterns that agents can use
to collaborate effectively, including task delegation, information sharing,
and consensus building.
"""

from typing import Dict, List, Any, Optional
from enum import Enum
import json

from utils.logger import logger


class CommunicationPattern(Enum):
    """Types of communication patterns that agents can use."""
    TASK_DELEGATION = "task_delegation"
    INFORMATION_REQUEST = "information_request"
    STATUS_UPDATE = "status_update"
    DECISION_REQUEST = "decision_request"
    FEEDBACK_REQUEST = "feedback_request"
    COLLABORATION_INVITATION = "collaboration_invitation"


class TaskPriority(Enum):
    """Priority levels for delegated tasks."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class TaskStatus(Enum):
    """Status values for delegated tasks."""
    PENDING = "pending"
    ACCEPTED = "accepted"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    REJECTED = "rejected"
    BLOCKED = "blocked"
    CANCELLED = "cancelled"


def create_task_delegation_message(
    task_title: str,
    task_description: str,
    priority: TaskPriority = TaskPriority.MEDIUM,
    due_date: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None,
    deliverables: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Create a structured task delegation message.
    
    Args:
        task_title: Brief title for the task
        task_description: Detailed description of the task
        priority: Priority level for the task
        due_date: Optional due date in ISO format
        context: Optional additional context for the task
        deliverables: Optional list of expected deliverables
        
    Returns:
        Structured message content for task delegation
    """
    return {
        "pattern": CommunicationPattern.TASK_DELEGATION.value,
        "task": {
            "title": task_title,
            "description": task_description,
            "priority": priority.value,
            "status": TaskStatus.PENDING.value,
            "due_date": due_date,
            "context": context or {},
            "deliverables": deliverables or []
        }
    }


def create_task_response_message(
    task_id: str,
    status: TaskStatus,
    message: str,
    estimated_completion: Optional[str] = None,
    blockers: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Create a structured response to a task delegation.
    
    Args:
        task_id: ID of the task being responded to
        status: New status for the task
        message: Message explaining the response
        estimated_completion: Optional estimated completion date in ISO format
        blockers: Optional list of blockers if the task is blocked
        
    Returns:
        Structured message content for task response
    """
    return {
        "pattern": CommunicationPattern.TASK_DELEGATION.value,
        "task_response": {
            "task_id": task_id,
            "status": status.value,
            "message": message,
            "estimated_completion": estimated_completion,
            "blockers": blockers or []
        }
    }


def create_information_request_message(
    question: str,
    context: Optional[Dict[str, Any]] = None,
    priority: TaskPriority = TaskPriority.MEDIUM
) -> Dict[str, Any]:
    """Create a structured information request message.
    
    Args:
        question: The question or information being requested
        context: Optional additional context for the request
        priority: Priority level for the request
        
    Returns:
        Structured message content for information request
    """
    return {
        "pattern": CommunicationPattern.INFORMATION_REQUEST.value,
        "request": {
            "question": question,
            "context": context or {},
            "priority": priority.value
        }
    }


def create_information_response_message(
    request_id: str,
    answer: str,
    sources: Optional[List[str]] = None,
    additional_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Create a structured response to an information request.
    
    Args:
        request_id: ID of the request being responded to
        answer: The answer to the question
        sources: Optional list of sources for the information
        additional_context: Optional additional context for the answer
        
    Returns:
        Structured message content for information response
    """
    return {
        "pattern": CommunicationPattern.INFORMATION_REQUEST.value,
        "response": {
            "request_id": request_id,
            "answer": answer,
            "sources": sources or [],
            "additional_context": additional_context or {}
        }
    }


def create_status_update_message(
    status: str,
    details: str,
    progress: Optional[float] = None,
    next_steps: Optional[List[str]] = None,
    blockers: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Create a structured status update message.
    
    Args:
        status: Brief status summary
        details: Detailed status description
        progress: Optional progress percentage (0-100)
        next_steps: Optional list of next steps
        blockers: Optional list of blockers
        
    Returns:
        Structured message content for status update
    """
    return {
        "pattern": CommunicationPattern.STATUS_UPDATE.value,
        "update": {
            "status": status,
            "details": details,
            "progress": progress,
            "next_steps": next_steps or [],
            "blockers": blockers or []
        }
    }


def create_decision_request_message(
    question: str,
    options: List[Dict[str, Any]],
    context: Optional[Dict[str, Any]] = None,
    deadline: Optional[str] = None,
    priority: TaskPriority = TaskPriority.MEDIUM
) -> Dict[str, Any]:
    """Create a structured decision request message.
    
    Args:
        question: The decision question
        options: List of options with their pros and cons
        context: Optional additional context for the decision
        deadline: Optional deadline for the decision in ISO format
        priority: Priority level for the request
        
    Returns:
        Structured message content for decision request
    """
    return {
        "pattern": CommunicationPattern.DECISION_REQUEST.value,
        "request": {
            "question": question,
            "options": options,
            "context": context or {},
            "deadline": deadline,
            "priority": priority.value
        }
    }


def create_decision_response_message(
    request_id: str,
    decision: str,
    rationale: str,
    alternative_options: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Create a structured response to a decision request.
    
    Args:
        request_id: ID of the request being responded to
        decision: The decision made
        rationale: Rationale for the decision
        alternative_options: Optional list of alternative options considered
        
    Returns:
        Structured message content for decision response
    """
    return {
        "pattern": CommunicationPattern.DECISION_REQUEST.value,
        "response": {
            "request_id": request_id,
            "decision": decision,
            "rationale": rationale,
            "alternative_options": alternative_options or []
        }
    }


def create_feedback_request_message(
    item: str,
    description: str,
    specific_questions: Optional[List[str]] = None,
    context: Optional[Dict[str, Any]] = None,
    deadline: Optional[str] = None
) -> Dict[str, Any]:
    """Create a structured feedback request message.
    
    Args:
        item: The item feedback is requested for
        description: Description of the item
        specific_questions: Optional list of specific questions
        context: Optional additional context for the request
        deadline: Optional deadline for the feedback in ISO format
        
    Returns:
        Structured message content for feedback request
    """
    return {
        "pattern": CommunicationPattern.FEEDBACK_REQUEST.value,
        "request": {
            "item": item,
            "description": description,
            "specific_questions": specific_questions or [],
            "context": context or {},
            "deadline": deadline
        }
    }


def create_feedback_response_message(
    request_id: str,
    feedback: str,
    specific_answers: Optional[Dict[str, str]] = None,
    suggestions: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Create a structured response to a feedback request.
    
    Args:
        request_id: ID of the request being responded to
        feedback: General feedback
        specific_answers: Optional answers to specific questions
        suggestions: Optional list of suggestions for improvement
        
    Returns:
        Structured message content for feedback response
    """
    return {
        "pattern": CommunicationPattern.FEEDBACK_REQUEST.value,
        "response": {
            "request_id": request_id,
            "feedback": feedback,
            "specific_answers": specific_answers or {},
            "suggestions": suggestions or []
        }
    }


def create_collaboration_invitation_message(
    project: str,
    description: str,
    roles: Dict[str, str],
    timeline: Optional[Dict[str, str]] = None,
    context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Create a structured collaboration invitation message.
    
    Args:
        project: Name of the project
        description: Description of the project
        roles: Dictionary mapping agent IDs to their roles
        timeline: Optional timeline for the project
        context: Optional additional context for the invitation
        
    Returns:
        Structured message content for collaboration invitation
    """
    return {
        "pattern": CommunicationPattern.COLLABORATION_INVITATION.value,
        "invitation": {
            "project": project,
            "description": description,
            "roles": roles,
            "timeline": timeline or {},
            "context": context or {}
        }
    }


def create_collaboration_response_message(
    invitation_id: str,
    accepted: bool,
    message: str,
    availability: Optional[Dict[str, str]] = None,
    constraints: Optional[List[str]] = None
) -> Dict[str, Any]:
    """Create a structured response to a collaboration invitation.
    
    Args:
        invitation_id: ID of the invitation being responded to
        accepted: Whether the invitation is accepted
        message: Message explaining the response
        availability: Optional availability information
        constraints: Optional list of constraints
        
    Returns:
        Structured message content for collaboration response
    """
    return {
        "pattern": CommunicationPattern.COLLABORATION_INVITATION.value,
        "response": {
            "invitation_id": invitation_id,
            "accepted": accepted,
            "message": message,
            "availability": availability or {},
            "constraints": constraints or []
        }
    }


def parse_communication_pattern(content: Dict[str, Any]) -> Optional[CommunicationPattern]:
    """Parse the communication pattern from a message content.
    
    Args:
        content: Message content to parse
        
    Returns:
        Communication pattern or None if not found
    """
    try:
        pattern_str = content.get("pattern")
        if pattern_str:
            return CommunicationPattern(pattern_str)
        return None
    except (ValueError, AttributeError):
        logger.warning(f"Failed to parse communication pattern from content: {content}")
        return None


def get_role_specific_communication_guidance(role: str) -> Dict[str, Any]:
    """Get role-specific communication guidance.
    
    Args:
        role: Role of the agent
        
    Returns:
        Dictionary with role-specific communication guidance
    """
    # Define role-specific communication guidance
    guidance = {
        "CEO": {
            "communication_style": "Direct, strategic, and focused on outcomes",
            "preferred_patterns": [
                CommunicationPattern.TASK_DELEGATION.value,
                CommunicationPattern.DECISION_REQUEST.value,
                CommunicationPattern.STATUS_UPDATE.value
            ],
            "guidance": [
                "Be clear and concise in your communications",
                "Focus on strategic outcomes rather than tactical details",
                "Delegate tasks to the appropriate team members based on their expertise",
                "Request regular status updates on delegated tasks",
                "Make decisions based on the information provided by your team"
            ]
        },
        "Developer": {
            "communication_style": "Technical, precise, and solution-oriented",
            "preferred_patterns": [
                CommunicationPattern.INFORMATION_REQUEST.value,
                CommunicationPattern.STATUS_UPDATE.value,
                CommunicationPattern.FEEDBACK_REQUEST.value
            ],
            "guidance": [
                "Provide technical details in a clear and structured manner",
                "Ask specific questions when requesting information",
                "Provide regular status updates on development tasks",
                "Request feedback on technical solutions",
                "Clearly communicate technical constraints and requirements"
            ]
        },
        "Marketing": {
            "communication_style": "Creative, persuasive, and audience-focused",
            "preferred_patterns": [
                CommunicationPattern.FEEDBACK_REQUEST.value,
                CommunicationPattern.COLLABORATION_INVITATION.value,
                CommunicationPattern.INFORMATION_REQUEST.value
            ],
            "guidance": [
                "Focus on the audience and their needs in your communications",
                "Use persuasive language to convey marketing messages",
                "Request feedback on marketing materials and campaigns",
                "Collaborate with other team members to align marketing with product",
                "Request information about product features and benefits"
            ]
        },
        "Product": {
            "communication_style": "User-focused, analytical, and collaborative",
            "preferred_patterns": [
                CommunicationPattern.DECISION_REQUEST.value,
                CommunicationPattern.TASK_DELEGATION.value,
                CommunicationPattern.FEEDBACK_REQUEST.value
            ],
            "guidance": [
                "Focus on user needs and experiences in your communications",
                "Use data and analytics to support product decisions",
                "Request feedback on product features and roadmaps",
                "Delegate tasks to the appropriate team members",
                "Collaborate with other team members to align product with business goals"
            ]
        },
        "Sales": {
            "communication_style": "Persuasive, relationship-focused, and results-driven",
            "preferred_patterns": [
                CommunicationPattern.INFORMATION_REQUEST.value,
                CommunicationPattern.STATUS_UPDATE.value,
                CommunicationPattern.COLLABORATION_INVITATION.value
            ],
            "guidance": [
                "Focus on customer needs and pain points in your communications",
                "Use persuasive language to convey value propositions",
                "Request information about product features and benefits",
                "Provide regular status updates on sales activities",
                "Collaborate with other team members to align sales with marketing"
            ]
        },
        "Finance": {
            "communication_style": "Analytical, precise, and data-driven",
            "preferred_patterns": [
                CommunicationPattern.INFORMATION_REQUEST.value,
                CommunicationPattern.STATUS_UPDATE.value,
                CommunicationPattern.DECISION_REQUEST.value
            ],
            "guidance": [
                "Provide financial data in a clear and structured manner",
                "Use precise language when discussing financial matters",
                "Request information about business activities that impact finances",
                "Provide regular status updates on financial performance",
                "Request decisions on financial matters with clear options and implications"
            ]
        },
        "Designer": {
            "communication_style": "Visual, creative, and user-centered",
            "preferred_patterns": [
                CommunicationPattern.FEEDBACK_REQUEST.value,
                CommunicationPattern.INFORMATION_REQUEST.value,
                CommunicationPattern.COLLABORATION_INVITATION.value
            ],
            "guidance": [
                "Focus on visual and user experience aspects in your communications",
                "Use visual examples to convey design concepts",
                "Request feedback on design concepts and prototypes",
                "Request information about user needs and preferences",
                "Collaborate with other team members to align design with product and marketing"
            ]
        },
        "Research": {
            "communication_style": "Analytical, thorough, and objective",
            "preferred_patterns": [
                CommunicationPattern.INFORMATION_REQUEST.value,
                CommunicationPattern.STATUS_UPDATE.value,
                CommunicationPattern.FEEDBACK_REQUEST.value
            ],
            "guidance": [
                "Provide research findings in a clear and structured manner",
                "Use data and evidence to support research conclusions",
                "Request information about research needs and priorities",
                "Provide regular status updates on research activities",
                "Request feedback on research methodologies and findings"
            ]
        }
    }
    
    return guidance.get(role, {
        "communication_style": "Professional and collaborative",
        "preferred_patterns": [p.value for p in CommunicationPattern],
        "guidance": [
            "Be clear and concise in your communications",
            "Focus on the specific needs of the recipient",
            "Provide context for your requests and updates",
            "Be respectful and professional in all communications",
            "Follow up on outstanding requests and tasks"
        ]
    })
