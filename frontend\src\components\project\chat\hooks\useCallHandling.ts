import { useState, useMemo } from 'react';
import { Message, CustomGroup, CallParticipant } from '../types';

export function useCallHandling(
  activeChat: string,
  customGroups: CustomGroup[],
  setCurrentMessages?: (messages: Message[] | ((prev: Message[]) => Message[])) => void,
  messagesContainerRef?: React.RefObject<HTMLDivElement>
) {
  // Call state
  const [isCallActive, setIsCallActive] = useState(false);
  const [callType, setCallType] = useState<'audio' | 'video'>('audio');
  const [callParticipants, setCallParticipants] = useState<CallParticipant[]>([]);
  const [showFullScreenCall, setShowFullScreenCall] = useState(false);
  const [callStartTime, setCallStartTime] = useState<Date | null>(null);
  const [isCallMinimized, setIsCallMinimized] = useState(false);

  // Call participants based on active chat
  const callParticipantsMemo = useMemo(() => {
    if (activeChat === 'ceo') {
      return [{
        id: '1',
        name: '<PERSON><PERSON>',
        avatar: '/roles/kenard.png',
        role: 'CEO',
        isMuted: false,
        isVideoOn: true,
        isSpeaking: Math.random() > 0.7
      }];
    } else if (activeChat === 'developer') {
      return [{
        id: '2',
        name: 'Alex',
        avatar: '/roles/alex.png',
        role: 'Developer',
        isMuted: false,
        isVideoOn: true,
        isSpeaking: Math.random() > 0.7
      }];
    } else {
      return [
        {
          id: '1',
          name: 'Kenard',
          avatar: '/roles/kenard.png',
          role: 'CEO',
          isMuted: false,
          isVideoOn: true,
          isSpeaking: Math.random() > 0.8
        },
        {
          id: '2',
          name: 'Alex',
          avatar: '/roles/alex.png',
          role: 'Developer',
          isMuted: Math.random() > 0.5,
          isVideoOn: true,
          isSpeaking: Math.random() > 0.8
        },
        {
          id: '3',
          name: 'Chloe',
          avatar: '/roles/chloe.png',
          role: 'Marketing',
          isMuted: false,
          isVideoOn: Math.random() > 0.5,
          isSpeaking: Math.random() > 0.8
        }
      ];
    }
  }, [activeChat]);

  // Start a call with the current chat
  const startCall = (type: 'audio' | 'video') => {
    setCallType(type);
    setIsCallActive(true);
    setCallStartTime(new Date()); // Record when the call started

    // Set participants based on active chat
    setCallParticipants(callParticipantsMemo);

    // Show full-screen call UI
    setShowFullScreenCall(true);
    setIsCallMinimized(false);

    // Don't add a start message - we'll only add one message when the call ends
  };

  // Minimize call (leave full-screen but stay connected)
  const minimizeCall = () => {
    setShowFullScreenCall(false);
    setIsCallMinimized(true);
  };

  // Maximize call (return to full-screen)
  const maximizeCall = () => {
    setShowFullScreenCall(true);
    setIsCallMinimized(false);
  };

  // End call function
  const endCall = () => {
    // Hide full-screen call UI first
    setShowFullScreenCall(false);
    setIsCallActive(false);
    setIsCallMinimized(false);

    // Calculate actual call duration
    const calculateCallDuration = () => {
      if (!callStartTime) return '0 seconds';

      const endTime = new Date();
      const durationMs = endTime.getTime() - callStartTime.getTime();
      const durationMinutes = Math.floor(durationMs / (1000 * 60));
      const durationSeconds = Math.floor((durationMs % (1000 * 60)) / 1000);

      // For very short calls (less than 5 seconds), show "a few seconds"
      if (durationMinutes === 0 && durationSeconds < 5) {
        return 'a few seconds';
      } else if (durationMinutes === 0) {
        return `${durationSeconds} second${durationSeconds !== 1 ? 's' : ''}`;
      } else if (durationSeconds === 0) {
        return `${durationMinutes} minute${durationMinutes !== 1 ? 's' : ''}`;
      } else {
        return `${durationMinutes} minute${durationMinutes !== 1 ? 's' : ''} and ${durationSeconds} second${durationSeconds !== 1 ? 's' : ''}`;
      }
    };

    // Get the agent's name based on active chat
    const getAgentName = () => {
      if (activeChat === 'ceo') return 'Kenard (CEO)';
      if (activeChat === 'developer') return 'Alex (Developer)';
      if (activeChat === 'group') return 'the team';

      // For custom groups
      const customGroup = customGroups.find(group => group.id === activeChat);
      if (customGroup) {
        if (customGroup.participants.length === 1) {
          const participant = customGroup.participants[0];
          return `${participant.name} (${participant.role})`;
        } else {
          return customGroup.name;
        }
      }
      return 'unknown agent';
    };

    const duration = calculateCallDuration();
    const agentName = getAgentName();

    // Add system message for call end with actual agent name and duration
    const endCallMessage: Message = {
      id: `call-end-${Date.now()}`,
      content: `You started a call with ${agentName} that lasted ${duration}.`,
      sender: 'system',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      isSystem: true,
      isCall: true,
      callType: callType,
      callStatus: 'ended',
      callDuration: duration,
      calledPerson: agentName
    };

    if (setCurrentMessages) {
      setCurrentMessages(prev => [...prev, endCallMessage]);
    }

    // Reset call state
    setCallParticipants([]);
    setCallStartTime(null);

    // Scroll to bottom of messages
    setTimeout(() => {
      if (messagesContainerRef?.current) {
        messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
      }
    }, 100);
  };

  return {
    isCallActive,
    callType,
    callParticipants: callParticipantsMemo,
    showFullScreenCall,
    isCallMinimized,
    callStartTime,
    startCall,
    endCall,
    minimizeCall,
    maximizeCall
  };
}
