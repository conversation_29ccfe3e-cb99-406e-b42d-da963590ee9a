from typing import Dict, Any, Optional, List, Union
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Request, Response
import json

from utils.auth_utils import get_user_id, get_account_id_from_project
from utils.supabase import get_supabase_client
from utils.logger import logger
from integrations.manager import IntegrationManager
from integrations import registry

router = APIRouter()

@router.get("/integrations/available")
async def get_available_integrations():
    integrations = registry.get_all_integrations()
    
    available_integrations = []
    for name, integration_class in integrations.items():
        available_integrations.append({
            "name": name,
            "display_name": integration_class.display_name,
            "description": integration_class.description
        })
    
    return {"integrations": available_integrations}

@router.get("/accounts/{account_id}/integrations")
async def get_account_integrations(account_id: str, request: Request):
    client = get_supabase_client(request)
    user_id = await get_user_id(client)
    
    if not user_id:
        raise HTTPException(status_code=401, detail="Unauthorized")
    
    integration_manager = IntegrationManager(client)
    integrations = await integration_manager.get_account_integrations(account_id)
    
    return {"integrations": integrations}

@router.post("/accounts/{account_id}/integrations")
async def create_integration(
    account_id: str, 
    request: Request,
    integration_data: Dict[str, Any]
):
    client = get_supabase_client(request)
    user_id = await get_user_id(client)
    
    if not user_id:
        raise HTTPException(status_code=401, detail="Unauthorized")
    
    integration_type = integration_data.get("integration_type")
    config = integration_data.get("config", {})
    integration_mode = integration_data.get("integration_mode", "company")
    
    if not integration_type:
        raise HTTPException(status_code=400, detail="Integration type is required")
    
    integration_manager = IntegrationManager(client)
    integration_id = await integration_manager.create_integration(
        integration_type=integration_type,
        account_id=account_id,
        user_id=user_id,
        config=config,
        integration_mode=integration_mode
    )
    
    if not integration_id:
        raise HTTPException(status_code=500, detail="Failed to create integration")
    
    return {"integration_id": integration_id}

@router.put("/integrations/{integration_id}")
async def update_integration(
    integration_id: str, 
    request: Request,
    integration_data: Dict[str, Any]
):
    client = get_supabase_client(request)
    user_id = await get_user_id(client)
    
    if not user_id:
        raise HTTPException(status_code=401, detail="Unauthorized")
    
    config = integration_data.get("config", {})
    
    integration_manager = IntegrationManager(client)
    success = await integration_manager.update_integration(
        integration_id=integration_id,
        config=config
    )
    
    if not success:
        raise HTTPException(status_code=500, detail="Failed to update integration")
    
    return {"success": True}

@router.delete("/integrations/{integration_id}")
async def delete_integration(integration_id: str, request: Request):
    client = get_supabase_client(request)
    user_id = await get_user_id(client)
    
    if not user_id:
        raise HTTPException(status_code=401, detail="Unauthorized")
    
    integration_manager = IntegrationManager(client)
    success = await integration_manager.delete_integration(integration_id)
    
    if not success:
        raise HTTPException(status_code=500, detail="Failed to delete integration")
    
    return {"success": True}

@router.post("/integrations/{integration_id}/actions/{action}")
async def execute_integration_action(
    integration_id: str, 
    action: str,
    request: Request,
    params: Dict[str, Any]
):
    client = get_supabase_client(request)
    user_id = await get_user_id(client)
    
    if not user_id:
        raise HTTPException(status_code=401, detail="Unauthorized")
    
    integration_manager = IntegrationManager(client)
    result = await integration_manager.execute_integration_action(
        integration_id=integration_id,
        action=action,
        params=params
    )
    
    if not result.get("success", False):
        raise HTTPException(status_code=500, detail=result.get("error", "Failed to execute action"))
    
    return result

@router.post("/integrations/webhooks/{integration_id}")
async def handle_integration_webhook(
    integration_id: str,
    request: Request,
    background_tasks: BackgroundTasks
):
    client = get_supabase_client(request)
    
    # Get the request body
    body = await request.body()
    payload = json.loads(body)
    
    # Process the webhook asynchronously
    background_tasks.add_task(process_webhook, client, integration_id, payload)
    
    return {"success": True, "message": "Webhook received"}

async def process_webhook(client, integration_id: str, payload: Dict[str, Any]):
    try:
        integration_manager = IntegrationManager(client)
        integration = await integration_manager.get_integration(integration_id)
        
        if not integration:
            logger.error(f"Integration {integration_id} not found for webhook")
            return
            
        result = await integration.handle_webhook(payload)
        
        if not result.get("success", False):
            logger.error(f"Error processing webhook for integration {integration_id}: {result.get('error')}")
        else:
            logger.info(f"Webhook processed successfully for integration {integration_id}")
            
    except Exception as e:
        logger.error(f"Error processing webhook: {str(e)}")
