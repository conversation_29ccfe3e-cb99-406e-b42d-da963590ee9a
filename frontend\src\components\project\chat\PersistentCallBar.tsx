'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import {
  Mic,
  MicOff,
  Video,
  VideoOff,
  Phone,
  PhoneOff,
  Maximize2,
  Settings,
  Volume2,
  VolumeX
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { CallParticipant } from './types';
import { useSidebar } from '@/components/ui/sidebar';

interface PersistentCallBarProps {
  isCallActive: boolean;
  callType: 'audio' | 'video';
  callParticipants: CallParticipant[];
  callStartTime: Date | null;
  onMaximize: () => void;
  onEndCall: () => void;
  className?: string;
}

export function PersistentCallBar({
  isCallActive,
  callType,
  callParticipants,
  callStartTime,
  onMaximize,
  onEndCall,
  className
}: PersistentCallBarProps) {
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOn, setIsVideoOn] = useState(callType === 'video');
  const [isSpeakerMuted, setIsSpeakerMuted] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const { state } = useSidebar();

  // Update call duration
  useEffect(() => {
    if (!isCallActive || !callStartTime) return;

    const interval = setInterval(() => {
      const now = new Date();
      const duration = Math.floor((now.getTime() - callStartTime.getTime()) / 1000);
      setCallDuration(duration);
    }, 1000);

    return () => clearInterval(interval);
  }, [isCallActive, callStartTime]);

  // Format call duration as MM:SS
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Get call display info
  const getCallInfo = () => {
    if (callParticipants.length === 1) {
      const participant = callParticipants[0];
      return {
        name: participant.name,
        avatar: participant.avatar,
        subtitle: participant.role
      };
    } else if (callParticipants.length > 1) {
      return {
        name: 'Team Chat',
        avatar: null,
        subtitle: `${callParticipants.length} participants`
      };
    }
    return {
      name: 'Unknown',
      avatar: null,
      subtitle: 'Call'
    };
  };

  if (!isCallActive) return null;

  const callInfo = getCallInfo();

  return (
    <div
      className={cn(
        "persistent-call-bar fixed bottom-6 left-6 z-50 bg-card border border-border rounded-md shadow-xl",
        "backdrop-blur-sm transition-all duration-200 ease-linear max-w-sm",
        className
      )}
    >
      {/* Call Header */}
      <div className="flex items-center justify-between p-3 border-b border-border">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          <span className="text-foreground text-sm font-medium">
            {callType === 'video' ? 'Video Call' : 'Voice Call'}
          </span>
        </div>
        <div className="text-muted-foreground text-sm">
          {formatDuration(callDuration)}
        </div>
      </div>

      {/* Participants */}
      <div className="p-3">
        <div className="flex items-center gap-3 mb-4">
          {callInfo.avatar ? (
            <div className="w-10 h-10 rounded-full overflow-hidden">
              <Image
                src={callInfo.avatar}
                alt={callInfo.name}
                width={40}
                height={40}
                className="w-full h-full object-cover"
              />
            </div>
          ) : (
            <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center text-primary-foreground text-sm font-medium">
              {callInfo.name.charAt(0)}
            </div>
          )}

          <div className="flex-1">
            <div className="text-foreground text-sm font-medium">{callInfo.name}</div>
            <div className="text-muted-foreground text-xs">{callInfo.subtitle}</div>
          </div>

          <button
            onClick={onMaximize}
            className="p-2 hover:bg-accent rounded-md transition-colors"
          >
            <Maximize2 className="h-4 w-4 text-muted-foreground" />
          </button>
        </div>

        {/* Call Controls */}
        <div className="flex items-center justify-center gap-2">
          <Button
            size="sm"
            variant="ghost"
            onClick={() => setIsMuted(!isMuted)}
            className={cn(
              "h-10 w-10 p-0 rounded-full",
              isMuted
                ? "bg-destructive/20 text-destructive hover:bg-destructive/30"
                : "bg-muted text-muted-foreground hover:bg-accent hover:text-foreground"
            )}
          >
            {isMuted ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
          </Button>

          {callType === 'video' && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsVideoOn(!isVideoOn)}
              className={cn(
                "h-10 w-10 p-0 rounded-full",
                !isVideoOn
                  ? "bg-destructive/20 text-destructive hover:bg-destructive/30"
                  : "bg-muted text-muted-foreground hover:bg-accent hover:text-foreground"
              )}
            >
              {isVideoOn ? <Video className="h-4 w-4" /> : <VideoOff className="h-4 w-4" />}
            </Button>
          )}

          <Button
            size="sm"
            variant="ghost"
            onClick={() => setIsSpeakerMuted(!isSpeakerMuted)}
            className={cn(
              "h-10 w-10 p-0 rounded-full",
              isSpeakerMuted
                ? "bg-destructive/20 text-destructive hover:bg-destructive/30"
                : "bg-muted text-muted-foreground hover:bg-accent hover:text-foreground"
            )}
          >
            {isSpeakerMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
          </Button>

          <Button
            size="sm"
            variant="ghost"
            onClick={onEndCall}
            className="h-10 w-10 p-0 rounded-full bg-destructive/20 text-destructive hover:bg-destructive/30"
          >
            <PhoneOff className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
