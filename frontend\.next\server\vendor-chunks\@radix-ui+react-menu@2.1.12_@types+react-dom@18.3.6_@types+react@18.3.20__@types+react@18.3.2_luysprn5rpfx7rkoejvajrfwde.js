"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-menu@2.1.12_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18.3.2_luysprn5rpfx7rkoejvajrfwde";
exports.ids = ["vendor-chunks/@radix-ui+react-menu@2.1.12_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18.3.2_luysprn5rpfx7rkoejvajrfwde"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.12_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18.3.2_luysprn5rpfx7rkoejvajrfwde/node_modules/@radix-ui/react-menu/dist/index.mjs":
/*!******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-menu@2.1.12_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18.3.2_luysprn5rpfx7rkoejvajrfwde/node_modules/@radix-ui/react-menu/dist/index.mjs ***!
  \******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Anchor: () => (/* binding */ Anchor2),\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   CheckboxItem: () => (/* binding */ CheckboxItem),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Item: () => (/* binding */ Item2),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Menu: () => (/* binding */ Menu),\n/* harmony export */   MenuAnchor: () => (/* binding */ MenuAnchor),\n/* harmony export */   MenuArrow: () => (/* binding */ MenuArrow),\n/* harmony export */   MenuCheckboxItem: () => (/* binding */ MenuCheckboxItem),\n/* harmony export */   MenuContent: () => (/* binding */ MenuContent),\n/* harmony export */   MenuGroup: () => (/* binding */ MenuGroup),\n/* harmony export */   MenuItem: () => (/* binding */ MenuItem),\n/* harmony export */   MenuItemIndicator: () => (/* binding */ MenuItemIndicator),\n/* harmony export */   MenuLabel: () => (/* binding */ MenuLabel),\n/* harmony export */   MenuPortal: () => (/* binding */ MenuPortal),\n/* harmony export */   MenuRadioGroup: () => (/* binding */ MenuRadioGroup),\n/* harmony export */   MenuRadioItem: () => (/* binding */ MenuRadioItem),\n/* harmony export */   MenuSeparator: () => (/* binding */ MenuSeparator),\n/* harmony export */   MenuSub: () => (/* binding */ MenuSub),\n/* harmony export */   MenuSubContent: () => (/* binding */ MenuSubContent),\n/* harmony export */   MenuSubTrigger: () => (/* binding */ MenuSubTrigger),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   RadioItem: () => (/* binding */ RadioItem),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Sub: () => (/* binding */ Sub),\n/* harmony export */   SubContent: () => (/* binding */ SubContent),\n/* harmony export */   SubTrigger: () => (/* binding */ SubTrigger),\n/* harmony export */   createMenuScope: () => (/* binding */ createMenuScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@1.1.4_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@1_iqqjhdcykw7osrsefnv7uedvz4/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.20_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.20_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1.1.1_@types+react@18.3.20_react@18.3.1/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.7_@types+react-dom@18.3.6_@types+react@18.3.20__@types+_ivm2noauh3nylye65gakoowrf4/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.2_@types+react@18.3.20_react@18.3.1/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.4_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@_zy4cr4mqefdv2inektj755jdre/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.20_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.4_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18.3._s45s625mh5lxv6oopwi45m4blu/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.6_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18.3._c62wyq6kw32ar3cpewmh56ub3u/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.4_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18._7eopujavluuxwj4zqzlva37e5e/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.0_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18_zeyypm4rlhlmbul7tazrcty7cm/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-roving-focus */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focus@1.1.7_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react_kmuh47xh7hk2p4zpn6mibqklie/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.0_@types+react@18.3.20_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.20_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.4/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.6.3_@types+react@18.3.20_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Anchor,Arrow,CheckboxItem,Content,Group,Item,ItemIndicator,Label,Menu,MenuAnchor,MenuArrow,MenuCheckboxItem,MenuContent,MenuGroup,MenuItem,MenuItemIndicator,MenuLabel,MenuPortal,MenuRadioGroup,MenuRadioItem,MenuSeparator,MenuSub,MenuSubContent,MenuSubTrigger,Portal,RadioGroup,RadioItem,Root,Separator,Sub,SubContent,SubTrigger,createMenuScope auto */ // src/menu.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar SELECTION_KEYS = [\n    \"Enter\",\n    \" \"\n];\nvar FIRST_KEYS = [\n    \"ArrowDown\",\n    \"PageUp\",\n    \"Home\"\n];\nvar LAST_KEYS = [\n    \"ArrowUp\",\n    \"PageDown\",\n    \"End\"\n];\nvar FIRST_LAST_KEYS = [\n    ...FIRST_KEYS,\n    ...LAST_KEYS\n];\nvar SUB_OPEN_KEYS = {\n    ltr: [\n        ...SELECTION_KEYS,\n        \"ArrowRight\"\n    ],\n    rtl: [\n        ...SELECTION_KEYS,\n        \"ArrowLeft\"\n    ]\n};\nvar SUB_CLOSE_KEYS = {\n    ltr: [\n        \"ArrowLeft\"\n    ],\n    rtl: [\n        \"ArrowRight\"\n    ]\n};\nvar MENU_NAME = \"Menu\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(MENU_NAME);\nvar [createMenuContext, createMenuScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(MENU_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.createPopperScope,\n    _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.createRovingFocusGroupScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.createPopperScope)();\nvar useRovingFocusGroupScope = (0,_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.createRovingFocusGroupScope)();\nvar [MenuProvider, useMenuContext] = createMenuContext(MENU_NAME);\nvar [MenuRootProvider, useMenuRootContext] = createMenuContext(MENU_NAME);\nvar Menu = (props)=>{\n    const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const isUsingKeyboardRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleOpenChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onOpenChange);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__.useDirection)(dir);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Menu.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"Menu.useEffect.handleKeyDown\": ()=>{\n                    isUsingKeyboardRef.current = true;\n                    document.addEventListener(\"pointerdown\", handlePointer, {\n                        capture: true,\n                        once: true\n                    });\n                    document.addEventListener(\"pointermove\", handlePointer, {\n                        capture: true,\n                        once: true\n                    });\n                }\n            }[\"Menu.useEffect.handleKeyDown\"];\n            const handlePointer = {\n                \"Menu.useEffect.handlePointer\": ()=>isUsingKeyboardRef.current = false\n            }[\"Menu.useEffect.handlePointer\"];\n            document.addEventListener(\"keydown\", handleKeyDown, {\n                capture: true\n            });\n            return ({\n                \"Menu.useEffect\": ()=>{\n                    document.removeEventListener(\"keydown\", handleKeyDown, {\n                        capture: true\n                    });\n                    document.removeEventListener(\"pointerdown\", handlePointer, {\n                        capture: true\n                    });\n                    document.removeEventListener(\"pointermove\", handlePointer, {\n                        capture: true\n                    });\n                }\n            })[\"Menu.useEffect\"];\n        }\n    }[\"Menu.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuProvider, {\n            scope: __scopeMenu,\n            open,\n            onOpenChange: handleOpenChange,\n            content,\n            onContentChange: setContent,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootProvider, {\n                scope: __scopeMenu,\n                onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n                    \"Menu.useCallback\": ()=>handleOpenChange(false)\n                }[\"Menu.useCallback\"], [\n                    handleOpenChange\n                ]),\n                isUsingKeyboardRef,\n                dir: direction,\n                modal,\n                children\n            })\n        })\n    });\n};\nMenu.displayName = MENU_NAME;\nvar ANCHOR_NAME = \"MenuAnchor\";\nvar MenuAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Anchor, {\n        ...popperScope,\n        ...anchorProps,\n        ref: forwardedRef\n    });\n});\nMenuAnchor.displayName = ANCHOR_NAME;\nvar PORTAL_NAME = \"MenuPortal\";\nvar [PortalProvider, usePortalContext] = createMenuContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar MenuPortal = (props)=>{\n    const { __scopeMenu, forceMount, children, container } = props;\n    const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeMenu,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nMenuPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"MenuContent\";\nvar [MenuContentProvider, useMenuContentContext] = createMenuContext(CONTENT_NAME);\nvar MenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeMenu,\n                children: rootContext.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootContentModal, {\n                    ...contentProps,\n                    ref: forwardedRef\n                }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootContentNonModal, {\n                    ...contentProps,\n                    ref: forwardedRef\n                })\n            })\n        })\n    });\n});\nvar MenuRootContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"MenuRootContentModal.useEffect\": ()=>{\n            const content = ref.current;\n            if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_11__.hideOthers)(content);\n        }\n    }[\"MenuRootContentModal.useEffect\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: context.open,\n        disableOutsideScroll: true,\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault(), {\n            checkForDefaultPrevented: false\n        }),\n        onDismiss: ()=>context.onOpenChange(false)\n    });\n});\nvar MenuRootContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        disableOutsideScroll: false,\n        onDismiss: ()=>context.onOpenChange(false)\n    });\n});\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_13__.createSlot)(\"MenuContent.ScrollLock\");\nvar MenuContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, loop = false, trapFocus, onOpenAutoFocus, onCloseAutoFocus, disableOutsidePointerEvents, onEntryFocus, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, disableOutsideScroll, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const pointerGraceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerGraceIntentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerDirRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"right\");\n    const lastPointerXRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const ScrollLockWrapper = disableOutsideScroll ? react_remove_scroll__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : react__WEBPACK_IMPORTED_MODULE_0__.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll ? {\n        as: Slot,\n        allowPinchZoom: true\n    } : void 0;\n    const handleTypeaheadSearch = (key)=>{\n        const search = searchRef.current + key;\n        const items = getItems().filter((item)=>!item.disabled);\n        const currentItem = document.activeElement;\n        const currentMatch = items.find((item)=>item.ref.current === currentItem)?.textValue;\n        const values = items.map((item)=>item.textValue);\n        const nextMatch = getNextMatch(values, search, currentMatch);\n        const newItem = items.find((item)=>item.textValue === nextMatch)?.ref.current;\n        (function updateSearch(value) {\n            searchRef.current = value;\n            window.clearTimeout(timerRef.current);\n            if (value !== \"\") timerRef.current = window.setTimeout(()=>updateSearch(\"\"), 1e3);\n        })(search);\n        if (newItem) {\n            setTimeout(()=>newItem.focus());\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"MenuContentImpl.useEffect\": ()=>{\n            return ({\n                \"MenuContentImpl.useEffect\": ()=>window.clearTimeout(timerRef.current)\n            })[\"MenuContentImpl.useEffect\"];\n        }\n    }[\"MenuContentImpl.useEffect\"], []);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__.useFocusGuards)();\n    const isPointerMovingToSubmenu = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"MenuContentImpl.useCallback[isPointerMovingToSubmenu]\": (event)=>{\n            const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n            return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n        }\n    }[\"MenuContentImpl.useCallback[isPointerMovingToSubmenu]\"], []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentProvider, {\n        scope: __scopeMenu,\n        searchRef,\n        onItemEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"MenuContentImpl.useCallback\": (event)=>{\n                if (isPointerMovingToSubmenu(event)) event.preventDefault();\n            }\n        }[\"MenuContentImpl.useCallback\"], [\n            isPointerMovingToSubmenu\n        ]),\n        onItemLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"MenuContentImpl.useCallback\": (event)=>{\n                if (isPointerMovingToSubmenu(event)) return;\n                contentRef.current?.focus();\n                setCurrentItemId(null);\n            }\n        }[\"MenuContentImpl.useCallback\"], [\n            isPointerMovingToSubmenu\n        ]),\n        onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"MenuContentImpl.useCallback\": (event)=>{\n                if (isPointerMovingToSubmenu(event)) event.preventDefault();\n            }\n        }[\"MenuContentImpl.useCallback\"], [\n            isPointerMovingToSubmenu\n        ]),\n        pointerGraceTimerRef,\n        onPointerGraceIntentChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"MenuContentImpl.useCallback\": (intent)=>{\n                pointerGraceIntentRef.current = intent;\n            }\n        }[\"MenuContentImpl.useCallback\"], []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollLockWrapper, {\n            ...scrollLockWrapperProps,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_16__.FocusScope, {\n                asChild: true,\n                trapped: trapFocus,\n                onMountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onOpenAutoFocus, (event)=>{\n                    event.preventDefault();\n                    contentRef.current?.focus({\n                        preventScroll: true\n                    });\n                }),\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_17__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside,\n                    onInteractOutside,\n                    onDismiss,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.Root, {\n                        asChild: true,\n                        ...rovingFocusGroupScope,\n                        dir: rootContext.dir,\n                        orientation: \"vertical\",\n                        loop,\n                        currentTabStopId: currentItemId,\n                        onCurrentTabStopIdChange: setCurrentItemId,\n                        onEntryFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEntryFocus, (event)=>{\n                            if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                        }),\n                        preventScrollOnEntryFocus: true,\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Content, {\n                            role: \"menu\",\n                            \"aria-orientation\": \"vertical\",\n                            \"data-state\": getOpenState(context.open),\n                            \"data-radix-menu-content\": \"\",\n                            dir: rootContext.dir,\n                            ...popperScope,\n                            ...contentProps,\n                            ref: composedRefs,\n                            style: {\n                                outline: \"none\",\n                                ...contentProps.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                                const target = event.target;\n                                const isKeyDownInside = target.closest(\"[data-radix-menu-content]\") === event.currentTarget;\n                                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                                const isCharacterKey = event.key.length === 1;\n                                if (isKeyDownInside) {\n                                    if (event.key === \"Tab\") event.preventDefault();\n                                    if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                                }\n                                const content = contentRef.current;\n                                if (event.target !== content) return;\n                                if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                                event.preventDefault();\n                                const items = getItems().filter((item)=>!item.disabled);\n                                const candidateNodes = items.map((item)=>item.ref.current);\n                                if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                                focusFirst(candidateNodes);\n                            }),\n                            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onBlur, (event)=>{\n                                if (!event.currentTarget.contains(event.target)) {\n                                    window.clearTimeout(timerRef.current);\n                                    searchRef.current = \"\";\n                                }\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                                const target = event.target;\n                                const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n                                if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                                    const newDir = event.clientX > lastPointerXRef.current ? \"right\" : \"left\";\n                                    pointerDirRef.current = newDir;\n                                    lastPointerXRef.current = event.clientX;\n                                }\n                            }))\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nMenuContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"MenuGroup\";\nvar MenuGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...groupProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        role: \"group\",\n        ...groupProps,\n        ref: forwardedRef\n    });\n});\nMenuGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"MenuLabel\";\nvar MenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...labelProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nMenuLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"MenuItem\";\nvar ITEM_SELECT = \"menu.itemSelect\";\nvar MenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleSelect = ()=>{\n        const menuItem = ref.current;\n        if (!disabled && menuItem) {\n            const itemSelectEvent = new CustomEvent(ITEM_SELECT, {\n                bubbles: true,\n                cancelable: true\n            });\n            menuItem.addEventListener(ITEM_SELECT, (event)=>onSelect?.(event), {\n                once: true\n            });\n            (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.dispatchDiscreteCustomEvent)(menuItem, itemSelectEvent);\n            if (itemSelectEvent.defaultPrevented) {\n                isPointerDownRef.current = false;\n            } else {\n                rootContext.onClose();\n            }\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItemImpl, {\n        ...itemProps,\n        ref: composedRefs,\n        disabled,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, handleSelect),\n        onPointerDown: (event)=>{\n            props.onPointerDown?.(event);\n            isPointerDownRef.current = true;\n        },\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n            if (!isPointerDownRef.current) event.currentTarget?.click();\n        }),\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n            const isTypingAhead = contentContext.searchRef.current !== \"\";\n            if (disabled || isTypingAhead && event.key === \" \") return;\n            if (SELECTION_KEYS.includes(event.key)) {\n                event.currentTarget.click();\n                event.preventDefault();\n            }\n        })\n    });\n});\nMenuItem.displayName = ITEM_NAME;\nvar MenuItemImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [textContent, setTextContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"\");\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"MenuItemImpl.useEffect\": ()=>{\n            const menuItem = ref.current;\n            if (menuItem) {\n                setTextContent((menuItem.textContent ?? \"\").trim());\n            }\n        }\n    }[\"MenuItemImpl.useEffect\"], [\n        itemProps.children\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeMenu,\n        disabled,\n        textValue: textValue ?? textContent,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.Item, {\n            asChild: true,\n            ...rovingFocusGroupScope,\n            focusable: !disabled,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n                role: \"menuitem\",\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                ...itemProps,\n                ref: composedRefs,\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                    if (disabled) {\n                        contentContext.onItemLeave(event);\n                    } else {\n                        contentContext.onItemEnter(event);\n                        if (!event.defaultPrevented) {\n                            const item = event.currentTarget;\n                            item.focus({\n                                preventScroll: true\n                            });\n                        }\n                    }\n                })),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse((event)=>contentContext.onItemLeave(event))),\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onBlur, ()=>setIsFocused(false))\n            })\n        })\n    });\n});\nvar CHECKBOX_ITEM_NAME = \"MenuCheckboxItem\";\nvar MenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ItemIndicatorProvider, {\n        scope: props.__scopeMenu,\n        checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItem, {\n            role: \"menuitemcheckbox\",\n            \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n            ...checkboxItemProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(checked),\n            onSelect: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(checkboxItemProps.onSelect, ()=>onCheckedChange?.(isIndeterminate(checked) ? true : !checked), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"MenuRadioGroup\";\nvar [RadioGroupProvider, useRadioGroupContext] = createMenuContext(RADIO_GROUP_NAME, {\n    value: void 0,\n    onValueChange: ()=>{}\n});\nvar MenuRadioGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onValueChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RadioGroupProvider, {\n        scope: props.__scopeMenu,\n        value,\n        onValueChange: handleValueChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuGroup, {\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"MenuRadioItem\";\nvar MenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ItemIndicatorProvider, {\n        scope: props.__scopeMenu,\n        checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItem, {\n            role: \"menuitemradio\",\n            \"aria-checked\": checked,\n            ...radioItemProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(checked),\n            onSelect: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(radioItemProps.onSelect, ()=>context.onValueChange?.(value), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\nvar ITEM_INDICATOR_NAME = \"MenuItemIndicator\";\nvar [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext(ITEM_INDICATOR_NAME, {\n    checked: false\n});\nvar MenuItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || isIndeterminate(indicatorContext.checked) || indicatorContext.checked === true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.span, {\n            ...itemIndicatorProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(indicatorContext.checked)\n        })\n    });\n});\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SEPARATOR_NAME = \"MenuSeparator\";\nvar MenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        role: \"separator\",\n        \"aria-orientation\": \"horizontal\",\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nMenuSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"MenuArrow\";\nvar MenuArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nMenuArrow.displayName = ARROW_NAME;\nvar SUB_NAME = \"MenuSub\";\nvar [MenuSubProvider, useMenuSubContext] = createMenuContext(SUB_NAME);\nvar MenuSub = (props)=>{\n    const { __scopeMenu, children, open = false, onOpenChange } = props;\n    const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const handleOpenChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onOpenChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"MenuSub.useEffect\": ()=>{\n            if (parentMenuContext.open === false) handleOpenChange(false);\n            return ({\n                \"MenuSub.useEffect\": ()=>handleOpenChange(false)\n            })[\"MenuSub.useEffect\"];\n        }\n    }[\"MenuSub.useEffect\"], [\n        parentMenuContext.open,\n        handleOpenChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuProvider, {\n            scope: __scopeMenu,\n            open,\n            onOpenChange: handleOpenChange,\n            content,\n            onContentChange: setContent,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuSubProvider, {\n                scope: __scopeMenu,\n                contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__.useId)(),\n                triggerId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__.useId)(),\n                trigger,\n                onTriggerChange: setTrigger,\n                children\n            })\n        })\n    });\n};\nMenuSub.displayName = SUB_NAME;\nvar SUB_TRIGGER_NAME = \"MenuSubTrigger\";\nvar MenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = {\n        __scopeMenu: props.__scopeMenu\n    };\n    const clearOpenTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"MenuSubTrigger.useCallback[clearOpenTimer]\": ()=>{\n            if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n            openTimerRef.current = null;\n        }\n    }[\"MenuSubTrigger.useCallback[clearOpenTimer]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"MenuSubTrigger.useEffect\": ()=>clearOpenTimer\n    }[\"MenuSubTrigger.useEffect\"], [\n        clearOpenTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"MenuSubTrigger.useEffect\": ()=>{\n            const pointerGraceTimer = pointerGraceTimerRef.current;\n            return ({\n                \"MenuSubTrigger.useEffect\": ()=>{\n                    window.clearTimeout(pointerGraceTimer);\n                    onPointerGraceIntentChange(null);\n                }\n            })[\"MenuSubTrigger.useEffect\"];\n        }\n    }[\"MenuSubTrigger.useEffect\"], [\n        pointerGraceTimerRef,\n        onPointerGraceIntentChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuAnchor, {\n        asChild: true,\n        ...scope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItemImpl, {\n            id: subContext.triggerId,\n            \"aria-haspopup\": \"menu\",\n            \"aria-expanded\": context.open,\n            \"aria-controls\": subContext.contentId,\n            \"data-state\": getOpenState(context.open),\n            ...props,\n            ref: (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.composeRefs)(forwardedRef, subContext.onTriggerChange),\n            onClick: (event)=>{\n                props.onClick?.(event);\n                if (props.disabled || event.defaultPrevented) return;\n                event.currentTarget.focus();\n                if (!context.open) context.onOpenChange(true);\n            },\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                contentContext.onItemEnter(event);\n                if (event.defaultPrevented) return;\n                if (!props.disabled && !context.open && !openTimerRef.current) {\n                    contentContext.onPointerGraceIntentChange(null);\n                    openTimerRef.current = window.setTimeout(()=>{\n                        context.onOpenChange(true);\n                        clearOpenTimer();\n                    }, 100);\n                }\n            })),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse((event)=>{\n                clearOpenTimer();\n                const contentRect = context.content?.getBoundingClientRect();\n                if (contentRect) {\n                    const side = context.content?.dataset.side;\n                    const rightSide = side === \"right\";\n                    const bleed = rightSide ? -5 : 5;\n                    const contentNearEdge = contentRect[rightSide ? \"left\" : \"right\"];\n                    const contentFarEdge = contentRect[rightSide ? \"right\" : \"left\"];\n                    contentContext.onPointerGraceIntentChange({\n                        area: [\n                            // Apply a bleed on clientX to ensure that our exit point is\n                            // consistently within polygon bounds\n                            {\n                                x: event.clientX + bleed,\n                                y: event.clientY\n                            },\n                            {\n                                x: contentNearEdge,\n                                y: contentRect.top\n                            },\n                            {\n                                x: contentFarEdge,\n                                y: contentRect.top\n                            },\n                            {\n                                x: contentFarEdge,\n                                y: contentRect.bottom\n                            },\n                            {\n                                x: contentNearEdge,\n                                y: contentRect.bottom\n                            }\n                        ],\n                        side\n                    });\n                    window.clearTimeout(pointerGraceTimerRef.current);\n                    pointerGraceTimerRef.current = window.setTimeout(()=>contentContext.onPointerGraceIntentChange(null), 300);\n                } else {\n                    contentContext.onTriggerLeave(event);\n                    if (event.defaultPrevented) return;\n                    contentContext.onPointerGraceIntentChange(null);\n                }\n            })),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isTypingAhead = contentContext.searchRef.current !== \"\";\n                if (props.disabled || isTypingAhead && event.key === \" \") return;\n                if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n                    context.onOpenChange(true);\n                    context.content?.focus();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"MenuSubContent\";\nvar MenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeMenu,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n                    id: subContext.contentId,\n                    \"aria-labelledby\": subContext.triggerId,\n                    ...subContentProps,\n                    ref: composedRefs,\n                    align: \"start\",\n                    side: rootContext.dir === \"rtl\" ? \"left\" : \"right\",\n                    disableOutsidePointerEvents: false,\n                    disableOutsideScroll: false,\n                    trapFocus: false,\n                    onOpenAutoFocus: (event)=>{\n                        if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                        event.preventDefault();\n                    },\n                    onCloseAutoFocus: (event)=>event.preventDefault(),\n                    onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>{\n                        if (event.target !== subContext.trigger) context.onOpenChange(false);\n                    }),\n                    onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onEscapeKeyDown, (event)=>{\n                        rootContext.onClose();\n                        event.preventDefault();\n                    }),\n                    onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                        const isKeyDownInside = event.currentTarget.contains(event.target);\n                        const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                        if (isKeyDownInside && isCloseKey) {\n                            context.onOpenChange(false);\n                            subContext.trigger?.focus();\n                            event.preventDefault();\n                        }\n                    })\n                })\n            })\n        })\n    });\n});\nMenuSubContent.displayName = SUB_CONTENT_NAME;\nfunction getOpenState(open) {\n    return open ? \"open\" : \"closed\";\n}\nfunction isIndeterminate(checked) {\n    return checked === \"indeterminate\";\n}\nfunction getCheckedState(checked) {\n    return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\nfunction focusFirst(candidates) {\n    const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n    for (const candidate of candidates){\n        if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n        candidate.focus();\n        if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n    }\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nfunction getNextMatch(values, search, currentMatch) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n    let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n    const excludeCurrentMatch = normalizedSearch.length === 1;\n    if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v)=>v !== currentMatch);\n    const nextMatch = wrappedValues.find((value)=>value.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextMatch !== currentMatch ? nextMatch : void 0;\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const ii = polygon[i];\n        const jj = polygon[j];\n        const xi = ii.x;\n        const yi = ii.y;\n        const xj = jj.x;\n        const yj = jj.y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction isPointerInGraceArea(event, area) {\n    if (!area) return false;\n    const cursorPos = {\n        x: event.clientX,\n        y: event.clientY\n    };\n    return isPointInPolygon(cursorPos, area);\n}\nfunction whenMouse(handler) {\n    return (event)=>event.pointerType === \"mouse\" ? handler(event) : void 0;\n}\nvar Root3 = Menu;\nvar Anchor2 = MenuAnchor;\nvar Portal = MenuPortal;\nvar Content2 = MenuContent;\nvar Group = MenuGroup;\nvar Label = MenuLabel;\nvar Item2 = MenuItem;\nvar CheckboxItem = MenuCheckboxItem;\nvar RadioGroup = MenuRadioGroup;\nvar RadioItem = MenuRadioItem;\nvar ItemIndicator = MenuItemIndicator;\nvar Separator = MenuSeparator;\nvar Arrow2 = MenuArrow;\nvar Sub = MenuSub;\nvar SubTrigger = MenuSubTrigger;\nvar SubContent = MenuSubContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.12_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18.3.2_luysprn5rpfx7rkoejvajrfwde/node_modules/@radix-ui/react-menu/dist/index.mjs\n");

/***/ })

};
;