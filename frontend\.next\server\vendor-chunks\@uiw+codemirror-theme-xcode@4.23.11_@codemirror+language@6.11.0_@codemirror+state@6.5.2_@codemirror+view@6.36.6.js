"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uiw+codemirror-theme-xcode@4.23.11_@codemirror+language@6.11.0_@codemirror+state@6.5.2_@codemirror+view@6.36.6";
exports.ids = ["vendor-chunks/@uiw+codemirror-theme-xcode@4.23.11_@codemirror+language@6.11.0_@codemirror+state@6.5.2_@codemirror+view@6.36.6"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@uiw+codemirror-theme-xcode@4.23.11_@codemirror+language@6.11.0_@codemirror+state@6.5.2_@codemirror+view@6.36.6/node_modules/@uiw/codemirror-theme-xcode/esm/index.js":
/*!**************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@uiw+codemirror-theme-xcode@4.23.11_@codemirror+language@6.11.0_@codemirror+state@6.5.2_@codemirror+view@6.36.6/node_modules/@uiw/codemirror-theme-xcode/esm/index.js ***!
  \**************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultSettingsXcodeDark: () => (/* binding */ defaultSettingsXcodeDark),\n/* harmony export */   defaultSettingsXcodeLight: () => (/* binding */ defaultSettingsXcodeLight),\n/* harmony export */   xcodeDark: () => (/* binding */ xcodeDark),\n/* harmony export */   xcodeDarkInit: () => (/* binding */ xcodeDarkInit),\n/* harmony export */   xcodeDarkStyle: () => (/* binding */ xcodeDarkStyle),\n/* harmony export */   xcodeLight: () => (/* binding */ xcodeLight),\n/* harmony export */   xcodeLightInit: () => (/* binding */ xcodeLightInit),\n/* harmony export */   xcodeLightStyle: () => (/* binding */ xcodeLightStyle)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js\");\n/* harmony import */ var _uiw_codemirror_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @uiw/codemirror-themes */ \"(ssr)/./node_modules/.pnpm/@uiw+codemirror-themes@4.23.11_@codemirror+language@6.11.0_@codemirror+state@6.5.2_@codemirror+view@6.36.6/node_modules/@uiw/codemirror-themes/esm/index.js\");\n\n/**\n * @name Xcode\n */\n\n\nvar defaultSettingsXcodeLight = {\n  background: '#fff',\n  foreground: '#3D3D3D',\n  selection: '#BBDFFF',\n  selectionMatch: '#BBDFFF',\n  gutterBackground: '#fff',\n  gutterForeground: '#AFAFAF',\n  lineHighlight: '#d5e6ff69'\n};\nvar xcodeLightStyle = [{\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.comment, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.quote],\n  color: '#707F8D'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeOperator],\n  color: '#aa0d91'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.keyword],\n  color: '#aa0d91',\n  fontWeight: 'bold'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.meta],\n  color: '#D23423'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name],\n  color: '#032f62'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeName],\n  color: '#522BB2'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName],\n  color: '#23575C'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.definition(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName)],\n  color: '#327A9E'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.regexp, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.link],\n  color: '#0e0eff'\n}];\nfunction xcodeLightInit(options) {\n  var {\n    theme = 'light',\n    settings = {},\n    styles = []\n  } = options || {};\n  return (0,_uiw_codemirror_themes__WEBPACK_IMPORTED_MODULE_2__.createTheme)({\n    theme: theme,\n    settings: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, defaultSettingsXcodeLight, settings),\n    styles: [...xcodeLightStyle, ...styles]\n  });\n}\nvar xcodeLight = xcodeLightInit();\nvar defaultSettingsXcodeDark = {\n  background: '#292A30',\n  foreground: '#CECFD0',\n  caret: '#fff',\n  selection: '#727377',\n  selectionMatch: '#727377',\n  lineHighlight: '#ffffff0f'\n};\nvar xcodeDarkStyle = [{\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.comment, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.quote],\n  color: '#7F8C98'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.keyword],\n  color: '#FF7AB2',\n  fontWeight: 'bold'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.meta],\n  color: '#FF8170'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeName],\n  color: '#DABAFF'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.definition(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName)],\n  color: '#6BDFFF'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.name],\n  color: '#6BAA9F'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName],\n  color: '#ACF2E4'\n}, {\n  tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.regexp, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.link],\n  color: '#FF8170'\n}];\nvar xcodeDarkInit = options => {\n  var {\n    theme = 'dark',\n    settings = {},\n    styles = []\n  } = options || {};\n  return (0,_uiw_codemirror_themes__WEBPACK_IMPORTED_MODULE_2__.createTheme)({\n    theme: theme,\n    settings: _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, defaultSettingsXcodeDark, settings),\n    styles: [...xcodeDarkStyle, ...styles]\n  });\n};\nvar xcodeDark = xcodeDarkInit();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHVpdytjb2RlbWlycm9yLXRoZW1lLXhjb2RlQDQuMjMuMTFfQGNvZGVtaXJyb3IrbGFuZ3VhZ2VANi4xMS4wX0Bjb2RlbWlycm9yK3N0YXRlQDYuNS4yX0Bjb2RlbWlycm9yK3ZpZXdANi4zNi42L25vZGVfbW9kdWxlcy9AdWl3L2NvZGVtaXJyb3ItdGhlbWUteGNvZGUvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFzRDtBQUN0RDtBQUNBO0FBQ0E7QUFDNkM7QUFDUTtBQUM5QztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLFFBQVEsa0RBQUMsVUFBVSxrREFBQztBQUNwQjtBQUNBLENBQUM7QUFDRCxRQUFRLGtEQUFDLFdBQVcsa0RBQUM7QUFDckI7QUFDQSxDQUFDO0FBQ0QsUUFBUSxrREFBQztBQUNUO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsUUFBUSxrREFBQyxTQUFTLGtEQUFDO0FBQ25CO0FBQ0EsQ0FBQztBQUNELFFBQVEsa0RBQUM7QUFDVDtBQUNBLENBQUM7QUFDRCxRQUFRLGtEQUFDO0FBQ1Q7QUFDQSxDQUFDO0FBQ0QsUUFBUSxrREFBQztBQUNUO0FBQ0EsQ0FBQztBQUNELFFBQVEsa0RBQUMsWUFBWSxrREFBQztBQUN0QjtBQUNBLENBQUM7QUFDRCxRQUFRLGtEQUFDLFNBQVMsa0RBQUM7QUFDbkI7QUFDQSxDQUFDO0FBQ007QUFDUDtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsSUFBSTtBQUNKLFNBQVMsbUVBQVc7QUFDcEI7QUFDQSxjQUFjLHFFQUFRLEdBQUc7QUFDekI7QUFDQSxHQUFHO0FBQ0g7QUFDTztBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLFFBQVEsa0RBQUMsVUFBVSxrREFBQztBQUNwQjtBQUNBLENBQUM7QUFDRCxRQUFRLGtEQUFDO0FBQ1Q7QUFDQTtBQUNBLENBQUM7QUFDRCxRQUFRLGtEQUFDLFNBQVMsa0RBQUM7QUFDbkI7QUFDQSxDQUFDO0FBQ0QsUUFBUSxrREFBQztBQUNUO0FBQ0EsQ0FBQztBQUNELFFBQVEsa0RBQUMsWUFBWSxrREFBQztBQUN0QjtBQUNBLENBQUM7QUFDRCxRQUFRLGtEQUFDO0FBQ1Q7QUFDQSxDQUFDO0FBQ0QsUUFBUSxrREFBQztBQUNUO0FBQ0EsQ0FBQztBQUNELFFBQVEsa0RBQUMsU0FBUyxrREFBQztBQUNuQjtBQUNBLENBQUM7QUFDTTtBQUNQO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxJQUFJO0FBQ0osU0FBUyxtRUFBVztBQUNwQjtBQUNBLGNBQWMscUVBQVEsR0FBRztBQUN6QjtBQUNBLEdBQUc7QUFDSDtBQUNPIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFuZHJlXFxPbmVEcml2ZVxcRGVza3RvcFxcR2l0aHViIFJlcG9zaXRvcmllc1xcZGVtb1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEB1aXcrY29kZW1pcnJvci10aGVtZS14Y29kZUA0LjIzLjExX0Bjb2RlbWlycm9yK2xhbmd1YWdlQDYuMTEuMF9AY29kZW1pcnJvcitzdGF0ZUA2LjUuMl9AY29kZW1pcnJvcit2aWV3QDYuMzYuNlxcbm9kZV9tb2R1bGVzXFxAdWl3XFxjb2RlbWlycm9yLXRoZW1lLXhjb2RlXFxlc21cXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9leHRlbmRzXCI7XG4vKipcbiAqIEBuYW1lIFhjb2RlXG4gKi9cbmltcG9ydCB7IHRhZ3MgYXMgdCB9IGZyb20gJ0BsZXplci9oaWdobGlnaHQnO1xuaW1wb3J0IHsgY3JlYXRlVGhlbWUgfSBmcm9tICdAdWl3L2NvZGVtaXJyb3ItdGhlbWVzJztcbmV4cG9ydCB2YXIgZGVmYXVsdFNldHRpbmdzWGNvZGVMaWdodCA9IHtcbiAgYmFja2dyb3VuZDogJyNmZmYnLFxuICBmb3JlZ3JvdW5kOiAnIzNEM0QzRCcsXG4gIHNlbGVjdGlvbjogJyNCQkRGRkYnLFxuICBzZWxlY3Rpb25NYXRjaDogJyNCQkRGRkYnLFxuICBndXR0ZXJCYWNrZ3JvdW5kOiAnI2ZmZicsXG4gIGd1dHRlckZvcmVncm91bmQ6ICcjQUZBRkFGJyxcbiAgbGluZUhpZ2hsaWdodDogJyNkNWU2ZmY2OSdcbn07XG5leHBvcnQgdmFyIHhjb2RlTGlnaHRTdHlsZSA9IFt7XG4gIHRhZzogW3QuY29tbWVudCwgdC5xdW90ZV0sXG4gIGNvbG9yOiAnIzcwN0Y4RCdcbn0sIHtcbiAgdGFnOiBbdC50eXBlTmFtZSwgdC50eXBlT3BlcmF0b3JdLFxuICBjb2xvcjogJyNhYTBkOTEnXG59LCB7XG4gIHRhZzogW3Qua2V5d29yZF0sXG4gIGNvbG9yOiAnI2FhMGQ5MScsXG4gIGZvbnRXZWlnaHQ6ICdib2xkJ1xufSwge1xuICB0YWc6IFt0LnN0cmluZywgdC5tZXRhXSxcbiAgY29sb3I6ICcjRDIzNDIzJ1xufSwge1xuICB0YWc6IFt0Lm5hbWVdLFxuICBjb2xvcjogJyMwMzJmNjInXG59LCB7XG4gIHRhZzogW3QudHlwZU5hbWVdLFxuICBjb2xvcjogJyM1MjJCQjInXG59LCB7XG4gIHRhZzogW3QudmFyaWFibGVOYW1lXSxcbiAgY29sb3I6ICcjMjM1NzVDJ1xufSwge1xuICB0YWc6IFt0LmRlZmluaXRpb24odC52YXJpYWJsZU5hbWUpXSxcbiAgY29sb3I6ICcjMzI3QTlFJ1xufSwge1xuICB0YWc6IFt0LnJlZ2V4cCwgdC5saW5rXSxcbiAgY29sb3I6ICcjMGUwZWZmJ1xufV07XG5leHBvcnQgZnVuY3Rpb24geGNvZGVMaWdodEluaXQob3B0aW9ucykge1xuICB2YXIge1xuICAgIHRoZW1lID0gJ2xpZ2h0JyxcbiAgICBzZXR0aW5ncyA9IHt9LFxuICAgIHN0eWxlcyA9IFtdXG4gIH0gPSBvcHRpb25zIHx8IHt9O1xuICByZXR1cm4gY3JlYXRlVGhlbWUoe1xuICAgIHRoZW1lOiB0aGVtZSxcbiAgICBzZXR0aW5nczogX2V4dGVuZHMoe30sIGRlZmF1bHRTZXR0aW5nc1hjb2RlTGlnaHQsIHNldHRpbmdzKSxcbiAgICBzdHlsZXM6IFsuLi54Y29kZUxpZ2h0U3R5bGUsIC4uLnN0eWxlc11cbiAgfSk7XG59XG5leHBvcnQgdmFyIHhjb2RlTGlnaHQgPSB4Y29kZUxpZ2h0SW5pdCgpO1xuZXhwb3J0IHZhciBkZWZhdWx0U2V0dGluZ3NYY29kZURhcmsgPSB7XG4gIGJhY2tncm91bmQ6ICcjMjkyQTMwJyxcbiAgZm9yZWdyb3VuZDogJyNDRUNGRDAnLFxuICBjYXJldDogJyNmZmYnLFxuICBzZWxlY3Rpb246ICcjNzI3Mzc3JyxcbiAgc2VsZWN0aW9uTWF0Y2g6ICcjNzI3Mzc3JyxcbiAgbGluZUhpZ2hsaWdodDogJyNmZmZmZmYwZidcbn07XG5leHBvcnQgdmFyIHhjb2RlRGFya1N0eWxlID0gW3tcbiAgdGFnOiBbdC5jb21tZW50LCB0LnF1b3RlXSxcbiAgY29sb3I6ICcjN0Y4Qzk4J1xufSwge1xuICB0YWc6IFt0LmtleXdvcmRdLFxuICBjb2xvcjogJyNGRjdBQjInLFxuICBmb250V2VpZ2h0OiAnYm9sZCdcbn0sIHtcbiAgdGFnOiBbdC5zdHJpbmcsIHQubWV0YV0sXG4gIGNvbG9yOiAnI0ZGODE3MCdcbn0sIHtcbiAgdGFnOiBbdC50eXBlTmFtZV0sXG4gIGNvbG9yOiAnI0RBQkFGRidcbn0sIHtcbiAgdGFnOiBbdC5kZWZpbml0aW9uKHQudmFyaWFibGVOYW1lKV0sXG4gIGNvbG9yOiAnIzZCREZGRidcbn0sIHtcbiAgdGFnOiBbdC5uYW1lXSxcbiAgY29sb3I6ICcjNkJBQTlGJ1xufSwge1xuICB0YWc6IFt0LnZhcmlhYmxlTmFtZV0sXG4gIGNvbG9yOiAnI0FDRjJFNCdcbn0sIHtcbiAgdGFnOiBbdC5yZWdleHAsIHQubGlua10sXG4gIGNvbG9yOiAnI0ZGODE3MCdcbn1dO1xuZXhwb3J0IHZhciB4Y29kZURhcmtJbml0ID0gb3B0aW9ucyA9PiB7XG4gIHZhciB7XG4gICAgdGhlbWUgPSAnZGFyaycsXG4gICAgc2V0dGluZ3MgPSB7fSxcbiAgICBzdHlsZXMgPSBbXVxuICB9ID0gb3B0aW9ucyB8fCB7fTtcbiAgcmV0dXJuIGNyZWF0ZVRoZW1lKHtcbiAgICB0aGVtZTogdGhlbWUsXG4gICAgc2V0dGluZ3M6IF9leHRlbmRzKHt9LCBkZWZhdWx0U2V0dGluZ3NYY29kZURhcmssIHNldHRpbmdzKSxcbiAgICBzdHlsZXM6IFsuLi54Y29kZURhcmtTdHlsZSwgLi4uc3R5bGVzXVxuICB9KTtcbn07XG5leHBvcnQgdmFyIHhjb2RlRGFyayA9IHhjb2RlRGFya0luaXQoKTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@uiw+codemirror-theme-xcode@4.23.11_@codemirror+language@6.11.0_@codemirror+state@6.5.2_@codemirror+view@6.36.6/node_modules/@uiw/codemirror-theme-xcode/esm/index.js\n");

/***/ })

};
;