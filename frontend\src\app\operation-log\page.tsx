'use client';

import React from 'react';
import { CompletionLog } from '@/components/demo/CompletionLog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function OperationLogPage() {
  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-xl font-medium text-[#252423]">Operation Log</h1>
          <p className="text-sm text-[#605e5c]">
            View detailed activity logs for your AI agent team
          </p>
        </div>
      </div>

      <Card className="bg-white border-[#e1dfdd]">
        <CardHeader className="border-b border-[#e1dfdd]">
          <CardTitle className="text-lg text-[#252423]">Activity Timeline</CardTitle>
          <CardDescription className="text-[#605e5c]">
            Chronological record of agent activities and system events
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <CompletionLog isActive={true} errorTriggered={true} />
        </CardContent>
      </Card>
    </div>
  );
}
