"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_codemirror_legacy-modes_6_5_1_node_modules_codemirror_le-a27702"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/ttcn-cfg.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/ttcn-cfg.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ttcnCfg: () => (/* binding */ ttcnCfg)\n/* harmony export */ });\nfunction words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i)\n    obj[words[i]] = true;\n  return obj;\n}\n\nconst parserConfig = {\n  name: \"ttcn-cfg\",\n  keywords: words(\"Yes No LogFile FileMask ConsoleMask AppendFile\" +\n                  \" TimeStampFormat LogEventTypes SourceInfoFormat\" +\n                  \" LogEntityName LogSourceInfo DiskFullAction\" +\n                  \" LogFileNumber LogFileSize MatchingHints Detailed\" +\n                  \" Compact SubCategories Stack Single None Seconds\" +\n                  \" DateTime Time Stop Error Retry Delete TCPPort KillTimer\" +\n                  \" NumHCs UnixSocketsEnabled LocalAddress\"),\n  fileNCtrlMaskOptions: words(\"TTCN_EXECUTOR TTCN_ERROR TTCN_WARNING\" +\n                              \" TTCN_PORTEVENT TTCN_TIMEROP TTCN_VERDICTOP\" +\n                              \" TTCN_DEFAULTOP TTCN_TESTCASE TTCN_ACTION\" +\n                              \" TTCN_USER TTCN_FUNCTION TTCN_STATISTICS\" +\n                              \" TTCN_PARALLEL TTCN_MATCHING TTCN_DEBUG\" +\n                              \" EXECUTOR ERROR WARNING PORTEVENT TIMEROP\" +\n                              \" VERDICTOP DEFAULTOP TESTCASE ACTION USER\" +\n                              \" FUNCTION STATISTICS PARALLEL MATCHING DEBUG\" +\n                              \" LOG_ALL LOG_NOTHING ACTION_UNQUALIFIED\" +\n                              \" DEBUG_ENCDEC DEBUG_TESTPORT\" +\n                              \" DEBUG_UNQUALIFIED DEFAULTOP_ACTIVATE\" +\n                              \" DEFAULTOP_DEACTIVATE DEFAULTOP_EXIT\" +\n                              \" DEFAULTOP_UNQUALIFIED ERROR_UNQUALIFIED\" +\n                              \" EXECUTOR_COMPONENT EXECUTOR_CONFIGDATA\" +\n                              \" EXECUTOR_EXTCOMMAND EXECUTOR_LOGOPTIONS\" +\n                              \" EXECUTOR_RUNTIME EXECUTOR_UNQUALIFIED\" +\n                              \" FUNCTION_RND FUNCTION_UNQUALIFIED\" +\n                              \" MATCHING_DONE MATCHING_MCSUCCESS\" +\n                              \" MATCHING_MCUNSUCC MATCHING_MMSUCCESS\" +\n                              \" MATCHING_MMUNSUCC MATCHING_PCSUCCESS\" +\n                              \" MATCHING_PCUNSUCC MATCHING_PMSUCCESS\" +\n                              \" MATCHING_PMUNSUCC MATCHING_PROBLEM\" +\n                              \" MATCHING_TIMEOUT MATCHING_UNQUALIFIED\" +\n                              \" PARALLEL_PORTCONN PARALLEL_PORTMAP\" +\n                              \" PARALLEL_PTC PARALLEL_UNQUALIFIED\" +\n                              \" PORTEVENT_DUALRECV PORTEVENT_DUALSEND\" +\n                              \" PORTEVENT_MCRECV PORTEVENT_MCSEND\" +\n                              \" PORTEVENT_MMRECV PORTEVENT_MMSEND\" +\n                              \" PORTEVENT_MQUEUE PORTEVENT_PCIN\" +\n                              \" PORTEVENT_PCOUT PORTEVENT_PMIN\" +\n                              \" PORTEVENT_PMOUT PORTEVENT_PQUEUE\" +\n                              \" PORTEVENT_STATE PORTEVENT_UNQUALIFIED\" +\n                              \" STATISTICS_UNQUALIFIED STATISTICS_VERDICT\" +\n                              \" TESTCASE_FINISH TESTCASE_START\" +\n                              \" TESTCASE_UNQUALIFIED TIMEROP_GUARD\" +\n                              \" TIMEROP_READ TIMEROP_START TIMEROP_STOP\" +\n                              \" TIMEROP_TIMEOUT TIMEROP_UNQUALIFIED\" +\n                              \" USER_UNQUALIFIED VERDICTOP_FINAL\" +\n                              \" VERDICTOP_GETVERDICT VERDICTOP_SETVERDICT\" +\n                              \" VERDICTOP_UNQUALIFIED WARNING_UNQUALIFIED\"),\n  externalCommands: words(\"BeginControlPart EndControlPart BeginTestCase\" +\n                          \" EndTestCase\"),\n  multiLineStrings: true\n}\n\nvar keywords = parserConfig.keywords,\n    fileNCtrlMaskOptions = parserConfig.fileNCtrlMaskOptions,\n    externalCommands = parserConfig.externalCommands,\n    multiLineStrings = parserConfig.multiLineStrings,\n    indentStatements = parserConfig.indentStatements !== false;\nvar isOperatorChar = /[\\|]/;\nvar curPunc;\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n  if (ch == '\"' || ch == \"'\") {\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  if (/[:=]/.test(ch)) {\n    curPunc = ch;\n    return \"punctuation\";\n  }\n  if (ch == \"#\"){\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  if (ch == \"[\"){\n    stream.eatWhile(/[\\w_\\]]/);\n    return \"number\";\n  }\n\n  stream.eatWhile(/[\\w\\$_]/);\n  var cur = stream.current();\n  if (keywords.propertyIsEnumerable(cur)) return \"keyword\";\n  if (fileNCtrlMaskOptions.propertyIsEnumerable(cur))\n    return \"atom\";\n  if (externalCommands.propertyIsEnumerable(cur)) return \"deleted\";\n\n  return \"variable\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while ((next = stream.next()) != null) {\n      if (next == quote && !escaped){\n        var afterNext = stream.peek();\n        //look if the character if the quote is like the B in '10100010'B\n        if (afterNext){\n          afterNext = afterNext.toLowerCase();\n          if(afterNext == \"b\" || afterNext == \"h\" || afterNext == \"o\")\n            stream.next();\n        }\n        end = true; break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !(escaped || multiLineStrings))\n      state.tokenize = null;\n    return \"string\";\n  };\n}\n\nfunction Context(indented, column, type, align, prev) {\n  this.indented = indented;\n  this.column = column;\n  this.type = type;\n  this.align = align;\n  this.prev = prev;\n}\nfunction pushContext(state, col, type) {\n  var indent = state.indented;\n  if (state.context && state.context.type == \"statement\")\n    indent = state.context.indented;\n  return state.context = new Context(indent, col, type, null, state.context);\n}\nfunction popContext(state) {\n  var t = state.context.type;\n  if (t == \")\" || t == \"]\" || t == \"}\")\n    state.indented = state.context.indented;\n  return state.context = state.context.prev;\n}\n\n//Interface\nconst ttcnCfg = {\n  name: \"ttcn\",\n  startState: function() {\n    return {\n      tokenize: null,\n      context: new Context(0, 0, \"top\", false),\n      indented: 0,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    var ctx = state.context;\n    if (stream.sol()) {\n      if (ctx.align == null) ctx.align = false;\n      state.indented = stream.indentation();\n      state.startOfLine = true;\n    }\n    if (stream.eatSpace()) return null;\n    curPunc = null;\n    var style = (state.tokenize || tokenBase)(stream, state);\n    if (style == \"comment\") return style;\n    if (ctx.align == null) ctx.align = true;\n\n    if ((curPunc == \";\" || curPunc == \":\" || curPunc == \",\")\n        && ctx.type == \"statement\"){\n      popContext(state);\n    }\n    else if (curPunc == \"{\") pushContext(state, stream.column(), \"}\");\n    else if (curPunc == \"[\") pushContext(state, stream.column(), \"]\");\n    else if (curPunc == \"(\") pushContext(state, stream.column(), \")\");\n    else if (curPunc == \"}\") {\n      while (ctx.type == \"statement\") ctx = popContext(state);\n      if (ctx.type == \"}\") ctx = popContext(state);\n      while (ctx.type == \"statement\") ctx = popContext(state);\n    }\n    else if (curPunc == ctx.type) popContext(state);\n    else if (indentStatements && (((ctx.type == \"}\" || ctx.type == \"top\")\n                                   && curPunc != ';') || (ctx.type == \"statement\"\n                                                          && curPunc == \"newstatement\")))\n      pushContext(state, stream.column(), \"statement\");\n    state.startOfLine = false;\n    return style;\n  },\n\n  languageData: {\n    indentOnInput: /^\\s*[{}]$/,\n    commentTokens: {line: \"#\"}\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/ttcn-cfg.js\n"));

/***/ })

}]);