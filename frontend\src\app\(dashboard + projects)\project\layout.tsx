'use client';

import React, { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { ProjectSidebarLeft } from '@/components/sidebar/project-sidebar-left';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar'
import { CallProvider } from '@/components/project/chat/contexts/CallContext'

export default function ProjectLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [activeSection, setActiveSection] = useState<'chat' | 'team' | 'tools' | 'settings' | 'calling'>('chat');

  // Determine active section based on pathname
  useEffect(() => {
    if (pathname.includes('/chat')) {
      setActiveSection('chat');
    } else if (pathname.includes('/team')) {
      setActiveSection('team');
    } else if (pathname.includes('/tools')) {
      setActiveSection('tools');
    } else if (pathname.includes('/settings')) {
      setActiveSection('settings');
    } else if (pathname.includes('/calling')) {
      setActiveSection('calling');
    } else {
      setActiveSection('chat');
    }
  }, [pathname]);

  // Determine if sidebar should be collapsed by default (only for chat page)
  const isOnChatPage = pathname.includes('/chat') || activeSection === 'chat';

  // Dispatch a custom event when the section changes and handle URL routing
  const handleSectionChange = (section: 'chat' | 'team' | 'tools' | 'settings' | 'calling') => {
    setActiveSection(section);

    // Update URL to match the section
    let targetPath = '';
    switch (section) {
      case 'chat':
        targetPath = '/project/chat';
        break;
      case 'team':
        targetPath = '/project/team';
        break;
      case 'tools':
        targetPath = '/project/tools';
        break;
      case 'calling':
        targetPath = '/project/calling';
        break;
      case 'settings':
        targetPath = '/project/settings';
        break;
      default:
        targetPath = '/project/chat';
    }

    // Navigate to the new URL
    router.push(targetPath);

    window.dispatchEvent(
      new CustomEvent('sidebar-section-change', {
        detail: { section },
      })
    );
  };

  return (
    <CallProvider>
      <SidebarProvider defaultOpen={!isOnChatPage}>
        <ProjectSidebarLeft
          activeSection={activeSection}
          onSectionChange={handleSectionChange}
        />
        <SidebarInset>
          <div className="bg-background">
            <main>{children}</main>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </CallProvider>
  );
}
