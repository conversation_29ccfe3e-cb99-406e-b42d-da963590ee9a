"""
API endpoints for managing project agents.

This module provides endpoints for:
- Getting the list of available agent types
- Getting the list of agents for a project
- Adding an agent to a project
- Removing an agent from a project
"""

import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, Body

from services.supabase import get_supabase_client
from utils.auth_utils import get_user_id, get_account_id_from_project
from utils.logger import logger

router = APIRouter(prefix="/project-agents", tags=["Project Agents"])


@router.get("/agent-types")
async def get_agent_types():
    """Get the list of available agent types."""
    try:
        client = await get_supabase_client()
        result = await client.from_("agent_types").select("*").execute()
        
        if result.data:
            return {"agent_types": result.data}
        else:
            return {"agent_types": []}
            
    except Exception as e:
        logger.error(f"Error getting agent types: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error getting agent types: {str(e)}")


@router.get("/{project_id}")
async def get_project_agents(project_id: str, user_id: str = Depends(get_user_id)):
    """Get the list of agents for a project."""
    try:
        client = await get_supabase_client()
        
        # Check if user has access to the project
        account_id = await get_account_id_from_project(client, project_id)
        if not account_id:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Call the database function to get project agents
        result = await client.rpc(
            'get_project_agents',
            {'p_project_id': project_id}
        ).execute()
        
        if result.data:
            return {"agents": result.data}
        else:
            return {"agents": []}
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting project agents: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error getting project agents: {str(e)}")


@router.post("/{project_id}")
async def add_agent_to_project(
    project_id: str,
    agent_type_id: str = Body(..., embed=True),
    user_id: str = Depends(get_user_id)
):
    """Add an agent to a project."""
    try:
        client = await get_supabase_client()
        
        # Check if user has access to the project
        account_id = await get_account_id_from_project(client, project_id)
        if not account_id:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Check if agent type exists
        agent_type_result = await client.from_("agent_types").select("*").eq("agent_type_id", agent_type_id).execute()
        if not agent_type_result.data:
            raise HTTPException(status_code=404, detail="Agent type not found")
        
        # Check if agent already exists for this project and agent type
        existing_agent = await client.from_("project_agents").select("*").eq("project_id", project_id).eq("agent_type_id", agent_type_id).execute()
        if existing_agent.data:
            # If agent exists but is inactive, reactivate it
            if not existing_agent.data[0].get("is_active", True):
                await client.from_("project_agents").update({"is_active": True}).eq("project_id", project_id).eq("agent_type_id", agent_type_id).execute()
                return {"message": "Agent reactivated successfully"}
            else:
                return {"message": "Agent already exists for this project"}
        
        # Create a new agent
        agent_id = str(uuid.uuid4())
        
        # First, create the agent in the agents table
        await client.from_("agents").insert({
            "agent_id": agent_id,
            "name": agent_type_result.data[0].get("name"),
            "description": agent_type_result.data[0].get("description"),
            "capabilities": agent_type_result.data[0].get("capabilities", {}),
            "created_at": datetime.now(timezone.utc).isoformat()
        }).execute()
        
        # Then, add the agent to the project
        await client.from_("project_agents").insert({
            "project_id": project_id,
            "agent_type_id": agent_type_id,
            "agent_id": agent_id,
            "is_active": True,
            "created_at": datetime.now(timezone.utc).isoformat()
        }).execute()
        
        return {"message": "Agent added successfully", "agent_id": agent_id}
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding agent to project: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error adding agent to project: {str(e)}")


@router.delete("/{project_id}/{agent_type_id}")
async def remove_agent_from_project(
    project_id: str,
    agent_type_id: str,
    user_id: str = Depends(get_user_id)
):
    """Remove an agent from a project."""
    try:
        client = await get_supabase_client()
        
        # Check if user has access to the project
        account_id = await get_account_id_from_project(client, project_id)
        if not account_id:
            raise HTTPException(status_code=404, detail="Project not found")
        
        # Check if agent exists for this project
        existing_agent = await client.from_("project_agents").select("*").eq("project_id", project_id).eq("agent_type_id", agent_type_id).execute()
        if not existing_agent.data:
            raise HTTPException(status_code=404, detail="Agent not found for this project")
        
        # Deactivate the agent instead of deleting it
        await client.from_("project_agents").update({"is_active": False}).eq("project_id", project_id).eq("agent_type_id", agent_type_id).execute()
        
        return {"message": "Agent removed successfully"}
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing agent from project: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error removing agent from project: {str(e)}")
