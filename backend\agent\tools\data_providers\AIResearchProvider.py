from typing import Dict

from agent.tools.data_providers.RapidDataProviderBase import RapidDataProviderBase, EndpointSchema


class AIResearchProvider(RapidDataProviderBase):
    def __init__(self):
        endpoints: Dict[str, EndpointSchema] = {
            "search_papers": {
                "route": "/search",
                "method": "GET",
                "name": "Search Research Papers",
                "description": "Search for AI research papers with various filters.",
                "payload": {
                    "query": "Search query (e.g., 'transformer architecture')",
                    "year": "Optional filter by publication year",
                    "venue": "Optional filter by publication venue (e.g., 'NeurIPS', 'ICML')",
                    "author": "Optional filter by author name",
                    "limit": "Optional maximum number of results to return (default: 10)",
                    "offset": "Optional offset for pagination"
                }
            },
            "get_paper": {
                "route": "/paper/{paper_id}",
                "method": "GET",
                "name": "Get Paper Details",
                "description": "Get detailed information about a specific research paper.",
                "payload": {
                    "paper_id": "ID of the paper to retrieve"
                }
            },
            "get_author": {
                "route": "/author/{author_id}",
                "method": "GET",
                "name": "Get Author Details",
                "description": "Get detailed information about a specific author.",
                "payload": {
                    "author_id": "ID of the author to retrieve"
                }
            },
            "trending_topics": {
                "route": "/trending",
                "method": "GET",
                "name": "Trending AI Topics",
                "description": "Get currently trending topics in AI research.",
                "payload": {
                    "period": "Optional time period ('week', 'month', 'year')",
                    "limit": "Optional maximum number of topics to return (default: 10)"
                }
            },
            "paper_citations": {
                "route": "/paper/{paper_id}/citations",
                "method": "GET",
                "name": "Paper Citations",
                "description": "Get papers that cite a specific paper.",
                "payload": {
                    "paper_id": "ID of the paper",
                    "limit": "Optional maximum number of citations to return (default: 10)",
                    "offset": "Optional offset for pagination"
                }
            },
            "paper_references": {
                "route": "/paper/{paper_id}/references",
                "method": "GET",
                "name": "Paper References",
                "description": "Get papers that are referenced by a specific paper.",
                "payload": {
                    "paper_id": "ID of the paper",
                    "limit": "Optional maximum number of references to return (default: 10)",
                    "offset": "Optional offset for pagination"
                }
            },
            "similar_papers": {
                "route": "/paper/{paper_id}/similar",
                "method": "GET",
                "name": "Similar Papers",
                "description": "Get papers that are similar to a specific paper.",
                "payload": {
                    "paper_id": "ID of the paper",
                    "limit": "Optional maximum number of similar papers to return (default: 10)",
                    "offset": "Optional offset for pagination"
                }
            },
            "conference_papers": {
                "route": "/venue/{venue_id}/papers",
                "method": "GET",
                "name": "Conference Papers",
                "description": "Get papers published at a specific conference or journal.",
                "payload": {
                    "venue_id": "ID of the venue (conference or journal)",
                    "year": "Optional filter by publication year",
                    "limit": "Optional maximum number of papers to return (default: 10)",
                    "offset": "Optional offset for pagination"
                }
            },
            "author_papers": {
                "route": "/author/{author_id}/papers",
                "method": "GET",
                "name": "Author Papers",
                "description": "Get papers published by a specific author.",
                "payload": {
                    "author_id": "ID of the author",
                    "limit": "Optional maximum number of papers to return (default: 10)",
                    "offset": "Optional offset for pagination"
                }
            },
            "author_collaborators": {
                "route": "/author/{author_id}/collaborators",
                "method": "GET",
                "name": "Author Collaborators",
                "description": "Get collaborators of a specific author.",
                "payload": {
                    "author_id": "ID of the author",
                    "limit": "Optional maximum number of collaborators to return (default: 10)",
                    "offset": "Optional offset for pagination"
                }
            }
        }
        
        # Use Semantic Scholar API as the base URL
        base_url = "https://api.semanticscholar.org/v1"
        super().__init__(base_url, endpoints)
        
        # Override call_endpoint to handle path parameters
        self.original_call_endpoint = self.call_endpoint
        self.call_endpoint = self.ai_research_call_endpoint
    
    def ai_research_call_endpoint(self, route: str, payload: Dict = None):
        """
        Custom call_endpoint method to handle path parameters.
        """
        if route.startswith("/"):
            route = route[1:]

        endpoint = self.endpoints.get(route)
        if not endpoint:
            raise ValueError(f"Endpoint {route} not found")
        
        # Format route with payload parameters
        api_route = endpoint["route"]
        if "{" in api_route:
            # Extract path parameters from the route
            path_params = [param.split("}")[0] for param in api_route.split("{")[1:]]
            
            # Replace path parameters with values from payload
            for param in path_params:
                if param in payload:
                    api_route = api_route.replace(f"{{{param}}}", payload[param])
                    # Remove used parameters from payload
                    del payload[param]
        
        # Update the route in the endpoints dictionary
        self.endpoints[route]["route"] = api_route
        
        # Call the original method
        return self.original_call_endpoint(route, payload)


if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    tool = AIResearchProvider()

    # Example for searching papers
    search_papers = tool.call_endpoint(
        route="search_papers",
        payload={
            "query": "transformer architecture",
            "limit": 5
        }
    )
    print("Search Papers:", search_papers)
    
    # Example for getting paper details
    get_paper = tool.call_endpoint(
        route="get_paper",
        payload={
            "paper_id": "1706.03762"  # Attention Is All You Need paper
        }
    )
    print("Get Paper:", get_paper)
    
    # Example for getting trending topics
    trending_topics = tool.call_endpoint(
        route="trending_topics",
        payload={
            "period": "month",
            "limit": 5
        }
    )
    print("Trending Topics:", trending_topics)
