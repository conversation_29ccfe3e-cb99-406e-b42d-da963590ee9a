"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-css@6.3.1";
exports.ids = ["vendor-chunks/@codemirror+lang-css@6.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-css@6.3.1/node_modules/@codemirror/lang-css/dist/index.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-css@6.3.1/node_modules/@codemirror/lang-css/dist/index.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: () => (/* binding */ css),\n/* harmony export */   cssCompletionSource: () => (/* binding */ cssCompletionSource),\n/* harmony export */   cssLanguage: () => (/* binding */ cssLanguage),\n/* harmony export */   defineCSSCompletionSource: () => (/* binding */ defineCSSCompletionSource)\n/* harmony export */ });\n/* harmony import */ var _lezer_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/css */ \"(ssr)/./node_modules/.pnpm/@lezer+css@1.1.11/node_modules/@lezer/css/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n\n\n\n\nlet _properties = null;\nfunction properties() {\n    if (!_properties && typeof document == \"object\" && document.body) {\n        let { style } = document.body, names = [], seen = new Set;\n        for (let prop in style)\n            if (prop != \"cssText\" && prop != \"cssFloat\") {\n                if (typeof style[prop] == \"string\") {\n                    if (/[A-Z]/.test(prop))\n                        prop = prop.replace(/[A-Z]/g, ch => \"-\" + ch.toLowerCase());\n                    if (!seen.has(prop)) {\n                        names.push(prop);\n                        seen.add(prop);\n                    }\n                }\n            }\n        _properties = names.sort().map(name => ({ type: \"property\", label: name, apply: name + \": \" }));\n    }\n    return _properties || [];\n}\nconst pseudoClasses = /*@__PURE__*/[\n    \"active\", \"after\", \"any-link\", \"autofill\", \"backdrop\", \"before\",\n    \"checked\", \"cue\", \"default\", \"defined\", \"disabled\", \"empty\",\n    \"enabled\", \"file-selector-button\", \"first\", \"first-child\",\n    \"first-letter\", \"first-line\", \"first-of-type\", \"focus\",\n    \"focus-visible\", \"focus-within\", \"fullscreen\", \"has\", \"host\",\n    \"host-context\", \"hover\", \"in-range\", \"indeterminate\", \"invalid\",\n    \"is\", \"lang\", \"last-child\", \"last-of-type\", \"left\", \"link\", \"marker\",\n    \"modal\", \"not\", \"nth-child\", \"nth-last-child\", \"nth-last-of-type\",\n    \"nth-of-type\", \"only-child\", \"only-of-type\", \"optional\", \"out-of-range\",\n    \"part\", \"placeholder\", \"placeholder-shown\", \"read-only\", \"read-write\",\n    \"required\", \"right\", \"root\", \"scope\", \"selection\", \"slotted\", \"target\",\n    \"target-text\", \"valid\", \"visited\", \"where\"\n].map(name => ({ type: \"class\", label: name }));\nconst values = /*@__PURE__*/[\n    \"above\", \"absolute\", \"activeborder\", \"additive\", \"activecaption\", \"after-white-space\",\n    \"ahead\", \"alias\", \"all\", \"all-scroll\", \"alphabetic\", \"alternate\", \"always\",\n    \"antialiased\", \"appworkspace\", \"asterisks\", \"attr\", \"auto\", \"auto-flow\", \"avoid\", \"avoid-column\",\n    \"avoid-page\", \"avoid-region\", \"axis-pan\", \"background\", \"backwards\", \"baseline\", \"below\",\n    \"bidi-override\", \"blink\", \"block\", \"block-axis\", \"bold\", \"bolder\", \"border\", \"border-box\",\n    \"both\", \"bottom\", \"break\", \"break-all\", \"break-word\", \"bullets\", \"button\", \"button-bevel\",\n    \"buttonface\", \"buttonhighlight\", \"buttonshadow\", \"buttontext\", \"calc\", \"capitalize\",\n    \"caps-lock-indicator\", \"caption\", \"captiontext\", \"caret\", \"cell\", \"center\", \"checkbox\", \"circle\",\n    \"cjk-decimal\", \"clear\", \"clip\", \"close-quote\", \"col-resize\", \"collapse\", \"color\", \"color-burn\",\n    \"color-dodge\", \"column\", \"column-reverse\", \"compact\", \"condensed\", \"contain\", \"content\",\n    \"contents\", \"content-box\", \"context-menu\", \"continuous\", \"copy\", \"counter\", \"counters\", \"cover\",\n    \"crop\", \"cross\", \"crosshair\", \"currentcolor\", \"cursive\", \"cyclic\", \"darken\", \"dashed\", \"decimal\",\n    \"decimal-leading-zero\", \"default\", \"default-button\", \"dense\", \"destination-atop\", \"destination-in\",\n    \"destination-out\", \"destination-over\", \"difference\", \"disc\", \"discard\", \"disclosure-closed\",\n    \"disclosure-open\", \"document\", \"dot-dash\", \"dot-dot-dash\", \"dotted\", \"double\", \"down\", \"e-resize\",\n    \"ease\", \"ease-in\", \"ease-in-out\", \"ease-out\", \"element\", \"ellipse\", \"ellipsis\", \"embed\", \"end\",\n    \"ethiopic-abegede-gez\", \"ethiopic-halehame-aa-er\", \"ethiopic-halehame-gez\", \"ew-resize\", \"exclusion\",\n    \"expanded\", \"extends\", \"extra-condensed\", \"extra-expanded\", \"fantasy\", \"fast\", \"fill\", \"fill-box\",\n    \"fixed\", \"flat\", \"flex\", \"flex-end\", \"flex-start\", \"footnotes\", \"forwards\", \"from\",\n    \"geometricPrecision\", \"graytext\", \"grid\", \"groove\", \"hand\", \"hard-light\", \"help\", \"hidden\", \"hide\",\n    \"higher\", \"highlight\", \"highlighttext\", \"horizontal\", \"hsl\", \"hsla\", \"hue\", \"icon\", \"ignore\",\n    \"inactiveborder\", \"inactivecaption\", \"inactivecaptiontext\", \"infinite\", \"infobackground\", \"infotext\",\n    \"inherit\", \"initial\", \"inline\", \"inline-axis\", \"inline-block\", \"inline-flex\", \"inline-grid\",\n    \"inline-table\", \"inset\", \"inside\", \"intrinsic\", \"invert\", \"italic\", \"justify\", \"keep-all\",\n    \"landscape\", \"large\", \"larger\", \"left\", \"level\", \"lighter\", \"lighten\", \"line-through\", \"linear\",\n    \"linear-gradient\", \"lines\", \"list-item\", \"listbox\", \"listitem\", \"local\", \"logical\", \"loud\", \"lower\",\n    \"lower-hexadecimal\", \"lower-latin\", \"lower-norwegian\", \"lowercase\", \"ltr\", \"luminosity\", \"manipulation\",\n    \"match\", \"matrix\", \"matrix3d\", \"medium\", \"menu\", \"menutext\", \"message-box\", \"middle\", \"min-intrinsic\",\n    \"mix\", \"monospace\", \"move\", \"multiple\", \"multiple_mask_images\", \"multiply\", \"n-resize\", \"narrower\",\n    \"ne-resize\", \"nesw-resize\", \"no-close-quote\", \"no-drop\", \"no-open-quote\", \"no-repeat\", \"none\",\n    \"normal\", \"not-allowed\", \"nowrap\", \"ns-resize\", \"numbers\", \"numeric\", \"nw-resize\", \"nwse-resize\",\n    \"oblique\", \"opacity\", \"open-quote\", \"optimizeLegibility\", \"optimizeSpeed\", \"outset\", \"outside\",\n    \"outside-shape\", \"overlay\", \"overline\", \"padding\", \"padding-box\", \"painted\", \"page\", \"paused\",\n    \"perspective\", \"pinch-zoom\", \"plus-darker\", \"plus-lighter\", \"pointer\", \"polygon\", \"portrait\",\n    \"pre\", \"pre-line\", \"pre-wrap\", \"preserve-3d\", \"progress\", \"push-button\", \"radial-gradient\", \"radio\",\n    \"read-only\", \"read-write\", \"read-write-plaintext-only\", \"rectangle\", \"region\", \"relative\", \"repeat\",\n    \"repeating-linear-gradient\", \"repeating-radial-gradient\", \"repeat-x\", \"repeat-y\", \"reset\", \"reverse\",\n    \"rgb\", \"rgba\", \"ridge\", \"right\", \"rotate\", \"rotate3d\", \"rotateX\", \"rotateY\", \"rotateZ\", \"round\",\n    \"row\", \"row-resize\", \"row-reverse\", \"rtl\", \"run-in\", \"running\", \"s-resize\", \"sans-serif\", \"saturation\",\n    \"scale\", \"scale3d\", \"scaleX\", \"scaleY\", \"scaleZ\", \"screen\", \"scroll\", \"scrollbar\", \"scroll-position\",\n    \"se-resize\", \"self-start\", \"self-end\", \"semi-condensed\", \"semi-expanded\", \"separate\", \"serif\", \"show\",\n    \"single\", \"skew\", \"skewX\", \"skewY\", \"skip-white-space\", \"slide\", \"slider-horizontal\",\n    \"slider-vertical\", \"sliderthumb-horizontal\", \"sliderthumb-vertical\", \"slow\", \"small\", \"small-caps\",\n    \"small-caption\", \"smaller\", \"soft-light\", \"solid\", \"source-atop\", \"source-in\", \"source-out\",\n    \"source-over\", \"space\", \"space-around\", \"space-between\", \"space-evenly\", \"spell-out\", \"square\", \"start\",\n    \"static\", \"status-bar\", \"stretch\", \"stroke\", \"stroke-box\", \"sub\", \"subpixel-antialiased\", \"svg_masks\",\n    \"super\", \"sw-resize\", \"symbolic\", \"symbols\", \"system-ui\", \"table\", \"table-caption\", \"table-cell\",\n    \"table-column\", \"table-column-group\", \"table-footer-group\", \"table-header-group\", \"table-row\",\n    \"table-row-group\", \"text\", \"text-bottom\", \"text-top\", \"textarea\", \"textfield\", \"thick\", \"thin\",\n    \"threeddarkshadow\", \"threedface\", \"threedhighlight\", \"threedlightshadow\", \"threedshadow\", \"to\", \"top\",\n    \"transform\", \"translate\", \"translate3d\", \"translateX\", \"translateY\", \"translateZ\", \"transparent\",\n    \"ultra-condensed\", \"ultra-expanded\", \"underline\", \"unidirectional-pan\", \"unset\", \"up\", \"upper-latin\",\n    \"uppercase\", \"url\", \"var\", \"vertical\", \"vertical-text\", \"view-box\", \"visible\", \"visibleFill\",\n    \"visiblePainted\", \"visibleStroke\", \"visual\", \"w-resize\", \"wait\", \"wave\", \"wider\", \"window\", \"windowframe\",\n    \"windowtext\", \"words\", \"wrap\", \"wrap-reverse\", \"x-large\", \"x-small\", \"xor\", \"xx-large\", \"xx-small\"\n].map(name => ({ type: \"keyword\", label: name })).concat(/*@__PURE__*/[\n    \"aliceblue\", \"antiquewhite\", \"aqua\", \"aquamarine\", \"azure\", \"beige\",\n    \"bisque\", \"black\", \"blanchedalmond\", \"blue\", \"blueviolet\", \"brown\",\n    \"burlywood\", \"cadetblue\", \"chartreuse\", \"chocolate\", \"coral\", \"cornflowerblue\",\n    \"cornsilk\", \"crimson\", \"cyan\", \"darkblue\", \"darkcyan\", \"darkgoldenrod\",\n    \"darkgray\", \"darkgreen\", \"darkkhaki\", \"darkmagenta\", \"darkolivegreen\",\n    \"darkorange\", \"darkorchid\", \"darkred\", \"darksalmon\", \"darkseagreen\",\n    \"darkslateblue\", \"darkslategray\", \"darkturquoise\", \"darkviolet\",\n    \"deeppink\", \"deepskyblue\", \"dimgray\", \"dodgerblue\", \"firebrick\",\n    \"floralwhite\", \"forestgreen\", \"fuchsia\", \"gainsboro\", \"ghostwhite\",\n    \"gold\", \"goldenrod\", \"gray\", \"grey\", \"green\", \"greenyellow\", \"honeydew\",\n    \"hotpink\", \"indianred\", \"indigo\", \"ivory\", \"khaki\", \"lavender\",\n    \"lavenderblush\", \"lawngreen\", \"lemonchiffon\", \"lightblue\", \"lightcoral\",\n    \"lightcyan\", \"lightgoldenrodyellow\", \"lightgray\", \"lightgreen\", \"lightpink\",\n    \"lightsalmon\", \"lightseagreen\", \"lightskyblue\", \"lightslategray\",\n    \"lightsteelblue\", \"lightyellow\", \"lime\", \"limegreen\", \"linen\", \"magenta\",\n    \"maroon\", \"mediumaquamarine\", \"mediumblue\", \"mediumorchid\", \"mediumpurple\",\n    \"mediumseagreen\", \"mediumslateblue\", \"mediumspringgreen\", \"mediumturquoise\",\n    \"mediumvioletred\", \"midnightblue\", \"mintcream\", \"mistyrose\", \"moccasin\",\n    \"navajowhite\", \"navy\", \"oldlace\", \"olive\", \"olivedrab\", \"orange\", \"orangered\",\n    \"orchid\", \"palegoldenrod\", \"palegreen\", \"paleturquoise\", \"palevioletred\",\n    \"papayawhip\", \"peachpuff\", \"peru\", \"pink\", \"plum\", \"powderblue\",\n    \"purple\", \"rebeccapurple\", \"red\", \"rosybrown\", \"royalblue\", \"saddlebrown\",\n    \"salmon\", \"sandybrown\", \"seagreen\", \"seashell\", \"sienna\", \"silver\", \"skyblue\",\n    \"slateblue\", \"slategray\", \"snow\", \"springgreen\", \"steelblue\", \"tan\",\n    \"teal\", \"thistle\", \"tomato\", \"turquoise\", \"violet\", \"wheat\", \"white\",\n    \"whitesmoke\", \"yellow\", \"yellowgreen\"\n].map(name => ({ type: \"constant\", label: name })));\nconst tags = /*@__PURE__*/[\n    \"a\", \"abbr\", \"address\", \"article\", \"aside\", \"b\", \"bdi\", \"bdo\", \"blockquote\", \"body\",\n    \"br\", \"button\", \"canvas\", \"caption\", \"cite\", \"code\", \"col\", \"colgroup\", \"dd\", \"del\",\n    \"details\", \"dfn\", \"dialog\", \"div\", \"dl\", \"dt\", \"em\", \"figcaption\", \"figure\", \"footer\",\n    \"form\", \"header\", \"hgroup\", \"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\", \"hr\", \"html\", \"i\", \"iframe\",\n    \"img\", \"input\", \"ins\", \"kbd\", \"label\", \"legend\", \"li\", \"main\", \"meter\", \"nav\", \"ol\", \"output\",\n    \"p\", \"pre\", \"ruby\", \"section\", \"select\", \"small\", \"source\", \"span\", \"strong\", \"sub\", \"summary\",\n    \"sup\", \"table\", \"tbody\", \"td\", \"template\", \"textarea\", \"tfoot\", \"th\", \"thead\", \"tr\", \"u\", \"ul\"\n].map(name => ({ type: \"type\", label: name }));\nconst atRules = /*@__PURE__*/[\n    \"@charset\", \"@color-profile\", \"@container\", \"@counter-style\", \"@font-face\", \"@font-feature-values\",\n    \"@font-palette-values\", \"@import\", \"@keyframes\", \"@layer\", \"@media\", \"@namespace\", \"@page\",\n    \"@position-try\", \"@property\", \"@scope\", \"@starting-style\", \"@supports\", \"@view-transition\"\n].map(label => ({ type: \"keyword\", label }));\nconst identifier = /^(\\w[\\w-]*|-\\w[\\w-]*|)$/, variable = /^-(-[\\w-]*)?$/;\nfunction isVarArg(node, doc) {\n    var _a;\n    if (node.name == \"(\" || node.type.isError)\n        node = node.parent || node;\n    if (node.name != \"ArgList\")\n        return false;\n    let callee = (_a = node.parent) === null || _a === void 0 ? void 0 : _a.firstChild;\n    if ((callee === null || callee === void 0 ? void 0 : callee.name) != \"Callee\")\n        return false;\n    return doc.sliceString(callee.from, callee.to) == \"var\";\n}\nconst VariablesByNode = /*@__PURE__*/new _lezer_common__WEBPACK_IMPORTED_MODULE_1__.NodeWeakMap();\nconst declSelector = [\"Declaration\"];\nfunction astTop(node) {\n    for (let cur = node;;) {\n        if (cur.type.isTop)\n            return cur;\n        if (!(cur = cur.parent))\n            return node;\n    }\n}\nfunction variableNames(doc, node, isVariable) {\n    if (node.to - node.from > 4096) {\n        let known = VariablesByNode.get(node);\n        if (known)\n            return known;\n        let result = [], seen = new Set, cursor = node.cursor(_lezer_common__WEBPACK_IMPORTED_MODULE_1__.IterMode.IncludeAnonymous);\n        if (cursor.firstChild())\n            do {\n                for (let option of variableNames(doc, cursor.node, isVariable))\n                    if (!seen.has(option.label)) {\n                        seen.add(option.label);\n                        result.push(option);\n                    }\n            } while (cursor.nextSibling());\n        VariablesByNode.set(node, result);\n        return result;\n    }\n    else {\n        let result = [], seen = new Set;\n        node.cursor().iterate(node => {\n            var _a;\n            if (isVariable(node) && node.matchContext(declSelector) && ((_a = node.node.nextSibling) === null || _a === void 0 ? void 0 : _a.name) == \":\") {\n                let name = doc.sliceString(node.from, node.to);\n                if (!seen.has(name)) {\n                    seen.add(name);\n                    result.push({ label: name, type: \"variable\" });\n                }\n            }\n        });\n        return result;\n    }\n}\n/**\nCreate a completion source for a CSS dialect, providing a\npredicate for determining what kind of syntax node can act as a\ncompletable variable. This is used by language modes like Sass and\nLess to reuse this package's completion logic.\n*/\nconst defineCSSCompletionSource = (isVariable) => context => {\n    let { state, pos } = context, node = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.syntaxTree)(state).resolveInner(pos, -1);\n    let isDash = node.type.isError && node.from == node.to - 1 && state.doc.sliceString(node.from, node.to) == \"-\";\n    if (node.name == \"PropertyName\" ||\n        (isDash || node.name == \"TagName\") && /^(Block|Styles)$/.test(node.resolve(node.to).name))\n        return { from: node.from, options: properties(), validFor: identifier };\n    if (node.name == \"ValueName\")\n        return { from: node.from, options: values, validFor: identifier };\n    if (node.name == \"PseudoClassName\")\n        return { from: node.from, options: pseudoClasses, validFor: identifier };\n    if (isVariable(node) || (context.explicit || isDash) && isVarArg(node, state.doc))\n        return { from: isVariable(node) || isDash ? node.from : pos,\n            options: variableNames(state.doc, astTop(node), isVariable),\n            validFor: variable };\n    if (node.name == \"TagName\") {\n        for (let { parent } = node; parent; parent = parent.parent)\n            if (parent.name == \"Block\")\n                return { from: node.from, options: properties(), validFor: identifier };\n        return { from: node.from, options: tags, validFor: identifier };\n    }\n    if (node.name == \"AtKeyword\")\n        return { from: node.from, options: atRules, validFor: identifier };\n    if (!context.explicit)\n        return null;\n    let above = node.resolve(pos), before = above.childBefore(pos);\n    if (before && before.name == \":\" && above.name == \"PseudoClassSelector\")\n        return { from: pos, options: pseudoClasses, validFor: identifier };\n    if (before && before.name == \":\" && above.name == \"Declaration\" || above.name == \"ArgList\")\n        return { from: pos, options: values, validFor: identifier };\n    if (above.name == \"Block\" || above.name == \"Styles\")\n        return { from: pos, options: properties(), validFor: identifier };\n    return null;\n};\n/**\nCSS property, variable, and value keyword completion source.\n*/\nconst cssCompletionSource = /*@__PURE__*/defineCSSCompletionSource(n => n.name == \"VariableName\");\n\n/**\nA language provider based on the [Lezer CSS\nparser](https://github.com/lezer-parser/css), extended with\nhighlighting and indentation information.\n*/\nconst cssLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LRLanguage.define({\n    name: \"css\",\n    parser: /*@__PURE__*/_lezer_css__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.indentNodeProp.add({\n                Declaration: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.continuedIndent)()\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldNodeProp.add({\n                \"Block KeyframeList\": _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldInside\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"/*\", close: \"*/\" } },\n        indentOnInput: /^\\s*\\}$/,\n        wordChars: \"-\"\n    }\n});\n/**\nLanguage support for CSS.\n*/\nfunction css() {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LanguageSupport(cssLanguage, cssLanguage.data.of({ autocomplete: cssCompletionSource }));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-css@6.3.1/node_modules/@codemirror/lang-css/dist/index.js\n");

/***/ })

};
;