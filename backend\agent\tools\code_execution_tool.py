import json
from typing import Optional

from agentpress.tool import Too<PERSON>, Too<PERSON><PERSON><PERSON><PERSON>, openapi_schema, xml_schema
from sandbox.code_executor import execute_code, CodeExecutionRequest
from utils.logger import logger


class CodeExecutionTool(Tool):
    """Tool for executing Python code using e2b code interpreter."""

    def __init__(self):
        super().__init__()

    @openapi_schema({
        "name": "execute_python_code",
        "description": "Execute Python code using e2b code interpreter. Perfect for data analysis, calculations, visualizations, and any Python programming tasks.",
        "parameters": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "description": "The Python code to execute"
                },
                "timeout": {
                    "type": "integer",
                    "description": "Timeout in milliseconds (default: 30000, max: 60000)",
                    "default": 30000
                }
            },
            "required": ["code"]
        }
    })
    @xml_schema(
        tag_name="execute-python-code",
        mappings=[
            {"param_name": "code", "node_type": "text", "path": "."},
            {"param_name": "timeout", "node_type": "attribute", "path": ".", "required": False}
        ],
        example='''
        <!-- Execute simple Python calculations -->
        <execute-python-code>
print("Hello from Python!")
result = 2 + 2
print(f"2 + 2 = {result}")
        </execute-python-code>

        <!-- Execute data analysis with pandas -->
        <execute-python-code>
import pandas as pd
import numpy as np

# Create sample data
data = {
    'Name': ['Alice', 'Bob', 'Charlie'],
    'Age': [25, 30, 35],
    'Score': [85.5, 92.0, 78.5]
}

df = pd.DataFrame(data)
print("DataFrame:")
print(df)
print(f"Average age: {df['Age'].mean()}")
        </execute-python-code>

        <!-- Execute visualization with matplotlib -->
        <execute-python-code>
import matplotlib.pyplot as plt
import numpy as np

x = np.linspace(0, 10, 100)
y = np.sin(x)

plt.figure(figsize=(8, 6))
plt.plot(x, y, 'b-', linewidth=2)
plt.xlabel('x')
plt.ylabel('sin(x)')
plt.title('Sine Wave')
plt.grid(True)
plt.show()
        </execute-python-code>

        <!-- Execute code with custom timeout -->
        <execute-python-code timeout="60000">
import time
print("Starting long calculation...")
time.sleep(2)
print("Calculation complete!")
        </execute-python-code>
        '''
    )
    async def execute_python_code(self, code: str, timeout: int = 30000) -> ToolResult:
        """Execute Python code using e2b code interpreter.

        Args:
            code: The Python code to execute
            timeout: Timeout in milliseconds (default: 30000, max: 60000)

        Returns:
            ToolResult with execution output or error
        """
        try:
            logger.info(f"Executing Python code via e2b code interpreter")
            logger.debug(f"Code to execute: {code[:200]}...")

            # Validate timeout
            timeout = min(max(timeout, 1000), 60000)  # Between 1s and 60s

            # Create execution request
            request = CodeExecutionRequest(
                code=code,
                language="python",
                timeout=timeout
            )

            # Execute the code
            response = await execute_code(request)

            # Check for errors
            if response.error:
                logger.warning(f"Code execution failed: {response.error}")

                # Create structured error response
                error_output = {
                    "text_output": response.output or "",
                    "error": response.error,
                    "images": response.images or [],
                    "image_count": len(response.images) if response.images else 0,
                    "files": response.files or [],
                    "execution_time": response.execution_time,
                    "exit_code": response.exitCode,
                    "language": "python",
                    "success": False
                }

                # Include structured data even for errors
                error_message = f"Code execution failed: {response.error}"
                if response.output:
                    error_message += f"\n\nOutput before error:\n{response.output}"

                error_message += f"\n\n__CODE_EXECUTION_RESULT__\n{json.dumps(error_output)}\n__CODE_EXECUTION_RESULT_END__"

                return self.fail_response(error_message, error_output)

            # Success
            logger.info(f"Code executed successfully in {response.execution_time:.2f}s")

            # Prepare enhanced output with structured data
            output = response.output or "Code executed successfully (no output)"

            # Create comprehensive structured response
            structured_output = {
                "text_output": output,
                "images": response.images or [],
                "image_count": len(response.images) if response.images else 0,
                "files": response.files or [],
                "execution_time": response.execution_time,
                "exit_code": response.exitCode,
                "language": "python",
                "success": True
            }

            # Include the structured data in the output for frontend parsing
            output += f"\n\n__CODE_EXECUTION_RESULT__\n{json.dumps(structured_output)}\n__CODE_EXECUTION_RESULT_END__"

            # Add execution summary
            if response.images:
                output += f"\n\n🖼️ Generated {len(response.images)} visualization(s)"
            if response.execution_time:
                output += f"\n⏱️ Execution time: {response.execution_time:.2f}s"

            return self.success_response(output)

        except Exception as e:
            logger.error(f"Error executing Python code: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to execute code: {str(e)}")

    @openapi_schema({
        "name": "execute_code_with_files",
        "description": "Execute Python code that works with files, data analysis, or generates outputs like plots.",
        "parameters": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "description": "The Python code to execute"
                },
                "description": {
                    "type": "string",
                    "description": "Description of what the code does"
                },
                "timeout": {
                    "type": "integer",
                    "description": "Timeout in milliseconds (default: 45000)",
                    "default": 45000
                }
            },
            "required": ["code", "description"]
        }
    })
    @xml_schema(
        tag_name="execute-code-with-files",
        mappings=[
            {"param_name": "code", "node_type": "element", "path": "code"},
            {"param_name": "description", "node_type": "element", "path": "description"},
            {"param_name": "timeout", "node_type": "attribute", "path": ".", "required": False}
        ],
        example='''
        <!-- Execute data analysis code -->
        <execute-code-with-files>
            <description>Analyze sales data and create visualization</description>
            <code>
import pandas as pd
import matplotlib.pyplot as plt

# Create sample sales data
sales_data = {
    'Month': ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    'Sales': [1000, 1200, 1100, 1300, 1500]
}

df = pd.DataFrame(sales_data)
print("Sales Data:")
print(df)

# Create visualization
plt.figure(figsize=(10, 6))
plt.plot(df['Month'], df['Sales'], marker='o', linewidth=2)
plt.title('Monthly Sales Trend')
plt.xlabel('Month')
plt.ylabel('Sales ($)')
plt.grid(True)
plt.show()

print(f"Total sales: ${df['Sales'].sum()}")
print(f"Average monthly sales: ${df['Sales'].mean():.2f}")
            </code>
        </execute-code-with-files>
        '''
    )
    async def execute_code_with_files(self, code: str, description: str, timeout: int = 45000) -> ToolResult:
        """Execute Python code with file operations and data analysis.

        Args:
            code: The Python code to execute
            description: Description of what the code does
            timeout: Timeout in milliseconds (default: 45000)

        Returns:
            ToolResult with execution output or error
        """
        try:
            logger.info(f"Executing Python code with files: {description}")
            
            # Use the regular execute_python_code method
            return await self.execute_python_code(code, timeout)

        except Exception as e:
            logger.error(f"Error executing code with files: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to execute code: {str(e)}")
