"use client";

import React from 'react';
import { Participant } from './types';
import { ChatPanel } from './ChatPanel';
import { ParticipantsList } from './ParticipantsList';

interface SidePanelProps {
  isChatOpen: boolean;
  isParticipantsOpen: boolean;
  participants: Participant[];
}

export function SidePanel({
  isChatOpen,
  isParticipantsOpen,
  participants
}: SidePanelProps) {
  // Don't render if neither panel is open
  if (!isChatOpen && !isParticipantsOpen) {
    return null;
  }

  return (
    <div className="w-80 border-l bg-card overflow-hidden flex flex-col">
      {isChatOpen && <ChatPanel participants={participants} />}
      {isParticipantsOpen && <ParticipantsList participants={participants} />}
    </div>
  );
}