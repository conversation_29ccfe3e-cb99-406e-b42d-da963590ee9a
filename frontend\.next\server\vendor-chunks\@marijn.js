"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@marijn";
exports.ids = ["vendor-chunks/@marijn"];
exports.modules = {

/***/ "(ssr)/./node_modules/@marijn/find-cluster-break/src/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@marijn/find-cluster-break/src/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findClusterBreak: () => (/* binding */ findClusterBreak),\n/* harmony export */   isExtendingChar: () => (/* binding */ isExtendingChar)\n/* harmony export */ });\n// These are filled with ranges (rangeFrom[i] up to but not including\n// rangeTo[i]) of code points that count as extending characters.\nlet rangeFrom = [], rangeTo = []\n\n;(() => {\n  // Compressed representation of the Grapheme_Cluster_Break=Extend\n  // information from\n  // http://www.unicode.org/Public/16.0.0/ucd/auxiliary/GraphemeBreakProperty.txt.\n  // Each pair of elements represents a range, as an offet from the\n  // previous range and a length. Numbers are in base-36, with the empty\n  // string being a shorthand for 1.\n  let numbers = \"lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o\".split(\",\").map(s => s ? parseInt(s, 36) : 1)\n  for (let i = 0, n = 0; i < numbers.length; i++)\n    (i % 2 ? rangeTo : rangeFrom).push(n = n + numbers[i])\n})()\n\nfunction isExtendingChar(code) {\n  if (code < 768) return false\n  for (let from = 0, to = rangeFrom.length;;) {\n    let mid = (from + to) >> 1\n    if (code < rangeFrom[mid]) to = mid\n    else if (code >= rangeTo[mid]) from = mid + 1\n    else return true\n    if (from == to) return false\n  }\n}\n\nfunction isRegionalIndicator(code) {\n  return code >= 0x1F1E6 && code <= 0x1F1FF\n}\n\nfunction check(code) {\n  for (let i = 0; i < rangeFrom.length; i++) {\n    if (rangeTo[i] > code) return rangeFrom[i] <= code\n  }\n  return false\n}\n\nconst ZWJ = 0x200d\n\nfunction findClusterBreak(str, pos, forward = true, includeExtending = true) {\n  return (forward ? nextClusterBreak : prevClusterBreak)(str, pos, includeExtending)\n}\n\nfunction nextClusterBreak(str, pos, includeExtending) {\n  if (pos == str.length) return pos\n  // If pos is in the middle of a surrogate pair, move to its start\n  if (pos && surrogateLow(str.charCodeAt(pos)) && surrogateHigh(str.charCodeAt(pos - 1))) pos--\n  let prev = codePointAt(str, pos)\n  pos += codePointSize(prev)\n  while (pos < str.length) {\n    let next = codePointAt(str, pos)\n    if (prev == ZWJ || next == ZWJ || includeExtending && isExtendingChar(next)) {\n      pos += codePointSize(next)\n      prev = next\n    } else if (isRegionalIndicator(next)) {\n      let countBefore = 0, i = pos - 2\n      while (i >= 0 && isRegionalIndicator(codePointAt(str, i))) { countBefore++; i -= 2 }\n      if (countBefore % 2 == 0) break\n      else pos += 2\n    } else {\n      break\n    }\n  }\n  return pos\n}\n\nfunction prevClusterBreak(str, pos, includeExtending) {\n  while (pos > 0) {\n    let found = nextClusterBreak(str, pos - 2, includeExtending)\n    if (found < pos) return found\n    pos--\n  }\n  return 0\n}\n\nfunction codePointAt(str, pos) {\n  let code0 = str.charCodeAt(pos)\n  if (!surrogateHigh(code0) || pos + 1 == str.length) return code0\n  let code1 = str.charCodeAt(pos + 1)\n  if (!surrogateLow(code1)) return code0\n  return ((code0 - 0xd800) << 10) + (code1 - 0xdc00) + 0x10000\n}\n\nfunction surrogateLow(ch) { return ch >= 0xDC00 && ch < 0xE000 }\nfunction surrogateHigh(ch) { return ch >= 0xD800 && ch < 0xDC00 }\nfunction codePointSize(code) { return code < 0x10000 ? 1 : 2 }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@marijn/find-cluster-break/src/index.js\n");

/***/ })

};
;