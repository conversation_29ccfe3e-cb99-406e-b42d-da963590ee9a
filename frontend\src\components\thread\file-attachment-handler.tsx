"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Paperclip, X, FileText } from "lucide-react";
import { FileMessage } from "./file-message";
import { toast } from "sonner";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON>ip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface FileAttachmentHandlerProps {
  onFileAttached: (file: File, content: string) => void;
  disabled?: boolean;
}

export function FileAttachmentHandler({
  onFileAttached,
  disabled = false
}: FileAttachmentHandlerProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileContent, setFileContent] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const handleFileButtonClick = () => {
    fileInputRef.current?.click();
  };
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Check file size (limit to 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size exceeds 5MB limit");
      return;
    }
    
    // Check if it's a text file
    if (file.type.startsWith('text/') || 
        file.name.endsWith('.js') || 
        file.name.endsWith('.jsx') || 
        file.name.endsWith('.ts') || 
        file.name.endsWith('.tsx') || 
        file.name.endsWith('.json') || 
        file.name.endsWith('.md') || 
        file.name.endsWith('.css') || 
        file.name.endsWith('.html')) {
      
      const reader = new FileReader();
      reader.onload = (event) => {
        const content = event.target?.result as string;
        setFileContent(content);
        setSelectedFile(file);
        onFileAttached(file, content);
      };
      reader.readAsText(file);
    } else {
      toast.error("Only text files are supported");
    }
    
    // Reset the input
    e.target.value = '';
  };
  
  const clearSelectedFile = () => {
    setSelectedFile(null);
    setFileContent(null);
  };
  
  return (
    <div>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
        accept=".txt,.js,.jsx,.ts,.tsx,.json,.md,.css,.html,.py,.java,.c,.cpp,.h,.cs,.go,.rs,.php,.rb,.sh"
      />
      
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              type="button"
              onClick={handleFileButtonClick}
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-md text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800"
              disabled={disabled}
            >
              <Paperclip className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="top">
            <p>Attach file</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      
      {selectedFile && fileContent && (
        <div className="mt-2 p-2 border rounded-md bg-muted/30 flex items-center justify-between">
          <div className="flex items-center">
            <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
            <span className="text-sm truncate max-w-[200px]">{selectedFile.name}</span>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={clearSelectedFile}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      )}
    </div>
  );
}
