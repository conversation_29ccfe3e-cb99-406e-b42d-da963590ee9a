"use client";

import React from 'react';
import { AgentRoleCard } from '@/components/project/agent-role-card';
import { Button } from '@/components/ui/button';

import { agentRoles, templatePresets } from './agent-roles-data';

interface AgentRoleSelectionProps {
  data: {
    selectedAgents: string[];
    templateId: string;
  };
  updateData: (data: Partial<{
    selectedAgents: string[];
  }>) => void;
}

// Agent roles data is now imported from './agent-roles-data'

export function AgentRoleSelection({ data, updateData }: AgentRoleSelectionProps) {
  const [selectedCount, setSelectedCount] = React.useState(0);

  React.useEffect(() => {
    // Initialize selected agents based on template if not already set
    if (data.templateId && (!data.selectedAgents || data.selectedAgents.length === 0)) {
      const preset = templatePresets[data.templateId] || [];
      updateData({ selectedAgents: preset });
    }

    setSelectedCount(data.selectedAgents?.length || 0);
  }, [data.templateId, data.selectedAgents]);

  const toggleAgentSelection = (agentId: string) => {
    const currentSelection = [...(data.selectedAgents || [])];

    if (currentSelection.includes(agentId)) {
      // Remove agent
      updateData({
        selectedAgents: currentSelection.filter(id => id !== agentId)
      });
    } else {
      // Add agent
      updateData({
        selectedAgents: [...currentSelection, agentId]
      });
    }
  };

  const clearAllSelections = () => {
    updateData({ selectedAgents: [] });
  };

  return (
    <div className="space-y-8 w-full">
      <div className="flex justify-between items-center">
        <div>
          <p className="text-muted-foreground text-base">
            Choose which AI agents you'd like on your team
          </p>
        </div>

        <div className="flex items-center gap-3">
          <span className="text-sm font-medium bg-primary/10 text-primary px-4 py-1.5 rounded-md">
            {selectedCount} selected
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={clearAllSelections}
            disabled={selectedCount === 0}
            className="px-4"
          >
            Clear All
          </Button>
        </div>
      </div>



      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
        {agentRoles.map((agent) => (
          <AgentRoleCard
            key={agent.id}
            id={agent.id}
            name={agent.name}
            role={agent.role}
            description={agent.description}
            skills={agent.skills}
            imageSrc={agent.imageSrc}
            selected={data.selectedAgents?.includes(agent.id)}
            onClick={() => toggleAgentSelection(agent.id)}
          />
        ))}
      </div>
    </div>
  );
}
