import React, { useRef } from "react";
import { Paperclip } from "lucide-react";
import { IconButton } from "./IconButton";

interface AttachmentButtonProps {
  onFileSelect: (files: FileList | null) => void;
  accept?: string;
  multiple?: boolean;
  className?: string;
  ariaLabel?: string;
}

export function AttachmentButton({
  onFileSelect,
  accept = ".txt,.js,.jsx,.ts,.tsx,.json,.md,.css,.html,.py,.java,.c,.cpp,.h,.cs,.go,.rs,.php,.rb,.sh",
  multiple = true,
  className,
  ariaLabel = "Attach file",
}: AttachmentButtonProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <>
      <IconButton
        icon={<Paperclip className="h-5 w-5" />}
        onClick={handleClick}
        className={className}
        ariaLabel={ariaLabel}
      />
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={(e) => onFileSelect(e.target.files)}
        multiple={multiple}
        accept={accept}
      />
    </>
  );
}
