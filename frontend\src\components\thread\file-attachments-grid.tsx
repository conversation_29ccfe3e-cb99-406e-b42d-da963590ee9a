"use client";

import React from "react";
import { FileText, File, Archive, FileIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface FileAttachment {
  name: string;
  type: string;
  size: string;
  content?: string;
}

interface FileAttachmentsGridProps {
  files: FileAttachment[];
  className?: string;
  onFileClick?: (file: FileAttachment) => void;
  showViewAllButton?: boolean;
  onViewAllClick?: () => void;
}

export function FileAttachmentsGrid({
  files,
  className,
  onFileClick,
  showViewAllButton = false,
  onViewAllClick
}: FileAttachmentsGridProps) {
  // Function to get the appropriate icon based on file type
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase() || '';

    // Document types
    if (['txt', 'doc', 'docx', 'pdf', 'md', 'markdown'].includes(extension)) {
      return <FileText className="h-5 w-5 text-white" />;
    }

    // Archive types
    if (['zip', 'rar', 'tar', 'gz', '7z'].includes(extension)) {
      return <Archive className="h-5 w-5 text-white" />;
    }

    // Default file icon
    return <File className="h-5 w-5 text-white" />;
  };

  // Function to get file type display text
  const getFileTypeDisplay = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase() || '';

    // Map of extensions to display names
    const typeMap: Record<string, string> = {
      'txt': 'Text',
      'doc': 'Document',
      'docx': 'Document',
      'pdf': 'PDF',
      'md': 'Markdown',
      'js': 'JavaScript',
      'jsx': 'React',
      'ts': 'TypeScript',
      'tsx': 'React TS',
      'html': 'HTML',
      'css': 'CSS',
      'json': 'JSON',
      'zip': 'Archive',
      'rar': 'Archive',
      'tar': 'Archive',
      'gz': 'Archive',
      '7z': 'Archive',
    };

    return typeMap[extension] || 'File';
  };

  // Function to get the appropriate background color based on file type
  const getFileIconBgColor = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase() || '';

    // Document types - muted blue to match theme
    if (['txt', 'doc', 'docx', 'pdf', 'md', 'markdown'].includes(extension)) {
      return "bg-[#444444]";
    }

    // Code types - matching chat theme purple
    if (['js', 'jsx', 'ts', 'tsx', 'html', 'css', 'json', 'py', 'java'].includes(extension)) {
      return "bg-[#555555]";
    }

    // Archive types - muted amber
    if (['zip', 'rar', 'tar', 'gz', '7z'].includes(extension)) {
      return "bg-[#666666]";
    }

    // Default - consistent with theme
    return "bg-[#555555]";
  };

  return (
    <div className={cn(
      "mt-2 mb-1 p-4 rounded-lg bg-[#2a2a2a] text-white border border-[#333333] min-w-[280px] max-w-[400px]",
      className
    )}>
      <div className="space-y-3">
        {files.map((file, index) => (
          <div
            key={index}
            className="flex items-center cursor-pointer hover:bg-[#333333] rounded-lg p-3 transition-all duration-200"
            onClick={() => onFileClick && onFileClick(file)}
          >
            <div className={`h-12 w-12 flex items-center justify-center ${getFileIconBgColor(file.name)} rounded-lg flex-shrink-0`}>
              {getFileIcon(file.name)}
            </div>
            <div className="ml-3 flex-1 min-w-0">
              <p className="text-sm font-medium text-white mb-1">{file.name}</p>
              <p className="text-xs text-[#999999]">
                {getFileTypeDisplay(file.name)} · {file.size}
              </p>
            </div>
          </div>
        ))}
      </div>

      {showViewAllButton && (
        <div className="mt-3 pt-2 border-t border-[#333333]">
          <button
            className="flex items-center justify-center w-full p-2 text-sm text-[#999999] hover:text-white hover:bg-[#333333] rounded-md transition-colors"
            onClick={onViewAllClick}
          >
            <FileIcon className="h-4 w-4 mr-2" />
            View all files in this task
          </button>
        </div>
      )}
    </div>
  );
}
