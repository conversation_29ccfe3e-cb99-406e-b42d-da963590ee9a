"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { PhoneOff } from 'lucide-react';

interface EndCallButtonProps {
  onClick: () => void;
  className?: string;
}

export function EndCallButton({
  onClick,
  className
}: EndCallButtonProps) {
  return (
      <Button 
        size="md" 
        onClick={onClick}
        className={`px-4 rounded-md bg-[var(--call-red)] text-white hover:bg-[var(--call-red)]/90 border-none ${className || ""}`}
      >
        <PhoneOff className="h-4 w-4 mr-2" />
        End Call
      </Button>
  );
}
