"use client";

import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';

export function HeroSection() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background">
      <div className="text-center space-y-6 max-w-3xl px-4">
        <h1 className="text-4xl font-bold">Siden.ai</h1>
        <p className="text-xl text-muted-foreground">
          Teams of role-based AI agents that work autonomously with approval options
          and communicate with each other and with real people.
        </p>
        <div className="pt-4 space-x-4">
          <Link href="/dashboard" passHref>
            <Button variant="outline" size="lg">
              Dashboard
            </Button>
          </Link>
          <Link href="/project/chat" passHref>
            <Button size="lg">
              View Project Demo
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
