{"node": {"601c170a67ae6fef70afe822441643e37116a2685d": {"workers": {"app/(dashboard + projects)/project/chat/page": {"moduleId": "(action-browser)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Candre%5C%5COneDrive%5C%5CDesktop%5C%5CGithub%20Repositories%5C%5Cdemo%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cteams.ts%22%2C%5B%7B%22id%22%3A%22601c170a67ae6fef70afe822441643e37116a2685d%22%2C%22exportedName%22%3A%22editTeamName%22%7D%2C%7B%22id%22%3A%226062632a978bb75746e9a51323251058df08e88e88%22%2C%22exportedName%22%3A%22editTeamSlug%22%7D%2C%7B%22id%22%3A%2260f534d13067e6efa040d889520859a4fda3e5e00e%22%2C%22exportedName%22%3A%22createTeam%22%7D%5D%5D%5D&__client_imported__=true!", "async": false}}, "layer": {"app/(dashboard + projects)/project/chat/page": "action-browser"}}, "6062632a978bb75746e9a51323251058df08e88e88": {"workers": {"app/(dashboard + projects)/project/chat/page": {"moduleId": "(action-browser)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Candre%5C%5COneDrive%5C%5CDesktop%5C%5CGithub%20Repositories%5C%5Cdemo%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cteams.ts%22%2C%5B%7B%22id%22%3A%22601c170a67ae6fef70afe822441643e37116a2685d%22%2C%22exportedName%22%3A%22editTeamName%22%7D%2C%7B%22id%22%3A%226062632a978bb75746e9a51323251058df08e88e88%22%2C%22exportedName%22%3A%22editTeamSlug%22%7D%2C%7B%22id%22%3A%2260f534d13067e6efa040d889520859a4fda3e5e00e%22%2C%22exportedName%22%3A%22createTeam%22%7D%5D%5D%5D&__client_imported__=true!", "async": false}}, "layer": {"app/(dashboard + projects)/project/chat/page": "action-browser"}}, "60f534d13067e6efa040d889520859a4fda3e5e00e": {"workers": {"app/(dashboard + projects)/project/chat/page": {"moduleId": "(action-browser)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Candre%5C%5COneDrive%5C%5CDesktop%5C%5CGithub%20Repositories%5C%5Cdemo%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cactions%5C%5Cteams.ts%22%2C%5B%7B%22id%22%3A%22601c170a67ae6fef70afe822441643e37116a2685d%22%2C%22exportedName%22%3A%22editTeamName%22%7D%2C%7B%22id%22%3A%226062632a978bb75746e9a51323251058df08e88e88%22%2C%22exportedName%22%3A%22editTeamSlug%22%7D%2C%7B%22id%22%3A%2260f534d13067e6efa040d889520859a4fda3e5e00e%22%2C%22exportedName%22%3A%22createTeam%22%7D%5D%5D%5D&__client_imported__=true!", "async": false}}, "layer": {"app/(dashboard + projects)/project/chat/page": "action-browser"}}}, "edge": {}, "encryptionKey": "oVBugW1xsoKQz+7BtvDY90Wr+yfKWtquBTHry6hH0jI="}