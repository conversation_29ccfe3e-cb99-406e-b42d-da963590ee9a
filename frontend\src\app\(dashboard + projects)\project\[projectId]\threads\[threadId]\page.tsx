'use client';

import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { getProject, getThread, getMessages, addUserMessage, startAgent } from '@/lib/api';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Send } from 'lucide-react';
import Link from 'next/link';
import { Textarea } from '@/components/ui/textarea';

// Define our internal message type for UI display
type ThreadMessage = {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  created_at: string;
  agent_id?: string;
  thinking?: string;
  type?: string;
};

// Function to convert API messages to our internal format
const convertApiMessageToThreadMessage = (msg: any): ThreadMessage => {
  // Parse content if it's a JSON string
  let content = msg.content;
  let role = msg.is_llm_message ? 'assistant' : 'user';
  
  try {
    // Handle different message formats
    if (typeof content === 'string') {
      try {
        const parsed = JSON.parse(content);
        if (parsed && typeof parsed === 'object') {
          if (parsed.content) {
            content = parsed.content;
          }
          if (parsed.role) {
            role = parsed.role;
          }
        }
      } catch (e) {
        // Not JSON, use as is
      }
    } else if (content && typeof content === 'object') {
      // Handle object content
      if (content.content) {
        content = content.content;
      }
    }
  } catch (e) {
    console.error('Error processing message content:', e);
  }
  
  // For system messages with type 'system_instruction', skip displaying them
  if (msg.type === 'system_instruction' || 
      (msg.metadata && msg.metadata.type === 'system_instruction')) {
    role = 'system';
  }
  
  // Generate a unique ID using message_id if available or a random ID
  const id = msg.message_id || msg.id || `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  // Use created_at if available or current timestamp
  const timestamp = msg.created_at || new Date().toISOString();
  
  return {
    id,
    content: typeof content === 'string' ? content : JSON.stringify(content),
    role: role as 'user' | 'assistant' | 'system',
    created_at: timestamp,
    type: msg.type || (role === 'user' ? 'user' : 'assistant'),
    agent_id: msg.agent_id
  };
};

export default function ThreadPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const projectId = params.projectId as string;
  const threadId = params.threadId as string;
  
  // Get selected agents and settings from URL query parameters
  const agentsParam = searchParams.get('agents');
  const autonomousModeParam = searchParams.get('autonomousMode');
  const sandboxModeParam = searchParams.get('sandboxMode');
  
  const selectedAgents = agentsParam ? agentsParam.split(',') : [];
  const autonomousMode = autonomousModeParam === 'true';
  const sandboxMode = sandboxModeParam === 'true';
  
  console.log('Thread settings:', { selectedAgents, autonomousMode, sandboxMode });
  
  const [project, setProject] = useState<any>(null);
  const [thread, setThread] = useState<any>(null);
  const [messages, setMessages] = useState<ThreadMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userInput, setUserInput] = useState('');
  const [sending, setSending] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [projectData, threadData, threadMessages] = await Promise.all([
          getProject(projectId),
          getThread(threadId),
          getMessages(threadId)
        ]);
        
        setProject(projectData);
        setThread(threadData);
        
        // Set the thread messages
        if (threadMessages && threadMessages.length > 0) {
          // Map API messages to our internal format
          const formattedMessages = threadMessages.map(convertApiMessageToThreadMessage);
          setMessages(formattedMessages);
        }
        
        setError(null);
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError(err.message || 'Failed to load conversation');
      } finally {
        setLoading(false);
      }
    };

    if (projectId && threadId) {
      fetchData();
    }
    
    // Set up polling for new messages every 10 seconds
    const intervalId = setInterval(async () => {
      if (!sending && threadId) {
        try {
          const threadMessages = await getMessages(threadId);
          if (threadMessages && threadMessages.length > messages.length) {
            // Map API messages to our internal format
            const formattedMessages = threadMessages.map(convertApiMessageToThreadMessage);
            setMessages(formattedMessages);
          }
        } catch (err) {
          console.error('Error polling messages:', err);
        }
      }
    }, 10000);
    
    return () => clearInterval(intervalId);
  }, [projectId, threadId, messages.length, sending]);

  const handleSendMessage = async () => {
    if (!userInput.trim() || sending) return;
    
    const newMessage: ThreadMessage = {
      id: `temp-${Date.now()}`,
      content: userInput,
      role: 'user' as const,
      created_at: new Date().toISOString(),
      type: 'user'
    };
    
    setMessages(prev => [...prev, newMessage]);
    setSending(true);
    setUserInput('');
    
    try {
      // Step 1: Add the user message to the thread
      await addUserMessage(threadId, userInput);
      
      // Step 2: Start the agent to process the message
      // Pass the selected agents and settings to the backend
      const options: any = {
        stream: false,
        enable_thinking: true,
        reasoning_effort: 'high',
        enable_context_manager: true,
        autonomous_mode: autonomousMode,
        sandbox_mode: sandboxMode
      };
      
      // Add the agents parameter if agents are selected
      if (selectedAgents.length > 0) {
        options.agents = selectedAgents.join(',');
      }
      
      console.log('Starting agent with options:', options);
      
      const { agent_run_id } = await startAgent(threadId, options);
      
      // Step 3: Poll for new messages until we get a response
      await pollForAgentResponse(agent_run_id);
      
    } catch (err: any) {
      console.error('Error processing message:', err);
      
      // Handle billing errors separately
      if (err.name === 'BillingError') {
        setError('Usage limit reached. Please upgrade your plan to continue.');
      } else {
        setError('Failed to process message. Please try again.');
      }
      
      setSending(false);
    }
  };
  
  // Poll for agent responses
  const pollForAgentResponse = async (agentRunId: string) => {
    const maxAttempts = 30; // Maximum polling attempts
    const pollInterval = 2000; // Polling interval in milliseconds
    let attempts = 0;
    
    const poll = async () => {
      attempts++;
      
      try {
        // Get the latest messages from the thread
        const latestMessages = await getMessages(threadId);
        
        // Find new messages that aren't in our current state
        // Map API messages to our internal format
        const formattedMessages = latestMessages.map(convertApiMessageToThreadMessage);
        
        // Find new messages that aren't in our current state
        const currentMessageIds = new Set(messages.map(m => m.id));
        const newMessages = formattedMessages.filter(m => !currentMessageIds.has(m.id));
        
        if (newMessages.length > 0) {
          // We have new messages, update the UI
          setMessages(prev => [...prev, ...newMessages]);
          setSending(false);
          return true;
        }
        
        // Check if we've reached the maximum attempts
        if (attempts >= maxAttempts) {
          console.warn('Polling timed out after maximum attempts');
          
          // Add a fallback message if no response was received
          const fallbackMessage = {
            id: `timeout-${Date.now()}`,
            content: "I'm still processing your request. Please wait a moment and try again if you don't see a response.",
            role: 'assistant' as const,
            created_at: new Date().toISOString()
          };
          
          setMessages(prev => [...prev, fallbackMessage]);
          setSending(false);
          return true;
        }
        
        // Continue polling
        await new Promise(resolve => setTimeout(resolve, pollInterval));
        return await poll();
      } catch (error) {
        console.error('Error polling for messages:', error);
        setSending(false);
        return false;
      }
    };
    
    return await poll();
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 max-w-7xl">
        <div className="mb-8">
          <Skeleton className="h-10 w-48 mb-4" />
        </div>
        <div className="space-y-4">
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-24 w-full" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8 max-w-7xl">
        <Button asChild variant="ghost" className="mb-6">
          <Link href={`/project/${projectId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Project
          </Link>
        </Button>
        
        <div className="bg-destructive/10 border border-destructive rounded-lg p-6 text-center">
          <h2 className="text-2xl font-bold text-destructive mb-4">Error Loading Conversation</h2>
          <p className="text-muted-foreground mb-6">{error}</p>
          <Button asChild>
            <Link href={`/project/${projectId}`}>Go back to project</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 max-w-7xl h-screen flex flex-col">
      <div className="flex items-center mb-6">
        <Button asChild variant="ghost">
          <Link href={`/project/${projectId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Project
          </Link>
        </Button>
        <h1 className="text-2xl font-bold ml-4">
          {project?.name} - Conversation
        </h1>
      </div>
      
      <div className="flex-1 overflow-y-auto mb-4 border rounded-lg p-4 bg-card">
        {messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <p className="text-muted-foreground mb-2">No messages yet</p>
              <p className="text-sm">Start the conversation by sending a message below.</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`p-4 rounded-lg ${
                  message.role === 'user'
                    ? 'bg-primary/10 ml-12'
                    : message.role === 'system'
                    ? 'bg-secondary/20'
                    : 'bg-muted mr-12'
                }`}
              >
                <div className="flex justify-between items-center mb-1">
                  <div className="font-semibold">
                    {message.role === 'user' 
                      ? 'You' 
                      : message.role === 'system'
                      ? 'System'
                      : message.agent_id 
                        ? `AI Agent (${message.agent_id})` 
                        : 'AI Assistant'}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(message.created_at).toLocaleTimeString()}
                  </div>
                </div>
                <div className="whitespace-pre-wrap">{message.content}</div>
                
                {/* Show thinking process if available */}
                {message.thinking && (
                  <div className="mt-2 pt-2 border-t border-border">
                    <details className="text-sm">
                      <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                        View thinking process
                      </summary>
                      <div className="mt-2 p-2 bg-muted/50 rounded text-xs font-mono whitespace-pre-wrap">
                        {message.thinking}
                      </div>
                    </details>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div className="border rounded-lg p-4 bg-card">
        <div className="flex gap-2">
          <Textarea
            placeholder="Type your message here..."
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            className="flex-1 resize-none"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
          />
          <Button 
            onClick={handleSendMessage} 
            disabled={!userInput.trim() || sending}
            className="self-end"
          >
            {sending ? 'Sending...' : <Send className="h-4 w-4" />}
          </Button>
        </div>
      </div>
    </div>
  );
}
