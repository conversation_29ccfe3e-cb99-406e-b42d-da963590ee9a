'use client';

import React, { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { getProject } from '@/lib/api';
import { Skeleton } from '@/components/ui/skeleton';
import { AgentDemoContainer } from '@/components/demo/AgentDemoContainer';
import { TeamMembersPage } from '@/components/demo/TeamMembersPage';
import { ToolsIntegrationsPage } from '@/components/demo/ToolsIntegrationsPage';
import { SettingsPage } from '@/components/demo/SettingsPage';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Video } from 'lucide-react';

export default function TeamPage() {
  const params = useParams();
  const projectId = params.projectId as string;

  const [project, setProject] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<'chat' | 'team' | 'tools' | 'settings' | 'calling'>('team');

  // Fetch project data
  useEffect(() => {
    const fetchProject = async () => {
      try {
        setLoading(true);
        const projectData = await getProject(projectId);
        setProject(projectData);
        setError(null);
      } catch (err: any) {
        console.error('Error fetching project:', err);
        setError(err.message || 'Failed to load project');
      } finally {
        setLoading(false);
      }
    };

    if (projectId) {
      fetchProject();
    }
  }, [projectId]);

  // Listen for sidebar section changes from the layout
  useEffect(() => {
    const handleSectionChange = (event: CustomEvent) => {
      setActiveSection(event.detail.section);
    };

    window.addEventListener('sidebar-section-change' as any, handleSectionChange);
    return () => {
      window.removeEventListener('sidebar-section-change' as any, handleSectionChange);
    };
  }, []);

  if (loading) {
    return (
      <div className="h-full w-full bg-background flex items-center justify-center">
        <Skeleton className="h-[500px] w-[800px]" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full w-full bg-background flex items-center justify-center">
        <div className="bg-destructive/10 border border-destructive rounded-lg p-6 text-center max-w-md">
          <h2 className="text-2xl font-bold text-destructive mb-4">Error Loading Project</h2>
          <p className="text-muted-foreground mb-6">{error}</p>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="h-full w-full bg-background flex items-center justify-center">
        <div className="bg-muted rounded-lg p-6 text-center max-w-md">
          <h2 className="text-2xl font-bold mb-4">Project Not Found</h2>
          <p className="text-muted-foreground mb-6">The project you're looking for doesn't exist or you don't have access to it.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full w-full bg-background">
      {activeSection === 'chat' && <AgentDemoContainer isActive={true} project={project} />}
      {activeSection === 'team' && <TeamMembersPage project={project} />}
      {activeSection === 'tools' && <ToolsIntegrationsPage project={project} />}
      {activeSection === 'settings' && <SettingsPage project={project} />}
      {activeSection === 'calling' && (
        <div className="flex flex-col items-center justify-center h-full p-8">
          <div className="max-w-lg text-center mb-8">
            <h1 className="text-3xl font-bold mb-4">Team Communication</h1>
            <p className="text-muted-foreground mb-6">
              Connect with your AI team members in {project.name} through audio and video calls. Experience seamless collaboration with both one-on-one and group calling options.
            </p>
            <div className="flex justify-center gap-4">
              <Link href={`/project/${projectId}/calling/interface`}>
                <Button size="lg" className="gap-2">
                  <Video className="h-5 w-5" />
                  Launch Calling Interface
                </Button>
              </Link>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
