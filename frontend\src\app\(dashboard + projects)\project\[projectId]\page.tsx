'use client';

import React, { useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';

export default function ProjectDetailPage() {
  const params = useParams();
  const router = useRouter();
  const projectId = params.projectId as string;

  // Redirect to chat sub-route by default
  useEffect(() => {
    if (projectId) {
      router.replace(`/project/${projectId}/chat`);
    }
  }, [projectId, router]);

  // Show loading while redirecting
  return (
    <div className="h-full w-full bg-background flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p>Loading project...</p>
      </div>
    </div>
  );
}
