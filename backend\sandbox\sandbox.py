import os
from typing import Optional, Dict, List, Any
import asyncio

from e2b_desktop import Sandbox as E2BSandbox
from dotenv import load_dotenv

from agentpress.tool import Tool
from utils.logger import logger
from utils.config import config
from utils.files_utils import clean_path
from agentpress.thread_manager import ThreadManager

load_dotenv()

logger.debug("Initializing E2B sandbox configuration")

# Check for E2B API key
if config.E2B_API_KEY:
    logger.debug("E2B API key configured successfully")
    os.environ["E2B_API_KEY"] = config.E2B_API_KEY
else:
    logger.warning("No E2B API key found in environment variables")

# Define a class to mimic the Daytona Sandbox interface for compatibility
class Sandbox:
    """
    Wrapper class for E2B Sandbox to maintain compatibility with existing code.
    """
    def __init__(self, e2b_sandbox: E2BSandbox, sandbox_id: str, metadata: Dict[str, Any] = None):
        self.e2b_sandbox = e2b_sandbox
        self.id = sandbox_id
        self.metadata = metadata or {}
        self.process = ProcessManager(e2b_sandbox)
        self.files = e2b_sandbox.files
        self.commands = e2b_sandbox.commands

    def get_preview_link(self, port: int) -> Dict[str, str]:
        """
        Simulate the Daytona preview link functionality.
        In E2B, we don't have direct preview links, but we can return a placeholder.
        """
        # For now, return a placeholder. This can be enhanced later if E2B provides similar functionality
        return {"url": f"http://localhost:{port}", "token": "e2b-sandbox"}

class ProcessManager:
    """
    Compatibility layer for process management to mimic Daytona's process API.
    """
    def __init__(self, e2b_sandbox: E2BSandbox):
        self.e2b_sandbox = e2b_sandbox
        self._sessions = {}

    def create_session(self, session_id: str):
        """Create a new session."""
        self._sessions[session_id] = {"commands": []}
        logger.debug(f"Created session {session_id}")

    def execute_session_command(self, session_id: str, command_request):
        """Execute a command in a session."""
        if session_id not in self._sessions:
            raise ValueError(f"Session {session_id} not found")

        command = command_request.command
        is_async = getattr(command_request, "var_async", False)

        logger.debug(f"Executing command in session {session_id}: {command}")

        # Execute the command using E2B
        try:
            result = self.e2b_sandbox.commands.run(command, background=is_async)
            self._sessions[session_id]["commands"].append({
                "command": command,
                "result": result
            })
            return result
        except Exception as e:
            logger.error(f"Error executing command in session {session_id}: {str(e)}")
            raise e

async def get_or_start_sandbox(sandbox_id: str):
    """Retrieve a sandbox by ID, check its state, and connect to it."""

    logger.info(f"Getting or starting sandbox with ID: {sandbox_id}")

    try:
        # Try to connect to an existing sandbox
        try:
            e2b_sandbox = E2BSandbox.connect(sandbox_id)
            logger.info(f"Connected to existing sandbox {sandbox_id}")
        except Exception as e:
            logger.warning(f"Could not connect to sandbox {sandbox_id}: {str(e)}. Creating a new one.")
            # If connection fails, create a new sandbox
            # E2BSandbox doesn't have a create method, use the constructor instead
            e2b_sandbox = E2BSandbox()
            logger.info(f"Created new sandbox with ID: {e2b_sandbox.sandbox_id}")

        # Create a wrapper sandbox object
        sandbox = Sandbox(e2b_sandbox, e2b_sandbox.sandbox_id)

        logger.info(f"Sandbox {sandbox_id} is ready")
        return sandbox

    except Exception as e:
        logger.error(f"Error retrieving or starting sandbox: {str(e)}")
        raise e

async def create_sandbox(password: str, project_id: str = None):
    """Create a new sandbox with all required services configured and running."""

    logger.debug("Creating new E2B sandbox environment")

    metadata = {}
    if project_id:
        logger.debug(f"Using project_id in metadata: {project_id}")
        metadata = {'id': project_id, 'password': password}

    # Create environment variables
    env_vars = {
        "CHROME_PERSISTENT_SESSION": "true",
        "RESOLUTION": "1024x768x24",
        "RESOLUTION_WIDTH": "1024",
        "RESOLUTION_HEIGHT": "768",
        "VNC_PASSWORD": password,
        "ANONYMIZED_TELEMETRY": "false",
        "CHROME_PATH": "",
        "CHROME_USER_DATA": "",
        "CHROME_DEBUGGING_PORT": "9222",
        "CHROME_DEBUGGING_HOST": "localhost",
        "CHROME_CDP": ""
    }

    try:
        # Create a new E2B sandbox using the constructor
        e2b_sandbox = E2BSandbox()
        sandbox_id = e2b_sandbox.sandbox_id
        logger.info(f"Created new E2B sandbox with ID: {sandbox_id}")

        # Create a wrapper sandbox object
        sandbox = Sandbox(e2b_sandbox, sandbox_id, metadata)

        # Set up the environment
        try:
            # Set environment variables
            for key, value in env_vars.items():
                e2b_sandbox.env.set(key, value)

            # Create workspace directory
            e2b_sandbox.commands.run("mkdir -p /workspace")

        except Exception as e:
            logger.error(f"Error setting up sandbox environment: {str(e)}")
            # Continue anyway, as the sandbox is created

        logger.debug(f"E2B Sandbox environment successfully initialized")
        return sandbox

    except Exception as e:
        logger.error(f"Error creating E2B sandbox: {str(e)}")
        raise e


class SandboxToolsBase(Tool):
    """Base class for all sandbox tools that provides project-based sandbox access."""

    # Class variable to track if sandbox URLs have been printed
    _urls_printed = False

    def __init__(self, project_id: str, thread_manager: Optional[ThreadManager] = None):
        super().__init__()
        self.project_id = project_id
        self.thread_manager = thread_manager
        self.workspace_path = "/workspace"
        self._sandbox = None
        self._sandbox_id = None
        self._sandbox_pass = None
        self._e2b_sandbox = None

    async def _ensure_sandbox(self) -> Sandbox:
        """Ensure we have a valid sandbox instance, retrieving it from the project if needed."""
        if self._sandbox is None:
            try:
                # Get database client
                client = await self.thread_manager.db.client

                # Get project data
                project = await client.table('projects').select('*').eq('project_id', self.project_id).execute()
                if not project.data or len(project.data) == 0:
                    raise ValueError(f"Project {self.project_id} not found")

                project_data = project.data[0]
                sandbox_info = project_data.get('sandbox', {})

                if not sandbox_info.get('id'):
                    raise ValueError(f"No sandbox found for project {self.project_id}")

                # Store sandbox info
                self._sandbox_id = sandbox_info['id']
                self._sandbox_pass = sandbox_info.get('pass')

                # Get or start the sandbox
                self._sandbox = await get_or_start_sandbox(self._sandbox_id)

                # Store E2B sandbox reference for direct access when needed
                self._e2b_sandbox = self._sandbox.e2b_sandbox

                # Log URLs if not already printed
                if not SandboxToolsBase._urls_printed:
                    vnc_link = self._sandbox.get_preview_link(6080)
                    website_link = self._sandbox.get_preview_link(8080)

                    vnc_url = vnc_link.get("url", "Not available")
                    website_url = website_link.get("url", "Not available")

                    print("\033[95m***")
                    print(f"VNC URL: {vnc_url}")
                    print(f"Website URL: {website_url}")
                    print("***\033[0m")
                    SandboxToolsBase._urls_printed = True

            except Exception as e:
                logger.error(f"Error retrieving sandbox for project {self.project_id}: {str(e)}", exc_info=True)
                raise e

        return self._sandbox

    @property
    def sandbox(self) -> Sandbox:
        """Get the sandbox instance, ensuring it exists."""
        if self._sandbox is None:
            raise RuntimeError("Sandbox not initialized. Call _ensure_sandbox() first.")
        return self._sandbox

    @property
    def e2b_sandbox(self) -> E2BSandbox:
        """Get the underlying E2B sandbox instance."""
        if self._e2b_sandbox is None:
            raise RuntimeError("E2B Sandbox not initialized. Call _ensure_sandbox() first.")
        return self._e2b_sandbox

    @property
    def sandbox_id(self) -> str:
        """Get the sandbox ID, ensuring it exists."""
        if self._sandbox_id is None:
            raise RuntimeError("Sandbox ID not initialized. Call _ensure_sandbox() first.")
        return self._sandbox_id

    def clean_path(self, path: str) -> str:
        """Clean and normalize a path to be relative to /workspace."""
        cleaned_path = clean_path(path, self.workspace_path)
        logger.debug(f"Cleaned path: {path} -> {cleaned_path}")
        return cleaned_path