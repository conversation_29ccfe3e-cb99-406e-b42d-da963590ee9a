'use client';

import { useState, useEffect, useRef } from 'react';
import { initiateAgent, initiateAgentWithFormData, sendMessage, getMessages, streamAgentWithFormData, StreamingAgentResponse } from '@/lib/api';
import { createClient } from '@/lib/supabase/client';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

// Define the StreamingAgentCallback interface
interface StreamingAgentCallback {
  onMessage: (data: any) => void;
  onError: (error: any) => void;
  onComplete: () => void;
}

// Available agent roles
const AGENT_ROLES = [
  { id: 'Kenard', name: 'CEO', description: 'Strategic leadership and decision making', group: 'leadership' },
  { id: 'Knarrd', name: 'CEO (Alt)', description: 'Alternative leadership perspective', group: 'leadership' },
  { id: 'Alex', name: 'Developer', description: 'Software development and implementation', group: 'technical' },
  { id: '<PERSON>', name: 'Lead Developer', description: 'Technical leadership and architecture', group: 'technical' },
  { id: 'Chloe', name: 'Marketing', description: 'Marketing strategy and content creation', group: 'business' },
  { id: 'Mark', name: 'Product', description: 'Product management and user experience', group: 'business' },
];

// Group definitions for agent roles
const AGENT_GROUPS = [
  { id: 'leadership', name: 'Leadership Team', color: 'blue' },
  { id: 'technical', name: 'Technical Team', color: 'green' },
  { id: 'business', name: 'Business Team', color: 'purple' },
];

// Available models
const AI_MODELS = [
  { id: 'anthropic/claude-3-7-sonnet-latest', name: 'Claude 3.7 Sonnet (Latest)', supportsThinking: true },
  { id: 'anthropic/claude-3-opus-20240229', name: 'Claude 3 Opus', supportsThinking: false },
  { id: 'anthropic/claude-3-5-sonnet-20240620', name: 'Claude 3.5 Sonnet', supportsThinking: true },
  { id: 'anthropic/claude-3-haiku-20240307', name: 'Claude 3 Haiku (Fastest)', supportsThinking: false },
  { id: 'openai/gpt-4o', name: 'GPT-4o (Reliable)', supportsThinking: false },
];

// Interface for sandbox execution request
interface SandboxExecutionRequest {
  code: string;
  language: string;
  timeout?: number;
}

// Interface for sandbox execution response
interface SandboxExecutionResponse {
  output: string;
  error?: string;
  exitCode: number;
}

export default function AgentTestPage() {
  // Log backend URL on component mount
  useEffect(() => {
    // Get the backend URL from environment variable
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';
    // Remove trailing /api if present to ensure consistent URL format
    const normalizedUrl = backendUrl.endsWith('/api') 
      ? backendUrl.slice(0, -4) 
      : backendUrl;
    
    console.log('Original Backend URL:', backendUrl);
    console.log('Normalized Backend URL:', normalizedUrl);
    
    // Store the normalized URL for use in API calls
    setApiUrl(normalizedUrl);
  }, []);
  
  const [threadId, setThreadId] = useState<string | null>(null);
  const [agentRunId, setAgentRunId] = useState<string | null>(null);
  const [messages, setMessages] = useState<any[]>([]);
  const [prompt, setPrompt] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [statusMessage, setStatusMessage] = useState('Ready');
  const [sandboxOutput, setSandboxOutput] = useState<string | null>(null);
  const [sandboxError, setSandboxError] = useState<string | null>(null);
  const [sandboxLoading, setSandboxLoading] = useState(false);
  const [selectedAgents, setSelectedAgents] = useState<string[]>(['Kenard']); // Default to CEO
  const [collaborationMode, setCollaborationMode] = useState(false); // New state for collaboration mode
  const [apiUrl, setApiUrl] = useState<string | null>(null); // Store API URL for debugging
  const [selectedModel, setSelectedModel] = useState<string>('anthropic/claude-3-7-sonnet-latest'); // Default to latest Claude
  const [enableThinking, setEnableThinking] = useState<boolean>(true); // Enable thinking by default
  const [suggestedResponses, setSuggestedResponses] = useState<string[]>([]); // Store suggested quick responses
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  // Function to toggle agent selection
  const toggleAgentSelection = (agentId: string) => {
    setSelectedAgents(prev => {
      // If the agent is already selected, remove it (unless it's the last one)
      if (prev.includes(agentId)) {
        // Don't allow removing the last agent
        if (prev.length === 1) {
          return prev;
        }
        return prev.filter(id => id !== agentId);
      }
      
      // If collaboration mode is off, replace the current selection
      if (!collaborationMode) {
        return [agentId];
      }
      
      // Otherwise, add the agent to the selection
      return [...prev, agentId];
    });
  };
  
  // Function to handle model selection
  const handleModelChange = (modelId: string) => {
    setSelectedModel(modelId);
    
    // Reset thinking state based on model capabilities
    const model = AI_MODELS.find(m => m.id === modelId);
    if (model && !model.supportsThinking) {
      setEnableThinking(false);
    }
  };
  
  // Function to handle clicking on a suggested response
  const handleSuggestedResponse = (suggestion: string) => {
    setPrompt(suggestion);
    // Auto-send after a short delay to give visual feedback
    setTimeout(() => {
      handleSendFollowUp();
    }, 300);
  };

  // Function to send a follow-up message to an existing thread
  const handleSendFollowUp = async () => {
    if (!threadId || !prompt.trim()) {
      setError('Thread ID or prompt is missing');
      return;
    }
    
    setLoading(true);
    setError(null);
    setStatusMessage('Sending follow-up message...');
    
    try {
      // Add the user message to the local state first
      const userMessage = {
        id: `user-${Date.now()}`,
        type: 'user',
        role: 'user',
        content: prompt,
        timestamp: new Date().toISOString()
      };
      
      // Create a placeholder for the AI response
      const aiMessageId = `ai-${Date.now()}`;
      const aiMessage = {
        id: aiMessageId,
        type: 'assistant',
        role: 'assistant',
        content: 'Thinking...',
        timestamp: new Date().toISOString()
      };
      
      // Add both messages to the state
      setMessages(prev => [...prev, userMessage, aiMessage]);
      
      // Clear the prompt input
      setPrompt('');
      
      // First, send the follow-up message to the thread
      console.log('Sending follow-up message to thread:', threadId);
      
      // Create the request for sending a message
      const sendMessageRequest = {
        thread_id: threadId,
        message: prompt,
        metadata: {
          enable_thinking: enableThinking,
          model: selectedModel,
          selected_agents: selectedAgents,
          collaboration_mode: collaborationMode,
          timestamp: new Date().toISOString()
        }
      };
      
      console.log('Send message request:', sendMessageRequest);
      
      // Send the follow-up message
      const response = await sendMessage(sendMessageRequest);
      console.log('Message sent:', response);
      
      // Now start streaming using the thread_id
      console.log('Starting streaming with thread_id:', threadId);
      
      // Start streaming
      streamAgentWithFormData(threadId, {
        onMessage: (data: any) => {
          if (data.content) {
            // Update the AI message content
            setMessages(prev => {
              const newMessages = [...prev];
              const aiMessageIndex = newMessages.findIndex(m => m.id === aiMessageId);
              if (aiMessageIndex !== -1) {
                newMessages[aiMessageIndex] = {
                  ...newMessages[aiMessageIndex],
                  content: data.content
                };
              }
              return newMessages;
            });
          }
          
          if (data.thread_id) {
            setThreadId(data.thread_id);
          }
          
          if (data.agent_run_id) {
            setAgentRunId(data.agent_run_id);
          }
        },
        onError: (error: any) => {
          console.error('Streaming error:', error);
          setError(`Streaming error: ${error.message || 'Unknown error'}`);
          setLoading(false);
          setStatusMessage('Error during streaming');
        },
        onComplete: () => {
          console.log('Streaming completed');
          setLoading(false);
          setStatusMessage('Streaming completed');
          scrollToBottom();
        }
      });
    } catch (error) {
      console.error('Error sending follow-up message:', error);
      setError(`Failed to send message: ${error instanceof Error ? error.message : String(error)}`);
      setLoading(false);
      setStatusMessage('Error sending message');
    }
  };
  

  
  // Function to check agent status directly from the backend API
  const checkAgentStatusDirectly = async (agentRunId: string) => {
    if (!agentRunId) {
      setError('Agent run ID is missing');
      return;
    }
    
    setStatusMessage(`Checking status of agent run ${agentRunId}...`);
    
    try {
      const response = await fetch(`${apiUrl}/agent-status/${agentRunId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to check agent status: ${response.statusText}`);
      }
      
      const data = await response.json();
      setStatusMessage(`Agent status: ${data.status} - ${data.message || 'No message'}`);
      
      if (data.status === 'completed' && data.thread_id) {
        setThreadId(data.thread_id);
        fetchMessages(data.thread_id);
      }
    } catch (error) {
      console.error('Error checking agent status:', error);
      setError(`Failed to check agent status: ${error instanceof Error ? error.message : String(error)}`);
      setStatusMessage('Error checking agent status');
    }
  };
  
  // Function to fetch messages for a thread
  const fetchMessages = async (threadId: string) => {
    if (!threadId) {
      setError('Thread ID is missing');
      return;
    }
    
    setStatusMessage(`Fetching messages for thread ${threadId}...`);
    
    try {
      const messagesData = await getMessages(threadId);
      if (messagesData && Array.isArray(messagesData)) {
        setMessages(messagesData);
      }
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    } catch (error) {
      console.error('Error fetching messages:', error);
      setError(`Failed to fetch messages: ${error instanceof Error ? error.message : String(error)}`);
      setStatusMessage('Error fetching messages');
    }
  };
  
  // Function to check agent status and fetch messages when complete
  const checkAgentStatus = async (agentRunId: string, threadId: string) => {
    if (!agentRunId || !threadId) {
      setError('Agent run ID or thread ID is missing');
      return;
    }
    
    setStatusMessage(`Checking status of agent run ${agentRunId}...`);
    
    try {
      const response = await fetch(`${apiUrl}/agent-status/${agentRunId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to check agent status: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.status === 'completed') {
        setStatusMessage('Agent run completed, fetching messages...');
        setLoading(false);
        
        // Fetch messages for the thread
        await fetchMessages(threadId);
      } else if (data.status === 'error') {
        setStatusMessage(`Agent run failed: ${data.message || 'Unknown error'}`);
        setError(data.message || 'Agent run failed with an unknown error');
        setLoading(false);
      } else {
        // Still running, poll again after a delay
        setStatusMessage(`Agent is still running (${data.status})...`);
        
        // Poll again after 2 seconds
        setTimeout(() => {
          checkAgentStatus(agentRunId, threadId);
        }, 2000);
      }
    } catch (error) {
      console.error('Error checking agent status:', error);
      setError(`Failed to check agent status: ${error instanceof Error ? error.message : String(error)}`);
      setStatusMessage('Error checking agent status');
      setLoading(false);
    }
  };
  
  // Function to start the agent (non-streaming version)
  const handleStartAgent = async () => {
    if (!prompt.trim()) {
      setError('Please enter a prompt');
      return;
    }
    
    setLoading(true);
    setError(null);
    setMessages([]);
    setStatusMessage('Starting agent...');
    
    try {
      // Add the user message to the local state first
      const userMessage = {
        id: `user-${Date.now()}`,
        type: 'user',
        role: 'user',
        content: prompt,
        timestamp: new Date().toISOString()
      };
      
      setMessages([userMessage]);
      
      // Prepare form data for agent initiation
      const formData = new FormData();
      formData.append('prompt', prompt);
      formData.append('model_name', selectedModel);
      formData.append('enable_thinking', enableThinking.toString());
      formData.append('agents', selectedAgents.join(','));
      formData.append('collaboration_mode', collaborationMode.toString());
      
      // Add the current timestamp to the form data
      formData.append('timestamp', new Date().toISOString());
      
      // Initiate the agent using the FormData-compatible function
      const response = await initiateAgentWithFormData(formData);
      
      setThreadId(response.thread_id);
      setAgentRunId(response.agent_run_id);
      
      // Clear the prompt input
      setPrompt('');
      
      // Start polling for agent status
      checkAgentStatus(response.agent_run_id, response.thread_id);
    } catch (error) {
      console.error('Error starting agent:', error);
      setError(`Failed to start agent: ${error instanceof Error ? error.message : String(error)}`);
      setLoading(false);
      setStatusMessage('Error starting agent');
    }
  };
  

  
  // Function to render a message
  const renderMessage = (message: any) => {
    if (!message) return null;
    
    // Handle status messages
    if (message.type === 'status') {
      // Special handling for model overload messages
      if (typeof message.content === 'string' && 
          (message.content.includes('overloaded') || message.content.includes('fallback model'))) {
        return (
          <div key={message.id} className="p-2 bg-yellow-50 border-l-4 border-yellow-400 text-yellow-800 mb-2">
            <p className="font-medium">Model Switching</p>
            <p className="text-sm">{message.content}</p>
          </div>
        );
      }
      
      return (
        <div key={message.id} className="p-2 bg-gray-50 border-l-4 border-gray-300 text-gray-700 mb-2">
          <p className="text-sm italic">{message.content}</p>
        </div>
      );
    }
    
    // Handle user messages
    if (message.role === 'user') {
      return (
        <div key={message.id} className="bg-blue-50 p-3 rounded-lg mb-2">
          <div className="font-medium text-blue-800 mb-1">You</div>
          <div className="text-gray-800">{message.content}</div>
        </div>
      );
    }
    
    // Handle assistant messages
    if (message.role === 'assistant') {
      // Extract agent name if available in the message metadata
      const agentName = message.metadata?.agent_name || 'Assistant';
      
      // Shorten long messages for more human-like display
      const shortenMessage = (content: string) => {
        // If content is less than 500 characters, return as is
        if (!content || content.length < 500) return content;
        
        // Check if there's a "thinking" section to remove
        const thinkingPattern = /\*\*Thinking\*\*[\s\S]*?(?=\n\n|$)/;
        const cleanedContent = content.replace(thinkingPattern, '');
        
        // Return the cleaned content or a shortened version if still too long
        return cleanedContent.length < 1000 ? 
          cleanedContent : 
          cleanedContent.substring(0, 500) + '...';
      };
      
      return (
        <div key={message.id} className="bg-green-50 border border-green-200 p-3 rounded-lg mb-2">
          <div className="font-medium text-green-800 mb-1">{agentName}</div>
          <div className="prose max-w-none text-black">
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {shortenMessage(message.content)}
            </ReactMarkdown>
          </div>
          
          {/* Add code execution buttons for code blocks */}
          {message.content && extractCodeBlocks(message.content).length > 0 && (
            <div className="mt-2">
              {extractCodeBlocks(message.content).map((block, index) => (
                <div key={index} className="mt-2">
                  <button
                    onClick={() => executeSandbox(block.code, block.language)}
                    className="text-xs bg-green-600 text-white px-2 py-1 rounded"
                    type="button"
                  >
                    Run {block.language} code
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      );
    }
    
    // Handle thinking messages
    if (message.role === 'thinking') {
      return (
        <div key={message.id} className="bg-gray-50 p-3 rounded-lg mb-2 border-l-4 border-gray-300">
          <div className="font-medium text-gray-700 mb-1">Thinking</div>
          <div className="text-gray-600 prose max-w-none">
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {message.content}
            </ReactMarkdown>
          </div>
        </div>
      );
    }
    
    // Default rendering for unknown message types
    return (
      <div key={message.id} className="bg-gray-50 p-3 rounded-lg mb-2">
        <div className="font-medium text-gray-700 mb-1">{message.role || 'Unknown'}</div>
        <div className="text-gray-800">{message.content}</div>
      </div>
    );
  };

  // Function to generate suggested responses based on conversation context
  const generateSuggestedResponses = () => {
    // Only generate suggestions if we have at least one message
    if (messages.length === 0) {
      setSuggestedResponses([]);
      return;
    }
    
    // Get the last few messages to analyze context
    const recentMessages = messages.slice(-3);
    const lastMessage = messages[messages.length - 1];
    
    // Don't suggest responses if the last message is from the user
    if (lastMessage?.role === 'user') {
      setSuggestedResponses([]);
      return;
    }
    
    // Generate contextual suggestions based on conversation
    const suggestions: string[] = [];
    
    // Extract topics from recent messages
    const content = recentMessages.map(m => m.content || '').join(' ');
    const lowerContent = content.toLowerCase();
    
    // Check for common conversation patterns and add relevant suggestions
    if (lowerContent.includes('question') || lowerContent.includes('?')) {
      suggestions.push('Yes', 'No', 'Tell me more');
    }
    
    if (lowerContent.includes('recommend') || lowerContent.includes('suggest') || lowerContent.includes('option')) {
      suggestions.push('Sounds good', 'Other options?', 'Why this?');
    }
    
    if (lowerContent.includes('code') || lowerContent.includes('function') || lowerContent.includes('programming')) {
      suggestions.push('Run it', 'Explain code', 'Optimize it');
    }
    
    if (lowerContent.includes('help') || lowerContent.includes('assistance')) {
      suggestions.push('Thanks', 'Not what I need', 'More details');
    }
    
    // Default suggestions if none matched
    if (suggestions.length === 0) {
      suggestions.push('Continue', 'Thanks', 'More details');
    }
    
    // Limit to 3 suggestions
    setSuggestedResponses(suggestions.slice(0, 3));
  };
  
  // Function to scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Effect to scroll to bottom when messages change and generate suggestions
  useEffect(() => {
    scrollToBottom();
    generateSuggestedResponses();
  }, [messages]);

  // Function to execute code in sandbox
  const executeSandbox = async (code: string, language = 'python') => {
    if (!code) {
      setError('No code to execute');
      return;
    }
    
    setSandboxLoading(true);
    setSandboxOutput(null);
    setSandboxError(null);
    
    try {
      const request: SandboxExecutionRequest = {
        code,
        language,
        timeout: 10 // 10 second timeout
      };
      
      const response = await fetch(`${apiUrl}/sandbox/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });
      
      if (!response.ok) {
        throw new Error(`Sandbox execution failed: ${response.statusText}`);
      }
      
      const result: SandboxExecutionResponse = await response.json();
      
      if (result.error) {
        setSandboxError(result.error);
      } else {
        setSandboxOutput(result.output);
      }
    } catch (error) {
      setSandboxError(`Error: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setSandboxLoading(false);
    }
  };

  // Function to extract code blocks from markdown
  const extractCodeBlocks = (markdown: string) => {
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    const blocks = [];
    let match;
    
    while ((match = codeBlockRegex.exec(markdown)) !== null) {
      blocks.push({
        language: match[1] || 'text',
        code: match[2]
      });
    }
    
    return blocks;
  };

  // Function to test backend connection
  const testBackendConnection = async () => {
    if (!apiUrl) {
      setError('API URL is not set');
      return;
    }
    
    setStatusMessage('Testing backend connection...');
    
    try {
      const response = await fetch(`${apiUrl}/api/heartbeat`);
      
      if (!response.ok) {
        throw new Error(`Backend connection failed: ${response.statusText}`);
      }
      
      const data = await response.json();
      setStatusMessage(`Backend connection successful: ${data.status} at ${data.timestamp}`);
    } catch (error) {
      console.error('Backend connection error:', error);
      setError(`Backend connection failed: ${error instanceof Error ? error.message : String(error)}`);
      setStatusMessage('Backend connection failed');
    }
  };
  
  // Function to handle streaming agent responses
  const handleStreamAgent = async () => {
    console.log('Starting streaming agent');
    if (!prompt.trim()) {
      setError('Please enter a prompt');
      return;
    }

    console.log('Prompt is valid, starting streaming agent process');
    console.log('Selected agents:', selectedAgents);
    console.log('Collaboration mode:', collaborationMode);
    console.log('Selected model:', selectedModel);
    
    setLoading(true);
    setError(null);
    setStatusMessage('Starting agent with streaming...');

    try {
      // Generate a unique ID for the AI message
      const aiMessageId = `ai-${Date.now()}`;
      
      // Add the user message to the local state first
      const userMessage = {
        id: `user-${Date.now()}`,
        type: 'user',
        role: 'user',
        content: prompt,
        timestamp: new Date().toISOString()
      };
      
      // Create a placeholder message for the AI response
      const aiMessage = {
        id: aiMessageId,
        type: 'assistant',
        role: 'assistant',
        content: '',
        timestamp: new Date().toISOString()
      };
      
      setMessages([userMessage, aiMessage]);
      
      // Clear the prompt input
      setPrompt('');
      
      // First, initiate the agent conversation to get a thread_id
      console.log('Initiating agent conversation...');
      
      // Map the selected agent to the correct agent_type for the API
      const agentTypeMap: Record<string, string> = {
        'Kenard': 'ceo',
        'Alex': 'developer',
        'Chloe': 'marketing',
        'Mark': 'product'
      };
      
      // Get the first selected agent (or default to 'developer' if none selected)
      const selectedAgent = selectedAgents.length > 0 ? selectedAgents[0] : 'Alex';
      const agent_type = agentTypeMap[selectedAgent] || 'developer';
      
      // Get the model ID without the provider prefix
      const model = selectedModel.includes('/') ? selectedModel.split('/')[1] : selectedModel;
      
      // Create the request for initiating the agent
      const initiateRequest = {
        agent_type,
        model,
        message: prompt,
        metadata: {
          enable_thinking: enableThinking,
          collaboration_mode: collaborationMode,
          selected_agents: selectedAgents,
          timestamp: new Date().toISOString()
        }
      };
      
      console.log('Initiate request:', initiateRequest);
      
      // Initiate the agent conversation
      const response = await initiateAgent(initiateRequest);
      console.log('Agent initiated:', response);
      
      // Store the thread_id and agent_run_id
      setThreadId(response.thread_id);
      setAgentRunId(response.agent_run_id);
      
      // Now start streaming using the thread_id
      console.log('Starting streaming with thread_id:', response.thread_id);
      
      // Start streaming
      streamAgentWithFormData(response.thread_id, {
        onMessage: (data: any) => {
          if (data.content) {
            // Update the AI message content
            setMessages(prev => {
              const newMessages = [...prev];
              const aiMessageIndex = newMessages.findIndex(m => m.id === aiMessageId);
              if (aiMessageIndex !== -1) {
                newMessages[aiMessageIndex] = {
                  ...newMessages[aiMessageIndex],
                  content: data.content
                };
              }
              return newMessages;
            });
          }
          
          if (data.thread_id) {
            setThreadId(data.thread_id);
          }
          
          if (data.agent_run_id) {
            setAgentRunId(data.agent_run_id);
          }
        },
        onError: (error: any) => {
          console.error('Streaming error:', error);
          setError(`Streaming error: ${error}`);
          setLoading(false);
          setStatusMessage('Error during streaming');
        },
        onComplete: () => {
          console.log('Streaming completed');
          setLoading(false);
          setStatusMessage('Streaming completed');
        }
      });
    } catch (error) {
      console.error('Error starting streaming agent:', error);
      setError(`Failed to start streaming: ${error instanceof Error ? error.message : String(error)}`);
      setLoading(false);
      setStatusMessage('Error starting streaming');
    }
  };
  
  // Function to test the echo endpoint (simplest possible test)
  const testEchoEndpoint = async () => {
    setStatusMessage('Testing echo endpoint...');
    setError(null);
    
    try {
      const response = await fetch(`${apiUrl}/echo`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: 'Test message from agent-test page'
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Echo endpoint test failed: ${response.statusText}`);
      }
      
      const data = await response.json();
      setStatusMessage(`Echo endpoint test successful: ${JSON.stringify(data)}`);
    } catch (error) {
      console.error('Echo endpoint test error:', error);
      setError(`Echo endpoint test failed: ${error instanceof Error ? error.message : String(error)}`);
      setStatusMessage('Echo endpoint test failed');
    }
  };
  
  // Function to test the AgentPress integration
  const testAgentEndpoint = async () => {
    setStatusMessage('Testing AgentPress integration...');
    setError(null);
    setSandboxOutput(null);
    setSandboxError(null);
    setLoading(true);
    
    try {
      // Get the backend URL from environment variable or use default
      const backendUrl = apiUrl || 'http://localhost:8000';
      
      // Test the agent/initiate endpoint with our new implementation
      const response = await fetch(`${backendUrl}/agent/initiate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: prompt || 'Hello, please introduce yourself and explain your role.',
          model_name: selectedModel,
          agents: selectedAgents,
          collaboration_mode: collaborationMode,
          enable_thinking: enableThinking
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      console.log('Agent initiate response:', data);
      
      // Store the thread ID and agent run ID
      setThreadId(data.thread_id);
      setAgentRunId(data.agent_run_id);
      
      // Display the response
      setSandboxOutput(JSON.stringify(data, null, 2));
      setStatusMessage('AgentPress initiation completed successfully!');
      
      // If there's an immediate response, add it to messages
      if (data.response) {
        const userMessage = {
          message_id: Date.now().toString(),
          thread_id: data.thread_id,
          type: 'user',
          content: prompt || 'Hello, please introduce yourself and explain your role.',
          created_at: new Date().toISOString(),
        };
        
        const agentMessage = {
          message_id: (Date.now() + 1).toString(),
          thread_id: data.thread_id,
          type: 'assistant',
          content: data.response,
          created_at: new Date().toISOString(),
        };
        
        setMessages([userMessage, agentMessage]);
      } else {
        // If no immediate response (collaboration mode), fetch messages
        await fetchMessages(data.thread_id);
      }
      
    } catch (err: any) {
      console.error('Error testing AgentPress integration:', err);
      setError(`Error testing AgentPress integration: ${err.message}`);
      setStatusMessage('AgentPress test failed!');
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Agent Test Page</h1>
      <p className="mb-4 text-gray-600">Use this page to test the agent functionality.</p>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p><strong>Error:</strong> {error}</p>
        </div>
      )}
      
      <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded mb-4">
        <p><strong>Status:</strong> {statusMessage}</p>
        {apiUrl && <p><strong>API URL:</strong> {apiUrl}</p>}
        {threadId && <p><strong>Thread ID:</strong> {threadId}</p>}
        {agentRunId && <p><strong>Agent Run ID:</strong> {agentRunId}</p>}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h2 className="text-xl font-semibold mb-2">Agent Configuration</h2>
          
          {/* Model Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Select AI Model:</label>
            <select
              value={selectedModel}
              onChange={(e) => handleModelChange(e.target.value)}
              className="w-full p-2 border rounded"
              disabled={loading}
            >
              {AI_MODELS.map(model => (
                <option key={model.id} value={model.id}>
                  {model.name}
                </option>
              ))}
            </select>
          </div>
          
          {/* Collaboration Mode Toggle */}
          <div className="mb-4">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={collaborationMode}
                onChange={() => setCollaborationMode(!collaborationMode)}
                className="h-4 w-4 text-blue-600"
                disabled={loading}
              />
              <span className="text-sm font-medium text-gray-700">Enable Collaboration Mode</span>
            </label>
            <p className="text-xs text-gray-500 mt-1">
              {collaborationMode 
                ? "Agents will collaborate to solve problems together, each contributing from their expertise." 
                : "Agents will work independently based on their individual roles."}
            </p>
          </div>

          {/* Agent Roles Selection */}
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm font-medium text-gray-700">Select Agent Roles:</label>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="collaboration-mode"
                  checked={collaborationMode}
                  onChange={() => setCollaborationMode(!collaborationMode)}
                  className="mr-2"
                  disabled={loading}
                />
                <label htmlFor="collaboration-mode" className="text-sm">
                  Enable Team Chat
                </label>
              </div>
            </div>
            
            {collaborationMode && (
              <div className="p-2 bg-blue-50 border-l-4 border-blue-400 text-blue-700 mb-3">
                <p className="text-sm">
                  <span className="font-medium">Team Chat Active:</span> Multiple agents will collaborate on your request.
                  Select agents from different teams for diverse perspectives.
                </p>
              </div>
            )}
            
            {/* Group agents by team */}
            {AGENT_GROUPS.map(group => (
              <div key={group.id} className="mb-4">
                <h3 className={`text-${group.color}-700 font-medium mb-2 text-sm border-b border-${group.color}-200 pb-1`}>
                  {group.name}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {AGENT_ROLES.filter(role => role.group === group.id).map(role => (
                    <div 
                      key={role.id} 
                      onClick={() => !loading && toggleAgentSelection(role.id)}
                      className={`cursor-pointer p-3 rounded-lg border transition-all ${selectedAgents.includes(role.id) 
                        ? `bg-${group.color}-50 border-${group.color}-500 shadow-sm` 
                        : 'bg-gray-50 border-gray-300 hover:bg-gray-100'}`}
                    >
                      <div className="font-medium">{role.name}</div>
                      <div className="text-xs text-gray-600 mt-1">{role.description}</div>
                      {selectedAgents.includes(role.id) && (
                        <div className={`text-xs text-${group.color}-600 mt-1 font-medium`}>
                          {collaborationMode 
                            ? "Will collaborate" 
                            : selectedAgents[0] === role.id 
                              ? "Primary agent" 
                              : "Will not respond (team chat off)"}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
            
            {!collaborationMode && selectedAgents.length > 1 && (
              <div className="p-2 bg-yellow-50 border-l-4 border-yellow-400 text-yellow-700 mt-3">
                <p className="text-sm">
                  <span className="font-medium">Note:</span> Multiple agents selected but collaboration mode is off. 
                  Only {AGENT_ROLES.find(r => r.id === selectedAgents[0])?.name} (the first selected agent) will respond.
                </p>
              </div>
            )}
          </div>
          
          {/* Prompt Input */}
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">Enter Prompt:</label>
            <div className="flex gap-2">
              <input
                type="text"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                placeholder="Enter your prompt"
                className="flex-1 p-2 border rounded"
                disabled={loading}
              />
              <button
                onClick={handleStreamAgent}
                disabled={loading}
                className="bg-blue-500 text-white px-4 py-2 rounded"
                type="button"
              >
                {loading ? 'Running...' : 'Start Agent'}
              </button>
              <button
                onClick={testAgentEndpoint}
                disabled={loading}
                className="ml-2 bg-green-500 text-white px-4 py-2 rounded"
                type="button"
              >
                Test AgentPress
              </button>
              <button
                onClick={testBackendConnection}
                className="ml-2 bg-yellow-500 text-white px-4 py-2 rounded"
                type="button"
              >
                Test Backend
              </button>
            </div>
          </div>
        </div>
        
        <div>
          <h2 className="text-xl font-semibold mb-2">
            {collaborationMode ? 'Team Chat' : 'Agent Messages'}
            {collaborationMode && selectedAgents.length > 1 && (
              <span className="text-sm font-normal text-gray-500 ml-2">
                ({selectedAgents.length} agents collaborating)
              </span>
            )}
          </h2>
          <div className="border rounded p-4 min-h-[300px] bg-gray-50">
            {messages.length === 0 ? (
              <p className="text-gray-500">{loading ? 'Waiting for response...' : 'No messages yet'}</p>
            ) : (
              <div className="space-y-4">
                {messages.map((message) => renderMessage(message))}
              </div>
            )}
          </div>
          <div ref={messagesEndRef} />
          
          {/* Add a form for sending additional messages */}
          {threadId && (
            <div className="mt-4">
              {/* Suggested Quick Responses */}
              {suggestedResponses.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-2">
                  {suggestedResponses.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestedResponse(suggestion)}
                      className="bg-gray-100 hover:bg-gray-200 text-gray-800 text-sm px-3 py-1 rounded-full transition-colors"
                      disabled={loading}
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              )}
              
              <div className="flex">
                <input
                  type="text"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="Type a follow-up message..."
                  className="flex-grow p-2 border rounded-l"
                  disabled={loading}
                />
                <button
                  onClick={handleSendFollowUp}
                  disabled={loading || !prompt.trim()}
                  className="bg-blue-600 text-white px-4 py-2 rounded-r"
                  type="button"
                >
                  Send
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Sandbox Output Section */}
      {(sandboxOutput || sandboxError) && (
        <div className="mt-6">
          <h2 className="text-xl font-semibold mb-2">Sandbox Execution Result</h2>
          <div className="border rounded p-4 bg-gray-50">
            {sandboxLoading && <p className="text-gray-500">Running code...</p>}
            
            {sandboxOutput && (
              <div className="bg-black text-green-400 p-4 rounded font-mono whitespace-pre-wrap overflow-auto max-h-[300px]">
                {sandboxOutput}
              </div>
            )}
            
            {sandboxError && (
              <div className="bg-red-100 border border-red-400 text-red-700 p-4 rounded font-mono whitespace-pre-wrap overflow-auto max-h-[300px]">
                {sandboxError}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}