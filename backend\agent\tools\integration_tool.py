from typing import Dict, Any, Optional, List, Union
import json
import asyncio
from datetime import datetime

from agentpress.tool import <PERSON><PERSON>, ToolResult, openapi_schema, xml_schema
from utils.logger import logger
from utils.supabase import get_supabase_client_from_env
from integrations.manager import IntegrationManager


class IntegrationTool(Tool):
    """Tool for interacting with external integrations like GitHub, Gmail, Google Docs, Slack, etc."""
    
    def __init__(self, project_id: Optional[str] = None, account_id: Optional[str] = None, **kwargs):
        """Initialize the IntegrationTool.
        
        Args:
            project_id: Optional ID of the project this tool is associated with
            account_id: Optional ID of the account this tool is associated with
            **kwargs: Additional arguments
        """
        super().__init__()
        self.project_id = project_id
        self.account_id = account_id
        self.supabase = get_supabase_client_from_env()
        self.integration_manager = IntegrationManager(self.supabase)
    
    @openapi_schema({
        "name": "list_available_integrations",
        "description": "List all available integration types that can be used",
        "parameters": {
            "type": "object",
            "properties": {},
            "required": []
        }
    })
    @xml_schema(
        tag_name="list-available-integrations",
        mappings=[],
        example='''
        <list-available-integrations />
        '''
    )
    async def list_available_integrations(self) -> ToolResult:
        """List all available integration types that can be used.
        
        Returns:
            ToolResult with success status and list of available integrations
        """
        try:
            from integrations import registry
            
            integrations = registry.get_all_integrations()
            
            available_integrations = []
            for name, integration_class in integrations.items():
                available_integrations.append({
                    "name": name,
                    "display_name": integration_class.display_name,
                    "description": integration_class.description
                })
            
            return self.success_response(json.dumps(available_integrations, indent=2))
        except Exception as e:
            logger.error(f"Error listing available integrations: {str(e)}")
            return self.fail_response(f"Failed to list available integrations: {str(e)}")
    
    @openapi_schema({
        "name": "list_account_integrations",
        "description": "List all integrations for the current account",
        "parameters": {
            "type": "object",
            "properties": {},
            "required": []
        }
    })
    @xml_schema(
        tag_name="list-account-integrations",
        mappings=[],
        example='''
        <list-account-integrations />
        '''
    )
    async def list_account_integrations(self) -> ToolResult:
        """List all integrations for the current account.
        
        Returns:
            ToolResult with success status and list of account integrations
        """
        try:
            if not self.account_id:
                if self.project_id:
                    # Try to get the account ID from the project
                    response = await self.supabase.table('projects').select('account_id').eq('project_id', self.project_id).execute()
                    if response.data:
                        self.account_id = response.data[0].get('account_id')
                    else:
                        return self.fail_response("No account ID available and could not determine it from project ID")
                else:
                    return self.fail_response("No account ID available")
            
            integrations = await self.integration_manager.get_account_integrations(self.account_id)
            
            return self.success_response(json.dumps(integrations, indent=2))
        except Exception as e:
            logger.error(f"Error listing account integrations: {str(e)}")
            return self.fail_response(f"Failed to list account integrations: {str(e)}")
    
    @openapi_schema({
        "name": "get_integration_status",
        "description": "Get the status of a specific integration",
        "parameters": {
            "type": "object",
            "properties": {
                "integration_id": {
                    "type": "string",
                    "description": "ID of the integration to get status for"
                }
            },
            "required": ["integration_id"]
        }
    })
    @xml_schema(
        tag_name="get-integration-status",
        mappings=[
            {"param_name": "integration_id", "node_type": "attribute", "path": "."}
        ],
        example='''
        <get-integration-status integration_id="550e8400-e29b-41d4-a716-************" />
        '''
    )
    async def get_integration_status(self, integration_id: str) -> ToolResult:
        """Get the status of a specific integration.
        
        Args:
            integration_id: ID of the integration to get status for
            
        Returns:
            ToolResult with success status and integration status
        """
        try:
            integration = await self.integration_manager.get_integration(integration_id)
            
            if not integration:
                return self.fail_response(f"Integration {integration_id} not found")
            
            status = await integration.get_status()
            
            return self.success_response(json.dumps(status, indent=2))
        except Exception as e:
            logger.error(f"Error getting integration status: {str(e)}")
            return self.fail_response(f"Failed to get integration status: {str(e)}")
    
    @openapi_schema({
        "name": "get_integration_capabilities",
        "description": "Get the capabilities of a specific integration",
        "parameters": {
            "type": "object",
            "properties": {
                "integration_id": {
                    "type": "string",
                    "description": "ID of the integration to get capabilities for"
                }
            },
            "required": ["integration_id"]
        }
    })
    @xml_schema(
        tag_name="get-integration-capabilities",
        mappings=[
            {"param_name": "integration_id", "node_type": "attribute", "path": "."}
        ],
        example='''
        <get-integration-capabilities integration_id="550e8400-e29b-41d4-a716-************" />
        '''
    )
    async def get_integration_capabilities(self, integration_id: str) -> ToolResult:
        """Get the capabilities of a specific integration.
        
        Args:
            integration_id: ID of the integration to get capabilities for
            
        Returns:
            ToolResult with success status and integration capabilities
        """
        try:
            integration = await self.integration_manager.get_integration(integration_id)
            
            if not integration:
                return self.fail_response(f"Integration {integration_id} not found")
            
            capabilities = await integration.get_capabilities()
            
            return self.success_response(json.dumps(capabilities, indent=2))
        except Exception as e:
            logger.error(f"Error getting integration capabilities: {str(e)}")
            return self.fail_response(f"Failed to get integration capabilities: {str(e)}")
    
    @openapi_schema({
        "name": "execute_integration_action",
        "description": "Execute an action on a specific integration",
        "parameters": {
            "type": "object",
            "properties": {
                "integration_id": {
                    "type": "string",
                    "description": "ID of the integration to execute the action on"
                },
                "action": {
                    "type": "string",
                    "description": "Name of the action to execute"
                },
                "params": {
                    "type": "object",
                    "description": "Parameters for the action"
                }
            },
            "required": ["integration_id", "action", "params"]
        }
    })
    @xml_schema(
        tag_name="execute-integration-action",
        mappings=[
            {"param_name": "integration_id", "node_type": "attribute", "path": "."},
            {"param_name": "action", "node_type": "attribute", "path": "."},
            {"param_name": "params", "node_type": "element", "path": "params"}
        ],
        example='''
        <execute-integration-action integration_id="550e8400-e29b-41d4-a716-************" action="list_repositories">
            <params>
                {"org": "example-org", "per_page": 10}
            </params>
        </execute-integration-action>
        '''
    )
    async def execute_integration_action(self, integration_id: str, action: str, params: Dict[str, Any]) -> ToolResult:
        """Execute an action on a specific integration.
        
        Args:
            integration_id: ID of the integration to execute the action on
            action: Name of the action to execute
            params: Parameters for the action
            
        Returns:
            ToolResult with success status and action result
        """
        try:
            result = await self.integration_manager.execute_integration_action(
                integration_id=integration_id,
                action=action,
                params=params
            )
            
            if not result.get("success", False):
                return self.fail_response(result.get("error", "Failed to execute action"))
            
            return self.success_response(json.dumps(result, indent=2))
        except Exception as e:
            logger.error(f"Error executing integration action: {str(e)}")
            return self.fail_response(f"Failed to execute integration action: {str(e)}")
    
    @openapi_schema({
        "name": "find_integration_by_type",
        "description": "Find an integration by its type",
        "parameters": {
            "type": "object",
            "properties": {
                "integration_type": {
                    "type": "string",
                    "description": "Type of integration to find (e.g., 'github', 'gmail')"
                }
            },
            "required": ["integration_type"]
        }
    })
    @xml_schema(
        tag_name="find-integration-by-type",
        mappings=[
            {"param_name": "integration_type", "node_type": "attribute", "path": "."}
        ],
        example='''
        <find-integration-by-type integration_type="github" />
        '''
    )
    async def find_integration_by_type(self, integration_type: str) -> ToolResult:
        """Find an integration by its type.
        
        Args:
            integration_type: Type of integration to find (e.g., 'github', 'gmail')
            
        Returns:
            ToolResult with success status and integration details
        """
        try:
            if not self.account_id:
                if self.project_id:
                    # Try to get the account ID from the project
                    response = await self.supabase.table('projects').select('account_id').eq('project_id', self.project_id).execute()
                    if response.data:
                        self.account_id = response.data[0].get('account_id')
                    else:
                        return self.fail_response("No account ID available and could not determine it from project ID")
                else:
                    return self.fail_response("No account ID available")
            
            integrations = await self.integration_manager.get_account_integrations(self.account_id)
            
            matching_integrations = [
                integration for integration in integrations 
                if integration.get("integration_type") == integration_type
            ]
            
            if not matching_integrations:
                return self.fail_response(f"No {integration_type} integration found for this account")
            
            # Return the first matching integration
            integration = matching_integrations[0]
            
            return self.success_response(json.dumps(integration, indent=2))
        except Exception as e:
            logger.error(f"Error finding integration by type: {str(e)}")
            return self.fail_response(f"Failed to find integration by type: {str(e)}")
    
    @openapi_schema({
        "name": "create_integration",
        "description": "Create a new integration for the current account",
        "parameters": {
            "type": "object",
            "properties": {
                "integration_type": {
                    "type": "string",
                    "description": "Type of integration to create (e.g., 'github', 'gmail')"
                },
                "config": {
                    "type": "object",
                    "description": "Configuration for the integration"
                },
                "integration_mode": {
                    "type": "string",
                    "description": "Mode of the integration ('company' or 'user')",
                    "default": "company"
                }
            },
            "required": ["integration_type", "config"]
        }
    })
    @xml_schema(
        tag_name="create-integration",
        mappings=[
            {"param_name": "integration_type", "node_type": "attribute", "path": "."},
            {"param_name": "config", "node_type": "element", "path": "config"},
            {"param_name": "integration_mode", "node_type": "attribute", "path": ".", "required": False}
        ],
        example='''
        <create-integration integration_type="github" integration_mode="company">
            <config>
                {"access_token": "ghp_1234567890abcdef"}
            </config>
        </create-integration>
        '''
    )
    async def create_integration(self, 
                               integration_type: str, 
                               config: Dict[str, Any],
                               integration_mode: str = "company") -> ToolResult:
        """Create a new integration for the current account.
        
        Args:
            integration_type: Type of integration to create (e.g., 'github', 'gmail')
            config: Configuration for the integration
            integration_mode: Mode of the integration ('company' or 'user')
            
        Returns:
            ToolResult with success status and integration ID
        """
        try:
            if not self.account_id:
                if self.project_id:
                    # Try to get the account ID from the project
                    response = await self.supabase.table('projects').select('account_id').eq('project_id', self.project_id).execute()
                    if response.data:
                        self.account_id = response.data[0].get('account_id')
                    else:
                        return self.fail_response("No account ID available and could not determine it from project ID")
                else:
                    return self.fail_response("No account ID available")
            
            # Get the user ID from the project
            response = await self.supabase.table('projects').select('created_by').eq('project_id', self.project_id).execute()
            user_id = response.data[0].get('created_by') if response.data else None
            
            if not user_id:
                return self.fail_response("Could not determine user ID")
            
            integration_id = await self.integration_manager.create_integration(
                integration_type=integration_type,
                account_id=self.account_id,
                user_id=user_id,
                config=config,
                integration_mode=integration_mode
            )
            
            if not integration_id:
                return self.fail_response("Failed to create integration")
            
            return self.success_response(json.dumps({"integration_id": integration_id}, indent=2))
        except Exception as e:
            logger.error(f"Error creating integration: {str(e)}")
            return self.fail_response(f"Failed to create integration: {str(e)}")
    
    @openapi_schema({
        "name": "update_integration",
        "description": "Update an existing integration",
        "parameters": {
            "type": "object",
            "properties": {
                "integration_id": {
                    "type": "string",
                    "description": "ID of the integration to update"
                },
                "config": {
                    "type": "object",
                    "description": "New configuration for the integration"
                }
            },
            "required": ["integration_id", "config"]
        }
    })
    @xml_schema(
        tag_name="update-integration",
        mappings=[
            {"param_name": "integration_id", "node_type": "attribute", "path": "."},
            {"param_name": "config", "node_type": "element", "path": "config"}
        ],
        example='''
        <update-integration integration_id="550e8400-e29b-41d4-a716-************">
            <config>
                {"access_token": "ghp_updated_token"}
            </config>
        </update-integration>
        '''
    )
    async def update_integration(self, integration_id: str, config: Dict[str, Any]) -> ToolResult:
        """Update an existing integration.
        
        Args:
            integration_id: ID of the integration to update
            config: New configuration for the integration
            
        Returns:
            ToolResult with success status
        """
        try:
            success = await self.integration_manager.update_integration(
                integration_id=integration_id,
                config=config
            )
            
            if not success:
                return self.fail_response("Failed to update integration")
            
            return self.success_response(json.dumps({"success": True}, indent=2))
        except Exception as e:
            logger.error(f"Error updating integration: {str(e)}")
            return self.fail_response(f"Failed to update integration: {str(e)}")
    
    @openapi_schema({
        "name": "delete_integration",
        "description": "Delete an existing integration",
        "parameters": {
            "type": "object",
            "properties": {
                "integration_id": {
                    "type": "string",
                    "description": "ID of the integration to delete"
                }
            },
            "required": ["integration_id"]
        }
    })
    @xml_schema(
        tag_name="delete-integration",
        mappings=[
            {"param_name": "integration_id", "node_type": "attribute", "path": "."}
        ],
        example='''
        <delete-integration integration_id="550e8400-e29b-41d4-a716-************" />
        '''
    )
    async def delete_integration(self, integration_id: str) -> ToolResult:
        """Delete an existing integration.
        
        Args:
            integration_id: ID of the integration to delete
            
        Returns:
            ToolResult with success status
        """
        try:
            success = await self.integration_manager.delete_integration(integration_id)
            
            if not success:
                return self.fail_response("Failed to delete integration")
            
            return self.success_response(json.dumps({"success": True}, indent=2))
        except Exception as e:
            logger.error(f"Error deleting integration: {str(e)}")
            return self.fail_response(f"Failed to delete integration: {str(e)}")
