'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import {
  Mic,
  MicOff,
  Video,
  VideoOff,
  PhoneOff,
  Volume2,
  VolumeX,
  ChevronUp,
  ChevronDown,
  ScreenShare
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { CallParticipant } from '@/components/project/chat/types';
import { useSidebar } from '@/components/ui/sidebar';

interface SidebarCallInterfaceProps {
  isCallActive: boolean;
  callType: 'audio' | 'video';
  callParticipants: CallParticipant[];
  callStartTime: Date | null;
  onEndCall: () => void;
}

export function SidebarCallInterface({
  isCallActive,
  callType,
  callParticipants,
  callStartTime,
  onEndCall,
}: SidebarCallInterfaceProps) {
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOn, setIsVideoOn] = useState(callType === 'video');
  const [isSpeakerMuted, setIsSpeakerMuted] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const [isExpanded, setIsExpanded] = useState(true);
  const { state } = useSidebar();

  // Update call duration
  useEffect(() => {
    if (!isCallActive || !callStartTime) return;

    const interval = setInterval(() => {
      const now = new Date();
      const duration = Math.floor((now.getTime() - callStartTime.getTime()) / 1000);
      setCallDuration(duration);
    }, 1000);

    return () => clearInterval(interval);
  }, [isCallActive, callStartTime]);

  // Format call duration as MM:SS
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Get call display info
  const getCallInfo = () => {
    if (callParticipants.length === 1) {
      const participant = callParticipants[0];
      return {
        name: participant.name,
        avatar: participant.avatar,
        subtitle: participant.role
      };
    } else if (callParticipants.length > 1) {
      return {
        name: 'Team Chat',
        avatar: null,
        subtitle: `${callParticipants.length} participants`
      };
    }
    return {
      name: 'Unknown',
      avatar: null,
      subtitle: 'Call'
    };
  };

  if (!isCallActive) return null;

  const callInfo = getCallInfo();

  return (
    <div className={cn(
      "mx-2",
      state === 'collapsed' ? "" : "bg-card rounded-t-md"
    )}>
      {state !== 'collapsed' && (
        <Button
          variant="ghost"
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full justify-between p-3 h-auto hover:bg-accent rounded-md"
        >
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-sm font-medium text-foreground">
              {callType === 'video' ? 'Video Call' : 'Voice Call'}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">
              {formatDuration(callDuration)}
            </span>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            ) : (
              <ChevronUp className="h-4 w-4 text-muted-foreground" />
            )}
          </div>
        </Button>
      )}

      {state !== 'collapsed' && isExpanded && (
        <div className="space-y-3 p-3 pt-0">
          {/* Participant Info */}
          <div className="flex items-center gap-3">
            {callInfo.avatar ? (
              <div className="w-8 h-8 rounded-md overflow-hidden">
                <Image
                  src={callInfo.avatar}
                  alt={callInfo.name}
                  width={32}
                  height={32}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="w-8 h-8 rounded-md bg-primary flex items-center justify-center text-primary-foreground text-xs font-medium">
                {callInfo.name.charAt(0)}
              </div>
            )}

            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-foreground truncate">
                {callInfo.name}
              </div>
              <div className="text-xs text-muted-foreground truncate">
                {callInfo.subtitle}
              </div>
            </div>
          </div>

          {/* Call Controls */}
          <div className="grid grid-cols-5 gap-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsMuted(!isMuted)}
              className={cn(
                "h-8 w-8 p-0 rounded-md",
                isMuted
                  ? "bg-destructive/20 text-destructive hover:bg-destructive/30"
                  : "bg-muted text-foreground hover:bg-accent"
              )}
            >
              {isMuted ? <MicOff className="h-3 w-3" /> : <Mic className="h-3 w-3" />}
            </Button>

            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsVideoOn(!isVideoOn)}
              className={cn(
                "h-8 w-8 p-0 rounded-md",
                !isVideoOn
                  ? "bg-destructive/20 text-destructive hover:bg-destructive/30"
                  : "bg-muted text-foreground hover:bg-accent"
              )}
            >
              {isVideoOn ? <Video className="h-3 w-3" /> : <VideoOff className="h-3 w-3" />}
            </Button>

            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsScreenSharing(!isScreenSharing)}
              className={cn(
                "h-8 w-8 p-0 rounded-md",
                isScreenSharing
                  ? "bg-primary/20 text-primary hover:bg-primary/30"
                  : "bg-muted text-foreground hover:bg-accent"
              )}
            >
              <ScreenShare className="h-3 w-3" />
            </Button>

            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsSpeakerMuted(!isSpeakerMuted)}
              className={cn(
                "h-8 w-8 p-0 rounded-md",
                isSpeakerMuted
                  ? "bg-destructive/20 text-destructive hover:bg-destructive/30"
                  : "bg-muted text-foreground hover:bg-accent"
              )}
            >
              {isSpeakerMuted ? <VolumeX className="h-3 w-3" /> : <Volume2 className="h-3 w-3" />}
            </Button>

            <Button
              size="sm"
              variant="ghost"
              onClick={onEndCall}
              className="h-8 w-8 p-0 rounded-md bg-destructive/20 text-destructive hover:bg-destructive/30"
            >
              <PhoneOff className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}

      {/* Collapsed state - centered controls */}
      {state === 'collapsed' && (
        <div className="flex flex-col items-center gap-2 py-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />

          <Button
            size="sm"
            variant="ghost"
            onClick={() => setIsMuted(!isMuted)}
            className={cn(
              "h-8 w-8 p-0 rounded-md",
              isMuted
                ? "bg-destructive/20 text-destructive hover:bg-destructive/30"
                : "bg-muted text-foreground hover:bg-accent"
            )}
          >
            {isMuted ? <MicOff className="h-3 w-3" /> : <Mic className="h-3 w-3" />}
          </Button>

          <Button
            size="sm"
            variant="ghost"
            onClick={onEndCall}
            className="h-8 w-8 p-0 rounded-md bg-destructive/20 text-destructive hover:bg-destructive/30"
          >
            <PhoneOff className="h-3 w-3" />
          </Button>
        </div>
      )}
      </div>
  );
}
