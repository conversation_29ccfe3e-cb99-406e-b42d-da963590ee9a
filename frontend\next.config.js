/** @type {import('next').NextConfig} */
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const nextConfig = {
  reactStrictMode: true,
  // Disable chunk optimization to fix chunk loading errors
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Disable code splitting to fix chunk loading errors
      config.optimization.splitChunks = false;

      // Prevent the 'window is not defined' error
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false, 
      };

      // Add mini-css-extract-plugin for font loading
      config.plugins.push(new MiniCssExtractPlugin());
    }
    return config;
  },
  // Disable static optimization to prevent chunk loading errors
  experimental: {
    disableOptimizedLoading: true,
  },
};

module.exports = nextConfig;
