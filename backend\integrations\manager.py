from typing import Dict, Any, Optional, List, Union
import asyncio
import json
from datetime import datetime

from utils.logger import logger
from integrations.base import BaseIntegration, IntegrationType, IntegrationStatus
from integrations import registry

class IntegrationManager:
    def __init__(self, supabase_client):
        self.supabase = supabase_client
        self.integrations_cache = {}
    
    async def get_integration(self, integration_id: str) -> Optional[BaseIntegration]:
        if integration_id in self.integrations_cache:
            return self.integrations_cache[integration_id]
        
        try:
            response = await self.supabase.table('integrations').select('*').eq('integration_id', integration_id).execute()
            
            if not response.data:
                return None
                
            integration_data = response.data[0]
            integration_class = registry.get_integration(integration_data['integration_type'])
            
            if not integration_class:
                logger.error(f"Integration type {integration_data['integration_type']} not found in registry")
                return None
                
            integration = integration_class(
                integration_id=integration_data['integration_id'],
                account_id=integration_data['account_id'],
                integration_type=IntegrationType(integration_data['integration_type']),
                config=integration_data.get('config', {})
            )
            
            await integration.initialize()
            self.integrations_cache[integration_id] = integration
            return integration
            
        except Exception as e:
            logger.error(f"Error getting integration {integration_id}: {str(e)}")
            return None
    
    async def create_integration(self, 
                                integration_type: str, 
                                account_id: str, 
                                user_id: str,
                                config: Dict[str, Any],
                                integration_mode: str = "company") -> Optional[str]:
        try:
            integration_class = registry.get_integration(integration_type)
            
            if not integration_class:
                logger.error(f"Integration type {integration_type} not found in registry")
                return None
            
            response = await self.supabase.table('integrations').insert({
                'integration_type': integration_type,
                'account_id': account_id,
                'created_by': user_id,
                'config': config,
                'status': IntegrationStatus.PENDING.value,
                'integration_mode': integration_mode
            }).execute()
            
            if not response.data:
                logger.error("Failed to create integration record")
                return None
                
            integration_id = response.data[0]['integration_id']
            
            integration = integration_class(
                integration_id=integration_id,
                account_id=account_id,
                integration_type=IntegrationType(integration_mode),
                config=config
            )
            
            initialized = await integration.initialize()
            
            if initialized:
                await self.supabase.table('integrations').update({
                    'status': IntegrationStatus.ACTIVE.value,
                    'last_sync': datetime.now().isoformat()
                }).eq('integration_id', integration_id).execute()
                
                self.integrations_cache[integration_id] = integration
                return integration_id
            else:
                await self.supabase.table('integrations').update({
                    'status': IntegrationStatus.ERROR.value,
                    'last_error': integration.last_error
                }).eq('integration_id', integration_id).execute()
                return None
                
        except Exception as e:
            logger.error(f"Error creating integration: {str(e)}")
            return None
    
    async def update_integration(self, 
                               integration_id: str, 
                               config: Dict[str, Any]) -> bool:
        try:
            integration = await self.get_integration(integration_id)
            
            if not integration:
                logger.error(f"Integration {integration_id} not found")
                return False
                
            integration.config.update(config)
            
            initialized = await integration.initialize()
            
            status = IntegrationStatus.ACTIVE if initialized else IntegrationStatus.ERROR
            
            await self.supabase.table('integrations').update({
                'config': integration.config,
                'status': status.value,
                'last_error': integration.last_error,
                'last_sync': datetime.now().isoformat()
            }).eq('integration_id', integration_id).execute()
            
            return initialized
            
        except Exception as e:
            logger.error(f"Error updating integration {integration_id}: {str(e)}")
            return False
    
    async def delete_integration(self, integration_id: str) -> bool:
        try:
            if integration_id in self.integrations_cache:
                del self.integrations_cache[integration_id]
                
            await self.supabase.table('integrations').delete().eq('integration_id', integration_id).execute()
            return True
            
        except Exception as e:
            logger.error(f"Error deleting integration {integration_id}: {str(e)}")
            return False
    
    async def get_account_integrations(self, account_id: str) -> List[Dict[str, Any]]:
        try:
            response = await self.supabase.table('integrations').select('*').eq('account_id', account_id).execute()
            
            if not response.data:
                return []
                
            return response.data
            
        except Exception as e:
            logger.error(f"Error getting integrations for account {account_id}: {str(e)}")
            return []
    
    async def execute_integration_action(self, 
                                       integration_id: str, 
                                       action: str, 
                                       params: Dict[str, Any]) -> Dict[str, Any]:
        try:
            integration = await self.get_integration(integration_id)
            
            if not integration:
                return {"success": False, "error": f"Integration {integration_id} not found"}
                
            result = await integration.execute_action(action, params)
            return result
            
        except Exception as e:
            logger.error(f"Error executing action {action} for integration {integration_id}: {str(e)}")
            return {"success": False, "error": str(e)}
