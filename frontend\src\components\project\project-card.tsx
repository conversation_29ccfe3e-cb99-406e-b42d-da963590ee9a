import React from 'react';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronRight } from 'lucide-react';
import Link from 'next/link';

interface ProjectCardProps {
  id: string;
  name: string;
  description?: string;
  agentCount?: number;
  lastActivity?: string;
  isActive?: boolean;
}

export function ProjectCardOld({
  id,
  name,
  description,
  agentCount = 0,
  lastActivity,
  isActive = true
}: ProjectCardProps) {
  return (
    <Card className={`h-full flex flex-col ${isActive ? 'border-border' : 'border-muted'}`}>
      <CardContent className="flex-1 p-6">
        <div className="space-y-2">
          <h3 className="font-medium text-lg">{name}</h3>
          {description && (
            <p className="text-sm text-muted-foreground line-clamp-2">{description}</p>
          )}
        </div>
        <div className="mt-4 flex items-center text-xs text-muted-foreground">
          <div className="flex items-center">
            <span>{agentCount} agents</span>
          </div>
          {lastActivity && (
            <>
              <span className="mx-2">•</span>
              <span>{lastActivity}</span>
            </>
          )}
        </div>
        <div className="mt-2">
          <span className={`text-xs px-2 py-1 rounded-full ${isActive ? 'bg-primary/10 text-primary' : 'bg-muted text-muted-foreground'}`}>
            {isActive ? 'active' : 'inactive'}
          </span>
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0 mt-auto">
        <Button asChild variant="ghost" className="w-full justify-between">
          <Link href={`/project/${id}`}>
            View Project <ChevronRight className="h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
