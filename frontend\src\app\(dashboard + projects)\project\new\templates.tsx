"use client";

import React from 'react';
import { TemplateCard } from '@/components/project/template-card';
import { Code, FileText, Users } from 'lucide-react';

interface TemplateSelectionProps {
  data: {
    templateId: string;
  };
  updateData: (data: Partial<{
    templateId: string;
  }>) => void;
}

const templates = [
  {
    id: 'ai-developer',
    name: 'AI Developer Team',
    description: 'Build technical solutions and development projects',
    icon: <Code className="h-6 w-6 text-primary" />,
  },
  {
    id: 'content-creation',
    name: 'Content Creation',
    description: 'Create marketing content and social media campaigns',
    icon: <FileText className="h-6 w-6 text-primary" />,
  },
  {
    id: 'custom-team',
    name: 'Custom Team',
    description: 'Build your project with a fully customized team',
    icon: <Users className="h-6 w-6 text-primary" />,
  },
];

export function TemplateSelection({ data, updateData }: TemplateSelectionProps) {
  const handleSelectTemplate = (templateId: string) => {
    updateData({ templateId });
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2">Starting Templates</h2>
        <p className="text-muted-foreground max-w-md mx-auto">
          Choose a template to get started or build your own custom team
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        {templates.map((template) => (
          <TemplateCard
            key={template.id}
            id={template.id}
            name={template.name}
            description={template.description}
            icon={template.icon}
            selected={data.templateId === template.id}
            onClick={() => handleSelectTemplate(template.id)}
          />
        ))}
      </div>
    </div>
  );
}
