"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@replit+codemirror-lang-solidity@6.0.2_@codemirror+language@6.11.0";
exports.ids = ["vendor-chunks/@replit+codemirror-lang-solidity@6.0.2_@codemirror+language@6.11.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@replit+codemirror-lang-solidity@6.0.2_@codemirror+language@6.11.0/node_modules/@replit/codemirror-lang-solidity/dist/index.js":
/*!***********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@replit+codemirror-lang-solidity@6.0.2_@codemirror+language@6.11.0/node_modules/@replit/codemirror-lang-solidity/dist/index.js ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parser: () => (/* binding */ parser),\n/* harmony export */   solidity: () => (/* binding */ solidity)\n/* harmony export */ });\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js\");\n\n\n\n/**\n* Wrapper around the legacy CM5 Solidity language mode\n* See: https://github.com/alincode/codemirror-solidity\n*/\nconst keywords = {\n    pragma: true,\n    solidity: true,\n    import: true,\n    as: true,\n    from: true,\n    contract: true,\n    constructor: true,\n    is: true,\n    function: true,\n    modifier: true,\n    // modifiers\n    pure: true,\n    view: true,\n    payable: true,\n    constant: true,\n    anonymous: true,\n    indexed: true,\n    returns: true,\n    return: true,\n    event: true,\n    struct: true,\n    mapping: true,\n    interface: true,\n    using: true,\n    library: true,\n    storage: true,\n    memory: true,\n    calldata: true,\n    public: true,\n    private: true,\n    external: true,\n    internal: true,\n    emit: true,\n    assembly: true,\n    abstract: true,\n    after: true,\n    catch: true,\n    final: true,\n    in: true,\n    inline: true,\n    let: true,\n    match: true,\n    null: true,\n    of: true,\n    relocatable: true,\n    static: true,\n    try: true,\n    typeof: true,\n    var: true,\n};\nconst keywordsSpecial = {\n    pragma: true,\n    returns: true,\n    address: true,\n    contract: true,\n    function: true,\n    struct: true,\n};\nconst keywordsEtherUnit = {\n    wei: true,\n    szabo: true,\n    finney: true,\n    ether: true,\n};\nconst keywordsTimeUnit = {\n    seconds: true,\n    minutes: true,\n    hours: true,\n    days: true,\n    weeks: true,\n};\nconst keywordsBlockAndTransactionProperties = {\n    block: ['coinbase', 'difficulty', 'gaslimit', 'number', 'timestamp'],\n    msg: ['data', 'sender', 'sig', 'value'],\n    tx: ['gasprice', 'origin'],\n};\nconst keywordsMoreBlockAndTransactionProperties = {\n    now: true,\n    gasleft: true,\n    blockhash: true,\n};\nconst keywordsErrorHandling = {\n    assert: true,\n    require: true,\n    revert: true,\n    throw: true,\n};\nconst keywordsMathematicalAndCryptographicFuctions = {\n    addmod: true,\n    mulmod: true,\n    keccak256: true,\n    sha256: true,\n    ripemd160: true,\n    ecrecover: true,\n};\nconst keywordsContractRelated = {\n    this: true,\n    selfdestruct: true,\n    super: true,\n};\nconst keywordsTypeInformation = { type: true };\nconst keywordsContractList = {};\nconst keywordsControlStructures = {\n    if: true,\n    else: true,\n    while: true,\n    do: true,\n    for: true,\n    break: true,\n    continue: true,\n    switch: true,\n    case: true,\n    default: true,\n};\nconst keywordsValueTypes = {\n    bool: true,\n    byte: true,\n    string: true,\n    enum: true,\n    address: true,\n};\nconst keywordsV0505NewReserve = {\n    alias: true,\n    apply: true,\n    auto: true,\n    copyof: true,\n    define: true,\n    immutable: true,\n    implements: true,\n    macro: true,\n    mutable: true,\n    override: true,\n    partial: true,\n    promise: true,\n    reference: true,\n    sealed: true,\n    sizeof: true,\n    supports: true,\n    typedef: true,\n    unchecked: true,\n};\nconst keywordsAbiEncodeDecodeFunctions = {\n    abi: [\n        'decode',\n        'encodePacked',\n        'encodeWithSelector',\n        'encodeWithSignature',\n        'encode',\n    ],\n};\nconst keywordsMembersOfAddressType = [\n    'transfer',\n    'send',\n    'balance',\n    'call',\n    'delegatecall',\n    'staticcall',\n];\nconst natSpecTags = ['title', 'author', 'notice', 'dev', 'param', 'return'];\nconst atoms = {\n    delete: true,\n    new: true,\n    true: true,\n    false: true,\n};\nconst isOperatorChar = /[+\\-*&^%:=<>!|/~]/;\nconst isNegativeChar = /[-]/;\nlet curPunc;\nfunction tokenBase(stream, state) {\n    let ch = stream.next();\n    if (ch === '\"' || ch === \"'\" || ch === '`') {\n        state.tokenize = tokenString(ch);\n        return state.tokenize(stream, state);\n    }\n    if (isVersion(stream, state)) {\n        return 'version';\n    }\n    if (ch === '.' &&\n        keywordsMembersOfAddressType.some(function (item) {\n            return stream.match(`${item}`);\n        })) {\n        return 'addressFunction';\n    }\n    if (typeof ch === 'string' && isNumber(ch, stream)) {\n        return 'number';\n    }\n    if (typeof ch === 'string' && /[[\\]{}(),;:.]/.test(ch)) {\n        return updateGarmmer(ch, state);\n    }\n    if (ch === '/') {\n        if (stream.eat('*')) {\n            state.tokenize = tokenComment;\n            return tokenComment(stream, state);\n        }\n        if (stream.match(/\\/{2}/)) {\n            ch = stream.next();\n            while (ch) {\n                if (ch === '@') {\n                    stream.backUp(1);\n                    state.grammar = 'doc';\n                    break;\n                }\n                ch = stream.next();\n            }\n            return 'doc';\n        }\n        if (stream.eat('/')) {\n            stream.skipToEnd();\n            return 'comment';\n        }\n    }\n    if (typeof ch === 'string' && isNegativeChar.test(ch)) {\n        const peeked = stream.peek();\n        if (typeof peeked === 'string' && isNumber(peeked, stream)) {\n            return 'number';\n        }\n        return 'operator';\n    }\n    if (typeof ch === 'string' && isOperatorChar.test(ch)) {\n        stream.eatWhile(isOperatorChar);\n        return 'operator';\n    }\n    stream.eatWhile(/[\\w$_\\xa1-\\uffff]/);\n    const cur = stream.current();\n    if (state.grammar === 'doc') {\n        if (natSpecTags.some(function (item) {\n            return cur === `@${item}`;\n        })) {\n            return 'docReserve';\n        }\n        return 'doc';\n    }\n    if (cur === 'solidity' && state.lastToken === 'pragma') {\n        state.lastToken = state.lastToken + ' ' + cur;\n    }\n    if (Object.prototype.propertyIsEnumerable.call(keywords, cur)) {\n        if (cur === 'case' || cur === 'default') {\n            curPunc = 'case';\n        }\n        if (Object.prototype.propertyIsEnumerable.call(keywordsSpecial, cur)) {\n            state.lastToken = cur;\n        }\n        //if (cur === 'function' && state.para === 'parameterMode')\n        return 'keyword';\n    }\n    if (Object.prototype.propertyIsEnumerable.call(keywordsEtherUnit, cur)) {\n        return 'etherUnit';\n    }\n    if (Object.prototype.propertyIsEnumerable.call(keywordsContractRelated, cur)) {\n        return 'contractRelated';\n    }\n    if (Object.prototype.propertyIsEnumerable.call(keywordsControlStructures, cur) ||\n        Object.prototype.propertyIsEnumerable.call(keywordsTypeInformation, cur) ||\n        Object.prototype.propertyIsEnumerable.call(keywordsV0505NewReserve, cur)) {\n        return 'keyword';\n    }\n    if (Object.prototype.propertyIsEnumerable.call(keywordsValueTypes, cur) ||\n        Object.prototype.propertyIsEnumerable.call(keywordsTimeUnit, cur) ||\n        isValidInteger(cur) ||\n        isValidBytes(cur) ||\n        isValidFixed(cur)) {\n        state.lastToken += 'variable';\n        return 'keyword';\n    }\n    if (Object.prototype.propertyIsEnumerable.call(atoms, cur)) {\n        return 'atom';\n    }\n    if (Object.prototype.propertyIsEnumerable.call(keywordsErrorHandling, cur)) {\n        return 'errorHandling';\n    }\n    if (Object.prototype.propertyIsEnumerable.call(keywordsMathematicalAndCryptographicFuctions, cur)) {\n        return 'mathematicalAndCryptographic';\n    }\n    if (Object.prototype.propertyIsEnumerable.call(keywordsMoreBlockAndTransactionProperties, cur) ||\n        (Object.prototype.propertyIsEnumerable.call(keywordsBlockAndTransactionProperties, cur) &&\n            keywordsBlockAndTransactionProperties[cur].some(function (item) {\n                return stream.match(`.${item}`);\n            }))) {\n        return 'variable-2';\n    }\n    if (cur === 'abi' &&\n        keywordsAbiEncodeDecodeFunctions[cur].some(function (item) {\n            return stream.match(`.${item}`);\n        })) {\n        return 'abi';\n    }\n    const style = updateHexLiterals(cur, stream);\n    if (style != null) {\n        return style;\n    }\n    if ((state.lastToken === 'functionName(' || state.lastToken === 'returns(') &&\n        Object.prototype.propertyIsEnumerable.call(keywordsContractList, cur)) {\n        state.lastToken += 'variable';\n        return 'variable';\n    }\n    if (state.lastToken === 'function') {\n        state.lastToken = 'functionName';\n        if (state.para == null) {\n            state.grammar = 'function';\n            state.para = '';\n        }\n        //state.parasMode = isNaN(state.parasMode) ? 1 : state.functionLayerCount++;\n        state.para += 'functionName';\n        return 'functionName';\n    }\n    if (state.lastToken === 'functionName(variable') {\n        state.lastToken = 'functionName(';\n        return 'parameterValue';\n    }\n    if (state.lastToken === 'returns(variable') {\n        state.lastToken = 'returns(';\n        return 'parameterValue';\n    }\n    if (state.lastToken === 'address' && cur === 'payable') {\n        state.lastToken = 'address payable';\n    }\n    if (state.lastToken === 'contract' || state.lastToken === 'struct') {\n        keywordsContractList[cur] = true;\n        state.lastToken = null;\n    }\n    if (state.grammar === 'function') {\n        return 'parameterValue';\n    }\n    return 'variable';\n}\nfunction tokenString(quote) {\n    return function (stream, state) {\n        let escaped = false;\n        let next;\n        let end = false;\n        next = stream.next();\n        while (next != null) {\n            if (next === quote && !escaped) {\n                end = true;\n                break;\n            }\n            escaped = !escaped && quote !== '`' && next === '\\\\';\n            next = stream.next();\n        }\n        if (end || !(escaped || quote === '`')) {\n            state.tokenize = tokenBase;\n        }\n        return 'string';\n    };\n}\nfunction tokenComment(stream, state) {\n    let maybeEnd = false;\n    let ch = stream.next();\n    while (ch) {\n        if (ch === '/' && maybeEnd) {\n            state.tokenize = tokenBase;\n            break;\n        }\n        maybeEnd = ch === '*';\n        ch = stream.next();\n    }\n    return 'comment';\n}\nfunction isVersion(stream, state) {\n    if (state.lastToken === 'pragma solidity') {\n        state.lastToken = null;\n        return (!state.startOfLine &&\n            (stream.match(/[\\^{0}][0-9.]+/) ||\n                stream.match(/[>=]+?[\\s]*[0-9.]+[\\s]*[<]?[\\s]*[0-9.]+/)));\n    }\n}\nfunction isNumber(ch, stream) {\n    if (/[\\d.]/.test(ch)) {\n        if (ch === '.') {\n            stream.match(/^[0-9]+([eE][-+]?[0-9]+)?/);\n        }\n        else if (ch === '0') {\n            if (!stream.match(/^[xX][0-9a-fA-F]+/)) {\n                stream.match(/^0[0-7]+/);\n            }\n        }\n        else {\n            stream.match(/^[0-9]*\\.?[0-9]*([eE][-+]?[0-9]+)?/);\n        }\n        return true;\n    }\n}\nfunction isValidInteger(token) {\n    if (token.match(/^[u]?int/)) {\n        if (token.indexOf('t') + 1 === token.length) {\n            return true;\n        }\n        const numberPart = Number(token.substr(token.indexOf('t') + 1, token.length));\n        return numberPart % 8 === 0 && numberPart <= 256;\n    }\n}\nfunction isValidBytes(token) {\n    if (token.match(/^bytes/)) {\n        if (token.indexOf('s') + 1 === token.length) {\n            return true;\n        }\n        const bytesPart = token.substr(token.indexOf('s') + 1, token.length);\n        return Number(bytesPart) <= 32;\n    }\n}\nfunction isValidFixed(token) {\n    if (token.match(/^[u]?fixed([0-9]+x[0-9]+)?/)) {\n        if (token.indexOf('d') + 1 === token.length) {\n            return true;\n        }\n        const numberPart = token\n            .substr(token.indexOf('d') + 1, token.length)\n            .split('x')\n            .map(Number);\n        return (numberPart[0] % 8 === 0 && numberPart[0] <= 256 && numberPart[1] <= 80);\n    }\n}\nfunction updateHexLiterals(token, stream) {\n    if (token.match(/^hex/) && stream.peek() === '\"') {\n        let maybeEnd = false;\n        let ch;\n        let hexValue = '';\n        let stringAfterHex = '';\n        ch = stream.next();\n        while (ch) {\n            stringAfterHex += ch;\n            if (ch === '\"' && maybeEnd) {\n                hexValue = stringAfterHex.substring(1, stringAfterHex.length - 1);\n                if (hexValue.match(/^[0-9a-fA-F]+$/)) {\n                    return 'number';\n                }\n                stream.backUp(stringAfterHex.length);\n                break;\n            }\n            maybeEnd = maybeEnd || ch === '\"';\n            ch = stream.next();\n        }\n    }\n}\nfunction updateGarmmer(ch, state) {\n    if (ch === ',' && state.para === 'functionName(variable') {\n        state.para = 'functionName(';\n    }\n    if (state.para != null && state.para.startsWith('functionName')) {\n        if (ch === ')') {\n            if (state.para.endsWith('(')) {\n                state.para = state.para.substr(0, state.para.length - 1);\n                if (state.para === 'functionName') {\n                    state.grammar = '';\n                }\n            }\n        }\n        else if (ch === '(') {\n            state.para += ch;\n        }\n    }\n    if (ch === '(' && state.lastToken === 'functionName') {\n        state.lastToken += ch;\n    }\n    else if (ch === ')' && state.lastToken === 'functionName(') {\n        state.lastToken = null;\n    }\n    else if (ch === '(' && state.lastToken === 'returns') {\n        state.lastToken += ch;\n    }\n    else if (ch === ')' &&\n        (state.lastToken === 'returns(' || state.lastToken === 'returns(variable')) {\n        state.lastToken = null;\n    }\n    if (ch === '(' && state.lastToken === 'address') {\n        state.lastToken += ch;\n    }\n    curPunc = ch;\n    return null;\n}\nclass Context {\n    constructor(indented, column, type, align, prev) {\n        this.indented = indented;\n        this.column = column;\n        this.type = type;\n        this.align = align;\n        this.prev = prev;\n    }\n}\nfunction pushContext(state, col, type) {\n    state.context = new Context(state.indented, col, type, null, state.context);\n    return state.context;\n}\nfunction popContext(state) {\n    if (!state.context.prev) {\n        return;\n    }\n    const t = state.context.type;\n    if (t === ')' || t === ']' || t === '}') {\n        state.indented = state.context.indented;\n    }\n    return (state.context = state.context.prev);\n}\nconst parser = {\n    startState(indentUnit) {\n        return {\n            tokenize: null,\n            context: new Context(0 - indentUnit, 0, 'top', false, null),\n            indented: 0,\n            startOfLine: true,\n            grammar: null,\n            lastToken: null,\n            para: null,\n        };\n    },\n    token(stream, state) {\n        const ctx = state.context;\n        if (stream.sol()) {\n            if (ctx.align == null) {\n                ctx.align = false;\n            }\n            state.indented = stream.indentation();\n            state.startOfLine = true;\n            if (ctx.type === 'case') {\n                ctx.type = '}';\n            }\n            if (state.grammar === 'doc') {\n                state.grammar = null;\n            }\n        }\n        if (stream.eatSpace()) {\n            return null;\n        }\n        curPunc = null;\n        const style = (state.tokenize || tokenBase)(stream, state);\n        if (style === 'comment') {\n            return style;\n        }\n        if (ctx.align == null) {\n            ctx.align = true;\n        }\n        if (curPunc === '{') {\n            pushContext(state, stream.column(), '}');\n        }\n        else if (curPunc === '[') {\n            pushContext(state, stream.column(), ']');\n        }\n        else if (curPunc === '(') {\n            pushContext(state, stream.column(), ')');\n        }\n        else if (curPunc === 'case') {\n            ctx.type = 'case';\n        }\n        else if (curPunc === '}' && ctx.type === '}') {\n            popContext(state);\n        }\n        else if (curPunc === ctx.type) {\n            popContext(state);\n        }\n        state.startOfLine = false;\n        return style;\n    },\n    indent(state, textAfter, indentContext) {\n        if (state.tokenize !== tokenBase && state.tokenize != null) {\n            return null;\n        }\n        const ctx = state.context;\n        const firstChar = textAfter && textAfter.charAt(0);\n        if (ctx.type === 'case' && /^(?:case|default)\\b/.test(textAfter)) {\n            state.context.type = '}';\n            return ctx.indented;\n        }\n        const closing = firstChar === ctx.type;\n        if (ctx.align) {\n            return ctx.column + (closing ? 0 : 1);\n        }\n        return ctx.indented + (closing ? 0 : indentContext.unit);\n    },\n    // @ts-ignore not specified in new stream parser, but maybe does something\n    electricChars: '{}):',\n    closeBrackets: '()[]{}\\'\\'\"\"``',\n    fold: 'brace',\n    blockCommentStart: '/*',\n    blockCommentEnd: '*/',\n    lineComment: '//',\n    tokenTable: {\n        functionName: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.Tag.define(),\n        parameterValue: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.Tag.define(),\n        addressFunction: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.Tag.define(),\n        errorHandling: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.Tag.define(),\n        contractRelated: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.Tag.define(),\n        version: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.Tag.define(),\n        etherUnit: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.Tag.define(),\n        doc: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.Tag.define(),\n        mathematicalAndCryptographic: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.Tag.define(),\n        abi: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.Tag.define(),\n    }\n};\nconst solidity = /*@__PURE__*/new _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LanguageSupport(/*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.StreamLanguage.define(parser));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@replit+codemirror-lang-solidity@6.0.2_@codemirror+language@6.11.0/node_modules/@replit/codemirror-lang-solidity/dist/index.js\n");

/***/ })

};
;