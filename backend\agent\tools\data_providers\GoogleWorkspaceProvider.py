from typing import Dict

from agent.tools.data_providers.RapidDataProviderBase import RapidDataProviderBase, EndpointSchema


class GoogleWorkspaceProvider(RapidDataProviderBase):
    def __init__(self):
        endpoints: Dict[str, EndpointSchema] = {
            # Google Docs endpoints
            "create_document": {
                "route": "/v1/documents",
                "method": "POST",
                "name": "Create Document",
                "description": "Create a new Google Doc.",
                "payload": {
                    "title": "Title of the document"
                }
            },
            "get_document": {
                "route": "/v1/documents/{documentId}",
                "method": "GET",
                "name": "Get Document",
                "description": "Get the content of a Google Doc.",
                "payload": {
                    "documentId": "ID of the document to retrieve"
                }
            },
            "update_document": {
                "route": "/v1/documents/{documentId}:batchUpdate",
                "method": "POST",
                "name": "Update Document",
                "description": "Update a Google Doc with a batch of requests.",
                "payload": {
                    "documentId": "ID of the document to update",
                    "requests": "Array of update requests to apply to the document"
                }
            },
            
            # Google Sheets endpoints
            "create_spreadsheet": {
                "route": "/v4/spreadsheets",
                "method": "POST",
                "name": "Create Spreadsheet",
                "description": "Create a new Google Sheet.",
                "payload": {
                    "properties": "Properties of the spreadsheet (e.g., title)"
                }
            },
            "get_spreadsheet": {
                "route": "/v4/spreadsheets/{spreadsheetId}",
                "method": "GET",
                "name": "Get Spreadsheet",
                "description": "Get the content of a Google Sheet.",
                "payload": {
                    "spreadsheetId": "ID of the spreadsheet to retrieve",
                    "ranges": "Optional ranges to retrieve",
                    "includeGridData": "Optional boolean to include grid data"
                }
            },
            "get_spreadsheet_values": {
                "route": "/v4/spreadsheets/{spreadsheetId}/values/{range}",
                "method": "GET",
                "name": "Get Spreadsheet Values",
                "description": "Get values from a range of a Google Sheet.",
                "payload": {
                    "spreadsheetId": "ID of the spreadsheet",
                    "range": "A1 notation range (e.g., 'Sheet1!A1:D10')",
                    "valueRenderOption": "Optional rendering option (FORMATTED_VALUE, UNFORMATTED_VALUE, FORMULA)",
                    "dateTimeRenderOption": "Optional datetime rendering option (SERIAL_NUMBER, FORMATTED_STRING)"
                }
            },
            "update_spreadsheet_values": {
                "route": "/v4/spreadsheets/{spreadsheetId}/values/{range}",
                "method": "PUT",
                "name": "Update Spreadsheet Values",
                "description": "Update values in a range of a Google Sheet.",
                "payload": {
                    "spreadsheetId": "ID of the spreadsheet",
                    "range": "A1 notation range (e.g., 'Sheet1!A1:D10')",
                    "values": "2D array of values to update",
                    "valueInputOption": "How input data should be interpreted (RAW, USER_ENTERED)"
                }
            },
            
            # Gmail endpoints
            "list_messages": {
                "route": "/v1/users/me/messages",
                "method": "GET",
                "name": "List Messages",
                "description": "List messages in the user's mailbox.",
                "payload": {
                    "maxResults": "Optional maximum number of messages to return",
                    "q": "Optional search query (same format as Gmail search box)",
                    "labelIds": "Optional array of label IDs to restrict results to",
                    "pageToken": "Optional page token for pagination"
                }
            },
            "get_message": {
                "route": "/v1/users/me/messages/{id}",
                "method": "GET",
                "name": "Get Message",
                "description": "Get a specific message from the user's mailbox.",
                "payload": {
                    "id": "ID of the message to retrieve",
                    "format": "Optional format of the message (MINIMAL, FULL, RAW, METADATA)"
                }
            },
            "send_message": {
                "route": "/v1/users/me/messages/send",
                "method": "POST",
                "name": "Send Message",
                "description": "Send an email message.",
                "payload": {
                    "raw": "Base64 encoded email message (RFC 2822 formatted)",
                    "to": "Recipient email address",
                    "subject": "Email subject",
                    "body": "Email body content",
                    "cc": "Optional CC recipients",
                    "bcc": "Optional BCC recipients"
                }
            },
            
            # Google Drive endpoints
            "list_files": {
                "route": "/v3/files",
                "method": "GET",
                "name": "List Files",
                "description": "List files in Google Drive.",
                "payload": {
                    "q": "Optional search query (e.g., \"name contains 'Report'\")",
                    "pageSize": "Optional maximum number of files to return",
                    "fields": "Optional fields to include in the response",
                    "orderBy": "Optional sort order",
                    "pageToken": "Optional page token for pagination"
                }
            },
            "get_file": {
                "route": "/v3/files/{fileId}",
                "method": "GET",
                "name": "Get File",
                "description": "Get metadata for a specific file.",
                "payload": {
                    "fileId": "ID of the file to retrieve",
                    "fields": "Optional fields to include in the response"
                }
            },
            "create_folder": {
                "route": "/v3/files",
                "method": "POST",
                "name": "Create Folder",
                "description": "Create a new folder in Google Drive.",
                "payload": {
                    "name": "Name of the folder",
                    "mimeType": "application/vnd.google-apps.folder",
                    "parents": "Optional array of parent folder IDs"
                }
            },
            "upload_file": {
                "route": "/upload/v3/files",
                "method": "POST",
                "name": "Upload File",
                "description": "Upload a file to Google Drive.",
                "payload": {
                    "name": "Name of the file",
                    "content": "Base64 encoded file content",
                    "mimeType": "MIME type of the file",
                    "parents": "Optional array of parent folder IDs"
                }
            }
        }
        
        # Base URLs for different Google APIs
        self.docs_base_url = "https://docs.googleapis.com"
        self.sheets_base_url = "https://sheets.googleapis.com"
        self.gmail_base_url = "https://gmail.googleapis.com"
        self.drive_base_url = "https://www.googleapis.com/drive"
        
        # Start with Docs API as default
        base_url = self.docs_base_url
        super().__init__(base_url, endpoints)
        
        # Override call_endpoint to handle different base URLs
        self.original_call_endpoint = self.call_endpoint
        self.call_endpoint = self.google_call_endpoint
    
    def google_call_endpoint(self, route: str, payload: Dict = None):
        """
        Custom call_endpoint method to handle different Google API base URLs.
        """
        import os
        import requests
        import base64
        from email.mime.text import MIMEText
        from email.mime.multipart import MIMEMultipart
        
        if route.startswith("/"):
            route = route[1:]

        endpoint = self.endpoints.get(route)
        if not endpoint:
            raise ValueError(f"Endpoint {route} not found")
        
        # Set the appropriate base URL based on the endpoint
        if route in ["create_document", "get_document", "update_document"]:
            self.base_url = self.docs_base_url
        elif route in ["create_spreadsheet", "get_spreadsheet", "get_spreadsheet_values", "update_spreadsheet_values"]:
            self.base_url = self.sheets_base_url
        elif route in ["list_messages", "get_message", "send_message"]:
            self.base_url = self.gmail_base_url
        elif route in ["list_files", "get_file", "create_folder", "upload_file"]:
            self.base_url = self.drive_base_url
        
        # Format route with payload parameters
        api_route = endpoint["route"]
        if "{" in api_route:
            # Extract path parameters from the route
            path_params = [param.split("}")[0] for param in api_route.split("{")[1:]]
            
            # Replace path parameters with values from payload
            for param in path_params:
                if param in payload:
                    api_route = api_route.replace(f"{{{param}}}", payload[param])
                    # Remove used parameters from payload
                    del payload[param]
        
        url = f"{self.base_url}{api_route}"
        
        # Get Google API token from environment
        google_token = os.getenv("GOOGLE_API_TOKEN")
        if not google_token:
            return {"error": "GOOGLE_API_TOKEN environment variable not set"}
        
        headers = {
            "Authorization": f"Bearer {google_token}",
            "Content-Type": "application/json"
        }

        # Special handling for Gmail send_message
        if route == "send_message":
            # Create email message
            message = MIMEMultipart()
            message["to"] = payload.get("to", "")
            message["subject"] = payload.get("subject", "")
            
            if "cc" in payload and payload["cc"]:
                message["cc"] = payload["cc"]
            if "bcc" in payload and payload["bcc"]:
                message["bcc"] = payload["bcc"]
                
            # Add body
            message.attach(MIMEText(payload.get("body", ""), "plain"))
            
            # Convert to base64 encoded string
            raw = base64.urlsafe_b64encode(message.as_bytes()).decode()
            
            # Update payload
            payload = {"raw": raw}

        method = endpoint.get('method', 'GET').upper()
        
        if method == 'GET':
            response = requests.get(url, params=payload, headers=headers)
        elif method == 'POST':
            response = requests.post(url, json=payload, headers=headers)
        elif method == 'PUT':
            response = requests.put(url, json=payload, headers=headers)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
            
        return response.json()


if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    tool = GoogleWorkspaceProvider()

    # Example for creating a document
    create_doc = tool.call_endpoint(
        route="create_document",
        payload={
            "title": "Test Document"
        }
    )
    print("Create Document:", create_doc)
    
    # Example for getting a document
    get_doc = tool.call_endpoint(
        route="get_document",
        payload={
            "documentId": "1234567890abcdef"
        }
    )
    print("Get Document:", get_doc)
    
    # Example for listing Gmail messages
    list_messages = tool.call_endpoint(
        route="list_messages",
        payload={
            "maxResults": 10,
            "q": "is:unread"
        }
    )
    print("List Messages:", list_messages)
    
    # Example for sending an email
    send_message = tool.call_endpoint(
        route="send_message",
        payload={
            "to": "<EMAIL>",
            "subject": "Test Email",
            "body": "This is a test email from the Google Workspace Provider tool."
        }
    )
    print("Send Message:", send_message)
