"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uiw+codemirror-extensions-basic-setup@4.23.11_@codemirror+autocomplete@6.18.6_@codemirror+co_qoz4gibf5xpps5nxf2mapku4yy";
exports.ids = ["vendor-chunks/@uiw+codemirror-extensions-basic-setup@4.23.11_@codemirror+autocomplete@6.18.6_@codemirror+co_qoz4gibf5xpps5nxf2mapku4yy"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@uiw+codemirror-extensions-basic-setup@4.23.11_@codemirror+autocomplete@6.18.6_@codemirror+co_qoz4gibf5xpps5nxf2mapku4yy/node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js":
/*!**********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@uiw+codemirror-extensions-basic-setup@4.23.11_@codemirror+autocomplete@6.18.6_@codemirror+co_qoz4gibf5xpps5nxf2mapku4yy/node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js ***!
  \**********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   basicSetup: () => (/* binding */ basicSetup),\n/* harmony export */   minimalSetup: () => (/* binding */ minimalSetup)\n/* harmony export */ });\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_commands__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/commands */ \"(ssr)/./node_modules/.pnpm/@codemirror+commands@6.8.1/node_modules/@codemirror/commands/dist/index.js\");\n/* harmony import */ var _codemirror_search__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/search */ \"(ssr)/./node_modules/.pnpm/@codemirror+search@6.5.10/node_modules/@codemirror/search/dist/index.js\");\n/* harmony import */ var _codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/autocomplete */ \"(ssr)/./node_modules/.pnpm/@codemirror+autocomplete@6.18.6/node_modules/@codemirror/autocomplete/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _codemirror_lint__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/lint */ \"(ssr)/./node_modules/.pnpm/@codemirror+lint@6.8.5/node_modules/@codemirror/lint/dist/index.js\");\n\n\n\n\n\n\n\n/**\nThis is an extension value that just pulls together a number of\nextensions that you might want in a basic editor. It is meant as a\nconvenient helper to quickly set up CodeMirror without installing\nand importing a lot of separate packages.\n\nSpecifically, it includes...\n\n - [the default command bindings](https://codemirror.net/6/docs/ref/#commands.defaultKeymap)\n - [line numbers](https://codemirror.net/6/docs/ref/#view.lineNumbers)\n - [special character highlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars)\n - [the undo history](https://codemirror.net/6/docs/ref/#commands.history)\n - [a fold gutter](https://codemirror.net/6/docs/ref/#language.foldGutter)\n - [custom selection drawing](https://codemirror.net/6/docs/ref/#view.drawSelection)\n - [drop cursor](https://codemirror.net/6/docs/ref/#view.dropCursor)\n - [multiple selections](https://codemirror.net/6/docs/ref/#state.EditorState^allowMultipleSelections)\n - [reindentation on input](https://codemirror.net/6/docs/ref/#language.indentOnInput)\n - [the default highlight style](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle) (as fallback)\n - [bracket matching](https://codemirror.net/6/docs/ref/#language.bracketMatching)\n - [bracket closing](https://codemirror.net/6/docs/ref/#autocomplete.closeBrackets)\n - [autocompletion](https://codemirror.net/6/docs/ref/#autocomplete.autocompletion)\n - [rectangular selection](https://codemirror.net/6/docs/ref/#view.rectangularSelection) and [crosshair cursor](https://codemirror.net/6/docs/ref/#view.crosshairCursor)\n - [active line highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLine)\n - [active line gutter highlighting](https://codemirror.net/6/docs/ref/#view.highlightActiveLineGutter)\n - [selection match highlighting](https://codemirror.net/6/docs/ref/#search.highlightSelectionMatches)\n - [search](https://codemirror.net/6/docs/ref/#search.searchKeymap)\n - [linting](https://codemirror.net/6/docs/ref/#lint.lintKeymap)\n\n(You'll probably want to add some language package to your setup\ntoo.)\n\nThis extension does not allow customization. The idea is that,\nonce you decide you want to configure your editor more precisely,\nyou take this package's source (which is just a bunch of imports\nand an array literal), copy it into your own code, and adjust it\nas desired.\n*/\nvar basicSetup = function basicSetup(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var {\n    crosshairCursor: initCrosshairCursor = false\n  } = options;\n  var keymaps = [];\n  if (options.closeBracketsKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.closeBracketsKeymap);\n  }\n  if (options.defaultKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.defaultKeymap);\n  }\n  if (options.searchKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_search__WEBPACK_IMPORTED_MODULE_2__.searchKeymap);\n  }\n  if (options.historyKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.historyKeymap);\n  }\n  if (options.foldKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.foldKeymap);\n  }\n  if (options.completionKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.completionKeymap);\n  }\n  if (options.lintKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_lint__WEBPACK_IMPORTED_MODULE_4__.lintKeymap);\n  }\n  var extensions = [];\n  if (options.lineNumbers !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumbers)());\n  if (options.highlightActiveLineGutter !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLineGutter)());\n  if (options.highlightSpecialChars !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightSpecialChars)());\n  if (options.history !== false) extensions.push((0,_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.history)());\n  if (options.foldGutter !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.foldGutter)());\n  if (options.drawSelection !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.drawSelection)());\n  if (options.dropCursor !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.dropCursor)());\n  if (options.allowMultipleSelections !== false) extensions.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_6__.EditorState.allowMultipleSelections.of(true));\n  if (options.indentOnInput !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentOnInput)());\n  if (options.syntaxHighlighting !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxHighlighting)(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.defaultHighlightStyle, {\n    fallback: true\n  }));\n  if (options.bracketMatching !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.bracketMatching)());\n  if (options.closeBrackets !== false) extensions.push((0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.closeBrackets)());\n  if (options.autocompletion !== false) extensions.push((0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_0__.autocompletion)());\n  if (options.rectangularSelection !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.rectangularSelection)());\n  if (initCrosshairCursor !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.crosshairCursor)());\n  if (options.highlightActiveLine !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLine)());\n  if (options.highlightSelectionMatches !== false) extensions.push((0,_codemirror_search__WEBPACK_IMPORTED_MODULE_2__.highlightSelectionMatches)());\n  if (options.tabSize && typeof options.tabSize === 'number') extensions.push(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentUnit.of(' '.repeat(options.tabSize)));\n  return extensions.concat([_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.keymap.of(keymaps.flat())]).filter(Boolean);\n};\n/**\nA minimal set of extensions to create a functional editor. Only\nincludes [the default keymap](https://codemirror.net/6/docs/ref/#commands.defaultKeymap), [undo\nhistory](https://codemirror.net/6/docs/ref/#commands.history), [special character\nhighlighting](https://codemirror.net/6/docs/ref/#view.highlightSpecialChars), [custom selection\ndrawing](https://codemirror.net/6/docs/ref/#view.drawSelection), and [default highlight\nstyle](https://codemirror.net/6/docs/ref/#language.defaultHighlightStyle).\n*/\nvar minimalSetup = function minimalSetup(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var keymaps = [];\n  if (options.defaultKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.defaultKeymap);\n  }\n  if (options.historyKeymap !== false) {\n    keymaps = keymaps.concat(_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.historyKeymap);\n  }\n  var extensions = [];\n  if (options.highlightSpecialChars !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightSpecialChars)());\n  if (options.history !== false) extensions.push((0,_codemirror_commands__WEBPACK_IMPORTED_MODULE_1__.history)());\n  if (options.drawSelection !== false) extensions.push((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.drawSelection)());\n  if (options.syntaxHighlighting !== false) extensions.push((0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxHighlighting)(_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.defaultHighlightStyle, {\n    fallback: true\n  }));\n  return extensions.concat([_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.keymap.of(keymaps.flat())]).filter(Boolean);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@uiw+codemirror-extensions-basic-setup@4.23.11_@codemirror+autocomplete@6.18.6_@codemirror+co_qoz4gibf5xpps5nxf2mapku4yy/node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js\n");

/***/ })

};
;