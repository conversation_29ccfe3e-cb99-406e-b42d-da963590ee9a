-- Custom Accounts System for Siden AI
-- This replaces the basejump accounts system with a simpler custom implementation

-- Create accounts table
CREATE TABLE IF NOT EXISTS public.accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'personal' CHECK (type IN ('personal', 'team')),
    settings JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Ensure each user has only one personal account
    UNIQUE(user_id, type) DEFERRABLE INITIALLY DEFERRED
);

-- Create account_members table for team accounts
CREATE TABLE IF NOT EXISTS public.account_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member')),
    invited_by UUID REFERENCES auth.users(id),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    
    -- Ensure each user can only be in an account once
    UNIQUE(account_id, user_id)
);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_accounts_updated_at
    BEFORE UPDATE ON public.accounts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON public.accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_accounts_type ON public.accounts(type);
CREATE INDEX IF NOT EXISTS idx_account_members_account_id ON public.account_members(account_id);
CREATE INDEX IF NOT EXISTS idx_account_members_user_id ON public.account_members(user_id);
CREATE INDEX IF NOT EXISTS idx_account_members_role ON public.account_members(role);

-- Enable Row Level Security
ALTER TABLE public.accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.account_members ENABLE ROW LEVEL SECURITY;

-- RLS Policies for accounts
CREATE POLICY "Users can view their own accounts" ON public.accounts
    FOR SELECT
    USING (user_id = auth.uid());

CREATE POLICY "Users can view accounts they are members of" ON public.accounts
    FOR SELECT
    USING (
        id IN (
            SELECT account_id 
            FROM public.account_members 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can create their own accounts" ON public.accounts
    FOR INSERT
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "Account owners can update their accounts" ON public.accounts
    FOR UPDATE
    USING (
        user_id = auth.uid() OR
        id IN (
            SELECT account_id 
            FROM public.account_members 
            WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
        )
    );

-- RLS Policies for account_members
CREATE POLICY "Users can view memberships of their accounts" ON public.account_members
    FOR SELECT
    USING (
        account_id IN (
            SELECT id FROM public.accounts WHERE user_id = auth.uid()
        ) OR
        user_id = auth.uid()
    );

CREATE POLICY "Account owners can manage members" ON public.account_members
    FOR ALL
    USING (
        account_id IN (
            SELECT id FROM public.accounts WHERE user_id = auth.uid()
        ) OR
        account_id IN (
            SELECT account_id 
            FROM public.account_members 
            WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
        )
    );

-- Grant permissions
GRANT ALL PRIVILEGES ON TABLE public.accounts TO authenticated, service_role;
GRANT ALL PRIVILEGES ON TABLE public.account_members TO authenticated, service_role;
GRANT SELECT ON TABLE public.accounts TO anon;
GRANT SELECT ON TABLE public.account_members TO anon;

-- Function to create a personal account for new users
CREATE OR REPLACE FUNCTION public.create_personal_account()
RETURNS TRIGGER AS $$
BEGIN
    -- Create a personal account for the new user
    INSERT INTO public.accounts (user_id, name, type, settings)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email, 'Personal Account'),
        'personal',
        '{}'::jsonb
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create personal account for new users
CREATE TRIGGER create_personal_account_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.create_personal_account();

-- Function to get user's primary account (personal account)
CREATE OR REPLACE FUNCTION public.get_user_account_id(user_uuid UUID)
RETURNS UUID AS $$
DECLARE
    account_uuid UUID;
BEGIN
    -- First try to get personal account
    SELECT id INTO account_uuid
    FROM public.accounts
    WHERE user_id = user_uuid AND type = 'personal'
    LIMIT 1;
    
    -- If no personal account, get any account where user is owner
    IF account_uuid IS NULL THEN
        SELECT a.id INTO account_uuid
        FROM public.accounts a
        LEFT JOIN public.account_members am ON a.id = am.account_id
        WHERE (a.user_id = user_uuid OR (am.user_id = user_uuid AND am.role = 'owner'))
        ORDER BY a.created_at ASC
        LIMIT 1;
    END IF;
    
    RETURN account_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
