'use client';

import { useState, useCallback } from 'react';
import { toast } from 'sonner';

interface CodeExecutionResult {
  output?: string;
  error?: string;
  images?: string[];
  isLoading: boolean;
  isSuccess?: boolean;
  executionTime?: number;
}

interface CodeExecutionRequest {
  code: string;
  language: string;
  timeout?: number;
}

export function useCodeExecution() {
  const [results, setResults] = useState<Record<string, CodeExecutionResult>>({});

  const executeCode = useCallback(async (
    code: string, 
    language: string = 'python',
    timeout: number = 30000,
    resultKey?: string
  ): Promise<CodeExecutionResult> => {
    const key = resultKey || `${Date.now()}-${Math.random()}`;
    
    // Set loading state
    setResults(prev => ({
      ...prev,
      [key]: {
        isLoading: true,
        isSuccess: false
      }
    }));

    try {
      const response = await fetch('/api/sandbox/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code,
          language,
          timeout
        } as CodeExecutionRequest),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      const result: CodeExecutionResult = {
        output: data.output || '',
        error: data.error || null,
        images: data.images || [],
        isLoading: false,
        isSuccess: !data.error && data.exitCode === 0,
        executionTime: data.execution_time
      };

      // Update results
      setResults(prev => ({
        ...prev,
        [key]: result
      }));

      // Show toast notification
      if (result.isSuccess) {
        toast.success("Code executed successfully", {
          description: result.images?.length
            ? `Generated ${result.images.length} visualization(s)`
            : "Code ran without errors",
        });
      } else {
        toast.error("Code execution failed", {
          description: result.error || "Unknown error occurred",
        });
      }

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      const result: CodeExecutionResult = {
        output: '',
        error: errorMessage,
        images: [],
        isLoading: false,
        isSuccess: false
      };

      setResults(prev => ({
        ...prev,
        [key]: result
      }));

      toast.error("Execution failed", {
        description: errorMessage,
      });

      return result;
    }
  }, [toast]);

  const getResult = useCallback((key: string): CodeExecutionResult | undefined => {
    return results[key];
  }, [results]);

  const clearResult = useCallback((key: string) => {
    setResults(prev => {
      const newResults = { ...prev };
      delete newResults[key];
      return newResults;
    });
  }, []);

  const clearAllResults = useCallback(() => {
    setResults({});
  }, []);

  return {
    executeCode,
    getResult,
    clearResult,
    clearAllResults,
    results
  };
}

// Utility function to extract code blocks from markdown
export function extractCodeBlocks(content: string): Array<{ code: string; language: string; startIndex: number; endIndex: number }> {
  const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
  const blocks: Array<{ code: string; language: string; startIndex: number; endIndex: number }> = [];
  let match;

  while ((match = codeBlockRegex.exec(content)) !== null) {
    blocks.push({
      code: match[2].trim(),
      language: match[1] || 'text',
      startIndex: match.index,
      endIndex: match.index + match[0].length
    });
  }

  return blocks;
}

// Utility function to check if content contains executable code
export function hasExecutableCode(content: string): boolean {
  const codeBlocks = extractCodeBlocks(content);
  return codeBlocks.some(block => 
    ['python', 'py', 'javascript', 'js', 'typescript', 'ts'].includes(block.language.toLowerCase())
  );
}

// Utility function to generate a unique key for code execution
export function generateExecutionKey(code: string, language: string): string {
  // Create a simple hash of the code for consistent keys
  let hash = 0;
  const str = code + language;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return `exec_${Math.abs(hash)}_${Date.now()}`;
}
