from typing import Dict

from agent.tools.data_providers.RapidDataProviderBase import RapidDataProviderBase, EndpointSchema


class MarketAnalysisProvider(RapidDataProviderBase):
    def __init__(self):
        endpoints: Dict[str, EndpointSchema] = {
            "company_profile": {
                "route": "/company/profile",
                "method": "GET",
                "name": "Company Profile",
                "description": "Get detailed profile information about a company.",
                "payload": {
                    "symbol": "Stock symbol of the company (e.g., 'AAPL')",
                    "exchange": "Optional stock exchange (e.g., 'NASDAQ')"
                }
            },
            "company_financials": {
                "route": "/company/financials",
                "method": "GET",
                "name": "Company Financials",
                "description": "Get financial data for a company.",
                "payload": {
                    "symbol": "Stock symbol of the company (e.g., 'AAPL')",
                    "period": "Optional reporting period ('annual', 'quarterly')",
                    "limit": "Optional maximum number of periods to return"
                }
            },
            "market_news": {
                "route": "/market/news",
                "method": "GET",
                "name": "Market News",
                "description": "Get latest market news.",
                "payload": {
                    "category": "Optional news category ('general', 'forex', 'crypto', 'merger')",
                    "limit": "Optional maximum number of news items to return"
                }
            },
            "company_news": {
                "route": "/company/news",
                "method": "GET",
                "name": "Company News",
                "description": "Get news about a specific company.",
                "payload": {
                    "symbol": "Stock symbol of the company (e.g., 'AAPL')",
                    "from": "Optional start date (YYYY-MM-DD)",
                    "to": "Optional end date (YYYY-MM-DD)",
                    "limit": "Optional maximum number of news items to return"
                }
            },
            "market_sentiment": {
                "route": "/market/sentiment",
                "method": "GET",
                "name": "Market Sentiment",
                "description": "Get overall market sentiment analysis.",
                "payload": {
                    "index": "Optional market index to analyze ('S&P500', 'NASDAQ', 'DOW')"
                }
            },
            "company_sentiment": {
                "route": "/company/sentiment",
                "method": "GET",
                "name": "Company Sentiment",
                "description": "Get sentiment analysis for a specific company.",
                "payload": {
                    "symbol": "Stock symbol of the company (e.g., 'AAPL')",
                    "from": "Optional start date (YYYY-MM-DD)",
                    "to": "Optional end date (YYYY-MM-DD)"
                }
            },
            "sector_performance": {
                "route": "/sector/performance",
                "method": "GET",
                "name": "Sector Performance",
                "description": "Get performance metrics for market sectors.",
                "payload": {
                    "timeframe": "Optional timeframe ('1d', '5d', '1m', '3m', '1y', '5y')"
                }
            },
            "industry_analysis": {
                "route": "/industry/analysis",
                "method": "GET",
                "name": "Industry Analysis",
                "description": "Get detailed analysis of a specific industry.",
                "payload": {
                    "industry": "Industry name or code",
                    "metrics": "Optional comma-separated list of metrics to include"
                }
            },
            "competitor_analysis": {
                "route": "/company/competitors",
                "method": "GET",
                "name": "Competitor Analysis",
                "description": "Get analysis of a company's competitors.",
                "payload": {
                    "symbol": "Stock symbol of the company (e.g., 'AAPL')",
                    "metrics": "Optional comma-separated list of metrics to include"
                }
            },
            "market_movers": {
                "route": "/market/movers",
                "method": "GET",
                "name": "Market Movers",
                "description": "Get top gainers, losers, and most active stocks.",
                "payload": {
                    "type": "Type of movers ('gainers', 'losers', 'active')",
                    "exchange": "Optional stock exchange (e.g., 'NASDAQ')",
                    "limit": "Optional maximum number of stocks to return"
                }
            },
            "economic_calendar": {
                "route": "/economic/calendar",
                "method": "GET",
                "name": "Economic Calendar",
                "description": "Get upcoming economic events and indicators.",
                "payload": {
                    "from": "Optional start date (YYYY-MM-DD)",
                    "to": "Optional end date (YYYY-MM-DD)",
                    "country": "Optional country code (e.g., 'US', 'EU')",
                    "importance": "Optional importance level ('low', 'medium', 'high')"
                }
            },
            "ipo_calendar": {
                "route": "/ipo/calendar",
                "method": "GET",
                "name": "IPO Calendar",
                "description": "Get upcoming initial public offerings.",
                "payload": {
                    "from": "Optional start date (YYYY-MM-DD)",
                    "to": "Optional end date (YYYY-MM-DD)"
                }
            }
        }
        
        # Use Financial Modeling Prep API as the base URL
        base_url = "https://financialmodelingprep.com/api/v3"
        super().__init__(base_url, endpoints)


if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    tool = MarketAnalysisProvider()

    # Example for getting company profile
    company_profile = tool.call_endpoint(
        route="company_profile",
        payload={
            "symbol": "AAPL"
        }
    )
    print("Company Profile:", company_profile)
    
    # Example for getting market news
    market_news = tool.call_endpoint(
        route="market_news",
        payload={
            "category": "general",
            "limit": 5
        }
    )
    print("Market News:", market_news)
    
    # Example for getting sector performance
    sector_performance = tool.call_endpoint(
        route="sector_performance",
        payload={
            "timeframe": "1m"
        }
    )
    print("Sector Performance:", sector_performance)
