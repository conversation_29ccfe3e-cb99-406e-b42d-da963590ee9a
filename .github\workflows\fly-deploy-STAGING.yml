name: Fly Deploy Staging
on:
  push:
    branches:
      - main
  workflow_dispatch:
jobs:
  deploy:
    name: Deploy staging app
    runs-on: ubuntu-latest
    concurrency: deploy-group    # optional: ensure only one action runs at a time
    steps:
      - uses: actions/checkout@v4
      - uses: superfly/flyctl-actions/setup-flyctl@master
      - run: cd backend && flyctl deploy --remote-only --config fly.staging.toml
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
