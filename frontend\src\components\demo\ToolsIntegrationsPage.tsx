'use client';

import React, { useState } from 'react';


import { cn } from '@/lib/utils';
import { IntegrationModal } from './IntegrationModal';



// All integrations data
const allIntegrations = [
  {
    id: 'google-workspace',
    name: 'Google Workspace',
    description: 'Allow agents to access your Docs, Sheets, and Gmail',
    iconSrc: '/logoicons/google.svg',
    category: 'Productivity',
  },
  {
    id: 'google-meet',
    name: 'Google Meet',
    description: 'Allow agents to schedule and manage video meetings',
    iconSrc: '/logoicons/google-meet.svg',
    category: 'Messaging',
  },
  {
    id: 'outlook',
    name: 'Outlook',
    description: 'Allow agents to manage emails and calendar events',
    iconSrc: '/logoicons/outlook.svg',
    category: 'Productivity',
  },
  {
    id: 'zoom',
    name: 'Zoom',
    description: 'Allow agents to schedule and manage video meetings',
    iconSrc: '/logoicons/zoom.svg',
    category: 'Messaging',
  },
  {
    id: 'teams',
    name: 'Microsoft Teams',
    description: 'Allow agents to collaborate with your team',
    iconSrc: '/logoicons/microsoft-teams.svg',
    category: 'Messaging',
  },
  {
    id: 'sharepoint',
    name: 'Microsoft SharePoint',
    description: 'Allow agents to manage documents and collaborate',
    iconSrc: '/logoicons/microsoft-sharepoint.svg',
    category: 'Productivity',
  },
  {
    id: 'salesforce',
    name: 'Salesforce',
    description: 'Allow agents to manage your CRM and sales pipeline',
    iconSrc: '/logoicons/salesforce.svg',
    category: 'Productivity',
  },
  {
    id: 'gitlab',
    name: 'GitLab',
    description: 'Allow agents to access repositories and create merge requests',
    iconSrc: '/logoicons/gitlab.svg',
    category: 'DevTools',
  },
  {
    id: 'github',
    name: 'GitHub',
    description: 'Allow agents to access repositories and create pull requests',
    iconSrc: '/logoicons/github.svg',
    category: 'DevTools',
  },
  {
    id: 'jira',
    name: 'Jira',
    description: 'Allow agents to track issues and manage projects',
    iconSrc: '/logoicons/jira.svg',
    category: 'DevTools',
  },
  {
    id: 'notion',
    name: 'Notion',
    description: 'Allow agents to manage knowledge and documentation',
    iconSrc: '/logoicons/notion.svg',
    category: 'Productivity',
  },
  {
    id: 'slack',
    name: 'Slack',
    description: 'Allow agents to send messages to your Slack channels',
    iconSrc: '/logoicons/slack.svg',
    category: 'Messaging',
  },
  {
    id: 'discord',
    name: 'Discord',
    description: 'Allow agents to manage your community and channels',
    iconSrc: '/logoicons/discord.svg',
    category: 'Messaging',
  },
  {
    id: 'dropbox',
    name: 'Dropbox',
    description: 'Allow agents to access and manage your files',
    iconSrc: '/logoicons/dropbox.svg',
    category: 'Productivity',
  },
  {
    id: 'calendly',
    name: 'Calendly',
    description: 'Allow agents to schedule appointments and meetings',
    iconSrc: '/logoicons/calendly.svg',
    category: 'Productivity',
  },
  {
    id: 'airtable',
    name: 'Airtable',
    description: 'Allow agents to manage flexible databases',
    iconSrc: '/logoicons/airtable.svg',
    category: 'Productivity',
  },
  {
    id: 'stripe',
    name: 'Stripe',
    description: 'Allow agents to process payments and manage subscriptions',
    iconSrc: '/logoicons/stripe.svg',
    category: 'Productivity',
  },
  {
    id: 'meta',
    name: 'Meta Business Suite',
    description: 'Allow agents to manage Facebook and Instagram presence',
    iconSrc: '/logoicons/meta.svg',
    category: 'Productivity',
  },
  {
    id: 'hubspot',
    name: 'HubSpot',
    description: 'Allow agents to manage contacts and deals',
    iconSrc: '/logoicons/hubspot.svg',
    category: 'Productivity',
  },
];

interface ToolsIntegrationsPageProps {
  project?: any;
}

export function ToolsIntegrationsPage({ project }: ToolsIntegrationsPageProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('All integrations');
  const [selectedIntegration, setSelectedIntegration] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const categories = ['All integrations', 'DevTools', 'Messaging', 'Productivity', 'Security'];

  const handleIntegrationClick = (integration: any) => {
    setSelectedIntegration(integration);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedIntegration(null);
  };

  const filteredIntegrations = allIntegrations.filter(integration => {
    const matchesSearch = integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         integration.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === 'All integrations' || integration.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="h-[60px] px-6 flex items-center border-b border-border">
        <div className="flex items-center gap-4">
          <h1 className="text-white font-semibold text-lg">Integrations</h1>
          <div className="text-sm text-[#999999]">
            Connect your tools and services to streamline workflows
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="px-6 pt-6 pb-3">
        <div className="relative bg-card border border-border rounded-md py-12 px-6 overflow-hidden">
          {/* Subtle gradient background */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/3"></div>

          <div className="relative max-w-3xl mx-auto text-center">


            <h1 className="text-3xl md:text-4xl font-semibold text-foreground mb-3 tracking-tight">
              Connect Everything
            </h1>
            <p className="text-muted-foreground text-base leading-relaxed">
              Streamline your workflow with powerful integrations
            </p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto px-6 pt-3 pb-6">

        {/* Category Tabs */}
        <div className="bg-card border border-border rounded-md p-1 mb-6 shadow-sm">
          <div className="flex items-center gap-1">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={cn(
                  "px-4 py-2.5 text-sm font-medium rounded-md transition-all duration-200",
                  activeCategory === category
                    ? "bg-accent text-foreground shadow-sm"
                    : "text-muted-foreground hover:text-foreground hover:bg-accent/50"
                )}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex items-center gap-4 mb-6">
          <div className="relative flex-1 max-w-sm">
            <input
              type="text"
              placeholder="Search integrations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2.5 border border-[#333333] rounded-lg bg-[#1a1a1a] text-white placeholder:text-[#666666] focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
            />
            <div className="absolute left-3 top-1/2 -translate-y-1/2">
              <svg className="h-4 w-4 text-[#666666]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
          <button className="flex items-center gap-2 px-4 py-2.5 border border-[#333333] rounded-lg bg-[#1a1a1a] text-[#666666] hover:text-white hover:bg-[#333333] hover:border-[#555555] transition-colors ml-auto">
            Add Integration
          </button>
        </div>

        {/* Integrations Grid */}
        <div className="bg-card border border-border rounded-md p-6 shadow-sm">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {filteredIntegrations.map((integration) => (
              <div
                key={integration.id}
                onClick={() => handleIntegrationClick(integration)}
                className="group flex items-center gap-3 p-3 rounded-lg hover:bg-accent transition-colors cursor-pointer"
              >
                <div className="w-8 h-8 rounded-lg bg-muted flex items-center justify-center flex-shrink-0">
                  <img src={integration.iconSrc} alt={integration.name} className="w-5 h-5" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-medium text-foreground text-sm truncate">
                      {integration.name}
                    </h3>
                    {['bolt', 'raycast', 'loom', 'nextjs', 'cleanshot', 'supabase', 'vercel', 'grok', 'v0'].includes(integration.id) && (
                      <span className="text-xs bg-primary/20 text-primary px-1.5 py-0.5 rounded font-medium">NEW</span>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground truncate">
                    {integration.id === 'google-workspace' ? 'workspace.google.com' :
                     integration.id === 'google-meet' ? 'meet.google.com' :
                     integration.id === 'outlook' ? 'outlook.com' :
                     integration.id === 'zoom' ? 'zoom.us' :
                     integration.id === 'teams' ? 'teams.microsoft.com' :
                     integration.id === 'sharepoint' ? 'sharepoint.com' :
                     integration.id === 'salesforce' ? 'salesforce.com' :
                     integration.id === 'gitlab' ? 'gitlab.com' :
                     integration.id === 'github' ? 'github.com' :
                     integration.id === 'jira' ? 'atlassian.com' :
                     integration.id === 'notion' ? 'notion.com' :
                     integration.id === 'slack' ? 'slack.com' :
                     integration.id === 'discord' ? 'discord.com' :
                     integration.id === 'dropbox' ? 'dropbox.com' :
                     integration.id === 'calendly' ? 'calendly.com' :
                     integration.id === 'airtable' ? 'airtable.com' :
                     integration.id === 'stripe' ? 'stripe.com' :
                     integration.id === 'meta' ? 'business.facebook.com' :
                     integration.id === 'hubspot' ? 'hubspot.com' :
                     `${integration.name.toLowerCase().replace(/\s+/g, '')}.com`}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="flex items-center justify-between mt-6 pt-4 border-t border-border">
            <button className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors">
              <div className="w-4 h-4 border border-border rounded flex items-center justify-center">
                <svg className="w-2 h-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              Documentation
            </button>
            <button className="px-4 py-2 text-sm font-medium text-muted-foreground hover:text-foreground border border-border rounded-md hover:bg-accent transition-colors">
              All integrations
            </button>
          </div>
        </div>
      </div>

      {/* Integration Modal */}
      <IntegrationModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        integration={selectedIntegration}
      />
    </div>
  );
}
