import httpx
from typing import List, Optional, Dict, Any
import os
from dotenv import load_dotenv
from agentpress.tool import Too<PERSON>, Tool<PERSON><PERSON>ult, openapi_schema, xml_schema
from utils.config import config
import json

class BraveWebTool(Tool):
    """Tool for performing web searches and web scraping using Brave API."""

    def __init__(self, api_key: str = None, ai_api_key: str = None):
        super().__init__()
        # Load environment variables
        load_dotenv()
        
        # Use the provided API keys or get them from environment variables
        self.brave_api_key = api_key or os.getenv("BRAVE_SEARCH_API_KEY") or config.BRAVE_SEARCH_API_KEY
        self.brave_ai_api_key = ai_api_key or os.getenv("BRAVE_SEARCH_AI_API_KEY") or config.BRAVE_SEARCH_AI_API_KEY
        
        if not self.brave_api_key:
            raise ValueError("BRAVE_SEARCH_API_KEY not found in configuration")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "brave_web_search",
            "description": "Search the web for up-to-date information on a specific topic using the Brave Search API. This tool allows you to gather real-time information from the internet to answer user queries, research topics, validate facts, and find recent developments. Results include titles, URLs, and summaries.",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "The search query to find information about."
                    },
                    "num_results": {
                        "type": "integer",
                        "description": "The number of search results to return (default: 10, max: 20).",
                        "default": 10
                    }
                },
                "required": ["query"]
            }
        }
    })
    @xml_schema(
        tag_name="brave-search",
        mappings=[
            {"param_name": "query", "node_type": "attribute", "path": "."},
            {"param_name": "num_results", "node_type": "attribute", "path": "."}
        ],
        example='''
        <!-- 
        The brave-search tool allows you to search the internet for real-time information.
        Use this tool when you need to find current information, research topics, or verify facts.
        
        The tool returns information including:
        - Titles of relevant web pages
        - URLs for accessing the pages
        - Summaries of the content
        -->
        
        <!-- Simple search example -->
        <brave-search 
            query="current weather in New York City" 
            num_results="10">
        </brave-search>
        
        <!-- Another search example -->
        <brave-search 
            query="latest AI research papers" 
            num_results="15">
        </brave-search>
        '''
    )
    async def brave_web_search(
        self, 
        query: str, 
        num_results: int = 10
    ) -> ToolResult:
        """
        Search the web using the Brave Search API to find relevant and up-to-date information.
        """
        try:
            # Ensure we have a valid query
            if not query or not isinstance(query, str):
                return self.fail_response("A valid search query is required.")
            
            # Normalize num_results
            if num_results is None:
                num_results = 10
            elif isinstance(num_results, int):
                num_results = max(1, min(num_results, 20))
            elif isinstance(num_results, str):
                try:
                    num_results = max(1, min(int(num_results), 20))
                except ValueError:
                    num_results = 10
            else:
                num_results = 10

            # Execute the search with Brave API
            async with httpx.AsyncClient() as client:
                headers = {
                    "Accept": "application/json",
                    "Accept-Encoding": "gzip",
                    "X-Subscription-Token": self.brave_api_key
                }
                
                params = {
                    "q": query,
                    "count": num_results
                }
                
                response = await client.get(
                    "https://api.search.brave.com/res/v1/web/search",
                    params=params,
                    headers=headers,
                    timeout=30
                )
                
                response.raise_for_status()
                search_response = response.json()

            # Format results consistently
            formatted_results = []
            
            if "web" in search_response and "results" in search_response["web"]:
                for result in search_response["web"]["results"]:
                    formatted_result = {
                        "title": result.get("title", ""),
                        "url": result.get("url", ""),
                        "description": result.get("description", "")
                    }
                    formatted_results.append(formatted_result)
            
            return self.success_response(formatted_results)
            
        except httpx.HTTPStatusError as e:
            return self.fail_response(f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
        except httpx.RequestError as e:
            return self.fail_response(f"Request error occurred: {str(e)}")
        except Exception as e:
            return self.fail_response(f"An error occurred: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "brave_scrape_webpage",
            "description": "Retrieve the complete text content of a specific webpage using Brave API. This tool extracts the full text content from any accessible web page and returns it for analysis, processing, or reference. The extracted text includes the main content of the page without HTML markup.",
            "parameters": {
                "type": "object",
                "properties": {
                    "url": {
                        "type": "string",
                        "description": "The URL of the webpage to scrape."
                    }
                },
                "required": ["url"]
            }
        }
    })
    @xml_schema(
        tag_name="brave-scrape",
        mappings=[
            {"param_name": "url", "node_type": "attribute", "path": "."}
        ],
        example='''
        <!-- 
        The brave-scrape tool extracts the complete text content from web pages using Brave API.
        IMPORTANT WORKFLOW RULES:
        1. ALWAYS use brave-search first to find relevant URLs
        2. Then use brave-scrape on URLs from search results
        
        Features:
        - Converts web pages into clean text
        - Handles dynamic content and JavaScript-rendered sites
        - Outputs clean, structured content
        -->
        
        <!-- Example workflow: -->
        <!-- 1. First search for relevant content -->
        <brave-search 
            query="latest AI research papers" 
            num_results="5">
        </brave-search>
        
        <!-- 2. Then scrape specific URLs from search results -->
        <brave-scrape 
            url="https://example.com/research/ai-paper-2024">
        </brave-scrape>
        '''
    )
    async def brave_scrape_webpage(
        self,
        url: str
    ) -> ToolResult:
        """
        Retrieve the complete text content of a webpage using Brave API.
        
        This function scrapes the specified URL and extracts the full text content from the page.
        The extracted text is returned in the response, making it available for further analysis,
        processing, or reference.
        """
        try:
            # Ensure we have a valid URL
            if not url or not isinstance(url, str):
                return self.fail_response("A valid URL is required.")
            
            # Use Brave Search API to get the content
            async with httpx.AsyncClient() as client:
                headers = {
                    "Accept": "application/json",
                    "Accept-Encoding": "gzip",
                    "X-Subscription-Token": self.brave_api_key
                }
                
                # First, try to get the content using the web search API with the URL as the query
                params = {
                    "q": f"url:{url}",
                    "count": 1
                }
                
                response = await client.get(
                    "https://api.search.brave.com/res/v1/web/search",
                    params=params,
                    headers=headers,
                    timeout=30
                )
                
                response.raise_for_status()
                search_response = response.json()
                
                # If we have AI API key, use the summarizer endpoint for better content extraction
                if self.brave_ai_api_key:
                    ai_headers = {
                        "Accept": "application/json",
                        "Accept-Encoding": "gzip",
                        "X-Subscription-Token": self.brave_ai_api_key
                    }
                    
                    ai_params = {
                        "q": f"url:{url}",
                        "search_response": json.dumps(search_response)
                    }
                    
                    ai_response = await client.get(
                        "https://api.search.brave.com/res/v1/summarizer/search",
                        params=ai_params,
                        headers=ai_headers,
                        timeout=60
                    )
                    
                    if ai_response.status_code == 200:
                        ai_data = ai_response.json()
                        if "summary" in ai_data:
                            # Format the response with AI-enhanced content
                            formatted_result = {
                                "Title": search_response.get("query", {}).get("original_query", url),
                                "URL": url,
                                "Text": ai_data.get("summary", ""),
                                "Source": "Brave AI API"
                            }
                            return self.success_response([formatted_result])
            
            # If we don't have AI API key or AI request failed, use the web search results
            content = ""
            title = url
            
            if "web" in search_response and "results" in search_response["web"] and search_response["web"]["results"]:
                result = search_response["web"]["results"][0]
                title = result.get("title", url)
                content = result.get("description", "")
                
                # If there's additional content in the result, add it
                if "extra_snippets" in result:
                    content += "\n\n" + "\n".join(result.get("extra_snippets", []))
            
            # Format the response
            formatted_result = {
                "Title": title,
                "URL": url,
                "Text": content,
                "Source": "Brave Search API"
            }
            
            return self.success_response([formatted_result])
            
        except httpx.HTTPStatusError as e:
            return self.fail_response(f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
        except httpx.RequestError as e:
            return self.fail_response(f"Request error occurred: {str(e)}")
        except Exception as e:
            return self.fail_response(f"An error occurred: {str(e)}")


if __name__ == "__main__":
    import asyncio
    
    async def test_brave_web_search():
        """Test function for the Brave web search tool"""
        search_tool = BraveWebTool()
        result = await search_tool.brave_web_search(
            query="latest AI developments",
            num_results=5
        )
        print(result)
    
    async def test_brave_scrape_webpage():
        """Test function for the Brave webpage scrape tool"""
        search_tool = BraveWebTool()
        result = await search_tool.brave_scrape_webpage(
            url="https://www.wired.com/story/anthropic-benevolent-artificial-intelligence/"
        )
        print(result)
    
    async def run_tests():
        """Run all test functions"""
        await test_brave_web_search()
        await test_brave_scrape_webpage()
        
    asyncio.run(run_tests())
