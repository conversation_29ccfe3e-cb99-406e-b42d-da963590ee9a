/**
 * Utilities for detecting and processing code in AI-generated content
 */

export interface CodeBlock {
  code: string;
  language: string;
  startIndex: number;
  endIndex: number;
  title?: string;
  description?: string;
}

export interface CodeGenerationContext {
  isCodeGeneration: boolean;
  codeBlocks: CodeBlock[];
  hasExecutableCode: boolean;
  primaryLanguage?: string;
  confidence: number;
}

/**
 * Detect if a message contains AI-generated code that should be displayed as artifacts
 */
export function detectCodeGeneration(content: string): CodeGenerationContext {
  const codeBlocks = extractCodeBlocks(content);
  
  if (codeBlocks.length === 0) {
    return {
      isCodeGeneration: false,
      codeBlocks: [],
      hasExecutableCode: false,
      confidence: 0
    };
  }

  // Calculate confidence based on various factors
  let confidence = 0;
  
  // Factor 1: Number of code blocks
  confidence += Math.min(codeBlocks.length * 0.2, 0.4);
  
  // Factor 2: Code block size (larger blocks are more likely to be intentional code generation)
  const avgCodeLength = codeBlocks.reduce((sum, block) => sum + block.code.length, 0) / codeBlocks.length;
  if (avgCodeLength > 100) confidence += 0.3;
  if (avgCodeLength > 500) confidence += 0.2;
  
  // Factor 3: Presence of executable languages
  const executableLanguages = ['python', 'py', 'javascript', 'js', 'typescript', 'ts', 'java', 'cpp', 'c', 'rust', 'go'];
  const hasExecutable = codeBlocks.some(block => executableLanguages.includes(block.language.toLowerCase()));
  if (hasExecutable) confidence += 0.3;
  
  // Factor 4: Code complexity indicators
  const complexityIndicators = [
    /import\s+\w+/g,
    /from\s+\w+\s+import/g,
    /def\s+\w+\(/g,
    /class\s+\w+/g,
    /function\s+\w+\(/g,
    /const\s+\w+\s*=/g,
    /let\s+\w+\s*=/g,
    /var\s+\w+\s*=/g,
    /#include\s*</g,
    /package\s+\w+/g
  ];
  
  const totalComplexity = codeBlocks.reduce((sum, block) => {
    return sum + complexityIndicators.reduce((count, pattern) => {
      const matches = block.code.match(pattern);
      return count + (matches ? matches.length : 0);
    }, 0);
  }, 0);
  
  if (totalComplexity > 2) confidence += 0.2;
  if (totalComplexity > 5) confidence += 0.1;
  
  // Factor 5: Context clues in surrounding text
  const contextClues = [
    /here'?s?\s+(the\s+)?code/i,
    /here'?s?\s+a\s+\w+\s+(script|program|function|example)/i,
    /you\s+can\s+use\s+this\s+code/i,
    /try\s+this\s+code/i,
    /run\s+this\s+code/i,
    /execute\s+this/i,
    /copy\s+and\s+paste/i,
    /save\s+this\s+as/i,
    /create\s+a\s+file/i,
    /implementation/i,
    /solution/i,
    /example/i
  ];
  
  const hasContextClues = contextClues.some(pattern => pattern.test(content));
  if (hasContextClues) confidence += 0.2;
  
  // Determine primary language
  const languageCounts = codeBlocks.reduce((counts, block) => {
    const lang = block.language.toLowerCase();
    counts[lang] = (counts[lang] || 0) + block.code.length;
    return counts;
  }, {} as Record<string, number>);
  
  const primaryLanguage = Object.entries(languageCounts)
    .sort(([, a], [, b]) => b - a)[0]?.[0];

  return {
    isCodeGeneration: confidence > 0.5,
    codeBlocks: enhanceCodeBlocks(codeBlocks, content),
    hasExecutableCode: hasExecutable,
    primaryLanguage,
    confidence: Math.min(confidence, 1)
  };
}

/**
 * Extract code blocks from markdown content
 */
function extractCodeBlocks(content: string): CodeBlock[] {
  const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
  const blocks: CodeBlock[] = [];
  let match;

  while ((match = codeBlockRegex.exec(content)) !== null) {
    blocks.push({
      code: match[2].trim(),
      language: match[1] || 'text',
      startIndex: match.index,
      endIndex: match.index + match[0].length
    });
  }

  return blocks;
}

/**
 * Enhance code blocks with titles and descriptions based on context
 */
function enhanceCodeBlocks(codeBlocks: CodeBlock[], content: string): CodeBlock[] {
  return codeBlocks.map((block, index) => {
    // Try to find a title or description before the code block
    const beforeContent = content.substring(0, block.startIndex);
    const lines = beforeContent.split('\n').reverse();
    
    let title = '';
    let description = '';
    
    // Look for titles in the preceding lines
    for (let i = 0; i < Math.min(lines.length, 5); i++) {
      const line = lines[i].trim();
      if (!line) continue;
      
      // Check for markdown headers
      const headerMatch = line.match(/^#+\s*(.+)$/);
      if (headerMatch) {
        title = headerMatch[1];
        break;
      }
      
      // Check for file names or descriptive text
      const fileMatch = line.match(/`([^`]+\.(py|js|ts|java|cpp|c|rs|go|php|rb|swift|kt|scala|r|sql|html|css|json|yaml|xml|md|sh))`/i);
      if (fileMatch) {
        title = fileMatch[1];
        break;
      }
      
      // Check for descriptive patterns
      const descPatterns = [
        /here'?s?\s+(the\s+)?(.+?)(?:\s+code)?:?$/i,
        /this\s+(.+?)\s+(?:will|does|can)/i,
        /(?:create|save|write)\s+(?:a\s+)?(.+?)(?:\s+file)?:?$/i
      ];
      
      for (const pattern of descPatterns) {
        const match = line.match(pattern);
        if (match) {
          description = match[1] || match[2];
          break;
        }
      }
      
      if (title || description) break;
    }
    
    // Generate default title if none found
    if (!title) {
      const lang = block.language.toLowerCase();
      const langNames: Record<string, string> = {
        python: 'Python',
        py: 'Python',
        javascript: 'JavaScript',
        js: 'JavaScript',
        typescript: 'TypeScript',
        ts: 'TypeScript',
        java: 'Java',
        cpp: 'C++',
        c: 'C',
        rust: 'Rust',
        go: 'Go',
        php: 'PHP',
        ruby: 'Ruby',
        swift: 'Swift',
        kotlin: 'Kotlin',
        scala: 'Scala',
        r: 'R',
        sql: 'SQL',
        html: 'HTML',
        css: 'CSS',
        json: 'JSON',
        yaml: 'YAML',
        xml: 'XML',
        markdown: 'Markdown',
        bash: 'Bash',
        shell: 'Shell'
      };
      
      const langName = langNames[lang] || lang.toUpperCase();
      title = codeBlocks.length > 1 
        ? `${langName} Code ${index + 1}`
        : `${langName} Code`;
    }
    
    return {
      ...block,
      title: title.trim(),
      description: description.trim()
    };
  });
}

/**
 * Check if content has executable code that can be run
 */
export function hasExecutableCode(content: string): boolean {
  const codeBlocks = extractCodeBlocks(content);
  const executableLanguages = ['python', 'py', 'javascript', 'js', 'typescript', 'ts'];
  
  return codeBlocks.some(block => 
    executableLanguages.includes(block.language.toLowerCase()) && 
    block.code.length > 10
  );
}

/**
 * Get the most appropriate language for execution from content
 */
export function getPrimaryExecutableLanguage(content: string): string | null {
  const codeBlocks = extractCodeBlocks(content);
  const executableLanguages = ['python', 'py', 'javascript', 'js', 'typescript', 'ts'];
  
  // Find the largest executable code block
  let largestBlock = null;
  let largestSize = 0;
  
  for (const block of codeBlocks) {
    if (executableLanguages.includes(block.language.toLowerCase()) && block.code.length > largestSize) {
      largestBlock = block;
      largestSize = block.code.length;
    }
  }
  
  return largestBlock ? largestBlock.language : null;
}
