"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-go@6.0.1";
exports.ids = ["vendor-chunks/@codemirror+lang-go@6.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-go@6.0.1/node_modules/@codemirror/lang-go/dist/index.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-go@6.0.1/node_modules/@codemirror/lang-go/dist/index.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   go: () => (/* binding */ go),\n/* harmony export */   goLanguage: () => (/* binding */ goLanguage),\n/* harmony export */   localCompletionSource: () => (/* binding */ localCompletionSource),\n/* harmony export */   snippets: () => (/* binding */ snippets)\n/* harmony export */ });\n/* harmony import */ var _lezer_go__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/go */ \"(ssr)/./node_modules/.pnpm/@lezer+go@1.0.0/node_modules/@lezer/go/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/autocomplete */ \"(ssr)/./node_modules/.pnpm/@codemirror+autocomplete@6.18.6/node_modules/@codemirror/autocomplete/dist/index.js\");\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n\n\n\n\n\n/**\nA collection of Go-related [snippets](https://codemirror.net/6/docs/ref/#autocomplete.snippet).\n*/\nconst snippets = [\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"func ${name}(${params}) ${type} {\\n\\t${}\\n}\", {\n        label: \"func\",\n        detail: \"declaration\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"func (${receiver}) ${name}(${params}) ${type} {\\n\\t${}\\n}\", {\n        label: \"func\",\n        detail: \"method declaration\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"var ${name} = ${value}\", {\n        label: \"var\",\n        detail: \"declaration\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"type ${name} ${type}\", {\n        label: \"type\",\n        detail: \"declaration\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"const ${name} = ${value}\", {\n        label: \"const\",\n        detail: \"declaration\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"type ${name} = ${type}\", {\n        label: \"type\",\n        detail: \"alias declaration\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"for ${init}; ${test}; ${update} {\\n\\t${}\\n}\", {\n        label: \"for\",\n        detail: \"loop\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"for ${i} := range ${value} {\\n\\t${}\\n}\", {\n        label: \"for\",\n        detail: \"range\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"select {\\n\\t${}\\n}\", {\n        label: \"select\",\n        detail: \"statement\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"case ${}:\\n${}\", {\n        label: \"case\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"switch ${} {\\n\\t${}\\n}\", {\n        label: \"switch\",\n        detail: \"statement\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"switch ${}.(${type}) {\\n\\t${}\\n}\", {\n        label: \"switch\",\n        detail: \"type statement\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"if ${} {\\n\\t${}\\n}\", {\n        label: \"if\",\n        detail: \"block\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"if ${} {\\n\\t${}\\n} else {\\n\\t${}\\n}\", {\n        label: \"if\",\n        detail: \"/ else block\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"import ${name} \\\"${module}\\\"\\n${}\", {\n        label: \"import\",\n        detail: \"declaration\",\n        type: \"keyword\"\n    }),\n];\n\nconst cache = /*@__PURE__*/new _lezer_common__WEBPACK_IMPORTED_MODULE_1__.NodeWeakMap();\nconst ScopeNodes = /*@__PURE__*/new Set([\n    \"SourceFile\", \"Block\",\n    \"FunctionDecl\", \"MethodDecl\", \"FunctionLiteral\",\n    \"ForStatement\", \"SwitchStatement\", \"TypeSwitchStatement\", \"IfStatement\",\n]);\nfunction defIDs(type, spec) {\n    return (node, def) => {\n        outer: for (let cur = node.node.firstChild, depth = 0, parent = null;;) {\n            while (!cur) {\n                if (!depth)\n                    break outer;\n                depth--;\n                cur = parent.nextSibling;\n                parent = parent.parent;\n            }\n            if (spec && cur.name == spec || cur.name == \"SpecList\") {\n                depth++;\n                parent = cur;\n                cur = cur.firstChild;\n            }\n            else {\n                if (cur.name == \"DefName\")\n                    def(cur, type);\n                cur = cur.nextSibling;\n            }\n        }\n        return true;\n    };\n}\nconst gatherCompletions = {\n    FunctionDecl: /*@__PURE__*/defIDs(\"function\"),\n    VarDecl: /*@__PURE__*/defIDs(\"var\", \"VarSpec\"),\n    ConstDecl: /*@__PURE__*/defIDs(\"constant\", \"ConstSpec\"),\n    TypeDecl: /*@__PURE__*/defIDs(\"type\", \"TypeSpec\"),\n    ImportDecl: /*@__PURE__*/defIDs(\"constant\", \"ImportSpec\"),\n    Parameter: /*@__PURE__*/defIDs(\"var\"),\n    __proto__: null\n};\nfunction getScope(doc, node) {\n    let cached = cache.get(node);\n    if (cached)\n        return cached;\n    let completions = [], top = true;\n    function def(node, type) {\n        let name = doc.sliceString(node.from, node.to);\n        completions.push({ label: name, type });\n    }\n    node.cursor(_lezer_common__WEBPACK_IMPORTED_MODULE_1__.IterMode.IncludeAnonymous).iterate(node => {\n        if (top) {\n            top = false;\n        }\n        else if (node.name) {\n            let gather = gatherCompletions[node.name];\n            if (gather && gather(node, def) || ScopeNodes.has(node.name))\n                return false;\n        }\n        else if (node.to - node.from > 8192) {\n            // Allow caching for bigger internal nodes\n            for (let c of getScope(doc, node.node))\n                completions.push(c);\n            return false;\n        }\n    });\n    cache.set(node, completions);\n    return completions;\n}\nconst Identifier = /^[\\w$\\xa1-\\uffff][\\w$\\d\\xa1-\\uffff]*$/;\nconst dontComplete = [\n    \"String\", \"LineComment\", \"BlockComment\",\n    \"DefName\", \"LabelName\", \"FieldName\",\n    \".\", \"?.\"\n];\n/**\nCompletion source that looks up locally defined names in Go code.\n*/\nconst localCompletionSource = context => {\n    let inner = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxTree)(context.state).resolveInner(context.pos, -1);\n    if (dontComplete.indexOf(inner.name) > -1)\n        return null;\n    let isWord = inner.name == \"VariableName\" ||\n        inner.to - inner.from < 20 && Identifier.test(context.state.sliceDoc(inner.from, inner.to));\n    if (!isWord && !context.explicit)\n        return null;\n    let options = [];\n    for (let pos = inner; pos; pos = pos.parent) {\n        if (ScopeNodes.has(pos.name))\n            options = options.concat(getScope(context.state.doc, pos));\n    }\n    return {\n        options,\n        from: isWord ? inner.from : context.pos,\n        validFor: Identifier\n    };\n};\n\n/**\nA language provider based on the [Lezer Go\nparser](https://github.com/lezer-parser/go), extended with\nfolding and indentation information.\n*/\nconst goLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.LRLanguage.define({\n    name: \"go\",\n    parser: /*@__PURE__*/_lezer_go__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentNodeProp.add({\n                IfStatement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.continuedIndent)({ except: /^\\s*({|else\\b)/ }),\n                LabeledStatement: _codemirror_language__WEBPACK_IMPORTED_MODULE_3__.flatIndent,\n                \"SwitchBlock SelectBlock\": context => {\n                    let after = context.textAfter, closed = /^\\s*\\}/.test(after), isCase = /^\\s*(case|default)\\b/.test(after);\n                    return context.baseIndent + (closed || isCase ? 0 : context.unit);\n                },\n                Block: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.delimitedIndent)({ closing: \"}\" }),\n                BlockComment: () => null,\n                Statement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.continuedIndent)({ except: /^{/ }),\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.foldNodeProp.add({\n                \"Block SwitchBlock SelectBlock LiteralValue InterfaceType StructType SpecList\": _codemirror_language__WEBPACK_IMPORTED_MODULE_3__.foldInside,\n                BlockComment(tree) { return { from: tree.from + 2, to: tree.to - 2 }; }\n            })\n        ]\n    }),\n    languageData: {\n        closeBrackets: { brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"] },\n        commentTokens: { line: \"//\", block: { open: \"/*\", close: \"*/\" } },\n        indentOnInput: /^\\s*(?:case\\b|default\\b|\\})$/\n    }\n});\nlet kwCompletion = (name) => ({ label: name, type: \"keyword\" });\nconst keywords = /*@__PURE__*/\"interface struct chan map package go return break continue goto fallthrough else defer range true false nil\".split(\" \").map(kwCompletion);\n/**\nGo support. Includes [snippet](https://codemirror.net/6/docs/ref/#lang-go.snippets) and local\nvariable completion.\n*/\nfunction go() {\n    let completions = snippets.concat(keywords);\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_3__.LanguageSupport(goLanguage, [\n        goLanguage.data.of({\n            autocomplete: (0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.ifNotIn)(dontComplete, (0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.completeFromList)(completions))\n        }),\n        goLanguage.data.of({\n            autocomplete: localCompletionSource\n        })\n    ]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-go@6.0.1/node_modules/@codemirror/lang-go/dist/index.js\n");

/***/ })

};
;