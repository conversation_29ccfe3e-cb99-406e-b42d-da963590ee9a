"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-to-parse5@8.0.0";
exports.ids = ["vendor-chunks/hast-util-to-parse5@8.0.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/hast-util-to-parse5@8.0.0/node_modules/hast-util-to-parse5/lib/index.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/hast-util-to-parse5@8.0.0/node_modules/hast-util-to-parse5/lib/index.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   toParse5: () => (/* binding */ toParse5)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/.pnpm/comma-separated-tokens@2.0.3/node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/.pnpm/devlop@1.1.0/node_modules/devlop/lib/development.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/.pnpm/property-information@6.5.0/node_modules/property-information/lib/find.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/.pnpm/space-separated-tokens@2.0.2/node_modules/space-separated-tokens/index.js\");\n/* harmony import */ var web_namespaces__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! web-namespaces */ \"(ssr)/./node_modules/.pnpm/web-namespaces@2.0.1/node_modules/web-namespaces/index.js\");\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zwitch */ \"(ssr)/./node_modules/.pnpm/zwitch@2.0.4/node_modules/zwitch/index.js\");\n/**\n * @typedef {import('hast').Comment} Comment\n * @typedef {import('hast').Doctype} Doctype\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Nodes} Nodes\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').RootContent} RootContent\n * @typedef {import('hast').Text} Text\n *\n * @typedef {import('parse5').DefaultTreeAdapterMap['document']} Parse5Document\n * @typedef {import('parse5').DefaultTreeAdapterMap['documentFragment']} Parse5Fragment\n * @typedef {import('parse5').DefaultTreeAdapterMap['element']} Parse5Element\n * @typedef {import('parse5').DefaultTreeAdapterMap['node']} Parse5Nodes\n * @typedef {import('parse5').DefaultTreeAdapterMap['documentType']} Parse5Doctype\n * @typedef {import('parse5').DefaultTreeAdapterMap['commentNode']} Parse5Comment\n * @typedef {import('parse5').DefaultTreeAdapterMap['textNode']} Parse5Text\n * @typedef {import('parse5').DefaultTreeAdapterMap['parentNode']} Parse5Parent\n * @typedef {import('parse5').Token.Attribute} Parse5Attribute\n *\n * @typedef {import('property-information').Schema} Schema\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {Space | null | undefined} [space='html']\n *   Which space the document is in (default: `'html'`).\n *\n *   When an `<svg>` element is found in the HTML space, this package already\n *   automatically switches to and from the SVG space when entering and exiting\n *   it.\n *\n * @typedef {Exclude<Parse5Nodes, Parse5Document | Parse5Fragment>} Parse5Content\n *\n * @typedef {'html' | 'svg'} Space\n */\n\n\n\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\n\nconst own = {}.hasOwnProperty\n\nconst one = (0,zwitch__WEBPACK_IMPORTED_MODULE_0__.zwitch)('type', {handlers: {root, element, text, comment, doctype}})\n\n/**\n * Transform a hast tree to a `parse5` AST.\n *\n * @param {Nodes} tree\n *   Tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Parse5Nodes}\n *   `parse5` node.\n */\nfunction toParse5(tree, options) {\n  const settings = options || emptyOptions\n  const space = settings.space\n  return one(tree, space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_1__.svg : property_information__WEBPACK_IMPORTED_MODULE_1__.html)\n}\n\n/**\n * @param {Root} node\n *   Node (hast) to transform.\n * @param {Schema} schema\n *   Current schema.\n * @returns {Parse5Document}\n *   Parse5 node.\n */\nfunction root(node, schema) {\n  /** @type {Parse5Document} */\n  const result = {\n    nodeName: '#document',\n    // @ts-expect-error: `parse5` uses enums, which are actually strings.\n    mode: (node.data || {}).quirksMode ? 'quirks' : 'no-quirks',\n    childNodes: []\n  }\n  result.childNodes = all(node.children, result, schema)\n  patch(node, result)\n  return result\n}\n\n/**\n * @param {Root} node\n *   Node (hast) to transform.\n * @param {Schema} schema\n *   Current schema.\n * @returns {Parse5Fragment}\n *   Parse5 node.\n */\nfunction fragment(node, schema) {\n  /** @type {Parse5Fragment} */\n  const result = {nodeName: '#document-fragment', childNodes: []}\n  result.childNodes = all(node.children, result, schema)\n  patch(node, result)\n  return result\n}\n\n/**\n * @param {Doctype} node\n *   Node (hast) to transform.\n * @returns {Parse5Doctype}\n *   Parse5 node.\n */\nfunction doctype(node) {\n  /** @type {Parse5Doctype} */\n  const result = {\n    nodeName: '#documentType',\n    name: 'html',\n    publicId: '',\n    systemId: '',\n    parentNode: null\n  }\n\n  patch(node, result)\n  return result\n}\n\n/**\n * @param {Text} node\n *   Node (hast) to transform.\n * @returns {Parse5Text}\n *   Parse5 node.\n */\nfunction text(node) {\n  /** @type {Parse5Text} */\n  const result = {\n    nodeName: '#text',\n    value: node.value,\n    parentNode: null\n  }\n  patch(node, result)\n  return result\n}\n\n/**\n * @param {Comment} node\n *   Node (hast) to transform.\n * @returns {Parse5Comment}\n *   Parse5 node.\n */\nfunction comment(node) {\n  /** @type {Parse5Comment} */\n  const result = {\n    nodeName: '#comment',\n    data: node.value,\n    parentNode: null\n  }\n\n  patch(node, result)\n\n  return result\n}\n\n/**\n * @param {Element} node\n *   Node (hast) to transform.\n * @param {Schema} schema\n *   Current schema.\n * @returns {Parse5Element}\n *   Parse5 node.\n */\nfunction element(node, schema) {\n  const parentSchema = schema\n  let currentSchema = parentSchema\n\n  if (\n    node.type === 'element' &&\n    node.tagName.toLowerCase() === 'svg' &&\n    parentSchema.space === 'html'\n  ) {\n    currentSchema = property_information__WEBPACK_IMPORTED_MODULE_1__.svg\n  }\n\n  /** @type {Array<Parse5Attribute>} */\n  const attrs = []\n  /** @type {string} */\n  let prop\n\n  if (node.properties) {\n    for (prop in node.properties) {\n      if (prop !== 'children' && own.call(node.properties, prop)) {\n        const result = createProperty(\n          currentSchema,\n          prop,\n          node.properties[prop]\n        )\n\n        if (result) {\n          attrs.push(result)\n        }\n      }\n    }\n  }\n\n  const space = currentSchema.space\n  // `html` and `svg` both have a space.\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(space)\n\n  /** @type {Parse5Element} */\n  const result = {\n    nodeName: node.tagName,\n    tagName: node.tagName,\n    attrs,\n    // @ts-expect-error: `parse5` types are wrong.\n    namespaceURI: web_namespaces__WEBPACK_IMPORTED_MODULE_3__.webNamespaces[space],\n    childNodes: [],\n    parentNode: null\n  }\n  result.childNodes = all(node.children, result, currentSchema)\n  patch(node, result)\n\n  if (node.tagName === 'template' && node.content) {\n    // @ts-expect-error: `parse5` types are wrong.\n    result.content = fragment(node.content, currentSchema)\n  }\n\n  return result\n}\n\n/**\n * Handle a property.\n *\n * @param {Schema} schema\n *   Current schema.\n * @param {string} prop\n *   Key.\n * @param {Array<number | string> | boolean | number | string | null | undefined} value\n *   hast property value.\n * @returns {Parse5Attribute | undefined}\n *   Field for runtime, optional.\n */\nfunction createProperty(schema, prop, value) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_4__.find)(schema, prop)\n\n  // Ignore nullish and `NaN` values.\n  if (\n    value === false ||\n    value === null ||\n    value === undefined ||\n    (typeof value === 'number' && Number.isNaN(value)) ||\n    (!value && info.boolean)\n  ) {\n    return\n  }\n\n  if (Array.isArray(value)) {\n    // Accept `array`.\n    // Most props are space-separated.\n    value = info.commaSeparated ? (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_5__.stringify)(value) : (0,space_separated_tokens__WEBPACK_IMPORTED_MODULE_6__.stringify)(value)\n  }\n\n  /** @type {Parse5Attribute} */\n  const attribute = {\n    name: info.attribute,\n    value: value === true ? '' : String(value)\n  }\n\n  if (info.space && info.space !== 'html' && info.space !== 'svg') {\n    const index = attribute.name.indexOf(':')\n\n    if (index < 0) {\n      attribute.prefix = ''\n    } else {\n      attribute.name = attribute.name.slice(index + 1)\n      attribute.prefix = info.attribute.slice(0, index)\n    }\n\n    attribute.namespace = web_namespaces__WEBPACK_IMPORTED_MODULE_3__.webNamespaces[info.space]\n  }\n\n  return attribute\n}\n\n/**\n * Transform all hast nodes.\n *\n * @param {Array<RootContent>} children\n *   List of children.\n * @param {Parse5Parent} parentNode\n *   `parse5` parent node.\n * @param {Schema} schema\n *   Current schema.\n * @returns {Array<Parse5Content>}\n *   Transformed children.\n */\nfunction all(children, parentNode, schema) {\n  let index = -1\n  /** @type {Array<Parse5Content>} */\n  const results = []\n\n  if (children) {\n    while (++index < children.length) {\n      /** @type {Parse5Content} */\n      const child = one(children[index], schema)\n\n      child.parentNode = parentNode\n\n      results.push(child)\n    }\n  }\n\n  return results\n}\n\n/**\n * Add position info from `from` to `to`.\n *\n * @param {Nodes} from\n *   hast node.\n * @param {Parse5Nodes} to\n *   `parse5` node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(from, to) {\n  const position = from.position\n\n  if (position && position.start && position.end) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(typeof position.start.offset === 'number')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(typeof position.end.offset === 'number')\n\n    to.sourceCodeLocation = {\n      startLine: position.start.line,\n      startCol: position.start.column,\n      startOffset: position.start.offset,\n      endLine: position.end.line,\n      endCol: position.end.column,\n      endOffset: position.end.offset\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/hast-util-to-parse5@8.0.0/node_modules/hast-util-to-parse5/lib/index.js\n");

/***/ })

};
;