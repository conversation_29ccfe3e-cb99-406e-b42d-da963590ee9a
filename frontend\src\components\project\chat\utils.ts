import { Message } from './types';

// Group messages from the same sender that are within 3 minutes of each other
export const groupMessagesByTime = (messages: Message[]): Message[][] => {
  const groups: Message[][] = [];
  let currentGroup: Message[] = [];

  messages.forEach((message, index) => {
    // Start a new group if:
    // 1. This is the first message
    // 2. The sender is different from the previous message
    // 3. The time difference is more than 3 minutes
    if (index === 0) {
      currentGroup = [message];
    } else {
      const prevMessage = messages[index - 1];

      // Parse timestamps (format: "2:14 AM")
      const prevTime = parseTimeString(prevMessage.timestamp);
      const currentTime = parseTimeString(message.timestamp);

      // Calculate time difference in minutes
      const timeDiffMinutes = getTimeDifferenceInMinutes(prevTime, currentTime);

      // Check if this message should be grouped with the previous one
      if (message.sender === prevMessage.sender && timeDiffMinutes <= 3 && !message.isEmail && !message.isGitHub && !prevMessage.isEmail && !prevMessage.isGitHub) {
        currentGroup.push(message);
      } else {
        // Start a new group
        groups.push([...currentGroup]);
        currentGroup = [message];
      }
    }

    // Add the last group
    if (index === messages.length - 1) {
      groups.push([...currentGroup]);
    }
  });

  return groups;
};

// Parse time string (e.g., "2:14 AM") to a Date object
export const parseTimeString = (timeStr: string): Date => {
  const now = new Date();
  const [time, period] = timeStr.split(' ');
  const [hours, minutes] = time.split(':').map(Number);

  // Convert to 24-hour format
  let hour24 = hours;
  if (period === 'PM' && hours < 12) {
    hour24 += 12;
  } else if (period === 'AM' && hours === 12) {
    hour24 = 0;
  }

  const date = new Date(now);
  date.setHours(hour24, minutes, 0, 0);
  return date;
};

// Calculate time difference in minutes
export const getTimeDifferenceInMinutes = (time1: Date, time2: Date): number => {
  return Math.abs(time2.getTime() - time1.getTime()) / (1000 * 60);
};

// Function to strip markdown syntax for sidebar previews
export const stripMarkdown = (text: string): string => {
  return text
    // Remove headers
    .replace(/^#{1,6}\s+/gm, '')
    // Remove bold and italic
    .replace(/\*\*([^*]+)\*\*/g, '$1')
    .replace(/\*([^*]+)\*/g, '$1')
    .replace(/__([^_]+)__/g, '$1')
    .replace(/_([^_]+)_/g, '$1')
    // Remove strikethrough
    .replace(/~~([^~]+)~~/g, '$1')
    // Remove inline code
    .replace(/`([^`]+)`/g, '$1')
    // Remove code blocks
    .replace(/```[\s\S]*?```/g, '[Code Block]')
    // Remove links
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
    // Remove blockquotes
    .replace(/^>\s+/gm, '')
    // Remove list markers
    .replace(/^[\s]*[-*+]\s+/gm, '')
    .replace(/^[\s]*\d+\.\s+/gm, '')
    // Remove extra whitespace and newlines
    .replace(/\n+/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
};

// Format file size
export const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
};

// Fixed configuration as per requirements
export const FIXED_MODEL = 'anthropic/claude-3-7-sonnet-20250219';
export const THINKING_ENABLED = true;
export const COLLABORATION_ENABLED = true;

// Agent configuration
export const AGENT_ROLES = [
  { id: 'ceo', name: 'Kenard', role: 'CEO', avatar: '/roles/kenard.png', type: 'ceo' },
  { id: 'developer', name: 'Alex', role: 'Developer', avatar: '/roles/alex.png', type: 'developer' },
  { id: 'marketing', name: 'Chloe', role: 'Marketing', avatar: '/roles/chloe.png', type: 'marketing' },
  { id: 'product', name: 'Mark', role: 'Product Manager', avatar: '/roles/mark.png', type: 'product' },
  { id: 'sales', name: 'Hannah', role: 'Sales Representative', avatar: '/roles/hannah.png', type: 'sales' },
  { id: 'finance', name: 'Jenna', role: 'Finance Advisor', avatar: '/roles/jenna.png', type: 'finance' },
  { id: 'designer', name: 'Maisie', role: 'Designer', avatar: '/roles/maisie.png', type: 'designer' },
  { id: 'research', name: 'Garek', role: 'Research Analyst', avatar: '/roles/garek.png', type: 'research' }
];

// Employee data
export const ALL_EMPLOYEES = [
  { id: 1, name: 'Kenard', role: 'CEO', avatar: '/roles/kenard.png' },
  { id: 2, name: 'Alex', role: 'Developer', avatar: '/roles/alex.png' },
  { id: 3, name: 'Chloe', role: 'Marketing', avatar: '/roles/chloe.png' },
  { id: 4, name: 'Mark', role: 'Product', avatar: '/roles/mark.png' },
  { id: 5, name: 'Hannah', role: 'Sales', avatar: '/roles/hannah.png' }
];
