// Agent roles data
export const agentRoles = [
  {
    id: 'ceo',
    name: '<PERSON><PERSON>',
    role: 'CEO',
    description: 'Leads the overall strategy and vision',
    skills: ['Strategic planning', 'Team leadership'],
    imageSrc: '/roles/kenard.png'
  },
  {
    id: 'developer',
    name: '<PERSON>',
    role: 'Developer',
    description: 'Builds and implements technical solutions',
    skills: ['Full-stack development', 'Code architecture'],
    imageSrc: '/roles/alex.png'
  },
  {
    id: 'marketing',
    name: '<PERSON>',
    role: 'Marketing Officer',
    description: 'Creates and executes marketing strategies',
    skills: ['Content creation', 'Campaign planning'],
    imageSrc: '/roles/chloe.png'
  },
  {
    id: 'product',
    name: '<PERSON>',
    role: 'Product Manager',
    description: 'Defines product vision and roadmap',
    skills: ['Feature prioritization', 'User research'],
    imageSrc: '/roles/mark.png',
  },
  {
    id: 'sales',
    name: '<PERSON>',
    role: 'Sales Representative',
    description: 'Converts leads into customers',
    skills: ['Lead qualification', 'Demos and pitches'],
    imageSrc: '/roles/hannah.png',
  },
  {
    id: 'finance',
    name: '<PERSON>',
    role: 'Finance Advisor',
    description: 'Manages budgets and financial strategy',
    skills: ['Budget planning', 'Financial analysis'],
    imageSrc: '/roles/jenna.png',
  },
  {
    id: 'designer',
    name: 'Maisie',
    role: 'Designer',
    description: 'Creates visuals and user experiences',
    skills: ['UI/UX design', 'Brand identity'],
    imageSrc: '/roles/maisie.png',
  },
  {
    id: 'research',
    name: 'Garek',
    role: 'Research Analyst',
    description: 'Gathers and analyzes market data',
    skills: ['Competitive analysis', 'Market trends'],
    imageSrc: '/roles/garek.png',
  },
];

// Preset agent selections based on template
export const templatePresets = {
  'ai-developer': ['ceo', 'developer', 'product'],
  'content-creation': ['ceo', 'marketing', 'designer'],
  'custom-team': [],
};
