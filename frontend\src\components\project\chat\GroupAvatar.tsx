'use client';

import React from 'react';
import Image from 'next/image';

interface GroupAvatarProps {
  participants: Array<{ id: number; name: string; role: string; avatar: string }>;
  size?: 'small' | 'large';
}

export function GroupAvatar({ participants, size = 'small' }: GroupAvatarProps) {
  const containerSize = size === 'large' ? 'h-10 w-10' : 'h-10 w-10';
  const borderRadius = 'rounded-full'; // Discord uses circular avatars

  if (participants.length === 1) {
    return (
      <div className={`${containerSize} ${borderRadius} overflow-hidden bg-[#36393f] ring-2 ring-[#202225] shadow-lg`}>
        <Image
          src={participants[0].avatar}
          alt={participants[0].name}
          width={40}
          height={40}
          className="h-full w-full object-cover"
        />
      </div>
    );
  } else if (participants.length === 2) {
    return (
      <div className={`${containerSize} relative`}>
        {/* Main avatar (larger, bottom-right) */}
        <div className="absolute bottom-0 right-0 h-7 w-7 rounded-full overflow-hidden bg-[#36393f] ring-2 ring-[#202225] shadow-lg">
          <Image
            src={participants[0].avatar}
            alt={participants[0].name}
            width={28}
            height={28}
            className="h-full w-full object-cover"
          />
        </div>
        {/* Secondary avatar (smaller, top-left) */}
        <div className="absolute top-0 left-0 h-6 w-6 rounded-full overflow-hidden bg-[#36393f] ring-2 ring-[#202225] shadow-lg">
          <Image
            src={participants[1].avatar}
            alt={participants[1].name}
            width={24}
            height={24}
            className="h-full w-full object-cover"
          />
        </div>
      </div>
    );
  } else if (participants.length === 3) {
    return (
      <div className={`${containerSize} relative`}>
        {/* Main avatar (center-bottom) */}
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 h-6 w-6 rounded-full overflow-hidden bg-[#36393f] ring-2 ring-[#202225] shadow-lg">
          <Image
            src={participants[0].avatar}
            alt={participants[0].name}
            width={24}
            height={24}
            className="h-full w-full object-cover"
          />
        </div>
        {/* Top-left avatar */}
        <div className="absolute top-0 left-0 h-5 w-5 rounded-full overflow-hidden bg-[#36393f] ring-2 ring-[#202225] shadow-lg">
          <Image
            src={participants[1].avatar}
            alt={participants[1].name}
            width={20}
            height={20}
            className="h-full w-full object-cover"
          />
        </div>
        {/* Top-right avatar */}
        <div className="absolute top-0 right-0 h-5 w-5 rounded-full overflow-hidden bg-[#36393f] ring-2 ring-[#202225] shadow-lg">
          <Image
            src={participants[2].avatar}
            alt={participants[2].name}
            width={20}
            height={20}
            className="h-full w-full object-cover"
          />
        </div>
      </div>
    );
  } else if (participants.length === 4) {
    return (
      <div className={`${containerSize} relative`}>
        {/* Top-left */}
        <div className="absolute top-0 left-0 h-5 w-5 rounded-full overflow-hidden bg-[#36393f] ring-1 ring-[#202225] shadow-md">
          <Image
            src={participants[0].avatar}
            alt={participants[0].name}
            width={20}
            height={20}
            className="h-full w-full object-cover"
          />
        </div>
        {/* Top-right */}
        <div className="absolute top-0 right-0 h-5 w-5 rounded-full overflow-hidden bg-[#36393f] ring-1 ring-[#202225] shadow-md">
          <Image
            src={participants[1].avatar}
            alt={participants[1].name}
            width={20}
            height={20}
            className="h-full w-full object-cover"
          />
        </div>
        {/* Bottom-left */}
        <div className="absolute bottom-0 left-0 h-5 w-5 rounded-full overflow-hidden bg-[#36393f] ring-1 ring-[#202225] shadow-md">
          <Image
            src={participants[2].avatar}
            alt={participants[2].name}
            width={20}
            height={20}
            className="h-full w-full object-cover"
          />
        </div>
        {/* Bottom-right */}
        <div className="absolute bottom-0 right-0 h-5 w-5 rounded-full overflow-hidden bg-[#36393f] ring-1 ring-[#202225] shadow-md">
          <Image
            src={participants[3].avatar}
            alt={participants[3].name}
            width={20}
            height={20}
            className="h-full w-full object-cover"
          />
        </div>
      </div>
    );
  } else {
    // For 5+ participants, show first 3 and a count indicator
    return (
      <div className={`${containerSize} relative`}>
        {/* Main avatar (center-bottom) */}
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 h-6 w-6 rounded-full overflow-hidden bg-[#36393f] ring-2 ring-[#202225] shadow-lg">
          <Image
            src={participants[0].avatar}
            alt={participants[0].name}
            width={24}
            height={24}
            className="h-full w-full object-cover"
          />
        </div>
        {/* Top-left avatar */}
        <div className="absolute top-0 left-0 h-5 w-5 rounded-full overflow-hidden bg-[#36393f] ring-2 ring-[#202225] shadow-lg">
          <Image
            src={participants[1].avatar}
            alt={participants[1].name}
            width={20}
            height={20}
            className="h-full w-full object-cover"
          />
        </div>
        {/* Top-right count indicator */}
        <div className="absolute top-0 right-0 h-5 w-5 rounded-full bg-gradient-to-br from-[#5865f2] to-[#4752c4] ring-2 ring-[#202225] shadow-lg flex items-center justify-center">
          <span className="text-white text-[10px] font-bold">+{participants.length - 2}</span>
        </div>
      </div>
    );
  }
}
