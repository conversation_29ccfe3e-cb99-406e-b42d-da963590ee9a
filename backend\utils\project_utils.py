"""
Utility functions for project management.

This module provides functions for project initialization, agent setup,
and other project-related operations.
"""

import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

from utils.logger import logger
from agent.roles import get_agent_type_id

async def initialize_project_agents(client, project_id: str, agent_names: List[str]) -> bool:
    """Initialize agents for a new project.
    
    Args:
        client: Supabase client
        project_id: ID of the project to initialize agents for
        agent_names: List of agent names to initialize
        
    Returns:
        Whether the initialization was successful
    """
    try:
        # Get agent type IDs for the specified agent names
        agent_type_ids = []
        for agent_name in agent_names:
            agent_type_id = get_agent_type_id(agent_name)
            if agent_type_id:
                agent_type_ids.append((agent_name, agent_type_id))
            else:
                logger.warning(f"No agent type ID found for agent {agent_name}")
        
        # Create agents for each agent type
        for agent_name, agent_type_id in agent_type_ids:
            # Create a new agent
            agent_id = str(uuid.uuid4())
            
            # First, create the agent in the agents table
            await client.from_("agents").insert({
                "agent_id": agent_id,
                "name": agent_name,
                "description": f"{agent_name} agent for project {project_id}",
                "capabilities": {},
                "created_at": datetime.now(timezone.utc).isoformat()
            }).execute()
            
            # Then, add the agent to the project
            await client.from_("project_agents").insert({
                "project_id": project_id,
                "agent_type_id": agent_type_id,
                "agent_id": agent_id,
                "is_active": True,
                "created_at": datetime.now(timezone.utc).isoformat()
            }).execute()
            
            logger.info(f"Initialized agent {agent_name} for project {project_id}")
        
        return True
    except Exception as e:
        logger.error(f"Error initializing project agents: {str(e)}")
        return False

async def get_project_name(client, project_id: str) -> Optional[str]:
    """Get the name of a project.
    
    Args:
        client: Supabase client
        project_id: ID of the project
        
    Returns:
        Project name or None if not found
    """
    try:
        result = await client.from_("projects").select("name").eq("project_id", project_id).execute()
        
        if result.data and len(result.data) > 0:
            return result.data[0].get("name")
        
        return None
    except Exception as e:
        logger.error(f"Error getting project name: {str(e)}")
        return None

async def update_project_name(client, project_id: str, name: str) -> bool:
    """Update the name of a project.
    
    Args:
        client: Supabase client
        project_id: ID of the project
        name: New name for the project
        
    Returns:
        Whether the update was successful
    """
    try:
        await client.from_("projects").update({"name": name}).eq("project_id", project_id).execute()
        return True
    except Exception as e:
        logger.error(f"Error updating project name: {str(e)}")
        return False
