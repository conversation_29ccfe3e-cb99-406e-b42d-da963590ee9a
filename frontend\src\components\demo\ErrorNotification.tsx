import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Mail, ExternalLink, Clock } from 'lucide-react';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';

interface ErrorNotificationProps {
  onTriggerDemo: () => void;
  isActive: boolean;
}

export function ErrorNotification({ onTriggerDemo, isActive }: ErrorNotificationProps) {
  const [isTriggered, setIsTriggered] = useState(false);

  const handleTriggerDemo = () => {
    setIsTriggered(true);
    onTriggerDemo();
    toast('New notification received', {
      description: '<PERSON><PERSON> (CEO) has been notified about the site issue.',
      duration: 5000,
      icon: <Mail className="h-5 w-5 text-[#c4314b]" />,
    });
  };

  return (
    <Card className={`bg-white border-[#e1dfdd] ${isTriggered ? 'border-[#c4314b]/50' : ''}`}>
      <CardHeader className="pb-2 border-b border-[#e1dfdd]">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center text-[#252423]">
            <div className={`h-8 w-8 rounded-full flex items-center justify-center mr-2 ${isTriggered ? 'bg-[#c4314b]' : 'bg-[#8561c5]'}`}>
              <Mail className="h-4 w-4 text-white" />
            </div>
            {isTriggered ? 'Incoming Alert' : 'Notification Center'}
          </CardTitle>
          {isTriggered ? (
            <Badge variant="outline" className="bg-[#c4314b]/10 text-[#c4314b] border-[#c4314b]/20">
              New
            </Badge>
          ) : (
            <Badge variant="outline" className="bg-[#f3f2f1] text-[#605e5c] border-[#e1dfdd]">
              Ready
            </Badge>
          )}
        </div>
        <CardDescription className="text-[#605e5c]">
          {isTriggered
            ? 'From: <EMAIL>'
            : 'Simulate an alert to start the demo'}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-4">
        {isTriggered ? (
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="bg-[#c4314b] h-8 w-8 rounded-full flex items-center justify-center flex-shrink-0">
                <AlertTriangle className="h-4 w-4 text-white" />
              </div>
              <div className="flex-1">
                <div className="font-medium text-sm text-[#252423]">Site Down - Production Error</div>
                <div className="text-xs text-[#605e5c] mt-1">2:14 AM</div>
              </div>
            </div>

            <div className="ml-11 bg-white border border-[#e1dfdd] rounded-md p-3">
              <p className="text-sm mb-2 text-[#252423]">
                We've detected that your site is currently down due to a JavaScript error.
              </p>
              <div className="bg-[#f3f2f1] p-2 rounded text-xs font-mono mb-2 text-[#252423]">
                <p>Error: Uncaught TypeError: Cannot read properties of undefined (reading 'data')</p>
                <p>at UserDashboard.tsx:42:18</p>
                <p>at processComponent (react-dom.js:18345:16)</p>
              </div>
              <div className="flex items-center mt-2 text-xs text-[#6264a7]">
                <ExternalLink className="h-3 w-3 mr-1" />
                <span>View in Vercel Dashboard</span>
              </div>
            </div>

            <div className="flex items-center justify-between text-xs text-[#605e5c] border-t border-[#e1dfdd] pt-3 mt-3">
              <div className="flex items-center gap-1">
                <Clock className="h-3.5 w-3.5" />
                <span>Received: Just now</span>
              </div>
              <span>Priority: High</span>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="bg-[#f3f2f1] h-16 w-16 rounded-full flex items-center justify-center mb-4">
              <Mail className="h-8 w-8 text-[#8561c5]" />
            </div>
            <p className="text-sm text-[#605e5c] mb-6">
              Click the button below to simulate receiving an alert
            </p>
            <Button
              className="w-full max-w-xs bg-[#6264a7] hover:bg-[#585a96] text-white"
              onClick={handleTriggerDemo}
              disabled={isTriggered || !isActive}
            >
              Simulate Alert
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
