"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-sanitize@5.0.2";
exports.ids = ["vendor-chunks/hast-util-sanitize@5.0.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/hast-util-sanitize@5.0.2/node_modules/hast-util-sanitize/lib/index.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/hast-util-sanitize@5.0.2/node_modules/hast-util-sanitize/lib/index.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sanitize: () => (/* binding */ sanitize)\n/* harmony export */ });\n/* harmony import */ var _ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ungap/structured-clone */ \"(ssr)/./node_modules/.pnpm/@ungap+structured-clone@1.3.0/node_modules/@ungap/structured-clone/esm/index.js\");\n/* harmony import */ var unist_util_position__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-position */ \"(ssr)/./node_modules/.pnpm/unist-util-position@5.0.0/node_modules/unist-util-position/lib/index.js\");\n/* harmony import */ var _schema_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema.js */ \"(ssr)/./node_modules/.pnpm/hast-util-sanitize@5.0.2/node_modules/hast-util-sanitize/lib/schema.js\");\n/**\n * @import {\n *   Comment,\n *   Doctype,\n *   ElementContent,\n *   Element,\n *   Nodes,\n *   Properties,\n *   RootContent,\n *   Root,\n *   Text\n * } from 'hast'\n */\n\n/**\n * @typedef {[string, ...Array<Exclude<Properties[keyof Properties], Array<any>> | RegExp>] | string} PropertyDefinition\n *   Definition for a property.\n *\n * @typedef Schema\n *   Schema that defines what nodes and properties are allowed.\n *\n *   The default schema is `defaultSchema`, which follows how GitHub cleans.\n *   If any top-level key is missing in the given schema, the corresponding\n *   value of the default schema is used.\n *\n *   To extend the standard schema with a few changes, clone `defaultSchema`\n *   like so:\n *\n *   ```js\n *   import deepmerge from 'deepmerge'\n *   import {h} from 'hastscript'\n *   import {defaultSchema, sanitize} from 'hast-util-sanitize'\n *\n *   // This allows `className` on all elements.\n *   const schema = deepmerge(defaultSchema, {attributes: {'*': ['className']}})\n *\n *   const tree = sanitize(h('div', {className: ['foo']}), schema)\n *\n *   // `tree` still has `className`.\n *   console.log(tree)\n *   // {\n *   //   type: 'element',\n *   //   tagName: 'div',\n *   //   properties: {className: ['foo']},\n *   //   children: []\n *   // }\n *   ```\n * @property {boolean | null | undefined} [allowComments=false]\n *   Whether to allow comment nodes (default: `false`).\n *\n *   For example:\n *\n *   ```js\n *   allowComments: true\n *   ```\n * @property {boolean | null | undefined} [allowDoctypes=false]\n *   Whether to allow doctype nodes (default: `false`).\n *\n *   For example:\n *\n *   ```js\n *   allowDoctypes: true\n *   ```\n * @property {Record<string, Array<string>> | null | undefined} [ancestors]\n *   Map of tag names to a list of tag names which are required ancestors\n *   (default: `defaultSchema.ancestors`).\n *\n *   Elements with these tag names will be ignored if they occur outside of one\n *   of their allowed parents.\n *\n *   For example:\n *\n *   ```js\n *   ancestors: {\n *     tbody: ['table'],\n *     // …\n *     tr: ['table']\n *   }\n *   ```\n * @property {Record<string, Array<PropertyDefinition>> | null | undefined} [attributes]\n *   Map of tag names to allowed property names (default:\n *   `defaultSchema.attributes`).\n *\n *   The special key `'*'` as a tag name defines property names allowed on all\n *   elements.\n *\n *   The special value `'data*'` as a property name can be used to allow all\n *   `data` properties.\n *\n *   For example:\n *\n *   ```js\n *   attributes: {\n *     'ariaDescribedBy', 'ariaLabel', 'ariaLabelledBy', …, 'href'\n *     // …\n *     '*': [\n *       'abbr',\n *       'accept',\n *       'acceptCharset',\n *       // …\n *       'vAlign',\n *       'value',\n *       'width'\n *     ]\n *   }\n *   ```\n *\n *   Instead of a single string in the array, which allows any property value\n *   for the field, you can use an array to allow several values.\n *   For example, `input: ['type']` allows `type` set to any value on `input`s.\n *   But `input: [['type', 'checkbox', 'radio']]` allows `type` when set to\n *   `'checkbox'` or `'radio'`.\n *\n *   You can use regexes, so for example `span: [['className', /^hljs-/]]`\n *   allows any class that starts with `hljs-` on `span`s.\n *\n *   When comma- or space-separated values are used (such as `className`), each\n *   value in is checked individually.\n *   For example, to allow certain classes on `span`s for syntax highlighting,\n *   use `span: [['className', 'number', 'operator', 'token']]`.\n *   This will allow `'number'`, `'operator'`, and `'token'` classes, but drop\n *   others.\n * @property {Array<string> | null | undefined} [clobber]\n *   List of property names that clobber (default: `defaultSchema.clobber`).\n *\n *   For example:\n *\n *   ```js\n *   clobber: ['ariaDescribedBy', 'ariaLabelledBy', 'id', 'name']\n *   ```\n * @property {string | null | undefined} [clobberPrefix]\n *   Prefix to use before clobbering properties (default:\n *   `defaultSchema.clobberPrefix`).\n *\n *   For example:\n *\n *   ```js\n *   clobberPrefix: 'user-content-'\n *   ```\n * @property {Record<string, Array<string> | null | undefined> | null | undefined} [protocols]\n *   Map of *property names* to allowed protocols (default:\n *   `defaultSchema.protocols`).\n *\n *   This defines URLs that are always allowed to have local URLs (relative to\n *   the current website, such as `this`, `#this`, `/this`, or `?this`), and\n *   only allowed to have remote URLs (such as `https://example.com`) if they\n *   use a known protocol.\n *\n *   For example:\n *\n *   ```js\n *   protocols: {\n *     cite: ['http', 'https'],\n *     // …\n *     src: ['http', 'https']\n *   }\n *   ```\n * @property {Record<string, Record<string, Properties[keyof Properties]>> | null | undefined} [required]\n *   Map of tag names to required property names with a default value\n *   (default: `defaultSchema.required`).\n *\n *   This defines properties that must be set.\n *   If a field does not exist (after the element was made safe), these will be\n *   added with the given value.\n *\n *   For example:\n *\n *   ```js\n *   required: {\n *     input: {disabled: true, type: 'checkbox'}\n *   }\n *   ```\n *\n *   > 👉 **Note**: properties are first checked based on `schema.attributes`,\n *   > then on `schema.required`.\n *   > That means properties could be removed by `attributes` and then added\n *   > again with `required`.\n * @property {Array<string> | null | undefined} [strip]\n *   List of tag names to strip from the tree (default: `defaultSchema.strip`).\n *\n *   By default, unsafe elements (those not in `schema.tagNames`) are replaced\n *   by what they contain.\n *   This option can drop their contents.\n *\n *   For example:\n *\n *   ```js\n *   strip: ['script']\n *   ```\n * @property {Array<string> | null | undefined} [tagNames]\n *   List of allowed tag names (default: `defaultSchema.tagNames`).\n *\n *   For example:\n *\n *   ```js\n *   tagNames: [\n *     'a',\n *     'b',\n *     // …\n *     'ul',\n *     'var'\n *   ]\n *   ```\n *\n * @typedef State\n *   Info passed around.\n * @property {Readonly<Schema>} schema\n *   Schema.\n * @property {Array<string>} stack\n *   Tag names of ancestors.\n */\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * Sanitize a tree.\n *\n * @param {Readonly<Nodes>} node\n *   Unsafe tree.\n * @param {Readonly<Schema> | null | undefined} [options]\n *   Configuration (default: `defaultSchema`).\n * @returns {Nodes}\n *   New, safe tree.\n */\nfunction sanitize(node, options) {\n  /** @type {Nodes} */\n  let result = {type: 'root', children: []}\n\n  /** @type {State} */\n  const state = {\n    schema: options ? {..._schema_js__WEBPACK_IMPORTED_MODULE_0__.defaultSchema, ...options} : _schema_js__WEBPACK_IMPORTED_MODULE_0__.defaultSchema,\n    stack: []\n  }\n  const replace = transform(state, node)\n\n  if (replace) {\n    if (Array.isArray(replace)) {\n      if (replace.length === 1) {\n        result = replace[0]\n      } else {\n        result.children = replace\n      }\n    } else {\n      result = replace\n    }\n  }\n\n  return result\n}\n\n/**\n * Sanitize `node`.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<unknown>} node\n *   Unsafe node.\n * @returns {Array<ElementContent> | Nodes | undefined}\n *   Safe result.\n */\nfunction transform(state, node) {\n  if (node && typeof node === 'object') {\n    const unsafe = /** @type {Record<string, Readonly<unknown>>} */ (node)\n    const type = typeof unsafe.type === 'string' ? unsafe.type : ''\n\n    switch (type) {\n      case 'comment': {\n        return comment(state, unsafe)\n      }\n\n      case 'doctype': {\n        return doctype(state, unsafe)\n      }\n\n      case 'element': {\n        return element(state, unsafe)\n      }\n\n      case 'root': {\n        return root(state, unsafe)\n      }\n\n      case 'text': {\n        return text(state, unsafe)\n      }\n\n      default:\n    }\n  }\n}\n\n/**\n * Make a safe comment.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe comment-like value.\n * @returns {Comment | undefined}\n *   Safe comment (if with `allowComments`).\n */\nfunction comment(state, unsafe) {\n  if (state.schema.allowComments) {\n    // See <https://html.spec.whatwg.org/multipage/parsing.html#serialising-html-fragments>\n    const result = typeof unsafe.value === 'string' ? unsafe.value : ''\n    const index = result.indexOf('-->')\n    const value = index < 0 ? result : result.slice(0, index)\n\n    /** @type {Comment} */\n    const node = {type: 'comment', value}\n\n    patch(node, unsafe)\n\n    return node\n  }\n}\n\n/**\n * Make a safe doctype.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe doctype-like value.\n * @returns {Doctype | undefined}\n *   Safe doctype (if with `allowDoctypes`).\n */\nfunction doctype(state, unsafe) {\n  if (state.schema.allowDoctypes) {\n    /** @type {Doctype} */\n    const node = {type: 'doctype'}\n\n    patch(node, unsafe)\n\n    return node\n  }\n}\n\n/**\n * Make a safe element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe element-like value.\n * @returns {Array<ElementContent> | Element | undefined}\n *   Safe element.\n */\nfunction element(state, unsafe) {\n  const name = typeof unsafe.tagName === 'string' ? unsafe.tagName : ''\n\n  state.stack.push(name)\n\n  const content = /** @type {Array<ElementContent>} */ (\n    children(state, unsafe.children)\n  )\n  const properties_ = properties(state, unsafe.properties)\n\n  state.stack.pop()\n\n  let safeElement = false\n\n  if (\n    name &&\n    name !== '*' &&\n    (!state.schema.tagNames || state.schema.tagNames.includes(name))\n  ) {\n    safeElement = true\n\n    // Some nodes can break out of their context if they don’t have a certain\n    // ancestor.\n    if (state.schema.ancestors && own.call(state.schema.ancestors, name)) {\n      const ancestors = state.schema.ancestors[name]\n      let index = -1\n\n      safeElement = false\n\n      while (++index < ancestors.length) {\n        if (state.stack.includes(ancestors[index])) {\n          safeElement = true\n        }\n      }\n    }\n  }\n\n  if (!safeElement) {\n    return state.schema.strip && !state.schema.strip.includes(name)\n      ? content\n      : undefined\n  }\n\n  /** @type {Element} */\n  const node = {\n    type: 'element',\n    tagName: name,\n    properties: properties_,\n    children: content\n  }\n\n  patch(node, unsafe)\n\n  return node\n}\n\n/**\n * Make a safe root.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe root-like value.\n * @returns {Root}\n *   Safe root.\n */\nfunction root(state, unsafe) {\n  const content = /** @type {Array<RootContent>} */ (\n    children(state, unsafe.children)\n  )\n\n  /** @type {Root} */\n  const node = {type: 'root', children: content}\n\n  patch(node, unsafe)\n\n  return node\n}\n\n/**\n * Make a safe text.\n *\n * @param {State} _\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe text-like value.\n * @returns {Text}\n *   Safe text.\n */\nfunction text(_, unsafe) {\n  const value = typeof unsafe.value === 'string' ? unsafe.value : ''\n  /** @type {Text} */\n  const node = {type: 'text', value}\n\n  patch(node, unsafe)\n\n  return node\n}\n\n/**\n * Make children safe.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<unknown>} children\n *   Unsafe value.\n * @returns {Array<Nodes>}\n *   Safe children.\n */\nfunction children(state, children) {\n  /** @type {Array<Nodes>} */\n  const results = []\n\n  if (Array.isArray(children)) {\n    const childrenUnknown = /** @type {Array<Readonly<unknown>>} */ (children)\n    let index = -1\n\n    while (++index < childrenUnknown.length) {\n      const value = transform(state, childrenUnknown[index])\n\n      if (value) {\n        if (Array.isArray(value)) {\n          results.push(...value)\n        } else {\n          results.push(value)\n        }\n      }\n    }\n  }\n\n  return results\n}\n\n/**\n * Make element properties safe.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<unknown>} properties\n *   Unsafe value.\n * @returns {Properties}\n *   Safe value.\n */\nfunction properties(state, properties) {\n  const tagName = state.stack[state.stack.length - 1]\n  const attributes = state.schema.attributes\n  const required = state.schema.required\n  const specific =\n    attributes && own.call(attributes, tagName)\n      ? attributes[tagName]\n      : undefined\n  const defaults =\n    attributes && own.call(attributes, '*') ? attributes['*'] : undefined\n  const properties_ =\n    /** @type {Readonly<Record<string, Readonly<unknown>>>} */ (\n      properties && typeof properties === 'object' ? properties : {}\n    )\n  /** @type {Properties} */\n  const result = {}\n  /** @type {string} */\n  let key\n\n  for (key in properties_) {\n    if (own.call(properties_, key)) {\n      const unsafe = properties_[key]\n      let safe = propertyValue(\n        state,\n        findDefinition(specific, key),\n        key,\n        unsafe\n      )\n\n      if (safe === null || safe === undefined) {\n        safe = propertyValue(state, findDefinition(defaults, key), key, unsafe)\n      }\n\n      if (safe !== null && safe !== undefined) {\n        result[key] = safe\n      }\n    }\n  }\n\n  if (required && own.call(required, tagName)) {\n    const properties = required[tagName]\n\n    for (key in properties) {\n      if (own.call(properties, key) && !own.call(result, key)) {\n        result[key] = properties[key]\n      }\n    }\n  }\n\n  return result\n}\n\n/**\n * Sanitize a property value.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<PropertyDefinition> | undefined} definition\n *   Definition.\n * @param {string} key\n *   Field name.\n * @param {Readonly<unknown>} value\n *   Unsafe value (but an array).\n * @returns {Array<number | string> | boolean | number | string | undefined}\n *   Safe value.\n */\nfunction propertyValue(state, definition, key, value) {\n  return definition\n    ? Array.isArray(value)\n      ? propertyValueMany(state, definition, key, value)\n      : propertyValuePrimitive(state, definition, key, value)\n    : undefined\n}\n\n/**\n * Sanitize a property value which is a list.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<PropertyDefinition>} definition\n *   Definition.\n * @param {string} key\n *   Field name.\n * @param {Readonly<Array<Readonly<unknown>>>} values\n *   Unsafe value (but an array).\n * @returns {Array<number | string>}\n *   Safe value.\n */\nfunction propertyValueMany(state, definition, key, values) {\n  let index = -1\n  /** @type {Array<number | string>} */\n  const result = []\n\n  while (++index < values.length) {\n    const value = propertyValuePrimitive(state, definition, key, values[index])\n\n    if (typeof value === 'number' || typeof value === 'string') {\n      result.push(value)\n    }\n  }\n\n  return result\n}\n\n/**\n * Sanitize a property value which is a primitive.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<PropertyDefinition>} definition\n *   Definition.\n * @param {string} key\n *   Field name.\n * @param {Readonly<unknown>} value\n *   Unsafe value (but not an array).\n * @returns {boolean | number | string | undefined}\n *   Safe value.\n */\nfunction propertyValuePrimitive(state, definition, key, value) {\n  if (\n    typeof value !== 'boolean' &&\n    typeof value !== 'number' &&\n    typeof value !== 'string'\n  ) {\n    return\n  }\n\n  if (!safeProtocol(state, key, value)) {\n    return\n  }\n\n  // Just a string, or only one item in an array, means all values are OK.\n  // More than one item means an allow list.\n  if (typeof definition === 'object' && definition.length > 1) {\n    let ok = false\n    let index = 0 // Ignore `key`, which is the first item.\n\n    while (++index < definition.length) {\n      const allowed = definition[index]\n\n      // Expression.\n      if (allowed && typeof allowed === 'object' && 'flags' in allowed) {\n        if (allowed.test(String(value))) {\n          ok = true\n          break\n        }\n      }\n      // Primitive.\n      else if (allowed === value) {\n        ok = true\n        break\n      }\n    }\n\n    if (!ok) return\n  }\n\n  return state.schema.clobber &&\n    state.schema.clobberPrefix &&\n    state.schema.clobber.includes(key)\n    ? state.schema.clobberPrefix + value\n    : value\n}\n\n/**\n * Check whether `value` is a safe URL.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} key\n *   Field name.\n * @param {Readonly<unknown>} value\n *   Unsafe value.\n * @returns {boolean}\n *   Whether it’s a safe value.\n */\nfunction safeProtocol(state, key, value) {\n  const protocols =\n    state.schema.protocols && own.call(state.schema.protocols, key)\n      ? state.schema.protocols[key]\n      : undefined\n\n  // No protocols defined? Then everything is fine.\n  if (!protocols || protocols.length === 0) {\n    return true\n  }\n\n  const url = String(value)\n  const colon = url.indexOf(':')\n  const questionMark = url.indexOf('?')\n  const numberSign = url.indexOf('#')\n  const slash = url.indexOf('/')\n\n  if (\n    colon < 0 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash > -1 && colon > slash) ||\n    (questionMark > -1 && colon > questionMark) ||\n    (numberSign > -1 && colon > numberSign)\n  ) {\n    return true\n  }\n\n  let index = -1\n\n  while (++index < protocols.length) {\n    const protocol = protocols[index]\n\n    if (\n      colon === protocol.length &&\n      url.slice(0, protocol.length) === protocol\n    ) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Add data and position.\n *\n * @param {Nodes} node\n *   Node to patch safe data and position on.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe node-like value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(node, unsafe) {\n  const cleanPosition = (0,unist_util_position__WEBPACK_IMPORTED_MODULE_1__.position)(\n    // @ts-expect-error: looks like a node.\n    unsafe\n  )\n\n  if (unsafe.data) {\n    node.data = (0,_ungap_structured_clone__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(unsafe.data)\n  }\n\n  if (cleanPosition) node.position = cleanPosition\n}\n\n/**\n *\n * @param {Readonly<Array<PropertyDefinition>> | undefined} definitions\n * @param {string} key\n * @returns {Readonly<PropertyDefinition> | undefined}\n */\nfunction findDefinition(definitions, key) {\n  /** @type {PropertyDefinition | undefined} */\n  let dataDefault\n  let index = -1\n\n  if (definitions) {\n    while (++index < definitions.length) {\n      const entry = definitions[index]\n      const name = typeof entry === 'string' ? entry : entry[0]\n\n      if (name === key) {\n        return entry\n      }\n\n      if (name === 'data*') dataDefault = entry\n    }\n  }\n\n  if (key.length > 4 && key.slice(0, 4).toLowerCase() === 'data') {\n    return dataDefault\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/hast-util-sanitize@5.0.2/node_modules/hast-util-sanitize/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/hast-util-sanitize@5.0.2/node_modules/hast-util-sanitize/lib/schema.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/.pnpm/hast-util-sanitize@5.0.2/node_modules/hast-util-sanitize/lib/schema.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultSchema: () => (/* binding */ defaultSchema)\n/* harmony export */ });\n/**\n * @import {Schema} from 'hast-util-sanitize'\n */\n\n// Couple of ARIA attributes allowed in several, but not all, places.\nconst aria = ['ariaDescribedBy', 'ariaLabel', 'ariaLabelledBy']\n\n/**\n * Default schema.\n *\n * Follows GitHub style sanitation.\n *\n * @type {Schema}\n */\nconst defaultSchema = {\n  ancestors: {\n    tbody: ['table'],\n    td: ['table'],\n    th: ['table'],\n    thead: ['table'],\n    tfoot: ['table'],\n    tr: ['table']\n  },\n  attributes: {\n    a: [\n      ...aria,\n      // Note: these 3 are used by GFM footnotes, they do work on all links.\n      'dataFootnoteBackref',\n      'dataFootnoteRef',\n      ['className', 'data-footnote-backref'],\n      'href'\n    ],\n    blockquote: ['cite'],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `code` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    code: [['className', /^language-./]],\n    del: ['cite'],\n    div: ['itemScope', 'itemType'],\n    dl: [...aria],\n    // Note: this is used by GFM footnotes.\n    h2: [['className', 'sr-only']],\n    img: [...aria, 'longDesc', 'src'],\n    // Note: `input` is not normally allowed by GH, when manually writing\n    // it in markdown, they add it from tasklists some other way.\n    // We can’t do that, so we have to allow it.\n    input: [\n      ['disabled', true],\n      ['type', 'checkbox']\n    ],\n    ins: ['cite'],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `li` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    li: [['className', 'task-list-item']],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `ol` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    ol: [...aria, ['className', 'contains-task-list']],\n    q: ['cite'],\n    section: ['dataFootnotes', ['className', 'footnotes']],\n    source: ['srcSet'],\n    summary: [...aria],\n    table: [...aria],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `ol` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    ul: [...aria, ['className', 'contains-task-list']],\n    '*': [\n      'abbr',\n      'accept',\n      'acceptCharset',\n      'accessKey',\n      'action',\n      'align',\n      'alt',\n      'axis',\n      'border',\n      'cellPadding',\n      'cellSpacing',\n      'char',\n      'charOff',\n      'charSet',\n      'checked',\n      'clear',\n      'colSpan',\n      'color',\n      'cols',\n      'compact',\n      'coords',\n      'dateTime',\n      'dir',\n      // Note: `disabled` is technically allowed on all elements by GH.\n      // But it is useless on everything except `input`.\n      // Because `input`s are normally not allowed, but we allow them for\n      // checkboxes due to tasklists, we allow `disabled` only there.\n      'encType',\n      'frame',\n      'hSpace',\n      'headers',\n      'height',\n      'hrefLang',\n      'htmlFor',\n      'id',\n      'isMap',\n      'itemProp',\n      'label',\n      'lang',\n      'maxLength',\n      'media',\n      'method',\n      'multiple',\n      'name',\n      'noHref',\n      'noShade',\n      'noWrap',\n      'open',\n      'prompt',\n      'readOnly',\n      'rev',\n      'rowSpan',\n      'rows',\n      'rules',\n      'scope',\n      'selected',\n      'shape',\n      'size',\n      'span',\n      'start',\n      'summary',\n      'tabIndex',\n      'title',\n      'useMap',\n      'vAlign',\n      'value',\n      'width'\n    ]\n  },\n  clobber: ['ariaDescribedBy', 'ariaLabelledBy', 'id', 'name'],\n  clobberPrefix: 'user-content-',\n  protocols: {\n    cite: ['http', 'https'],\n    href: ['http', 'https', 'irc', 'ircs', 'mailto', 'xmpp'],\n    longDesc: ['http', 'https'],\n    src: ['http', 'https']\n  },\n  required: {\n    input: {disabled: true, type: 'checkbox'}\n  },\n  strip: ['script'],\n  tagNames: [\n    'a',\n    'b',\n    'blockquote',\n    'br',\n    'code',\n    'dd',\n    'del',\n    'details',\n    'div',\n    'dl',\n    'dt',\n    'em',\n    'h1',\n    'h2',\n    'h3',\n    'h4',\n    'h5',\n    'h6',\n    'hr',\n    'i',\n    'img',\n    // Note: `input` is not normally allowed by GH, when manually writing\n    // it in markdown, they add it from tasklists some other way.\n    // We can’t do that, so we have to allow it.\n    'input',\n    'ins',\n    'kbd',\n    'li',\n    'ol',\n    'p',\n    'picture',\n    'pre',\n    'q',\n    'rp',\n    'rt',\n    'ruby',\n    's',\n    'samp',\n    'section',\n    'source',\n    'span',\n    'strike',\n    'strong',\n    'sub',\n    'summary',\n    'sup',\n    'table',\n    'tbody',\n    'td',\n    'tfoot',\n    'th',\n    'thead',\n    'tr',\n    'tt',\n    'ul',\n    'var'\n  ]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/hast-util-sanitize@5.0.2/node_modules/hast-util-sanitize/lib/schema.js\n");

/***/ })

};
;