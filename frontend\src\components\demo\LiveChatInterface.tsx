'use client';

import React, { useState, useEffect, useRef } from 'react';
import { initiateAgent, initiateAgentWithFormData, sendMessage, getMessages, streamAgentWithFormData } from '@/lib/api';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { Send, Phone, Video, Monitor, Settings, Users, MessageSquare } from 'lucide-react';

// Available agent roles
const AGENT_ROLES = [
  { id: '<PERSON><PERSON>', name: 'CEO', description: 'Strategic leadership and decision making' },
  { id: '<PERSON>', name: '<PERSON><PERSON><PERSON>', description: 'Software development and technical implementation' },
  { id: 'Chloe', name: 'Marketing', description: 'Marketing strategy and content creation' },
  { id: '<PERSON>', name: 'Product', description: 'Product management and user experience' },
];

// Available models
const AI_MODELS = [
  { id: 'anthropic/claude-3-7-sonnet-latest', name: 'Claude 3.7 Sonnet (Latest)', supportsThinking: true },
  { id: 'anthropic/claude-3-opus-20240229', name: 'Claude 3 Opus', supportsThinking: false },
  { id: 'anthropic/claude-3-5-sonnet-20240620', name: 'Claude 3.5 Sonnet', supportsThinking: true },
  { id: 'anthropic/claude-3-haiku-20240307', name: 'Claude 3 Haiku (Fastest)', supportsThinking: false },
  { id: 'openai/gpt-4o', name: 'GPT-4o (Reliable)', supportsThinking: false },
];

interface Message {
  id: string;
  sender: 'ceo' | 'developer' | 'marketing' | 'product' | 'user' | 'system';
  content: string;
  timestamp: string;
  role?: string;
  type?: 'user' | 'assistant' | 'thinking';
  isCode?: boolean;
}

interface LiveChatInterfaceProps {
  isActive: boolean;
  project?: any;
}

export function LiveChatInterface({ isActive, project }: LiveChatInterfaceProps) {
  const [activeChat, setActiveChat] = useState<'ceo' | 'developer' | 'marketing' | 'product' | 'group'>('ceo');
  const [inputValue, setInputValue] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [threadId, setThreadId] = useState<string | null>(null);
  const [agentRunId, setAgentRunId] = useState<string | null>(null);
  const [selectedAgents, setSelectedAgents] = useState<string[]>(['Kenard']);
  const [collaborationMode, setCollaborationMode] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string>('anthropic/claude-3-7-sonnet-latest');
  const [enableThinking, setEnableThinking] = useState<boolean>(true);
  const [apiUrl, setApiUrl] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Initialize API URL
  useEffect(() => {
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:8000';
    const normalizedUrl = backendUrl.endsWith('/api') 
      ? backendUrl.slice(0, -4) 
      : backendUrl;
    setApiUrl(normalizedUrl);
  }, []);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle sending messages
  const handleSendMessage = async (e: React.FormEvent, messageContent?: string) => {
    e.preventDefault();
    const content = messageContent || inputValue;
    if (!content.trim()) return;

    setLoading(true);
    setError(null);

    try {
      // Add user message immediately
      const userMessage: Message = {
        id: `user-${Date.now()}`,
        sender: 'user',
        content: content,
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        role: 'user',
        type: 'user'
      };

      setMessages(prev => [...prev, userMessage]);
      setInputValue('');

      if (!threadId) {
        // Start new conversation
        await startNewConversation(content);
      } else {
        // Send follow-up message
        await sendFollowUpMessage(content);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setError(`Failed to send message: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setLoading(false);
    }
  };

  // Start new conversation
  const startNewConversation = async (content: string) => {
    try {
      // Map active chat to agent type
      const agentTypeMap: Record<string, string> = {
        'ceo': 'ceo',
        'developer': 'developer',
        'marketing': 'marketing',
        'product': 'product',
        'group': 'developer' // Default to developer for group chat
      };

      const agent_type = agentTypeMap[activeChat] || 'developer';
      const model = selectedModel.includes('/') ? selectedModel.split('/')[1] : selectedModel;

      // For group chat, always enable collaboration mode and include all agents
      const isGroupChat = activeChat === 'group';
      const effectiveCollaborationMode = isGroupChat || collaborationMode;
      const effectiveSelectedAgents = isGroupChat
        ? AGENT_ROLES.map(role => role.id)
        : (collaborationMode ? selectedAgents : [activeChat]);

      const initiateRequest = {
        agent_type,
        model,
        message: content,
        metadata: {
          enable_thinking: enableThinking,
          collaboration_mode: effectiveCollaborationMode,
          selected_agents: effectiveSelectedAgents,
          is_group_chat: isGroupChat,
          timestamp: new Date().toISOString()
        }
      };

      const response = await initiateAgent(initiateRequest);
      setThreadId(response.thread_id);
      setAgentRunId(response.agent_run_id);

      // Start streaming response
      await streamResponse(response.thread_id);
    } catch (error) {
      throw error;
    }
  };

  // Send follow-up message
  const sendFollowUpMessage = async (content: string) => {
    if (!threadId) return;

    try {
      // For group chat, always enable collaboration mode and include all agents
      const isGroupChat = activeChat === 'group';
      const effectiveCollaborationMode = isGroupChat || collaborationMode;
      const effectiveSelectedAgents = isGroupChat
        ? AGENT_ROLES.map(role => role.id)
        : (collaborationMode ? selectedAgents : [activeChat]);

      const sendMessageRequest = {
        thread_id: threadId,
        message: content,
        metadata: {
          enable_thinking: enableThinking,
          model: selectedModel,
          selected_agents: effectiveSelectedAgents,
          collaboration_mode: effectiveCollaborationMode,
          is_group_chat: isGroupChat,
          timestamp: new Date().toISOString()
        }
      };

      await sendMessage(sendMessageRequest);
      await streamResponse(threadId);
    } catch (error) {
      throw error;
    }
  };

  // Stream agent response
  const streamResponse = async (threadId: string) => {
    const aiMessageId = `ai-${Date.now()}`;
    const thinkingMessageId = `thinking-${Date.now()}`;

    // In collaboration mode, we might get responses from multiple agents
    // For now, we'll show the response from the active agent, but we could enhance this
    // to show responses from all participating agents
    const respondingAgent = collaborationMode ? 'system' : activeChat;

    // Keep track of accumulated content
    let accumulatedContent = '';
    let messageCreated = false; // Track if we've created the message bubble yet
    let thinkingMessageCreated = false; // Track if we've created a thinking message
    let thinkingMessageRemoved = false; // Track if we've already removed the thinking message

    return new Promise<void>((resolve, reject) => {
      streamAgentWithFormData(threadId, {
        onMessage: (data: any) => {
          // Handle thinking messages
          if (data.type === 'thinking' && data.content) {
            if (!thinkingMessageCreated && !messageCreated && !thinkingMessageRemoved) {
              // Create a thinking message bubble
              const thinkingMessage: Message = {
                id: thinkingMessageId,
                sender: respondingAgent as any,
                content: 'Thinking...',
                timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                role: 'assistant',
                type: 'thinking'
              };

              setMessages(prev => [...prev, thinkingMessage]);
              thinkingMessageCreated = true;
            }
            return; // Don't process thinking content further
          }

          // Handle actual content - check for any content that's not thinking
          if (data.content && data.type !== 'thinking' && data.type !== 'unknown') {
            // Remove thinking message immediately when actual content starts
            if (thinkingMessageCreated && !thinkingMessageRemoved) {
              setMessages(prev => prev.filter(m => m.id !== thinkingMessageId));
              thinkingMessageCreated = false;
              thinkingMessageRemoved = true;
            }
            // Accumulate the content
            accumulatedContent += data.content;

            // Only create the message bubble when we receive the first actual content
            if (!messageCreated) {
              // Set loading to false when we start displaying the message
              setLoading(false);

              const aiMessage: Message = {
                id: aiMessageId,
                sender: respondingAgent as any,
                content: accumulatedContent,
                timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                role: 'assistant',
                type: 'assistant'
              };

              setMessages(prev => [...prev, aiMessage]);
              messageCreated = true;
            } else {
              // Update existing message with accumulated content
              setMessages(prev => {
                const newMessages = [...prev];
                const aiMessageIndex = newMessages.findIndex(m => m.id === aiMessageId);
                if (aiMessageIndex !== -1) {
                  newMessages[aiMessageIndex] = {
                    ...newMessages[aiMessageIndex],
                    content: accumulatedContent
                  };
                }
                return newMessages;
              });
            }
          }

          // Handle completion
          if (data.type === 'done' || data.event === 'done') {
            // Final cleanup: ensure thinking message is removed
            if (thinkingMessageCreated && !thinkingMessageRemoved) {
              setMessages(prev => prev.filter(m => m.id !== thinkingMessageId));
              thinkingMessageCreated = false;
              thinkingMessageRemoved = true;
            }
            resolve();
            return;
          }

          // Handle agent identification in collaboration mode
          if (data.agent_id && collaborationMode) {
            setMessages(prev => {
              const newMessages = [...prev];
              const aiMessageIndex = newMessages.findIndex(m => m.id === aiMessageId);
              if (aiMessageIndex !== -1) {
                // Map agent_id to sender
                const agentSenderMap: Record<string, string> = {
                  'ceo': 'ceo',
                  'developer': 'developer',
                  'marketing': 'marketing',
                  'product': 'product'
                };
                newMessages[aiMessageIndex] = {
                  ...newMessages[aiMessageIndex],
                  sender: agentSenderMap[data.agent_id] as any || 'system'
                };
              }
              return newMessages;
            });
          }
        },
        onError: (error: any) => {
          console.error('Streaming error:', error);
          setError(`Streaming error: ${error.message || 'Unknown error'}`);
          reject(error);
        },
        onComplete: () => {
          // Final cleanup: ensure thinking message is removed
          if (thinkingMessageCreated && !thinkingMessageRemoved) {
            setMessages(prev => prev.filter(m => m.id !== thinkingMessageId));
            thinkingMessageCreated = false;
            thinkingMessageRemoved = true;
          }

          // In collaboration mode, fetch any additional messages that might have been created
          // by other agents during the conversation
          if (collaborationMode) {
            setTimeout(() => {
              fetchLatestMessages(threadId);
            }, 1000);
          }

          resolve();
        }
      });
    });
  };

  // Fetch latest messages to catch any additional agent responses in collaboration mode
  const fetchLatestMessages = async (threadId: string) => {
    try {
      const messagesData = await getMessages(threadId);
      if (messagesData && Array.isArray(messagesData)) {
        // Convert API messages to our Message format
        const formattedMessages: Message[] = messagesData.map((msg: any) => ({
          id: msg.message_id || msg.id || `msg-${Date.now()}-${Math.random()}`,
          sender: msg.role === 'user' ? 'user' :
                  msg.agent_type === 'ceo' ? 'ceo' :
                  msg.agent_type === 'developer' ? 'developer' :
                  msg.agent_type === 'marketing' ? 'marketing' :
                  msg.agent_type === 'product' ? 'product' : 'system',
          content: msg.content || '',
          timestamp: new Date(msg.created_at || msg.timestamp || Date.now()).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          role: msg.role,
          type: msg.type || msg.role
        }));

        // Only update if we have new messages
        setMessages(prev => {
          const existingIds = new Set(prev.map(m => m.id));
          const newMessages = formattedMessages.filter(m => !existingIds.has(m.id));

          if (newMessages.length > 0) {
            return [...prev, ...newMessages];
          }
          return prev;
        });
      }
    } catch (error) {
      console.error('Error fetching latest messages:', error);
      // Don't show error to user for this background operation
    }
  };

  // Render message
  const renderMessage = (message: Message, isFirst: boolean = false, isGrouped: boolean = false) => {
    const isUser = message.sender === 'user';
    const isThinking = message.type === 'thinking';
    const agentName = message.sender === 'ceo' ? 'Kenard (CEO)' :
                     message.sender === 'developer' ? 'Alex (Developer)' :
                     message.sender === 'marketing' ? 'Chloe (Marketing)' :
                     message.sender === 'product' ? 'Mark (Product)' :
                     message.sender === 'system' ? 'AI Assistant' : 'You';

    // In collaboration mode, show which agent is responding even if it's not the active chat
    const showAgentIndicator = collaborationMode && !isUser && message.sender !== activeChat;

    return (
      <div key={message.id} className={cn(
        "flex gap-3 mb-4",
        isUser ? "flex-row-reverse" : "flex-row"
      )}>
        {isFirst && (
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={message.sender === 'ceo' ? '/roles/kenard.svg' :
                   message.sender === 'developer' ? '/roles/alex.svg' :
                   message.sender === 'marketing' ? '/roles/chloe.svg' :
                   message.sender === 'product' ? '/roles/mark.svg' : '/default-avatar.svg'}
              alt={agentName}
            />
            <AvatarFallback>
              {message.sender === 'ceo' ? 'K' :
               message.sender === 'developer' ? 'A' :
               message.sender === 'marketing' ? 'C' :
               message.sender === 'product' ? 'M' :
               message.sender === 'system' ? 'AI' : 'U'}
            </AvatarFallback>
          </Avatar>
        )}
        {!isFirst && <div className="w-8" />}

        <div className={cn(
          "max-w-[80%] rounded-lg p-3",
          isUser
            ? "bg-primary text-primary-foreground"
            : isThinking
              ? "bg-gray-100 border border-gray-200 text-gray-600"
              : showAgentIndicator
                ? "bg-blue-50 border border-blue-200 text-blue-900"
                : "bg-muted text-muted-foreground"
        )}>
          {isFirst && (
            <div className="font-semibold text-xs mb-1 flex items-center gap-2">
              {agentName}
              {isThinking && (
                <span className="bg-gray-200 text-gray-600 px-2 py-0.5 rounded-full text-xs">
                  Thinking
                </span>
              )}
              {showAgentIndicator && !isThinking && (
                <span className="bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full text-xs">
                  Collaboration
                </span>
              )}
            </div>
          )}
          <div className="prose prose-sm max-w-none [&>*:first-child]:mt-0 [&>*:last-child]:mb-0">
            {isThinking ? (
              <div className="flex items-center justify-center gap-2 text-gray-500 italic min-h-[20px]">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span>Thinking...</span>
              </div>
            ) : message.content ? (
              <ReactMarkdown remarkPlugins={[remarkGfm]}>
                {message.content}
              </ReactMarkdown>
            ) : (
              <div className="text-muted-foreground italic">
                {loading ? 'Thinking...' : 'No content'}
              </div>
            )}
          </div>

          {/* Show timestamp for collaboration messages */}
          {showAgentIndicator && !isThinking && (
            <div className="text-xs text-blue-600 mt-1 opacity-70">
              {message.timestamp}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Group messages by sender and time
  const groupMessagesByTime = (messages: Message[]) => {
    const groups: Message[][] = [];
    let currentGroup: Message[] = [];
    let lastSender = '';
    let lastTime = 0;

    messages.forEach((message) => {
      const messageTime = new Date(message.timestamp).getTime();
      const timeDiff = messageTime - lastTime;
      
      if (message.sender !== lastSender || timeDiff > 300000) { // 5 minutes
        if (currentGroup.length > 0) {
          groups.push(currentGroup);
        }
        currentGroup = [message];
      } else {
        currentGroup.push(message);
      }
      
      lastSender = message.sender;
      lastTime = messageTime;
    });
    
    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }
    
    return groups;
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Main chat area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="border-b border-border p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage
                  src={activeChat === 'ceo' ? '/roles/kenard.svg' :
                       activeChat === 'developer' ? '/roles/alex.svg' :
                       activeChat === 'marketing' ? '/roles/chloe.svg' :
                       activeChat === 'product' ? '/roles/mark.svg' : '/default-avatar.svg'}
                  alt={activeChat === 'ceo' ? 'Kenard (CEO)' :
                       activeChat === 'developer' ? 'Alex (Developer)' :
                       activeChat === 'marketing' ? 'Chloe (Marketing)' :
                       activeChat === 'product' ? 'Mark (Product)' : 'Team Chat'}
                />
                <AvatarFallback>
                  {activeChat === 'ceo' ? 'K' :
                   activeChat === 'developer' ? 'A' :
                   activeChat === 'marketing' ? 'C' :
                   activeChat === 'product' ? 'M' :
                   activeChat === 'group' ? 'T' : 'AI'}
                </AvatarFallback>
              </Avatar>
              <div>
                <h2 className="font-semibold flex items-center gap-2">
                  {activeChat === 'ceo' ? 'Kenard (CEO)' :
                   activeChat === 'developer' ? 'Alex (Developer)' :
                   activeChat === 'marketing' ? 'Chloe (Marketing)' :
                   activeChat === 'product' ? 'Mark (Product)' :
                   activeChat === 'group' ? 'Team Chat' : 'AI Assistant'}
                  {activeChat === 'group' && (
                    <span className="bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full text-xs">
                      Group
                    </span>
                  )}
                  {collaborationMode && activeChat !== 'group' && (
                    <span className="bg-green-100 text-green-700 px-2 py-0.5 rounded-full text-xs">
                      Collaboration
                    </span>
                  )}
                </h2>
                <p className="text-sm text-muted-foreground">
                  {loading ? 'Typing...' :
                   activeChat === 'group' ? `${AGENT_ROLES.length} agents available` :
                   collaborationMode ? 'Collaboration mode active' : 'Online'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon">
                <Phone className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon">
                <Video className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon">
                <Monitor className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4" ref={messagesContainerRef}>
          {messages.length === 0 ? (
            <div className="h-full flex flex-col items-center justify-center text-muted-foreground">
              <p className="text-center mb-2">No messages yet</p>
              <p className="text-center text-sm">
                Your conversation with {activeChat === 'ceo' ? 'Kenard' : 
                                      activeChat === 'developer' ? 'Alex' :
                                      activeChat === 'marketing' ? 'Chloe' :
                                      activeChat === 'product' ? 'Mark' : 'the team'} will appear here
              </p>
            </div>
          ) : (
            groupMessagesByTime(messages).map((group, groupIndex) => (
              <div key={`group-${groupIndex}`}>
                {group.map((message, messageIndex) => 
                  renderMessage(message, messageIndex === 0, group.length > 1)
                )}
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Error display */}
        {error && (
          <div className="p-4 bg-destructive/10 border-t border-destructive/20">
            <p className="text-destructive text-sm">{error}</p>
          </div>
        )}

        {/* Input */}
        <div className="border-t border-border p-4">
          <form onSubmit={handleSendMessage} className="flex gap-2">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder={`Message ${activeChat === 'ceo' ? 'Kenard' :
                                    activeChat === 'developer' ? 'Alex' :
                                    activeChat === 'marketing' ? 'Chloe' :
                                    activeChat === 'product' ? 'Mark' :
                                    activeChat === 'group' ? 'the team' : 'AI assistant'}...`}
              disabled={loading}
              className="flex-1"
            />
            <Button type="submit" disabled={loading || !inputValue.trim()}>
              <Send className="h-4 w-4" />
            </Button>
          </form>
        </div>
      </div>

      {/* Sidebar for agent selection */}
      <div className="w-80 border-l border-border bg-muted/30">
        <div className="p-4">
          <h3 className="font-semibold mb-4">Chat with Agents</h3>
          
          {/* Agent selection */}
          <div className="space-y-2 mb-6">
            {/* Group chat option */}
            <button
              onClick={() => {
                setActiveChat('group');
                setCollaborationMode(true);
              }}
              className={cn(
                "w-full p-3 rounded-lg border text-left transition-all",
                activeChat === 'group'
                  ? 'bg-primary text-primary-foreground border-primary'
                  : 'bg-background border-border hover:bg-muted'
              )}
            >
              <div className="font-medium flex items-center gap-2">
                <Users className="h-4 w-4" />
                Team Chat
              </div>
              <div className="text-xs opacity-70">Chat with all agents together</div>
            </button>

            {/* Individual agent chats */}
            {AGENT_ROLES.map(role => (
              <button
                key={role.id}
                onClick={() => {
                  setActiveChat(role.id.toLowerCase() as any);
                  if (activeChat === 'group') {
                    setCollaborationMode(false);
                  }
                }}
                className={cn(
                  "w-full p-3 rounded-lg border text-left transition-all",
                  activeChat === role.id.toLowerCase()
                    ? 'bg-primary text-primary-foreground border-primary'
                    : 'bg-background border-border hover:bg-muted'
                )}
              >
                <div className="font-medium">{role.name}</div>
                <div className="text-xs opacity-70">{role.description}</div>
              </button>
            ))}
          </div>

          {/* Collaboration mode toggle */}
          <div className="mb-4">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={collaborationMode || activeChat === 'group'}
                onChange={() => setCollaborationMode(!collaborationMode)}
                className="h-4 w-4"
                disabled={loading || activeChat === 'group'}
              />
              <span className="text-sm font-medium">Collaboration Mode</span>
            </label>
            <p className="text-xs text-muted-foreground mt-1">
              {activeChat === 'group'
                ? "Group chat automatically enables collaboration mode"
                : collaborationMode
                  ? "Multiple agents can participate in the conversation"
                  : "Only the selected agent will respond"}
            </p>
          </div>

          {/* Model selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">AI Model:</label>
            <select
              value={selectedModel}
              onChange={(e) => setSelectedModel(e.target.value)}
              className="w-full p-2 border rounded text-sm"
              disabled={loading}
            >
              {AI_MODELS.map(model => (
                <option key={model.id} value={model.id}>
                  {model.name}
                </option>
              ))}
            </select>
          </div>

          {/* Thinking toggle */}
          <div className="mb-4">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={enableThinking}
                onChange={() => setEnableThinking(!enableThinking)}
                className="h-4 w-4"
                disabled={loading}
              />
              <span className="text-sm font-medium">Enable Thinking</span>
            </label>
          </div>

          {/* Connection status */}
          {threadId && (
            <div className="text-xs text-muted-foreground">
              <p>Thread ID: {threadId.slice(0, 8)}...</p>
              {agentRunId && <p>Run ID: {agentRunId.slice(0, 8)}...</p>}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
