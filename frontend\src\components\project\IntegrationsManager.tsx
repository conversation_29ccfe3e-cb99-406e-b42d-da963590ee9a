"use client";

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { toast } from 'sonner'
import { PlusCircle, Trash2, RefreshCw, Check, X, AlertCircle } from 'lucide-react'
import { getAccountIntegrations, getAvailableIntegrations, createIntegration, deleteIntegration, executeIntegrationAction } from '@/lib/api'

import { Integration, AvailableIntegration } from '@/lib/api'

interface IntegrationsManagerProps {
  projectId: string;
}

export default function IntegrationsManager({ projectId }: IntegrationsManagerProps) {
  const [integrations, setIntegrations] = useState<Integration[]>([])
  const [availableIntegrations, setAvailableIntegrations] = useState<AvailableIntegration[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedIntegrationType, setSelectedIntegrationType] = useState('')
  const [integrationConfig, setIntegrationConfig] = useState<Record<string, any>>({})
  const [integrationMode, setIntegrationMode] = useState('company')
  const [addingIntegration, setAddingIntegration] = useState(false)
  const [testingIntegration, setTestingIntegration] = useState(false)

  useEffect(() => {
    fetchIntegrations()
    fetchAvailableIntegrations()
  }, [projectId])

  const fetchIntegrations = async () => {
    try {
      setLoading(true)
      const response = await getAccountIntegrations(projectId)
      setIntegrations(response.integrations || [])
    } catch (error) {
      console.error('Error fetching integrations:', error)
      toast.error('Failed to load integrations')
    } finally {
      setLoading(false)
    }
  }

  const fetchAvailableIntegrations = async () => {
    try {
      const response = await getAvailableIntegrations()
      setAvailableIntegrations(response.integrations || [])
    } catch (error) {
      console.error('Error fetching available integrations:', error)
      toast.error('Failed to load available integrations')
    }
  }

  const handleAddIntegration = async () => {
    if (!selectedIntegrationType) {
      toast.error('Please select an integration type')
      return
    }

    try {
      setAddingIntegration(true)
      const response = await createIntegration(projectId, selectedIntegrationType, {
        ...integrationConfig,
        integration_mode: integrationMode
      })

      if (response.id) {
        toast.success('Integration added successfully')
        fetchIntegrations()
        setSelectedIntegrationType('')
        setIntegrationConfig({})
        setIntegrationMode('company')
      }
    } catch (error) {
      console.error('Error adding integration:', error)
      toast.error('Failed to add integration')
    } finally {
      setAddingIntegration(false)
    }
  }

  const handleDeleteIntegration = async (integrationId: string) => {
    if (!confirm('Are you sure you want to delete this integration?')) {
      return
    }

    try {
      await deleteIntegration(projectId, integrationId)
      toast.success('Integration deleted successfully')
      fetchIntegrations()
    } catch (error) {
      console.error('Error deleting integration:', error)
      toast.error('Failed to delete integration')
    }
  }

  const handleTestIntegration = async (integrationId: string, integrationType: string) => {
    try {
      setTestingIntegration(true)
      
      // Different test actions based on integration type
      let action = ''
      let params = {}
      
      if (integrationType === 'github') {
        action = 'list_repositories'
        params = { per_page: 5 }
      } else if (integrationType === 'gmail') {
        action = 'list_labels'
        params = {}
      } else if (integrationType === 'slack') {
        action = 'list_channels'
        params = {}
      } else if (integrationType === 'hubspot') {
        action = 'list_contacts'
        params = { limit: 5 }
      } else {
        // Default test action
        action = 'get_status'
        params = {}
      }
      
      const response = await executeIntegrationAction(projectId, integrationId, action, params)
      
      if (response.success) {
        toast.success('Integration test successful')
      } else {
        toast.error(`Integration test failed: ${response.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('Error testing integration:', error)
      toast.error('Failed to test integration')
    } finally {
      setTestingIntegration(false)
    }
  }

  const renderConfigFields = () => {
    if (!selectedIntegrationType) return null

    switch (selectedIntegrationType) {
      case 'github':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="access_token">GitHub Access Token</Label>
              <Input
                id="access_token"
                type="password"
                value={integrationConfig.access_token || ''}
                onChange={(e) => setIntegrationConfig({ ...integrationConfig, access_token: e.target.value })}
                placeholder="ghp_..."
              />
              <p className="text-sm text-muted-foreground">
                Create a personal access token with repo and user scopes at{' '}
                <a href="https://github.com/settings/tokens" target="_blank" rel="noopener noreferrer" className="underline">
                  GitHub Settings
                </a>
              </p>
            </div>
          </div>
        )
      
      case 'gmail':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="client_id">Google Client ID</Label>
              <Input
                id="client_id"
                value={integrationConfig.client_id || ''}
                onChange={(e) => setIntegrationConfig({ ...integrationConfig, client_id: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="client_secret">Google Client Secret</Label>
              <Input
                id="client_secret"
                type="password"
                value={integrationConfig.client_secret || ''}
                onChange={(e) => setIntegrationConfig({ ...integrationConfig, client_secret: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="refresh_token">Refresh Token</Label>
              <Input
                id="refresh_token"
                type="password"
                value={integrationConfig.refresh_token || ''}
                onChange={(e) => setIntegrationConfig({ ...integrationConfig, refresh_token: e.target.value })}
              />
            </div>
          </div>
        )
      
      case 'slack':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="bot_token">Slack Bot Token</Label>
              <Input
                id="bot_token"
                type="password"
                value={integrationConfig.bot_token || ''}
                onChange={(e) => setIntegrationConfig({ ...integrationConfig, bot_token: e.target.value })}
                placeholder="xoxb-..."
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="signing_secret">Signing Secret</Label>
              <Input
                id="signing_secret"
                type="password"
                value={integrationConfig.signing_secret || ''}
                onChange={(e) => setIntegrationConfig({ ...integrationConfig, signing_secret: e.target.value })}
              />
            </div>
          </div>
        )
      
      case 'hubspot':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="api_key">HubSpot API Key</Label>
              <Input
                id="api_key"
                type="password"
                value={integrationConfig.api_key || ''}
                onChange={(e) => setIntegrationConfig({ ...integrationConfig, api_key: e.target.value })}
              />
            </div>
          </div>
        )
      
      default:
        return (
          <div className="py-4 text-center text-muted-foreground">
            Please select an integration type to configure
          </div>
        )
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>
      case 'error':
        return <Badge className="bg-red-500">Error</Badge>
      case 'pending':
        return <Badge className="bg-yellow-500">Pending</Badge>
      default:
        return <Badge className="bg-gray-500">Inactive</Badge>
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Integrations</CardTitle>
        <CardDescription>
          Connect your AI agents with external services to enhance their capabilities
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="current">
          <TabsList>
            <TabsTrigger value="current">Current Integrations</TabsTrigger>
            <TabsTrigger value="add">Add Integration</TabsTrigger>
          </TabsList>
          
          <TabsContent value="current" className="space-y-4">
            {loading ? (
              <div className="flex justify-center py-8">
                <RefreshCw className="animate-spin h-8 w-8 text-muted-foreground" />
              </div>
            ) : integrations.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <p>No integrations configured yet</p>
                <p className="text-sm mt-2">Add integrations to enhance your AI agents&apos; capabilities</p>
              </div>
            ) : (
              <div className="space-y-4">
                {integrations.map((integration) => (
                  <Card key={integration.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-center">
                        <div>
                          <CardTitle className="text-lg flex items-center gap-2">
                            {integration.type.charAt(0).toUpperCase() + integration.type.slice(1)}
                            {getStatusBadge(integration.status)}
                          </CardTitle>
                          <CardDescription>
                            {integration.config?.integration_mode === 'company' ? 'Company Account' : 'User Account'}
                          </CardDescription>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTestIntegration(integration.id, integration.type)}
                            disabled={testingIntegration}
                          >
                            Test
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteIntegration(integration.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-3">
                      {integration.config?.last_sync && (
                        <p className="text-sm text-muted-foreground">
                          Last synced: {new Date(integration.config.last_sync).toLocaleString()}
                        </p>
                      )}
                      {integration.config?.last_error && (
                        <div className="mt-2 p-2 bg-red-50 text-red-800 text-sm rounded flex items-start gap-2">
                          <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                          <span>{integration.config.last_error}</span>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="add" className="space-y-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="integration_type">Integration Type</Label>
                <select
                  id="integration_type"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={selectedIntegrationType}
                  onChange={(e) => setSelectedIntegrationType(e.target.value)}
                >
                  <option value="">Select integration type</option>
                  {availableIntegrations.map((integration) => (
                    <option key={integration.name} value={integration.name}>
                      {integration.display_name}
                    </option>
                  ))}
                </select>
              </div>
              
              {selectedIntegrationType && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="integration_mode">Integration Mode</Label>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="integration_mode"
                        checked={integrationMode === 'company'}
                        onCheckedChange={(checked) => setIntegrationMode(checked ? 'company' : 'user')}
                      />
                      <Label htmlFor="integration_mode">
                        {integrationMode === 'company' ? 'Company Account' : 'User Account'}
                      </Label>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {integrationMode === 'company'
                        ? 'Company account integrations are shared across all users in your organization'
                        : 'User account integrations are only accessible to you'}
                    </p>
                  </div>
                  
                  <Separator className="my-4" />
                  
                  {renderConfigFields()}
                </>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      
      {selectedIntegrationType && (
        <CardFooter className="flex justify-end border-t p-4">
          <Button
            onClick={handleAddIntegration}
            disabled={addingIntegration || !selectedIntegrationType}
          >
            {addingIntegration ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Adding...
              </>
            ) : (
              <>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Integration
              </>
            )}
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}
