"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-sass@6.0.2";
exports.ids = ["vendor-chunks/@codemirror+lang-sass@6.0.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-sass@6.0.2/node_modules/@codemirror/lang-sass/dist/index.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-sass@6.0.2/node_modules/@codemirror/lang-sass/dist/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sass: () => (/* binding */ sass),\n/* harmony export */   sassCompletionSource: () => (/* binding */ sassCompletionSource),\n/* harmony export */   sassLanguage: () => (/* binding */ sassLanguage)\n/* harmony export */ });\n/* harmony import */ var _lezer_sass__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/sass */ \"(ssr)/./node_modules/.pnpm/@lezer+sass@1.0.7/node_modules/@lezer/sass/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _codemirror_lang_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/lang-css */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-css@6.3.1/node_modules/@codemirror/lang-css/dist/index.js\");\n\n\n\n\n/**\nA language provider based on the [Lezer Sass\nparser](https://github.com/lezer-parser/sass), extended with\nhighlighting and indentation information.\n*/\nconst sassLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LRLanguage.define({\n    name: \"sass\",\n    parser: /*@__PURE__*/_lezer_sass__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldNodeProp.add({\n                Block: _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldInside,\n                Comment(node, state) {\n                    return { from: node.from + 2, to: state.sliceDoc(node.to - 2, node.to) == \"*/\" ? node.to - 2 : node.to };\n                }\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.indentNodeProp.add({\n                Declaration: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.continuedIndent)()\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"/*\", close: \"*/\" }, line: \"//\" },\n        indentOnInput: /^\\s*\\}$/,\n        wordChars: \"$-\"\n    }\n});\nconst indentedSassLanguage = /*@__PURE__*/sassLanguage.configure({\n    dialect: \"indented\",\n    props: [\n        /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.indentNodeProp.add({\n            \"Block RuleSet\": cx => cx.baseIndent + cx.unit\n        }),\n        /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldNodeProp.add({\n            Block: node => ({ from: node.from, to: node.to })\n        })\n    ]\n});\n/**\nProperty, variable, $-variable, and value keyword completion\nsource.\n*/\nconst sassCompletionSource = /*@__PURE__*/(0,_codemirror_lang_css__WEBPACK_IMPORTED_MODULE_2__.defineCSSCompletionSource)(node => node.name == \"VariableName\" || node.name == \"SassVariableName\");\n/**\nLanguage support for CSS.\n*/\nfunction sass(config) {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LanguageSupport((config === null || config === void 0 ? void 0 : config.indented) ? indentedSassLanguage : sassLanguage, sassLanguage.data.of({ autocomplete: sassCompletionSource }));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-sass@6.0.2/node_modules/@codemirror/lang-sass/dist/index.js\n");

/***/ })

};
;