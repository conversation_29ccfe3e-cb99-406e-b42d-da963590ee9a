"""
Agent Communication Tool for inter-agent communication.

This tool provides functions for sending and receiving messages between agents,
enabling teams of agents to collaborate effectively.
"""

import json
from typing import Dict, List, Any, Optional
from datetime import datetime

from agentpress.tool import Tool, ToolResult, openapi_schema, xml_schema
from agentpress.agent_communication import Agent<PERSON><PERSON><PERSON>Manager, MessageType
from agentpress.communication_patterns import (
    CommunicationPattern, TaskPriority, TaskStatus,
    create_task_delegation_message, create_task_response_message,
    create_information_request_message, create_information_response_message,
    create_status_update_message, create_decision_request_message,
    create_decision_response_message, create_feedback_request_message,
    create_feedback_response_message, create_collaboration_invitation_message,
    create_collaboration_response_message, parse_communication_pattern,
    get_role_specific_communication_guidance
)
from utils.logger import logger


class AgentCommunicationTool(Tool):
    """Tool for inter-agent communication."""

    def __init__(self, agent_id: str, project_id: Optional[str] = None, **kwargs):
        """Initialize the AgentCommunicationTool.

        Args:
            agent_id: ID of the agent using this tool
            project_id: Optional ID of the project this tool is associated with
            **kwargs: Additional arguments
        """
        super().__init__()
        self.agent_id = agent_id
        self.project_id = project_id or agent_id  # Use agent_id as project_id if not provided
        self.communication_manager = AgentCommunicationManager(project_id=self.project_id)
        logger.debug(f"Initialized AgentCommunicationTool for agent {agent_id} in project {self.project_id}")

    @openapi_schema({
        "name": "send_message",
        "description": "Send a message to another agent",
        "parameters": {
            "type": "object",
            "properties": {
                "to_agent_id": {
                    "type": "string",
                    "description": "ID of the agent to send the message to"
                },
                "message_type": {
                    "type": "string",
                    "enum": ["query", "response", "notification", "task", "result", "status", "error"],
                    "description": "Type of message to send"
                },
                "content": {
                    "type": "object",
                    "description": "Content of the message"
                },
                "thread_id": {
                    "type": "string",
                    "description": "Optional thread ID for conversation tracking",
                    "default": None
                },
                "reference_id": {
                    "type": "string",
                    "description": "Optional reference to another message",
                    "default": None
                },
                "metadata": {
                    "type": "object",
                    "description": "Optional additional metadata",
                    "default": {}
                }
            },
            "required": ["to_agent_id", "message_type", "content"]
        }
    })
    @xml_schema(
        tag_name="send-message",
        mappings=[
            {"param_name": "to_agent_id", "node_type": "attribute", "path": "."},
            {"param_name": "message_type", "node_type": "attribute", "path": "."},
            {"param_name": "content", "node_type": "element", "path": "content"},
            {"param_name": "thread_id", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "reference_id", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "metadata", "node_type": "element", "path": "metadata", "required": False}
        ],
        example='''
        <send-message to_agent_id="550e8400-e29b-41d4-a716-446655440000" message_type="query" thread_id="123456">
            <content>{"question": "What is the status of the marketing campaign?", "priority": "high"}</content>
            <metadata>{"source": "weekly_meeting", "context": "planning"}</metadata>
        </send-message>
        '''
    )
    async def send_message(
        self,
        to_agent_id: str,
        message_type: str,
        content: Dict[str, Any],
        thread_id: Optional[str] = None,
        reference_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ToolResult:
        """Send a message to another agent.

        Args:
            to_agent_id: ID of the agent to send the message to
            message_type: Type of message to send
            content: Content of the message
            thread_id: Optional thread ID for conversation tracking
            reference_id: Optional reference to another message
            metadata: Optional additional metadata

        Returns:
            ToolResult with success status and message ID
        """
        try:
            # Convert string type to enum value
            message_type_enum = MessageType(message_type)

            # Send the message
            message_id = await self.communication_manager.send_message(
                from_agent_id=self.agent_id,
                to_agent_id=to_agent_id,
                message_type=message_type_enum,
                content=content,
                thread_id=thread_id,
                reference_id=reference_id,
                metadata=metadata
            )

            return self.success_response(f"Message sent successfully with ID: {message_id}")

        except Exception as e:
            logger.error(f"Error sending message: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to send message: {str(e)}")

    @openapi_schema({
        "name": "get_messages",
        "description": "Get messages for the agent",
        "parameters": {
            "type": "object",
            "properties": {
                "thread_id": {
                    "type": "string",
                    "description": "Optional thread ID to filter by",
                    "default": None
                },
                "unread_only": {
                    "type": "boolean",
                    "description": "Whether to return only unread messages",
                    "default": False
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of messages to return",
                    "default": 100
                },
                "offset": {
                    "type": "integer",
                    "description": "Offset for pagination",
                    "default": 0
                }
            },
            "required": []
        }
    })
    @xml_schema(
        tag_name="get-messages",
        mappings=[
            {"param_name": "thread_id", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "unread_only", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "limit", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "offset", "node_type": "attribute", "path": ".", "required": False}
        ],
        example='''
        <get-messages thread_id="123456" unread_only="true" limit="10" />
        '''
    )
    async def get_messages(
        self,
        thread_id: Optional[str] = None,
        unread_only: bool = False,
        limit: int = 100,
        offset: int = 0
    ) -> ToolResult:
        """Get messages for the agent.

        Args:
            thread_id: Optional thread ID to filter by
            unread_only: Whether to return only unread messages
            limit: Maximum number of messages to return
            offset: Offset for pagination

        Returns:
            ToolResult with success status and messages
        """
        try:
            # Get messages
            messages = await self.communication_manager.get_messages(
                agent_id=self.agent_id,
                thread_id=thread_id,
                unread_only=unread_only,
                limit=limit,
                offset=offset
            )

            if messages:
                return self.success_response(json.dumps(messages, indent=2))
            else:
                return self.success_response("No messages found")

        except Exception as e:
            logger.error(f"Error getting messages: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to get messages: {str(e)}")

    @openapi_schema({
        "name": "mark_as_read",
        "description": "Mark a message as read",
        "parameters": {
            "type": "object",
            "properties": {
                "message_id": {
                    "type": "string",
                    "description": "ID of the message to mark as read"
                }
            },
            "required": ["message_id"]
        }
    })
    @xml_schema(
        tag_name="mark-as-read",
        mappings=[
            {"param_name": "message_id", "node_type": "attribute", "path": "."}
        ],
        example='''
        <mark-as-read message_id="550e8400-e29b-41d4-a716-446655440000" />
        '''
    )
    async def mark_as_read(self, message_id: str) -> ToolResult:
        """Mark a message as read.

        Args:
            message_id: ID of the message to mark as read

        Returns:
            ToolResult with success status
        """
        try:
            # Mark the message as read
            success = await self.communication_manager.mark_as_read(message_id)

            if success:
                return self.success_response(f"Message {message_id} marked as read")
            else:
                return self.fail_response(f"Failed to mark message {message_id} as read")

        except Exception as e:
            logger.error(f"Error marking message as read: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to mark message as read: {str(e)}")

    @openapi_schema({
        "name": "get_agent_capabilities",
        "description": "Get the capabilities of an agent",
        "parameters": {
            "type": "object",
            "properties": {
                "agent_id": {
                    "type": "string",
                    "description": "ID of the agent to get capabilities for"
                }
            },
            "required": ["agent_id"]
        }
    })
    @xml_schema(
        tag_name="get-agent-capabilities",
        mappings=[
            {"param_name": "agent_id", "node_type": "attribute", "path": "."}
        ],
        example='''
        <get-agent-capabilities agent_id="550e8400-e29b-41d4-a716-446655440000" />
        '''
    )
    async def get_agent_capabilities(self, agent_id: str) -> ToolResult:
        """Get the capabilities of an agent.

        Args:
            agent_id: ID of the agent to get capabilities for

        Returns:
            ToolResult with success status and agent capabilities
        """
        try:
            # Get agent capabilities
            capabilities = await self.communication_manager.get_agent_capabilities(agent_id)

            if capabilities:
                return self.success_response(json.dumps(capabilities, indent=2))
            else:
                return self.success_response(f"No capabilities found for agent {agent_id}")

        except Exception as e:
            logger.error(f"Error getting agent capabilities: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to get agent capabilities: {str(e)}")

    @openapi_schema({
        "name": "register_capabilities",
        "description": "Register or update the agent's capabilities",
        "parameters": {
            "type": "object",
            "properties": {
                "capabilities": {
                    "type": "object",
                    "description": "Dictionary of agent capabilities"
                }
            },
            "required": ["capabilities"]
        }
    })
    @xml_schema(
        tag_name="register-capabilities",
        mappings=[
            {"param_name": "capabilities", "node_type": "element", "path": "capabilities"}
        ],
        example='''
        <register-capabilities>
            <capabilities>{"data_analysis": true, "natural_language_processing": true, "image_generation": false}</capabilities>
        </register-capabilities>
        '''
    )
    async def register_capabilities(self, capabilities: Dict[str, Any]) -> ToolResult:
        """Register or update the agent's capabilities.

        Args:
            capabilities: Dictionary of agent capabilities

        Returns:
            ToolResult with success status
        """
        try:
            # Register agent capabilities
            success = await self.communication_manager.register_agent_capabilities(
                agent_id=self.agent_id,
                capabilities=capabilities
            )

            if success:
                return self.success_response("Agent capabilities registered successfully")
            else:
                return self.fail_response("Failed to register agent capabilities")

        except Exception as e:
            logger.error(f"Error registering agent capabilities: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to register agent capabilities: {str(e)}")

    @openapi_schema({
        "name": "find_agents_by_capability",
        "description": "Find agents that have a specific capability",
        "parameters": {
            "type": "object",
            "properties": {
                "capability": {
                    "type": "string",
                    "description": "Capability to search for"
                }
            },
            "required": ["capability"]
        }
    })
    @xml_schema(
        tag_name="find-agents-by-capability",
        mappings=[
            {"param_name": "capability", "node_type": "attribute", "path": "."}
        ],
        example='''
        <find-agents-by-capability capability="data_analysis" />
        '''
    )
    async def find_agents_by_capability(self, capability: str) -> ToolResult:
        """Find agents that have a specific capability.

        Args:
            capability: Capability to search for

        Returns:
            ToolResult with success status and list of agent IDs
        """
        try:
            # Find agents by capability
            agents = await self.communication_manager.find_agents_by_capability(capability)

            if agents:
                return self.success_response(json.dumps(agents, indent=2))
            else:
                return self.success_response(f"No agents found with capability '{capability}'")

        except Exception as e:
            logger.error(f"Error finding agents by capability: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to find agents by capability: {str(e)}")

    @openapi_schema({
        "name": "get_project_agents",
        "description": "Get the list of agents available for the current project",
        "parameters": {
            "type": "object",
            "properties": {},
            "required": []
        }
    })
    @xml_schema(
        tag_name="get-project-agents",
        mappings=[],
        example='''
        <get-project-agents />
        '''
    )
    async def get_project_agents(self) -> ToolResult:
        """Get the list of agents available for the current project.

        Returns:
            ToolResult with success status and list of agents
        """
        try:
            # Get project agents
            agents = await self.communication_manager.get_project_agents(self.project_id)

            if agents:
                # Format the response to be more user-friendly
                formatted_agents = []
                for agent in agents:
                    formatted_agent = {
                        "name": agent.get("name"),
                        "role": agent.get("role"),
                        "description": agent.get("description"),
                        "agent_id": agent.get("agent_id"),
                        "capabilities": agent.get("capabilities", {})
                    }
                    formatted_agents.append(formatted_agent)

                return self.success_response(json.dumps(formatted_agents, indent=2))
            else:
                return self.success_response("No agents available for this project")

        except Exception as e:
            logger.error(f"Error getting project agents: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to get project agents: {str(e)}")

    @openapi_schema({
        "name": "can_communicate_with",
        "description": "Check if the current agent can communicate with another agent",
        "parameters": {
            "type": "object",
            "properties": {
                "agent_id": {
                    "type": "string",
                    "description": "ID of the agent to check communication with"
                }
            },
            "required": ["agent_id"]
        }
    })
    @xml_schema(
        tag_name="can-communicate-with",
        mappings=[
            {"param_name": "agent_id", "node_type": "attribute", "path": "."}
        ],
        example='''
        <can-communicate-with agent_id="550e8400-e29b-41d4-a716-446655440000" />
        '''
    )
    async def can_communicate_with(self, agent_id: str) -> ToolResult:
        """Check if the current agent can communicate with another agent.

        Args:
            agent_id: ID of the agent to check communication with

        Returns:
            ToolResult with success status and whether communication is possible
        """
        try:
            # Check if agents can communicate
            can_communicate = await self.communication_manager.can_agents_communicate(
                from_agent_id=self.agent_id,
                to_agent_id=agent_id
            )

            if can_communicate:
                return self.success_response("Yes, communication is possible with this agent")
            else:
                return self.success_response("No, communication is not possible with this agent - it may not be active in the same project")

        except Exception as e:
            logger.error(f"Error checking if agents can communicate: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to check if agents can communicate: {str(e)}")

    @openapi_schema({
        "name": "get_communication_guidance",
        "description": "Get role-specific communication guidance",
        "parameters": {
            "type": "object",
            "properties": {
                "role": {
                    "type": "string",
                    "description": "Role to get communication guidance for (e.g., CEO, Developer, Marketing)"
                }
            },
            "required": ["role"]
        }
    })
    @xml_schema(
        tag_name="get-communication-guidance",
        mappings=[
            {"param_name": "role", "node_type": "attribute", "path": "."}
        ],
        example='''
        <get-communication-guidance role="CEO" />
        '''
    )
    async def get_communication_guidance(self, role: str) -> ToolResult:
        """Get role-specific communication guidance.

        Args:
            role: Role to get communication guidance for

        Returns:
            ToolResult with success status and communication guidance
        """
        try:
            # Get role-specific communication guidance
            guidance = get_role_specific_communication_guidance(role)

            if guidance:
                return self.success_response(json.dumps(guidance, indent=2))
            else:
                return self.success_response("No communication guidance found for this role")

        except Exception as e:
            logger.error(f"Error getting communication guidance: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to get communication guidance: {str(e)}")

    @openapi_schema({
        "name": "delegate_task",
        "description": "Delegate a task to another agent",
        "parameters": {
            "type": "object",
            "properties": {
                "to_agent_id": {
                    "type": "string",
                    "description": "ID of the agent to delegate the task to"
                },
                "task_title": {
                    "type": "string",
                    "description": "Brief title for the task"
                },
                "task_description": {
                    "type": "string",
                    "description": "Detailed description of the task"
                },
                "priority": {
                    "type": "string",
                    "enum": ["low", "medium", "high", "urgent"],
                    "description": "Priority level for the task",
                    "default": "medium"
                },
                "due_date": {
                    "type": "string",
                    "description": "Optional due date in ISO format (e.g., 2023-12-31T23:59:59Z)",
                    "default": None
                },
                "context": {
                    "type": "object",
                    "description": "Optional additional context for the task",
                    "default": {}
                },
                "deliverables": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "Optional list of expected deliverables",
                    "default": []
                }
            },
            "required": ["to_agent_id", "task_title", "task_description"]
        }
    })
    @xml_schema(
        tag_name="delegate-task",
        mappings=[
            {"param_name": "to_agent_id", "node_type": "attribute", "path": "."},
            {"param_name": "task_title", "node_type": "element", "path": "task-title"},
            {"param_name": "task_description", "node_type": "element", "path": "task-description"},
            {"param_name": "priority", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "due_date", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "context", "node_type": "element", "path": "context", "required": False},
            {"param_name": "deliverables", "node_type": "element", "path": "deliverables", "required": False}
        ],
        example='''
        <delegate-task to_agent_id="550e8400-e29b-41d4-a716-446655440000" priority="high" due_date="2023-12-31T23:59:59Z">
            <task-title>Create marketing campaign for new product</task-title>
            <task-description>We need a comprehensive marketing campaign for our new product launch. This should include social media, email, and content marketing.</task-description>
            <context>{"product_name": "SuperWidget", "launch_date": "2024-01-15"}</context>
            <deliverables>["Marketing plan", "Social media calendar", "Email templates"]</deliverables>
        </delegate-task>
        '''
    )
    async def delegate_task(
        self,
        to_agent_id: str,
        task_title: str,
        task_description: str,
        priority: str = "medium",
        due_date: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        deliverables: Optional[List[str]] = None
    ) -> ToolResult:
        """Delegate a task to another agent.

        Args:
            to_agent_id: ID of the agent to delegate the task to
            task_title: Brief title for the task
            task_description: Detailed description of the task
            priority: Priority level for the task
            due_date: Optional due date in ISO format
            context: Optional additional context for the task
            deliverables: Optional list of expected deliverables

        Returns:
            ToolResult with success status and message ID
        """
        try:
            # Check if agents can communicate
            can_communicate = await self.communication_manager.can_agents_communicate(
                from_agent_id=self.agent_id,
                to_agent_id=to_agent_id
            )

            if not can_communicate:
                return self.fail_response(f"Cannot delegate task to agent {to_agent_id} - it may not be active in the same project")

            # Create task delegation message
            task_content = create_task_delegation_message(
                task_title=task_title,
                task_description=task_description,
                priority=TaskPriority(priority),
                due_date=due_date,
                context=context or {},
                deliverables=deliverables or []
            )

            # Send the message
            message_id = await self.communication_manager.send_message(
                from_agent_id=self.agent_id,
                to_agent_id=to_agent_id,
                message_type=MessageType.TASK,
                content=task_content,
                metadata={"task_title": task_title}
            )

            return self.success_response(f"Task '{task_title}' delegated successfully to agent {to_agent_id} with message ID: {message_id}")

        except Exception as e:
            logger.error(f"Error delegating task: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to delegate task: {str(e)}")

    @openapi_schema({
        "name": "respond_to_task",
        "description": "Respond to a delegated task",
        "parameters": {
            "type": "object",
            "properties": {
                "to_agent_id": {
                    "type": "string",
                    "description": "ID of the agent who delegated the task"
                },
                "task_id": {
                    "type": "string",
                    "description": "ID of the task being responded to"
                },
                "status": {
                    "type": "string",
                    "enum": ["accepted", "in_progress", "completed", "rejected", "blocked", "cancelled"],
                    "description": "New status for the task"
                },
                "message": {
                    "type": "string",
                    "description": "Message explaining the response"
                },
                "estimated_completion": {
                    "type": "string",
                    "description": "Optional estimated completion date in ISO format",
                    "default": None
                },
                "blockers": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "Optional list of blockers if the task is blocked",
                    "default": []
                }
            },
            "required": ["to_agent_id", "task_id", "status", "message"]
        }
    })
    @xml_schema(
        tag_name="respond-to-task",
        mappings=[
            {"param_name": "to_agent_id", "node_type": "attribute", "path": "."},
            {"param_name": "task_id", "node_type": "attribute", "path": "."},
            {"param_name": "status", "node_type": "attribute", "path": "."},
            {"param_name": "message", "node_type": "element", "path": "message"},
            {"param_name": "estimated_completion", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "blockers", "node_type": "element", "path": "blockers", "required": False}
        ],
        example='''
        <respond-to-task to_agent_id="550e8400-e29b-41d4-a716-446655440000" task_id="task-123" status="accepted" estimated_completion="2023-12-25T23:59:59Z">
            <message>I'll start working on the marketing campaign right away. I'll have a draft plan ready by next week.</message>
            <blockers>["Need product specifications", "Need budget approval"]</blockers>
        </respond-to-task>
        '''
    )
    async def respond_to_task(
        self,
        to_agent_id: str,
        task_id: str,
        status: str,
        message: str,
        estimated_completion: Optional[str] = None,
        blockers: Optional[List[str]] = None
    ) -> ToolResult:
        """Respond to a delegated task.

        Args:
            to_agent_id: ID of the agent who delegated the task
            task_id: ID of the task being responded to
            status: New status for the task
            message: Message explaining the response
            estimated_completion: Optional estimated completion date in ISO format
            blockers: Optional list of blockers if the task is blocked

        Returns:
            ToolResult with success status and message ID
        """
        try:
            # Check if agents can communicate
            can_communicate = await self.communication_manager.can_agents_communicate(
                from_agent_id=self.agent_id,
                to_agent_id=to_agent_id
            )

            if not can_communicate:
                return self.fail_response(f"Cannot respond to task for agent {to_agent_id} - it may not be active in the same project")

            # Create task response message
            task_content = create_task_response_message(
                task_id=task_id,
                status=TaskStatus(status),
                message=message,
                estimated_completion=estimated_completion,
                blockers=blockers or []
            )

            # Send the message
            message_id = await self.communication_manager.send_message(
                from_agent_id=self.agent_id,
                to_agent_id=to_agent_id,
                message_type=MessageType.RESULT,
                content=task_content,
                metadata={"task_id": task_id, "status": status}
            )

            return self.success_response(f"Task response sent successfully to agent {to_agent_id} with message ID: {message_id}")

        except Exception as e:
            logger.error(f"Error responding to task: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to respond to task: {str(e)}")

    @openapi_schema({
        "name": "request_information",
        "description": "Request information from another agent",
        "parameters": {
            "type": "object",
            "properties": {
                "to_agent_id": {
                    "type": "string",
                    "description": "ID of the agent to request information from"
                },
                "question": {
                    "type": "string",
                    "description": "The question or information being requested"
                },
                "context": {
                    "type": "object",
                    "description": "Optional additional context for the request",
                    "default": {}
                },
                "priority": {
                    "type": "string",
                    "enum": ["low", "medium", "high", "urgent"],
                    "description": "Priority level for the request",
                    "default": "medium"
                }
            },
            "required": ["to_agent_id", "question"]
        }
    })
    @xml_schema(
        tag_name="request-information",
        mappings=[
            {"param_name": "to_agent_id", "node_type": "attribute", "path": "."},
            {"param_name": "question", "node_type": "element", "path": "question"},
            {"param_name": "context", "node_type": "element", "path": "context", "required": False},
            {"param_name": "priority", "node_type": "attribute", "path": ".", "required": False}
        ],
        example='''
        <request-information to_agent_id="550e8400-e29b-41d4-a716-446655440000" priority="high">
            <question>What are the key features of our new product?</question>
            <context>{"purpose": "marketing campaign planning"}</context>
        </request-information>
        '''
    )
    async def request_information(
        self,
        to_agent_id: str,
        question: str,
        context: Optional[Dict[str, Any]] = None,
        priority: str = "medium"
    ) -> ToolResult:
        """Request information from another agent.

        Args:
            to_agent_id: ID of the agent to request information from
            question: The question or information being requested
            context: Optional additional context for the request
            priority: Priority level for the request

        Returns:
            ToolResult with success status and message ID
        """
        try:
            # Check if agents can communicate
            can_communicate = await self.communication_manager.can_agents_communicate(
                from_agent_id=self.agent_id,
                to_agent_id=to_agent_id
            )

            if not can_communicate:
                return self.fail_response(f"Cannot request information from agent {to_agent_id} - it may not be active in the same project")

            # Create information request message
            request_content = create_information_request_message(
                question=question,
                context=context or {},
                priority=TaskPriority(priority)
            )

            # Send the message
            message_id = await self.communication_manager.send_message(
                from_agent_id=self.agent_id,
                to_agent_id=to_agent_id,
                message_type=MessageType.QUERY,
                content=request_content,
                metadata={"question": question[:50] + "..." if len(question) > 50 else question}
            )

            return self.success_response(f"Information request sent successfully to agent {to_agent_id} with message ID: {message_id}")

        except Exception as e:
            logger.error(f"Error requesting information: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to request information: {str(e)}")

    @openapi_schema({
        "name": "provide_information",
        "description": "Provide information in response to a request",
        "parameters": {
            "type": "object",
            "properties": {
                "to_agent_id": {
                    "type": "string",
                    "description": "ID of the agent who requested the information"
                },
                "request_id": {
                    "type": "string",
                    "description": "ID of the request being responded to"
                },
                "answer": {
                    "type": "string",
                    "description": "The answer to the question"
                },
                "sources": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "Optional list of sources for the information",
                    "default": []
                },
                "additional_context": {
                    "type": "object",
                    "description": "Optional additional context for the answer",
                    "default": {}
                }
            },
            "required": ["to_agent_id", "request_id", "answer"]
        }
    })
    @xml_schema(
        tag_name="provide-information",
        mappings=[
            {"param_name": "to_agent_id", "node_type": "attribute", "path": "."},
            {"param_name": "request_id", "node_type": "attribute", "path": "."},
            {"param_name": "answer", "node_type": "element", "path": "answer"},
            {"param_name": "sources", "node_type": "element", "path": "sources", "required": False},
            {"param_name": "additional_context", "node_type": "element", "path": "additional-context", "required": False}
        ],
        example='''
        <provide-information to_agent_id="550e8400-e29b-41d4-a716-446655440000" request_id="request-123">
            <answer>The key features of our new product include AI-powered recommendations, real-time analytics, and seamless integration with existing systems.</answer>
            <sources>["Product specification document", "Engineering team briefing"]</sources>
            <additional-context>{"release_date": "2024-01-15", "target_audience": "enterprise customers"}</additional-context>
        </provide-information>
        '''
    )
    async def provide_information(
        self,
        to_agent_id: str,
        request_id: str,
        answer: str,
        sources: Optional[List[str]] = None,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> ToolResult:
        """Provide information in response to a request.

        Args:
            to_agent_id: ID of the agent who requested the information
            request_id: ID of the request being responded to
            answer: The answer to the question
            sources: Optional list of sources for the information
            additional_context: Optional additional context for the answer

        Returns:
            ToolResult with success status and message ID
        """
        try:
            # Check if agents can communicate
            can_communicate = await self.communication_manager.can_agents_communicate(
                from_agent_id=self.agent_id,
                to_agent_id=to_agent_id
            )

            if not can_communicate:
                return self.fail_response(f"Cannot provide information to agent {to_agent_id} - it may not be active in the same project")

            # Create information response message
            response_content = create_information_response_message(
                request_id=request_id,
                answer=answer,
                sources=sources or [],
                additional_context=additional_context or {}
            )

            # Send the message
            message_id = await self.communication_manager.send_message(
                from_agent_id=self.agent_id,
                to_agent_id=to_agent_id,
                message_type=MessageType.RESPONSE,
                content=response_content,
                metadata={"request_id": request_id}
            )

            return self.success_response(f"Information provided successfully to agent {to_agent_id} with message ID: {message_id}")

        except Exception as e:
            logger.error(f"Error providing information: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to provide information: {str(e)}")

    @openapi_schema({
        "name": "get_conversation_history",
        "description": "Get the conversation history between the current agent and another agent",
        "parameters": {
            "type": "object",
            "properties": {
                "other_agent_id": {
                    "type": "string",
                    "description": "ID of the other agent"
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of messages to return",
                    "default": 20
                },
                "offset": {
                    "type": "integer",
                    "description": "Offset for pagination",
                    "default": 0
                }
            },
            "required": ["other_agent_id"]
        }
    })
    @xml_schema(
        tag_name="get-conversation-history",
        mappings=[
            {"param_name": "other_agent_id", "node_type": "attribute", "path": "."},
            {"param_name": "limit", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "offset", "node_type": "attribute", "path": ".", "required": False}
        ],
        example='''
        <get-conversation-history other_agent_id="550e8400-e29b-41d4-a716-446655440000" limit="10" offset="0" />
        '''
    )
    async def get_conversation_history(
        self,
        other_agent_id: str,
        limit: int = 20,
        offset: int = 0
    ) -> ToolResult:
        """Get the conversation history between the current agent and another agent.

        Args:
            other_agent_id: ID of the other agent
            limit: Maximum number of messages to return
            offset: Offset for pagination

        Returns:
            ToolResult with success status and conversation history
        """
        try:
            # Check if agents can communicate
            can_communicate = await self.communication_manager.can_agents_communicate(
                from_agent_id=self.agent_id,
                to_agent_id=other_agent_id
            )

            if not can_communicate:
                return self.fail_response(f"Cannot get conversation history with agent {other_agent_id} - it may not be active in the same project")

            # Get conversation history
            history = await self.communication_manager.get_conversation_history(
                agent_id=self.agent_id,
                other_agent_id=other_agent_id,
                limit=limit,
                offset=offset
            )

            if history:
                # Format the history for better readability
                formatted_history = []
                for message in history:
                    formatted_message = {
                        "message_id": message.get("message_id"),
                        "from_agent_id": message.get("from_agent_id"),
                        "to_agent_id": message.get("to_agent_id"),
                        "message_type": message.get("message_type"),
                        "content": message.get("content"),
                        "created_at": message.get("created_at")
                    }
                    formatted_history.append(formatted_message)

                return self.success_response(json.dumps(formatted_history, indent=2))
            else:
                return self.success_response("No conversation history found with this agent")

        except Exception as e:
            logger.error(f"Error getting conversation history: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to get conversation history: {str(e)}")

    @openapi_schema({
        "name": "get_task_status",
        "description": "Get the status of a task",
        "parameters": {
            "type": "object",
            "properties": {
                "task_id": {
                    "type": "string",
                    "description": "ID of the task to get status for"
                }
            },
            "required": ["task_id"]
        }
    })
    @xml_schema(
        tag_name="get-task-status",
        mappings=[
            {"param_name": "task_id", "node_type": "attribute", "path": "."}
        ],
        example='''
        <get-task-status task_id="task-123" />
        '''
    )
    async def get_task_status(self, task_id: str) -> ToolResult:
        """Get the status of a task.

        Args:
            task_id: ID of the task to get status for

        Returns:
            ToolResult with success status and task status information
        """
        try:
            # Get task status
            status = await self.communication_manager.get_task_status(task_id)

            if status:
                return self.success_response(json.dumps(status, indent=2))
            else:
                return self.success_response(f"No task found with ID {task_id}")

        except Exception as e:
            logger.error(f"Error getting task status: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to get task status: {str(e)}")

    @openapi_schema({
        "name": "get_agent_tasks",
        "description": "Get tasks assigned to the current agent",
        "parameters": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "string",
                    "description": "Optional status to filter by",
                    "default": None
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of tasks to return",
                    "default": 20
                },
                "offset": {
                    "type": "integer",
                    "description": "Offset for pagination",
                    "default": 0
                }
            },
            "required": []
        }
    })
    @xml_schema(
        tag_name="get-agent-tasks",
        mappings=[
            {"param_name": "status", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "limit", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "offset", "node_type": "attribute", "path": ".", "required": False}
        ],
        example='''
        <get-agent-tasks status="pending" limit="10" offset="0" />
        '''
    )
    async def get_agent_tasks(
        self,
        status: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> ToolResult:
        """Get tasks assigned to the current agent.

        Args:
            status: Optional status to filter by
            limit: Maximum number of tasks to return
            offset: Offset for pagination

        Returns:
            ToolResult with success status and list of tasks
        """
        try:
            # Get agent tasks
            tasks = await self.communication_manager.get_agent_tasks(
                agent_id=self.agent_id,
                status=status,
                limit=limit,
                offset=offset
            )

            if tasks:
                return self.success_response(json.dumps(tasks, indent=2))
            else:
                return self.success_response("No tasks found for this agent")

        except Exception as e:
            logger.error(f"Error getting agent tasks: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to get agent tasks: {str(e)}")

    @openapi_schema({
        "name": "get_agent_info",
        "description": "Get information about an agent",
        "parameters": {
            "type": "object",
            "properties": {
                "agent_id": {
                    "type": "string",
                    "description": "ID of the agent to get information for"
                }
            },
            "required": ["agent_id"]
        }
    })
    @xml_schema(
        tag_name="get-agent-info",
        mappings=[
            {"param_name": "agent_id", "node_type": "attribute", "path": "."}
        ],
        example='''
        <get-agent-info agent_id="550e8400-e29b-41d4-a716-446655440000" />
        '''
    )
    async def get_agent_info(self, agent_id: str) -> ToolResult:
        """Get information about an agent.

        Args:
            agent_id: ID of the agent to get information for

        Returns:
            ToolResult with success status and agent information
        """
        try:
            # Get agent information
            info = await self.communication_manager.get_agent_info(agent_id)

            if info:
                return self.success_response(json.dumps(info, indent=2))
            else:
                return self.success_response(f"No agent found with ID {agent_id}")

        except Exception as e:
            logger.error(f"Error getting agent info: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to get agent info: {str(e)}")
