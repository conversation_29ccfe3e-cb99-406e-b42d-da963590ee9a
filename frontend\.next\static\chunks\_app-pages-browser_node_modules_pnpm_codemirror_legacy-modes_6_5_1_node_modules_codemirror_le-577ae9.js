"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_codemirror_legacy-modes_6_5_1_node_modules_codemirror_le-577ae9"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/css.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/css.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   css: () => (/* binding */ css),\n/* harmony export */   gss: () => (/* binding */ gss),\n/* harmony export */   keywords: () => (/* binding */ keywords),\n/* harmony export */   less: () => (/* binding */ less),\n/* harmony export */   mkCSS: () => (/* binding */ mkCSS),\n/* harmony export */   sCSS: () => (/* binding */ sCSS)\n/* harmony export */ });\nfunction mkCSS(parserConfig) {\n  parserConfig = {...defaults, ...parserConfig}\n  var inline = parserConfig.inline\n\n  var tokenHooks = parserConfig.tokenHooks,\n      documentTypes = parserConfig.documentTypes || {},\n      mediaTypes = parserConfig.mediaTypes || {},\n      mediaFeatures = parserConfig.mediaFeatures || {},\n      mediaValueKeywords = parserConfig.mediaValueKeywords || {},\n      propertyKeywords = parserConfig.propertyKeywords || {},\n      nonStandardPropertyKeywords = parserConfig.nonStandardPropertyKeywords || {},\n      fontProperties = parserConfig.fontProperties || {},\n      counterDescriptors = parserConfig.counterDescriptors || {},\n      colorKeywords = parserConfig.colorKeywords || {},\n      valueKeywords = parserConfig.valueKeywords || {},\n      allowNested = parserConfig.allowNested,\n      lineComment = parserConfig.lineComment,\n      supportsAtComponent = parserConfig.supportsAtComponent === true,\n      highlightNonStandardPropertyKeywords = parserConfig.highlightNonStandardPropertyKeywords !== false;\n\n  var type, override;\n  function ret(style, tp) { type = tp; return style; }\n\n  // Tokenizers\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n    if (tokenHooks[ch]) {\n      var result = tokenHooks[ch](stream, state);\n      if (result !== false) return result;\n    }\n    if (ch == \"@\") {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"def\", stream.current());\n    } else if (ch == \"=\" || (ch == \"~\" || ch == \"|\") && stream.eat(\"=\")) {\n      return ret(null, \"compare\");\n    } else if (ch == \"\\\"\" || ch == \"'\") {\n      state.tokenize = tokenString(ch);\n      return state.tokenize(stream, state);\n    } else if (ch == \"#\") {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"atom\", \"hash\");\n    } else if (ch == \"!\") {\n      stream.match(/^\\s*\\w*/);\n      return ret(\"keyword\", \"important\");\n    } else if (/\\d/.test(ch) || ch == \".\" && stream.eat(/\\d/)) {\n      stream.eatWhile(/[\\w.%]/);\n      return ret(\"number\", \"unit\");\n    } else if (ch === \"-\") {\n      if (/[\\d.]/.test(stream.peek())) {\n        stream.eatWhile(/[\\w.%]/);\n        return ret(\"number\", \"unit\");\n      } else if (stream.match(/^-[\\w\\\\\\-]*/)) {\n        stream.eatWhile(/[\\w\\\\\\-]/);\n        if (stream.match(/^\\s*:/, false))\n          return ret(\"def\", \"variable-definition\");\n        return ret(\"variableName\", \"variable\");\n      } else if (stream.match(/^\\w+-/)) {\n        return ret(\"meta\", \"meta\");\n      }\n    } else if (/[,+>*\\/]/.test(ch)) {\n      return ret(null, \"select-op\");\n    } else if (ch == \".\" && stream.match(/^-?[_a-z][_a-z0-9-]*/i)) {\n      return ret(\"qualifier\", \"qualifier\");\n    } else if (/[:;{}\\[\\]\\(\\)]/.test(ch)) {\n      return ret(null, ch);\n    } else if (stream.match(/^[\\w-.]+(?=\\()/)) {\n      if (/^(url(-prefix)?|domain|regexp)$/i.test(stream.current())) {\n        state.tokenize = tokenParenthesized;\n      }\n      return ret(\"variableName.function\", \"variable\");\n    } else if (/[\\w\\\\\\-]/.test(ch)) {\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      return ret(\"property\", \"word\");\n    } else {\n      return ret(null, null);\n    }\n  }\n\n  function tokenString(quote) {\n    return function(stream, state) {\n      var escaped = false, ch;\n      while ((ch = stream.next()) != null) {\n        if (ch == quote && !escaped) {\n          if (quote == \")\") stream.backUp(1);\n          break;\n        }\n        escaped = !escaped && ch == \"\\\\\";\n      }\n      if (ch == quote || !escaped && quote != \")\") state.tokenize = null;\n      return ret(\"string\", \"string\");\n    };\n  }\n\n  function tokenParenthesized(stream, state) {\n    stream.next(); // Must be '('\n    if (!stream.match(/^\\s*[\\\"\\')]/, false))\n      state.tokenize = tokenString(\")\");\n    else\n      state.tokenize = null;\n    return ret(null, \"(\");\n  }\n\n  // Context management\n\n  function Context(type, indent, prev) {\n    this.type = type;\n    this.indent = indent;\n    this.prev = prev;\n  }\n\n  function pushContext(state, stream, type, indent) {\n    state.context = new Context(type, stream.indentation() + (indent === false ? 0 : stream.indentUnit), state.context);\n    return type;\n  }\n\n  function popContext(state) {\n    if (state.context.prev)\n      state.context = state.context.prev;\n    return state.context.type;\n  }\n\n  function pass(type, stream, state) {\n    return states[state.context.type](type, stream, state);\n  }\n  function popAndPass(type, stream, state, n) {\n    for (var i = n || 1; i > 0; i--)\n      state.context = state.context.prev;\n    return pass(type, stream, state);\n  }\n\n  // Parser\n\n  function wordAsValue(stream) {\n    var word = stream.current().toLowerCase();\n    if (valueKeywords.hasOwnProperty(word))\n      override = \"atom\";\n    else if (colorKeywords.hasOwnProperty(word))\n      override = \"keyword\";\n    else\n      override = \"variable\";\n  }\n\n  var states = {};\n\n  states.top = function(type, stream, state) {\n    if (type == \"{\") {\n      return pushContext(state, stream, \"block\");\n    } else if (type == \"}\" && state.context.prev) {\n      return popContext(state);\n    } else if (supportsAtComponent && /@component/i.test(type)) {\n      return pushContext(state, stream, \"atComponentBlock\");\n    } else if (/^@(-moz-)?document$/i.test(type)) {\n      return pushContext(state, stream, \"documentTypes\");\n    } else if (/^@(media|supports|(-moz-)?document|import)$/i.test(type)) {\n      return pushContext(state, stream, \"atBlock\");\n    } else if (/^@(font-face|counter-style)/i.test(type)) {\n      state.stateArg = type;\n      return \"restricted_atBlock_before\";\n    } else if (/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(type)) {\n      return \"keyframes\";\n    } else if (type && type.charAt(0) == \"@\") {\n      return pushContext(state, stream, \"at\");\n    } else if (type == \"hash\") {\n      override = \"builtin\";\n    } else if (type == \"word\") {\n      override = \"tag\";\n    } else if (type == \"variable-definition\") {\n      return \"maybeprop\";\n    } else if (type == \"interpolation\") {\n      return pushContext(state, stream, \"interpolation\");\n    } else if (type == \":\") {\n      return \"pseudo\";\n    } else if (allowNested && type == \"(\") {\n      return pushContext(state, stream, \"parens\");\n    }\n    return state.context.type;\n  };\n\n  states.block = function(type, stream, state) {\n    if (type == \"word\") {\n      var word = stream.current().toLowerCase();\n      if (propertyKeywords.hasOwnProperty(word)) {\n        override = \"property\";\n        return \"maybeprop\";\n      } else if (nonStandardPropertyKeywords.hasOwnProperty(word)) {\n        override = highlightNonStandardPropertyKeywords ? \"string.special\" : \"property\";\n        return \"maybeprop\";\n      } else if (allowNested) {\n        override = stream.match(/^\\s*:(?:\\s|$)/, false) ? \"property\" : \"tag\";\n        return \"block\";\n      } else {\n        override = \"error\";\n        return \"maybeprop\";\n      }\n    } else if (type == \"meta\") {\n      return \"block\";\n    } else if (!allowNested && (type == \"hash\" || type == \"qualifier\")) {\n      override = \"error\";\n      return \"block\";\n    } else {\n      return states.top(type, stream, state);\n    }\n  };\n\n  states.maybeprop = function(type, stream, state) {\n    if (type == \":\") return pushContext(state, stream, \"prop\");\n    return pass(type, stream, state);\n  };\n\n  states.prop = function(type, stream, state) {\n    if (type == \";\") return popContext(state);\n    if (type == \"{\" && allowNested) return pushContext(state, stream, \"propBlock\");\n    if (type == \"}\" || type == \"{\") return popAndPass(type, stream, state);\n    if (type == \"(\") return pushContext(state, stream, \"parens\");\n\n    if (type == \"hash\" && !/^#([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(stream.current())) {\n      override = \"error\";\n    } else if (type == \"word\") {\n      wordAsValue(stream);\n    } else if (type == \"interpolation\") {\n      return pushContext(state, stream, \"interpolation\");\n    }\n    return \"prop\";\n  };\n\n  states.propBlock = function(type, _stream, state) {\n    if (type == \"}\") return popContext(state);\n    if (type == \"word\") { override = \"property\"; return \"maybeprop\"; }\n    return state.context.type;\n  };\n\n  states.parens = function(type, stream, state) {\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state);\n    if (type == \")\") return popContext(state);\n    if (type == \"(\") return pushContext(state, stream, \"parens\");\n    if (type == \"interpolation\") return pushContext(state, stream, \"interpolation\");\n    if (type == \"word\") wordAsValue(stream);\n    return \"parens\";\n  };\n\n  states.pseudo = function(type, stream, state) {\n    if (type == \"meta\") return \"pseudo\";\n\n    if (type == \"word\") {\n      override = \"variableName.constant\";\n      return state.context.type;\n    }\n    return pass(type, stream, state);\n  };\n\n  states.documentTypes = function(type, stream, state) {\n    if (type == \"word\" && documentTypes.hasOwnProperty(stream.current())) {\n      override = \"tag\";\n      return state.context.type;\n    } else {\n      return states.atBlock(type, stream, state);\n    }\n  };\n\n  states.atBlock = function(type, stream, state) {\n    if (type == \"(\") return pushContext(state, stream, \"atBlock_parens\");\n    if (type == \"}\" || type == \";\") return popAndPass(type, stream, state);\n    if (type == \"{\") return popContext(state) && pushContext(state, stream, allowNested ? \"block\" : \"top\");\n\n    if (type == \"interpolation\") return pushContext(state, stream, \"interpolation\");\n\n    if (type == \"word\") {\n      var word = stream.current().toLowerCase();\n      if (word == \"only\" || word == \"not\" || word == \"and\" || word == \"or\")\n        override = \"keyword\";\n      else if (mediaTypes.hasOwnProperty(word))\n        override = \"attribute\";\n      else if (mediaFeatures.hasOwnProperty(word))\n        override = \"property\";\n      else if (mediaValueKeywords.hasOwnProperty(word))\n        override = \"keyword\";\n      else if (propertyKeywords.hasOwnProperty(word))\n        override = \"property\";\n      else if (nonStandardPropertyKeywords.hasOwnProperty(word))\n        override = highlightNonStandardPropertyKeywords ? \"string.special\" : \"property\";\n      else if (valueKeywords.hasOwnProperty(word))\n        override = \"atom\";\n      else if (colorKeywords.hasOwnProperty(word))\n        override = \"keyword\";\n      else\n        override = \"error\";\n    }\n    return state.context.type;\n  };\n\n  states.atComponentBlock = function(type, stream, state) {\n    if (type == \"}\")\n      return popAndPass(type, stream, state);\n    if (type == \"{\")\n      return popContext(state) && pushContext(state, stream, allowNested ? \"block\" : \"top\", false);\n    if (type == \"word\")\n      override = \"error\";\n    return state.context.type;\n  };\n\n  states.atBlock_parens = function(type, stream, state) {\n    if (type == \")\") return popContext(state);\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state, 2);\n    return states.atBlock(type, stream, state);\n  };\n\n  states.restricted_atBlock_before = function(type, stream, state) {\n    if (type == \"{\")\n      return pushContext(state, stream, \"restricted_atBlock\");\n    if (type == \"word\" && state.stateArg == \"@counter-style\") {\n      override = \"variable\";\n      return \"restricted_atBlock_before\";\n    }\n    return pass(type, stream, state);\n  };\n\n  states.restricted_atBlock = function(type, stream, state) {\n    if (type == \"}\") {\n      state.stateArg = null;\n      return popContext(state);\n    }\n    if (type == \"word\") {\n      if ((state.stateArg == \"@font-face\" && !fontProperties.hasOwnProperty(stream.current().toLowerCase())) ||\n          (state.stateArg == \"@counter-style\" && !counterDescriptors.hasOwnProperty(stream.current().toLowerCase())))\n        override = \"error\";\n      else\n        override = \"property\";\n      return \"maybeprop\";\n    }\n    return \"restricted_atBlock\";\n  };\n\n  states.keyframes = function(type, stream, state) {\n    if (type == \"word\") { override = \"variable\"; return \"keyframes\"; }\n    if (type == \"{\") return pushContext(state, stream, \"top\");\n    return pass(type, stream, state);\n  };\n\n  states.at = function(type, stream, state) {\n    if (type == \";\") return popContext(state);\n    if (type == \"{\" || type == \"}\") return popAndPass(type, stream, state);\n    if (type == \"word\") override = \"tag\";\n    else if (type == \"hash\") override = \"builtin\";\n    return \"at\";\n  };\n\n  states.interpolation = function(type, stream, state) {\n    if (type == \"}\") return popContext(state);\n    if (type == \"{\" || type == \";\") return popAndPass(type, stream, state);\n    if (type == \"word\") override = \"variable\";\n    else if (type != \"variable\" && type != \"(\" && type != \")\") override = \"error\";\n    return \"interpolation\";\n  };\n\n  return {\n    name: parserConfig.name,\n    startState: function() {\n      return {tokenize: null,\n              state: inline ? \"block\" : \"top\",\n              stateArg: null,\n              context: new Context(inline ? \"block\" : \"top\", 0, null)};\n    },\n\n    token: function(stream, state) {\n      if (!state.tokenize && stream.eatSpace()) return null;\n      var style = (state.tokenize || tokenBase)(stream, state);\n      if (style && typeof style == \"object\") {\n        type = style[1];\n        style = style[0];\n      }\n      override = style;\n      if (type != \"comment\")\n        state.state = states[state.state](type, stream, state);\n      return override;\n    },\n\n    indent: function(state, textAfter, iCx) {\n      var cx = state.context, ch = textAfter && textAfter.charAt(0);\n      var indent = cx.indent;\n      if (cx.type == \"prop\" && (ch == \"}\" || ch == \")\")) cx = cx.prev;\n      if (cx.prev) {\n        if (ch == \"}\" && (cx.type == \"block\" || cx.type == \"top\" ||\n                          cx.type == \"interpolation\" || cx.type == \"restricted_atBlock\")) {\n          // Resume indentation from parent context.\n          cx = cx.prev;\n          indent = cx.indent;\n        } else if (ch == \")\" && (cx.type == \"parens\" || cx.type == \"atBlock_parens\") ||\n                   ch == \"{\" && (cx.type == \"at\" || cx.type == \"atBlock\")) {\n          // Dedent relative to current context.\n          indent = Math.max(0, cx.indent - iCx.unit);\n        }\n      }\n      return indent;\n    },\n\n    languageData: {\n      indentOnInput: /^\\s*\\}$/,\n      commentTokens: {line: lineComment, block: {open: \"/*\", close: \"*/\"}},\n      autocomplete: allWords\n    }\n  };\n};\n\nfunction keySet(array) {\n  var keys = {};\n  for (var i = 0; i < array.length; ++i) {\n    keys[array[i].toLowerCase()] = true;\n  }\n  return keys;\n}\n\nvar documentTypes_ = [\n  \"domain\", \"regexp\", \"url\", \"url-prefix\"\n], documentTypes = keySet(documentTypes_);\n\nvar mediaTypes_ = [\n  \"all\", \"aural\", \"braille\", \"handheld\", \"print\", \"projection\", \"screen\",\n  \"tty\", \"tv\", \"embossed\"\n], mediaTypes = keySet(mediaTypes_);\n\nvar mediaFeatures_ = [\n  \"width\", \"min-width\", \"max-width\", \"height\", \"min-height\", \"max-height\",\n  \"device-width\", \"min-device-width\", \"max-device-width\", \"device-height\",\n  \"min-device-height\", \"max-device-height\", \"aspect-ratio\",\n  \"min-aspect-ratio\", \"max-aspect-ratio\", \"device-aspect-ratio\",\n  \"min-device-aspect-ratio\", \"max-device-aspect-ratio\", \"color\", \"min-color\",\n  \"max-color\", \"color-index\", \"min-color-index\", \"max-color-index\",\n  \"monochrome\", \"min-monochrome\", \"max-monochrome\", \"resolution\",\n  \"min-resolution\", \"max-resolution\", \"scan\", \"grid\", \"orientation\",\n  \"device-pixel-ratio\", \"min-device-pixel-ratio\", \"max-device-pixel-ratio\",\n  \"pointer\", \"any-pointer\", \"hover\", \"any-hover\", \"prefers-color-scheme\",\n  \"dynamic-range\", \"video-dynamic-range\"\n], mediaFeatures = keySet(mediaFeatures_);\n\nvar mediaValueKeywords_ = [\n  \"landscape\", \"portrait\", \"none\", \"coarse\", \"fine\", \"on-demand\", \"hover\",\n  \"interlace\", \"progressive\",\n  \"dark\", \"light\",\n  \"standard\", \"high\"\n], mediaValueKeywords = keySet(mediaValueKeywords_);\n\nvar propertyKeywords_ = [\n  \"align-content\", \"align-items\", \"align-self\", \"alignment-adjust\",\n  \"alignment-baseline\", \"all\", \"anchor-point\", \"animation\", \"animation-delay\",\n  \"animation-direction\", \"animation-duration\", \"animation-fill-mode\",\n  \"animation-iteration-count\", \"animation-name\", \"animation-play-state\",\n  \"animation-timing-function\", \"appearance\", \"azimuth\", \"backdrop-filter\",\n  \"backface-visibility\", \"background\", \"background-attachment\",\n  \"background-blend-mode\", \"background-clip\", \"background-color\",\n  \"background-image\", \"background-origin\", \"background-position\",\n  \"background-position-x\", \"background-position-y\", \"background-repeat\",\n  \"background-size\", \"baseline-shift\", \"binding\", \"bleed\", \"block-size\",\n  \"bookmark-label\", \"bookmark-level\", \"bookmark-state\", \"bookmark-target\",\n  \"border\", \"border-bottom\", \"border-bottom-color\", \"border-bottom-left-radius\",\n  \"border-bottom-right-radius\", \"border-bottom-style\", \"border-bottom-width\",\n  \"border-collapse\", \"border-color\", \"border-image\", \"border-image-outset\",\n  \"border-image-repeat\", \"border-image-slice\", \"border-image-source\",\n  \"border-image-width\", \"border-left\", \"border-left-color\", \"border-left-style\",\n  \"border-left-width\", \"border-radius\", \"border-right\", \"border-right-color\",\n  \"border-right-style\", \"border-right-width\", \"border-spacing\", \"border-style\",\n  \"border-top\", \"border-top-color\", \"border-top-left-radius\",\n  \"border-top-right-radius\", \"border-top-style\", \"border-top-width\",\n  \"border-width\", \"bottom\", \"box-decoration-break\", \"box-shadow\", \"box-sizing\",\n  \"break-after\", \"break-before\", \"break-inside\", \"caption-side\", \"caret-color\",\n  \"clear\", \"clip\", \"color\", \"color-profile\", \"column-count\", \"column-fill\",\n  \"column-gap\", \"column-rule\", \"column-rule-color\", \"column-rule-style\",\n  \"column-rule-width\", \"column-span\", \"column-width\", \"columns\", \"contain\",\n  \"content\", \"counter-increment\", \"counter-reset\", \"crop\", \"cue\", \"cue-after\",\n  \"cue-before\", \"cursor\", \"direction\", \"display\", \"dominant-baseline\",\n  \"drop-initial-after-adjust\", \"drop-initial-after-align\",\n  \"drop-initial-before-adjust\", \"drop-initial-before-align\", \"drop-initial-size\",\n  \"drop-initial-value\", \"elevation\", \"empty-cells\", \"fit\", \"fit-content\", \"fit-position\",\n  \"flex\", \"flex-basis\", \"flex-direction\", \"flex-flow\", \"flex-grow\",\n  \"flex-shrink\", \"flex-wrap\", \"float\", \"float-offset\", \"flow-from\", \"flow-into\",\n  \"font\", \"font-family\", \"font-feature-settings\", \"font-kerning\",\n  \"font-language-override\", \"font-optical-sizing\", \"font-size\",\n  \"font-size-adjust\", \"font-stretch\", \"font-style\", \"font-synthesis\",\n  \"font-variant\", \"font-variant-alternates\", \"font-variant-caps\",\n  \"font-variant-east-asian\", \"font-variant-ligatures\", \"font-variant-numeric\",\n  \"font-variant-position\", \"font-variation-settings\", \"font-weight\", \"gap\",\n  \"grid\", \"grid-area\", \"grid-auto-columns\", \"grid-auto-flow\", \"grid-auto-rows\",\n  \"grid-column\", \"grid-column-end\", \"grid-column-gap\", \"grid-column-start\",\n  \"grid-gap\", \"grid-row\", \"grid-row-end\", \"grid-row-gap\", \"grid-row-start\",\n  \"grid-template\", \"grid-template-areas\", \"grid-template-columns\",\n  \"grid-template-rows\", \"hanging-punctuation\", \"height\", \"hyphens\", \"icon\",\n  \"image-orientation\", \"image-rendering\", \"image-resolution\", \"inline-box-align\",\n  \"inset\", \"inset-block\", \"inset-block-end\", \"inset-block-start\", \"inset-inline\",\n  \"inset-inline-end\", \"inset-inline-start\", \"isolation\", \"justify-content\",\n  \"justify-items\", \"justify-self\", \"left\", \"letter-spacing\", \"line-break\",\n  \"line-height\", \"line-height-step\", \"line-stacking\", \"line-stacking-ruby\",\n  \"line-stacking-shift\", \"line-stacking-strategy\", \"list-style\",\n  \"list-style-image\", \"list-style-position\", \"list-style-type\", \"margin\",\n  \"margin-bottom\", \"margin-left\", \"margin-right\", \"margin-top\", \"marks\",\n  \"marquee-direction\", \"marquee-loop\", \"marquee-play-count\", \"marquee-speed\",\n  \"marquee-style\", \"mask-clip\", \"mask-composite\", \"mask-image\", \"mask-mode\",\n  \"mask-origin\", \"mask-position\", \"mask-repeat\", \"mask-size\",\"mask-type\",\n  \"max-block-size\", \"max-height\", \"max-inline-size\",\n  \"max-width\", \"min-block-size\", \"min-height\", \"min-inline-size\", \"min-width\",\n  \"mix-blend-mode\", \"move-to\", \"nav-down\", \"nav-index\", \"nav-left\", \"nav-right\",\n  \"nav-up\", \"object-fit\", \"object-position\", \"offset\", \"offset-anchor\",\n  \"offset-distance\", \"offset-path\", \"offset-position\", \"offset-rotate\",\n  \"opacity\", \"order\", \"orphans\", \"outline\", \"outline-color\", \"outline-offset\",\n  \"outline-style\", \"outline-width\", \"overflow\", \"overflow-style\",\n  \"overflow-wrap\", \"overflow-x\", \"overflow-y\", \"padding\", \"padding-bottom\",\n  \"padding-left\", \"padding-right\", \"padding-top\", \"page\", \"page-break-after\",\n  \"page-break-before\", \"page-break-inside\", \"page-policy\", \"pause\",\n  \"pause-after\", \"pause-before\", \"perspective\", \"perspective-origin\", \"pitch\",\n  \"pitch-range\", \"place-content\", \"place-items\", \"place-self\", \"play-during\",\n  \"position\", \"presentation-level\", \"punctuation-trim\", \"quotes\",\n  \"region-break-after\", \"region-break-before\", \"region-break-inside\",\n  \"region-fragment\", \"rendering-intent\", \"resize\", \"rest\", \"rest-after\",\n  \"rest-before\", \"richness\", \"right\", \"rotate\", \"rotation\", \"rotation-point\",\n  \"row-gap\", \"ruby-align\", \"ruby-overhang\", \"ruby-position\", \"ruby-span\",\n  \"scale\", \"scroll-behavior\", \"scroll-margin\", \"scroll-margin-block\",\n  \"scroll-margin-block-end\", \"scroll-margin-block-start\", \"scroll-margin-bottom\",\n  \"scroll-margin-inline\", \"scroll-margin-inline-end\",\n  \"scroll-margin-inline-start\", \"scroll-margin-left\", \"scroll-margin-right\",\n  \"scroll-margin-top\", \"scroll-padding\", \"scroll-padding-block\",\n  \"scroll-padding-block-end\", \"scroll-padding-block-start\",\n  \"scroll-padding-bottom\", \"scroll-padding-inline\", \"scroll-padding-inline-end\",\n  \"scroll-padding-inline-start\", \"scroll-padding-left\", \"scroll-padding-right\",\n  \"scroll-padding-top\", \"scroll-snap-align\", \"scroll-snap-type\",\n  \"shape-image-threshold\", \"shape-inside\", \"shape-margin\", \"shape-outside\",\n  \"size\", \"speak\", \"speak-as\", \"speak-header\", \"speak-numeral\",\n  \"speak-punctuation\", \"speech-rate\", \"stress\", \"string-set\", \"tab-size\",\n  \"table-layout\", \"target\", \"target-name\", \"target-new\", \"target-position\",\n  \"text-align\", \"text-align-last\", \"text-combine-upright\", \"text-decoration\",\n  \"text-decoration-color\", \"text-decoration-line\", \"text-decoration-skip\",\n  \"text-decoration-skip-ink\", \"text-decoration-style\", \"text-emphasis\",\n  \"text-emphasis-color\", \"text-emphasis-position\", \"text-emphasis-style\",\n  \"text-height\", \"text-indent\", \"text-justify\", \"text-orientation\",\n  \"text-outline\", \"text-overflow\", \"text-rendering\", \"text-shadow\",\n  \"text-size-adjust\", \"text-space-collapse\", \"text-transform\",\n  \"text-underline-position\", \"text-wrap\", \"top\", \"touch-action\", \"transform\", \"transform-origin\",\n  \"transform-style\", \"transition\", \"transition-delay\", \"transition-duration\",\n  \"transition-property\", \"transition-timing-function\", \"translate\",\n  \"unicode-bidi\", \"user-select\", \"vertical-align\", \"visibility\", \"voice-balance\",\n  \"voice-duration\", \"voice-family\", \"voice-pitch\", \"voice-range\", \"voice-rate\",\n  \"voice-stress\", \"voice-volume\", \"volume\", \"white-space\", \"widows\", \"width\",\n  \"will-change\", \"word-break\", \"word-spacing\", \"word-wrap\", \"writing-mode\", \"z-index\",\n  // SVG-specific\n  \"clip-path\", \"clip-rule\", \"mask\", \"enable-background\", \"filter\", \"flood-color\",\n  \"flood-opacity\", \"lighting-color\", \"stop-color\", \"stop-opacity\", \"pointer-events\",\n  \"color-interpolation\", \"color-interpolation-filters\",\n  \"color-rendering\", \"fill\", \"fill-opacity\", \"fill-rule\", \"image-rendering\",\n  \"marker\", \"marker-end\", \"marker-mid\", \"marker-start\", \"paint-order\", \"shape-rendering\", \"stroke\",\n  \"stroke-dasharray\", \"stroke-dashoffset\", \"stroke-linecap\", \"stroke-linejoin\",\n  \"stroke-miterlimit\", \"stroke-opacity\", \"stroke-width\", \"text-rendering\",\n  \"baseline-shift\", \"dominant-baseline\", \"glyph-orientation-horizontal\",\n  \"glyph-orientation-vertical\", \"text-anchor\", \"writing-mode\",\n], propertyKeywords = keySet(propertyKeywords_);\n\nvar nonStandardPropertyKeywords_ = [\n  \"accent-color\", \"aspect-ratio\", \"border-block\", \"border-block-color\", \"border-block-end\",\n  \"border-block-end-color\", \"border-block-end-style\", \"border-block-end-width\",\n  \"border-block-start\", \"border-block-start-color\", \"border-block-start-style\",\n  \"border-block-start-width\", \"border-block-style\", \"border-block-width\",\n  \"border-inline\", \"border-inline-color\", \"border-inline-end\",\n  \"border-inline-end-color\", \"border-inline-end-style\",\n  \"border-inline-end-width\", \"border-inline-start\", \"border-inline-start-color\",\n  \"border-inline-start-style\", \"border-inline-start-width\",\n  \"border-inline-style\", \"border-inline-width\", \"content-visibility\", \"margin-block\",\n  \"margin-block-end\", \"margin-block-start\", \"margin-inline\", \"margin-inline-end\",\n  \"margin-inline-start\", \"overflow-anchor\", \"overscroll-behavior\", \"padding-block\", \"padding-block-end\",\n  \"padding-block-start\", \"padding-inline\", \"padding-inline-end\",\n  \"padding-inline-start\", \"scroll-snap-stop\", \"scrollbar-3d-light-color\",\n  \"scrollbar-arrow-color\", \"scrollbar-base-color\", \"scrollbar-dark-shadow-color\",\n  \"scrollbar-face-color\", \"scrollbar-highlight-color\", \"scrollbar-shadow-color\",\n  \"scrollbar-track-color\", \"searchfield-cancel-button\", \"searchfield-decoration\",\n  \"searchfield-results-button\", \"searchfield-results-decoration\", \"shape-inside\", \"zoom\"\n], nonStandardPropertyKeywords = keySet(nonStandardPropertyKeywords_);\n\nvar fontProperties_ = [\n  \"font-display\", \"font-family\", \"src\", \"unicode-range\", \"font-variant\",\n  \"font-feature-settings\", \"font-stretch\", \"font-weight\", \"font-style\"\n], fontProperties = keySet(fontProperties_);\n\nvar counterDescriptors_ = [\n  \"additive-symbols\", \"fallback\", \"negative\", \"pad\", \"prefix\", \"range\",\n  \"speak-as\", \"suffix\", \"symbols\", \"system\"\n], counterDescriptors = keySet(counterDescriptors_);\n\nvar colorKeywords_ = [\n  \"aliceblue\", \"antiquewhite\", \"aqua\", \"aquamarine\", \"azure\", \"beige\",\n  \"bisque\", \"black\", \"blanchedalmond\", \"blue\", \"blueviolet\", \"brown\",\n  \"burlywood\", \"cadetblue\", \"chartreuse\", \"chocolate\", \"coral\", \"cornflowerblue\",\n  \"cornsilk\", \"crimson\", \"cyan\", \"darkblue\", \"darkcyan\", \"darkgoldenrod\",\n  \"darkgray\", \"darkgreen\", \"darkgrey\", \"darkkhaki\", \"darkmagenta\", \"darkolivegreen\",\n  \"darkorange\", \"darkorchid\", \"darkred\", \"darksalmon\", \"darkseagreen\",\n  \"darkslateblue\", \"darkslategray\", \"darkslategrey\", \"darkturquoise\", \"darkviolet\",\n  \"deeppink\", \"deepskyblue\", \"dimgray\", \"dimgrey\", \"dodgerblue\", \"firebrick\",\n  \"floralwhite\", \"forestgreen\", \"fuchsia\", \"gainsboro\", \"ghostwhite\",\n  \"gold\", \"goldenrod\", \"gray\", \"grey\", \"green\", \"greenyellow\", \"honeydew\",\n  \"hotpink\", \"indianred\", \"indigo\", \"ivory\", \"khaki\", \"lavender\",\n  \"lavenderblush\", \"lawngreen\", \"lemonchiffon\", \"lightblue\", \"lightcoral\",\n  \"lightcyan\", \"lightgoldenrodyellow\", \"lightgray\", \"lightgreen\", \"lightgrey\", \"lightpink\",\n  \"lightsalmon\", \"lightseagreen\", \"lightskyblue\", \"lightslategray\", \"lightslategrey\",\n  \"lightsteelblue\", \"lightyellow\", \"lime\", \"limegreen\", \"linen\", \"magenta\",\n  \"maroon\", \"mediumaquamarine\", \"mediumblue\", \"mediumorchid\", \"mediumpurple\",\n  \"mediumseagreen\", \"mediumslateblue\", \"mediumspringgreen\", \"mediumturquoise\",\n  \"mediumvioletred\", \"midnightblue\", \"mintcream\", \"mistyrose\", \"moccasin\",\n  \"navajowhite\", \"navy\", \"oldlace\", \"olive\", \"olivedrab\", \"orange\", \"orangered\",\n  \"orchid\", \"palegoldenrod\", \"palegreen\", \"paleturquoise\", \"palevioletred\",\n  \"papayawhip\", \"peachpuff\", \"peru\", \"pink\", \"plum\", \"powderblue\",\n  \"purple\", \"rebeccapurple\", \"red\", \"rosybrown\", \"royalblue\", \"saddlebrown\",\n  \"salmon\", \"sandybrown\", \"seagreen\", \"seashell\", \"sienna\", \"silver\", \"skyblue\",\n  \"slateblue\", \"slategray\", \"slategrey\", \"snow\", \"springgreen\", \"steelblue\", \"tan\",\n  \"teal\", \"thistle\", \"tomato\", \"turquoise\", \"violet\", \"wheat\", \"white\",\n  \"whitesmoke\", \"yellow\", \"yellowgreen\"\n], colorKeywords = keySet(colorKeywords_);\n\nvar valueKeywords_ = [\n  \"above\", \"absolute\", \"activeborder\", \"additive\", \"activecaption\", \"afar\",\n  \"after-white-space\", \"ahead\", \"alias\", \"all\", \"all-scroll\", \"alphabetic\", \"alternate\",\n  \"always\", \"amharic\", \"amharic-abegede\", \"antialiased\", \"appworkspace\",\n  \"arabic-indic\", \"armenian\", \"asterisks\", \"attr\", \"auto\", \"auto-flow\", \"avoid\", \"avoid-column\", \"avoid-page\",\n  \"avoid-region\", \"axis-pan\", \"background\", \"backwards\", \"baseline\", \"below\", \"bidi-override\", \"binary\",\n  \"bengali\", \"blink\", \"block\", \"block-axis\", \"blur\", \"bold\", \"bolder\", \"border\", \"border-box\",\n  \"both\", \"bottom\", \"break\", \"break-all\", \"break-word\", \"brightness\", \"bullets\", \"button\",\n  \"buttonface\", \"buttonhighlight\", \"buttonshadow\", \"buttontext\", \"calc\", \"cambodian\",\n  \"capitalize\", \"caps-lock-indicator\", \"caption\", \"captiontext\", \"caret\",\n  \"cell\", \"center\", \"checkbox\", \"circle\", \"cjk-decimal\", \"cjk-earthly-branch\",\n  \"cjk-heavenly-stem\", \"cjk-ideographic\", \"clear\", \"clip\", \"close-quote\",\n  \"col-resize\", \"collapse\", \"color\", \"color-burn\", \"color-dodge\", \"column\", \"column-reverse\",\n  \"compact\", \"condensed\", \"conic-gradient\", \"contain\", \"content\", \"contents\",\n  \"content-box\", \"context-menu\", \"continuous\", \"contrast\", \"copy\", \"counter\", \"counters\", \"cover\", \"crop\",\n  \"cross\", \"crosshair\", \"cubic-bezier\", \"currentcolor\", \"cursive\", \"cyclic\", \"darken\", \"dashed\", \"decimal\",\n  \"decimal-leading-zero\", \"default\", \"default-button\", \"dense\", \"destination-atop\",\n  \"destination-in\", \"destination-out\", \"destination-over\", \"devanagari\", \"difference\",\n  \"disc\", \"discard\", \"disclosure-closed\", \"disclosure-open\", \"document\",\n  \"dot-dash\", \"dot-dot-dash\",\n  \"dotted\", \"double\", \"down\", \"drop-shadow\", \"e-resize\", \"ease\", \"ease-in\", \"ease-in-out\", \"ease-out\",\n  \"element\", \"ellipse\", \"ellipsis\", \"embed\", \"end\", \"ethiopic\", \"ethiopic-abegede\",\n  \"ethiopic-abegede-am-et\", \"ethiopic-abegede-gez\", \"ethiopic-abegede-ti-er\",\n  \"ethiopic-abegede-ti-et\", \"ethiopic-halehame-aa-er\",\n  \"ethiopic-halehame-aa-et\", \"ethiopic-halehame-am-et\",\n  \"ethiopic-halehame-gez\", \"ethiopic-halehame-om-et\",\n  \"ethiopic-halehame-sid-et\", \"ethiopic-halehame-so-et\",\n  \"ethiopic-halehame-ti-er\", \"ethiopic-halehame-ti-et\", \"ethiopic-halehame-tig\",\n  \"ethiopic-numeric\", \"ew-resize\", \"exclusion\", \"expanded\", \"extends\", \"extra-condensed\",\n  \"extra-expanded\", \"fantasy\", \"fast\", \"fill\", \"fill-box\", \"fixed\", \"flat\", \"flex\", \"flex-end\", \"flex-start\", \"footnotes\",\n  \"forwards\", \"from\", \"geometricPrecision\", \"georgian\", \"grayscale\", \"graytext\", \"grid\", \"groove\",\n  \"gujarati\", \"gurmukhi\", \"hand\", \"hangul\", \"hangul-consonant\", \"hard-light\", \"hebrew\",\n  \"help\", \"hidden\", \"hide\", \"higher\", \"highlight\", \"highlighttext\",\n  \"hiragana\", \"hiragana-iroha\", \"horizontal\", \"hsl\", \"hsla\", \"hue\", \"hue-rotate\", \"icon\", \"ignore\",\n  \"inactiveborder\", \"inactivecaption\", \"inactivecaptiontext\", \"infinite\",\n  \"infobackground\", \"infotext\", \"inherit\", \"initial\", \"inline\", \"inline-axis\",\n  \"inline-block\", \"inline-flex\", \"inline-grid\", \"inline-table\", \"inset\", \"inside\", \"intrinsic\", \"invert\",\n  \"italic\", \"japanese-formal\", \"japanese-informal\", \"justify\", \"kannada\",\n  \"katakana\", \"katakana-iroha\", \"keep-all\", \"khmer\",\n  \"korean-hangul-formal\", \"korean-hanja-formal\", \"korean-hanja-informal\",\n  \"landscape\", \"lao\", \"large\", \"larger\", \"left\", \"level\", \"lighter\", \"lighten\",\n  \"line-through\", \"linear\", \"linear-gradient\", \"lines\", \"list-item\", \"listbox\", \"listitem\",\n  \"local\", \"logical\", \"loud\", \"lower\", \"lower-alpha\", \"lower-armenian\",\n  \"lower-greek\", \"lower-hexadecimal\", \"lower-latin\", \"lower-norwegian\",\n  \"lower-roman\", \"lowercase\", \"ltr\", \"luminosity\", \"malayalam\", \"manipulation\", \"match\", \"matrix\", \"matrix3d\",\n  \"media-play-button\", \"media-slider\", \"media-sliderthumb\",\n  \"media-volume-slider\", \"media-volume-sliderthumb\", \"medium\",\n  \"menu\", \"menulist\", \"menulist-button\",\n  \"menutext\", \"message-box\", \"middle\", \"min-intrinsic\",\n  \"mix\", \"mongolian\", \"monospace\", \"move\", \"multiple\", \"multiple_mask_images\", \"multiply\", \"myanmar\", \"n-resize\",\n  \"narrower\", \"ne-resize\", \"nesw-resize\", \"no-close-quote\", \"no-drop\",\n  \"no-open-quote\", \"no-repeat\", \"none\", \"normal\", \"not-allowed\", \"nowrap\",\n  \"ns-resize\", \"numbers\", \"numeric\", \"nw-resize\", \"nwse-resize\", \"oblique\", \"octal\", \"opacity\", \"open-quote\",\n  \"optimizeLegibility\", \"optimizeSpeed\", \"oriya\", \"oromo\", \"outset\",\n  \"outside\", \"outside-shape\", \"overlay\", \"overline\", \"padding\", \"padding-box\",\n  \"painted\", \"page\", \"paused\", \"persian\", \"perspective\", \"pinch-zoom\", \"plus-darker\", \"plus-lighter\",\n  \"pointer\", \"polygon\", \"portrait\", \"pre\", \"pre-line\", \"pre-wrap\", \"preserve-3d\",\n  \"progress\", \"push-button\", \"radial-gradient\", \"radio\", \"read-only\",\n  \"read-write\", \"read-write-plaintext-only\", \"rectangle\", \"region\",\n  \"relative\", \"repeat\", \"repeating-linear-gradient\", \"repeating-radial-gradient\",\n  \"repeating-conic-gradient\", \"repeat-x\", \"repeat-y\", \"reset\", \"reverse\",\n  \"rgb\", \"rgba\", \"ridge\", \"right\", \"rotate\", \"rotate3d\", \"rotateX\", \"rotateY\",\n  \"rotateZ\", \"round\", \"row\", \"row-resize\", \"row-reverse\", \"rtl\", \"run-in\", \"running\",\n  \"s-resize\", \"sans-serif\", \"saturate\", \"saturation\", \"scale\", \"scale3d\", \"scaleX\", \"scaleY\", \"scaleZ\", \"screen\",\n  \"scroll\", \"scrollbar\", \"scroll-position\", \"se-resize\", \"searchfield\",\n  \"searchfield-cancel-button\", \"searchfield-decoration\",\n  \"searchfield-results-button\", \"searchfield-results-decoration\", \"self-start\", \"self-end\",\n  \"semi-condensed\", \"semi-expanded\", \"separate\", \"sepia\", \"serif\", \"show\", \"sidama\",\n  \"simp-chinese-formal\", \"simp-chinese-informal\", \"single\",\n  \"skew\", \"skewX\", \"skewY\", \"skip-white-space\", \"slide\", \"slider-horizontal\",\n  \"slider-vertical\", \"sliderthumb-horizontal\", \"sliderthumb-vertical\", \"slow\",\n  \"small\", \"small-caps\", \"small-caption\", \"smaller\", \"soft-light\", \"solid\", \"somali\",\n  \"source-atop\", \"source-in\", \"source-out\", \"source-over\", \"space\", \"space-around\", \"space-between\", \"space-evenly\", \"spell-out\", \"square\",\n  \"square-button\", \"start\", \"static\", \"status-bar\", \"stretch\", \"stroke\", \"stroke-box\", \"sub\",\n  \"subpixel-antialiased\", \"svg_masks\", \"super\", \"sw-resize\", \"symbolic\", \"symbols\", \"system-ui\", \"table\",\n  \"table-caption\", \"table-cell\", \"table-column\", \"table-column-group\",\n  \"table-footer-group\", \"table-header-group\", \"table-row\", \"table-row-group\",\n  \"tamil\",\n  \"telugu\", \"text\", \"text-bottom\", \"text-top\", \"textarea\", \"textfield\", \"thai\",\n  \"thick\", \"thin\", \"threeddarkshadow\", \"threedface\", \"threedhighlight\",\n  \"threedlightshadow\", \"threedshadow\", \"tibetan\", \"tigre\", \"tigrinya-er\",\n  \"tigrinya-er-abegede\", \"tigrinya-et\", \"tigrinya-et-abegede\", \"to\", \"top\",\n  \"trad-chinese-formal\", \"trad-chinese-informal\", \"transform\",\n  \"translate\", \"translate3d\", \"translateX\", \"translateY\", \"translateZ\",\n  \"transparent\", \"ultra-condensed\", \"ultra-expanded\", \"underline\", \"unidirectional-pan\", \"unset\", \"up\",\n  \"upper-alpha\", \"upper-armenian\", \"upper-greek\", \"upper-hexadecimal\",\n  \"upper-latin\", \"upper-norwegian\", \"upper-roman\", \"uppercase\", \"urdu\", \"url\",\n  \"var\", \"vertical\", \"vertical-text\", \"view-box\", \"visible\", \"visibleFill\", \"visiblePainted\",\n  \"visibleStroke\", \"visual\", \"w-resize\", \"wait\", \"wave\", \"wider\",\n  \"window\", \"windowframe\", \"windowtext\", \"words\", \"wrap\", \"wrap-reverse\", \"x-large\", \"x-small\", \"xor\",\n  \"xx-large\", \"xx-small\"\n], valueKeywords = keySet(valueKeywords_);\n\nvar allWords = documentTypes_.concat(mediaTypes_).concat(mediaFeatures_).concat(mediaValueKeywords_)\n    .concat(propertyKeywords_).concat(nonStandardPropertyKeywords_).concat(colorKeywords_)\n    .concat(valueKeywords_);\n\nconst keywords = {properties: propertyKeywords_, colors: colorKeywords_,\n                         fonts: fontProperties_, values: valueKeywords_, all: allWords}\n\nconst defaults = {\n  documentTypes: documentTypes,\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  fontProperties: fontProperties,\n  counterDescriptors: counterDescriptors,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (!stream.eat(\"*\")) return false;\n      state.tokenize = tokenCComment;\n      return tokenCComment(stream, state);\n    }\n  }\n}\n\nconst css = mkCSS({name: \"css\"})\n\nfunction tokenCComment(stream, state) {\n  var maybeEnd = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return [\"comment\", \"comment\"];\n}\n\nconst sCSS = mkCSS({\n  name: \"scss\",\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  fontProperties: fontProperties,\n  allowNested: true,\n  lineComment: \"//\",\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return [\"comment\", \"comment\"];\n      } else if (stream.eat(\"*\")) {\n        state.tokenize = tokenCComment;\n        return tokenCComment(stream, state);\n      } else {\n        return [\"operator\", \"operator\"];\n      }\n    },\n    \":\": function(stream) {\n      if (stream.match(/^\\s*\\{/, false))\n        return [null, null]\n      return false;\n    },\n    \"$\": function(stream) {\n      stream.match(/^[\\w-]+/);\n      if (stream.match(/^\\s*:/, false))\n        return [\"def\", \"variable-definition\"];\n      return [\"variableName.special\", \"variable\"];\n    },\n    \"#\": function(stream) {\n      if (!stream.eat(\"{\")) return false;\n      return [null, \"interpolation\"];\n    }\n  }\n})\n\nconst less = mkCSS({\n  name: \"less\",\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  mediaValueKeywords: mediaValueKeywords,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  fontProperties: fontProperties,\n  allowNested: true,\n  lineComment: \"//\",\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (stream.eat(\"/\")) {\n        stream.skipToEnd();\n        return [\"comment\", \"comment\"];\n      } else if (stream.eat(\"*\")) {\n        state.tokenize = tokenCComment;\n        return tokenCComment(stream, state);\n      } else {\n        return [\"operator\", \"operator\"];\n      }\n    },\n    \"@\": function(stream) {\n      if (stream.eat(\"{\")) return [null, \"interpolation\"];\n      if (stream.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\\b/i, false)) return false;\n      stream.eatWhile(/[\\w\\\\\\-]/);\n      if (stream.match(/^\\s*:/, false))\n        return [\"def\", \"variable-definition\"];\n      return [\"variableName\", \"variable\"];\n    },\n    \"&\": function() {\n      return [\"atom\", \"atom\"];\n    }\n  }\n})\n\nconst gss = mkCSS({\n  name: \"gss\",\n  documentTypes: documentTypes,\n  mediaTypes: mediaTypes,\n  mediaFeatures: mediaFeatures,\n  propertyKeywords: propertyKeywords,\n  nonStandardPropertyKeywords: nonStandardPropertyKeywords,\n  fontProperties: fontProperties,\n  counterDescriptors: counterDescriptors,\n  colorKeywords: colorKeywords,\n  valueKeywords: valueKeywords,\n  supportsAtComponent: true,\n  tokenHooks: {\n    \"/\": function(stream, state) {\n      if (!stream.eat(\"*\")) return false;\n      state.tokenize = tokenCComment;\n      return tokenCComment(stream, state);\n    }\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/css.js\n"));

/***/ })

}]);