import os
import json
import httpx
import asyncio
from typing import Optional, Dict, List, Any, Union
from datetime import datetime, timedelta

from agentpress.tool import Tool, ToolResult, openapi_schema, xml_schema
from utils.logger import logger

class GitHubTool(Tool):
    """Tool for interacting with GitHub API to get trending repositories, developers, and other GitHub data."""
    
    def __init__(self):
        """Initialize GitHub tool with API token from environment."""
        super().__init__()
        self.api_base_url = "https://api.github.com"
        self.github_token = os.getenv("GITHUB_TOKEN", "")
        self.headers = {
            "Accept": "application/vnd.github.v3+json",
        }
        
        # Add authorization header if token is available
        if self.github_token:
            self.headers["Authorization"] = f"token {self.github_token}"
            
        # For trending data (not directly available through GitHub API)
        self.trending_base_url = "https://github-trending-api.now.sh"
    
    async def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None, data: Optional[Dict] = None) -> Dict:
        """Make a request to the GitHub API."""
        try:
            url = f"{self.api_base_url}{endpoint}" if endpoint.startswith("/") else f"{self.api_base_url}/{endpoint}"
            
            async with httpx.AsyncClient() as client:
                if method.upper() == "GET":
                    response = await client.get(url, headers=self.headers, params=params, timeout=30)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=self.headers, json=data, timeout=30)
                else:
                    return {"success": False, "error": f"Unsupported method: {method}"}
                
                response.raise_for_status()
                return {"success": True, "data": response.json()}
                
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error: {e.response.status_code} - {e.response.text}")
            return {"success": False, "error": f"HTTP error: {e.response.status_code}", "details": e.response.text}
        except Exception as e:
            logger.error(f"Request error: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _make_trending_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict:
        """Make a request to the GitHub trending API."""
        try:
            url = f"{self.trending_base_url}{endpoint}"
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params, timeout=30)
                response.raise_for_status()
                return {"success": True, "data": response.json()}
                
        except Exception as e:
            logger.error(f"Trending API request error: {str(e)}")
            return {"success": False, "error": str(e)}
    
    @openapi_schema({
        "type": "function",
        "function": {
            "name": "get_trending_repositories",
            "description": "Get trending repositories on GitHub for a specific language and time period",
            "parameters": {
                "type": "object",
                "properties": {
                    "language": {
                        "type": "string",
                        "description": "Programming language (e.g., 'python', 'javascript', 'go'). Leave empty for all languages."
                    },
                    "since": {
                        "type": "string",
                        "description": "Time period for trending repositories",
                        "enum": ["daily", "weekly", "monthly"],
                        "default": "daily"
                    },
                    "spoken_language": {
                        "type": "string",
                        "description": "Filter by spoken language (e.g., 'en' for English)"
                    }
                }
            }
        }
    })
    @xml_schema(
        tag_name="get-trending-repositories",
        mappings=[
            {"param_name": "language", "node_type": "attribute", "path": "language"},
            {"param_name": "since", "node_type": "attribute", "path": "since"},
            {"param_name": "spoken_language", "node_type": "attribute", "path": "spoken_language"}
        ],
        example='''
        <get-trending-repositories language="python" since="weekly">
        </get-trending-repositories>
        '''
    )
    async def get_trending_repositories(self, language: str = "", since: str = "daily", spoken_language: str = "") -> ToolResult:
        """
        Get trending repositories on GitHub for a specific language and time period.
        
        Args:
            language: Programming language (e.g., 'python', 'javascript', 'go'). Leave empty for all languages.
            since: Time period for trending repositories ('daily', 'weekly', 'monthly')
            spoken_language: Filter by spoken language (e.g., 'en' for English)
            
        Returns:
            List of trending repositories with details
        """
        try:
            params = {}
            if language:
                params["language"] = language
            if since:
                params["since"] = since
            if spoken_language:
                params["spoken_language_code"] = spoken_language
                
            result = await self._make_trending_request("/repositories", params)
            
            if result["success"]:
                # Format the response for better readability
                formatted_repos = []
                for repo in result["data"]:
                    formatted_repo = {
                        "name": repo.get("name", ""),
                        "author": repo.get("author", ""),
                        "url": repo.get("url", ""),
                        "description": repo.get("description", ""),
                        "language": repo.get("language", ""),
                        "stars": repo.get("stars", 0),
                        "forks": repo.get("forks", 0),
                        "current_period_stars": repo.get("currentPeriodStars", 0)
                    }
                    formatted_repos.append(formatted_repo)
                
                return self.success_response(formatted_repos)
            else:
                return self.fail_response(f"Failed to get trending repositories: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            return self.fail_response(f"Error getting trending repositories: {str(e)}")
    
    @openapi_schema({
        "type": "function",
        "function": {
            "name": "get_trending_developers",
            "description": "Get trending developers on GitHub for a specific language and time period",
            "parameters": {
                "type": "object",
                "properties": {
                    "language": {
                        "type": "string",
                        "description": "Programming language (e.g., 'python', 'javascript', 'go'). Leave empty for all languages."
                    },
                    "since": {
                        "type": "string",
                        "description": "Time period for trending developers",
                        "enum": ["daily", "weekly", "monthly"],
                        "default": "daily"
                    }
                }
            }
        }
    })
    @xml_schema(
        tag_name="get-trending-developers",
        mappings=[
            {"param_name": "language", "node_type": "attribute", "path": "language"},
            {"param_name": "since", "node_type": "attribute", "path": "since"}
        ],
        example='''
        <get-trending-developers language="javascript" since="weekly">
        </get-trending-developers>
        '''
    )
    async def get_trending_developers(self, language: str = "", since: str = "daily") -> ToolResult:
        """
        Get trending developers on GitHub for a specific language and time period.
        
        Args:
            language: Programming language (e.g., 'python', 'javascript', 'go'). Leave empty for all languages.
            since: Time period for trending developers ('daily', 'weekly', 'monthly')
            
        Returns:
            List of trending developers with details
        """
        try:
            params = {}
            if language:
                params["language"] = language
            if since:
                params["since"] = since
                
            result = await self._make_trending_request("/developers", params)
            
            if result["success"]:
                # Format the response for better readability
                formatted_devs = []
                for dev in result["data"]:
                    formatted_dev = {
                        "username": dev.get("username", ""),
                        "name": dev.get("name", ""),
                        "url": dev.get("url", ""),
                        "avatar": dev.get("avatar", ""),
                        "repository": {
                            "name": dev.get("repo", {}).get("name", ""),
                            "description": dev.get("repo", {}).get("description", ""),
                            "url": dev.get("repo", {}).get("url", "")
                        }
                    }
                    formatted_devs.append(formatted_dev)
                
                return self.success_response(formatted_devs)
            else:
                return self.fail_response(f"Failed to get trending developers: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            return self.fail_response(f"Error getting trending developers: {str(e)}")
    
    @openapi_schema({
        "type": "function",
        "function": {
            "name": "get_repository_stars",
            "description": "Get users who starred a specific repository",
            "parameters": {
                "type": "object",
                "properties": {
                    "owner": {
                        "type": "string",
                        "description": "Repository owner (username or organization)"
                    },
                    "repo": {
                        "type": "string",
                        "description": "Repository name"
                    },
                    "per_page": {
                        "type": "integer",
                        "description": "Number of results per page (max 100)",
                        "default": 30
                    },
                    "page": {
                        "type": "integer",
                        "description": "Page number for pagination",
                        "default": 1
                    }
                },
                "required": ["owner", "repo"]
            }
        }
    })
    @xml_schema(
        tag_name="get-repository-stars",
        mappings=[
            {"param_name": "owner", "node_type": "attribute", "path": "owner"},
            {"param_name": "repo", "node_type": "attribute", "path": "repo"},
            {"param_name": "per_page", "node_type": "attribute", "path": "per_page"},
            {"param_name": "page", "node_type": "attribute", "path": "page"}
        ],
        example='''
        <get-repository-stars owner="facebook" repo="react" per_page="50" page="1">
        </get-repository-stars>
        '''
    )
    async def get_repository_stars(self, owner: str, repo: str, per_page: int = 30, page: int = 1) -> ToolResult:
        """
        Get users who starred a specific repository.
        
        Args:
            owner: Repository owner (username or organization)
            repo: Repository name
            per_page: Number of results per page (max 100)
            page: Page number for pagination
            
        Returns:
            List of users who starred the repository
        """
        try:
            if not owner or not repo:
                return self.fail_response("Repository owner and name are required")
                
            params = {
                "per_page": min(100, max(1, per_page)),
                "page": max(1, page)
            }
                
            result = await self._make_request("GET", f"/repos/{owner}/{repo}/stargazers", params=params)
            
            if result["success"]:
                # Format the response for better readability
                formatted_users = []
                for user in result["data"]:
                    formatted_user = {
                        "login": user.get("login", ""),
                        "id": user.get("id", 0),
                        "avatar_url": user.get("avatar_url", ""),
                        "html_url": user.get("html_url", ""),
                        "type": user.get("type", "")
                    }
                    formatted_users.append(formatted_user)
                
                return self.success_response(formatted_users)
            else:
                return self.fail_response(f"Failed to get repository stars: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            return self.fail_response(f"Error getting repository stars: {str(e)}")
    
    @openapi_schema({
        "type": "function",
        "function": {
            "name": "get_user_profile",
            "description": "Get detailed information about a GitHub user profile",
            "parameters": {
                "type": "object",
                "properties": {
                    "username": {
                        "type": "string",
                        "description": "GitHub username"
                    }
                },
                "required": ["username"]
            }
        }
    })
    @xml_schema(
        tag_name="get-user-profile",
        mappings=[
            {"param_name": "username", "node_type": "attribute", "path": "username"}
        ],
        example='''
        <get-user-profile username="octocat">
        </get-user-profile>
        '''
    )
    async def get_user_profile(self, username: str) -> ToolResult:
        """
        Get detailed information about a GitHub user profile.
        
        Args:
            username: GitHub username
            
        Returns:
            Detailed user profile information
        """
        try:
            if not username:
                return self.fail_response("Username is required")
                
            result = await self._make_request("GET", f"/users/{username}")
            
            if result["success"]:
                user_data = result["data"]
                # Format the response for better readability
                formatted_user = {
                    "login": user_data.get("login", ""),
                    "id": user_data.get("id", 0),
                    "name": user_data.get("name", ""),
                    "company": user_data.get("company", ""),
                    "blog": user_data.get("blog", ""),
                    "location": user_data.get("location", ""),
                    "email": user_data.get("email", ""),
                    "bio": user_data.get("bio", ""),
                    "twitter_username": user_data.get("twitter_username", ""),
                    "public_repos": user_data.get("public_repos", 0),
                    "public_gists": user_data.get("public_gists", 0),
                    "followers": user_data.get("followers", 0),
                    "following": user_data.get("following", 0),
                    "created_at": user_data.get("created_at", ""),
                    "updated_at": user_data.get("updated_at", ""),
                    "html_url": user_data.get("html_url", ""),
                    "avatar_url": user_data.get("avatar_url", "")
                }
                
                return self.success_response(formatted_user)
            else:
                return self.fail_response(f"Failed to get user profile: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            return self.fail_response(f"Error getting user profile: {str(e)}")
    
    @openapi_schema({
        "type": "function",
        "function": {
            "name": "search_repositories",
            "description": "Search for repositories on GitHub based on various criteria",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query (e.g., 'machine learning language:python')"
                    },
                    "sort": {
                        "type": "string",
                        "description": "Sort results by",
                        "enum": ["stars", "forks", "help-wanted-issues", "updated"],
                        "default": "stars"
                    },
                    "order": {
                        "type": "string",
                        "description": "Sort order",
                        "enum": ["desc", "asc"],
                        "default": "desc"
                    },
                    "per_page": {
                        "type": "integer",
                        "description": "Number of results per page (max 100)",
                        "default": 30
                    },
                    "page": {
                        "type": "integer",
                        "description": "Page number for pagination",
                        "default": 1
                    }
                },
                "required": ["query"]
            }
        }
    })
    @xml_schema(
        tag_name="search-repositories",
        mappings=[
            {"param_name": "query", "node_type": "attribute", "path": "query"},
            {"param_name": "sort", "node_type": "attribute", "path": "sort"},
            {"param_name": "order", "node_type": "attribute", "path": "order"},
            {"param_name": "per_page", "node_type": "attribute", "path": "per_page"},
            {"param_name": "page", "node_type": "attribute", "path": "page"}
        ],
        example='''
        <search-repositories query="machine learning language:python" sort="stars" order="desc" per_page="20" page="1">
        </search-repositories>
        '''
    )
    async def search_repositories(self, query: str, sort: str = "stars", order: str = "desc", 
                                 per_page: int = 30, page: int = 1) -> ToolResult:
        """
        Search for repositories on GitHub based on various criteria.
        
        Args:
            query: Search query (e.g., 'machine learning language:python')
            sort: Sort results by ('stars', 'forks', 'help-wanted-issues', 'updated')
            order: Sort order ('desc', 'asc')
            per_page: Number of results per page (max 100)
            page: Page number for pagination
            
        Returns:
            List of repositories matching the search criteria
        """
        try:
            if not query:
                return self.fail_response("Search query is required")
                
            params = {
                "q": query,
                "sort": sort,
                "order": order,
                "per_page": min(100, max(1, per_page)),
                "page": max(1, page)
            }
                
            result = await self._make_request("GET", "/search/repositories", params=params)
            
            if result["success"]:
                # Format the response for better readability
                total_count = result["data"].get("total_count", 0)
                items = result["data"].get("items", [])
                
                formatted_repos = []
                for repo in items:
                    formatted_repo = {
                        "name": repo.get("name", ""),
                        "full_name": repo.get("full_name", ""),
                        "html_url": repo.get("html_url", ""),
                        "description": repo.get("description", ""),
                        "owner": {
                            "login": repo.get("owner", {}).get("login", ""),
                            "avatar_url": repo.get("owner", {}).get("avatar_url", ""),
                            "html_url": repo.get("owner", {}).get("html_url", "")
                        },
                        "language": repo.get("language", ""),
                        "stargazers_count": repo.get("stargazers_count", 0),
                        "forks_count": repo.get("forks_count", 0),
                        "open_issues_count": repo.get("open_issues_count", 0),
                        "topics": repo.get("topics", []),
                        "created_at": repo.get("created_at", ""),
                        "updated_at": repo.get("updated_at", ""),
                        "license": repo.get("license", {}).get("name", "") if repo.get("license") else ""
                    }
                    formatted_repos.append(formatted_repo)
                
                return self.success_response({
                    "total_count": total_count,
                    "repositories": formatted_repos
                })
            else:
                return self.fail_response(f"Failed to search repositories: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            return self.fail_response(f"Error searching repositories: {str(e)}")
    
    @openapi_schema({
        "type": "function",
        "function": {
            "name": "get_repository_stats",
            "description": "Get detailed statistics for a specific repository",
            "parameters": {
                "type": "object",
                "properties": {
                    "owner": {
                        "type": "string",
                        "description": "Repository owner (username or organization)"
                    },
                    "repo": {
                        "type": "string",
                        "description": "Repository name"
                    }
                },
                "required": ["owner", "repo"]
            }
        }
    })
    @xml_schema(
        tag_name="get-repository-stats",
        mappings=[
            {"param_name": "owner", "node_type": "attribute", "path": "owner"},
            {"param_name": "repo", "node_type": "attribute", "path": "repo"}
        ],
        example='''
        <get-repository-stats owner="tensorflow" repo="tensorflow">
        </get-repository-stats>
        '''
    )
    async def get_repository_stats(self, owner: str, repo: str) -> ToolResult:
        """
        Get detailed statistics for a specific repository.
        
        Args:
            owner: Repository owner (username or organization)
            repo: Repository name
            
        Returns:
            Detailed repository statistics
        """
        try:
            if not owner or not repo:
                return self.fail_response("Repository owner and name are required")
            
            # Make multiple API calls in parallel to get comprehensive repository data
            tasks = [
                self._make_request("GET", f"/repos/{owner}/{repo}"),  # Basic repo info
                self._make_request("GET", f"/repos/{owner}/{repo}/languages"),  # Language breakdown
                self._make_request("GET", f"/repos/{owner}/{repo}/contributors", {"per_page": 10}),  # Top contributors
                self._make_request("GET", f"/repos/{owner}/{repo}/commits", {"per_page": 1}),  # Latest commit
                self._make_request("GET", f"/repos/{owner}/{repo}/releases", {"per_page": 5})  # Recent releases
            ]
            
            results = await asyncio.gather(*tasks)
            
            # Check if all requests were successful
            if not all(result["success"] for result in results):
                failed_results = [result.get("error", "Unknown error") for result in results if not result["success"]]
                return self.fail_response(f"Failed to get repository stats: {', '.join(failed_results)}")
            
            # Extract data from results
            repo_info = results[0]["data"]
            languages = results[1]["data"]
            contributors = results[2]["data"]
            latest_commit = results[3]["data"][0] if results[3]["data"] else None
            releases = results[4]["data"]
            
            # Format the response
            formatted_stats = {
                "name": repo_info.get("name", ""),
                "full_name": repo_info.get("full_name", ""),
                "description": repo_info.get("description", ""),
                "html_url": repo_info.get("html_url", ""),
                "owner": {
                    "login": repo_info.get("owner", {}).get("login", ""),
                    "avatar_url": repo_info.get("owner", {}).get("avatar_url", ""),
                    "html_url": repo_info.get("owner", {}).get("html_url", "")
                },
                "stats": {
                    "stargazers_count": repo_info.get("stargazers_count", 0),
                    "watchers_count": repo_info.get("watchers_count", 0),
                    "forks_count": repo_info.get("forks_count", 0),
                    "open_issues_count": repo_info.get("open_issues_count", 0),
                    "subscribers_count": repo_info.get("subscribers_count", 0),
                    "network_count": repo_info.get("network_count", 0),
                    "size": repo_info.get("size", 0)
                },
                "languages": languages,
                "top_contributors": [
                    {
                        "login": contributor.get("login", ""),
                        "avatar_url": contributor.get("avatar_url", ""),
                        "html_url": contributor.get("html_url", ""),
                        "contributions": contributor.get("contributions", 0)
                    } for contributor in contributors
                ],
                "latest_commit": {
                    "sha": latest_commit.get("sha", "") if latest_commit else "",
                    "message": latest_commit.get("commit", {}).get("message", "") if latest_commit else "",
                    "author": latest_commit.get("commit", {}).get("author", {}).get("name", "") if latest_commit else "",
                    "date": latest_commit.get("commit", {}).get("author", {}).get("date", "") if latest_commit else ""
                } if latest_commit else {},
                "recent_releases": [
                    {
                        "name": release.get("name", ""),
                        "tag_name": release.get("tag_name", ""),
                        "published_at": release.get("published_at", ""),
                        "html_url": release.get("html_url", "")
                    } for release in releases
                ],
                "created_at": repo_info.get("created_at", ""),
                "updated_at": repo_info.get("updated_at", ""),
                "pushed_at": repo_info.get("pushed_at", ""),
                "default_branch": repo_info.get("default_branch", ""),
                "license": repo_info.get("license", {}).get("name", "") if repo_info.get("license") else "",
                "topics": repo_info.get("topics", [])
            }
            
            return self.success_response(formatted_stats)
                
        except Exception as e:
            return self.fail_response(f"Error getting repository stats: {str(e)}")


if __name__ == "__main__":
    import asyncio
    
    async def test_github_tool():
        """Test function for the GitHub tool"""
        github_tool = GitHubTool()
        
        # Test trending repositories
        print("\nTesting get_trending_repositories...")
        result = await github_tool.get_trending_repositories(language="python", since="weekly")
        print(json.dumps(result.output, indent=2))
        
        # Test trending developers
        print("\nTesting get_trending_developers...")
        result = await github_tool.get_trending_developers(language="javascript")
        print(json.dumps(result.output, indent=2))
        
        # Test repository search
        print("\nTesting search_repositories...")
        result = await github_tool.search_repositories(query="machine learning language:python", per_page=5)
        print(json.dumps(result.output, indent=2))
        
        # Test user profile
        print("\nTesting get_user_profile...")
        result = await github_tool.get_user_profile(username="octocat")
        print(json.dumps(result.output, indent=2))
        
    asyncio.run(test_github_tool())
