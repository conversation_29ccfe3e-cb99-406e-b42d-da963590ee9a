"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_codemirror_legacy-modes_6_5_1_node_modules_codemirror_le-34d27c"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/clojure.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/clojure.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clojure: () => (/* binding */ clojure)\n/* harmony export */ });\nvar atoms = [\"false\", \"nil\", \"true\"];\nvar specialForms = [\".\", \"catch\", \"def\", \"do\", \"if\", \"monitor-enter\",\n                    \"monitor-exit\", \"new\", \"quote\", \"recur\", \"set!\", \"throw\", \"try\", \"var\"];\nvar coreSymbols = [\"*\", \"*'\", \"*1\", \"*2\", \"*3\", \"*agent*\",\n                   \"*allow-unresolved-vars*\", \"*assert*\", \"*clojure-version*\",\n                   \"*command-line-args*\", \"*compile-files*\", \"*compile-path*\",\n                   \"*compiler-options*\", \"*data-readers*\", \"*default-data-reader-fn*\", \"*e\",\n                   \"*err*\", \"*file*\", \"*flush-on-newline*\", \"*fn-loader*\", \"*in*\",\n                   \"*math-context*\", \"*ns*\", \"*out*\", \"*print-dup*\", \"*print-length*\",\n                   \"*print-level*\", \"*print-meta*\", \"*print-namespace-maps*\",\n                   \"*print-readably*\", \"*read-eval*\", \"*reader-resolver*\", \"*source-path*\",\n                   \"*suppress-read*\", \"*unchecked-math*\", \"*use-context-classloader*\",\n                   \"*verbose-defrecords*\", \"*warn-on-reflection*\", \"+\", \"+'\", \"-\", \"-'\",\n                   \"->\", \"->>\", \"->ArrayChunk\", \"->Eduction\", \"->Vec\", \"->VecNode\",\n                   \"->VecSeq\", \"-cache-protocol-fn\", \"-reset-methods\", \"..\", \"/\", \"<\", \"<=\",\n                   \"=\", \"==\", \">\", \">=\", \"EMPTY-NODE\", \"Inst\", \"StackTraceElement->vec\",\n                   \"Throwable->map\", \"accessor\", \"aclone\", \"add-classpath\", \"add-watch\",\n                   \"agent\", \"agent-error\", \"agent-errors\", \"aget\", \"alength\", \"alias\",\n                   \"all-ns\", \"alter\", \"alter-meta!\", \"alter-var-root\", \"amap\", \"ancestors\",\n                   \"and\", \"any?\", \"apply\", \"areduce\", \"array-map\", \"as->\", \"aset\",\n                   \"aset-boolean\", \"aset-byte\", \"aset-char\", \"aset-double\", \"aset-float\",\n                   \"aset-int\", \"aset-long\", \"aset-short\", \"assert\", \"assoc\", \"assoc!\",\n                   \"assoc-in\", \"associative?\", \"atom\", \"await\", \"await-for\", \"await1\",\n                   \"bases\", \"bean\", \"bigdec\", \"bigint\", \"biginteger\", \"binding\", \"bit-and\",\n                   \"bit-and-not\", \"bit-clear\", \"bit-flip\", \"bit-not\", \"bit-or\", \"bit-set\",\n                   \"bit-shift-left\", \"bit-shift-right\", \"bit-test\", \"bit-xor\", \"boolean\",\n                   \"boolean-array\", \"boolean?\", \"booleans\", \"bound-fn\", \"bound-fn*\",\n                   \"bound?\", \"bounded-count\", \"butlast\", \"byte\", \"byte-array\", \"bytes\",\n                   \"bytes?\", \"case\", \"cast\", \"cat\", \"char\", \"char-array\",\n                   \"char-escape-string\", \"char-name-string\", \"char?\", \"chars\", \"chunk\",\n                   \"chunk-append\", \"chunk-buffer\", \"chunk-cons\", \"chunk-first\", \"chunk-next\",\n                   \"chunk-rest\", \"chunked-seq?\", \"class\", \"class?\", \"clear-agent-errors\",\n                   \"clojure-version\", \"coll?\", \"comment\", \"commute\", \"comp\", \"comparator\",\n                   \"compare\", \"compare-and-set!\", \"compile\", \"complement\", \"completing\",\n                   \"concat\", \"cond\", \"cond->\", \"cond->>\", \"condp\", \"conj\", \"conj!\", \"cons\",\n                   \"constantly\", \"construct-proxy\", \"contains?\", \"count\", \"counted?\",\n                   \"create-ns\", \"create-struct\", \"cycle\", \"dec\", \"dec'\", \"decimal?\",\n                   \"declare\", \"dedupe\", \"default-data-readers\", \"definline\", \"definterface\",\n                   \"defmacro\", \"defmethod\", \"defmulti\", \"defn\", \"defn-\", \"defonce\",\n                   \"defprotocol\", \"defrecord\", \"defstruct\", \"deftype\", \"delay\", \"delay?\",\n                   \"deliver\", \"denominator\", \"deref\", \"derive\", \"descendants\", \"destructure\",\n                   \"disj\", \"disj!\", \"dissoc\", \"dissoc!\", \"distinct\", \"distinct?\", \"doall\",\n                   \"dorun\", \"doseq\", \"dosync\", \"dotimes\", \"doto\", \"double\", \"double-array\",\n                   \"double?\", \"doubles\", \"drop\", \"drop-last\", \"drop-while\", \"eduction\",\n                   \"empty\", \"empty?\", \"ensure\", \"ensure-reduced\", \"enumeration-seq\",\n                   \"error-handler\", \"error-mode\", \"eval\", \"even?\", \"every-pred\", \"every?\",\n                   \"ex-data\", \"ex-info\", \"extend\", \"extend-protocol\", \"extend-type\",\n                   \"extenders\", \"extends?\", \"false?\", \"ffirst\", \"file-seq\", \"filter\",\n                   \"filterv\", \"find\", \"find-keyword\", \"find-ns\", \"find-protocol-impl\",\n                   \"find-protocol-method\", \"find-var\", \"first\", \"flatten\", \"float\",\n                   \"float-array\", \"float?\", \"floats\", \"flush\", \"fn\", \"fn?\", \"fnext\", \"fnil\",\n                   \"for\", \"force\", \"format\", \"frequencies\", \"future\", \"future-call\",\n                   \"future-cancel\", \"future-cancelled?\", \"future-done?\", \"future?\",\n                   \"gen-class\", \"gen-interface\", \"gensym\", \"get\", \"get-in\", \"get-method\",\n                   \"get-proxy-class\", \"get-thread-bindings\", \"get-validator\", \"group-by\",\n                   \"halt-when\", \"hash\", \"hash-combine\", \"hash-map\", \"hash-ordered-coll\",\n                   \"hash-set\", \"hash-unordered-coll\", \"ident?\", \"identical?\", \"identity\",\n                   \"if-let\", \"if-not\", \"if-some\", \"ifn?\", \"import\", \"in-ns\", \"inc\", \"inc'\",\n                   \"indexed?\", \"init-proxy\", \"inst-ms\", \"inst-ms*\", \"inst?\", \"instance?\",\n                   \"int\", \"int-array\", \"int?\", \"integer?\", \"interleave\", \"intern\",\n                   \"interpose\", \"into\", \"into-array\", \"ints\", \"io!\", \"isa?\", \"iterate\",\n                   \"iterator-seq\", \"juxt\", \"keep\", \"keep-indexed\", \"key\", \"keys\", \"keyword\",\n                   \"keyword?\", \"last\", \"lazy-cat\", \"lazy-seq\", \"let\", \"letfn\", \"line-seq\",\n                   \"list\", \"list*\", \"list?\", \"load\", \"load-file\", \"load-reader\",\n                   \"load-string\", \"loaded-libs\", \"locking\", \"long\", \"long-array\", \"longs\",\n                   \"loop\", \"macroexpand\", \"macroexpand-1\", \"make-array\", \"make-hierarchy\",\n                   \"map\", \"map-entry?\", \"map-indexed\", \"map?\", \"mapcat\", \"mapv\", \"max\",\n                   \"max-key\", \"memfn\", \"memoize\", \"merge\", \"merge-with\", \"meta\",\n                   \"method-sig\", \"methods\", \"min\", \"min-key\", \"mix-collection-hash\", \"mod\",\n                   \"munge\", \"name\", \"namespace\", \"namespace-munge\", \"nat-int?\", \"neg-int?\",\n                   \"neg?\", \"newline\", \"next\", \"nfirst\", \"nil?\", \"nnext\", \"not\", \"not-any?\",\n                   \"not-empty\", \"not-every?\", \"not=\", \"ns\", \"ns-aliases\", \"ns-imports\",\n                   \"ns-interns\", \"ns-map\", \"ns-name\", \"ns-publics\", \"ns-refers\",\n                   \"ns-resolve\", \"ns-unalias\", \"ns-unmap\", \"nth\", \"nthnext\", \"nthrest\",\n                   \"num\", \"number?\", \"numerator\", \"object-array\", \"odd?\", \"or\", \"parents\",\n                   \"partial\", \"partition\", \"partition-all\", \"partition-by\", \"pcalls\", \"peek\",\n                   \"persistent!\", \"pmap\", \"pop\", \"pop!\", \"pop-thread-bindings\", \"pos-int?\",\n                   \"pos?\", \"pr\", \"pr-str\", \"prefer-method\", \"prefers\",\n                   \"primitives-classnames\", \"print\", \"print-ctor\", \"print-dup\",\n                   \"print-method\", \"print-simple\", \"print-str\", \"printf\", \"println\",\n                   \"println-str\", \"prn\", \"prn-str\", \"promise\", \"proxy\",\n                   \"proxy-call-with-super\", \"proxy-mappings\", \"proxy-name\", \"proxy-super\",\n                   \"push-thread-bindings\", \"pvalues\", \"qualified-ident?\",\n                   \"qualified-keyword?\", \"qualified-symbol?\", \"quot\", \"rand\", \"rand-int\",\n                   \"rand-nth\", \"random-sample\", \"range\", \"ratio?\", \"rational?\",\n                   \"rationalize\", \"re-find\", \"re-groups\", \"re-matcher\", \"re-matches\",\n                   \"re-pattern\", \"re-seq\", \"read\", \"read-line\", \"read-string\",\n                   \"reader-conditional\", \"reader-conditional?\", \"realized?\", \"record?\",\n                   \"reduce\", \"reduce-kv\", \"reduced\", \"reduced?\", \"reductions\", \"ref\",\n                   \"ref-history-count\", \"ref-max-history\", \"ref-min-history\", \"ref-set\",\n                   \"refer\", \"refer-clojure\", \"reify\", \"release-pending-sends\", \"rem\",\n                   \"remove\", \"remove-all-methods\", \"remove-method\", \"remove-ns\",\n                   \"remove-watch\", \"repeat\", \"repeatedly\", \"replace\", \"replicate\", \"require\",\n                   \"reset!\", \"reset-meta!\", \"reset-vals!\", \"resolve\", \"rest\",\n                   \"restart-agent\", \"resultset-seq\", \"reverse\", \"reversible?\", \"rseq\",\n                   \"rsubseq\", \"run!\", \"satisfies?\", \"second\", \"select-keys\", \"send\",\n                   \"send-off\", \"send-via\", \"seq\", \"seq?\", \"seqable?\", \"seque\", \"sequence\",\n                   \"sequential?\", \"set\", \"set-agent-send-executor!\",\n                   \"set-agent-send-off-executor!\", \"set-error-handler!\", \"set-error-mode!\",\n                   \"set-validator!\", \"set?\", \"short\", \"short-array\", \"shorts\", \"shuffle\",\n                   \"shutdown-agents\", \"simple-ident?\", \"simple-keyword?\", \"simple-symbol?\",\n                   \"slurp\", \"some\", \"some->\", \"some->>\", \"some-fn\", \"some?\", \"sort\",\n                   \"sort-by\", \"sorted-map\", \"sorted-map-by\", \"sorted-set\", \"sorted-set-by\",\n                   \"sorted?\", \"special-symbol?\", \"spit\", \"split-at\", \"split-with\", \"str\",\n                   \"string?\", \"struct\", \"struct-map\", \"subs\", \"subseq\", \"subvec\", \"supers\",\n                   \"swap!\", \"swap-vals!\", \"symbol\", \"symbol?\", \"sync\", \"tagged-literal\",\n                   \"tagged-literal?\", \"take\", \"take-last\", \"take-nth\", \"take-while\", \"test\",\n                   \"the-ns\", \"thread-bound?\", \"time\", \"to-array\", \"to-array-2d\",\n                   \"trampoline\", \"transduce\", \"transient\", \"tree-seq\", \"true?\", \"type\",\n                   \"unchecked-add\", \"unchecked-add-int\", \"unchecked-byte\", \"unchecked-char\",\n                   \"unchecked-dec\", \"unchecked-dec-int\", \"unchecked-divide-int\",\n                   \"unchecked-double\", \"unchecked-float\", \"unchecked-inc\",\n                   \"unchecked-inc-int\", \"unchecked-int\", \"unchecked-long\",\n                   \"unchecked-multiply\", \"unchecked-multiply-int\", \"unchecked-negate\",\n                   \"unchecked-negate-int\", \"unchecked-remainder-int\", \"unchecked-short\",\n                   \"unchecked-subtract\", \"unchecked-subtract-int\", \"underive\", \"unquote\",\n                   \"unquote-splicing\", \"unreduced\", \"unsigned-bit-shift-right\", \"update\",\n                   \"update-in\", \"update-proxy\", \"uri?\", \"use\", \"uuid?\", \"val\", \"vals\",\n                   \"var-get\", \"var-set\", \"var?\", \"vary-meta\", \"vec\", \"vector\", \"vector-of\",\n                   \"vector?\", \"volatile!\", \"volatile?\", \"vreset!\", \"vswap!\", \"when\",\n                   \"when-first\", \"when-let\", \"when-not\", \"when-some\", \"while\",\n                   \"with-bindings\", \"with-bindings*\", \"with-in-str\", \"with-loading-context\",\n                   \"with-local-vars\", \"with-meta\", \"with-open\", \"with-out-str\",\n                   \"with-precision\", \"with-redefs\", \"with-redefs-fn\", \"xml-seq\", \"zero?\",\n                   \"zipmap\"];\nvar haveBodyParameter = [\n  \"->\", \"->>\", \"as->\", \"binding\", \"bound-fn\", \"case\", \"catch\", \"comment\",\n  \"cond\", \"cond->\", \"cond->>\", \"condp\", \"def\", \"definterface\", \"defmethod\",\n  \"defn\", \"defmacro\", \"defprotocol\", \"defrecord\", \"defstruct\", \"deftype\",\n  \"do\", \"doseq\", \"dotimes\", \"doto\", \"extend\", \"extend-protocol\",\n  \"extend-type\", \"fn\", \"for\", \"future\", \"if\", \"if-let\", \"if-not\", \"if-some\",\n  \"let\", \"letfn\", \"locking\", \"loop\", \"ns\", \"proxy\", \"reify\", \"struct-map\",\n  \"some->\", \"some->>\", \"try\", \"when\", \"when-first\", \"when-let\", \"when-not\",\n  \"when-some\", \"while\", \"with-bindings\", \"with-bindings*\", \"with-in-str\",\n  \"with-loading-context\", \"with-local-vars\", \"with-meta\", \"with-open\",\n  \"with-out-str\", \"with-precision\", \"with-redefs\", \"with-redefs-fn\"];\n\nvar atom = createLookupMap(atoms);\nvar specialForm = createLookupMap(specialForms);\nvar coreSymbol = createLookupMap(coreSymbols);\nvar hasBodyParameter = createLookupMap(haveBodyParameter);\nvar delimiter = /^(?:[\\\\\\[\\]\\s\"(),;@^`{}~]|$)/;\nvar numberLiteral = /^(?:[+\\-]?\\d+(?:(?:N|(?:[eE][+\\-]?\\d+))|(?:\\.?\\d*(?:M|(?:[eE][+\\-]?\\d+))?)|\\/\\d+|[xX][0-9a-fA-F]+|r[0-9a-zA-Z]+)?(?=[\\\\\\[\\]\\s\"#'(),;@^`{}~]|$))/;\nvar characterLiteral = /^(?:\\\\(?:backspace|formfeed|newline|return|space|tab|o[0-7]{3}|u[0-9A-Fa-f]{4}|x[0-9A-Fa-f]{4}|.)?(?=[\\\\\\[\\]\\s\"(),;@^`{}~]|$))/;\n\n// simple-namespace := /^[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~.][^\\\\\\[\\]\\s\"(),;@^`{}~.\\/]*/\n// simple-symbol    := /^(?:\\/|[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~][^\\\\\\[\\]\\s\"(),;@^`{}~]*)/\n// qualified-symbol := (<simple-namespace>(<.><simple-namespace>)*</>)?<simple-symbol>\nvar qualifiedSymbol = /^(?:(?:[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~.][^\\\\\\[\\]\\s\"(),;@^`{}~.\\/]*(?:\\.[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~.][^\\\\\\[\\]\\s\"(),;@^`{}~.\\/]*)*\\/)?(?:\\/|[^\\\\\\/\\[\\]\\d\\s\"#'(),;@^`{}~][^\\\\\\[\\]\\s\"(),;@^`{}~]*)*(?=[\\\\\\[\\]\\s\"(),;@^`{}~]|$))/;\n\nfunction base(stream, state) {\n  if (stream.eatSpace() || stream.eat(\",\")) return [\"space\", null];\n  if (stream.match(numberLiteral)) return [null, \"number\"];\n  if (stream.match(characterLiteral)) return [null, \"string.special\"];\n  if (stream.eat(/^\"/)) return (state.tokenize = inString)(stream, state);\n  if (stream.eat(/^[(\\[{]/)) return [\"open\", \"bracket\"];\n  if (stream.eat(/^[)\\]}]/)) return [\"close\", \"bracket\"];\n  if (stream.eat(/^;/)) {stream.skipToEnd(); return [\"space\", \"comment\"];}\n  if (stream.eat(/^[#'@^`~]/)) return [null, \"meta\"];\n\n  var matches = stream.match(qualifiedSymbol);\n  var symbol = matches && matches[0];\n\n  if (!symbol) {\n    // advance stream by at least one character so we don't get stuck.\n    stream.next();\n    stream.eatWhile(function (c) {return !is(c, delimiter);});\n    return [null, \"error\"];\n  }\n\n  if (symbol === \"comment\" && state.lastToken === \"(\")\n    return (state.tokenize = inComment)(stream, state);\n  if (is(symbol, atom) || symbol.charAt(0) === \":\") return [\"symbol\", \"atom\"];\n  if (is(symbol, specialForm) || is(symbol, coreSymbol)) return [\"symbol\", \"keyword\"];\n  if (state.lastToken === \"(\") return [\"symbol\", \"builtin\"]; // other operator\n\n  return [\"symbol\", \"variable\"];\n}\n\nfunction inString(stream, state) {\n  var escaped = false, next;\n\n  while (next = stream.next()) {\n    if (next === \"\\\"\" && !escaped) {state.tokenize = base; break;}\n    escaped = !escaped && next === \"\\\\\";\n  }\n\n  return [null, \"string\"];\n}\n\nfunction inComment(stream, state) {\n  var parenthesisCount = 1;\n  var next;\n\n  while (next = stream.next()) {\n    if (next === \")\") parenthesisCount--;\n    if (next === \"(\") parenthesisCount++;\n    if (parenthesisCount === 0) {\n      stream.backUp(1);\n      state.tokenize = base;\n      break;\n    }\n  }\n\n  return [\"space\", \"comment\"];\n}\n\nfunction createLookupMap(words) {\n  var obj = {};\n\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n\n  return obj;\n}\n\nfunction is(value, test) {\n  if (test instanceof RegExp) return test.test(value);\n  if (test instanceof Object) return test.propertyIsEnumerable(value);\n}\n\nconst clojure = {\n  name: \"clojure\",\n  startState: function () {\n    return {\n      ctx: {prev: null, start: 0, indentTo: 0},\n      lastToken: null,\n      tokenize: base\n    };\n  },\n\n  token: function (stream, state) {\n    if (stream.sol() && (typeof state.ctx.indentTo !== \"number\"))\n      state.ctx.indentTo = state.ctx.start + 1;\n\n    var typeStylePair = state.tokenize(stream, state);\n    var type = typeStylePair[0];\n    var style = typeStylePair[1];\n    var current = stream.current();\n\n    if (type !== \"space\") {\n      if (state.lastToken === \"(\" && state.ctx.indentTo === null) {\n        if (type === \"symbol\" && is(current, hasBodyParameter))\n          state.ctx.indentTo = state.ctx.start + stream.indentUnit;\n        else state.ctx.indentTo = \"next\";\n      } else if (state.ctx.indentTo === \"next\") {\n        state.ctx.indentTo = stream.column();\n      }\n\n      state.lastToken = current;\n    }\n\n    if (type === \"open\")\n      state.ctx = {prev: state.ctx, start: stream.column(), indentTo: null};\n    else if (type === \"close\") state.ctx = state.ctx.prev || state.ctx;\n\n    return style;\n  },\n\n  indent: function (state) {\n    var i = state.ctx.indentTo;\n\n    return (typeof i === \"number\") ?\n      i :\n      state.ctx.start + 1;\n  },\n\n  languageData: {\n    closeBrackets: {brackets: [\"(\", \"[\", \"{\", '\"']},\n    commentTokens: {line: \";;\"},\n    autocomplete: [].concat(atoms, specialForms, coreSymbols)\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/clojure.js\n"));

/***/ })

}]);