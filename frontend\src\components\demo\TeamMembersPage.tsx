'use client';

import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Users, Bot } from 'lucide-react';
import { TeamMemberCard } from '@/components/project/team-member-card';
import { AvailableAgentCard } from '@/components/project/available-agent-card';
import { useProjectTeam } from '@/hooks/use-project-team';

// Agent roles data
const agentRoles = {
  'ceo': {
    name: '<PERSON><PERSON>',
    role: 'CEO',
    imageSrc: '/roles/kenard.png',
    skills: ['Strategic planning', 'Team leadership'],
    description: 'Manages the team and makes strategic decisions',
    color: '#4f6bed'
  },
  'developer': {
    name: '<PERSON>',
    role: 'Developer',
    imageSrc: '/roles/alex.png',
    skills: ['Full-stack development', 'Code architecture'],
    description: 'Builds and maintains the codebase',
    color: '#5b5fc7'
  },
  'marketing': {
    name: '<PERSON>',
    role: 'Marketing Officer',
    imageSrc: '/roles/chloe.png',
    skills: ['Content creation', 'Campaign planning'],
    description: 'Creates and executes marketing strategies',
    color: '#8561c5'
  },
  'product': {
    name: '<PERSON>',
    role: 'Product Manager',
    imageSrc: '/roles/mark.png',
    skills: ['Feature prioritization', 'User research'],
    description: 'Defines product vision and roadmap',
    color: '#6366f1'
  },
  'sales': {
    name: 'Hannah',
    role: 'Sales Representative',
    imageSrc: '/roles/hannah.png',
    skills: ['Lead qualification', 'Demos and pitches'],
    description: 'Converts leads into customers',
    color: '#8b5cf6'
  },
  'finance': {
    name: 'Jenna',
    role: 'Finance Advisor',
    imageSrc: '/roles/jenna.png',
    skills: ['Budget planning', 'Financial analysis'],
    description: 'Manages budgets and financial planning',
    color: '#a855f7'
  },
  'designer': {
    name: 'Maisie',
    role: 'Designer',
    imageSrc: '/roles/maisie.png',
    skills: ['UI/UX design', 'Brand identity'],
    description: 'Creates visuals and user experiences',
    color: '#c084fc'
  },
  'research': {
    name: 'Garek',
    role: 'Research Analyst',
    imageSrc: '/roles/garek.png',
    skills: ['Competitive analysis', 'Market trends'],
    description: 'Gathers and analyzes market data',
    color: '#d946ef'
  },
};

// Available roles that can be added (for the available agents section)

// Available roles that can be added
const availableRoles = Object.entries(agentRoles)
  .filter(([id]) => !['ceo', 'developer'].includes(id))
  .map(([id, role]) => ({
    id,
    ...role
  }));

interface TeamMembersPageProps {
  project?: any;
}

export function TeamMembersPage({ project }: TeamMembersPageProps) {
  const [activeSection, setActiveSection] = useState<'real' | 'agents'>('real');

  // Use the project team hook
  const {
    realMembers,
    agentMembers,
    loading,
    error,
    addAgent,
    removeAgent
  } = useProjectTeam(project?.id);

  // Mock current user role - in real app, this would come from auth context
  const currentUserRole = 'admin';

  // Add team member
  const handleAddTeamMember = (id: string) => {
    addAgent(id);
  };

  // Remove team member
  const handleRemoveTeamMember = (id: string) => {
    removeAgent(id);
  };

  if (loading) {
    return (
      <div className="flex h-screen bg-background items-center justify-center">
        <div className="text-white">Loading team data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen bg-background items-center justify-center">
        <div className="text-red-400">Error: {error}</div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-background">
      {/* Left sidebar for team navigation */}
      <div className="w-72 border-r border-border flex flex-col">
        <div className="h-[60px] px-4 flex items-center justify-between border-b border-border">
          <h2 className="text-white font-semibold text-lg">Team</h2>
        </div>

        <div className="flex-1 overflow-y-auto">
          {/* Team sections */}
          <div
            className={`flex h-[72px] px-4 py-3 cursor-pointer ${activeSection === 'real' ? 'bg-accent' : 'hover:bg-muted'}`}
            onClick={() => setActiveSection('real')}
          >
            <div className="flex-shrink-0 h-10 w-10 rounded-md bg-[#6466E9] mr-3 text-white font-medium overflow-hidden">
              <div className="h-10 w-10 flex items-center justify-center">
                <Users className="h-5 w-5" />
              </div>
            </div>
            <div className="flex-1 min-w-0 flex flex-col justify-center">
              <div className="flex justify-between items-center">
                <p className="text-white font-medium truncate">Team Members</p>
                <span className="text-xs text-[#999999] flex-shrink-0 ml-2">
                  {realMembers.length}
                </span>
              </div>
              <p className="text-sm text-[#999999] truncate">
                Real team members
              </p>
            </div>
          </div>

          <div
            className={`flex h-[72px] px-4 py-3 cursor-pointer ${activeSection === 'agents' ? 'bg-accent' : 'hover:bg-muted'}`}
            onClick={() => setActiveSection('agents')}
          >
            <div className="flex-shrink-0 h-10 w-10 rounded-md bg-[#8561c5] mr-3 text-white font-medium overflow-hidden">
              <div className="h-10 w-10 flex items-center justify-center">
                <Bot className="h-5 w-5" />
              </div>
            </div>
            <div className="flex-1 min-w-0 flex flex-col justify-center">
              <div className="flex justify-between items-center">
                <p className="text-white font-medium truncate">AI Agents</p>
                <span className="text-xs text-[#999999] flex-shrink-0 ml-2">
                  {agentMembers.length}
                </span>
              </div>
              <p className="text-sm text-[#999999] truncate">
                AI team members
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="h-[60px] px-6 flex items-center justify-between border-b border-border">
          <div className="flex items-center gap-3">
            {activeSection === 'real' ? (
              <>
                <h1 className="text-white font-semibold text-lg">Team Members</h1>
                <Badge variant="secondary" className="bg-[#333333] text-[#999999]">
                  {realMembers.length} members
                </Badge>
              </>
            ) : (
              <>
                <h1 className="text-white font-semibold text-lg">Agents Members</h1>
                <Badge variant="secondary" className="bg-[#333333] text-[#999999]">
                  {agentMembers.length} active
                </Badge>
              </>
            )}
          </div>

        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeSection === 'real' ? (
            <div className="space-y-4">
              {/* Real team members */}
              {realMembers.map((member) => (
                <TeamMemberCard
                  key={member.id}
                  member={member}
                  type="real"
                  showRemove={false}
                  currentUserRole={currentUserRole}
                />
              ))}

              {/* Empty state */}
              {realMembers.length === 0 && (
                <div className="text-center py-12">
                  <Users className="h-12 w-12 text-[#666666] mx-auto mb-4" />
                  <h3 className="text-white font-medium mb-2">No team members yet</h3>
                  <p className="text-[#999999] text-sm">Team member management coming soon</p>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-6">
              {/* Active AI Agents */}
              <div>
                <h3 className="text-white font-medium mb-4">Active Agents</h3>
                <div className="space-y-3">
                  {agentMembers.map((member) => (
                    <TeamMemberCard
                      key={member.id}
                      member={member}
                      type="agent"
                      onRemove={handleRemoveTeamMember}
                      showRemove={true}
                    />
                  ))}
                </div>
              </div>

              {/* Available Agents */}
              <div>
                <h3 className="text-white font-medium mb-4">Available Agents</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {availableRoles.filter(role => !agentMembers.some(member => member.id === role.id)).map((role) => (
                    <AvailableAgentCard
                      key={role.id}
                      agent={role}
                      onAdd={handleAddTeamMember}
                    />
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
