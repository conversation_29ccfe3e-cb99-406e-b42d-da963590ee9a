"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq";
exports.ids = ["vendor-chunks/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js":
/*!********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js ***!
  \********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   color: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.color),\n/* harmony export */   defaultLightThemeOption: () => (/* reexport safe */ _theme_light__WEBPACK_IMPORTED_MODULE_1__.defaultLightThemeOption),\n/* harmony export */   getDefaultExtensions: () => (/* binding */ getDefaultExtensions),\n/* harmony export */   oneDark: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDark),\n/* harmony export */   oneDarkHighlightStyle: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDarkHighlightStyle),\n/* harmony export */   oneDarkTheme: () => (/* reexport safe */ _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDarkTheme)\n/* harmony export */ });\n/* harmony import */ var _codemirror_commands__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/commands */ \"(ssr)/./node_modules/.pnpm/@codemirror+commands@6.8.1/node_modules/@codemirror/commands/dist/index.js\");\n/* harmony import */ var _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @uiw/codemirror-extensions-basic-setup */ \"(ssr)/./node_modules/.pnpm/@uiw+codemirror-extensions-basic-setup@4.23.11_@codemirror+autocomplete@6.18.6_@codemirror+co_qoz4gibf5xpps5nxf2mapku4yy/node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/theme-one-dark */ \"(ssr)/./node_modules/.pnpm/@codemirror+theme-one-dark@6.1.2/node_modules/@codemirror/theme-one-dark/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _theme_light__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./theme/light */ \"(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/theme/light.js\");\n\n\n\n\n\n\n\n\nvar getDefaultExtensions = function getDefaultExtensions(optios) {\n  if (optios === void 0) {\n    optios = {};\n  }\n  var {\n    indentWithTab: defaultIndentWithTab = true,\n    editable = true,\n    readOnly = false,\n    theme = 'light',\n    placeholder: placeholderStr = '',\n    basicSetup: defaultBasicSetup = true\n  } = optios;\n  var getExtensions = [];\n  if (defaultIndentWithTab) {\n    getExtensions.unshift(_codemirror_view__WEBPACK_IMPORTED_MODULE_3__.keymap.of([_codemirror_commands__WEBPACK_IMPORTED_MODULE_4__.indentWithTab]));\n  }\n  if (defaultBasicSetup) {\n    if (typeof defaultBasicSetup === 'boolean') {\n      getExtensions.unshift((0,_uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_0__.basicSetup)());\n    } else {\n      getExtensions.unshift((0,_uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_0__.basicSetup)(defaultBasicSetup));\n    }\n  }\n  if (placeholderStr) {\n    getExtensions.unshift((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_3__.placeholder)(placeholderStr));\n  }\n  switch (theme) {\n    case 'light':\n      getExtensions.push(_theme_light__WEBPACK_IMPORTED_MODULE_1__.defaultLightThemeOption);\n      break;\n    case 'dark':\n      getExtensions.push(_codemirror_theme_one_dark__WEBPACK_IMPORTED_MODULE_2__.oneDark);\n      break;\n    case 'none':\n      break;\n    default:\n      getExtensions.push(theme);\n      break;\n  }\n  if (editable === false) {\n    getExtensions.push(_codemirror_view__WEBPACK_IMPORTED_MODULE_3__.EditorView.editable.of(false));\n  }\n  if (readOnly) {\n    getExtensions.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_5__.EditorState.readOnly.of(true));\n  }\n  return [...getExtensions];\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/index.js":
/*!*****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/index.js ***!
  \*****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Annotation: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Annotation),\n/* harmony export */   AnnotationType: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.AnnotationType),\n/* harmony export */   BidiSpan: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.BidiSpan),\n/* harmony export */   BlockInfo: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.BlockInfo),\n/* harmony export */   BlockType: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.BlockType),\n/* harmony export */   ChangeDesc: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.ChangeDesc),\n/* harmony export */   ChangeSet: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.ChangeSet),\n/* harmony export */   CharCategory: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.CharCategory),\n/* harmony export */   Compartment: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Compartment),\n/* harmony export */   Decoration: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.Decoration),\n/* harmony export */   Direction: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.Direction),\n/* harmony export */   EditorSelection: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.EditorSelection),\n/* harmony export */   EditorState: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.EditorState),\n/* harmony export */   EditorView: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.EditorView),\n/* harmony export */   Facet: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Facet),\n/* harmony export */   GutterMarker: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.GutterMarker),\n/* harmony export */   Line: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Line),\n/* harmony export */   MapMode: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.MapMode),\n/* harmony export */   MatchDecorator: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.MatchDecorator),\n/* harmony export */   Prec: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Prec),\n/* harmony export */   Range: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Range),\n/* harmony export */   RangeSet: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.RangeSet),\n/* harmony export */   RangeSetBuilder: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.RangeSetBuilder),\n/* harmony export */   RangeValue: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.RangeValue),\n/* harmony export */   RectangleMarker: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.RectangleMarker),\n/* harmony export */   SelectionRange: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.SelectionRange),\n/* harmony export */   StateEffect: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.StateEffect),\n/* harmony export */   StateEffectType: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.StateEffectType),\n/* harmony export */   StateField: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.StateField),\n/* harmony export */   Text: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Text),\n/* harmony export */   Transaction: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.Transaction),\n/* harmony export */   ViewPlugin: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.ViewPlugin),\n/* harmony export */   ViewUpdate: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.ViewUpdate),\n/* harmony export */   WidgetType: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.WidgetType),\n/* harmony export */   __test: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.__test),\n/* harmony export */   basicSetup: () => (/* reexport safe */ _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_7__.basicSetup),\n/* harmony export */   closeHoverTooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.closeHoverTooltips),\n/* harmony export */   codePointAt: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.codePointAt),\n/* harmony export */   codePointSize: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.codePointSize),\n/* harmony export */   color: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.color),\n/* harmony export */   combineConfig: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.combineConfig),\n/* harmony export */   countColumn: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.countColumn),\n/* harmony export */   crosshairCursor: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.crosshairCursor),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   defaultLightThemeOption: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.defaultLightThemeOption),\n/* harmony export */   drawSelection: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.drawSelection),\n/* harmony export */   dropCursor: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.dropCursor),\n/* harmony export */   findClusterBreak: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.findClusterBreak),\n/* harmony export */   findColumn: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.findColumn),\n/* harmony export */   fromCodePoint: () => (/* reexport safe */ _codemirror_state__WEBPACK_IMPORTED_MODULE_6__.fromCodePoint),\n/* harmony export */   getDefaultExtensions: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.getDefaultExtensions),\n/* harmony export */   getDrawSelectionConfig: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.getDrawSelectionConfig),\n/* harmony export */   getPanel: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.getPanel),\n/* harmony export */   getStatistics: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_9__.getStatistics),\n/* harmony export */   getTooltip: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.getTooltip),\n/* harmony export */   gutter: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutter),\n/* harmony export */   gutterLineClass: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutterLineClass),\n/* harmony export */   gutterWidgetClass: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutterWidgetClass),\n/* harmony export */   gutters: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.gutters),\n/* harmony export */   hasHoverTooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.hasHoverTooltips),\n/* harmony export */   highlightActiveLine: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLine),\n/* harmony export */   highlightActiveLineGutter: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightActiveLineGutter),\n/* harmony export */   highlightSpecialChars: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightSpecialChars),\n/* harmony export */   highlightTrailingWhitespace: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightTrailingWhitespace),\n/* harmony export */   highlightWhitespace: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.highlightWhitespace),\n/* harmony export */   hoverTooltip: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.hoverTooltip),\n/* harmony export */   keymap: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.keymap),\n/* harmony export */   layer: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.layer),\n/* harmony export */   lineNumberMarkers: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumberMarkers),\n/* harmony export */   lineNumberWidgetMarker: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumberWidgetMarker),\n/* harmony export */   lineNumbers: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.lineNumbers),\n/* harmony export */   logException: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.logException),\n/* harmony export */   minimalSetup: () => (/* reexport safe */ _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_7__.minimalSetup),\n/* harmony export */   oneDark: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.oneDark),\n/* harmony export */   oneDarkHighlightStyle: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.oneDarkHighlightStyle),\n/* harmony export */   oneDarkTheme: () => (/* reexport safe */ _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__.oneDarkTheme),\n/* harmony export */   panels: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.panels),\n/* harmony export */   placeholder: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.placeholder),\n/* harmony export */   rectangularSelection: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.rectangularSelection),\n/* harmony export */   repositionTooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.repositionTooltips),\n/* harmony export */   runScopeHandlers: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.runScopeHandlers),\n/* harmony export */   scrollPastEnd: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.scrollPastEnd),\n/* harmony export */   showPanel: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.showPanel),\n/* harmony export */   showTooltip: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.showTooltip),\n/* harmony export */   tooltips: () => (/* reexport safe */ _codemirror_view__WEBPACK_IMPORTED_MODULE_5__.tooltips),\n/* harmony export */   useCodeMirror: () => (/* reexport safe */ _useCodeMirror__WEBPACK_IMPORTED_MODULE_3__.useCodeMirror)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/.pnpm/@babel+runtime@7.27.0/node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useCodeMirror__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useCodeMirror */ \"(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/useCodeMirror.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _uiw_codemirror_extensions_basic_setup__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @uiw/codemirror-extensions-basic-setup */ \"(ssr)/./node_modules/.pnpm/@uiw+codemirror-extensions-basic-setup@4.23.11_@codemirror+autocomplete@6.18.6_@codemirror+co_qoz4gibf5xpps5nxf2mapku4yy/node_modules/@uiw/codemirror-extensions-basic-setup/esm/index.js\");\n/* harmony import */ var _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./getDefaultExtensions */ \"(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/utils.js\");\n\n\nvar _excluded = [\"className\", \"value\", \"selection\", \"extensions\", \"onChange\", \"onStatistics\", \"onCreateEditor\", \"onUpdate\", \"autoFocus\", \"theme\", \"height\", \"minHeight\", \"maxHeight\", \"width\", \"minWidth\", \"maxWidth\", \"basicSetup\", \"placeholder\", \"indentWithTab\", \"editable\", \"readOnly\", \"root\", \"initialState\"];\n\n\n\n\n\n\n\n\n\nvar ReactCodeMirror = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)((props, ref) => {\n  var {\n      className,\n      value = '',\n      selection,\n      extensions = [],\n      onChange,\n      onStatistics,\n      onCreateEditor,\n      onUpdate,\n      autoFocus,\n      theme = 'light',\n      height,\n      minHeight,\n      maxHeight,\n      width,\n      minWidth,\n      maxWidth,\n      basicSetup,\n      placeholder,\n      indentWithTab,\n      editable,\n      readOnly,\n      root,\n      initialState\n    } = props,\n    other = _babel_runtime_helpers_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__(props, _excluded);\n  var editor = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n  var {\n    state,\n    view,\n    container,\n    setContainer\n  } = (0,_useCodeMirror__WEBPACK_IMPORTED_MODULE_3__.useCodeMirror)({\n    root,\n    value,\n    autoFocus,\n    theme,\n    height,\n    minHeight,\n    maxHeight,\n    width,\n    minWidth,\n    maxWidth,\n    basicSetup,\n    placeholder,\n    indentWithTab,\n    editable,\n    readOnly,\n    selection,\n    onChange,\n    onStatistics,\n    onCreateEditor,\n    onUpdate,\n    extensions,\n    initialState\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, () => ({\n    editor: editor.current,\n    state: state,\n    view: view\n  }), [editor, container, state, view]);\n  var setEditorRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(el => {\n    editor.current = el;\n    setContainer(el);\n  }, [setContainer]);\n\n  // check type of value\n  if (typeof value !== 'string') {\n    throw new Error(\"value must be typeof string but got \" + typeof value);\n  }\n  var defaultClassNames = typeof theme === 'string' ? \"cm-theme-\" + theme : 'cm-theme';\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(\"div\", _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__({\n    ref: setEditorRef,\n    className: \"\" + defaultClassNames + (className ? \" \" + className : '')\n  }, other));\n});\nReactCodeMirror.displayName = 'CodeMirror';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ReactCodeMirror);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHVpdytyZWFjdC1jb2RlbWlycm9yQDQuMjMuMTFfQGJhYmVsK3J1bnRpbWVANy4yNy4wX0Bjb2RlbWlycm9yK2F1dG9jb21wbGV0ZUA2LjE4LjZfQGNvZGVtaXJyXzJseHdubnNpcGdma3h6ZHJrZmVmbnF5ZnNxL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LWNvZGVtaXJyb3IvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFzRDtBQUMwQztBQUNoRztBQUNvRjtBQUNwQztBQUNBO0FBQ2Y7QUFDQztBQUNxQjtBQUN2QjtBQUNPO0FBQ2Y7QUFDeEIsbUNBQW1DLGlEQUFVO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTixZQUFZLGdGQUE2QjtBQUN6QyxlQUFlLDZDQUFNO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLEVBQUUsNkRBQWE7QUFDbkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsRUFBRSwwREFBbUI7QUFDckI7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILHFCQUFxQixrREFBVztBQUNoQztBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLHNEQUFJLFFBQVEsMkRBQVE7QUFDMUM7QUFDQTtBQUNBLEdBQUc7QUFDSCxDQUFDO0FBQ0Q7QUFDQSxpRUFBZSxlQUFlIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFuZHJlXFxPbmVEcml2ZVxcRGVza3RvcFxcR2l0aHViIFJlcG9zaXRvcmllc1xcZGVtb1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEB1aXcrcmVhY3QtY29kZW1pcnJvckA0LjIzLjExX0BiYWJlbCtydW50aW1lQDcuMjcuMF9AY29kZW1pcnJvcithdXRvY29tcGxldGVANi4xOC42X0Bjb2RlbWlycl8ybHh3bm5zaXBnZmt4emRya2ZlZm5xeWZzcVxcbm9kZV9tb2R1bGVzXFxAdWl3XFxyZWFjdC1jb2RlbWlycm9yXFxlc21cXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfZXh0ZW5kcyBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9leHRlbmRzXCI7XG5pbXBvcnQgX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZVwiO1xudmFyIF9leGNsdWRlZCA9IFtcImNsYXNzTmFtZVwiLCBcInZhbHVlXCIsIFwic2VsZWN0aW9uXCIsIFwiZXh0ZW5zaW9uc1wiLCBcIm9uQ2hhbmdlXCIsIFwib25TdGF0aXN0aWNzXCIsIFwib25DcmVhdGVFZGl0b3JcIiwgXCJvblVwZGF0ZVwiLCBcImF1dG9Gb2N1c1wiLCBcInRoZW1lXCIsIFwiaGVpZ2h0XCIsIFwibWluSGVpZ2h0XCIsIFwibWF4SGVpZ2h0XCIsIFwid2lkdGhcIiwgXCJtaW5XaWR0aFwiLCBcIm1heFdpZHRoXCIsIFwiYmFzaWNTZXR1cFwiLCBcInBsYWNlaG9sZGVyXCIsIFwiaW5kZW50V2l0aFRhYlwiLCBcImVkaXRhYmxlXCIsIFwicmVhZE9ubHlcIiwgXCJyb290XCIsIFwiaW5pdGlhbFN0YXRlXCJdO1xuaW1wb3J0IFJlYWN0LCB7IHVzZVJlZiwgZm9yd2FyZFJlZiwgdXNlSW1wZXJhdGl2ZUhhbmRsZSwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VDb2RlTWlycm9yIH0gZnJvbSAnLi91c2VDb2RlTWlycm9yJztcbmltcG9ydCB7IGpzeCBhcyBfanN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG5leHBvcnQgKiBmcm9tICdAY29kZW1pcnJvci92aWV3JztcbmV4cG9ydCAqIGZyb20gJ0Bjb2RlbWlycm9yL3N0YXRlJztcbmV4cG9ydCAqIGZyb20gJ0B1aXcvY29kZW1pcnJvci1leHRlbnNpb25zLWJhc2ljLXNldHVwJztcbmV4cG9ydCAqIGZyb20gJy4vdXNlQ29kZU1pcnJvcic7XG5leHBvcnQgKiBmcm9tICcuL2dldERlZmF1bHRFeHRlbnNpb25zJztcbmV4cG9ydCAqIGZyb20gJy4vdXRpbHMnO1xudmFyIFJlYWN0Q29kZU1pcnJvciA9IC8qI19fUFVSRV9fKi9mb3J3YXJkUmVmKChwcm9wcywgcmVmKSA9PiB7XG4gIHZhciB7XG4gICAgICBjbGFzc05hbWUsXG4gICAgICB2YWx1ZSA9ICcnLFxuICAgICAgc2VsZWN0aW9uLFxuICAgICAgZXh0ZW5zaW9ucyA9IFtdLFxuICAgICAgb25DaGFuZ2UsXG4gICAgICBvblN0YXRpc3RpY3MsXG4gICAgICBvbkNyZWF0ZUVkaXRvcixcbiAgICAgIG9uVXBkYXRlLFxuICAgICAgYXV0b0ZvY3VzLFxuICAgICAgdGhlbWUgPSAnbGlnaHQnLFxuICAgICAgaGVpZ2h0LFxuICAgICAgbWluSGVpZ2h0LFxuICAgICAgbWF4SGVpZ2h0LFxuICAgICAgd2lkdGgsXG4gICAgICBtaW5XaWR0aCxcbiAgICAgIG1heFdpZHRoLFxuICAgICAgYmFzaWNTZXR1cCxcbiAgICAgIHBsYWNlaG9sZGVyLFxuICAgICAgaW5kZW50V2l0aFRhYixcbiAgICAgIGVkaXRhYmxlLFxuICAgICAgcmVhZE9ubHksXG4gICAgICByb290LFxuICAgICAgaW5pdGlhbFN0YXRlXG4gICAgfSA9IHByb3BzLFxuICAgIG90aGVyID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UocHJvcHMsIF9leGNsdWRlZCk7XG4gIHZhciBlZGl0b3IgPSB1c2VSZWYobnVsbCk7XG4gIHZhciB7XG4gICAgc3RhdGUsXG4gICAgdmlldyxcbiAgICBjb250YWluZXIsXG4gICAgc2V0Q29udGFpbmVyXG4gIH0gPSB1c2VDb2RlTWlycm9yKHtcbiAgICByb290LFxuICAgIHZhbHVlLFxuICAgIGF1dG9Gb2N1cyxcbiAgICB0aGVtZSxcbiAgICBoZWlnaHQsXG4gICAgbWluSGVpZ2h0LFxuICAgIG1heEhlaWdodCxcbiAgICB3aWR0aCxcbiAgICBtaW5XaWR0aCxcbiAgICBtYXhXaWR0aCxcbiAgICBiYXNpY1NldHVwLFxuICAgIHBsYWNlaG9sZGVyLFxuICAgIGluZGVudFdpdGhUYWIsXG4gICAgZWRpdGFibGUsXG4gICAgcmVhZE9ubHksXG4gICAgc2VsZWN0aW9uLFxuICAgIG9uQ2hhbmdlLFxuICAgIG9uU3RhdGlzdGljcyxcbiAgICBvbkNyZWF0ZUVkaXRvcixcbiAgICBvblVwZGF0ZSxcbiAgICBleHRlbnNpb25zLFxuICAgIGluaXRpYWxTdGF0ZVxuICB9KTtcbiAgdXNlSW1wZXJhdGl2ZUhhbmRsZShyZWYsICgpID0+ICh7XG4gICAgZWRpdG9yOiBlZGl0b3IuY3VycmVudCxcbiAgICBzdGF0ZTogc3RhdGUsXG4gICAgdmlldzogdmlld1xuICB9KSwgW2VkaXRvciwgY29udGFpbmVyLCBzdGF0ZSwgdmlld10pO1xuICB2YXIgc2V0RWRpdG9yUmVmID0gdXNlQ2FsbGJhY2soZWwgPT4ge1xuICAgIGVkaXRvci5jdXJyZW50ID0gZWw7XG4gICAgc2V0Q29udGFpbmVyKGVsKTtcbiAgfSwgW3NldENvbnRhaW5lcl0pO1xuXG4gIC8vIGNoZWNrIHR5cGUgb2YgdmFsdWVcbiAgaWYgKHR5cGVvZiB2YWx1ZSAhPT0gJ3N0cmluZycpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJ2YWx1ZSBtdXN0IGJlIHR5cGVvZiBzdHJpbmcgYnV0IGdvdCBcIiArIHR5cGVvZiB2YWx1ZSk7XG4gIH1cbiAgdmFyIGRlZmF1bHRDbGFzc05hbWVzID0gdHlwZW9mIHRoZW1lID09PSAnc3RyaW5nJyA/IFwiY20tdGhlbWUtXCIgKyB0aGVtZSA6ICdjbS10aGVtZSc7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovX2pzeChcImRpdlwiLCBfZXh0ZW5kcyh7XG4gICAgcmVmOiBzZXRFZGl0b3JSZWYsXG4gICAgY2xhc3NOYW1lOiBcIlwiICsgZGVmYXVsdENsYXNzTmFtZXMgKyAoY2xhc3NOYW1lID8gXCIgXCIgKyBjbGFzc05hbWUgOiAnJylcbiAgfSwgb3RoZXIpKTtcbn0pO1xuUmVhY3RDb2RlTWlycm9yLmRpc3BsYXlOYW1lID0gJ0NvZGVNaXJyb3InO1xuZXhwb3J0IGRlZmF1bHQgUmVhY3RDb2RlTWlycm9yOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/theme/light.js":
/*!***********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/theme/light.js ***!
  \***********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLightThemeOption: () => (/* binding */ defaultLightThemeOption)\n/* harmony export */ });\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n\nvar defaultLightThemeOption = _codemirror_view__WEBPACK_IMPORTED_MODULE_0__.EditorView.theme({\n  '&': {\n    backgroundColor: '#fff'\n  }\n}, {\n  dark: false\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHVpdytyZWFjdC1jb2RlbWlycm9yQDQuMjMuMTFfQGJhYmVsK3J1bnRpbWVANy4yNy4wX0Bjb2RlbWlycm9yK2F1dG9jb21wbGV0ZUA2LjE4LjZfQGNvZGVtaXJyXzJseHdubnNpcGdma3h6ZHJrZmVmbnF5ZnNxL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LWNvZGVtaXJyb3IvZXNtL3RoZW1lL2xpZ2h0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQThDO0FBQ3ZDLDhCQUE4Qix3REFBVTtBQUMvQztBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFuZHJlXFxPbmVEcml2ZVxcRGVza3RvcFxcR2l0aHViIFJlcG9zaXRvcmllc1xcZGVtb1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEB1aXcrcmVhY3QtY29kZW1pcnJvckA0LjIzLjExX0BiYWJlbCtydW50aW1lQDcuMjcuMF9AY29kZW1pcnJvcithdXRvY29tcGxldGVANi4xOC42X0Bjb2RlbWlycl8ybHh3bm5zaXBnZmt4emRya2ZlZm5xeWZzcVxcbm9kZV9tb2R1bGVzXFxAdWl3XFxyZWFjdC1jb2RlbWlycm9yXFxlc21cXHRoZW1lXFxsaWdodC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFZGl0b3JWaWV3IH0gZnJvbSAnQGNvZGVtaXJyb3Ivdmlldyc7XG5leHBvcnQgdmFyIGRlZmF1bHRMaWdodFRoZW1lT3B0aW9uID0gRWRpdG9yVmlldy50aGVtZSh7XG4gICcmJzoge1xuICAgIGJhY2tncm91bmRDb2xvcjogJyNmZmYnXG4gIH1cbn0sIHtcbiAgZGFyazogZmFsc2Vcbn0pOyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/theme/light.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/useCodeMirror.js":
/*!*************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/useCodeMirror.js ***!
  \*************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCodeMirror: () => (/* binding */ useCodeMirror)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _getDefaultExtensions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getDefaultExtensions */ \"(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/getDefaultExtensions.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/utils.js\");\n\n\n\n\n\nvar External = _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Annotation.define();\nvar emptyExtensions = [];\nfunction useCodeMirror(props) {\n  var {\n    value,\n    selection,\n    onChange,\n    onStatistics,\n    onCreateEditor,\n    onUpdate,\n    extensions = emptyExtensions,\n    autoFocus,\n    theme = 'light',\n    height = null,\n    minHeight = null,\n    maxHeight = null,\n    width = null,\n    minWidth = null,\n    maxWidth = null,\n    placeholder: placeholderStr = '',\n    editable = true,\n    readOnly = false,\n    indentWithTab: defaultIndentWithTab = true,\n    basicSetup: defaultBasicSetup = true,\n    root,\n    initialState\n  } = props;\n  var [container, setContainer] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  var [view, setView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  var [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n  var defaultThemeOption = _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.theme({\n    '&': {\n      height,\n      minHeight,\n      maxHeight,\n      width,\n      minWidth,\n      maxWidth\n    },\n    '& .cm-scroller': {\n      height: '100% !important'\n    }\n  });\n  var updateListener = _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.updateListener.of(vu => {\n    if (vu.docChanged && typeof onChange === 'function' &&\n    // Fix echoing of the remote changes:\n    // If transaction is market as remote we don't have to call `onChange` handler again\n    !vu.transactions.some(tr => tr.annotation(External))) {\n      var doc = vu.state.doc;\n      var _value = doc.toString();\n      onChange(_value, vu);\n    }\n    onStatistics && onStatistics((0,_utils__WEBPACK_IMPORTED_MODULE_2__.getStatistics)(vu));\n  });\n  var defaultExtensions = (0,_getDefaultExtensions__WEBPACK_IMPORTED_MODULE_1__.getDefaultExtensions)({\n    theme,\n    editable,\n    readOnly,\n    placeholder: placeholderStr,\n    indentWithTab: defaultIndentWithTab,\n    basicSetup: defaultBasicSetup\n  });\n  var getExtensions = [updateListener, defaultThemeOption, ...defaultExtensions];\n  if (onUpdate && typeof onUpdate === 'function') {\n    getExtensions.push(_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.updateListener.of(onUpdate));\n  }\n  getExtensions = getExtensions.concat(extensions);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    if (container && !state) {\n      var config = {\n        doc: value,\n        selection,\n        extensions: getExtensions\n      };\n      var stateCurrent = initialState ? _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState.fromJSON(initialState.json, config, initialState.fields) : _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState.create(config);\n      setState(stateCurrent);\n      if (!view) {\n        var viewCurrent = new _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView({\n          state: stateCurrent,\n          parent: container,\n          root\n        });\n        setView(viewCurrent);\n        onCreateEditor && onCreateEditor(viewCurrent, stateCurrent);\n      }\n    }\n    return () => {\n      if (view) {\n        setState(undefined);\n        setView(undefined);\n      }\n    };\n  }, [container, state]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (props.container) {\n      setContainer(props.container);\n    }\n  }, [props.container]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => () => {\n    if (view) {\n      view.destroy();\n      setView(undefined);\n    }\n  }, [view]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (autoFocus && view) {\n      view.focus();\n    }\n  }, [autoFocus, view]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (view) {\n      view.dispatch({\n        effects: _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.StateEffect.reconfigure.of(getExtensions)\n      });\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [theme, extensions, height, minHeight, maxHeight, width, minWidth, maxWidth, placeholderStr, editable, readOnly, defaultIndentWithTab, defaultBasicSetup, onChange, onUpdate]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (value === undefined) {\n      return;\n    }\n    var currentValue = view ? view.state.doc.toString() : '';\n    if (view && value !== currentValue) {\n      view.dispatch({\n        changes: {\n          from: 0,\n          to: currentValue.length,\n          insert: value || ''\n        },\n        annotations: [External.of(true)]\n      });\n    }\n  }, [value, view]);\n  return {\n    state,\n    setState,\n    view,\n    setView,\n    container,\n    setContainer\n  };\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/useCodeMirror.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/utils.js":
/*!*****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/utils.js ***!
  \*****************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getStatistics: () => (/* binding */ getStatistics)\n/* harmony export */ });\nvar getStatistics = view => {\n  return {\n    line: view.state.doc.lineAt(view.state.selection.main.from),\n    lineCount: view.state.doc.lines,\n    lineBreak: view.state.lineBreak,\n    length: view.state.doc.length,\n    readOnly: view.state.readOnly,\n    tabSize: view.state.tabSize,\n    selection: view.state.selection,\n    selectionAsSingle: view.state.selection.asSingle().main,\n    ranges: view.state.selection.ranges,\n    selectionCode: view.state.sliceDoc(view.state.selection.main.from, view.state.selection.main.to),\n    selections: view.state.selection.ranges.map(r => view.state.sliceDoc(r.from, r.to)),\n    selectedText: view.state.selection.ranges.some(r => !r.empty)\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHVpdytyZWFjdC1jb2RlbWlycm9yQDQuMjMuMTFfQGJhYmVsK3J1bnRpbWVANy4yNy4wX0Bjb2RlbWlycm9yK2F1dG9jb21wbGV0ZUA2LjE4LjZfQGNvZGVtaXJyXzJseHdubnNpcGdma3h6ZHJrZmVmbnF5ZnNxL25vZGVfbW9kdWxlcy9AdWl3L3JlYWN0LWNvZGVtaXJyb3IvZXNtL3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbmRyZVxcT25lRHJpdmVcXERlc2t0b3BcXEdpdGh1YiBSZXBvc2l0b3JpZXNcXGRlbW9cXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAdWl3K3JlYWN0LWNvZGVtaXJyb3JANC4yMy4xMV9AYmFiZWwrcnVudGltZUA3LjI3LjBfQGNvZGVtaXJyb3IrYXV0b2NvbXBsZXRlQDYuMTguNl9AY29kZW1pcnJfMmx4d25uc2lwZ2ZreHpkcmtmZWZucXlmc3FcXG5vZGVfbW9kdWxlc1xcQHVpd1xccmVhY3QtY29kZW1pcnJvclxcZXNtXFx1dGlscy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIGdldFN0YXRpc3RpY3MgPSB2aWV3ID0+IHtcbiAgcmV0dXJuIHtcbiAgICBsaW5lOiB2aWV3LnN0YXRlLmRvYy5saW5lQXQodmlldy5zdGF0ZS5zZWxlY3Rpb24ubWFpbi5mcm9tKSxcbiAgICBsaW5lQ291bnQ6IHZpZXcuc3RhdGUuZG9jLmxpbmVzLFxuICAgIGxpbmVCcmVhazogdmlldy5zdGF0ZS5saW5lQnJlYWssXG4gICAgbGVuZ3RoOiB2aWV3LnN0YXRlLmRvYy5sZW5ndGgsXG4gICAgcmVhZE9ubHk6IHZpZXcuc3RhdGUucmVhZE9ubHksXG4gICAgdGFiU2l6ZTogdmlldy5zdGF0ZS50YWJTaXplLFxuICAgIHNlbGVjdGlvbjogdmlldy5zdGF0ZS5zZWxlY3Rpb24sXG4gICAgc2VsZWN0aW9uQXNTaW5nbGU6IHZpZXcuc3RhdGUuc2VsZWN0aW9uLmFzU2luZ2xlKCkubWFpbixcbiAgICByYW5nZXM6IHZpZXcuc3RhdGUuc2VsZWN0aW9uLnJhbmdlcyxcbiAgICBzZWxlY3Rpb25Db2RlOiB2aWV3LnN0YXRlLnNsaWNlRG9jKHZpZXcuc3RhdGUuc2VsZWN0aW9uLm1haW4uZnJvbSwgdmlldy5zdGF0ZS5zZWxlY3Rpb24ubWFpbi50byksXG4gICAgc2VsZWN0aW9uczogdmlldy5zdGF0ZS5zZWxlY3Rpb24ucmFuZ2VzLm1hcChyID0+IHZpZXcuc3RhdGUuc2xpY2VEb2Moci5mcm9tLCByLnRvKSksXG4gICAgc2VsZWN0ZWRUZXh0OiB2aWV3LnN0YXRlLnNlbGVjdGlvbi5yYW5nZXMuc29tZShyID0+ICFyLmVtcHR5KVxuICB9O1xufTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@uiw+react-codemirror@4.23.11_@babel+runtime@7.27.0_@codemirror+autocomplete@6.18.6_@codemirr_2lxwnnsipgfkxzdrkfefnqyfsq/node_modules/@uiw/react-codemirror/esm/utils.js\n");

/***/ })

};
;