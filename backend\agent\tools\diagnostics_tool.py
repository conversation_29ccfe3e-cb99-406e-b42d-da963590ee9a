import os
import json
import async<PERSON>
from typing import List, Dict, Any, Optional

from agentpress.tool import Too<PERSON><PERSON><PERSON><PERSON>, openapi_schema, xml_schema
from sandbox.sandbox import SandboxToolsBase
from agentpress.thread_manager import ThreadManager
from utils.logger import logger

class DiagnosticsTool(SandboxToolsBase):
    """Tool for getting diagnostics (errors, warnings, etc.) from files in the sandbox."""

    def __init__(self, project_id: str, thread_manager: ThreadManager):
        """Initialize the diagnostics tool.

        Args:
            project_id: ID of the project
            thread_manager: Thread manager instance
        """
        super().__init__(project_id, thread_manager)
        self.workspace_path = "/workspace"

    async def _get_diagnostics_from_file(self, file_path: str) -> List[Dict[str, Any]]:
        """Get diagnostics for a specific file in the sandbox.

        Uses simple linting rules to check for common issues in files.

        Args:
            file_path: Path to the file to get diagnostics for (relative to /workspace)

        Returns:
            List of diagnostics for the file
        """
        diagnostics = []

        try:
            await self._ensure_sandbox()  # Ensure sandbox is initialized

            # Determine file type
            ext = os.path.splitext(file_path)[1].lower()

            # Clean path to ensure it's relative to /workspace
            clean_path = file_path.lstrip('/')
            sandbox_path = f"{self.workspace_path}/{clean_path}"

            # Check if file exists in sandbox
            try:
                # Use ls to check if file exists
                result = self.sandbox.commands.run(f"ls -la {sandbox_path}", timeout=5)
                if result.exit_code != 0:
                    return [{
                        'file': file_path,
                        'line': 0,
                        'column': 0,
                        'severity': 'error',
                        'message': f'File not found: {file_path}'
                    }]
            except Exception:
                return [{
                    'file': file_path,
                    'line': 0,
                    'column': 0,
                    'severity': 'error',
                    'message': f'File not found: {file_path}'
                }]

            # Read file content from sandbox
            try:
                content = self.sandbox.files.read(sandbox_path)
                lines = content.split('\n')
            except Exception as e:
                return [{
                    'file': file_path,
                    'line': 0,
                    'column': 0,
                    'severity': 'error',
                    'message': f'Error reading file: {str(e)}'
                }]

            # Simple diagnostics based on file type
            if ext == '.py':
                # Check for Python syntax errors using the sandbox
                try:
                    # Create a temporary script to check syntax
                    check_script = f"""
import ast
try:
    with open('{sandbox_path}', 'r') as f:
        ast.parse(f.read())
    print('SYNTAX_OK')
except SyntaxError as e:
    print(f'SYNTAX_ERROR:{e.lineno}:{e.offset}:{str(e)}')
"""
                    # Write the script to the sandbox
                    file_name = file_path.split('/')[-1] if '/' in file_path else file_path
                    temp_script_path = f"/tmp/syntax_check_{file_name}.py"
                    self.sandbox.files.write(temp_script_path, check_script)

                    # Run the script
                    result = self.sandbox.commands.run(f"python {temp_script_path}", timeout=10)

                    # Check the result
                    if "SYNTAX_ERROR" in result.stdout:
                        # Parse the error
                        error_parts = result.stdout.strip().split(':', 4)
                        if len(error_parts) >= 4:
                            lineno = int(error_parts[1])
                            offset = int(error_parts[2])
                            error_msg = error_parts[3]

                            diagnostics.append({
                                'file': file_path,
                                'line': lineno,
                                'column': offset,
                                'severity': 'error',
                                'message': f'Syntax error: {error_msg}'
                            })

                    # Clean up
                    self.sandbox.commands.run(f"rm -f {temp_script_path}", timeout=5)

                except Exception as e:
                    logger.error(f"Error checking Python syntax: {str(e)}")

                # Simple checks for common issues
                for i, line in enumerate(lines):
                    # Check for TODO comments
                    if 'TODO' in line:
                        diagnostics.append({
                            'file': file_path,
                            'line': i + 1,
                            'column': line.find('TODO') + 1,
                            'severity': 'info',
                            'message': f'TODO comment: {line.strip()}'
                        })

                    # Check for print statements (often left in during debugging)
                    if 'print(' in line:
                        diagnostics.append({
                            'file': file_path,
                            'line': i + 1,
                            'column': line.find('print(') + 1,
                            'severity': 'warning',
                            'message': 'Print statement found, consider removing for production'
                        })

            elif ext in ['.js', '.jsx', '.ts', '.tsx']:
                # Simple checks for JavaScript/TypeScript files
                for i, line in enumerate(lines):
                    # Check for console.log statements
                    if 'console.log(' in line:
                        diagnostics.append({
                            'file': file_path,
                            'line': i + 1,
                            'column': line.find('console.log(') + 1,
                            'severity': 'warning',
                            'message': 'Console.log statement found, consider removing for production'
                        })

                    # Check for TODO comments
                    if 'TODO' in line:
                        diagnostics.append({
                            'file': file_path,
                            'line': i + 1,
                            'column': line.find('TODO') + 1,
                            'severity': 'info',
                            'message': f'TODO comment: {line.strip()}'
                        })

            # Add more file types and checks as needed

            return diagnostics

        except Exception as e:
            logger.error(f"Error getting diagnostics for {file_path}: {str(e)}")
            return [{
                'file': file_path,
                'line': 0,
                'column': 0,
                'severity': 'error',
                'message': f'Error analyzing file: {str(e)}'
            }]

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "diagnostics",
            "description": "Get diagnostics (errors, warnings, etc.) from the IDE for specific files or the entire workspace.",
            "parameters": {
                "type": "object",
                "properties": {
                    "paths": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Optional list of file paths to get diagnostics for. If not provided, returns diagnostics for all files in the workspace."
                    },
                    "severity_filter": {
                        "type": "string",
                        "description": "Filter diagnostics by severity level",
                        "enum": ["error", "warning", "info", "hint", "all"],
                        "default": "all"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of diagnostics to return",
                        "default": 100
                    }
                }
            }
        }
    })
    @xml_schema(
        tag_name="diagnostics",
        mappings=[
            {"param_name": "paths", "node_type": "attribute", "path": "paths"},
            {"param_name": "severity_filter", "node_type": "attribute", "path": "severity"},
            {"param_name": "limit", "node_type": "attribute", "path": "limit"}
        ],
        example='''
        <!-- Get diagnostics for specific files -->
        <diagnostics
            paths="src/main.py,src/utils.py"
            severity="error"
            limit="50">
        </diagnostics>

        <!-- Get all diagnostics -->
        <diagnostics>
        </diagnostics>
        '''
    )
    async def diagnostics(
        self,
        paths: Optional[List[str]] = None,
        severity_filter: str = "all",
        limit: int = 100
    ) -> ToolResult:
        """
        Get diagnostics (errors, warnings, etc.) from files in the sandbox.

        Args:
            paths: Optional list of file paths to get diagnostics for
            severity_filter: Filter diagnostics by severity level
            limit: Maximum number of diagnostics to return

        Returns:
            ToolResult with diagnostics information
        """
        try:
            await self._ensure_sandbox()  # Ensure sandbox is initialized

            # Process paths input
            if paths and isinstance(paths, str):
                paths = [p.strip() for p in paths.split(',')]

            # If no paths provided, get diagnostics for all files
            if not paths:
                # Find all code files in the workspace using find command
                code_extensions = "\\( -name '*.py' -o -name '*.js' -o -name '*.jsx' -o -name '*.ts' -o -name '*.tsx' -o -name '*.html' -o -name '*.css' -o -name '*.json' -o -name '*.md' \\)"

                try:
                    result = self.sandbox.commands.run(
                        f"find {self.workspace_path} -type f {code_extensions} | sort",
                        timeout=30
                    )

                    if result.exit_code == 0 and result.stdout:
                        all_files = [
                            file_path.replace(f"{self.workspace_path}/", "")
                            for file_path in result.stdout.strip().split('\n')
                            if file_path.strip()
                        ]
                    else:
                        all_files = []

                    # Limit to a reasonable number of files to avoid performance issues
                    paths = all_files[:50]  # Limit to 50 files
                except Exception as e:
                    logger.error(f"Error finding files: {str(e)}")
                    return self.fail_response(f"Error finding files: {str(e)}")

            # Get diagnostics for each file
            all_diagnostics = []
            for file_path in paths:
                file_diagnostics = await self._get_diagnostics_from_file(file_path)
                all_diagnostics.extend(file_diagnostics)

            # Filter by severity if needed
            if severity_filter and severity_filter.lower() != "all":
                all_diagnostics = [d for d in all_diagnostics if d['severity'] == severity_filter.lower()]

            # Sort by severity (error > warning > info > hint)
            severity_order = {"error": 0, "warning": 1, "info": 2, "hint": 3}
            all_diagnostics.sort(key=lambda d: severity_order.get(d['severity'], 4))

            # Limit the number of diagnostics
            all_diagnostics = all_diagnostics[:limit]

            # Count by severity
            severity_counts = {
                "error": len([d for d in all_diagnostics if d['severity'] == 'error']),
                "warning": len([d for d in all_diagnostics if d['severity'] == 'warning']),
                "info": len([d for d in all_diagnostics if d['severity'] == 'info']),
                "hint": len([d for d in all_diagnostics if d['severity'] == 'hint'])
            }

            return self.success_response({
                'diagnostics': all_diagnostics,
                'total': len(all_diagnostics),
                'severity_counts': severity_counts,
                'files_analyzed': len(paths)
            })

        except Exception as e:
            logger.error(f"Error in diagnostics: {str(e)}")
            return self.fail_response(f"Error getting diagnostics: {str(e)}")


if __name__ == "__main__":
    import asyncio
    from agentpress.thread_manager import ThreadManager

    async def test_diagnostics():
        """Test function for the diagnostics tool"""
        # Create a thread manager
        thread_manager = ThreadManager("test_thread")

        # Create a diagnostics tool
        diagnostics_tool = DiagnosticsTool("test_project", thread_manager)

        # Test diagnostics
        result = await diagnostics_tool.diagnostics(
            paths=["backend/agent/tools/diagnostics_tool.py"],
            severity_filter="all"
        )
        print(json.dumps(result.output, indent=2))

    asyncio.run(test_diagnostics())
