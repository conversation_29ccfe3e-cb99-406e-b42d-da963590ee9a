"""
Common utilities for sandbox operations.
This module breaks circular dependencies between agent and sandbox modules.
"""

import os
from typing import Tuple, Optional, Any
from datetime import datetime, timezone
from utils.logger import logger
from sandbox.sandbox import get_or_start_sandbox

async def get_or_create_project_sandbox(client: Any, project_id: str) -> Tuple[Any, str, Optional[str]]:
    """
    Get or create a sandbox for a project.
    
    Args:
        client: The Supabase client
        project_id: The project ID to get or create a sandbox for
        
    Returns:
        Tuple[Sandbox, str, Optional[str]]: The sandbox object, sandbox ID, and sandbox password (if applicable)
    """
    logger.info(f"Getting or creating sandbox for project {project_id}")
    
    try:
        # Get the project data
        project_result = await client.table('projects').select('*').eq('project_id', project_id).execute()
        
        if not project_result.data or len(project_result.data) == 0:
            logger.error(f"Project not found: {project_id}")
            raise ValueError(f"Project not found: {project_id}")
        
        project_data = project_result.data[0]
        
        # Check if the project already has a sandbox
        sandbox_data = project_data.get('sandbox', {})
        sandbox_id = sandbox_data.get('id')
        sandbox_pass = sandbox_data.get('password')
        
        if sandbox_id:
            logger.info(f"Project {project_id} already has sandbox {sandbox_id}, retrieving it")
            # Get the existing sandbox
            sandbox = await get_or_start_sandbox(sandbox_id)
            return sandbox, sandbox_id, sandbox_pass
        
        # Create a new sandbox
        logger.info(f"Creating new sandbox for project {project_id}")
        sandbox = await get_or_start_sandbox()
        sandbox_id = sandbox.id
        
        # Update the project with the new sandbox information
        await client.table('projects').update({
            'sandbox': {
                'id': sandbox_id,
                'created_at': str(datetime.now(timezone.utc))
            }
        }).eq('project_id', project_id).execute()
        
        logger.info(f"Created new sandbox {sandbox_id} for project {project_id}")
        return sandbox, sandbox_id, None
    except Exception as e:
        logger.error(f"Error getting or creating sandbox for project {project_id}: {str(e)}")
        raise e
