import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import Image from 'next/image';

// Generic database icon component
export function DatabaseIcon({ className = "", size = 40 }: { className?: string, size?: number }) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M12 8C16.4183 8 20 6.65685 20 5C20 3.34315 16.4183 2 12 2C7.58172 2 4 3.34315 4 5C4 6.65685 7.58172 8 12 8Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4 5V19C4 20.6569 7.58172 22 12 22C16.4183 22 20 20.6569 20 19V5"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4 12C4 13.6569 7.58172 15 12 15C16.4183 15 20 13.6569 20 12"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

interface DatabaseOption {
  id: string;
  name: string;
  description: string;
  iconSrc: string;
}

interface DatabaseOptionsProps {
  onSelect: (databaseId: string) => void;
  onCancel: () => void;
}

const databaseOptions: DatabaseOption[] = [
  {
    id: 'supabase',
    name: 'Supabase',
    description: 'Open source Firebase alternative with PostgreSQL',
    iconSrc: '/logoicons/supabase.svg',
  },
  {
    id: 'firebase',
    name: 'Firebase',
    description: 'Google\'s app development platform with real-time database',
    iconSrc: '/logoicons/firebase.svg',
  },
  {
    id: 'mongodb',
    name: 'MongoDB',
    description: 'NoSQL document database for modern applications',
    iconSrc: '/logoicons/mongodb.svg',
  },
  {
    id: 'aws',
    name: 'AWS',
    description: 'Amazon Web Services database solutions',
    iconSrc: '/logoicons/aws.svg',
  },
];

export function DatabaseOptions({ onSelect, onCancel }: DatabaseOptionsProps) {
  const [selectedDatabase, setSelectedDatabase] = useState<string>('supabase');

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">Select Database Provider</h3>
        <p className="text-sm text-muted-foreground">
          Choose a database provider to create an account for your agents
        </p>
      </div>

      <RadioGroup
        value={selectedDatabase}
        onValueChange={setSelectedDatabase}
        className="space-y-3"
      >
        {databaseOptions.map((option) => (
          <Card
            key={option.id}
            className={`border cursor-pointer transition-all ${
              selectedDatabase === option.id ? 'border-primary' : 'border-border'
            }`}
            onClick={() => setSelectedDatabase(option.id)}
          >
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <RadioGroupItem
                  value={option.id}
                  id={option.id}
                  className="mt-0"
                />
                <div className="h-10 w-10 flex items-center justify-center">
                  <Image
                    src={option.iconSrc}
                    alt={`${option.name} logo`}
                    width={40}
                    height={40}
                    className="object-contain"
                  />
                </div>
                <div>
                  <Label htmlFor={option.id} className="font-medium">
                    {option.name}
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    {option.description}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </RadioGroup>

      <div className="flex justify-end gap-3 pt-2">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={() => onSelect(selectedDatabase)}>
          Continue
        </Button>
      </div>
    </div>
  );
}
