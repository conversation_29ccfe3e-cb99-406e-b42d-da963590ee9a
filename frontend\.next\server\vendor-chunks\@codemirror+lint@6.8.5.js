"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lint@6.8.5";
exports.ids = ["vendor-chunks/@codemirror+lint@6.8.5"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lint@6.8.5/node_modules/@codemirror/lint/dist/index.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lint@6.8.5/node_modules/@codemirror/lint/dist/index.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   closeLintPanel: () => (/* binding */ closeLintPanel),\n/* harmony export */   diagnosticCount: () => (/* binding */ diagnosticCount),\n/* harmony export */   forEachDiagnostic: () => (/* binding */ forEachDiagnostic),\n/* harmony export */   forceLinting: () => (/* binding */ forceLinting),\n/* harmony export */   lintGutter: () => (/* binding */ lintGutter),\n/* harmony export */   lintKeymap: () => (/* binding */ lintKeymap),\n/* harmony export */   linter: () => (/* binding */ linter),\n/* harmony export */   nextDiagnostic: () => (/* binding */ nextDiagnostic),\n/* harmony export */   openLintPanel: () => (/* binding */ openLintPanel),\n/* harmony export */   previousDiagnostic: () => (/* binding */ previousDiagnostic),\n/* harmony export */   setDiagnostics: () => (/* binding */ setDiagnostics),\n/* harmony export */   setDiagnosticsEffect: () => (/* binding */ setDiagnosticsEffect)\n/* harmony export */ });\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var crelt__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crelt */ \"(ssr)/./node_modules/.pnpm/crelt@1.0.6/node_modules/crelt/index.js\");\n\n\n\n\nclass SelectedDiagnostic {\n    constructor(from, to, diagnostic) {\n        this.from = from;\n        this.to = to;\n        this.diagnostic = diagnostic;\n    }\n}\nclass LintState {\n    constructor(diagnostics, panel, selected) {\n        this.diagnostics = diagnostics;\n        this.panel = panel;\n        this.selected = selected;\n    }\n    static init(diagnostics, panel, state) {\n        // Filter the list of diagnostics for which to create markers\n        let diagnosticFilter = state.facet(lintConfig).markerFilter;\n        if (diagnosticFilter)\n            diagnostics = diagnosticFilter(diagnostics, state);\n        let sorted = diagnostics.slice().sort((a, b) => a.from - b.from || a.to - b.to);\n        let deco = new _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.RangeSetBuilder(), active = [], pos = 0;\n        for (let i = 0;;) {\n            let next = i == sorted.length ? null : sorted[i];\n            if (!next && !active.length)\n                break;\n            let from, to;\n            if (active.length) {\n                from = pos;\n                to = active.reduce((p, d) => Math.min(p, d.to), next && next.from > from ? next.from : 1e8);\n            }\n            else {\n                from = next.from;\n                to = next.to;\n                active.push(next);\n                i++;\n            }\n            while (i < sorted.length) {\n                let next = sorted[i];\n                if (next.from == from && (next.to > next.from || next.to == from)) {\n                    active.push(next);\n                    i++;\n                    to = Math.min(next.to, to);\n                }\n                else {\n                    to = Math.min(next.from, to);\n                    break;\n                }\n            }\n            let sev = maxSeverity(active);\n            if (active.some(d => d.from == d.to || (d.from == d.to - 1 && state.doc.lineAt(d.from).to == d.from))) {\n                deco.add(from, from, _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.widget({\n                    widget: new DiagnosticWidget(sev),\n                    diagnostics: active.slice()\n                }));\n            }\n            else {\n                let markClass = active.reduce((c, d) => d.markClass ? c + \" \" + d.markClass : c, \"\");\n                deco.add(from, to, _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.mark({\n                    class: \"cm-lintRange cm-lintRange-\" + sev + markClass,\n                    diagnostics: active.slice(),\n                    inclusiveEnd: active.some(a => a.to > to)\n                }));\n            }\n            pos = to;\n            for (let i = 0; i < active.length; i++)\n                if (active[i].to <= pos)\n                    active.splice(i--, 1);\n        }\n        let set = deco.finish();\n        return new LintState(set, panel, findDiagnostic(set));\n    }\n}\nfunction findDiagnostic(diagnostics, diagnostic = null, after = 0) {\n    let found = null;\n    diagnostics.between(after, 1e9, (from, to, { spec }) => {\n        if (diagnostic && spec.diagnostics.indexOf(diagnostic) < 0)\n            return;\n        if (!found)\n            found = new SelectedDiagnostic(from, to, diagnostic || spec.diagnostics[0]);\n        else if (spec.diagnostics.indexOf(found.diagnostic) < 0)\n            return false;\n        else\n            found = new SelectedDiagnostic(found.from, to, found.diagnostic);\n    });\n    return found;\n}\nfunction hideTooltip(tr, tooltip) {\n    let from = tooltip.pos, to = tooltip.end || from;\n    let result = tr.state.facet(lintConfig).hideOn(tr, from, to);\n    if (result != null)\n        return result;\n    let line = tr.startState.doc.lineAt(tooltip.pos);\n    return !!(tr.effects.some(e => e.is(setDiagnosticsEffect)) || tr.changes.touchesRange(line.from, Math.max(line.to, to)));\n}\nfunction maybeEnableLint(state, effects) {\n    return state.field(lintState, false) ? effects : effects.concat(_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.appendConfig.of(lintExtensions));\n}\n/**\nReturns a transaction spec which updates the current set of\ndiagnostics, and enables the lint extension if if wasn't already\nactive.\n*/\nfunction setDiagnostics(state, diagnostics) {\n    return {\n        effects: maybeEnableLint(state, [setDiagnosticsEffect.of(diagnostics)])\n    };\n}\n/**\nThe state effect that updates the set of active diagnostics. Can\nbe useful when writing an extension that needs to track these.\n*/\nconst setDiagnosticsEffect = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define();\nconst togglePanel = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define();\nconst movePanelSelection = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define();\nconst lintState = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateField.define({\n    create() {\n        return new LintState(_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.none, null, null);\n    },\n    update(value, tr) {\n        if (tr.docChanged && value.diagnostics.size) {\n            let mapped = value.diagnostics.map(tr.changes), selected = null, panel = value.panel;\n            if (value.selected) {\n                let selPos = tr.changes.mapPos(value.selected.from, 1);\n                selected = findDiagnostic(mapped, value.selected.diagnostic, selPos) || findDiagnostic(mapped, null, selPos);\n            }\n            if (!mapped.size && panel && tr.state.facet(lintConfig).autoPanel)\n                panel = null;\n            value = new LintState(mapped, panel, selected);\n        }\n        for (let effect of tr.effects) {\n            if (effect.is(setDiagnosticsEffect)) {\n                let panel = !tr.state.facet(lintConfig).autoPanel ? value.panel : effect.value.length ? LintPanel.open : null;\n                value = LintState.init(effect.value, panel, tr.state);\n            }\n            else if (effect.is(togglePanel)) {\n                value = new LintState(value.diagnostics, effect.value ? LintPanel.open : null, value.selected);\n            }\n            else if (effect.is(movePanelSelection)) {\n                value = new LintState(value.diagnostics, value.panel, effect.value);\n            }\n        }\n        return value;\n    },\n    provide: f => [_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.showPanel.from(f, val => val.panel),\n        _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.decorations.from(f, s => s.diagnostics)]\n});\n/**\nReturns the number of active lint diagnostics in the given state.\n*/\nfunction diagnosticCount(state) {\n    let lint = state.field(lintState, false);\n    return lint ? lint.diagnostics.size : 0;\n}\nconst activeMark = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.mark({ class: \"cm-lintRange cm-lintRange-active\" });\nfunction lintTooltip(view, pos, side) {\n    let { diagnostics } = view.state.field(lintState);\n    let found, start = -1, end = -1;\n    diagnostics.between(pos - (side < 0 ? 1 : 0), pos + (side > 0 ? 1 : 0), (from, to, { spec }) => {\n        if (pos >= from && pos <= to &&\n            (from == to || ((pos > from || side > 0) && (pos < to || side < 0)))) {\n            found = spec.diagnostics;\n            start = from;\n            end = to;\n            return false;\n        }\n    });\n    let diagnosticFilter = view.state.facet(lintConfig).tooltipFilter;\n    if (found && diagnosticFilter)\n        found = diagnosticFilter(found, view.state);\n    if (!found)\n        return null;\n    return {\n        pos: start,\n        end: end,\n        above: view.state.doc.lineAt(start).to < end,\n        create() {\n            return { dom: diagnosticsTooltip(view, found) };\n        }\n    };\n}\nfunction diagnosticsTooltip(view, diagnostics) {\n    return (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ul\", { class: \"cm-tooltip-lint\" }, diagnostics.map(d => renderDiagnostic(view, d, false)));\n}\n/**\nCommand to open and focus the lint panel.\n*/\nconst openLintPanel = (view) => {\n    let field = view.state.field(lintState, false);\n    if (!field || !field.panel)\n        view.dispatch({ effects: maybeEnableLint(view.state, [togglePanel.of(true)]) });\n    let panel = (0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.getPanel)(view, LintPanel.open);\n    if (panel)\n        panel.dom.querySelector(\".cm-panel-lint ul\").focus();\n    return true;\n};\n/**\nCommand to close the lint panel, when open.\n*/\nconst closeLintPanel = (view) => {\n    let field = view.state.field(lintState, false);\n    if (!field || !field.panel)\n        return false;\n    view.dispatch({ effects: togglePanel.of(false) });\n    return true;\n};\n/**\nMove the selection to the next diagnostic.\n*/\nconst nextDiagnostic = (view) => {\n    let field = view.state.field(lintState, false);\n    if (!field)\n        return false;\n    let sel = view.state.selection.main, next = field.diagnostics.iter(sel.to + 1);\n    if (!next.value) {\n        next = field.diagnostics.iter(0);\n        if (!next.value || next.from == sel.from && next.to == sel.to)\n            return false;\n    }\n    view.dispatch({ selection: { anchor: next.from, head: next.to }, scrollIntoView: true });\n    return true;\n};\n/**\nMove the selection to the previous diagnostic.\n*/\nconst previousDiagnostic = (view) => {\n    let { state } = view, field = state.field(lintState, false);\n    if (!field)\n        return false;\n    let sel = state.selection.main;\n    let prevFrom, prevTo, lastFrom, lastTo;\n    field.diagnostics.between(0, state.doc.length, (from, to) => {\n        if (to < sel.to && (prevFrom == null || prevFrom < from)) {\n            prevFrom = from;\n            prevTo = to;\n        }\n        if (lastFrom == null || from > lastFrom) {\n            lastFrom = from;\n            lastTo = to;\n        }\n    });\n    if (lastFrom == null || prevFrom == null && lastFrom == sel.from)\n        return false;\n    view.dispatch({ selection: { anchor: prevFrom !== null && prevFrom !== void 0 ? prevFrom : lastFrom, head: prevTo !== null && prevTo !== void 0 ? prevTo : lastTo }, scrollIntoView: true });\n    return true;\n};\n/**\nA set of default key bindings for the lint functionality.\n\n- Ctrl-Shift-m (Cmd-Shift-m on macOS): [`openLintPanel`](https://codemirror.net/6/docs/ref/#lint.openLintPanel)\n- F8: [`nextDiagnostic`](https://codemirror.net/6/docs/ref/#lint.nextDiagnostic)\n*/\nconst lintKeymap = [\n    { key: \"Mod-Shift-m\", run: openLintPanel, preventDefault: true },\n    { key: \"F8\", run: nextDiagnostic }\n];\nconst lintPlugin = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.view = view;\n        this.timeout = -1;\n        this.set = true;\n        let { delay } = view.state.facet(lintConfig);\n        this.lintTime = Date.now() + delay;\n        this.run = this.run.bind(this);\n        this.timeout = setTimeout(this.run, delay);\n    }\n    run() {\n        clearTimeout(this.timeout);\n        let now = Date.now();\n        if (now < this.lintTime - 10) {\n            this.timeout = setTimeout(this.run, this.lintTime - now);\n        }\n        else {\n            this.set = false;\n            let { state } = this.view, { sources } = state.facet(lintConfig);\n            if (sources.length)\n                batchResults(sources.map(s => Promise.resolve(s(this.view))), annotations => {\n                    if (this.view.state.doc == state.doc)\n                        this.view.dispatch(setDiagnostics(this.view.state, annotations.reduce((a, b) => a.concat(b))));\n                }, error => { (0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.logException)(this.view.state, error); });\n        }\n    }\n    update(update) {\n        let config = update.state.facet(lintConfig);\n        if (update.docChanged || config != update.startState.facet(lintConfig) ||\n            config.needsRefresh && config.needsRefresh(update)) {\n            this.lintTime = Date.now() + config.delay;\n            if (!this.set) {\n                this.set = true;\n                this.timeout = setTimeout(this.run, config.delay);\n            }\n        }\n    }\n    force() {\n        if (this.set) {\n            this.lintTime = Date.now();\n            this.run();\n        }\n    }\n    destroy() {\n        clearTimeout(this.timeout);\n    }\n});\nfunction batchResults(promises, sink, error) {\n    let collected = [], timeout = -1;\n    for (let p of promises)\n        p.then(value => {\n            collected.push(value);\n            clearTimeout(timeout);\n            if (collected.length == promises.length)\n                sink(collected);\n            else\n                timeout = setTimeout(() => sink(collected), 200);\n        }, error);\n}\nconst lintConfig = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Facet.define({\n    combine(input) {\n        return Object.assign({ sources: input.map(i => i.source).filter(x => x != null) }, (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.combineConfig)(input.map(i => i.config), {\n            delay: 750,\n            markerFilter: null,\n            tooltipFilter: null,\n            needsRefresh: null,\n            hideOn: () => null,\n        }, {\n            needsRefresh: (a, b) => !a ? b : !b ? a : u => a(u) || b(u)\n        }));\n    }\n});\n/**\nGiven a diagnostic source, this function returns an extension that\nenables linting with that source. It will be called whenever the\neditor is idle (after its content changed). If `null` is given as\nsource, this only configures the lint extension.\n*/\nfunction linter(source, config = {}) {\n    return [\n        lintConfig.of({ source, config }),\n        lintPlugin,\n        lintExtensions\n    ];\n}\n/**\nForces any linters [configured](https://codemirror.net/6/docs/ref/#lint.linter) to run when the\neditor is idle to run right away.\n*/\nfunction forceLinting(view) {\n    let plugin = view.plugin(lintPlugin);\n    if (plugin)\n        plugin.force();\n}\nfunction assignKeys(actions) {\n    let assigned = [];\n    if (actions)\n        actions: for (let { name } of actions) {\n            for (let i = 0; i < name.length; i++) {\n                let ch = name[i];\n                if (/[a-zA-Z]/.test(ch) && !assigned.some(c => c.toLowerCase() == ch.toLowerCase())) {\n                    assigned.push(ch);\n                    continue actions;\n                }\n            }\n            assigned.push(\"\");\n        }\n    return assigned;\n}\nfunction renderDiagnostic(view, diagnostic, inPanel) {\n    var _a;\n    let keys = inPanel ? assignKeys(diagnostic.actions) : [];\n    return (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"li\", { class: \"cm-diagnostic cm-diagnostic-\" + diagnostic.severity }, (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"span\", { class: \"cm-diagnosticText\" }, diagnostic.renderMessage ? diagnostic.renderMessage(view) : diagnostic.message), (_a = diagnostic.actions) === null || _a === void 0 ? void 0 : _a.map((action, i) => {\n        let fired = false, click = (e) => {\n            e.preventDefault();\n            if (fired)\n                return;\n            fired = true;\n            let found = findDiagnostic(view.state.field(lintState).diagnostics, diagnostic);\n            if (found)\n                action.apply(view, found.from, found.to);\n        };\n        let { name } = action, keyIndex = keys[i] ? name.indexOf(keys[i]) : -1;\n        let nameElt = keyIndex < 0 ? name : [name.slice(0, keyIndex),\n            (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"u\", name.slice(keyIndex, keyIndex + 1)),\n            name.slice(keyIndex + 1)];\n        return (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"button\", {\n            type: \"button\",\n            class: \"cm-diagnosticAction\",\n            onclick: click,\n            onmousedown: click,\n            \"aria-label\": ` Action: ${name}${keyIndex < 0 ? \"\" : ` (access key \"${keys[i]})\"`}.`\n        }, nameElt);\n    }), diagnostic.source && (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"div\", { class: \"cm-diagnosticSource\" }, diagnostic.source));\n}\nclass DiagnosticWidget extends _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.WidgetType {\n    constructor(sev) {\n        super();\n        this.sev = sev;\n    }\n    eq(other) { return other.sev == this.sev; }\n    toDOM() {\n        return (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"span\", { class: \"cm-lintPoint cm-lintPoint-\" + this.sev });\n    }\n}\nclass PanelItem {\n    constructor(view, diagnostic) {\n        this.diagnostic = diagnostic;\n        this.id = \"item_\" + Math.floor(Math.random() * 0xffffffff).toString(16);\n        this.dom = renderDiagnostic(view, diagnostic, true);\n        this.dom.id = this.id;\n        this.dom.setAttribute(\"role\", \"option\");\n    }\n}\nclass LintPanel {\n    constructor(view) {\n        this.view = view;\n        this.items = [];\n        let onkeydown = (event) => {\n            if (event.keyCode == 27) { // Escape\n                closeLintPanel(this.view);\n                this.view.focus();\n            }\n            else if (event.keyCode == 38 || event.keyCode == 33) { // ArrowUp, PageUp\n                this.moveSelection((this.selectedIndex - 1 + this.items.length) % this.items.length);\n            }\n            else if (event.keyCode == 40 || event.keyCode == 34) { // ArrowDown, PageDown\n                this.moveSelection((this.selectedIndex + 1) % this.items.length);\n            }\n            else if (event.keyCode == 36) { // Home\n                this.moveSelection(0);\n            }\n            else if (event.keyCode == 35) { // End\n                this.moveSelection(this.items.length - 1);\n            }\n            else if (event.keyCode == 13) { // Enter\n                this.view.focus();\n            }\n            else if (event.keyCode >= 65 && event.keyCode <= 90 && this.selectedIndex >= 0) { // A-Z\n                let { diagnostic } = this.items[this.selectedIndex], keys = assignKeys(diagnostic.actions);\n                for (let i = 0; i < keys.length; i++)\n                    if (keys[i].toUpperCase().charCodeAt(0) == event.keyCode) {\n                        let found = findDiagnostic(this.view.state.field(lintState).diagnostics, diagnostic);\n                        if (found)\n                            diagnostic.actions[i].apply(view, found.from, found.to);\n                    }\n            }\n            else {\n                return;\n            }\n            event.preventDefault();\n        };\n        let onclick = (event) => {\n            for (let i = 0; i < this.items.length; i++) {\n                if (this.items[i].dom.contains(event.target))\n                    this.moveSelection(i);\n            }\n        };\n        this.list = (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ul\", {\n            tabIndex: 0,\n            role: \"listbox\",\n            \"aria-label\": this.view.state.phrase(\"Diagnostics\"),\n            onkeydown,\n            onclick\n        });\n        this.dom = (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"div\", { class: \"cm-panel-lint\" }, this.list, (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"button\", {\n            type: \"button\",\n            name: \"close\",\n            \"aria-label\": this.view.state.phrase(\"close\"),\n            onclick: () => closeLintPanel(this.view)\n        }, \"×\"));\n        this.update();\n    }\n    get selectedIndex() {\n        let selected = this.view.state.field(lintState).selected;\n        if (!selected)\n            return -1;\n        for (let i = 0; i < this.items.length; i++)\n            if (this.items[i].diagnostic == selected.diagnostic)\n                return i;\n        return -1;\n    }\n    update() {\n        let { diagnostics, selected } = this.view.state.field(lintState);\n        let i = 0, needsSync = false, newSelectedItem = null;\n        let seen = new Set();\n        diagnostics.between(0, this.view.state.doc.length, (_start, _end, { spec }) => {\n            for (let diagnostic of spec.diagnostics) {\n                if (seen.has(diagnostic))\n                    continue;\n                seen.add(diagnostic);\n                let found = -1, item;\n                for (let j = i; j < this.items.length; j++)\n                    if (this.items[j].diagnostic == diagnostic) {\n                        found = j;\n                        break;\n                    }\n                if (found < 0) {\n                    item = new PanelItem(this.view, diagnostic);\n                    this.items.splice(i, 0, item);\n                    needsSync = true;\n                }\n                else {\n                    item = this.items[found];\n                    if (found > i) {\n                        this.items.splice(i, found - i);\n                        needsSync = true;\n                    }\n                }\n                if (selected && item.diagnostic == selected.diagnostic) {\n                    if (!item.dom.hasAttribute(\"aria-selected\")) {\n                        item.dom.setAttribute(\"aria-selected\", \"true\");\n                        newSelectedItem = item;\n                    }\n                }\n                else if (item.dom.hasAttribute(\"aria-selected\")) {\n                    item.dom.removeAttribute(\"aria-selected\");\n                }\n                i++;\n            }\n        });\n        while (i < this.items.length && !(this.items.length == 1 && this.items[0].diagnostic.from < 0)) {\n            needsSync = true;\n            this.items.pop();\n        }\n        if (this.items.length == 0) {\n            this.items.push(new PanelItem(this.view, {\n                from: -1, to: -1,\n                severity: \"info\",\n                message: this.view.state.phrase(\"No diagnostics\")\n            }));\n            needsSync = true;\n        }\n        if (newSelectedItem) {\n            this.list.setAttribute(\"aria-activedescendant\", newSelectedItem.id);\n            this.view.requestMeasure({\n                key: this,\n                read: () => ({ sel: newSelectedItem.dom.getBoundingClientRect(), panel: this.list.getBoundingClientRect() }),\n                write: ({ sel, panel }) => {\n                    let scaleY = panel.height / this.list.offsetHeight;\n                    if (sel.top < panel.top)\n                        this.list.scrollTop -= (panel.top - sel.top) / scaleY;\n                    else if (sel.bottom > panel.bottom)\n                        this.list.scrollTop += (sel.bottom - panel.bottom) / scaleY;\n                }\n            });\n        }\n        else if (this.selectedIndex < 0) {\n            this.list.removeAttribute(\"aria-activedescendant\");\n        }\n        if (needsSync)\n            this.sync();\n    }\n    sync() {\n        let domPos = this.list.firstChild;\n        function rm() {\n            let prev = domPos;\n            domPos = prev.nextSibling;\n            prev.remove();\n        }\n        for (let item of this.items) {\n            if (item.dom.parentNode == this.list) {\n                while (domPos != item.dom)\n                    rm();\n                domPos = item.dom.nextSibling;\n            }\n            else {\n                this.list.insertBefore(item.dom, domPos);\n            }\n        }\n        while (domPos)\n            rm();\n    }\n    moveSelection(selectedIndex) {\n        if (this.selectedIndex < 0)\n            return;\n        let field = this.view.state.field(lintState);\n        let selection = findDiagnostic(field.diagnostics, this.items[selectedIndex].diagnostic);\n        if (!selection)\n            return;\n        this.view.dispatch({\n            selection: { anchor: selection.from, head: selection.to },\n            scrollIntoView: true,\n            effects: movePanelSelection.of(selection)\n        });\n    }\n    static open(view) { return new LintPanel(view); }\n}\nfunction svg(content, attrs = `viewBox=\"0 0 40 40\"`) {\n    return `url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" ${attrs}>${encodeURIComponent(content)}</svg>')`;\n}\nfunction underline(color) {\n    return svg(`<path d=\"m0 2.5 l2 -1.5 l1 0 l2 1.5 l1 0\" stroke=\"${color}\" fill=\"none\" stroke-width=\".7\"/>`, `width=\"6\" height=\"3\"`);\n}\nconst baseTheme = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.baseTheme({\n    \".cm-diagnostic\": {\n        padding: \"3px 6px 3px 8px\",\n        marginLeft: \"-1px\",\n        display: \"block\",\n        whiteSpace: \"pre-wrap\"\n    },\n    \".cm-diagnostic-error\": { borderLeft: \"5px solid #d11\" },\n    \".cm-diagnostic-warning\": { borderLeft: \"5px solid orange\" },\n    \".cm-diagnostic-info\": { borderLeft: \"5px solid #999\" },\n    \".cm-diagnostic-hint\": { borderLeft: \"5px solid #66d\" },\n    \".cm-diagnosticAction\": {\n        font: \"inherit\",\n        border: \"none\",\n        padding: \"2px 4px\",\n        backgroundColor: \"#444\",\n        color: \"white\",\n        borderRadius: \"3px\",\n        marginLeft: \"8px\",\n        cursor: \"pointer\"\n    },\n    \".cm-diagnosticSource\": {\n        fontSize: \"70%\",\n        opacity: .7\n    },\n    \".cm-lintRange\": {\n        backgroundPosition: \"left bottom\",\n        backgroundRepeat: \"repeat-x\",\n        paddingBottom: \"0.7px\",\n    },\n    \".cm-lintRange-error\": { backgroundImage: /*@__PURE__*/underline(\"#d11\") },\n    \".cm-lintRange-warning\": { backgroundImage: /*@__PURE__*/underline(\"orange\") },\n    \".cm-lintRange-info\": { backgroundImage: /*@__PURE__*/underline(\"#999\") },\n    \".cm-lintRange-hint\": { backgroundImage: /*@__PURE__*/underline(\"#66d\") },\n    \".cm-lintRange-active\": { backgroundColor: \"#ffdd9980\" },\n    \".cm-tooltip-lint\": {\n        padding: 0,\n        margin: 0\n    },\n    \".cm-lintPoint\": {\n        position: \"relative\",\n        \"&:after\": {\n            content: '\"\"',\n            position: \"absolute\",\n            bottom: 0,\n            left: \"-2px\",\n            borderLeft: \"3px solid transparent\",\n            borderRight: \"3px solid transparent\",\n            borderBottom: \"4px solid #d11\"\n        }\n    },\n    \".cm-lintPoint-warning\": {\n        \"&:after\": { borderBottomColor: \"orange\" }\n    },\n    \".cm-lintPoint-info\": {\n        \"&:after\": { borderBottomColor: \"#999\" }\n    },\n    \".cm-lintPoint-hint\": {\n        \"&:after\": { borderBottomColor: \"#66d\" }\n    },\n    \".cm-panel.cm-panel-lint\": {\n        position: \"relative\",\n        \"& ul\": {\n            maxHeight: \"100px\",\n            overflowY: \"auto\",\n            \"& [aria-selected]\": {\n                backgroundColor: \"#ddd\",\n                \"& u\": { textDecoration: \"underline\" }\n            },\n            \"&:focus [aria-selected]\": {\n                background_fallback: \"#bdf\",\n                backgroundColor: \"Highlight\",\n                color_fallback: \"white\",\n                color: \"HighlightText\"\n            },\n            \"& u\": { textDecoration: \"none\" },\n            padding: 0,\n            margin: 0\n        },\n        \"& [name=close]\": {\n            position: \"absolute\",\n            top: \"0\",\n            right: \"2px\",\n            background: \"inherit\",\n            border: \"none\",\n            font: \"inherit\",\n            padding: 0,\n            margin: 0\n        }\n    }\n});\nfunction severityWeight(sev) {\n    return sev == \"error\" ? 4 : sev == \"warning\" ? 3 : sev == \"info\" ? 2 : 1;\n}\nfunction maxSeverity(diagnostics) {\n    let sev = \"hint\", weight = 1;\n    for (let d of diagnostics) {\n        let w = severityWeight(d.severity);\n        if (w > weight) {\n            weight = w;\n            sev = d.severity;\n        }\n    }\n    return sev;\n}\nclass LintGutterMarker extends _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.GutterMarker {\n    constructor(diagnostics) {\n        super();\n        this.diagnostics = diagnostics;\n        this.severity = maxSeverity(diagnostics);\n    }\n    toDOM(view) {\n        let elt = document.createElement(\"div\");\n        elt.className = \"cm-lint-marker cm-lint-marker-\" + this.severity;\n        let diagnostics = this.diagnostics;\n        let diagnosticsFilter = view.state.facet(lintGutterConfig).tooltipFilter;\n        if (diagnosticsFilter)\n            diagnostics = diagnosticsFilter(diagnostics, view.state);\n        if (diagnostics.length)\n            elt.onmouseover = () => gutterMarkerMouseOver(view, elt, diagnostics);\n        return elt;\n    }\n}\nfunction trackHoverOn(view, marker) {\n    let mousemove = (event) => {\n        let rect = marker.getBoundingClientRect();\n        if (event.clientX > rect.left - 10 /* Hover.Margin */ && event.clientX < rect.right + 10 /* Hover.Margin */ &&\n            event.clientY > rect.top - 10 /* Hover.Margin */ && event.clientY < rect.bottom + 10 /* Hover.Margin */)\n            return;\n        for (let target = event.target; target; target = target.parentNode) {\n            if (target.nodeType == 1 && target.classList.contains(\"cm-tooltip-lint\"))\n                return;\n        }\n        window.removeEventListener(\"mousemove\", mousemove);\n        if (view.state.field(lintGutterTooltip))\n            view.dispatch({ effects: setLintGutterTooltip.of(null) });\n    };\n    window.addEventListener(\"mousemove\", mousemove);\n}\nfunction gutterMarkerMouseOver(view, marker, diagnostics) {\n    function hovered() {\n        let line = view.elementAtHeight(marker.getBoundingClientRect().top + 5 - view.documentTop);\n        const linePos = view.coordsAtPos(line.from);\n        if (linePos) {\n            view.dispatch({ effects: setLintGutterTooltip.of({\n                    pos: line.from,\n                    above: false,\n                    clip: false,\n                    create() {\n                        return {\n                            dom: diagnosticsTooltip(view, diagnostics),\n                            getCoords: () => marker.getBoundingClientRect()\n                        };\n                    }\n                }) });\n        }\n        marker.onmouseout = marker.onmousemove = null;\n        trackHoverOn(view, marker);\n    }\n    let { hoverTime } = view.state.facet(lintGutterConfig);\n    let hoverTimeout = setTimeout(hovered, hoverTime);\n    marker.onmouseout = () => {\n        clearTimeout(hoverTimeout);\n        marker.onmouseout = marker.onmousemove = null;\n    };\n    marker.onmousemove = () => {\n        clearTimeout(hoverTimeout);\n        hoverTimeout = setTimeout(hovered, hoverTime);\n    };\n}\nfunction markersForDiagnostics(doc, diagnostics) {\n    let byLine = Object.create(null);\n    for (let diagnostic of diagnostics) {\n        let line = doc.lineAt(diagnostic.from);\n        (byLine[line.from] || (byLine[line.from] = [])).push(diagnostic);\n    }\n    let markers = [];\n    for (let line in byLine) {\n        markers.push(new LintGutterMarker(byLine[line]).range(+line));\n    }\n    return _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.RangeSet.of(markers, true);\n}\nconst lintGutterExtension = /*@__PURE__*/(0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.gutter)({\n    class: \"cm-gutter-lint\",\n    markers: view => view.state.field(lintGutterMarkers),\n    widgetMarker: (view, widget, block) => {\n        let diagnostics = [];\n        view.state.field(lintGutterMarkers).between(block.from, block.to, (from, to, value) => {\n            if (from > block.from && from < block.to)\n                diagnostics.push(...value.diagnostics);\n        });\n        return diagnostics.length ? new LintGutterMarker(diagnostics) : null;\n    }\n});\nconst lintGutterMarkers = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateField.define({\n    create() {\n        return _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.RangeSet.empty;\n    },\n    update(markers, tr) {\n        markers = markers.map(tr.changes);\n        let diagnosticFilter = tr.state.facet(lintGutterConfig).markerFilter;\n        for (let effect of tr.effects) {\n            if (effect.is(setDiagnosticsEffect)) {\n                let diagnostics = effect.value;\n                if (diagnosticFilter)\n                    diagnostics = diagnosticFilter(diagnostics || [], tr.state);\n                markers = markersForDiagnostics(tr.state.doc, diagnostics.slice(0));\n            }\n        }\n        return markers;\n    }\n});\nconst setLintGutterTooltip = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define();\nconst lintGutterTooltip = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateField.define({\n    create() { return null; },\n    update(tooltip, tr) {\n        if (tooltip && tr.docChanged)\n            tooltip = hideTooltip(tr, tooltip) ? null : Object.assign(Object.assign({}, tooltip), { pos: tr.changes.mapPos(tooltip.pos) });\n        return tr.effects.reduce((t, e) => e.is(setLintGutterTooltip) ? e.value : t, tooltip);\n    },\n    provide: field => _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.showTooltip.from(field)\n});\nconst lintGutterTheme = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.baseTheme({\n    \".cm-gutter-lint\": {\n        width: \"1.4em\",\n        \"& .cm-gutterElement\": {\n            padding: \".2em\"\n        }\n    },\n    \".cm-lint-marker\": {\n        width: \"1em\",\n        height: \"1em\"\n    },\n    \".cm-lint-marker-info\": {\n        content: /*@__PURE__*/svg(`<path fill=\"#aaf\" stroke=\"#77e\" stroke-width=\"6\" stroke-linejoin=\"round\" d=\"M5 5L35 5L35 35L5 35Z\"/>`)\n    },\n    \".cm-lint-marker-warning\": {\n        content: /*@__PURE__*/svg(`<path fill=\"#fe8\" stroke=\"#fd7\" stroke-width=\"6\" stroke-linejoin=\"round\" d=\"M20 6L37 35L3 35Z\"/>`),\n    },\n    \".cm-lint-marker-error\": {\n        content: /*@__PURE__*/svg(`<circle cx=\"20\" cy=\"20\" r=\"15\" fill=\"#f87\" stroke=\"#f43\" stroke-width=\"6\"/>`)\n    },\n});\nconst lintExtensions = [\n    lintState,\n    /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.decorations.compute([lintState], state => {\n        let { selected, panel } = state.field(lintState);\n        return !selected || !panel || selected.from == selected.to ? _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.none : _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.set([\n            activeMark.range(selected.from, selected.to)\n        ]);\n    }),\n    /*@__PURE__*/(0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.hoverTooltip)(lintTooltip, { hideOn: hideTooltip }),\n    baseTheme\n];\nconst lintGutterConfig = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Facet.define({\n    combine(configs) {\n        return (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.combineConfig)(configs, {\n            hoverTime: 300 /* Hover.Time */,\n            markerFilter: null,\n            tooltipFilter: null\n        });\n    }\n});\n/**\nReturns an extension that installs a gutter showing markers for\neach line that has diagnostics, which can be hovered over to see\nthe diagnostics.\n*/\nfunction lintGutter(config = {}) {\n    return [lintGutterConfig.of(config), lintGutterMarkers, lintGutterExtension, lintGutterTheme, lintGutterTooltip];\n}\n/**\nIterate over the marked diagnostics for the given editor state,\ncalling `f` for each of them. Note that, if the document changed\nsince the diagnostics were created, the `Diagnostic` object will\nhold the original outdated position, whereas the `to` and `from`\narguments hold the diagnostic's current position.\n*/\nfunction forEachDiagnostic(state, f) {\n    let lState = state.field(lintState, false);\n    if (lState && lState.diagnostics.size) {\n        let pending = [], pendingStart = [], lastEnd = -1;\n        for (let iter = _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.RangeSet.iter([lState.diagnostics]);; iter.next()) {\n            for (let i = 0; i < pending.length; i++)\n                if (!iter.value || iter.value.spec.diagnostics.indexOf(pending[i]) < 0) {\n                    f(pending[i], pendingStart[i], lastEnd);\n                    pending.splice(i, 1);\n                    pendingStart.splice(i--, 1);\n                }\n            if (!iter.value)\n                break;\n            for (let d of iter.value.spec.diagnostics)\n                if (pending.indexOf(d) < 0) {\n                    pending.push(d);\n                    pendingStart.push(iter.from);\n                }\n            lastEnd = iter.to;\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lint@6.8.5/node_modules/@codemirror/lint/dist/index.js\n");

/***/ })

};
;