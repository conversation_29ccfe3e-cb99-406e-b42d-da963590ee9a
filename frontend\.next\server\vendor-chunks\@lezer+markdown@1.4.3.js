"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lezer+markdown@1.4.3";
exports.ids = ["vendor-chunks/@lezer+markdown@1.4.3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@lezer+markdown@1.4.3/node_modules/@lezer/markdown/dist/index.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lezer+markdown@1.4.3/node_modules/@lezer/markdown/dist/index.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Autolink: () => (/* binding */ Autolink),\n/* harmony export */   BlockContext: () => (/* binding */ BlockContext),\n/* harmony export */   Element: () => (/* binding */ Element),\n/* harmony export */   Emoji: () => (/* binding */ Emoji),\n/* harmony export */   GFM: () => (/* binding */ GFM),\n/* harmony export */   InlineContext: () => (/* binding */ InlineContext),\n/* harmony export */   LeafBlock: () => (/* binding */ LeafBlock),\n/* harmony export */   Line: () => (/* binding */ Line),\n/* harmony export */   MarkdownParser: () => (/* binding */ MarkdownParser),\n/* harmony export */   Strikethrough: () => (/* binding */ Strikethrough),\n/* harmony export */   Subscript: () => (/* binding */ Subscript),\n/* harmony export */   Superscript: () => (/* binding */ Superscript),\n/* harmony export */   Table: () => (/* binding */ Table),\n/* harmony export */   TaskList: () => (/* binding */ TaskList),\n/* harmony export */   parseCode: () => (/* binding */ parseCode),\n/* harmony export */   parser: () => (/* binding */ parser)\n/* harmony export */ });\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js\");\n\n\n\nclass CompositeBlock {\n    static create(type, value, from, parentHash, end) {\n        let hash = (parentHash + (parentHash << 8) + type + (value << 4)) | 0;\n        return new CompositeBlock(type, value, from, hash, end, [], []);\n    }\n    constructor(type, \n    // Used for indentation in list items, markup character in lists\n    value, from, hash, end, children, positions) {\n        this.type = type;\n        this.value = value;\n        this.from = from;\n        this.hash = hash;\n        this.end = end;\n        this.children = children;\n        this.positions = positions;\n        this.hashProp = [[_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.contextHash, hash]];\n    }\n    addChild(child, pos) {\n        if (child.prop(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.contextHash) != this.hash)\n            child = new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree(child.type, child.children, child.positions, child.length, this.hashProp);\n        this.children.push(child);\n        this.positions.push(pos);\n    }\n    toTree(nodeSet, end = this.end) {\n        let last = this.children.length - 1;\n        if (last >= 0)\n            end = Math.max(end, this.positions[last] + this.children[last].length + this.from);\n        return new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree(nodeSet.types[this.type], this.children, this.positions, end - this.from).balance({\n            makeTree: (children, positions, length) => new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeType.none, children, positions, length, this.hashProp)\n        });\n    }\n}\nvar Type;\n(function (Type) {\n    Type[Type[\"Document\"] = 1] = \"Document\";\n    Type[Type[\"CodeBlock\"] = 2] = \"CodeBlock\";\n    Type[Type[\"FencedCode\"] = 3] = \"FencedCode\";\n    Type[Type[\"Blockquote\"] = 4] = \"Blockquote\";\n    Type[Type[\"HorizontalRule\"] = 5] = \"HorizontalRule\";\n    Type[Type[\"BulletList\"] = 6] = \"BulletList\";\n    Type[Type[\"OrderedList\"] = 7] = \"OrderedList\";\n    Type[Type[\"ListItem\"] = 8] = \"ListItem\";\n    Type[Type[\"ATXHeading1\"] = 9] = \"ATXHeading1\";\n    Type[Type[\"ATXHeading2\"] = 10] = \"ATXHeading2\";\n    Type[Type[\"ATXHeading3\"] = 11] = \"ATXHeading3\";\n    Type[Type[\"ATXHeading4\"] = 12] = \"ATXHeading4\";\n    Type[Type[\"ATXHeading5\"] = 13] = \"ATXHeading5\";\n    Type[Type[\"ATXHeading6\"] = 14] = \"ATXHeading6\";\n    Type[Type[\"SetextHeading1\"] = 15] = \"SetextHeading1\";\n    Type[Type[\"SetextHeading2\"] = 16] = \"SetextHeading2\";\n    Type[Type[\"HTMLBlock\"] = 17] = \"HTMLBlock\";\n    Type[Type[\"LinkReference\"] = 18] = \"LinkReference\";\n    Type[Type[\"Paragraph\"] = 19] = \"Paragraph\";\n    Type[Type[\"CommentBlock\"] = 20] = \"CommentBlock\";\n    Type[Type[\"ProcessingInstructionBlock\"] = 21] = \"ProcessingInstructionBlock\";\n    // Inline\n    Type[Type[\"Escape\"] = 22] = \"Escape\";\n    Type[Type[\"Entity\"] = 23] = \"Entity\";\n    Type[Type[\"HardBreak\"] = 24] = \"HardBreak\";\n    Type[Type[\"Emphasis\"] = 25] = \"Emphasis\";\n    Type[Type[\"StrongEmphasis\"] = 26] = \"StrongEmphasis\";\n    Type[Type[\"Link\"] = 27] = \"Link\";\n    Type[Type[\"Image\"] = 28] = \"Image\";\n    Type[Type[\"InlineCode\"] = 29] = \"InlineCode\";\n    Type[Type[\"HTMLTag\"] = 30] = \"HTMLTag\";\n    Type[Type[\"Comment\"] = 31] = \"Comment\";\n    Type[Type[\"ProcessingInstruction\"] = 32] = \"ProcessingInstruction\";\n    Type[Type[\"Autolink\"] = 33] = \"Autolink\";\n    // Smaller tokens\n    Type[Type[\"HeaderMark\"] = 34] = \"HeaderMark\";\n    Type[Type[\"QuoteMark\"] = 35] = \"QuoteMark\";\n    Type[Type[\"ListMark\"] = 36] = \"ListMark\";\n    Type[Type[\"LinkMark\"] = 37] = \"LinkMark\";\n    Type[Type[\"EmphasisMark\"] = 38] = \"EmphasisMark\";\n    Type[Type[\"CodeMark\"] = 39] = \"CodeMark\";\n    Type[Type[\"CodeText\"] = 40] = \"CodeText\";\n    Type[Type[\"CodeInfo\"] = 41] = \"CodeInfo\";\n    Type[Type[\"LinkTitle\"] = 42] = \"LinkTitle\";\n    Type[Type[\"LinkLabel\"] = 43] = \"LinkLabel\";\n    Type[Type[\"URL\"] = 44] = \"URL\";\n})(Type || (Type = {}));\n/**\nData structure used to accumulate a block's content during [leaf\nblock parsing](#BlockParser.leaf).\n*/\nclass LeafBlock {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The start position of the block.\n    */\n    start, \n    /**\n    The block's text content.\n    */\n    content) {\n        this.start = start;\n        this.content = content;\n        /**\n        @internal\n        */\n        this.marks = [];\n        /**\n        The block parsers active for this block.\n        */\n        this.parsers = [];\n    }\n}\n/**\nData structure used during block-level per-line parsing.\n*/\nclass Line {\n    constructor() {\n        /**\n        The line's full text.\n        */\n        this.text = \"\";\n        /**\n        The base indent provided by the composite contexts (that have\n        been handled so far).\n        */\n        this.baseIndent = 0;\n        /**\n        The string position corresponding to the base indent.\n        */\n        this.basePos = 0;\n        /**\n        The number of contexts handled @internal\n        */\n        this.depth = 0;\n        /**\n        Any markers (i.e. block quote markers) parsed for the contexts. @internal\n        */\n        this.markers = [];\n        /**\n        The position of the next non-whitespace character beyond any\n        list, blockquote, or other composite block markers.\n        */\n        this.pos = 0;\n        /**\n        The column of the next non-whitespace character.\n        */\n        this.indent = 0;\n        /**\n        The character code of the character after `pos`.\n        */\n        this.next = -1;\n    }\n    /**\n    @internal\n    */\n    forward() {\n        if (this.basePos > this.pos)\n            this.forwardInner();\n    }\n    /**\n    @internal\n    */\n    forwardInner() {\n        let newPos = this.skipSpace(this.basePos);\n        this.indent = this.countIndent(newPos, this.pos, this.indent);\n        this.pos = newPos;\n        this.next = newPos == this.text.length ? -1 : this.text.charCodeAt(newPos);\n    }\n    /**\n    Skip whitespace after the given position, return the position of\n    the next non-space character or the end of the line if there's\n    only space after `from`.\n    */\n    skipSpace(from) { return skipSpace(this.text, from); }\n    /**\n    @internal\n    */\n    reset(text) {\n        this.text = text;\n        this.baseIndent = this.basePos = this.pos = this.indent = 0;\n        this.forwardInner();\n        this.depth = 1;\n        while (this.markers.length)\n            this.markers.pop();\n    }\n    /**\n    Move the line's base position forward to the given position.\n    This should only be called by composite [block\n    parsers](#BlockParser.parse) or [markup skipping\n    functions](#NodeSpec.composite).\n    */\n    moveBase(to) {\n        this.basePos = to;\n        this.baseIndent = this.countIndent(to, this.pos, this.indent);\n    }\n    /**\n    Move the line's base position forward to the given _column_.\n    */\n    moveBaseColumn(indent) {\n        this.baseIndent = indent;\n        this.basePos = this.findColumn(indent);\n    }\n    /**\n    Store a composite-block-level marker. Should be called from\n    [markup skipping functions](#NodeSpec.composite) when they\n    consume any non-whitespace characters.\n    */\n    addMarker(elt) {\n        this.markers.push(elt);\n    }\n    /**\n    Find the column position at `to`, optionally starting at a given\n    position and column.\n    */\n    countIndent(to, from = 0, indent = 0) {\n        for (let i = from; i < to; i++)\n            indent += this.text.charCodeAt(i) == 9 ? 4 - indent % 4 : 1;\n        return indent;\n    }\n    /**\n    Find the position corresponding to the given column.\n    */\n    findColumn(goal) {\n        let i = 0;\n        for (let indent = 0; i < this.text.length && indent < goal; i++)\n            indent += this.text.charCodeAt(i) == 9 ? 4 - indent % 4 : 1;\n        return i;\n    }\n    /**\n    @internal\n    */\n    scrub() {\n        if (!this.baseIndent)\n            return this.text;\n        let result = \"\";\n        for (let i = 0; i < this.basePos; i++)\n            result += \" \";\n        return result + this.text.slice(this.basePos);\n    }\n}\nfunction skipForList(bl, cx, line) {\n    if (line.pos == line.text.length ||\n        (bl != cx.block && line.indent >= cx.stack[line.depth + 1].value + line.baseIndent))\n        return true;\n    if (line.indent >= line.baseIndent + 4)\n        return false;\n    let size = (bl.type == Type.OrderedList ? isOrderedList : isBulletList)(line, cx, false);\n    return size > 0 &&\n        (bl.type != Type.BulletList || isHorizontalRule(line, cx, false) < 0) &&\n        line.text.charCodeAt(line.pos + size - 1) == bl.value;\n}\nconst DefaultSkipMarkup = {\n    [Type.Blockquote](bl, cx, line) {\n        if (line.next != 62 /* '>' */)\n            return false;\n        line.markers.push(elt(Type.QuoteMark, cx.lineStart + line.pos, cx.lineStart + line.pos + 1));\n        line.moveBase(line.pos + (space(line.text.charCodeAt(line.pos + 1)) ? 2 : 1));\n        bl.end = cx.lineStart + line.text.length;\n        return true;\n    },\n    [Type.ListItem](bl, _cx, line) {\n        if (line.indent < line.baseIndent + bl.value && line.next > -1)\n            return false;\n        line.moveBaseColumn(line.baseIndent + bl.value);\n        return true;\n    },\n    [Type.OrderedList]: skipForList,\n    [Type.BulletList]: skipForList,\n    [Type.Document]() { return true; }\n};\nfunction space(ch) { return ch == 32 || ch == 9 || ch == 10 || ch == 13; }\nfunction skipSpace(line, i = 0) {\n    while (i < line.length && space(line.charCodeAt(i)))\n        i++;\n    return i;\n}\nfunction skipSpaceBack(line, i, to) {\n    while (i > to && space(line.charCodeAt(i - 1)))\n        i--;\n    return i;\n}\nfunction isFencedCode(line) {\n    if (line.next != 96 && line.next != 126 /* '`~' */)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == line.next)\n        pos++;\n    if (pos < line.pos + 3)\n        return -1;\n    if (line.next == 96)\n        for (let i = pos; i < line.text.length; i++)\n            if (line.text.charCodeAt(i) == 96)\n                return -1;\n    return pos;\n}\nfunction isBlockquote(line) {\n    return line.next != 62 /* '>' */ ? -1 : line.text.charCodeAt(line.pos + 1) == 32 ? 2 : 1;\n}\nfunction isHorizontalRule(line, cx, breaking) {\n    if (line.next != 42 && line.next != 45 && line.next != 95 /* '_-*' */)\n        return -1;\n    let count = 1;\n    for (let pos = line.pos + 1; pos < line.text.length; pos++) {\n        let ch = line.text.charCodeAt(pos);\n        if (ch == line.next)\n            count++;\n        else if (!space(ch))\n            return -1;\n    }\n    // Setext headers take precedence\n    if (breaking && line.next == 45 && isSetextUnderline(line) > -1 && line.depth == cx.stack.length &&\n        cx.parser.leafBlockParsers.indexOf(DefaultLeafBlocks.SetextHeading) > -1)\n        return -1;\n    return count < 3 ? -1 : 1;\n}\nfunction inList(cx, type) {\n    for (let i = cx.stack.length - 1; i >= 0; i--)\n        if (cx.stack[i].type == type)\n            return true;\n    return false;\n}\nfunction isBulletList(line, cx, breaking) {\n    return (line.next == 45 || line.next == 43 || line.next == 42 /* '-+*' */) &&\n        (line.pos == line.text.length - 1 || space(line.text.charCodeAt(line.pos + 1))) &&\n        (!breaking || inList(cx, Type.BulletList) || line.skipSpace(line.pos + 2) < line.text.length) ? 1 : -1;\n}\nfunction isOrderedList(line, cx, breaking) {\n    let pos = line.pos, next = line.next;\n    for (;;) {\n        if (next >= 48 && next <= 57 /* '0-9' */)\n            pos++;\n        else\n            break;\n        if (pos == line.text.length)\n            return -1;\n        next = line.text.charCodeAt(pos);\n    }\n    if (pos == line.pos || pos > line.pos + 9 ||\n        (next != 46 && next != 41 /* '.)' */) ||\n        (pos < line.text.length - 1 && !space(line.text.charCodeAt(pos + 1))) ||\n        breaking && !inList(cx, Type.OrderedList) &&\n            (line.skipSpace(pos + 1) == line.text.length || pos > line.pos + 1 || line.next != 49 /* '1' */))\n        return -1;\n    return pos + 1 - line.pos;\n}\nfunction isAtxHeading(line) {\n    if (line.next != 35 /* '#' */)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == 35)\n        pos++;\n    if (pos < line.text.length && line.text.charCodeAt(pos) != 32)\n        return -1;\n    let size = pos - line.pos;\n    return size > 6 ? -1 : size;\n}\nfunction isSetextUnderline(line) {\n    if (line.next != 45 && line.next != 61 /* '-=' */ || line.indent >= line.baseIndent + 4)\n        return -1;\n    let pos = line.pos + 1;\n    while (pos < line.text.length && line.text.charCodeAt(pos) == line.next)\n        pos++;\n    let end = pos;\n    while (pos < line.text.length && space(line.text.charCodeAt(pos)))\n        pos++;\n    return pos == line.text.length ? end : -1;\n}\nconst EmptyLine = /^[ \\t]*$/, CommentEnd = /-->/, ProcessingEnd = /\\?>/;\nconst HTMLBlockStyle = [\n    [/^<(?:script|pre|style)(?:\\s|>|$)/i, /<\\/(?:script|pre|style)>/i],\n    [/^\\s*<!--/, CommentEnd],\n    [/^\\s*<\\?/, ProcessingEnd],\n    [/^\\s*<![A-Z]/, />/],\n    [/^\\s*<!\\[CDATA\\[/, /\\]\\]>/],\n    [/^\\s*<\\/?(?:address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul)(?:\\s|\\/?>|$)/i, EmptyLine],\n    [/^\\s*(?:<\\/[a-z][\\w-]*\\s*>|<[a-z][\\w-]*(\\s+[a-z:_][\\w-.]*(?:\\s*=\\s*(?:[^\\s\"'=<>`]+|'[^']*'|\"[^\"]*\"))?)*\\s*>)\\s*$/i, EmptyLine]\n];\nfunction isHTMLBlock(line, _cx, breaking) {\n    if (line.next != 60 /* '<' */)\n        return -1;\n    let rest = line.text.slice(line.pos);\n    for (let i = 0, e = HTMLBlockStyle.length - (breaking ? 1 : 0); i < e; i++)\n        if (HTMLBlockStyle[i][0].test(rest))\n            return i;\n    return -1;\n}\nfunction getListIndent(line, pos) {\n    let indentAfter = line.countIndent(pos, line.pos, line.indent);\n    let indented = line.countIndent(line.skipSpace(pos), pos, indentAfter);\n    return indented >= indentAfter + 5 ? indentAfter + 1 : indented;\n}\nfunction addCodeText(marks, from, to) {\n    let last = marks.length - 1;\n    if (last >= 0 && marks[last].to == from && marks[last].type == Type.CodeText)\n        marks[last].to = to;\n    else\n        marks.push(elt(Type.CodeText, from, to));\n}\n// Rules for parsing blocks. A return value of false means the rule\n// doesn't apply here, true means it does. When true is returned and\n// `p.line` has been updated, the rule is assumed to have consumed a\n// leaf block. Otherwise, it is assumed to have opened a context.\nconst DefaultBlockParsers = {\n    LinkReference: undefined,\n    IndentedCode(cx, line) {\n        let base = line.baseIndent + 4;\n        if (line.indent < base)\n            return false;\n        let start = line.findColumn(base);\n        let from = cx.lineStart + start, to = cx.lineStart + line.text.length;\n        let marks = [], pendingMarks = [];\n        addCodeText(marks, from, to);\n        while (cx.nextLine() && line.depth >= cx.stack.length) {\n            if (line.pos == line.text.length) { // Empty\n                addCodeText(pendingMarks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    pendingMarks.push(m);\n            }\n            else if (line.indent < base) {\n                break;\n            }\n            else {\n                if (pendingMarks.length) {\n                    for (let m of pendingMarks) {\n                        if (m.type == Type.CodeText)\n                            addCodeText(marks, m.from, m.to);\n                        else\n                            marks.push(m);\n                    }\n                    pendingMarks = [];\n                }\n                addCodeText(marks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    marks.push(m);\n                to = cx.lineStart + line.text.length;\n                let codeStart = cx.lineStart + line.findColumn(line.baseIndent + 4);\n                if (codeStart < to)\n                    addCodeText(marks, codeStart, to);\n            }\n        }\n        if (pendingMarks.length) {\n            pendingMarks = pendingMarks.filter(m => m.type != Type.CodeText);\n            if (pendingMarks.length)\n                line.markers = pendingMarks.concat(line.markers);\n        }\n        cx.addNode(cx.buffer.writeElements(marks, -from).finish(Type.CodeBlock, to - from), from);\n        return true;\n    },\n    FencedCode(cx, line) {\n        let fenceEnd = isFencedCode(line);\n        if (fenceEnd < 0)\n            return false;\n        let from = cx.lineStart + line.pos, ch = line.next, len = fenceEnd - line.pos;\n        let infoFrom = line.skipSpace(fenceEnd), infoTo = skipSpaceBack(line.text, line.text.length, infoFrom);\n        let marks = [elt(Type.CodeMark, from, from + len)];\n        if (infoFrom < infoTo)\n            marks.push(elt(Type.CodeInfo, cx.lineStart + infoFrom, cx.lineStart + infoTo));\n        for (let first = true; cx.nextLine() && line.depth >= cx.stack.length; first = false) {\n            let i = line.pos;\n            if (line.indent - line.baseIndent < 4)\n                while (i < line.text.length && line.text.charCodeAt(i) == ch)\n                    i++;\n            if (i - line.pos >= len && line.skipSpace(i) == line.text.length) {\n                for (let m of line.markers)\n                    marks.push(m);\n                marks.push(elt(Type.CodeMark, cx.lineStart + line.pos, cx.lineStart + i));\n                cx.nextLine();\n                break;\n            }\n            else {\n                if (!first)\n                    addCodeText(marks, cx.lineStart - 1, cx.lineStart);\n                for (let m of line.markers)\n                    marks.push(m);\n                let textStart = cx.lineStart + line.basePos, textEnd = cx.lineStart + line.text.length;\n                if (textStart < textEnd)\n                    addCodeText(marks, textStart, textEnd);\n            }\n        }\n        cx.addNode(cx.buffer.writeElements(marks, -from)\n            .finish(Type.FencedCode, cx.prevLineEnd() - from), from);\n        return true;\n    },\n    Blockquote(cx, line) {\n        let size = isBlockquote(line);\n        if (size < 0)\n            return false;\n        cx.startContext(Type.Blockquote, line.pos);\n        cx.addNode(Type.QuoteMark, cx.lineStart + line.pos, cx.lineStart + line.pos + 1);\n        line.moveBase(line.pos + size);\n        return null;\n    },\n    HorizontalRule(cx, line) {\n        if (isHorizontalRule(line, cx, false) < 0)\n            return false;\n        let from = cx.lineStart + line.pos;\n        cx.nextLine();\n        cx.addNode(Type.HorizontalRule, from);\n        return true;\n    },\n    BulletList(cx, line) {\n        let size = isBulletList(line, cx, false);\n        if (size < 0)\n            return false;\n        if (cx.block.type != Type.BulletList)\n            cx.startContext(Type.BulletList, line.basePos, line.next);\n        let newBase = getListIndent(line, line.pos + 1);\n        cx.startContext(Type.ListItem, line.basePos, newBase - line.baseIndent);\n        cx.addNode(Type.ListMark, cx.lineStart + line.pos, cx.lineStart + line.pos + size);\n        line.moveBaseColumn(newBase);\n        return null;\n    },\n    OrderedList(cx, line) {\n        let size = isOrderedList(line, cx, false);\n        if (size < 0)\n            return false;\n        if (cx.block.type != Type.OrderedList)\n            cx.startContext(Type.OrderedList, line.basePos, line.text.charCodeAt(line.pos + size - 1));\n        let newBase = getListIndent(line, line.pos + size);\n        cx.startContext(Type.ListItem, line.basePos, newBase - line.baseIndent);\n        cx.addNode(Type.ListMark, cx.lineStart + line.pos, cx.lineStart + line.pos + size);\n        line.moveBaseColumn(newBase);\n        return null;\n    },\n    ATXHeading(cx, line) {\n        let size = isAtxHeading(line);\n        if (size < 0)\n            return false;\n        let off = line.pos, from = cx.lineStart + off;\n        let endOfSpace = skipSpaceBack(line.text, line.text.length, off), after = endOfSpace;\n        while (after > off && line.text.charCodeAt(after - 1) == line.next)\n            after--;\n        if (after == endOfSpace || after == off || !space(line.text.charCodeAt(after - 1)))\n            after = line.text.length;\n        let buf = cx.buffer\n            .write(Type.HeaderMark, 0, size)\n            .writeElements(cx.parser.parseInline(line.text.slice(off + size + 1, after), from + size + 1), -from);\n        if (after < line.text.length)\n            buf.write(Type.HeaderMark, after - off, endOfSpace - off);\n        let node = buf.finish(Type.ATXHeading1 - 1 + size, line.text.length - off);\n        cx.nextLine();\n        cx.addNode(node, from);\n        return true;\n    },\n    HTMLBlock(cx, line) {\n        let type = isHTMLBlock(line, cx, false);\n        if (type < 0)\n            return false;\n        let from = cx.lineStart + line.pos, end = HTMLBlockStyle[type][1];\n        let marks = [], trailing = end != EmptyLine;\n        while (!end.test(line.text) && cx.nextLine()) {\n            if (line.depth < cx.stack.length) {\n                trailing = false;\n                break;\n            }\n            for (let m of line.markers)\n                marks.push(m);\n        }\n        if (trailing)\n            cx.nextLine();\n        let nodeType = end == CommentEnd ? Type.CommentBlock : end == ProcessingEnd ? Type.ProcessingInstructionBlock : Type.HTMLBlock;\n        let to = cx.prevLineEnd();\n        cx.addNode(cx.buffer.writeElements(marks, -from).finish(nodeType, to - from), from);\n        return true;\n    },\n    SetextHeading: undefined // Specifies relative precedence for block-continue function\n};\n// This implements a state machine that incrementally parses link references. At each\n// next line, it looks ahead to see if the line continues the reference or not. If it\n// doesn't and a valid link is available ending before that line, it finishes that.\n// Similarly, on `finish` (when the leaf is terminated by external circumstances), it\n// creates a link reference if there's a valid reference up to the current point.\nclass LinkReferenceParser {\n    constructor(leaf) {\n        this.stage = 0 /* RefStage.Start */;\n        this.elts = [];\n        this.pos = 0;\n        this.start = leaf.start;\n        this.advance(leaf.content);\n    }\n    nextLine(cx, line, leaf) {\n        if (this.stage == -1 /* RefStage.Failed */)\n            return false;\n        let content = leaf.content + \"\\n\" + line.scrub();\n        let finish = this.advance(content);\n        if (finish > -1 && finish < content.length)\n            return this.complete(cx, leaf, finish);\n        return false;\n    }\n    finish(cx, leaf) {\n        if ((this.stage == 2 /* RefStage.Link */ || this.stage == 3 /* RefStage.Title */) && skipSpace(leaf.content, this.pos) == leaf.content.length)\n            return this.complete(cx, leaf, leaf.content.length);\n        return false;\n    }\n    complete(cx, leaf, len) {\n        cx.addLeafElement(leaf, elt(Type.LinkReference, this.start, this.start + len, this.elts));\n        return true;\n    }\n    nextStage(elt) {\n        if (elt) {\n            this.pos = elt.to - this.start;\n            this.elts.push(elt);\n            this.stage++;\n            return true;\n        }\n        if (elt === false)\n            this.stage = -1 /* RefStage.Failed */;\n        return false;\n    }\n    advance(content) {\n        for (;;) {\n            if (this.stage == -1 /* RefStage.Failed */) {\n                return -1;\n            }\n            else if (this.stage == 0 /* RefStage.Start */) {\n                if (!this.nextStage(parseLinkLabel(content, this.pos, this.start, true)))\n                    return -1;\n                if (content.charCodeAt(this.pos) != 58 /* ':' */)\n                    return this.stage = -1 /* RefStage.Failed */;\n                this.elts.push(elt(Type.LinkMark, this.pos + this.start, this.pos + this.start + 1));\n                this.pos++;\n            }\n            else if (this.stage == 1 /* RefStage.Label */) {\n                if (!this.nextStage(parseURL(content, skipSpace(content, this.pos), this.start)))\n                    return -1;\n            }\n            else if (this.stage == 2 /* RefStage.Link */) {\n                let skip = skipSpace(content, this.pos), end = 0;\n                if (skip > this.pos) {\n                    let title = parseLinkTitle(content, skip, this.start);\n                    if (title) {\n                        let titleEnd = lineEnd(content, title.to - this.start);\n                        if (titleEnd > 0) {\n                            this.nextStage(title);\n                            end = titleEnd;\n                        }\n                    }\n                }\n                if (!end)\n                    end = lineEnd(content, this.pos);\n                return end > 0 && end < content.length ? end : -1;\n            }\n            else { // RefStage.Title\n                return lineEnd(content, this.pos);\n            }\n        }\n    }\n}\nfunction lineEnd(text, pos) {\n    for (; pos < text.length; pos++) {\n        let next = text.charCodeAt(pos);\n        if (next == 10)\n            break;\n        if (!space(next))\n            return -1;\n    }\n    return pos;\n}\nclass SetextHeadingParser {\n    nextLine(cx, line, leaf) {\n        let underline = line.depth < cx.stack.length ? -1 : isSetextUnderline(line);\n        let next = line.next;\n        if (underline < 0)\n            return false;\n        let underlineMark = elt(Type.HeaderMark, cx.lineStart + line.pos, cx.lineStart + underline);\n        cx.nextLine();\n        cx.addLeafElement(leaf, elt(next == 61 ? Type.SetextHeading1 : Type.SetextHeading2, leaf.start, cx.prevLineEnd(), [\n            ...cx.parser.parseInline(leaf.content, leaf.start),\n            underlineMark\n        ]));\n        return true;\n    }\n    finish() {\n        return false;\n    }\n}\nconst DefaultLeafBlocks = {\n    LinkReference(_, leaf) { return leaf.content.charCodeAt(0) == 91 /* '[' */ ? new LinkReferenceParser(leaf) : null; },\n    SetextHeading() { return new SetextHeadingParser; }\n};\nconst DefaultEndLeaf = [\n    (_, line) => isAtxHeading(line) >= 0,\n    (_, line) => isFencedCode(line) >= 0,\n    (_, line) => isBlockquote(line) >= 0,\n    (p, line) => isBulletList(line, p, true) >= 0,\n    (p, line) => isOrderedList(line, p, true) >= 0,\n    (p, line) => isHorizontalRule(line, p, true) >= 0,\n    (p, line) => isHTMLBlock(line, p, true) >= 0\n];\nconst scanLineResult = { text: \"\", end: 0 };\n/**\nBlock-level parsing functions get access to this context object.\n*/\nclass BlockContext {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parser configuration used.\n    */\n    parser, \n    /**\n    @internal\n    */\n    input, fragments, \n    /**\n    @internal\n    */\n    ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.line = new Line();\n        this.atEnd = false;\n        /**\n        For reused nodes on gaps, we can't directly put the original\n        node into the tree, since that may be bigger than its parent.\n        When this happens, we create a dummy tree that is replaced by\n        the proper node in `injectGaps` @internal\n        */\n        this.reusePlaceholders = new Map;\n        this.stoppedAt = null;\n        /**\n        The range index that absoluteLineStart points into @internal\n        */\n        this.rangeI = 0;\n        this.to = ranges[ranges.length - 1].to;\n        this.lineStart = this.absoluteLineStart = this.absoluteLineEnd = ranges[0].from;\n        this.block = CompositeBlock.create(Type.Document, 0, this.lineStart, 0, 0);\n        this.stack = [this.block];\n        this.fragments = fragments.length ? new FragmentCursor(fragments, input) : null;\n        this.readLine();\n    }\n    get parsedPos() {\n        return this.absoluteLineStart;\n    }\n    advance() {\n        if (this.stoppedAt != null && this.absoluteLineStart > this.stoppedAt)\n            return this.finish();\n        let { line } = this;\n        for (;;) {\n            for (let markI = 0;;) {\n                let next = line.depth < this.stack.length ? this.stack[this.stack.length - 1] : null;\n                while (markI < line.markers.length && (!next || line.markers[markI].from < next.end)) {\n                    let mark = line.markers[markI++];\n                    this.addNode(mark.type, mark.from, mark.to);\n                }\n                if (!next)\n                    break;\n                this.finishContext();\n            }\n            if (line.pos < line.text.length)\n                break;\n            // Empty line\n            if (!this.nextLine())\n                return this.finish();\n        }\n        if (this.fragments && this.reuseFragment(line.basePos))\n            return null;\n        start: for (;;) {\n            for (let type of this.parser.blockParsers)\n                if (type) {\n                    let result = type(this, line);\n                    if (result != false) {\n                        if (result == true)\n                            return null;\n                        line.forward();\n                        continue start;\n                    }\n                }\n            break;\n        }\n        let leaf = new LeafBlock(this.lineStart + line.pos, line.text.slice(line.pos));\n        for (let parse of this.parser.leafBlockParsers)\n            if (parse) {\n                let parser = parse(this, leaf);\n                if (parser)\n                    leaf.parsers.push(parser);\n            }\n        lines: while (this.nextLine()) {\n            if (line.pos == line.text.length)\n                break;\n            if (line.indent < line.baseIndent + 4) {\n                for (let stop of this.parser.endLeafBlock)\n                    if (stop(this, line, leaf))\n                        break lines;\n            }\n            for (let parser of leaf.parsers)\n                if (parser.nextLine(this, line, leaf))\n                    return null;\n            leaf.content += \"\\n\" + line.scrub();\n            for (let m of line.markers)\n                leaf.marks.push(m);\n        }\n        this.finishLeaf(leaf);\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    reuseFragment(start) {\n        if (!this.fragments.moveTo(this.absoluteLineStart + start, this.absoluteLineStart) ||\n            !this.fragments.matches(this.block.hash))\n            return false;\n        let taken = this.fragments.takeNodes(this);\n        if (!taken)\n            return false;\n        this.absoluteLineStart += taken;\n        this.lineStart = toRelative(this.absoluteLineStart, this.ranges);\n        this.moveRangeI();\n        if (this.absoluteLineStart < this.to) {\n            this.lineStart++;\n            this.absoluteLineStart++;\n            this.readLine();\n        }\n        else {\n            this.atEnd = true;\n            this.readLine();\n        }\n        return true;\n    }\n    /**\n    The number of parent blocks surrounding the current block.\n    */\n    get depth() {\n        return this.stack.length;\n    }\n    /**\n    Get the type of the parent block at the given depth. When no\n    depth is passed, return the type of the innermost parent.\n    */\n    parentType(depth = this.depth - 1) {\n        return this.parser.nodeSet.types[this.stack[depth].type];\n    }\n    /**\n    Move to the next input line. This should only be called by\n    (non-composite) [block parsers](#BlockParser.parse) that consume\n    the line directly, or leaf block parser\n    [`nextLine`](#LeafBlockParser.nextLine) methods when they\n    consume the current line (and return true).\n    */\n    nextLine() {\n        this.lineStart += this.line.text.length;\n        if (this.absoluteLineEnd >= this.to) {\n            this.absoluteLineStart = this.absoluteLineEnd;\n            this.atEnd = true;\n            this.readLine();\n            return false;\n        }\n        else {\n            this.lineStart++;\n            this.absoluteLineStart = this.absoluteLineEnd + 1;\n            this.moveRangeI();\n            this.readLine();\n            return true;\n        }\n    }\n    /**\n    Retrieve the text of the line after the current one, without\n    actually moving the context's current line forward.\n    */\n    peekLine() {\n        return this.scanLine(this.absoluteLineEnd + 1).text;\n    }\n    moveRangeI() {\n        while (this.rangeI < this.ranges.length - 1 && this.absoluteLineStart >= this.ranges[this.rangeI].to) {\n            this.rangeI++;\n            this.absoluteLineStart = Math.max(this.absoluteLineStart, this.ranges[this.rangeI].from);\n        }\n    }\n    /**\n    @internal\n    Collect the text for the next line.\n    */\n    scanLine(start) {\n        let r = scanLineResult;\n        r.end = start;\n        if (start >= this.to) {\n            r.text = \"\";\n        }\n        else {\n            r.text = this.lineChunkAt(start);\n            r.end += r.text.length;\n            if (this.ranges.length > 1) {\n                let textOffset = this.absoluteLineStart, rangeI = this.rangeI;\n                while (this.ranges[rangeI].to < r.end) {\n                    rangeI++;\n                    let nextFrom = this.ranges[rangeI].from;\n                    let after = this.lineChunkAt(nextFrom);\n                    r.end = nextFrom + after.length;\n                    r.text = r.text.slice(0, this.ranges[rangeI - 1].to - textOffset) + after;\n                    textOffset = r.end - r.text.length;\n                }\n            }\n        }\n        return r;\n    }\n    /**\n    @internal\n    Populate this.line with the content of the next line. Skip\n    leading characters covered by composite blocks.\n    */\n    readLine() {\n        let { line } = this, { text, end } = this.scanLine(this.absoluteLineStart);\n        this.absoluteLineEnd = end;\n        line.reset(text);\n        for (; line.depth < this.stack.length; line.depth++) {\n            let cx = this.stack[line.depth], handler = this.parser.skipContextMarkup[cx.type];\n            if (!handler)\n                throw new Error(\"Unhandled block context \" + Type[cx.type]);\n            if (!handler(cx, this, line))\n                break;\n            line.forward();\n        }\n    }\n    lineChunkAt(pos) {\n        let next = this.input.chunk(pos), text;\n        if (!this.input.lineChunks) {\n            let eol = next.indexOf(\"\\n\");\n            text = eol < 0 ? next : next.slice(0, eol);\n        }\n        else {\n            text = next == \"\\n\" ? \"\" : next;\n        }\n        return pos + text.length > this.to ? text.slice(0, this.to - pos) : text;\n    }\n    /**\n    The end position of the previous line.\n    */\n    prevLineEnd() { return this.atEnd ? this.lineStart : this.lineStart - 1; }\n    /**\n    @internal\n    */\n    startContext(type, start, value = 0) {\n        this.block = CompositeBlock.create(type, value, this.lineStart + start, this.block.hash, this.lineStart + this.line.text.length);\n        this.stack.push(this.block);\n    }\n    /**\n    Start a composite block. Should only be called from [block\n    parser functions](#BlockParser.parse) that return null.\n    */\n    startComposite(type, start, value = 0) {\n        this.startContext(this.parser.getNodeType(type), start, value);\n    }\n    /**\n    @internal\n    */\n    addNode(block, from, to) {\n        if (typeof block == \"number\")\n            block = new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree(this.parser.nodeSet.types[block], none, none, (to !== null && to !== void 0 ? to : this.prevLineEnd()) - from);\n        this.block.addChild(block, from - this.block.from);\n    }\n    /**\n    Add a block element. Can be called by [block\n    parsers](#BlockParser.parse).\n    */\n    addElement(elt) {\n        this.block.addChild(elt.toTree(this.parser.nodeSet), elt.from - this.block.from);\n    }\n    /**\n    Add a block element from a [leaf parser](#LeafBlockParser). This\n    makes sure any extra composite block markup (such as blockquote\n    markers) inside the block are also added to the syntax tree.\n    */\n    addLeafElement(leaf, elt) {\n        this.addNode(this.buffer\n            .writeElements(injectMarks(elt.children, leaf.marks), -elt.from)\n            .finish(elt.type, elt.to - elt.from), elt.from);\n    }\n    /**\n    @internal\n    */\n    finishContext() {\n        let cx = this.stack.pop();\n        let top = this.stack[this.stack.length - 1];\n        top.addChild(cx.toTree(this.parser.nodeSet), cx.from - top.from);\n        this.block = top;\n    }\n    finish() {\n        while (this.stack.length > 1)\n            this.finishContext();\n        return this.addGaps(this.block.toTree(this.parser.nodeSet, this.lineStart));\n    }\n    addGaps(tree) {\n        return this.ranges.length > 1 ?\n            injectGaps(this.ranges, 0, tree.topNode, this.ranges[0].from, this.reusePlaceholders) : tree;\n    }\n    /**\n    @internal\n    */\n    finishLeaf(leaf) {\n        for (let parser of leaf.parsers)\n            if (parser.finish(this, leaf))\n                return;\n        let inline = injectMarks(this.parser.parseInline(leaf.content, leaf.start), leaf.marks);\n        this.addNode(this.buffer\n            .writeElements(inline, -leaf.start)\n            .finish(Type.Paragraph, leaf.content.length), leaf.start);\n    }\n    elt(type, from, to, children) {\n        if (typeof type == \"string\")\n            return elt(this.parser.getNodeType(type), from, to, children);\n        return new TreeElement(type, from);\n    }\n    /**\n    @internal\n    */\n    get buffer() { return new Buffer(this.parser.nodeSet); }\n}\nfunction injectGaps(ranges, rangeI, tree, offset, dummies) {\n    let rangeEnd = ranges[rangeI].to;\n    let children = [], positions = [], start = tree.from + offset;\n    function movePastNext(upto, inclusive) {\n        while (inclusive ? upto >= rangeEnd : upto > rangeEnd) {\n            let size = ranges[rangeI + 1].from - rangeEnd;\n            offset += size;\n            upto += size;\n            rangeI++;\n            rangeEnd = ranges[rangeI].to;\n        }\n    }\n    for (let ch = tree.firstChild; ch; ch = ch.nextSibling) {\n        movePastNext(ch.from + offset, true);\n        let from = ch.from + offset, node, reuse = dummies.get(ch.tree);\n        if (reuse) {\n            node = reuse;\n        }\n        else if (ch.to + offset > rangeEnd) {\n            node = injectGaps(ranges, rangeI, ch, offset, dummies);\n            movePastNext(ch.to + offset, false);\n        }\n        else {\n            node = ch.toTree();\n        }\n        children.push(node);\n        positions.push(from - start);\n    }\n    movePastNext(tree.to + offset, false);\n    return new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree(tree.type, children, positions, tree.to + offset - start, tree.tree ? tree.tree.propValues : undefined);\n}\n/**\nA Markdown parser configuration.\n*/\nclass MarkdownParser extends _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Parser {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parser's syntax [node\n    types](https://lezer.codemirror.net/docs/ref/#common.NodeSet).\n    */\n    nodeSet, \n    /**\n    @internal\n    */\n    blockParsers, \n    /**\n    @internal\n    */\n    leafBlockParsers, \n    /**\n    @internal\n    */\n    blockNames, \n    /**\n    @internal\n    */\n    endLeafBlock, \n    /**\n    @internal\n    */\n    skipContextMarkup, \n    /**\n    @internal\n    */\n    inlineParsers, \n    /**\n    @internal\n    */\n    inlineNames, \n    /**\n    @internal\n    */\n    wrappers) {\n        super();\n        this.nodeSet = nodeSet;\n        this.blockParsers = blockParsers;\n        this.leafBlockParsers = leafBlockParsers;\n        this.blockNames = blockNames;\n        this.endLeafBlock = endLeafBlock;\n        this.skipContextMarkup = skipContextMarkup;\n        this.inlineParsers = inlineParsers;\n        this.inlineNames = inlineNames;\n        this.wrappers = wrappers;\n        /**\n        @internal\n        */\n        this.nodeTypes = Object.create(null);\n        for (let t of nodeSet.types)\n            this.nodeTypes[t.name] = t.id;\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new BlockContext(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /**\n    Reconfigure the parser.\n    */\n    configure(spec) {\n        let config = resolveConfig(spec);\n        if (!config)\n            return this;\n        let { nodeSet, skipContextMarkup } = this;\n        let blockParsers = this.blockParsers.slice(), leafBlockParsers = this.leafBlockParsers.slice(), blockNames = this.blockNames.slice(), inlineParsers = this.inlineParsers.slice(), inlineNames = this.inlineNames.slice(), endLeafBlock = this.endLeafBlock.slice(), wrappers = this.wrappers;\n        if (nonEmpty(config.defineNodes)) {\n            skipContextMarkup = Object.assign({}, skipContextMarkup);\n            let nodeTypes = nodeSet.types.slice(), styles;\n            for (let s of config.defineNodes) {\n                let { name, block, composite, style } = typeof s == \"string\" ? { name: s } : s;\n                if (nodeTypes.some(t => t.name == name))\n                    continue;\n                if (composite)\n                    skipContextMarkup[nodeTypes.length] =\n                        (bl, cx, line) => composite(cx, line, bl.value);\n                let id = nodeTypes.length;\n                let group = composite ? [\"Block\", \"BlockContext\"] : !block ? undefined\n                    : id >= Type.ATXHeading1 && id <= Type.SetextHeading2 ? [\"Block\", \"LeafBlock\", \"Heading\"] : [\"Block\", \"LeafBlock\"];\n                nodeTypes.push(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeType.define({\n                    id,\n                    name,\n                    props: group && [[_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.group, group]]\n                }));\n                if (style) {\n                    if (!styles)\n                        styles = {};\n                    if (Array.isArray(style) || style instanceof _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.Tag)\n                        styles[name] = style;\n                    else\n                        Object.assign(styles, style);\n                }\n            }\n            nodeSet = new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeSet(nodeTypes);\n            if (styles)\n                nodeSet = nodeSet.extend((0,_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.styleTags)(styles));\n        }\n        if (nonEmpty(config.props))\n            nodeSet = nodeSet.extend(...config.props);\n        if (nonEmpty(config.remove)) {\n            for (let rm of config.remove) {\n                let block = this.blockNames.indexOf(rm), inline = this.inlineNames.indexOf(rm);\n                if (block > -1)\n                    blockParsers[block] = leafBlockParsers[block] = undefined;\n                if (inline > -1)\n                    inlineParsers[inline] = undefined;\n            }\n        }\n        if (nonEmpty(config.parseBlock)) {\n            for (let spec of config.parseBlock) {\n                let found = blockNames.indexOf(spec.name);\n                if (found > -1) {\n                    blockParsers[found] = spec.parse;\n                    leafBlockParsers[found] = spec.leaf;\n                }\n                else {\n                    let pos = spec.before ? findName(blockNames, spec.before)\n                        : spec.after ? findName(blockNames, spec.after) + 1 : blockNames.length - 1;\n                    blockParsers.splice(pos, 0, spec.parse);\n                    leafBlockParsers.splice(pos, 0, spec.leaf);\n                    blockNames.splice(pos, 0, spec.name);\n                }\n                if (spec.endLeaf)\n                    endLeafBlock.push(spec.endLeaf);\n            }\n        }\n        if (nonEmpty(config.parseInline)) {\n            for (let spec of config.parseInline) {\n                let found = inlineNames.indexOf(spec.name);\n                if (found > -1) {\n                    inlineParsers[found] = spec.parse;\n                }\n                else {\n                    let pos = spec.before ? findName(inlineNames, spec.before)\n                        : spec.after ? findName(inlineNames, spec.after) + 1 : inlineNames.length - 1;\n                    inlineParsers.splice(pos, 0, spec.parse);\n                    inlineNames.splice(pos, 0, spec.name);\n                }\n            }\n        }\n        if (config.wrap)\n            wrappers = wrappers.concat(config.wrap);\n        return new MarkdownParser(nodeSet, blockParsers, leafBlockParsers, blockNames, endLeafBlock, skipContextMarkup, inlineParsers, inlineNames, wrappers);\n    }\n    /**\n    @internal\n    */\n    getNodeType(name) {\n        let found = this.nodeTypes[name];\n        if (found == null)\n            throw new RangeError(`Unknown node type '${name}'`);\n        return found;\n    }\n    /**\n    Parse the given piece of inline text at the given offset,\n    returning an array of [`Element`](#Element) objects representing\n    the inline content.\n    */\n    parseInline(text, offset) {\n        let cx = new InlineContext(this, text, offset);\n        outer: for (let pos = offset; pos < cx.end;) {\n            let next = cx.char(pos);\n            for (let token of this.inlineParsers)\n                if (token) {\n                    let result = token(cx, next, pos);\n                    if (result >= 0) {\n                        pos = result;\n                        continue outer;\n                    }\n                }\n            pos++;\n        }\n        return cx.resolveMarkers(0);\n    }\n}\nfunction nonEmpty(a) {\n    return a != null && a.length > 0;\n}\nfunction resolveConfig(spec) {\n    if (!Array.isArray(spec))\n        return spec;\n    if (spec.length == 0)\n        return null;\n    let conf = resolveConfig(spec[0]);\n    if (spec.length == 1)\n        return conf;\n    let rest = resolveConfig(spec.slice(1));\n    if (!rest || !conf)\n        return conf || rest;\n    let conc = (a, b) => (a || none).concat(b || none);\n    let wrapA = conf.wrap, wrapB = rest.wrap;\n    return {\n        props: conc(conf.props, rest.props),\n        defineNodes: conc(conf.defineNodes, rest.defineNodes),\n        parseBlock: conc(conf.parseBlock, rest.parseBlock),\n        parseInline: conc(conf.parseInline, rest.parseInline),\n        remove: conc(conf.remove, rest.remove),\n        wrap: !wrapA ? wrapB : !wrapB ? wrapA :\n            (inner, input, fragments, ranges) => wrapA(wrapB(inner, input, fragments, ranges), input, fragments, ranges)\n    };\n}\nfunction findName(names, name) {\n    let found = names.indexOf(name);\n    if (found < 0)\n        throw new RangeError(`Position specified relative to unknown parser ${name}`);\n    return found;\n}\nlet nodeTypes = [_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeType.none];\nfor (let i = 1, name; name = Type[i]; i++) {\n    nodeTypes[i] = _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeType.define({\n        id: i,\n        name,\n        props: i >= Type.Escape ? [] : [[_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.group, i in DefaultSkipMarkup ? [\"Block\", \"BlockContext\"] : [\"Block\", \"LeafBlock\"]]],\n        top: name == \"Document\"\n    });\n}\nconst none = [];\nclass Buffer {\n    constructor(nodeSet) {\n        this.nodeSet = nodeSet;\n        this.content = [];\n        this.nodes = [];\n    }\n    write(type, from, to, children = 0) {\n        this.content.push(type, from, to, 4 + children * 4);\n        return this;\n    }\n    writeElements(elts, offset = 0) {\n        for (let e of elts)\n            e.writeTo(this, offset);\n        return this;\n    }\n    finish(type, length) {\n        return _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree.build({\n            buffer: this.content,\n            nodeSet: this.nodeSet,\n            reused: this.nodes,\n            topID: type,\n            length\n        });\n    }\n}\n/**\nElements are used to compose syntax nodes during parsing.\n*/\nclass Element {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The node's\n    [id](https://lezer.codemirror.net/docs/ref/#common.NodeType.id).\n    */\n    type, \n    /**\n    The start of the node, as an offset from the start of the document.\n    */\n    from, \n    /**\n    The end of the node.\n    */\n    to, \n    /**\n    The node's child nodes @internal\n    */\n    children = none) {\n        this.type = type;\n        this.from = from;\n        this.to = to;\n        this.children = children;\n    }\n    /**\n    @internal\n    */\n    writeTo(buf, offset) {\n        let startOff = buf.content.length;\n        buf.writeElements(this.children, offset);\n        buf.content.push(this.type, this.from + offset, this.to + offset, buf.content.length + 4 - startOff);\n    }\n    /**\n    @internal\n    */\n    toTree(nodeSet) {\n        return new Buffer(nodeSet).writeElements(this.children, -this.from).finish(this.type, this.to - this.from);\n    }\n}\nclass TreeElement {\n    constructor(tree, from) {\n        this.tree = tree;\n        this.from = from;\n    }\n    get to() { return this.from + this.tree.length; }\n    get type() { return this.tree.type.id; }\n    get children() { return none; }\n    writeTo(buf, offset) {\n        buf.nodes.push(this.tree);\n        buf.content.push(buf.nodes.length - 1, this.from + offset, this.to + offset, -1);\n    }\n    toTree() { return this.tree; }\n}\nfunction elt(type, from, to, children) {\n    return new Element(type, from, to, children);\n}\nconst EmphasisUnderscore = { resolve: \"Emphasis\", mark: \"EmphasisMark\" };\nconst EmphasisAsterisk = { resolve: \"Emphasis\", mark: \"EmphasisMark\" };\nconst LinkStart = {}, ImageStart = {};\nclass InlineDelimiter {\n    constructor(type, from, to, side) {\n        this.type = type;\n        this.from = from;\n        this.to = to;\n        this.side = side;\n    }\n}\nconst Escapable = \"!\\\"#$%&'()*+,-./:;<=>?@[\\\\]^_`{|}~\";\nlet Punctuation = /[!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~\\xA1\\u2010-\\u2027]/;\ntry {\n    Punctuation = new RegExp(\"[\\\\p{S}|\\\\p{P}]\", \"u\");\n}\ncatch (_) { }\nconst DefaultInline = {\n    Escape(cx, next, start) {\n        if (next != 92 /* '\\\\' */ || start == cx.end - 1)\n            return -1;\n        let escaped = cx.char(start + 1);\n        for (let i = 0; i < Escapable.length; i++)\n            if (Escapable.charCodeAt(i) == escaped)\n                return cx.append(elt(Type.Escape, start, start + 2));\n        return -1;\n    },\n    Entity(cx, next, start) {\n        if (next != 38 /* '&' */)\n            return -1;\n        let m = /^(?:#\\d+|#x[a-f\\d]+|\\w+);/i.exec(cx.slice(start + 1, start + 31));\n        return m ? cx.append(elt(Type.Entity, start, start + 1 + m[0].length)) : -1;\n    },\n    InlineCode(cx, next, start) {\n        if (next != 96 /* '`' */ || start && cx.char(start - 1) == 96)\n            return -1;\n        let pos = start + 1;\n        while (pos < cx.end && cx.char(pos) == 96)\n            pos++;\n        let size = pos - start, curSize = 0;\n        for (; pos < cx.end; pos++) {\n            if (cx.char(pos) == 96) {\n                curSize++;\n                if (curSize == size && cx.char(pos + 1) != 96)\n                    return cx.append(elt(Type.InlineCode, start, pos + 1, [\n                        elt(Type.CodeMark, start, start + size),\n                        elt(Type.CodeMark, pos + 1 - size, pos + 1)\n                    ]));\n            }\n            else {\n                curSize = 0;\n            }\n        }\n        return -1;\n    },\n    HTMLTag(cx, next, start) {\n        if (next != 60 /* '<' */ || start == cx.end - 1)\n            return -1;\n        let after = cx.slice(start + 1, cx.end);\n        let url = /^(?:[a-z][-\\w+.]+:[^\\s>]+|[a-z\\d.!#$%&'*+/=?^_`{|}~-]+@[a-z\\d](?:[a-z\\d-]{0,61}[a-z\\d])?(?:\\.[a-z\\d](?:[a-z\\d-]{0,61}[a-z\\d])?)*)>/i.exec(after);\n        if (url) {\n            return cx.append(elt(Type.Autolink, start, start + 1 + url[0].length, [\n                elt(Type.LinkMark, start, start + 1),\n                // url[0] includes the closing bracket, so exclude it from this slice\n                elt(Type.URL, start + 1, start + url[0].length),\n                elt(Type.LinkMark, start + url[0].length, start + 1 + url[0].length)\n            ]));\n        }\n        let comment = /^!--[^>](?:-[^-]|[^-])*?-->/i.exec(after);\n        if (comment)\n            return cx.append(elt(Type.Comment, start, start + 1 + comment[0].length));\n        let procInst = /^\\?[^]*?\\?>/.exec(after);\n        if (procInst)\n            return cx.append(elt(Type.ProcessingInstruction, start, start + 1 + procInst[0].length));\n        let m = /^(?:![A-Z][^]*?>|!\\[CDATA\\[[^]*?\\]\\]>|\\/\\s*[a-zA-Z][\\w-]*\\s*>|\\s*[a-zA-Z][\\w-]*(\\s+[a-zA-Z:_][\\w-.:]*(?:\\s*=\\s*(?:[^\\s\"'=<>`]+|'[^']*'|\"[^\"]*\"))?)*\\s*(\\/\\s*)?>)/.exec(after);\n        if (!m)\n            return -1;\n        return cx.append(elt(Type.HTMLTag, start, start + 1 + m[0].length));\n    },\n    Emphasis(cx, next, start) {\n        if (next != 95 && next != 42)\n            return -1;\n        let pos = start + 1;\n        while (cx.char(pos) == next)\n            pos++;\n        let before = cx.slice(start - 1, start), after = cx.slice(pos, pos + 1);\n        let pBefore = Punctuation.test(before), pAfter = Punctuation.test(after);\n        let sBefore = /\\s|^$/.test(before), sAfter = /\\s|^$/.test(after);\n        let leftFlanking = !sAfter && (!pAfter || sBefore || pBefore);\n        let rightFlanking = !sBefore && (!pBefore || sAfter || pAfter);\n        let canOpen = leftFlanking && (next == 42 || !rightFlanking || pBefore);\n        let canClose = rightFlanking && (next == 42 || !leftFlanking || pAfter);\n        return cx.append(new InlineDelimiter(next == 95 ? EmphasisUnderscore : EmphasisAsterisk, start, pos, (canOpen ? 1 /* Mark.Open */ : 0 /* Mark.None */) | (canClose ? 2 /* Mark.Close */ : 0 /* Mark.None */)));\n    },\n    HardBreak(cx, next, start) {\n        if (next == 92 /* '\\\\' */ && cx.char(start + 1) == 10 /* '\\n' */)\n            return cx.append(elt(Type.HardBreak, start, start + 2));\n        if (next == 32) {\n            let pos = start + 1;\n            while (cx.char(pos) == 32)\n                pos++;\n            if (cx.char(pos) == 10 && pos >= start + 2)\n                return cx.append(elt(Type.HardBreak, start, pos + 1));\n        }\n        return -1;\n    },\n    Link(cx, next, start) {\n        return next == 91 /* '[' */ ? cx.append(new InlineDelimiter(LinkStart, start, start + 1, 1 /* Mark.Open */)) : -1;\n    },\n    Image(cx, next, start) {\n        return next == 33 /* '!' */ && cx.char(start + 1) == 91 /* '[' */\n            ? cx.append(new InlineDelimiter(ImageStart, start, start + 2, 1 /* Mark.Open */)) : -1;\n    },\n    LinkEnd(cx, next, start) {\n        if (next != 93 /* ']' */)\n            return -1;\n        // Scanning back to the next link/image start marker\n        for (let i = cx.parts.length - 1; i >= 0; i--) {\n            let part = cx.parts[i];\n            if (part instanceof InlineDelimiter && (part.type == LinkStart || part.type == ImageStart)) {\n                // If this one has been set invalid (because it would produce\n                // a nested link) or there's no valid link here ignore both.\n                if (!part.side || cx.skipSpace(part.to) == start && !/[(\\[]/.test(cx.slice(start + 1, start + 2))) {\n                    cx.parts[i] = null;\n                    return -1;\n                }\n                // Finish the content and replace the entire range in\n                // this.parts with the link/image node.\n                let content = cx.takeContent(i);\n                let link = cx.parts[i] = finishLink(cx, content, part.type == LinkStart ? Type.Link : Type.Image, part.from, start + 1);\n                // Set any open-link markers before this link to invalid.\n                if (part.type == LinkStart)\n                    for (let j = 0; j < i; j++) {\n                        let p = cx.parts[j];\n                        if (p instanceof InlineDelimiter && p.type == LinkStart)\n                            p.side = 0 /* Mark.None */;\n                    }\n                return link.to;\n            }\n        }\n        return -1;\n    }\n};\nfunction finishLink(cx, content, type, start, startPos) {\n    let { text } = cx, next = cx.char(startPos), endPos = startPos;\n    content.unshift(elt(Type.LinkMark, start, start + (type == Type.Image ? 2 : 1)));\n    content.push(elt(Type.LinkMark, startPos - 1, startPos));\n    if (next == 40 /* '(' */) {\n        let pos = cx.skipSpace(startPos + 1);\n        let dest = parseURL(text, pos - cx.offset, cx.offset), title;\n        if (dest) {\n            pos = cx.skipSpace(dest.to);\n            // The destination and title must be separated by whitespace\n            if (pos != dest.to) {\n                title = parseLinkTitle(text, pos - cx.offset, cx.offset);\n                if (title)\n                    pos = cx.skipSpace(title.to);\n            }\n        }\n        if (cx.char(pos) == 41 /* ')' */) {\n            content.push(elt(Type.LinkMark, startPos, startPos + 1));\n            endPos = pos + 1;\n            if (dest)\n                content.push(dest);\n            if (title)\n                content.push(title);\n            content.push(elt(Type.LinkMark, pos, endPos));\n        }\n    }\n    else if (next == 91 /* '[' */) {\n        let label = parseLinkLabel(text, startPos - cx.offset, cx.offset, false);\n        if (label) {\n            content.push(label);\n            endPos = label.to;\n        }\n    }\n    return elt(type, start, endPos, content);\n}\n// These return `null` when falling off the end of the input, `false`\n// when parsing fails otherwise (for use in the incremental link\n// reference parser).\nfunction parseURL(text, start, offset) {\n    let next = text.charCodeAt(start);\n    if (next == 60 /* '<' */) {\n        for (let pos = start + 1; pos < text.length; pos++) {\n            let ch = text.charCodeAt(pos);\n            if (ch == 62 /* '>' */)\n                return elt(Type.URL, start + offset, pos + 1 + offset);\n            if (ch == 60 || ch == 10 /* '<\\n' */)\n                return false;\n        }\n        return null;\n    }\n    else {\n        let depth = 0, pos = start;\n        for (let escaped = false; pos < text.length; pos++) {\n            let ch = text.charCodeAt(pos);\n            if (space(ch)) {\n                break;\n            }\n            else if (escaped) {\n                escaped = false;\n            }\n            else if (ch == 40 /* '(' */) {\n                depth++;\n            }\n            else if (ch == 41 /* ')' */) {\n                if (!depth)\n                    break;\n                depth--;\n            }\n            else if (ch == 92 /* '\\\\' */) {\n                escaped = true;\n            }\n        }\n        return pos > start ? elt(Type.URL, start + offset, pos + offset) : pos == text.length ? null : false;\n    }\n}\nfunction parseLinkTitle(text, start, offset) {\n    let next = text.charCodeAt(start);\n    if (next != 39 && next != 34 && next != 40 /* '\"\\'(' */)\n        return false;\n    let end = next == 40 ? 41 : next;\n    for (let pos = start + 1, escaped = false; pos < text.length; pos++) {\n        let ch = text.charCodeAt(pos);\n        if (escaped)\n            escaped = false;\n        else if (ch == end)\n            return elt(Type.LinkTitle, start + offset, pos + 1 + offset);\n        else if (ch == 92 /* '\\\\' */)\n            escaped = true;\n    }\n    return null;\n}\nfunction parseLinkLabel(text, start, offset, requireNonWS) {\n    for (let escaped = false, pos = start + 1, end = Math.min(text.length, pos + 999); pos < end; pos++) {\n        let ch = text.charCodeAt(pos);\n        if (escaped)\n            escaped = false;\n        else if (ch == 93 /* ']' */)\n            return requireNonWS ? false : elt(Type.LinkLabel, start + offset, pos + 1 + offset);\n        else {\n            if (requireNonWS && !space(ch))\n                requireNonWS = false;\n            if (ch == 91 /* '[' */)\n                return false;\n            else if (ch == 92 /* '\\\\' */)\n                escaped = true;\n        }\n    }\n    return null;\n}\n/**\nInline parsing functions get access to this context, and use it to\nread the content and emit syntax nodes.\n*/\nclass InlineContext {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parser that is being used.\n    */\n    parser, \n    /**\n    The text of this inline section.\n    */\n    text, \n    /**\n    The starting offset of the section in the document.\n    */\n    offset) {\n        this.parser = parser;\n        this.text = text;\n        this.offset = offset;\n        /**\n        @internal\n        */\n        this.parts = [];\n    }\n    /**\n    Get the character code at the given (document-relative)\n    position.\n    */\n    char(pos) { return pos >= this.end ? -1 : this.text.charCodeAt(pos - this.offset); }\n    /**\n    The position of the end of this inline section.\n    */\n    get end() { return this.offset + this.text.length; }\n    /**\n    Get a substring of this inline section. Again uses\n    document-relative positions.\n    */\n    slice(from, to) { return this.text.slice(from - this.offset, to - this.offset); }\n    /**\n    @internal\n    */\n    append(elt) {\n        this.parts.push(elt);\n        return elt.to;\n    }\n    /**\n    Add a [delimiter](#DelimiterType) at this given position. `open`\n    and `close` indicate whether this delimiter is opening, closing,\n    or both. Returns the end of the delimiter, for convenient\n    returning from [parse functions](#InlineParser.parse).\n    */\n    addDelimiter(type, from, to, open, close) {\n        return this.append(new InlineDelimiter(type, from, to, (open ? 1 /* Mark.Open */ : 0 /* Mark.None */) | (close ? 2 /* Mark.Close */ : 0 /* Mark.None */)));\n    }\n    /**\n    Returns true when there is an unmatched link or image opening\n    token before the current position.\n    */\n    get hasOpenLink() {\n        for (let i = this.parts.length - 1; i >= 0; i--) {\n            let part = this.parts[i];\n            if (part instanceof InlineDelimiter && (part.type == LinkStart || part.type == ImageStart))\n                return true;\n        }\n        return false;\n    }\n    /**\n    Add an inline element. Returns the end of the element.\n    */\n    addElement(elt) {\n        return this.append(elt);\n    }\n    /**\n    Resolve markers between this.parts.length and from, wrapping matched markers in the\n    appropriate node and updating the content of this.parts. @internal\n    */\n    resolveMarkers(from) {\n        // Scan forward, looking for closing tokens\n        for (let i = from; i < this.parts.length; i++) {\n            let close = this.parts[i];\n            if (!(close instanceof InlineDelimiter && close.type.resolve && (close.side & 2 /* Mark.Close */)))\n                continue;\n            let emp = close.type == EmphasisUnderscore || close.type == EmphasisAsterisk;\n            let closeSize = close.to - close.from;\n            let open, j = i - 1;\n            // Continue scanning for a matching opening token\n            for (; j >= from; j--) {\n                let part = this.parts[j];\n                if (part instanceof InlineDelimiter && (part.side & 1 /* Mark.Open */) && part.type == close.type &&\n                    // Ignore emphasis delimiters where the character count doesn't match\n                    !(emp && ((close.side & 1 /* Mark.Open */) || (part.side & 2 /* Mark.Close */)) &&\n                        (part.to - part.from + closeSize) % 3 == 0 && ((part.to - part.from) % 3 || closeSize % 3))) {\n                    open = part;\n                    break;\n                }\n            }\n            if (!open)\n                continue;\n            let type = close.type.resolve, content = [];\n            let start = open.from, end = close.to;\n            // Emphasis marker effect depends on the character count. Size consumed is minimum of the two\n            // markers.\n            if (emp) {\n                let size = Math.min(2, open.to - open.from, closeSize);\n                start = open.to - size;\n                end = close.from + size;\n                type = size == 1 ? \"Emphasis\" : \"StrongEmphasis\";\n            }\n            // Move the covered region into content, optionally adding marker nodes\n            if (open.type.mark)\n                content.push(this.elt(open.type.mark, start, open.to));\n            for (let k = j + 1; k < i; k++) {\n                if (this.parts[k] instanceof Element)\n                    content.push(this.parts[k]);\n                this.parts[k] = null;\n            }\n            if (close.type.mark)\n                content.push(this.elt(close.type.mark, close.from, end));\n            let element = this.elt(type, start, end, content);\n            // If there are leftover emphasis marker characters, shrink the close/open markers. Otherwise, clear them.\n            this.parts[j] = emp && open.from != start ? new InlineDelimiter(open.type, open.from, start, open.side) : null;\n            let keep = this.parts[i] = emp && close.to != end ? new InlineDelimiter(close.type, end, close.to, close.side) : null;\n            // Insert the new element in this.parts\n            if (keep)\n                this.parts.splice(i, 0, element);\n            else\n                this.parts[i] = element;\n        }\n        // Collect the elements remaining in this.parts into an array.\n        let result = [];\n        for (let i = from; i < this.parts.length; i++) {\n            let part = this.parts[i];\n            if (part instanceof Element)\n                result.push(part);\n        }\n        return result;\n    }\n    /**\n    Find an opening delimiter of the given type. Returns `null` if\n    no delimiter is found, or an index that can be passed to\n    [`takeContent`](#InlineContext.takeContent) otherwise.\n    */\n    findOpeningDelimiter(type) {\n        for (let i = this.parts.length - 1; i >= 0; i--) {\n            let part = this.parts[i];\n            if (part instanceof InlineDelimiter && part.type == type)\n                return i;\n        }\n        return null;\n    }\n    /**\n    Remove all inline elements and delimiters starting from the\n    given index (which you should get from\n    [`findOpeningDelimiter`](#InlineContext.findOpeningDelimiter),\n    resolve delimiters inside of them, and return them as an array\n    of elements.\n    */\n    takeContent(startIndex) {\n        let content = this.resolveMarkers(startIndex);\n        this.parts.length = startIndex;\n        return content;\n    }\n    /**\n    Skip space after the given (document) position, returning either\n    the position of the next non-space character or the end of the\n    section.\n    */\n    skipSpace(from) { return skipSpace(this.text, from - this.offset) + this.offset; }\n    elt(type, from, to, children) {\n        if (typeof type == \"string\")\n            return elt(this.parser.getNodeType(type), from, to, children);\n        return new TreeElement(type, from);\n    }\n}\nfunction injectMarks(elements, marks) {\n    if (!marks.length)\n        return elements;\n    if (!elements.length)\n        return marks;\n    let elts = elements.slice(), eI = 0;\n    for (let mark of marks) {\n        while (eI < elts.length && elts[eI].to < mark.to)\n            eI++;\n        if (eI < elts.length && elts[eI].from < mark.from) {\n            let e = elts[eI];\n            if (e instanceof Element)\n                elts[eI] = new Element(e.type, e.from, e.to, injectMarks(e.children, [mark]));\n        }\n        else {\n            elts.splice(eI++, 0, mark);\n        }\n    }\n    return elts;\n}\n// These are blocks that can span blank lines, and should thus only be\n// reused if their next sibling is also being reused.\nconst NotLast = [Type.CodeBlock, Type.ListItem, Type.OrderedList, Type.BulletList];\nclass FragmentCursor {\n    constructor(fragments, input) {\n        this.fragments = fragments;\n        this.input = input;\n        // Index into fragment array\n        this.i = 0;\n        // Active fragment\n        this.fragment = null;\n        this.fragmentEnd = -1;\n        // Cursor into the current fragment, if any. When `moveTo` returns\n        // true, this points at the first block after `pos`.\n        this.cursor = null;\n        if (fragments.length)\n            this.fragment = fragments[this.i++];\n    }\n    nextFragment() {\n        this.fragment = this.i < this.fragments.length ? this.fragments[this.i++] : null;\n        this.cursor = null;\n        this.fragmentEnd = -1;\n    }\n    moveTo(pos, lineStart) {\n        while (this.fragment && this.fragment.to <= pos)\n            this.nextFragment();\n        if (!this.fragment || this.fragment.from > (pos ? pos - 1 : 0))\n            return false;\n        if (this.fragmentEnd < 0) {\n            let end = this.fragment.to;\n            while (end > 0 && this.input.read(end - 1, end) != \"\\n\")\n                end--;\n            this.fragmentEnd = end ? end - 1 : 0;\n        }\n        let c = this.cursor;\n        if (!c) {\n            c = this.cursor = this.fragment.tree.cursor();\n            c.firstChild();\n        }\n        let rPos = pos + this.fragment.offset;\n        while (c.to <= rPos)\n            if (!c.parent())\n                return false;\n        for (;;) {\n            if (c.from >= rPos)\n                return this.fragment.from <= lineStart;\n            if (!c.childAfter(rPos))\n                return false;\n        }\n    }\n    matches(hash) {\n        let tree = this.cursor.tree;\n        return tree && tree.prop(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.contextHash) == hash;\n    }\n    takeNodes(cx) {\n        let cur = this.cursor, off = this.fragment.offset, fragEnd = this.fragmentEnd - (this.fragment.openEnd ? 1 : 0);\n        let start = cx.absoluteLineStart, end = start, blockI = cx.block.children.length;\n        let prevEnd = end, prevI = blockI;\n        for (;;) {\n            if (cur.to - off > fragEnd) {\n                if (cur.type.isAnonymous && cur.firstChild())\n                    continue;\n                break;\n            }\n            let pos = toRelative(cur.from - off, cx.ranges);\n            if (cur.to - off <= cx.ranges[cx.rangeI].to) { // Fits in current range\n                cx.addNode(cur.tree, pos);\n            }\n            else {\n                let dummy = new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree(cx.parser.nodeSet.types[Type.Paragraph], [], [], 0, cx.block.hashProp);\n                cx.reusePlaceholders.set(dummy, cur.tree);\n                cx.addNode(dummy, pos);\n            }\n            // Taken content must always end in a block, because incremental\n            // parsing happens on block boundaries. Never stop directly\n            // after an indented code block, since those can continue after\n            // any number of blank lines.\n            if (cur.type.is(\"Block\")) {\n                if (NotLast.indexOf(cur.type.id) < 0) {\n                    end = cur.to - off;\n                    blockI = cx.block.children.length;\n                }\n                else {\n                    end = prevEnd;\n                    blockI = prevI;\n                    prevEnd = cur.to - off;\n                    prevI = cx.block.children.length;\n                }\n            }\n            if (!cur.nextSibling())\n                break;\n        }\n        while (cx.block.children.length > blockI) {\n            cx.block.children.pop();\n            cx.block.positions.pop();\n        }\n        return end - start;\n    }\n}\n// Convert an input-stream-relative position to a\n// Markdown-doc-relative position by subtracting the size of all input\n// gaps before `abs`.\nfunction toRelative(abs, ranges) {\n    let pos = abs;\n    for (let i = 1; i < ranges.length; i++) {\n        let gapFrom = ranges[i - 1].to, gapTo = ranges[i].from;\n        if (gapFrom < abs)\n            pos -= gapTo - gapFrom;\n    }\n    return pos;\n}\nconst markdownHighlighting = (0,_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.styleTags)({\n    \"Blockquote/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.quote,\n    HorizontalRule: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.contentSeparator,\n    \"ATXHeading1/... SetextHeading1/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.heading1,\n    \"ATXHeading2/... SetextHeading2/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.heading2,\n    \"ATXHeading3/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.heading3,\n    \"ATXHeading4/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.heading4,\n    \"ATXHeading5/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.heading5,\n    \"ATXHeading6/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.heading6,\n    \"Comment CommentBlock\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.comment,\n    Escape: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.escape,\n    Entity: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.character,\n    \"Emphasis/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.emphasis,\n    \"StrongEmphasis/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.strong,\n    \"Link/... Image/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.link,\n    \"OrderedList/... BulletList/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.list,\n    \"BlockQuote/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.quote,\n    \"InlineCode CodeText\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.monospace,\n    \"URL Autolink\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.url,\n    \"HeaderMark HardBreak QuoteMark ListMark LinkMark EmphasisMark CodeMark\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.processingInstruction,\n    \"CodeInfo LinkLabel\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.labelName,\n    LinkTitle: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string,\n    Paragraph: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.content\n});\n/**\nThe default CommonMark parser.\n*/\nconst parser = new MarkdownParser(new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeSet(nodeTypes).extend(markdownHighlighting), Object.keys(DefaultBlockParsers).map(n => DefaultBlockParsers[n]), Object.keys(DefaultBlockParsers).map(n => DefaultLeafBlocks[n]), Object.keys(DefaultBlockParsers), DefaultEndLeaf, DefaultSkipMarkup, Object.keys(DefaultInline).map(n => DefaultInline[n]), Object.keys(DefaultInline), []);\n\nfunction leftOverSpace(node, from, to) {\n    let ranges = [];\n    for (let n = node.firstChild, pos = from;; n = n.nextSibling) {\n        let nextPos = n ? n.from : to;\n        if (nextPos > pos)\n            ranges.push({ from: pos, to: nextPos });\n        if (!n)\n            break;\n        pos = n.to;\n    }\n    return ranges;\n}\n/**\nCreate a Markdown extension to enable nested parsing on code\nblocks and/or embedded HTML.\n*/\nfunction parseCode(config) {\n    let { codeParser, htmlParser } = config;\n    let wrap = (0,_lezer_common__WEBPACK_IMPORTED_MODULE_0__.parseMixed)((node, input) => {\n        let id = node.type.id;\n        if (codeParser && (id == Type.CodeBlock || id == Type.FencedCode)) {\n            let info = \"\";\n            if (id == Type.FencedCode) {\n                let infoNode = node.node.getChild(Type.CodeInfo);\n                if (infoNode)\n                    info = input.read(infoNode.from, infoNode.to);\n            }\n            let parser = codeParser(info);\n            if (parser)\n                return { parser, overlay: node => node.type.id == Type.CodeText };\n        }\n        else if (htmlParser && (id == Type.HTMLBlock || id == Type.HTMLTag || id == Type.CommentBlock)) {\n            return { parser: htmlParser, overlay: leftOverSpace(node.node, node.from, node.to) };\n        }\n        return null;\n    });\n    return { wrap };\n}\n\nconst StrikethroughDelim = { resolve: \"Strikethrough\", mark: \"StrikethroughMark\" };\n/**\nAn extension that implements\n[GFM-style](https://github.github.com/gfm/#strikethrough-extension-)\nStrikethrough syntax using `~~` delimiters.\n*/\nconst Strikethrough = {\n    defineNodes: [{\n            name: \"Strikethrough\",\n            style: { \"Strikethrough/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.strikethrough }\n        }, {\n            name: \"StrikethroughMark\",\n            style: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.processingInstruction\n        }],\n    parseInline: [{\n            name: \"Strikethrough\",\n            parse(cx, next, pos) {\n                if (next != 126 /* '~' */ || cx.char(pos + 1) != 126 || cx.char(pos + 2) == 126)\n                    return -1;\n                let before = cx.slice(pos - 1, pos), after = cx.slice(pos + 2, pos + 3);\n                let sBefore = /\\s|^$/.test(before), sAfter = /\\s|^$/.test(after);\n                let pBefore = Punctuation.test(before), pAfter = Punctuation.test(after);\n                return cx.addDelimiter(StrikethroughDelim, pos, pos + 2, !sAfter && (!pAfter || sBefore || pBefore), !sBefore && (!pBefore || sAfter || pAfter));\n            },\n            after: \"Emphasis\"\n        }]\n};\n// Parse a line as a table row and return the row count. When `elts`\n// is given, push syntax elements for the content onto it.\nfunction parseRow(cx, line, startI = 0, elts, offset = 0) {\n    let count = 0, first = true, cellStart = -1, cellEnd = -1, esc = false;\n    let parseCell = () => {\n        elts.push(cx.elt(\"TableCell\", offset + cellStart, offset + cellEnd, cx.parser.parseInline(line.slice(cellStart, cellEnd), offset + cellStart)));\n    };\n    for (let i = startI; i < line.length; i++) {\n        let next = line.charCodeAt(i);\n        if (next == 124 /* '|' */ && !esc) {\n            if (!first || cellStart > -1)\n                count++;\n            first = false;\n            if (elts) {\n                if (cellStart > -1)\n                    parseCell();\n                elts.push(cx.elt(\"TableDelimiter\", i + offset, i + offset + 1));\n            }\n            cellStart = cellEnd = -1;\n        }\n        else if (esc || next != 32 && next != 9) {\n            if (cellStart < 0)\n                cellStart = i;\n            cellEnd = i + 1;\n        }\n        esc = !esc && next == 92;\n    }\n    if (cellStart > -1) {\n        count++;\n        if (elts)\n            parseCell();\n    }\n    return count;\n}\nfunction hasPipe(str, start) {\n    for (let i = start; i < str.length; i++) {\n        let next = str.charCodeAt(i);\n        if (next == 124 /* '|' */)\n            return true;\n        if (next == 92 /* '\\\\' */)\n            i++;\n    }\n    return false;\n}\nconst delimiterLine = /^\\|?(\\s*:?-+:?\\s*\\|)+(\\s*:?-+:?\\s*)?$/;\nclass TableParser {\n    constructor() {\n        // Null means we haven't seen the second line yet, false means this\n        // isn't a table, and an array means this is a table and we've\n        // parsed the given rows so far.\n        this.rows = null;\n    }\n    nextLine(cx, line, leaf) {\n        if (this.rows == null) { // Second line\n            this.rows = false;\n            let lineText;\n            if ((line.next == 45 || line.next == 58 || line.next == 124 /* '-:|' */) &&\n                delimiterLine.test(lineText = line.text.slice(line.pos))) {\n                let firstRow = [], firstCount = parseRow(cx, leaf.content, 0, firstRow, leaf.start);\n                if (firstCount == parseRow(cx, lineText, line.pos))\n                    this.rows = [cx.elt(\"TableHeader\", leaf.start, leaf.start + leaf.content.length, firstRow),\n                        cx.elt(\"TableDelimiter\", cx.lineStart + line.pos, cx.lineStart + line.text.length)];\n            }\n        }\n        else if (this.rows) { // Line after the second\n            let content = [];\n            parseRow(cx, line.text, line.pos, content, cx.lineStart);\n            this.rows.push(cx.elt(\"TableRow\", cx.lineStart + line.pos, cx.lineStart + line.text.length, content));\n        }\n        return false;\n    }\n    finish(cx, leaf) {\n        if (!this.rows)\n            return false;\n        cx.addLeafElement(leaf, cx.elt(\"Table\", leaf.start, leaf.start + leaf.content.length, this.rows));\n        return true;\n    }\n}\n/**\nThis extension provides\n[GFM-style](https://github.github.com/gfm/#tables-extension-)\ntables, using syntax like this:\n\n```\n| head 1 | head 2 |\n| ---    | ---    |\n| cell 1 | cell 2 |\n```\n*/\nconst Table = {\n    defineNodes: [\n        { name: \"Table\", block: true },\n        { name: \"TableHeader\", style: { \"TableHeader/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.heading } },\n        \"TableRow\",\n        { name: \"TableCell\", style: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.content },\n        { name: \"TableDelimiter\", style: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.processingInstruction },\n    ],\n    parseBlock: [{\n            name: \"Table\",\n            leaf(_, leaf) { return hasPipe(leaf.content, 0) ? new TableParser : null; },\n            endLeaf(cx, line, leaf) {\n                if (leaf.parsers.some(p => p instanceof TableParser) || !hasPipe(line.text, line.basePos))\n                    return false;\n                let next = cx.peekLine();\n                return delimiterLine.test(next) && parseRow(cx, line.text, line.basePos) == parseRow(cx, next, line.basePos);\n            },\n            before: \"SetextHeading\"\n        }]\n};\nclass TaskParser {\n    nextLine() { return false; }\n    finish(cx, leaf) {\n        cx.addLeafElement(leaf, cx.elt(\"Task\", leaf.start, leaf.start + leaf.content.length, [\n            cx.elt(\"TaskMarker\", leaf.start, leaf.start + 3),\n            ...cx.parser.parseInline(leaf.content.slice(3), leaf.start + 3)\n        ]));\n        return true;\n    }\n}\n/**\nExtension providing\n[GFM-style](https://github.github.com/gfm/#task-list-items-extension-)\ntask list items, where list items can be prefixed with `[ ]` or\n`[x]` to add a checkbox.\n*/\nconst TaskList = {\n    defineNodes: [\n        { name: \"Task\", block: true, style: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.list },\n        { name: \"TaskMarker\", style: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.atom }\n    ],\n    parseBlock: [{\n            name: \"TaskList\",\n            leaf(cx, leaf) {\n                return /^\\[[ xX]\\][ \\t]/.test(leaf.content) && cx.parentType().name == \"ListItem\" ? new TaskParser : null;\n            },\n            after: \"SetextHeading\"\n        }]\n};\nconst autolinkRE = /(www\\.)|(https?:\\/\\/)|([\\w.+-]{1,100}@)|(mailto:|xmpp:)/gy;\nconst urlRE = /[\\w-]+(\\.[\\w-]+)+(\\/[^\\s<]*)?/gy;\nconst lastTwoDomainWords = /[\\w-]+\\.[\\w-]+($|\\/)/;\nconst emailRE = /[\\w.+-]+@[\\w-]+(\\.[\\w.-]+)+/gy;\nconst xmppResourceRE = /\\/[a-zA-Z\\d@.]+/gy;\nfunction count(str, from, to, ch) {\n    let result = 0;\n    for (let i = from; i < to; i++)\n        if (str[i] == ch)\n            result++;\n    return result;\n}\nfunction autolinkURLEnd(text, from) {\n    urlRE.lastIndex = from;\n    let m = urlRE.exec(text);\n    if (!m || lastTwoDomainWords.exec(m[0])[0].indexOf(\"_\") > -1)\n        return -1;\n    let end = from + m[0].length;\n    for (;;) {\n        let last = text[end - 1], m;\n        if (/[?!.,:*_~]/.test(last) ||\n            last == \")\" && count(text, from, end, \")\") > count(text, from, end, \"(\"))\n            end--;\n        else if (last == \";\" && (m = /&(?:#\\d+|#x[a-f\\d]+|\\w+);$/.exec(text.slice(from, end))))\n            end = from + m.index;\n        else\n            break;\n    }\n    return end;\n}\nfunction autolinkEmailEnd(text, from) {\n    emailRE.lastIndex = from;\n    let m = emailRE.exec(text);\n    if (!m)\n        return -1;\n    let last = m[0][m[0].length - 1];\n    return last == \"_\" || last == \"-\" ? -1 : from + m[0].length - (last == \".\" ? 1 : 0);\n}\n/**\nExtension that implements autolinking for\n`www.`/`http://`/`https://`/`mailto:`/`xmpp:` URLs and email\naddresses.\n*/\nconst Autolink = {\n    parseInline: [{\n            name: \"Autolink\",\n            parse(cx, next, absPos) {\n                let pos = absPos - cx.offset;\n                if (pos && /\\w/.test(cx.text[pos - 1]))\n                    return -1;\n                autolinkRE.lastIndex = pos;\n                let m = autolinkRE.exec(cx.text), end = -1;\n                if (!m)\n                    return -1;\n                if (m[1] || m[2]) { // www., http://\n                    end = autolinkURLEnd(cx.text, pos + m[0].length);\n                    if (end > -1 && cx.hasOpenLink) {\n                        let noBracket = /([^\\[\\]]|\\[[^\\]]*\\])*/.exec(cx.text.slice(pos, end));\n                        end = pos + noBracket[0].length;\n                    }\n                }\n                else if (m[3]) { // email address\n                    end = autolinkEmailEnd(cx.text, pos);\n                }\n                else { // mailto:/xmpp:\n                    end = autolinkEmailEnd(cx.text, pos + m[0].length);\n                    if (end > -1 && m[0] == \"xmpp:\") {\n                        xmppResourceRE.lastIndex = end;\n                        m = xmppResourceRE.exec(cx.text);\n                        if (m)\n                            end = m.index + m[0].length;\n                    }\n                }\n                if (end < 0)\n                    return -1;\n                cx.addElement(cx.elt(\"URL\", absPos, end + cx.offset));\n                return end + cx.offset;\n            }\n        }]\n};\n/**\nExtension bundle containing [`Table`](#Table),\n[`TaskList`](#TaskList), [`Strikethrough`](#Strikethrough), and\n[`Autolink`](#Autolink).\n*/\nconst GFM = [Table, TaskList, Strikethrough, Autolink];\nfunction parseSubSuper(ch, node, mark) {\n    return (cx, next, pos) => {\n        if (next != ch || cx.char(pos + 1) == ch)\n            return -1;\n        let elts = [cx.elt(mark, pos, pos + 1)];\n        for (let i = pos + 1; i < cx.end; i++) {\n            let next = cx.char(i);\n            if (next == ch)\n                return cx.addElement(cx.elt(node, pos, i + 1, elts.concat(cx.elt(mark, i, i + 1))));\n            if (next == 92 /* '\\\\' */)\n                elts.push(cx.elt(\"Escape\", i, i++ + 2));\n            if (space(next))\n                break;\n        }\n        return -1;\n    };\n}\n/**\nExtension providing\n[Pandoc-style](https://pandoc.org/MANUAL.html#superscripts-and-subscripts)\nsuperscript using `^` markers.\n*/\nconst Superscript = {\n    defineNodes: [\n        { name: \"Superscript\", style: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.content) },\n        { name: \"SuperscriptMark\", style: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.processingInstruction }\n    ],\n    parseInline: [{\n            name: \"Superscript\",\n            parse: parseSubSuper(94 /* '^' */, \"Superscript\", \"SuperscriptMark\")\n        }]\n};\n/**\nExtension providing\n[Pandoc-style](https://pandoc.org/MANUAL.html#superscripts-and-subscripts)\nsubscript using `~` markers.\n*/\nconst Subscript = {\n    defineNodes: [\n        { name: \"Subscript\", style: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.content) },\n        { name: \"SubscriptMark\", style: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.processingInstruction }\n    ],\n    parseInline: [{\n            name: \"Subscript\",\n            parse: parseSubSuper(126 /* '~' */, \"Subscript\", \"SubscriptMark\")\n        }]\n};\n/**\nExtension that parses two colons with only letters, underscores,\nand numbers between them as `Emoji` nodes.\n*/\nconst Emoji = {\n    defineNodes: [{ name: \"Emoji\", style: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.character }],\n    parseInline: [{\n            name: \"Emoji\",\n            parse(cx, next, pos) {\n                let match;\n                if (next != 58 /* ':' */ || !(match = /^[a-zA-Z_0-9]+:/.exec(cx.slice(pos + 1, cx.end))))\n                    return -1;\n                return cx.addElement(cx.elt(\"Emoji\", pos, pos + 1 + match[0].length));\n            }\n        }]\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@lezer+markdown@1.4.3/node_modules/@lezer/markdown/dist/index.js\n");

/***/ })

};
;