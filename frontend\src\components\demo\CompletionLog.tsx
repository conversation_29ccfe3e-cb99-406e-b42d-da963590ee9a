'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle2, AlertCircle, Clock, GitPullRequest, GitCommit, GitMerge } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LogEntry {
  id: string;
  type: 'error' | 'info' | 'success' | 'warning' | 'github';
  message: string;
  timestamp: string;
  agent?: 'ceo' | 'developer';
  gitHubAction?: 'commit' | 'pull-request' | 'merge' | 'test';
}

interface CompletionLogProps {
  isActive: boolean;
  errorTriggered: boolean;
}

export function CompletionLog({ isActive, errorTriggered }: CompletionLogProps) {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  // Demo log entries
  const logSteps = useMemo(() => [
    {
      id: 'log-1',
      type: 'warning' as const,
      message: 'Email alert received from Vercel: Site down',
      timestamp: '2:14 AM',
    },
    {
      id: 'log-2',
      type: 'info' as const,
      message: 'CEO agent received alert notification',
      timestamp: '2:14 AM',
      agent: 'ceo' as const,
    },
    {
      id: 'log-3',
      type: 'info' as const,
      message: 'CEO agent delegated issue to Developer agent',
      timestamp: '2:16 AM',
      agent: 'ceo' as const,
    },
    {
      id: 'log-4',
      type: 'info' as const,
      message: 'Developer agent analyzing error logs',
      timestamp: '2:17 AM',
      agent: 'developer' as const,
    },
    {
      id: 'log-5',
      type: 'info' as const,
      message: 'Issue identified in UserDashboard component',
      timestamp: '2:18 AM',
      agent: 'developer' as const,
    },
    {
      id: 'log-6',
      type: 'warning' as const,
      message: 'Root cause: Missing error handling in UserDashboard.tsx',
      timestamp: '2:20 AM',
      agent: 'developer' as const,
    },
    {
      id: 'log-7',
      type: 'info' as const,
      message: 'Developer agent implementing fix with proper error handling',
      timestamp: '2:23 AM',
      agent: 'developer' as const,
    },
    {
      id: 'log-8',
      type: 'github' as const,
      message: 'Developer agent created commit: Fix UserDashboard component error handling',
      timestamp: '2:28 AM',
      gitHubAction: 'commit' as const,
    },
    {
      id: 'log-9',
      type: 'github' as const,
      message: 'Developer agent created pull request: Fix UserDashboard component error handling (#42)',
      timestamp: '2:29 AM',
      gitHubAction: 'pull-request' as const,
    },
    {
      id: 'log-10',
      type: 'github' as const,
      message: 'Developer agent running tests on pull request #42',
      timestamp: '2:30 AM',
      gitHubAction: 'test' as const,
    },
    {
      id: 'log-11',
      type: 'github' as const,
      message: 'Tests passed: All 24 tests passing',
      timestamp: '2:31 AM',
      gitHubAction: 'test' as const,
    },
    {
      id: 'log-12',
      type: 'github' as const,
      message: 'Developer agent merged pull request: Fix UserDashboard component error handling (#42)',
      timestamp: '2:32 AM',
      gitHubAction: 'merge' as const,
    },
    {
      id: 'log-13',
      type: 'info' as const,
      message: 'Developer agent deploying fix to production',
      timestamp: '2:33 AM',
      agent: 'developer' as const,
    },
    {
      id: 'log-14',
      type: 'success' as const,
      message: 'Fix deployed successfully by Developer agent',
      timestamp: '2:36 AM',
      agent: 'developer' as const,
    },
    {
      id: 'log-15',
      type: 'info' as const,
      message: 'Developer agent reported resolution to CEO agent',
      timestamp: '2:37 AM',
      agent: 'developer' as const,
    },
    {
      id: 'log-16',
      type: 'info' as const,
      message: 'Developer agent provided recommendations for preventing future issues',
      timestamp: '2:38 AM',
      agent: 'developer' as const,
    },
    {
      id: 'log-17',
      type: 'success' as const,
      message: 'CEO agent confirmed issue resolution - Response time: 23 minutes',
      timestamp: '2:39 AM',
      agent: 'ceo' as const,
    },
  ], []);

  // Progress through log steps when active
  useEffect(() => {
    if (!isActive || !errorTriggered || currentStep >= logSteps.length || isComplete) {
      return;
    }

    const timer = setTimeout(() => {
      setLogs(prev => [...prev, logSteps[currentStep]]);

      if (currentStep === logSteps.length - 1) {
        setIsComplete(true);
      } else {
        setCurrentStep(prev => prev + 1);
      }
    }, Math.random() * 1000 + 1000); // Random delay between 1-2 seconds

    return () => clearTimeout(timer);
  }, [isActive, errorTriggered, currentStep, isComplete, logSteps]);

  // Reset demo when triggered
  useEffect(() => {
    if (errorTriggered) {
      setLogs([]);
      setCurrentStep(0);
      setIsComplete(false);
    }
  }, [errorTriggered]);

  const getLogIcon = (log: LogEntry) => {
    if (log.type === 'error') return <AlertCircle className="h-4 w-4 text-white" />;
    if (log.type === 'success') return <CheckCircle2 className="h-4 w-4 text-white" />;
    if (log.type === 'warning') return <AlertCircle className="h-4 w-4 text-white" />;
    if (log.type === 'github') {
      if (log.gitHubAction === 'commit') return <GitCommit className="h-4 w-4 text-white" />;
      if (log.gitHubAction === 'pull-request') return <GitPullRequest className="h-4 w-4 text-white" />;
      if (log.gitHubAction === 'merge') return <GitMerge className="h-4 w-4 text-white" />;
      if (log.gitHubAction === 'test') return <CheckCircle2 className="h-4 w-4 text-white" />;
    }
    return <Clock className="h-4 w-4 text-white" />;
  };

  const getLogBadge = (log: LogEntry) => {
    if (log.agent === 'ceo') {
      return <Badge variant="outline" className="ml-2 bg-[#4f6bed]/10 text-[#4f6bed] border-[#4f6bed]/20 text-xs font-normal">Kenard</Badge>;
    }
    if (log.agent === 'developer') {
      return <Badge variant="outline" className="ml-2 bg-[#5b5fc7]/10 text-[#5b5fc7] border-[#5b5fc7]/20 text-xs font-normal">Alex</Badge>;
    }
    return null;
  };

  return (
    <Card className="h-[600px] bg-white border-[#e1dfdd]">
      <CardHeader className="pb-2 border-b border-[#e1dfdd]">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg text-[#252423]">Activity Timeline</CardTitle>
          {logs.length > 0 && isComplete && (
            <Badge variant="outline" className="bg-[#107c10]/10 text-[#107c10] border-[#107c10]/20">
              Completed
            </Badge>
          )}
          {logs.length > 0 && !isComplete && (
            <Badge variant="outline" className="bg-[#0078d4]/10 text-[#0078d4] border-[#0078d4]/20">
              In Progress
            </Badge>
          )}
        </div>
        <CardDescription className="text-[#605e5c]">
          {logs.length === 0
            ? 'Simulate an alert to see agent activities'
            : isComplete
              ? 'Issue successfully resolved'
              : 'Agents responding to alert...'}
        </CardDescription>
      </CardHeader>
      <CardContent className="overflow-y-auto h-[calc(600px-4rem)] p-0">
        {logs.length === 0 ? (
          <div className="h-full flex items-center justify-center text-[#605e5c]">
            <p>No activity yet</p>
          </div>
        ) : (
          <div className="divide-y divide-[#e1dfdd]">
            {logs.map((log) => {
              const iconBg =
                log.type === 'error' ? "bg-[#c4314b]" :
                log.type === 'success' ? "bg-[#107c10]" :
                log.type === 'warning' ? "bg-[#f2c811]" :
                log.type === 'github' ? "bg-[#5e2398]" :
                "bg-[#0078d4]";

              return (
                <div key={log.id} className="p-4 hover:bg-[#f3f2f1] transition-colors">
                  <div className="flex items-start gap-3">
                    <div className={`${iconBg} h-8 w-8 rounded-full flex items-center justify-center flex-shrink-0`}>
                      {getLogIcon(log)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between gap-2">
                        <span className="font-medium text-sm truncate text-[#252423]">{log.message}</span>
                        {getLogBadge(log)}
                      </div>
                      <div className="flex items-center justify-between mt-1">
                        <div className="text-xs text-[#605e5c]">{log.timestamp}</div>
                        {log.type === 'success' && (
                          <div className="flex items-center text-xs text-[#107c10]">
                            <CheckCircle2 className="h-3 w-3 mr-1" />
                            <span>Success</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
