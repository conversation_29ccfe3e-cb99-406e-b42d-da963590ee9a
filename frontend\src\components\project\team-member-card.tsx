'use client';

import React from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MoreHorizontal, Crown } from 'lucide-react';

interface TeamMemberCardProps {
  member: {
    id: string;
    name: string;
    email?: string;
    role: string;
    avatar?: string | null;
    status?: string;
    lastActive?: string;
    imageSrc?: string;
    skills?: string[];
    color?: string;
    description?: string;
  };
  type: 'real' | 'agent';
  onRemove?: (id: string) => void;
  showRemove?: boolean;
  currentUserRole?: string;
}

export function TeamMemberCard({
  member,
  type,
  onRemove,
  showRemove = true,
  currentUserRole = 'member'
}: TeamMemberCardProps) {
  if (type === 'real') {
    return (
      <div className="p-4 border border-[#333333] rounded-md bg-[#1a1a1a]">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0 w-10 h-10 rounded-md bg-[#6466E9] overflow-hidden">
              {member.avatar ? (
                <Image
                  src={member.avatar}
                  alt={member.name}
                  width={40}
                  height={40}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="h-10 w-10 flex items-center justify-center text-white font-medium">
                  {member.name.split(' ').map(n => n[0]).join('')}
                </div>
              )}
            </div>
            <div>
              <div className="flex items-center gap-2">
                <span className="text-white font-medium">{member.name}</span>
                <Badge variant="secondary" className="bg-[#333333] text-[#999999] text-xs">
                  {member.role}
                </Badge>
                {member.role === 'Admin' && (
                  <Crown className="h-3 w-3 text-yellow-500" />
                )}
              </div>
              {member.email && (
                <div className="text-[#999999] text-sm">{member.email}</div>
              )}
            </div>
          </div>
          <div className="flex items-center">

          </div>
        </div>
      </div>
    );
  }

  // Agent card
  return (
    <div className="p-4 border border-[#333333] rounded-md bg-[#1a1a1a]">
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-3">
          <div 
            className="flex-shrink-0 w-10 h-10 rounded-md overflow-hidden" 
            style={{ backgroundColor: member.color }}
          >
            {member.imageSrc && (
              <Image
                src={member.imageSrc}
                alt={member.name}
                width={40}
                height={40}
                className="w-full h-full object-cover"
              />
            )}
          </div>
          <div>
            <div className="flex items-center gap-2">
              <span className="text-white font-medium">{member.name}</span>
              <Badge variant="secondary" className="bg-[#333333] text-[#999999] text-xs">
                {member.role}
              </Badge>
            </div>
            {member.email && (
              <div className="text-[#999999] text-sm">{member.email}</div>
            )}
          </div>
        </div>
        {showRemove && onRemove && (
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-[#999999] hover:text-red-400"
            onClick={() => onRemove(member.id)}
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
