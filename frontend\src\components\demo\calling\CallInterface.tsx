"use client";

import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { ProjectSidebarLeft } from '@/components/sidebar/project-sidebar-left';

// Import types
import { CallInterfaceProps, VideoDevice } from './types';

// Import components
import { CallControls } from './CallControls';
import { CallHeader } from './CallHeader';
import { UserVideo } from './UserVideo';
import { UserVideoPiP } from './UserVideoPiP';
import { VideoGrid } from './VideoGrid';
import { SidePanel } from './SidePanel';
import { ParticipantVideo } from './ParticipantVideo';

export function CallInterface({ callType, callName, participants, onEndCall, onMinimize }: CallInterfaceProps) {
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOn, setIsVideoOn] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isSpeakerMuted, setIsSpeakerMuted] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isParticipantsOpen, setIsParticipantsOpen] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const [videoDevices, setVideoDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string>("");
  const [isDeviceMenuOpen, setIsDeviceMenuOpen] = useState(false);
  const [webcamStream, setWebcamStream] = useState<MediaStream | null>(null);
  const userVideoRef = useRef<HTMLVideoElement>(null);
  const pipVideoRef = useRef<HTMLVideoElement>(null);
  const deviceMenuRef = useRef<HTMLDivElement>(null);

  // Enumerate available video devices
  useEffect(() => {
    const getVideoDevices = async () => {
      try {
        // First request permission to access devices
        await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
        
        // Then enumerate devices
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoInputs = devices.filter(device => device.kind === 'videoinput');
        setVideoDevices(videoInputs);
        
        // Set the first device as default if we have devices and no selection yet
        if (videoInputs.length > 0 && !selectedDeviceId) {
          setSelectedDeviceId(videoInputs[0].deviceId);
        }
      } catch (err) {
        console.error("Error enumerating video devices:", err);
      }
    };
    
    getVideoDevices();
    
    // Listen for device changes
    navigator.mediaDevices.addEventListener('devicechange', getVideoDevices);
    
    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', getVideoDevices);
    };
  }, [selectedDeviceId]); // selectedDeviceId is intentionally included here
  
  // Preload participant avatars to ensure they load properly
  useEffect(() => {
    // Preload all participant avatars
    const preloadedImages: HTMLImageElement[] = [];
    
    participants.forEach(participant => {
      if (participant.avatar) {
        const img = document.createElement('img');
        img.src = participant.avatar;
        img.style.display = 'none'; // Hide the image
        img.onload = () => console.log(`Avatar for ${participant.name} preloaded successfully`);
        img.onerror = (e) => console.error(`Failed to preload avatar for ${participant.name}:`, e);
        document.body.appendChild(img);
        preloadedImages.push(img);
      }
    });
    
    // Cleanup function to remove the preloaded images
    return () => {
      preloadedImages.forEach(img => {
        if (img.parentNode) {
          img.parentNode.removeChild(img);
        }
      });
    };
  }, [participants]);
  
  // Initialize webcam on component mount
  useEffect(() => {
    let mounted = true;
    const localStream: MediaStream | null = null;
    
    const initializeWebcam = async () => {
      try {
        console.log('Starting webcam initialization');
        
        // First request permission with simple constraints
        const initialStream = await navigator.mediaDevices.getUserMedia({
          video: true,
          audio: true
        });
        
        // Now that we have permission, enumerate devices
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoInputs = devices.filter(device => device.kind === 'videoinput');
        console.log('Available video devices:', videoInputs.map(d => d.label));
        
        // Set first device as default if none selected
        if (videoInputs.length > 0 && !selectedDeviceId) {
          setSelectedDeviceId(videoInputs[0].deviceId);
          setVideoDevices(videoInputs);
        }
        
        // Use the initial stream
        if (mounted) {
          setWebcamStream(initialStream);
          console.log('Initial webcam stream set with tracks:',
            initialStream.getTracks().map(t => `${t.kind}:${t.label}:${t.enabled}`));

          // Connect to video elements - let the useEffect handle playing
          if (userVideoRef.current) {
            userVideoRef.current.srcObject = initialStream;
          }
          if (pipVideoRef.current) {
            pipVideoRef.current.srcObject = initialStream;
          }
        } else {
          // Clean up if component unmounted during initialization
          initialStream.getTracks().forEach(track => track.stop());
        }
      } catch (err) {
        console.error("Error initializing webcam:", err);
      }
    };
    
    // Initialize webcam on mount
    initializeWebcam();
    
    // Cleanup function
    return () => {
      mounted = false;
      if (localStream) {
        localStream.getTracks().forEach(track => track.stop());
      }
    };
  }, [selectedDeviceId]); // Include selectedDeviceId as dependency since it's used in the effect // Empty dependency array - only run once on mount
  
  // Handle device selection changes
  useEffect(() => {
    if (!selectedDeviceId || !isVideoOn) return;

    let localStream: MediaStream | null = null;
    let isCancelled = false;

    const switchCamera = async () => {
      try {
        // Stop previous stream tracks
        if (webcamStream) {
          webcamStream.getTracks().forEach(track => track.stop());
        }

        // Get stream with new device
        localStream = await navigator.mediaDevices.getUserMedia({
          video: { deviceId: { exact: selectedDeviceId } },
          audio: true
        });

        // Only update if not cancelled
        if (!isCancelled) {
          setWebcamStream(localStream);
          console.log('Switched to camera:', selectedDeviceId);
        } else {
          // Clean up if cancelled
          localStream.getTracks().forEach(track => track.stop());
        }
      } catch (err) {
        console.error("Error switching camera:", err);
      }
    };

    switchCamera();

    return () => {
      isCancelled = true;
      if (localStream && localStream !== webcamStream) {
        localStream.getTracks().forEach(track => track.stop());
      }
    };
  }, [selectedDeviceId, isVideoOn]);
  
  // Update video elements when webcam stream changes
  useEffect(() => {
    if (!webcamStream) return;

    console.log('Updating video elements with stream');

    // Capture ref values to avoid ESLint warnings
    const userVideo = userVideoRef.current;
    const pipVideo = pipVideoRef.current;

    // Helper function to safely play video
    const safePlayVideo = async (video: HTMLVideoElement, name: string) => {
      try {
        // Pause any existing playback first
        if (!video.paused) {
          video.pause();
        }

        // Wait a brief moment for any pending operations to complete
        await new Promise(resolve => setTimeout(resolve, 10));

        // Attempt to play
        await video.play();
        console.log(`${name} video playing successfully`);
      } catch (error) {
        // Only log if it's not an abort error (which is expected during stream changes)
        if (error instanceof Error && !error.message.includes('interrupted')) {
          console.error(`Error playing ${name} video:`, error);
        }
      }
    };

    // Set stream to video elements
    if (userVideo) {
      userVideo.srcObject = webcamStream;

      // Handle metadata loaded event
      const handleUserVideoMetadata = () => {
        console.log('User video metadata loaded');
        safePlayVideo(userVideo, 'user');
      };

      userVideo.onloadedmetadata = handleUserVideoMetadata;

      // If already loaded, play it now
      if (userVideo.readyState >= 2) {
        handleUserVideoMetadata();
      }
    }

    if (pipVideo) {
      pipVideo.srcObject = webcamStream;

      // Handle metadata loaded event
      const handlePipVideoMetadata = () => {
        console.log('PiP video metadata loaded');
        safePlayVideo(pipVideo, 'PiP');
      };

      pipVideo.onloadedmetadata = handlePipVideoMetadata;

      // If already loaded, play it now
      if (pipVideo.readyState >= 2) {
        handlePipVideoMetadata();
      }
    }

    // Set track enabled state based on isVideoOn
    webcamStream.getVideoTracks().forEach(track => {
      track.enabled = isVideoOn;
      console.log(`Initial track state: ${track.label} enabled=${track.enabled}`);
    });

    // Cleanup function
    return () => {
      if (userVideo) {
        userVideo.onloadedmetadata = null;
        // Pause video to prevent any pending play operations
        if (!userVideo.paused) {
          userVideo.pause();
        }
      }
      if (pipVideo) {
        pipVideo.onloadedmetadata = null;
        // Pause video to prevent any pending play operations
        if (!pipVideo.paused) {
          pipVideo.pause();
        }
      }
    };
  }, [webcamStream, isVideoOn]); // Both webcamStream and isVideoOn are used in the effect
  
  // Handle video on/off toggle
  useEffect(() => {
    if (!webcamStream) return;
    
    // Toggle video tracks enabled state
    webcamStream.getVideoTracks().forEach(track => {
      track.enabled = isVideoOn;
      console.log(`Video track ${track.label} enabled: ${track.enabled}`);
    });
  }, [isVideoOn, webcamStream]);
  
  // Simulate call duration timer
  useEffect(() => {
    const timer = setInterval(() => {
      setCallDuration(prev => prev + 1);
    }, 1000);
    
    return () => clearInterval(timer);
  }, []);

  // Format call duration as MM:SS
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Toggle functions
  const toggleMute = () => setIsMuted(!isMuted);
  const toggleVideo = () => setIsVideoOn(!isVideoOn);
  const toggleScreenShare = () => setIsScreenSharing(!isScreenSharing);
  const toggleSpeaker = () => setIsSpeakerMuted(!isSpeakerMuted);
  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
    if (!isChatOpen) setIsParticipantsOpen(false);
  };
  const toggleParticipants = () => {
    setIsParticipantsOpen(!isParticipantsOpen);
    if (!isParticipantsOpen) setIsChatOpen(false);
  };

  // Handle right-click on video button to open device menu
  const handleVideoRightClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDeviceMenuOpen(!isDeviceMenuOpen);
  };

  // Select camera
  const selectCamera = (deviceId: string) => {
    setSelectedDeviceId(deviceId);
    setIsDeviceMenuOpen(false);
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <ProjectSidebarLeft activeSection="calling" onSectionChange={() => {}} />
      
      <div className="flex flex-col flex-1">
        {/* Header */}
        <CallHeader
          callName={callName}
          callDuration={callDuration}
          participantsCount={participants.length + 1}
          toggleChat={toggleChat}
          toggleParticipants={toggleParticipants}
          onMinimize={onMinimize}
        />

        {/* Main content area */}
        <div className="flex-1 flex overflow-hidden h-full relative">
          {/* Screen sharing PiP */}
          {isScreenSharing && (
            <UserVideoPiP 
              isVideoOn={isVideoOn}
              pipVideoRef={pipVideoRef}
            />
          )}
          
          {/* Video grid */}
          <div className={cn(isChatOpen || isParticipantsOpen ? 
            "flex-1 p-2 overflow-auto flex items-center justify-center" : 
            "w-full p-3 overflow-auto flex items-center justify-center", 
            "h-full")}>
            <div className={cn(
              "grid gap-2 place-items-center w-full auto-rows-fr",
              isChatOpen || isParticipantsOpen ? "max-w-6xl" : "max-w-7xl",
              "mx-auto",
              participants.length === 0 ? "grid-cols-1" :
              participants.length === 1 ? "grid-cols-1 md:grid-cols-2" :
              participants.length <= 3 ? "grid-cols-2" :
              participants.length <= 5 ? "grid-cols-3" :
              participants.length <= 8 ? "grid-cols-3" : "grid-cols-4"
            )}>
              {/* User's video */}
              <UserVideo
                isVideoOn={isVideoOn}
                isScreenSharing={isScreenSharing}
                isMuted={isMuted}
                userVideoRef={userVideoRef}
              />

              {/* Participant videos */}
              {participants.map(participant => (
                <ParticipantVideo
                  key={participant.id}
                  participant={participant}
                />
              ))}
            </div>
          </div>

          {/* Side panel - Chat or Participants */}
          <SidePanel
            isChatOpen={isChatOpen}
            isParticipantsOpen={isParticipantsOpen}
            participants={participants}
          />
        </div>

        {/* Call controls */}
        <footer>
          <CallControls
            isMuted={isMuted}
            isVideoOn={isVideoOn}
            isScreenSharing={isScreenSharing}
            isSpeakerMuted={isSpeakerMuted}
            isChatOpen={isChatOpen}
            isParticipantsOpen={isParticipantsOpen}
            videoDevices={videoDevices}
            selectedDeviceId={selectedDeviceId}
            isDeviceMenuOpen={isDeviceMenuOpen}
            toggleMute={toggleMute}
            toggleVideo={toggleVideo}
            toggleScreenShare={toggleScreenShare}
            toggleSpeaker={toggleSpeaker}
            toggleChat={toggleChat}
            toggleParticipants={toggleParticipants}
            handleVideoRightClick={handleVideoRightClick}
            selectCamera={selectCamera}
            onEndCall={onEndCall}
          />
        </footer>
      </div>
    </div>
  );
}