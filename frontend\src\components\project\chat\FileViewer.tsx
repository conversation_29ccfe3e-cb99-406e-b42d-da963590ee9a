'use client';

import React from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { File, FileText } from 'lucide-react';
import { FileRenderer } from '@/components/file-renderers';
import { SelectedFile } from './types';

interface FileViewerProps {
  fileViewerOpen: boolean;
  setFileViewerOpen: (open: boolean) => void;
  selectedFile: SelectedFile | null;
  setSelectedFile: (file: SelectedFile | null) => void;
}

export function FileViewer({
  fileViewerOpen,
  setFileViewerOpen,
  selectedFile,
  setSelectedFile
}: FileViewerProps) {
  return (
    <Dialog open={fileViewerOpen} onOpenChange={setFileViewerOpen}>
      <DialogContent className="max-w-4xl h-[80vh] p-0 flex flex-col">
        <div className="p-4 border-b border-border flex items-center justify-between">
          <div className="flex items-center">
            <File className="h-5 w-5 mr-2 text-primary" />
            <h2 className="text-lg font-semibold">Agent's Workspace</h2>
          </div>
          <Button variant="ghost" size="sm" onClick={() => setFileViewerOpen(false)}>
            Close
          </Button>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* File Browser Sidebar */}
          <div className="w-64 border-r border-border overflow-y-auto p-2">
            <div className="mb-4">
              <h3 className="text-sm font-medium mb-2 px-2">Workspace</h3>
              <div className="space-y-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setSelectedFile({
                      name: 'UserDashboard.tsx',
                      content: `import React from 'react';
import { useUserData } from '@/hooks/useUserData';
import { UserMetricsDisplay } from '@/components/UserMetricsDisplay';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { ErrorDisplay } from '@/components/ErrorDisplay';

const UserDashboard = ({ userId }) => {
  const { data, isLoading, error } = useUserData(userId);

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorDisplay message={error.message} />;
  if (!data) return <ErrorDisplay message="Failed to load user data" />;

  const userMetrics = data.metrics || {};

  return (
    <div>
      <h2>Welcome, {data.name}</h2>
      <UserMetricsDisplay metrics={userMetrics} />
    </div>
  );
};

export default UserDashboard;`,
                      type: 'code'
                    });
                  }}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  <span className="truncate">UserDashboard.tsx</span>
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setSelectedFile({
                      name: 'README.md',
                      content: `# Project Documentation

## Overview
This is the main dashboard application for our service.

## Components
- UserDashboard: Displays user information and metrics
- ErrorDisplay: Shows error messages
- LoadingSpinner: Indicates loading state

## Recent Changes
- Fixed error handling in UserDashboard component
- Added proper loading states
- Implemented fallback values for missing data`,
                      type: 'markdown'
                    });
                  }}
                >
                  <FileText className="h-4 w-4 mr-2" />
                  <span className="truncate">README.md</span>
                </Button>
              </div>
            </div>
          </div>

          {/* File Content */}
          <div className="flex-1 overflow-auto">
            {selectedFile ? (
              <div className="h-full">
                <div className="border-b border-border p-2 bg-muted/30 flex items-center">
                  <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span className="text-sm font-medium">{selectedFile.name}</span>
                </div>
                <div className="h-[calc(100%-36px)]">
                  <FileRenderer
                    content={selectedFile.content}
                    binaryUrl={null}
                    fileName={selectedFile.name}
                    className="h-full"
                  />
                </div>
              </div>
            ) : (
              <div className="h-full flex flex-col items-center justify-center text-muted-foreground">
                <File className="h-16 w-16 mb-4 opacity-20" />
                <p className="text-lg mb-1">No file selected</p>
                <p className="text-sm">Select a file from the sidebar to view its contents</p>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
