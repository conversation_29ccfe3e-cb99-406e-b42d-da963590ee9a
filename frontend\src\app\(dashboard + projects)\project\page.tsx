'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function ProjectPage() {
  const router = useRouter();

  // Redirect to chat sub-route by default
  useEffect(() => {
    router.replace('/project/chat');
  }, [router]);

  // Show loading while redirecting
  return (
    <div className="h-full w-full bg-background flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p>Loading project...</p>
      </div>
    </div>
  );
}
