from typing import Dict, Any, Optional, List, Union
from enum import Enum
import asyncio
import json
from datetime import datetime

from utils.logger import logger


class IntegrationType(Enum):
    COMPANY = "company"
    USER = "user"


class IntegrationStatus(Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PENDING = "pending"


class BaseIntegration:
    name = "base"
    display_name = "Base Integration"
    description = "Base integration class that all integrations should inherit from"
    
    def __init__(self, 
                 integration_id: str, 
                 account_id: str, 
                 integration_type: IntegrationType = IntegrationType.COMPANY,
                 config: Optional[Dict[str, Any]] = None):
        self.integration_id = integration_id
        self.account_id = account_id
        self.integration_type = integration_type
        self.config = config or {}
        self.status = IntegrationStatus.INACTIVE
        self.last_error = None
        self.last_sync = None
    
    async def initialize(self) -> bool:
        try:
            self.status = IntegrationStatus.ACTIVE
            self.last_sync = datetime.now().isoformat()
            return True
        except Exception as e:
            self.status = IntegrationStatus.ERROR
            self.last_error = str(e)
            logger.error(f"Error initializing {self.name} integration: {str(e)}")
            return False
    
    async def validate_credentials(self) -> bool:
        return True
    
    async def get_status(self) -> Dict[str, Any]:
        return {
            "integration_id": self.integration_id,
            "name": self.name,
            "display_name": self.display_name,
            "status": self.status.value,
            "last_error": self.last_error,
            "last_sync": self.last_sync,
            "integration_type": self.integration_type.value,
            "account_id": self.account_id
        }
    
    async def get_capabilities(self) -> List[str]:
        return []
    
    async def execute_action(self, action: str, params: Dict[str, Any]) -> Dict[str, Any]:
        raise NotImplementedError(f"Action {action} not implemented for {self.name} integration")
    
    async def handle_webhook(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        raise NotImplementedError(f"Webhook handling not implemented for {self.name} integration")
    
    async def sync(self) -> bool:
        try:
            self.last_sync = datetime.now().isoformat()
            return True
        except Exception as e:
            self.last_error = str(e)
            logger.error(f"Error syncing {self.name} integration: {str(e)}")
            return False
