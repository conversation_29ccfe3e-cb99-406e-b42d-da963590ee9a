import React from 'react';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

export function DemoNavigation() {
  return (
    <div className="container py-4 flex items-center">
      <Link href="/" passHref>
        <Button variant="ghost" size="sm">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Home
        </Button>
      </Link>
      <div className="ml-auto">
        <span className="text-sm text-muted-foreground">Siden.ai YC Demo</span>
      </div>
    </div>
  );
}
