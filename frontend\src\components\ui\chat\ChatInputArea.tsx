import React, { useState } from "react";
import { Smile, Send } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { AttachmentButton } from "./AttachmentButton";
import { MessageInput } from "./MessageInput";
import { IconButton } from "./IconButton";
import { SendButton } from "./SendButton";

interface ChatInputAreaProps {
  onSendMessage: (message: string) => void;
  onFileSelect?: (files: FileList | null) => void;
  placeholder?: string;
  className?: string;
  showEmojiPicker?: boolean;
  value?: string;
  onValueChange?: (value: string) => void;
  hasAttachments?: boolean;
}

export function ChatInputArea({
  onSendMessage,
  onFileSelect,
  placeholder = "Type a message...",
  className = "",
  showEmojiPicker = true,
  value,
  onValueChange,
  hasAttachments = false,
}: ChatInputAreaProps) {
  // Use internal state if no external state is provided
  const [internalValue, setInternalValue] = useState("");
  
  // Use either the external or internal state
  const inputValue = value !== undefined ? value : internalValue;
  const setInputValue = (newValue: string) => {
    if (onValueChange) {
      onValueChange(newValue);
    } else {
      setInternalValue(newValue);
    }
  };

  const handleSendMessage = (e?: React.FormEvent) => {
    if (e) e.preventDefault();

    if (inputValue.trim() || hasAttachments) {
      onSendMessage(inputValue);
      setInputValue("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    setInputValue(inputValue + emoji);
  };

  return (
    <form 
      onSubmit={handleSendMessage} 
      className={`border-t border-border h-[60px] px-4 flex items-center gap-2 bg-sidebar-accent ${className}`}
    >
      {onFileSelect && (
        <AttachmentButton onFileSelect={onFileSelect} />
      )}
      
      <MessageInput
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
      />
      
      <SendButton
        onSend={(e) => {
          if (inputValue.trim() || hasAttachments) {
            onSendMessage(inputValue);
            setInputValue('');
          }
        }}
        onEmojiSelect={handleEmojiSelect}
        disabled={!inputValue.trim() && !hasAttachments}
        showEmojiPicker={showEmojiPicker}
      />
    </form>
  );
}
