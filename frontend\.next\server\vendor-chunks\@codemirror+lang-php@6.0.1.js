"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-php@6.0.1";
exports.ids = ["vendor-chunks/@codemirror+lang-php@6.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-php@6.0.1/node_modules/@codemirror/lang-php/dist/index.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-php@6.0.1/node_modules/@codemirror/lang-php/dist/index.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   php: () => (/* binding */ php),\n/* harmony export */   phpLanguage: () => (/* binding */ phpLanguage)\n/* harmony export */ });\n/* harmony import */ var _lezer_php__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/php */ \"(ssr)/./node_modules/.pnpm/@lezer+php@1.0.2/node_modules/@lezer/php/dist/index.es.js\");\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n/* harmony import */ var _codemirror_lang_html__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/lang-html */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-html@6.4.9/node_modules/@codemirror/lang-html/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n\n\n\n\n\n/**\nA language provider based on the [Lezer PHP\nparser](https://github.com/lezer-parser/php), extended with\nhighlighting and indentation information.\n*/\nconst phpLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LRLanguage.define({\n    name: \"php\",\n    parser: /*@__PURE__*/_lezer_php__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.indentNodeProp.add({\n                IfStatement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.continuedIndent)({ except: /^\\s*({|else\\b|elseif\\b|endif\\b)/ }),\n                TryStatement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.continuedIndent)({ except: /^\\s*({|catch\\b|finally\\b)/ }),\n                SwitchBody: context => {\n                    let after = context.textAfter, closed = /^\\s*\\}/.test(after), isCase = /^\\s*(case|default)\\b/.test(after);\n                    return context.baseIndent + (closed ? 0 : isCase ? 1 : 2) * context.unit;\n                },\n                ColonBlock: cx => cx.baseIndent + cx.unit,\n                \"Block EnumBody DeclarationList\": /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.delimitedIndent)({ closing: \"}\" }),\n                ArrowFunction: cx => cx.baseIndent + cx.unit,\n                \"String BlockComment\": () => null,\n                Statement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.continuedIndent)({ except: /^({|end(for|foreach|switch|while)\\b)/ })\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldNodeProp.add({\n                \"Block EnumBody DeclarationList SwitchBody ArrayExpression ValueList\": _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldInside,\n                ColonBlock(tree) { return { from: tree.from + 1, to: tree.to }; },\n                BlockComment(tree) { return { from: tree.from + 2, to: tree.to - 2 }; }\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"/*\", close: \"*/\" }, line: \"//\" },\n        indentOnInput: /^\\s*(?:case |default:|end(?:if|for(?:each)?|switch|while)|else(?:if)?|\\{|\\})$/,\n        wordChars: \"$\",\n        closeBrackets: { stringPrefixes: [\"b\", \"B\"] }\n    }\n});\n/**\nPHP language support.\n*/\nfunction php(config = {}) {\n    let support = [], base;\n    if (config.baseLanguage === null) ;\n    else if (config.baseLanguage) {\n        base = config.baseLanguage;\n    }\n    else {\n        let htmlSupport = (0,_codemirror_lang_html__WEBPACK_IMPORTED_MODULE_3__.html)({ matchClosingTags: false });\n        support.push(htmlSupport.support);\n        base = htmlSupport.language;\n    }\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LanguageSupport(phpLanguage.configure({\n        wrap: base && (0,_lezer_common__WEBPACK_IMPORTED_MODULE_1__.parseMixed)(node => {\n            if (!node.type.isTop)\n                return null;\n            return {\n                parser: base.parser,\n                overlay: node => node.name == \"Text\"\n            };\n        }),\n        top: config.plain ? \"Program\" : \"Template\"\n    }), support);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-php@6.0.1/node_modules/@codemirror/lang-php/dist/index.js\n");

/***/ })

};
;