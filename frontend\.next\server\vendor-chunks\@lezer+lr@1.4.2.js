"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lezer+lr@1.4.2";
exports.ids = ["vendor-chunks/@lezer+lr@1.4.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@lezer+lr@1.4.2/node_modules/@lezer/lr/dist/index.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/@lezer+lr@1.4.2/node_modules/@lezer/lr/dist/index.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContextTracker: () => (/* binding */ ContextTracker),\n/* harmony export */   ExternalTokenizer: () => (/* binding */ ExternalTokenizer),\n/* harmony export */   InputStream: () => (/* binding */ InputStream),\n/* harmony export */   LRParser: () => (/* binding */ LRParser),\n/* harmony export */   LocalTokenGroup: () => (/* binding */ LocalTokenGroup),\n/* harmony export */   Stack: () => (/* binding */ Stack)\n/* harmony export */ });\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n\n\n/**\nA parse stack. These are used internally by the parser to track\nparsing progress. They also provide some properties and methods\nthat external code such as a tokenizer can use to get information\nabout the parse state.\n*/\nclass Stack {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parse that this stack is part of @internal\n    */\n    p, \n    /**\n    Holds state, input pos, buffer index triplets for all but the\n    top state @internal\n    */\n    stack, \n    /**\n    The current parse state @internal\n    */\n    state, \n    // The position at which the next reduce should take place. This\n    // can be less than `this.pos` when skipped expressions have been\n    // added to the stack (which should be moved outside of the next\n    // reduction)\n    /**\n    @internal\n    */\n    reducePos, \n    /**\n    The input position up to which this stack has parsed.\n    */\n    pos, \n    /**\n    The dynamic score of the stack, including dynamic precedence\n    and error-recovery penalties\n    @internal\n    */\n    score, \n    // The output buffer. Holds (type, start, end, size) quads\n    // representing nodes created by the parser, where `size` is\n    // amount of buffer array entries covered by this node.\n    /**\n    @internal\n    */\n    buffer, \n    // The base offset of the buffer. When stacks are split, the split\n    // instance shared the buffer history with its parent up to\n    // `bufferBase`, which is the absolute offset (including the\n    // offset of previous splits) into the buffer at which this stack\n    // starts writing.\n    /**\n    @internal\n    */\n    bufferBase, \n    /**\n    @internal\n    */\n    curContext, \n    /**\n    @internal\n    */\n    lookAhead = 0, \n    // A parent stack from which this was split off, if any. This is\n    // set up so that it always points to a stack that has some\n    // additional buffer content, never to a stack with an equal\n    // `bufferBase`.\n    /**\n    @internal\n    */\n    parent) {\n        this.p = p;\n        this.stack = stack;\n        this.state = state;\n        this.reducePos = reducePos;\n        this.pos = pos;\n        this.score = score;\n        this.buffer = buffer;\n        this.bufferBase = bufferBase;\n        this.curContext = curContext;\n        this.lookAhead = lookAhead;\n        this.parent = parent;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return `[${this.stack.filter((_, i) => i % 3 == 0).concat(this.state)}]@${this.pos}${this.score ? \"!\" + this.score : \"\"}`;\n    }\n    // Start an empty stack\n    /**\n    @internal\n    */\n    static start(p, state, pos = 0) {\n        let cx = p.parser.context;\n        return new Stack(p, [], state, pos, pos, 0, [], 0, cx ? new StackContext(cx, cx.start) : null, 0, null);\n    }\n    /**\n    The stack's current [context](#lr.ContextTracker) value, if\n    any. Its type will depend on the context tracker's type\n    parameter, or it will be `null` if there is no context\n    tracker.\n    */\n    get context() { return this.curContext ? this.curContext.context : null; }\n    // Push a state onto the stack, tracking its start position as well\n    // as the buffer base at that point.\n    /**\n    @internal\n    */\n    pushState(state, start) {\n        this.stack.push(this.state, start, this.bufferBase + this.buffer.length);\n        this.state = state;\n    }\n    // Apply a reduce action\n    /**\n    @internal\n    */\n    reduce(action) {\n        var _a;\n        let depth = action >> 19 /* Action.ReduceDepthShift */, type = action & 65535 /* Action.ValueMask */;\n        let { parser } = this.p;\n        let lookaheadRecord = this.reducePos < this.pos - 25 /* Lookahead.Margin */;\n        if (lookaheadRecord)\n            this.setLookAhead(this.pos);\n        let dPrec = parser.dynamicPrecedence(type);\n        if (dPrec)\n            this.score += dPrec;\n        if (depth == 0) {\n            this.pushState(parser.getGoto(this.state, type, true), this.reducePos);\n            // Zero-depth reductions are a special case—they add stuff to\n            // the stack without popping anything off.\n            if (type < parser.minRepeatTerm)\n                this.storeNode(type, this.reducePos, this.reducePos, lookaheadRecord ? 8 : 4, true);\n            this.reduceContext(type, this.reducePos);\n            return;\n        }\n        // Find the base index into `this.stack`, content after which will\n        // be dropped. Note that with `StayFlag` reductions we need to\n        // consume two extra frames (the dummy parent node for the skipped\n        // expression and the state that we'll be staying in, which should\n        // be moved to `this.state`).\n        let base = this.stack.length - ((depth - 1) * 3) - (action & 262144 /* Action.StayFlag */ ? 6 : 0);\n        let start = base ? this.stack[base - 2] : this.p.ranges[0].from, size = this.reducePos - start;\n        // This is a kludge to try and detect overly deep left-associative\n        // trees, which will not increase the parse stack depth and thus\n        // won't be caught by the regular stack-depth limit check.\n        if (size >= 2000 /* Recover.MinBigReduction */ && !((_a = this.p.parser.nodeSet.types[type]) === null || _a === void 0 ? void 0 : _a.isAnonymous)) {\n            if (start == this.p.lastBigReductionStart) {\n                this.p.bigReductionCount++;\n                this.p.lastBigReductionSize = size;\n            }\n            else if (this.p.lastBigReductionSize < size) {\n                this.p.bigReductionCount = 1;\n                this.p.lastBigReductionStart = start;\n                this.p.lastBigReductionSize = size;\n            }\n        }\n        let bufferBase = base ? this.stack[base - 1] : 0, count = this.bufferBase + this.buffer.length - bufferBase;\n        // Store normal terms or `R -> R R` repeat reductions\n        if (type < parser.minRepeatTerm || (action & 131072 /* Action.RepeatFlag */)) {\n            let pos = parser.stateFlag(this.state, 1 /* StateFlag.Skipped */) ? this.pos : this.reducePos;\n            this.storeNode(type, start, pos, count + 4, true);\n        }\n        if (action & 262144 /* Action.StayFlag */) {\n            this.state = this.stack[base];\n        }\n        else {\n            let baseStateID = this.stack[base - 3];\n            this.state = parser.getGoto(baseStateID, type, true);\n        }\n        while (this.stack.length > base)\n            this.stack.pop();\n        this.reduceContext(type, start);\n    }\n    // Shift a value into the buffer\n    /**\n    @internal\n    */\n    storeNode(term, start, end, size = 4, mustSink = false) {\n        if (term == 0 /* Term.Err */ &&\n            (!this.stack.length || this.stack[this.stack.length - 1] < this.buffer.length + this.bufferBase)) {\n            // Try to omit/merge adjacent error nodes\n            let cur = this, top = this.buffer.length;\n            if (top == 0 && cur.parent) {\n                top = cur.bufferBase - cur.parent.bufferBase;\n                cur = cur.parent;\n            }\n            if (top > 0 && cur.buffer[top - 4] == 0 /* Term.Err */ && cur.buffer[top - 1] > -1) {\n                if (start == end)\n                    return;\n                if (cur.buffer[top - 2] >= start) {\n                    cur.buffer[top - 2] = end;\n                    return;\n                }\n            }\n        }\n        if (!mustSink || this.pos == end) { // Simple case, just append\n            this.buffer.push(term, start, end, size);\n        }\n        else { // There may be skipped nodes that have to be moved forward\n            let index = this.buffer.length;\n            if (index > 0 && this.buffer[index - 4] != 0 /* Term.Err */) {\n                let mustMove = false;\n                for (let scan = index; scan > 0 && this.buffer[scan - 2] > end; scan -= 4) {\n                    if (this.buffer[scan - 1] >= 0) {\n                        mustMove = true;\n                        break;\n                    }\n                }\n                if (mustMove)\n                    while (index > 0 && this.buffer[index - 2] > end) {\n                        // Move this record forward\n                        this.buffer[index] = this.buffer[index - 4];\n                        this.buffer[index + 1] = this.buffer[index - 3];\n                        this.buffer[index + 2] = this.buffer[index - 2];\n                        this.buffer[index + 3] = this.buffer[index - 1];\n                        index -= 4;\n                        if (size > 4)\n                            size -= 4;\n                    }\n            }\n            this.buffer[index] = term;\n            this.buffer[index + 1] = start;\n            this.buffer[index + 2] = end;\n            this.buffer[index + 3] = size;\n        }\n    }\n    // Apply a shift action\n    /**\n    @internal\n    */\n    shift(action, type, start, end) {\n        if (action & 131072 /* Action.GotoFlag */) {\n            this.pushState(action & 65535 /* Action.ValueMask */, this.pos);\n        }\n        else if ((action & 262144 /* Action.StayFlag */) == 0) { // Regular shift\n            let nextState = action, { parser } = this.p;\n            if (end > this.pos || type <= parser.maxNode) {\n                this.pos = end;\n                if (!parser.stateFlag(nextState, 1 /* StateFlag.Skipped */))\n                    this.reducePos = end;\n            }\n            this.pushState(nextState, start);\n            this.shiftContext(type, start);\n            if (type <= parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n        else { // Shift-and-stay, which means this is a skipped token\n            this.pos = end;\n            this.shiftContext(type, start);\n            if (type <= this.p.parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n    }\n    // Apply an action\n    /**\n    @internal\n    */\n    apply(action, next, nextStart, nextEnd) {\n        if (action & 65536 /* Action.ReduceFlag */)\n            this.reduce(action);\n        else\n            this.shift(action, next, nextStart, nextEnd);\n    }\n    // Add a prebuilt (reused) node into the buffer.\n    /**\n    @internal\n    */\n    useNode(value, next) {\n        let index = this.p.reused.length - 1;\n        if (index < 0 || this.p.reused[index] != value) {\n            this.p.reused.push(value);\n            index++;\n        }\n        let start = this.pos;\n        this.reducePos = this.pos = start + value.length;\n        this.pushState(next, start);\n        this.buffer.push(index, start, this.reducePos, -1 /* size == -1 means this is a reused value */);\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reuse(this.curContext.context, value, this, this.p.stream.reset(this.pos - value.length)));\n    }\n    // Split the stack. Due to the buffer sharing and the fact\n    // that `this.stack` tends to stay quite shallow, this isn't very\n    // expensive.\n    /**\n    @internal\n    */\n    split() {\n        let parent = this;\n        let off = parent.buffer.length;\n        // Because the top of the buffer (after this.pos) may be mutated\n        // to reorder reductions and skipped tokens, and shared buffers\n        // should be immutable, this copies any outstanding skipped tokens\n        // to the new buffer, and puts the base pointer before them.\n        while (off > 0 && parent.buffer[off - 2] > parent.reducePos)\n            off -= 4;\n        let buffer = parent.buffer.slice(off), base = parent.bufferBase + off;\n        // Make sure parent points to an actual parent with content, if there is such a parent.\n        while (parent && base == parent.bufferBase)\n            parent = parent.parent;\n        return new Stack(this.p, this.stack.slice(), this.state, this.reducePos, this.pos, this.score, buffer, base, this.curContext, this.lookAhead, parent);\n    }\n    // Try to recover from an error by 'deleting' (ignoring) one token.\n    /**\n    @internal\n    */\n    recoverByDelete(next, nextEnd) {\n        let isNode = next <= this.p.parser.maxNode;\n        if (isNode)\n            this.storeNode(next, this.pos, nextEnd, 4);\n        this.storeNode(0 /* Term.Err */, this.pos, nextEnd, isNode ? 8 : 4);\n        this.pos = this.reducePos = nextEnd;\n        this.score -= 190 /* Recover.Delete */;\n    }\n    /**\n    Check if the given term would be able to be shifted (optionally\n    after some reductions) on this stack. This can be useful for\n    external tokenizers that want to make sure they only provide a\n    given token when it applies.\n    */\n    canShift(term) {\n        for (let sim = new SimulatedStack(this);;) {\n            let action = this.p.parser.stateSlot(sim.state, 4 /* ParseState.DefaultReduce */) || this.p.parser.hasAction(sim.state, term);\n            if (action == 0)\n                return false;\n            if ((action & 65536 /* Action.ReduceFlag */) == 0)\n                return true;\n            sim.reduce(action);\n        }\n    }\n    // Apply up to Recover.MaxNext recovery actions that conceptually\n    // inserts some missing token or rule.\n    /**\n    @internal\n    */\n    recoverByInsert(next) {\n        if (this.stack.length >= 300 /* Recover.MaxInsertStackDepth */)\n            return [];\n        let nextStates = this.p.parser.nextStates(this.state);\n        if (nextStates.length > 4 /* Recover.MaxNext */ << 1 || this.stack.length >= 120 /* Recover.DampenInsertStackDepth */) {\n            let best = [];\n            for (let i = 0, s; i < nextStates.length; i += 2) {\n                if ((s = nextStates[i + 1]) != this.state && this.p.parser.hasAction(s, next))\n                    best.push(nextStates[i], s);\n            }\n            if (this.stack.length < 120 /* Recover.DampenInsertStackDepth */)\n                for (let i = 0; best.length < 4 /* Recover.MaxNext */ << 1 && i < nextStates.length; i += 2) {\n                    let s = nextStates[i + 1];\n                    if (!best.some((v, i) => (i & 1) && v == s))\n                        best.push(nextStates[i], s);\n                }\n            nextStates = best;\n        }\n        let result = [];\n        for (let i = 0; i < nextStates.length && result.length < 4 /* Recover.MaxNext */; i += 2) {\n            let s = nextStates[i + 1];\n            if (s == this.state)\n                continue;\n            let stack = this.split();\n            stack.pushState(s, this.pos);\n            stack.storeNode(0 /* Term.Err */, stack.pos, stack.pos, 4, true);\n            stack.shiftContext(nextStates[i], this.pos);\n            stack.reducePos = this.pos;\n            stack.score -= 200 /* Recover.Insert */;\n            result.push(stack);\n        }\n        return result;\n    }\n    // Force a reduce, if possible. Return false if that can't\n    // be done.\n    /**\n    @internal\n    */\n    forceReduce() {\n        let { parser } = this.p;\n        let reduce = parser.stateSlot(this.state, 5 /* ParseState.ForcedReduce */);\n        if ((reduce & 65536 /* Action.ReduceFlag */) == 0)\n            return false;\n        if (!parser.validAction(this.state, reduce)) {\n            let depth = reduce >> 19 /* Action.ReduceDepthShift */, term = reduce & 65535 /* Action.ValueMask */;\n            let target = this.stack.length - depth * 3;\n            if (target < 0 || parser.getGoto(this.stack[target], term, false) < 0) {\n                let backup = this.findForcedReduction();\n                if (backup == null)\n                    return false;\n                reduce = backup;\n            }\n            this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n            this.score -= 100 /* Recover.Reduce */;\n        }\n        this.reducePos = this.pos;\n        this.reduce(reduce);\n        return true;\n    }\n    /**\n    Try to scan through the automaton to find some kind of reduction\n    that can be applied. Used when the regular ForcedReduce field\n    isn't a valid action. @internal\n    */\n    findForcedReduction() {\n        let { parser } = this.p, seen = [];\n        let explore = (state, depth) => {\n            if (seen.includes(state))\n                return;\n            seen.push(state);\n            return parser.allActions(state, (action) => {\n                if (action & (262144 /* Action.StayFlag */ | 131072 /* Action.GotoFlag */)) ;\n                else if (action & 65536 /* Action.ReduceFlag */) {\n                    let rDepth = (action >> 19 /* Action.ReduceDepthShift */) - depth;\n                    if (rDepth > 1) {\n                        let term = action & 65535 /* Action.ValueMask */, target = this.stack.length - rDepth * 3;\n                        if (target >= 0 && parser.getGoto(this.stack[target], term, false) >= 0)\n                            return (rDepth << 19 /* Action.ReduceDepthShift */) | 65536 /* Action.ReduceFlag */ | term;\n                    }\n                }\n                else {\n                    let found = explore(action, depth + 1);\n                    if (found != null)\n                        return found;\n                }\n            });\n        };\n        return explore(this.state, 0);\n    }\n    /**\n    @internal\n    */\n    forceAll() {\n        while (!this.p.parser.stateFlag(this.state, 2 /* StateFlag.Accepting */)) {\n            if (!this.forceReduce()) {\n                this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n                break;\n            }\n        }\n        return this;\n    }\n    /**\n    Check whether this state has no further actions (assumed to be a direct descendant of the\n    top state, since any other states must be able to continue\n    somehow). @internal\n    */\n    get deadEnd() {\n        if (this.stack.length != 3)\n            return false;\n        let { parser } = this.p;\n        return parser.data[parser.stateSlot(this.state, 1 /* ParseState.Actions */)] == 65535 /* Seq.End */ &&\n            !parser.stateSlot(this.state, 4 /* ParseState.DefaultReduce */);\n    }\n    /**\n    Restart the stack (put it back in its start state). Only safe\n    when this.stack.length == 3 (state is directly below the top\n    state). @internal\n    */\n    restart() {\n        this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n        this.state = this.stack[0];\n        this.stack.length = 0;\n    }\n    /**\n    @internal\n    */\n    sameState(other) {\n        if (this.state != other.state || this.stack.length != other.stack.length)\n            return false;\n        for (let i = 0; i < this.stack.length; i += 3)\n            if (this.stack[i] != other.stack[i])\n                return false;\n        return true;\n    }\n    /**\n    Get the parser used by this stack.\n    */\n    get parser() { return this.p.parser; }\n    /**\n    Test whether a given dialect (by numeric ID, as exported from\n    the terms file) is enabled.\n    */\n    dialectEnabled(dialectID) { return this.p.parser.dialect.flags[dialectID]; }\n    shiftContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.shift(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    reduceContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reduce(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    /**\n    @internal\n    */\n    emitContext() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -3)\n            this.buffer.push(this.curContext.hash, this.pos, this.pos, -3);\n    }\n    /**\n    @internal\n    */\n    emitLookAhead() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -4)\n            this.buffer.push(this.lookAhead, this.pos, this.pos, -4);\n    }\n    updateContext(context) {\n        if (context != this.curContext.context) {\n            let newCx = new StackContext(this.curContext.tracker, context);\n            if (newCx.hash != this.curContext.hash)\n                this.emitContext();\n            this.curContext = newCx;\n        }\n    }\n    /**\n    @internal\n    */\n    setLookAhead(lookAhead) {\n        if (lookAhead > this.lookAhead) {\n            this.emitLookAhead();\n            this.lookAhead = lookAhead;\n        }\n    }\n    /**\n    @internal\n    */\n    close() {\n        if (this.curContext && this.curContext.tracker.strict)\n            this.emitContext();\n        if (this.lookAhead > 0)\n            this.emitLookAhead();\n    }\n}\nclass StackContext {\n    constructor(tracker, context) {\n        this.tracker = tracker;\n        this.context = context;\n        this.hash = tracker.strict ? tracker.hash(context) : 0;\n    }\n}\n// Used to cheaply run some reductions to scan ahead without mutating\n// an entire stack\nclass SimulatedStack {\n    constructor(start) {\n        this.start = start;\n        this.state = start.state;\n        this.stack = start.stack;\n        this.base = this.stack.length;\n    }\n    reduce(action) {\n        let term = action & 65535 /* Action.ValueMask */, depth = action >> 19 /* Action.ReduceDepthShift */;\n        if (depth == 0) {\n            if (this.stack == this.start.stack)\n                this.stack = this.stack.slice();\n            this.stack.push(this.state, 0, 0);\n            this.base += 3;\n        }\n        else {\n            this.base -= (depth - 1) * 3;\n        }\n        let goto = this.start.p.parser.getGoto(this.stack[this.base - 3], term, true);\n        this.state = goto;\n    }\n}\n// This is given to `Tree.build` to build a buffer, and encapsulates\n// the parent-stack-walking necessary to read the nodes.\nclass StackBufferCursor {\n    constructor(stack, pos, index) {\n        this.stack = stack;\n        this.pos = pos;\n        this.index = index;\n        this.buffer = stack.buffer;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    static create(stack, pos = stack.bufferBase + stack.buffer.length) {\n        return new StackBufferCursor(stack, pos, pos - stack.bufferBase);\n    }\n    maybeNext() {\n        let next = this.stack.parent;\n        if (next != null) {\n            this.index = this.stack.bufferBase - next.bufferBase;\n            this.stack = next;\n            this.buffer = next.buffer;\n        }\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    next() {\n        this.index -= 4;\n        this.pos -= 4;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    fork() {\n        return new StackBufferCursor(this.stack, this.pos, this.index);\n    }\n}\n\n// See lezer-generator/src/encode.ts for comments about the encoding\n// used here\nfunction decodeArray(input, Type = Uint16Array) {\n    if (typeof input != \"string\")\n        return input;\n    let array = null;\n    for (let pos = 0, out = 0; pos < input.length;) {\n        let value = 0;\n        for (;;) {\n            let next = input.charCodeAt(pos++), stop = false;\n            if (next == 126 /* Encode.BigValCode */) {\n                value = 65535 /* Encode.BigVal */;\n                break;\n            }\n            if (next >= 92 /* Encode.Gap2 */)\n                next--;\n            if (next >= 34 /* Encode.Gap1 */)\n                next--;\n            let digit = next - 32 /* Encode.Start */;\n            if (digit >= 46 /* Encode.Base */) {\n                digit -= 46 /* Encode.Base */;\n                stop = true;\n            }\n            value += digit;\n            if (stop)\n                break;\n            value *= 46 /* Encode.Base */;\n        }\n        if (array)\n            array[out++] = value;\n        else\n            array = new Type(value);\n    }\n    return array;\n}\n\nclass CachedToken {\n    constructor() {\n        this.start = -1;\n        this.value = -1;\n        this.end = -1;\n        this.extended = -1;\n        this.lookAhead = 0;\n        this.mask = 0;\n        this.context = 0;\n    }\n}\nconst nullToken = new CachedToken;\n/**\n[Tokenizers](#lr.ExternalTokenizer) interact with the input\nthrough this interface. It presents the input as a stream of\ncharacters, tracking lookahead and hiding the complexity of\n[ranges](#common.Parser.parse^ranges) from tokenizer code.\n*/\nclass InputStream {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    input, \n    /**\n    @internal\n    */\n    ranges) {\n        this.input = input;\n        this.ranges = ranges;\n        /**\n        @internal\n        */\n        this.chunk = \"\";\n        /**\n        @internal\n        */\n        this.chunkOff = 0;\n        /**\n        Backup chunk\n        */\n        this.chunk2 = \"\";\n        this.chunk2Pos = 0;\n        /**\n        The character code of the next code unit in the input, or -1\n        when the stream is at the end of the input.\n        */\n        this.next = -1;\n        /**\n        @internal\n        */\n        this.token = nullToken;\n        this.rangeIndex = 0;\n        this.pos = this.chunkPos = ranges[0].from;\n        this.range = ranges[0];\n        this.end = ranges[ranges.length - 1].to;\n        this.readNext();\n    }\n    /**\n    @internal\n    */\n    resolveOffset(offset, assoc) {\n        let range = this.range, index = this.rangeIndex;\n        let pos = this.pos + offset;\n        while (pos < range.from) {\n            if (!index)\n                return null;\n            let next = this.ranges[--index];\n            pos -= range.from - next.to;\n            range = next;\n        }\n        while (assoc < 0 ? pos > range.to : pos >= range.to) {\n            if (index == this.ranges.length - 1)\n                return null;\n            let next = this.ranges[++index];\n            pos += next.from - range.to;\n            range = next;\n        }\n        return pos;\n    }\n    /**\n    @internal\n    */\n    clipPos(pos) {\n        if (pos >= this.range.from && pos < this.range.to)\n            return pos;\n        for (let range of this.ranges)\n            if (range.to > pos)\n                return Math.max(pos, range.from);\n        return this.end;\n    }\n    /**\n    Look at a code unit near the stream position. `.peek(0)` equals\n    `.next`, `.peek(-1)` gives you the previous character, and so\n    on.\n    \n    Note that looking around during tokenizing creates dependencies\n    on potentially far-away content, which may reduce the\n    effectiveness incremental parsing—when looking forward—or even\n    cause invalid reparses when looking backward more than 25 code\n    units, since the library does not track lookbehind.\n    */\n    peek(offset) {\n        let idx = this.chunkOff + offset, pos, result;\n        if (idx >= 0 && idx < this.chunk.length) {\n            pos = this.pos + offset;\n            result = this.chunk.charCodeAt(idx);\n        }\n        else {\n            let resolved = this.resolveOffset(offset, 1);\n            if (resolved == null)\n                return -1;\n            pos = resolved;\n            if (pos >= this.chunk2Pos && pos < this.chunk2Pos + this.chunk2.length) {\n                result = this.chunk2.charCodeAt(pos - this.chunk2Pos);\n            }\n            else {\n                let i = this.rangeIndex, range = this.range;\n                while (range.to <= pos)\n                    range = this.ranges[++i];\n                this.chunk2 = this.input.chunk(this.chunk2Pos = pos);\n                if (pos + this.chunk2.length > range.to)\n                    this.chunk2 = this.chunk2.slice(0, range.to - pos);\n                result = this.chunk2.charCodeAt(0);\n            }\n        }\n        if (pos >= this.token.lookAhead)\n            this.token.lookAhead = pos + 1;\n        return result;\n    }\n    /**\n    Accept a token. By default, the end of the token is set to the\n    current stream position, but you can pass an offset (relative to\n    the stream position) to change that.\n    */\n    acceptToken(token, endOffset = 0) {\n        let end = endOffset ? this.resolveOffset(endOffset, -1) : this.pos;\n        if (end == null || end < this.token.start)\n            throw new RangeError(\"Token end out of bounds\");\n        this.token.value = token;\n        this.token.end = end;\n    }\n    /**\n    Accept a token ending at a specific given position.\n    */\n    acceptTokenTo(token, endPos) {\n        this.token.value = token;\n        this.token.end = endPos;\n    }\n    getChunk() {\n        if (this.pos >= this.chunk2Pos && this.pos < this.chunk2Pos + this.chunk2.length) {\n            let { chunk, chunkPos } = this;\n            this.chunk = this.chunk2;\n            this.chunkPos = this.chunk2Pos;\n            this.chunk2 = chunk;\n            this.chunk2Pos = chunkPos;\n            this.chunkOff = this.pos - this.chunkPos;\n        }\n        else {\n            this.chunk2 = this.chunk;\n            this.chunk2Pos = this.chunkPos;\n            let nextChunk = this.input.chunk(this.pos);\n            let end = this.pos + nextChunk.length;\n            this.chunk = end > this.range.to ? nextChunk.slice(0, this.range.to - this.pos) : nextChunk;\n            this.chunkPos = this.pos;\n            this.chunkOff = 0;\n        }\n    }\n    readNext() {\n        if (this.chunkOff >= this.chunk.length) {\n            this.getChunk();\n            if (this.chunkOff == this.chunk.length)\n                return this.next = -1;\n        }\n        return this.next = this.chunk.charCodeAt(this.chunkOff);\n    }\n    /**\n    Move the stream forward N (defaults to 1) code units. Returns\n    the new value of [`next`](#lr.InputStream.next).\n    */\n    advance(n = 1) {\n        this.chunkOff += n;\n        while (this.pos + n >= this.range.to) {\n            if (this.rangeIndex == this.ranges.length - 1)\n                return this.setDone();\n            n -= this.range.to - this.pos;\n            this.range = this.ranges[++this.rangeIndex];\n            this.pos = this.range.from;\n        }\n        this.pos += n;\n        if (this.pos >= this.token.lookAhead)\n            this.token.lookAhead = this.pos + 1;\n        return this.readNext();\n    }\n    setDone() {\n        this.pos = this.chunkPos = this.end;\n        this.range = this.ranges[this.rangeIndex = this.ranges.length - 1];\n        this.chunk = \"\";\n        return this.next = -1;\n    }\n    /**\n    @internal\n    */\n    reset(pos, token) {\n        if (token) {\n            this.token = token;\n            token.start = pos;\n            token.lookAhead = pos + 1;\n            token.value = token.extended = -1;\n        }\n        else {\n            this.token = nullToken;\n        }\n        if (this.pos != pos) {\n            this.pos = pos;\n            if (pos == this.end) {\n                this.setDone();\n                return this;\n            }\n            while (pos < this.range.from)\n                this.range = this.ranges[--this.rangeIndex];\n            while (pos >= this.range.to)\n                this.range = this.ranges[++this.rangeIndex];\n            if (pos >= this.chunkPos && pos < this.chunkPos + this.chunk.length) {\n                this.chunkOff = pos - this.chunkPos;\n            }\n            else {\n                this.chunk = \"\";\n                this.chunkOff = 0;\n            }\n            this.readNext();\n        }\n        return this;\n    }\n    /**\n    @internal\n    */\n    read(from, to) {\n        if (from >= this.chunkPos && to <= this.chunkPos + this.chunk.length)\n            return this.chunk.slice(from - this.chunkPos, to - this.chunkPos);\n        if (from >= this.chunk2Pos && to <= this.chunk2Pos + this.chunk2.length)\n            return this.chunk2.slice(from - this.chunk2Pos, to - this.chunk2Pos);\n        if (from >= this.range.from && to <= this.range.to)\n            return this.input.read(from, to);\n        let result = \"\";\n        for (let r of this.ranges) {\n            if (r.from >= to)\n                break;\n            if (r.to > from)\n                result += this.input.read(Math.max(r.from, from), Math.min(r.to, to));\n        }\n        return result;\n    }\n}\n/**\n@internal\n*/\nclass TokenGroup {\n    constructor(data, id) {\n        this.data = data;\n        this.id = id;\n    }\n    token(input, stack) {\n        let { parser } = stack.p;\n        readToken(this.data, input, stack, this.id, parser.data, parser.tokenPrecTable);\n    }\n}\nTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n@hide\n*/\nclass LocalTokenGroup {\n    constructor(data, precTable, elseToken) {\n        this.precTable = precTable;\n        this.elseToken = elseToken;\n        this.data = typeof data == \"string\" ? decodeArray(data) : data;\n    }\n    token(input, stack) {\n        let start = input.pos, skipped = 0;\n        for (;;) {\n            let atEof = input.next < 0, nextPos = input.resolveOffset(1, 1);\n            readToken(this.data, input, stack, 0, this.data, this.precTable);\n            if (input.token.value > -1)\n                break;\n            if (this.elseToken == null)\n                return;\n            if (!atEof)\n                skipped++;\n            if (nextPos == null)\n                break;\n            input.reset(nextPos, input.token);\n        }\n        if (skipped) {\n            input.reset(start, input.token);\n            input.acceptToken(this.elseToken, skipped);\n        }\n    }\n}\nLocalTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n`@external tokens` declarations in the grammar should resolve to\nan instance of this class.\n*/\nclass ExternalTokenizer {\n    /**\n    Create a tokenizer. The first argument is the function that,\n    given an input stream, scans for the types of tokens it\n    recognizes at the stream's position, and calls\n    [`acceptToken`](#lr.InputStream.acceptToken) when it finds\n    one.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    token, options = {}) {\n        this.token = token;\n        this.contextual = !!options.contextual;\n        this.fallback = !!options.fallback;\n        this.extend = !!options.extend;\n    }\n}\n// Tokenizer data is stored a big uint16 array containing, for each\n// state:\n//\n//  - A group bitmask, indicating what token groups are reachable from\n//    this state, so that paths that can only lead to tokens not in\n//    any of the current groups can be cut off early.\n//\n//  - The position of the end of the state's sequence of accepting\n//    tokens\n//\n//  - The number of outgoing edges for the state\n//\n//  - The accepting tokens, as (token id, group mask) pairs\n//\n//  - The outgoing edges, as (start character, end character, state\n//    index) triples, with end character being exclusive\n//\n// This function interprets that data, running through a stream as\n// long as new states with the a matching group mask can be reached,\n// and updating `input.token` when it matches a token.\nfunction readToken(data, input, stack, group, precTable, precOffset) {\n    let state = 0, groupMask = 1 << group, { dialect } = stack.p.parser;\n    scan: for (;;) {\n        if ((groupMask & data[state]) == 0)\n            break;\n        let accEnd = data[state + 1];\n        // Check whether this state can lead to a token in the current group\n        // Accept tokens in this state, possibly overwriting\n        // lower-precedence / shorter tokens\n        for (let i = state + 3; i < accEnd; i += 2)\n            if ((data[i + 1] & groupMask) > 0) {\n                let term = data[i];\n                if (dialect.allows(term) &&\n                    (input.token.value == -1 || input.token.value == term ||\n                        overrides(term, input.token.value, precTable, precOffset))) {\n                    input.acceptToken(term);\n                    break;\n                }\n            }\n        let next = input.next, low = 0, high = data[state + 2];\n        // Special case for EOF\n        if (input.next < 0 && high > low && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */) {\n            state = data[accEnd + high * 3 - 1];\n            continue scan;\n        }\n        // Do a binary search on the state's edges\n        for (; low < high;) {\n            let mid = (low + high) >> 1;\n            let index = accEnd + mid + (mid << 1);\n            let from = data[index], to = data[index + 1] || 0x10000;\n            if (next < from)\n                high = mid;\n            else if (next >= to)\n                low = mid + 1;\n            else {\n                state = data[index + 2];\n                input.advance();\n                continue scan;\n            }\n        }\n        break;\n    }\n}\nfunction findOffset(data, start, term) {\n    for (let i = start, next; (next = data[i]) != 65535 /* Seq.End */; i++)\n        if (next == term)\n            return i - start;\n    return -1;\n}\nfunction overrides(token, prev, tableData, tableOffset) {\n    let iPrev = findOffset(tableData, tableOffset, prev);\n    return iPrev < 0 || findOffset(tableData, tableOffset, token) < iPrev;\n}\n\n// Environment variable used to control console output\nconst verbose = typeof process != \"undefined\" && process.env && /\\bparse\\b/.test(process.env.LOG);\nlet stackIDs = null;\nfunction cutAt(tree, pos, side) {\n    let cursor = tree.cursor(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.IterMode.IncludeAnonymous);\n    cursor.moveTo(pos);\n    for (;;) {\n        if (!(side < 0 ? cursor.childBefore(pos) : cursor.childAfter(pos)))\n            for (;;) {\n                if ((side < 0 ? cursor.to < pos : cursor.from > pos) && !cursor.type.isError)\n                    return side < 0 ? Math.max(0, Math.min(cursor.to - 1, pos - 25 /* Lookahead.Margin */))\n                        : Math.min(tree.length, Math.max(cursor.from + 1, pos + 25 /* Lookahead.Margin */));\n                if (side < 0 ? cursor.prevSibling() : cursor.nextSibling())\n                    break;\n                if (!cursor.parent())\n                    return side < 0 ? 0 : tree.length;\n            }\n    }\n}\nclass FragmentCursor {\n    constructor(fragments, nodeSet) {\n        this.fragments = fragments;\n        this.nodeSet = nodeSet;\n        this.i = 0;\n        this.fragment = null;\n        this.safeFrom = -1;\n        this.safeTo = -1;\n        this.trees = [];\n        this.start = [];\n        this.index = [];\n        this.nextFragment();\n    }\n    nextFragment() {\n        let fr = this.fragment = this.i == this.fragments.length ? null : this.fragments[this.i++];\n        if (fr) {\n            this.safeFrom = fr.openStart ? cutAt(fr.tree, fr.from + fr.offset, 1) - fr.offset : fr.from;\n            this.safeTo = fr.openEnd ? cutAt(fr.tree, fr.to + fr.offset, -1) - fr.offset : fr.to;\n            while (this.trees.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n            }\n            this.trees.push(fr.tree);\n            this.start.push(-fr.offset);\n            this.index.push(0);\n            this.nextStart = this.safeFrom;\n        }\n        else {\n            this.nextStart = 1e9;\n        }\n    }\n    // `pos` must be >= any previously given `pos` for this cursor\n    nodeAt(pos) {\n        if (pos < this.nextStart)\n            return null;\n        while (this.fragment && this.safeTo <= pos)\n            this.nextFragment();\n        if (!this.fragment)\n            return null;\n        for (;;) {\n            let last = this.trees.length - 1;\n            if (last < 0) { // End of tree\n                this.nextFragment();\n                return null;\n            }\n            let top = this.trees[last], index = this.index[last];\n            if (index == top.children.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n                continue;\n            }\n            let next = top.children[index];\n            let start = this.start[last] + top.positions[index];\n            if (start > pos) {\n                this.nextStart = start;\n                return null;\n            }\n            if (next instanceof _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree) {\n                if (start == pos) {\n                    if (start < this.safeFrom)\n                        return null;\n                    let end = start + next.length;\n                    if (end <= this.safeTo) {\n                        let lookAhead = next.prop(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.lookAhead);\n                        if (!lookAhead || end + lookAhead < this.fragment.to)\n                            return next;\n                    }\n                }\n                this.index[last]++;\n                if (start + next.length >= Math.max(this.safeFrom, pos)) { // Enter this node\n                    this.trees.push(next);\n                    this.start.push(start);\n                    this.index.push(0);\n                }\n            }\n            else {\n                this.index[last]++;\n                this.nextStart = start + next.length;\n            }\n        }\n    }\n}\nclass TokenCache {\n    constructor(parser, stream) {\n        this.stream = stream;\n        this.tokens = [];\n        this.mainToken = null;\n        this.actions = [];\n        this.tokens = parser.tokenizers.map(_ => new CachedToken);\n    }\n    getActions(stack) {\n        let actionIndex = 0;\n        let main = null;\n        let { parser } = stack.p, { tokenizers } = parser;\n        let mask = parser.stateSlot(stack.state, 3 /* ParseState.TokenizerMask */);\n        let context = stack.curContext ? stack.curContext.hash : 0;\n        let lookAhead = 0;\n        for (let i = 0; i < tokenizers.length; i++) {\n            if (((1 << i) & mask) == 0)\n                continue;\n            let tokenizer = tokenizers[i], token = this.tokens[i];\n            if (main && !tokenizer.fallback)\n                continue;\n            if (tokenizer.contextual || token.start != stack.pos || token.mask != mask || token.context != context) {\n                this.updateCachedToken(token, tokenizer, stack);\n                token.mask = mask;\n                token.context = context;\n            }\n            if (token.lookAhead > token.end + 25 /* Lookahead.Margin */)\n                lookAhead = Math.max(token.lookAhead, lookAhead);\n            if (token.value != 0 /* Term.Err */) {\n                let startIndex = actionIndex;\n                if (token.extended > -1)\n                    actionIndex = this.addActions(stack, token.extended, token.end, actionIndex);\n                actionIndex = this.addActions(stack, token.value, token.end, actionIndex);\n                if (!tokenizer.extend) {\n                    main = token;\n                    if (actionIndex > startIndex)\n                        break;\n                }\n            }\n        }\n        while (this.actions.length > actionIndex)\n            this.actions.pop();\n        if (lookAhead)\n            stack.setLookAhead(lookAhead);\n        if (!main && stack.pos == this.stream.end) {\n            main = new CachedToken;\n            main.value = stack.p.parser.eofTerm;\n            main.start = main.end = stack.pos;\n            actionIndex = this.addActions(stack, main.value, main.end, actionIndex);\n        }\n        this.mainToken = main;\n        return this.actions;\n    }\n    getMainToken(stack) {\n        if (this.mainToken)\n            return this.mainToken;\n        let main = new CachedToken, { pos, p } = stack;\n        main.start = pos;\n        main.end = Math.min(pos + 1, p.stream.end);\n        main.value = pos == p.stream.end ? p.parser.eofTerm : 0 /* Term.Err */;\n        return main;\n    }\n    updateCachedToken(token, tokenizer, stack) {\n        let start = this.stream.clipPos(stack.pos);\n        tokenizer.token(this.stream.reset(start, token), stack);\n        if (token.value > -1) {\n            let { parser } = stack.p;\n            for (let i = 0; i < parser.specialized.length; i++)\n                if (parser.specialized[i] == token.value) {\n                    let result = parser.specializers[i](this.stream.read(token.start, token.end), stack);\n                    if (result >= 0 && stack.p.parser.dialect.allows(result >> 1)) {\n                        if ((result & 1) == 0 /* Specialize.Specialize */)\n                            token.value = result >> 1;\n                        else\n                            token.extended = result >> 1;\n                        break;\n                    }\n                }\n        }\n        else {\n            token.value = 0 /* Term.Err */;\n            token.end = this.stream.clipPos(start + 1);\n        }\n    }\n    putAction(action, token, end, index) {\n        // Don't add duplicate actions\n        for (let i = 0; i < index; i += 3)\n            if (this.actions[i] == action)\n                return index;\n        this.actions[index++] = action;\n        this.actions[index++] = token;\n        this.actions[index++] = end;\n        return index;\n    }\n    addActions(stack, token, end, index) {\n        let { state } = stack, { parser } = stack.p, { data } = parser;\n        for (let set = 0; set < 2; set++) {\n            for (let i = parser.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */);; i += 3) {\n                if (data[i] == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */) {\n                        i = pair(data, i + 2);\n                    }\n                    else {\n                        if (index == 0 && data[i + 1] == 2 /* Seq.Other */)\n                            index = this.putAction(pair(data, i + 2), token, end, index);\n                        break;\n                    }\n                }\n                if (data[i] == token)\n                    index = this.putAction(pair(data, i + 1), token, end, index);\n            }\n        }\n        return index;\n    }\n}\nclass Parse {\n    constructor(parser, input, fragments, ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.recovering = 0;\n        this.nextStackID = 0x2654; // ♔, ♕, ♖, ♗, ♘, ♙, ♠, ♡, ♢, ♣, ♤, ♥, ♦, ♧\n        this.minStackPos = 0;\n        this.reused = [];\n        this.stoppedAt = null;\n        this.lastBigReductionStart = -1;\n        this.lastBigReductionSize = 0;\n        this.bigReductionCount = 0;\n        this.stream = new InputStream(input, ranges);\n        this.tokens = new TokenCache(parser, this.stream);\n        this.topTerm = parser.top[1];\n        let { from } = ranges[0];\n        this.stacks = [Stack.start(this, parser.top[0], from)];\n        this.fragments = fragments.length && this.stream.end - from > parser.bufferLength * 4\n            ? new FragmentCursor(fragments, parser.nodeSet) : null;\n    }\n    get parsedPos() {\n        return this.minStackPos;\n    }\n    // Move the parser forward. This will process all parse stacks at\n    // `this.pos` and try to advance them to a further position. If no\n    // stack for such a position is found, it'll start error-recovery.\n    //\n    // When the parse is finished, this will return a syntax tree. When\n    // not, it returns `null`.\n    advance() {\n        let stacks = this.stacks, pos = this.minStackPos;\n        // This will hold stacks beyond `pos`.\n        let newStacks = this.stacks = [];\n        let stopped, stoppedTokens;\n        // If a large amount of reductions happened with the same start\n        // position, force the stack out of that production in order to\n        // avoid creating a tree too deep to recurse through.\n        // (This is an ugly kludge, because unfortunately there is no\n        // straightforward, cheap way to check for this happening, due to\n        // the history of reductions only being available in an\n        // expensive-to-access format in the stack buffers.)\n        if (this.bigReductionCount > 300 /* Rec.MaxLeftAssociativeReductionCount */ && stacks.length == 1) {\n            let [s] = stacks;\n            while (s.forceReduce() && s.stack.length && s.stack[s.stack.length - 2] >= this.lastBigReductionStart) { }\n            this.bigReductionCount = this.lastBigReductionSize = 0;\n        }\n        // Keep advancing any stacks at `pos` until they either move\n        // forward or can't be advanced. Gather stacks that can't be\n        // advanced further in `stopped`.\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i];\n            for (;;) {\n                this.tokens.mainToken = null;\n                if (stack.pos > pos) {\n                    newStacks.push(stack);\n                }\n                else if (this.advanceStack(stack, newStacks, stacks)) {\n                    continue;\n                }\n                else {\n                    if (!stopped) {\n                        stopped = [];\n                        stoppedTokens = [];\n                    }\n                    stopped.push(stack);\n                    let tok = this.tokens.getMainToken(stack);\n                    stoppedTokens.push(tok.value, tok.end);\n                }\n                break;\n            }\n        }\n        if (!newStacks.length) {\n            let finished = stopped && findFinished(stopped);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Finish with \" + this.stackID(finished));\n                return this.stackToTree(finished);\n            }\n            if (this.parser.strict) {\n                if (verbose && stopped)\n                    console.log(\"Stuck with token \" + (this.tokens.mainToken ? this.parser.getName(this.tokens.mainToken.value) : \"none\"));\n                throw new SyntaxError(\"No parse at \" + pos);\n            }\n            if (!this.recovering)\n                this.recovering = 5 /* Rec.Distance */;\n        }\n        if (this.recovering && stopped) {\n            let finished = this.stoppedAt != null && stopped[0].pos > this.stoppedAt ? stopped[0]\n                : this.runRecovery(stopped, stoppedTokens, newStacks);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Force-finish \" + this.stackID(finished));\n                return this.stackToTree(finished.forceAll());\n            }\n        }\n        if (this.recovering) {\n            let maxRemaining = this.recovering == 1 ? 1 : this.recovering * 3 /* Rec.MaxRemainingPerStep */;\n            if (newStacks.length > maxRemaining) {\n                newStacks.sort((a, b) => b.score - a.score);\n                while (newStacks.length > maxRemaining)\n                    newStacks.pop();\n            }\n            if (newStacks.some(s => s.reducePos > pos))\n                this.recovering--;\n        }\n        else if (newStacks.length > 1) {\n            // Prune stacks that are in the same state, or that have been\n            // running without splitting for a while, to avoid getting stuck\n            // with multiple successful stacks running endlessly on.\n            outer: for (let i = 0; i < newStacks.length - 1; i++) {\n                let stack = newStacks[i];\n                for (let j = i + 1; j < newStacks.length; j++) {\n                    let other = newStacks[j];\n                    if (stack.sameState(other) ||\n                        stack.buffer.length > 500 /* Rec.MinBufferLengthPrune */ && other.buffer.length > 500 /* Rec.MinBufferLengthPrune */) {\n                        if (((stack.score - other.score) || (stack.buffer.length - other.buffer.length)) > 0) {\n                            newStacks.splice(j--, 1);\n                        }\n                        else {\n                            newStacks.splice(i--, 1);\n                            continue outer;\n                        }\n                    }\n                }\n            }\n            if (newStacks.length > 12 /* Rec.MaxStackCount */)\n                newStacks.splice(12 /* Rec.MaxStackCount */, newStacks.length - 12 /* Rec.MaxStackCount */);\n        }\n        this.minStackPos = newStacks[0].pos;\n        for (let i = 1; i < newStacks.length; i++)\n            if (newStacks[i].pos < this.minStackPos)\n                this.minStackPos = newStacks[i].pos;\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    // Returns an updated version of the given stack, or null if the\n    // stack can't advance normally. When `split` and `stacks` are\n    // given, stacks split off by ambiguous operations will be pushed to\n    // `split`, or added to `stacks` if they move `pos` forward.\n    advanceStack(stack, stacks, split) {\n        let start = stack.pos, { parser } = this;\n        let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n        if (this.stoppedAt != null && start > this.stoppedAt)\n            return stack.forceReduce() ? stack : null;\n        if (this.fragments) {\n            let strictCx = stack.curContext && stack.curContext.tracker.strict, cxHash = strictCx ? stack.curContext.hash : 0;\n            for (let cached = this.fragments.nodeAt(start); cached;) {\n                let match = this.parser.nodeSet.types[cached.type.id] == cached.type ? parser.getGoto(stack.state, cached.type.id) : -1;\n                if (match > -1 && cached.length && (!strictCx || (cached.prop(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.contextHash) || 0) == cxHash)) {\n                    stack.useNode(cached, match);\n                    if (verbose)\n                        console.log(base + this.stackID(stack) + ` (via reuse of ${parser.getName(cached.type.id)})`);\n                    return true;\n                }\n                if (!(cached instanceof _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree) || cached.children.length == 0 || cached.positions[0] > 0)\n                    break;\n                let inner = cached.children[0];\n                if (inner instanceof _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree && cached.positions[0] == 0)\n                    cached = inner;\n                else\n                    break;\n            }\n        }\n        let defaultReduce = parser.stateSlot(stack.state, 4 /* ParseState.DefaultReduce */);\n        if (defaultReduce > 0) {\n            stack.reduce(defaultReduce);\n            if (verbose)\n                console.log(base + this.stackID(stack) + ` (via always-reduce ${parser.getName(defaultReduce & 65535 /* Action.ValueMask */)})`);\n            return true;\n        }\n        if (stack.stack.length >= 8400 /* Rec.CutDepth */) {\n            while (stack.stack.length > 6000 /* Rec.CutTo */ && stack.forceReduce()) { }\n        }\n        let actions = this.tokens.getActions(stack);\n        for (let i = 0; i < actions.length;) {\n            let action = actions[i++], term = actions[i++], end = actions[i++];\n            let last = i == actions.length || !split;\n            let localStack = last ? stack : stack.split();\n            let main = this.tokens.mainToken;\n            localStack.apply(action, term, main ? main.start : localStack.pos, end);\n            if (verbose)\n                console.log(base + this.stackID(localStack) + ` (via ${(action & 65536 /* Action.ReduceFlag */) == 0 ? \"shift\"\n                    : `reduce of ${parser.getName(action & 65535 /* Action.ValueMask */)}`} for ${parser.getName(term)} @ ${start}${localStack == stack ? \"\" : \", split\"})`);\n            if (last)\n                return true;\n            else if (localStack.pos > start)\n                stacks.push(localStack);\n            else\n                split.push(localStack);\n        }\n        return false;\n    }\n    // Advance a given stack forward as far as it will go. Returns the\n    // (possibly updated) stack if it got stuck, or null if it moved\n    // forward and was given to `pushStackDedup`.\n    advanceFully(stack, newStacks) {\n        let pos = stack.pos;\n        for (;;) {\n            if (!this.advanceStack(stack, null, null))\n                return false;\n            if (stack.pos > pos) {\n                pushStackDedup(stack, newStacks);\n                return true;\n            }\n        }\n    }\n    runRecovery(stacks, tokens, newStacks) {\n        let finished = null, restarted = false;\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i], token = tokens[i << 1], tokenEnd = tokens[(i << 1) + 1];\n            let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n            if (stack.deadEnd) {\n                if (restarted)\n                    continue;\n                restarted = true;\n                stack.restart();\n                if (verbose)\n                    console.log(base + this.stackID(stack) + \" (restarted)\");\n                let done = this.advanceFully(stack, newStacks);\n                if (done)\n                    continue;\n            }\n            let force = stack.split(), forceBase = base;\n            for (let j = 0; force.forceReduce() && j < 10 /* Rec.ForceReduceLimit */; j++) {\n                if (verbose)\n                    console.log(forceBase + this.stackID(force) + \" (via force-reduce)\");\n                let done = this.advanceFully(force, newStacks);\n                if (done)\n                    break;\n                if (verbose)\n                    forceBase = this.stackID(force) + \" -> \";\n            }\n            for (let insert of stack.recoverByInsert(token)) {\n                if (verbose)\n                    console.log(base + this.stackID(insert) + \" (via recover-insert)\");\n                this.advanceFully(insert, newStacks);\n            }\n            if (this.stream.end > stack.pos) {\n                if (tokenEnd == stack.pos) {\n                    tokenEnd++;\n                    token = 0 /* Term.Err */;\n                }\n                stack.recoverByDelete(token, tokenEnd);\n                if (verbose)\n                    console.log(base + this.stackID(stack) + ` (via recover-delete ${this.parser.getName(token)})`);\n                pushStackDedup(stack, newStacks);\n            }\n            else if (!finished || finished.score < stack.score) {\n                finished = stack;\n            }\n        }\n        return finished;\n    }\n    // Convert the stack's buffer to a syntax tree.\n    stackToTree(stack) {\n        stack.close();\n        return _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree.build({ buffer: StackBufferCursor.create(stack),\n            nodeSet: this.parser.nodeSet,\n            topID: this.topTerm,\n            maxBufferLength: this.parser.bufferLength,\n            reused: this.reused,\n            start: this.ranges[0].from,\n            length: stack.pos - this.ranges[0].from,\n            minRepeatType: this.parser.minRepeatTerm });\n    }\n    stackID(stack) {\n        let id = (stackIDs || (stackIDs = new WeakMap)).get(stack);\n        if (!id)\n            stackIDs.set(stack, id = String.fromCodePoint(this.nextStackID++));\n        return id + stack;\n    }\n}\nfunction pushStackDedup(stack, newStacks) {\n    for (let i = 0; i < newStacks.length; i++) {\n        let other = newStacks[i];\n        if (other.pos == stack.pos && other.sameState(stack)) {\n            if (newStacks[i].score < stack.score)\n                newStacks[i] = stack;\n            return;\n        }\n    }\n    newStacks.push(stack);\n}\nclass Dialect {\n    constructor(source, flags, disabled) {\n        this.source = source;\n        this.flags = flags;\n        this.disabled = disabled;\n    }\n    allows(term) { return !this.disabled || this.disabled[term] == 0; }\n}\nconst id = x => x;\n/**\nContext trackers are used to track stateful context (such as\nindentation in the Python grammar, or parent elements in the XML\ngrammar) needed by external tokenizers. You declare them in a\ngrammar file as `@context exportName from \"module\"`.\n\nContext values should be immutable, and can be updated (replaced)\non shift or reduce actions.\n\nThe export used in a `@context` declaration should be of this\ntype.\n*/\nclass ContextTracker {\n    /**\n    Define a context tracker.\n    */\n    constructor(spec) {\n        this.start = spec.start;\n        this.shift = spec.shift || id;\n        this.reduce = spec.reduce || id;\n        this.reuse = spec.reuse || id;\n        this.hash = spec.hash || (() => 0);\n        this.strict = spec.strict !== false;\n    }\n}\n/**\nHolds the parse tables for a given grammar, as generated by\n`lezer-generator`, and provides [methods](#common.Parser) to parse\ncontent with.\n*/\nclass LRParser extends _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Parser {\n    /**\n    @internal\n    */\n    constructor(spec) {\n        super();\n        /**\n        @internal\n        */\n        this.wrappers = [];\n        if (spec.version != 14 /* File.Version */)\n            throw new RangeError(`Parser version (${spec.version}) doesn't match runtime version (${14 /* File.Version */})`);\n        let nodeNames = spec.nodeNames.split(\" \");\n        this.minRepeatTerm = nodeNames.length;\n        for (let i = 0; i < spec.repeatNodeCount; i++)\n            nodeNames.push(\"\");\n        let topTerms = Object.keys(spec.topRules).map(r => spec.topRules[r][1]);\n        let nodeProps = [];\n        for (let i = 0; i < nodeNames.length; i++)\n            nodeProps.push([]);\n        function setProp(nodeID, prop, value) {\n            nodeProps[nodeID].push([prop, prop.deserialize(String(value))]);\n        }\n        if (spec.nodeProps)\n            for (let propSpec of spec.nodeProps) {\n                let prop = propSpec[0];\n                if (typeof prop == \"string\")\n                    prop = _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp[prop];\n                for (let i = 1; i < propSpec.length;) {\n                    let next = propSpec[i++];\n                    if (next >= 0) {\n                        setProp(next, prop, propSpec[i++]);\n                    }\n                    else {\n                        let value = propSpec[i + -next];\n                        for (let j = -next; j > 0; j--)\n                            setProp(propSpec[i++], prop, value);\n                        i++;\n                    }\n                }\n            }\n        this.nodeSet = new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeSet(nodeNames.map((name, i) => _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeType.define({\n            name: i >= this.minRepeatTerm ? undefined : name,\n            id: i,\n            props: nodeProps[i],\n            top: topTerms.indexOf(i) > -1,\n            error: i == 0,\n            skipped: spec.skippedNodes && spec.skippedNodes.indexOf(i) > -1\n        })));\n        if (spec.propSources)\n            this.nodeSet = this.nodeSet.extend(...spec.propSources);\n        this.strict = false;\n        this.bufferLength = _lezer_common__WEBPACK_IMPORTED_MODULE_0__.DefaultBufferLength;\n        let tokenArray = decodeArray(spec.tokenData);\n        this.context = spec.context;\n        this.specializerSpecs = spec.specialized || [];\n        this.specialized = new Uint16Array(this.specializerSpecs.length);\n        for (let i = 0; i < this.specializerSpecs.length; i++)\n            this.specialized[i] = this.specializerSpecs[i].term;\n        this.specializers = this.specializerSpecs.map(getSpecializer);\n        this.states = decodeArray(spec.states, Uint32Array);\n        this.data = decodeArray(spec.stateData);\n        this.goto = decodeArray(spec.goto);\n        this.maxTerm = spec.maxTerm;\n        this.tokenizers = spec.tokenizers.map(value => typeof value == \"number\" ? new TokenGroup(tokenArray, value) : value);\n        this.topRules = spec.topRules;\n        this.dialects = spec.dialects || {};\n        this.dynamicPrecedences = spec.dynamicPrecedences || null;\n        this.tokenPrecTable = spec.tokenPrec;\n        this.termNames = spec.termNames || null;\n        this.maxNode = this.nodeSet.types.length - 1;\n        this.dialect = this.parseDialect();\n        this.top = this.topRules[Object.keys(this.topRules)[0]];\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new Parse(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /**\n    Get a goto table entry @internal\n    */\n    getGoto(state, term, loose = false) {\n        let table = this.goto;\n        if (term >= table[0])\n            return -1;\n        for (let pos = table[term + 1];;) {\n            let groupTag = table[pos++], last = groupTag & 1;\n            let target = table[pos++];\n            if (last && loose)\n                return target;\n            for (let end = pos + (groupTag >> 1); pos < end; pos++)\n                if (table[pos] == state)\n                    return target;\n            if (last)\n                return -1;\n        }\n    }\n    /**\n    Check if this state has an action for a given terminal @internal\n    */\n    hasAction(state, terminal) {\n        let data = this.data;\n        for (let set = 0; set < 2; set++) {\n            for (let i = this.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */), next;; i += 3) {\n                if ((next = data[i]) == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */)\n                        next = data[i = pair(data, i + 2)];\n                    else if (data[i + 1] == 2 /* Seq.Other */)\n                        return pair(data, i + 2);\n                    else\n                        break;\n                }\n                if (next == terminal || next == 0 /* Term.Err */)\n                    return pair(data, i + 1);\n            }\n        }\n        return 0;\n    }\n    /**\n    @internal\n    */\n    stateSlot(state, slot) {\n        return this.states[(state * 6 /* ParseState.Size */) + slot];\n    }\n    /**\n    @internal\n    */\n    stateFlag(state, flag) {\n        return (this.stateSlot(state, 0 /* ParseState.Flags */) & flag) > 0;\n    }\n    /**\n    @internal\n    */\n    validAction(state, action) {\n        return !!this.allActions(state, a => a == action ? true : null);\n    }\n    /**\n    @internal\n    */\n    allActions(state, action) {\n        let deflt = this.stateSlot(state, 4 /* ParseState.DefaultReduce */);\n        let result = deflt ? action(deflt) : undefined;\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */); result == null; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            result = action(pair(this.data, i + 1));\n        }\n        return result;\n    }\n    /**\n    Get the states that can follow this one through shift actions or\n    goto jumps. @internal\n    */\n    nextStates(state) {\n        let result = [];\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */);; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            if ((this.data[i + 2] & (65536 /* Action.ReduceFlag */ >> 16)) == 0) {\n                let value = this.data[i + 1];\n                if (!result.some((v, i) => (i & 1) && v == value))\n                    result.push(this.data[i], value);\n            }\n        }\n        return result;\n    }\n    /**\n    Configure the parser. Returns a new parser instance that has the\n    given settings modified. Settings not provided in `config` are\n    kept from the original parser.\n    */\n    configure(config) {\n        // Hideous reflection-based kludge to make it easy to create a\n        // slightly modified copy of a parser.\n        let copy = Object.assign(Object.create(LRParser.prototype), this);\n        if (config.props)\n            copy.nodeSet = this.nodeSet.extend(...config.props);\n        if (config.top) {\n            let info = this.topRules[config.top];\n            if (!info)\n                throw new RangeError(`Invalid top rule name ${config.top}`);\n            copy.top = info;\n        }\n        if (config.tokenizers)\n            copy.tokenizers = this.tokenizers.map(t => {\n                let found = config.tokenizers.find(r => r.from == t);\n                return found ? found.to : t;\n            });\n        if (config.specializers) {\n            copy.specializers = this.specializers.slice();\n            copy.specializerSpecs = this.specializerSpecs.map((s, i) => {\n                let found = config.specializers.find(r => r.from == s.external);\n                if (!found)\n                    return s;\n                let spec = Object.assign(Object.assign({}, s), { external: found.to });\n                copy.specializers[i] = getSpecializer(spec);\n                return spec;\n            });\n        }\n        if (config.contextTracker)\n            copy.context = config.contextTracker;\n        if (config.dialect)\n            copy.dialect = this.parseDialect(config.dialect);\n        if (config.strict != null)\n            copy.strict = config.strict;\n        if (config.wrap)\n            copy.wrappers = copy.wrappers.concat(config.wrap);\n        if (config.bufferLength != null)\n            copy.bufferLength = config.bufferLength;\n        return copy;\n    }\n    /**\n    Tells you whether any [parse wrappers](#lr.ParserConfig.wrap)\n    are registered for this parser.\n    */\n    hasWrappers() {\n        return this.wrappers.length > 0;\n    }\n    /**\n    Returns the name associated with a given term. This will only\n    work for all terms when the parser was generated with the\n    `--names` option. By default, only the names of tagged terms are\n    stored.\n    */\n    getName(term) {\n        return this.termNames ? this.termNames[term] : String(term <= this.maxNode && this.nodeSet.types[term].name || term);\n    }\n    /**\n    The eof term id is always allocated directly after the node\n    types. @internal\n    */\n    get eofTerm() { return this.maxNode + 1; }\n    /**\n    The type of top node produced by the parser.\n    */\n    get topNode() { return this.nodeSet.types[this.top[1]]; }\n    /**\n    @internal\n    */\n    dynamicPrecedence(term) {\n        let prec = this.dynamicPrecedences;\n        return prec == null ? 0 : prec[term] || 0;\n    }\n    /**\n    @internal\n    */\n    parseDialect(dialect) {\n        let values = Object.keys(this.dialects), flags = values.map(() => false);\n        if (dialect)\n            for (let part of dialect.split(\" \")) {\n                let id = values.indexOf(part);\n                if (id >= 0)\n                    flags[id] = true;\n            }\n        let disabled = null;\n        for (let i = 0; i < values.length; i++)\n            if (!flags[i]) {\n                for (let j = this.dialects[values[i]], id; (id = this.data[j++]) != 65535 /* Seq.End */;)\n                    (disabled || (disabled = new Uint8Array(this.maxTerm + 1)))[id] = 1;\n            }\n        return new Dialect(dialect, flags, disabled);\n    }\n    /**\n    Used by the output of the parser generator. Not available to\n    user code. @hide\n    */\n    static deserialize(spec) {\n        return new LRParser(spec);\n    }\n}\nfunction pair(data, off) { return data[off] | (data[off + 1] << 16); }\nfunction findFinished(stacks) {\n    let best = null;\n    for (let stack of stacks) {\n        let stopped = stack.p.stoppedAt;\n        if ((stack.pos == stack.p.stream.end || stopped != null && stack.pos > stopped) &&\n            stack.p.parser.stateFlag(stack.state, 2 /* StateFlag.Accepting */) &&\n            (!best || best.score < stack.score))\n            best = stack;\n    }\n    return best;\n}\nfunction getSpecializer(spec) {\n    if (spec.external) {\n        let mask = spec.extend ? 1 /* Specialize.Extend */ : 0 /* Specialize.Specialize */;\n        return (value, stack) => (spec.external(value, stack) << 1) | mask;\n    }\n    return spec.get;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@lezer+lr@1.4.2/node_modules/@lezer/lr/dist/index.js\n");

/***/ })

};
;