"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { MessageSquare, Users, Minimize2 } from 'lucide-react';

interface CallHeaderProps {
  callName: string;
  callDuration: number;
  participantsCount: number;
  toggleChat: () => void;
  toggleParticipants: () => void;
  onMinimize?: () => void;
}

export function CallHeader({
  callName,
  callDuration,
  participantsCount,
  toggleChat,
  toggleParticipants,
  onMinimize
}: CallHeaderProps) {
  // Format call duration as MM:SS
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <header className="border-b p-4 flex items-center justify-between">
      <div className="flex items-center gap-3">
        <h1 className="text-xl font-semibold">{callName}</h1>
        <div className="text-sm text-muted-foreground bg-muted px-2 py-1 rounded-md">
          {formatDuration(callDuration)}
        </div>
      </div>

      <div className="flex items-center gap-2">
        {onMinimize && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onMinimize}
            className="h-8 w-8 p-0"
          >
            <Minimize2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    </header>
  );
}