import asyncio
import time
from typing import Optional, Dict, Any
from uuid import uuid4
from datetime import datetime
from agentpress.tool import <PERSON><PERSON><PERSON><PERSON><PERSON>, openapi_schema, xml_schema
from sandbox.sandbox import SandboxToolsBase
from agentpress.thread_manager import ThreadManager
from utils.logger import logger


class ProcessInfo:
    """Class to store information about a running process in the sandbox."""

    def __init__(self, terminal_id: int, command: str, process=None):
        """Initialize process information.

        Args:
            terminal_id: Unique identifier for the terminal
            command: Command that was executed
            process: Process object (not used in sandbox implementation)
        """
        self.terminal_id = terminal_id
        self.command = command
        self.process = process  # Not used in sandbox implementation
        self.start_time = datetime.now()
        self.output = []
        self.exit_code = None
        self.is_running = True

        # Sandbox-specific attributes
        self.session_id = None
        self.window_name = None
        self.script_path = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert process info to dictionary.

        Returns:
            Dictionary representation of process info
        """
        return {
            'terminal_id': self.terminal_id,
            'command': self.command,
            'start_time': self.start_time.isoformat(),
            'running_time': str(datetime.now() - self.start_time),
            'is_running': self.is_running,
            'exit_code': self.exit_code,
            'session_id': self.session_id,
            'window_name': self.window_name
        }

class SandboxShellTool(SandboxToolsBase):
    """Tool for executing tasks in an E2B sandbox with browser-use capabilities.
    Uses sessions to maintain state between commands and provides comprehensive process management."""

    def __init__(self, project_id: str, thread_manager: ThreadManager):
        super().__init__(project_id, thread_manager)
        self._sessions: Dict[str, Dict] = {}  # Maps session names to session info
        self.workspace_path = "/home/<USER>"  # Use home directory instead of /workspace
        self.processes: Dict[int, ProcessInfo] = {}  # Maps terminal IDs to process info
        self.next_terminal_id = 1

    async def _ensure_session(self, session_name: str = "default") -> str:
        """Ensure a session exists and return its ID."""
        if session_name not in self._sessions:
            session_id = str(uuid4())
            try:
                await self._ensure_sandbox()  # Ensure sandbox is initialized
                # In E2B, we don't have direct session management like Daytona
                # Instead, we'll track sessions ourselves
                self._sessions[session_name] = {
                    "id": session_id,
                    "commands": []
                }

                # Create a tmux session to maintain state
                try:
                    # Check if tmux is installed
                    result = self.sandbox.commands.run("which tmux", timeout=5)
                    if result.exit_code != 0:
                        # Install tmux if not available
                        logger.debug("Installing tmux in sandbox")
                        self.sandbox.commands.run("apt-get update && apt-get install -y tmux", timeout=60)

                    # Create a new tmux session
                    self.sandbox.commands.run(f"tmux new-session -d -s {session_id}", timeout=5)
                    logger.debug(f"Created tmux session {session_id}")
                except Exception as e:
                    logger.warning(f"Could not create tmux session: {str(e)}. Continuing without tmux.")

            except Exception as e:
                raise RuntimeError(f"Failed to create session: {str(e)}")
        return self._sessions[session_name]["id"]

    async def _cleanup_session(self, session_name: str):
        """Clean up a session if it exists."""
        if session_name in self._sessions:
            try:
                await self._ensure_sandbox()  # Ensure sandbox is initialized
                session_id = self._sessions[session_name]["id"]

                # Kill the tmux session if it exists
                try:
                    self.sandbox.commands.run(f"tmux kill-session -t {session_id}", timeout=5)
                    logger.debug(f"Killed tmux session {session_id}")
                except Exception as e:
                    logger.warning(f"Could not kill tmux session: {str(e)}")

                del self._sessions[session_name]
            except Exception as e:
                logger.warning(f"Warning: Failed to cleanup session {session_name}: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "execute_command",
            "description": "Execute a shell command in the workspace directory. IMPORTANT: By default, commands are blocking and will wait for completion before returning. For long-running operations, use background execution techniques (& operator, nohup) to prevent timeouts. Uses sessions to maintain state between commands. This tool is essential for running CLI tools, installing packages, and managing system operations. Always verify command outputs before using the data. Commands can be chained using && for sequential execution, || for fallback execution, and | for piping output.",
            "parameters": {
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "The shell command to execute. Use this for running CLI tools, installing packages, or system operations. Commands can be chained using &&, ||, and | operators. Example: 'find . -type f | sort && grep -r \"pattern\" . | awk \"{print $1}\" | sort | uniq -c'"
                    },
                    "folder": {
                        "type": "string",
                        "description": "Optional relative path to a subdirectory of /workspace where the command should be executed. Example: 'data/pdfs'"
                    },
                    "session_name": {
                        "type": "string",
                        "description": "Optional name of the session to use. Use named sessions for related commands that need to maintain state. Defaults to 'default'.",
                        "default": "default"
                    },
                    "timeout": {
                        "type": "integer",
                        "description": "Optional timeout in seconds. Increase for long-running commands. Defaults to 60. For commands that might exceed this timeout, use background execution with & operator instead.",
                        "default": 60
                    }
                },
                "required": ["command"]
            }
        }
    })
    @xml_schema(
        tag_name="execute-command",
        mappings=[
            {"param_name": "command", "node_type": "content", "path": "."},
            {"param_name": "folder", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "session_name", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "timeout", "node_type": "attribute", "path": ".", "required": False}
        ],
        example='''
        <!-- BLOCKING COMMANDS (Direct Execution) -->
        <!-- Example 1: Basic Command Execution -->
        <execute-command>
        ls -la
        </execute-command>

        <!-- Example 2: Running in Specific Directory -->
        <execute-command folder="src">
        npm install
        </execute-command>

        <!-- Example 3: Long-running Process with Extended Timeout -->
        <execute-command timeout="300">
        npm run build
        </execute-command>

        <!-- Example 4: Complex Command with Environment Variables -->
        <execute-command>
        export NODE_ENV=production && npm run preview
        </execute-command>

        <!-- Example 5: Command with Output Redirection -->
        <execute-command>
        npm run build > build.log 2>&1
        </execute-command>

        <!-- NON-BLOCKING COMMANDS (TMUX Sessions) -->
        <!-- Example 1: Start a Vite Development Server -->
        <execute-command>
        tmux new-session -d -s vite_dev "cd /workspace && npm run dev"
        </execute-command>

        <!-- Example 2: Check if Vite Server is Running -->
        <execute-command>
        tmux list-sessions | grep -q vite_dev && echo "Vite server running" || echo "Vite server not found"
        </execute-command>

        <!-- Example 3: Get Vite Server Output -->
        <execute-command>
        tmux capture-pane -pt vite_dev
        </execute-command>

        <!-- Example 4: Stop Vite Server -->
        <execute-command>
        tmux kill-session -t vite_dev
        </execute-command>

        <!-- Example 5: Start a Vite Build Process -->
        <execute-command>
        tmux new-session -d -s vite_build "cd /workspace && npm run build"
        </execute-command>

        <!-- Example 6: Monitor Vite Build Progress -->
        <execute-command>
        tmux capture-pane -pt vite_build
        </execute-command>

        <!-- Example 7: Start Multiple Vite Services -->
        <execute-command>
        tmux new-session -d -s vite_services "cd /workspace && npm run start:all"
        </execute-command>

        <!-- Example 8: Check All Running Services -->
        <execute-command>
        tmux list-sessions
        </execute-command>

        <!-- Example 9: Kill All TMUX Sessions -->
        <execute-command>
        tmux kill-server
        </execute-command>
        '''
    )
    async def execute_command(
        self,
        command: str,
        folder: Optional[str] = None,
        session_name: str = "default",
        timeout: int = 60
    ) -> ToolResult:
        try:
            # Ensure sandbox is initialized
            await self._ensure_sandbox()

            # Ensure session exists
            session_id = await self._ensure_session(session_name)

            # Set up working directory
            cwd = self.workspace_path
            if folder:
                folder = folder.strip('/')
                cwd = f"{self.workspace_path}/{folder}"

            # Prepare the command
            full_command = command

            # If using tmux, run the command in the tmux session
            try:
                # Check if tmux is available and the session exists
                tmux_check = self.sandbox.commands.run(f"tmux has-session -t {session_id} 2>/dev/null && echo 'exists' || echo 'not exists'", timeout=5)

                if "exists" in tmux_check.stdout:
                    # Run in tmux session
                    logger.debug(f"Running command in tmux session {session_id}")

                    # Create a temporary script to execute the command
                    script_path = f"/tmp/cmd_{session_id}.sh"
                    script_content = f"""#!/bin/bash
cd {cwd}
{command}
echo "E2B_EXIT_CODE=$?"
"""
                    # Write the script
                    self.sandbox.files.write(script_path, script_content)
                    self.sandbox.commands.run(f"chmod +x {script_path}", timeout=5)

                    # Execute in tmux and capture output
                    self.sandbox.commands.run(f"tmux send-keys -t {session_id} '{script_path}' C-m", timeout=5)

                    # Wait for command to complete (with timeout)
                    import time
                    start_time = time.time()
                    output = ""
                    exit_code = None

                    while time.time() - start_time < timeout:
                        # Capture the current output
                        capture = self.sandbox.commands.run(f"tmux capture-pane -p -t {session_id}", timeout=5)
                        current_output = capture.stdout

                        # Check if command has completed
                        if "E2B_EXIT_CODE=" in current_output:
                            output = current_output
                            # Extract exit code
                            exit_code_line = [line for line in current_output.split('\n') if "E2B_EXIT_CODE=" in line][-1]
                            exit_code = int(exit_code_line.split("=")[1])
                            break

                        # Wait a bit before checking again
                        time.sleep(1)

                    # Clean up
                    self.sandbox.commands.run(f"rm -f {script_path}", timeout=5)

                    if exit_code is None:
                        # Command timed out
                        return self.fail_response(f"Command timed out after {timeout} seconds")

                    if exit_code == 0:
                        # Remove the exit code line from output
                        clean_output = '\n'.join([line for line in output.split('\n') if "E2B_EXIT_CODE=" not in line])
                        return self.success_response({
                            "output": clean_output,
                            "exit_code": exit_code,
                            "cwd": cwd
                        })
                    else:
                        return self.fail_response(f"Command failed with exit code {exit_code}: {output}")

            except Exception as tmux_error:
                logger.warning(f"Could not use tmux session: {str(tmux_error)}. Falling back to direct command execution.")

            # If tmux failed or is not available, fall back to direct command execution
            # Ensure we're in the correct directory before executing the command
            full_command = f"cd {cwd} && {command}"

            # Execute command directly
            logger.debug(f"Running command directly: {full_command}")
            result = self.sandbox.commands.run(
                full_command,
                timeout=timeout
            )

            if result.exit_code == 0:
                return self.success_response({
                    "output": result.stdout,
                    "exit_code": result.exit_code,
                    "cwd": cwd
                })
            else:
                error_msg = f"Command failed with exit code {result.exit_code}"
                if result.stderr:
                    error_msg += f": {result.stderr}"
                elif result.stdout:
                    error_msg += f": {result.stdout}"
                return self.fail_response(error_msg)

        except Exception as e:
            return self.fail_response(f"Error executing command: {str(e)}")

    async def cleanup(self):
        """Clean up all sessions."""
        for session_name in list(self._sessions.keys()):
            await self._cleanup_session(session_name)

    async def _launch_process(self, command: str, cwd: Optional[str] = None, session_name: str = "default") -> ProcessInfo:
        """Launch a new process in the sandbox.

        Args:
            command: Command to execute
            cwd: Working directory for the command
            session_name: Name of the session to use

        Returns:
            ProcessInfo object for the launched process
        """
        await self._ensure_sandbox()  # Ensure sandbox is initialized

        # Set up working directory
        if cwd:
            cwd = cwd.strip('/')
            full_cwd = f"{self.workspace_path}/{cwd}"
        else:
            full_cwd = self.workspace_path

        # Try to use tmux session if available
        try:
            session_id = await self._ensure_session(session_name)

            # Create a script to run the command
            script_path = f"/tmp/cmd_{session_id}_{self.next_terminal_id}.sh"
            script_content = f"""#!/bin/bash
cd {full_cwd}
{command}
echo "EXIT_CODE=$?"
"""
            # Write the script
            self.sandbox.files.write(script_path, script_content)
            self.sandbox.commands.run(f"chmod +x {script_path}", timeout=5)

            # Run the command in the background
            result = self.sandbox.commands.run(
                f"tmux new-window -d -t {session_id} -n 'proc{self.next_terminal_id}' '{script_path}'",
                timeout=5
            )

            if result.exit_code != 0:
                raise RuntimeError(f"Failed to launch process: {result.stderr}")

            # Create a ProcessInfo object
            terminal_id = self.next_terminal_id
            self.next_terminal_id += 1

            # We don't have a direct process object, so we'll create a placeholder
            # The actual process is managed by tmux
            process_info = ProcessInfo(
                terminal_id=terminal_id,
                command=command,
                process=None  # No direct process object
            )
            process_info.session_id = session_id
            process_info.window_name = f"proc{terminal_id}"
            process_info.script_path = script_path

            self.processes[terminal_id] = process_info
            return process_info

        except Exception as e:
            logger.error(f"Error launching process: {str(e)}")
            raise RuntimeError(f"Failed to launch process: {str(e)}")

    async def _read_process_output(self, process_info: ProcessInfo, wait: bool = False, timeout: float = 10.0) -> str:
        """Read output from a process in the sandbox.

        Args:
            process_info: ProcessInfo object
            wait: Whether to wait for the process to complete
            timeout: Maximum time to wait in seconds

        Returns:
            Process output
        """
        await self._ensure_sandbox()  # Ensure sandbox is initialized

        if not process_info.is_running and process_info.output:
            # Process already completed, return stored output
            return '\n'.join(process_info.output)

        if not process_info.session_id or not process_info.window_name:
            return "Process information incomplete. Cannot read output."

        try:
            # Capture output from tmux
            result = self.sandbox.commands.run(
                f"tmux capture-pane -p -t {process_info.session_id}:{process_info.window_name}",
                timeout=5
            )

            if result.exit_code != 0:
                return f"Error capturing output: {result.stderr}"

            output = result.stdout

            # Check if process has completed
            if "EXIT_CODE=" in output:
                # Extract exit code
                exit_code_line = [line for line in output.split('\n') if "EXIT_CODE=" in line][-1]
                exit_code = int(exit_code_line.split("=")[1])

                # Update process status
                process_info.is_running = False
                process_info.exit_code = exit_code

                # Clean up the output (remove the exit code line)
                output = '\n'.join([line for line in output.split('\n') if "EXIT_CODE=" not in line])
            elif wait and process_info.is_running:
                # Wait for process to complete
                start_time = time.time()
                while time.time() - start_time < timeout:
                    # Check if process has completed
                    result = self.sandbox.commands.run(
                        f"tmux capture-pane -p -t {process_info.session_id}:{process_info.window_name}",
                        timeout=5
                    )

                    current_output = result.stdout

                    if "EXIT_CODE=" in current_output:
                        # Extract exit code
                        exit_code_line = [line for line in current_output.split('\n') if "EXIT_CODE=" in line][-1]
                        exit_code = int(exit_code_line.split("=")[1])

                        # Update process status
                        process_info.is_running = False
                        process_info.exit_code = exit_code

                        # Clean up the output (remove the exit code line)
                        output = '\n'.join([line for line in current_output.split('\n') if "EXIT_CODE=" not in line])
                        break

                    # Wait a bit before checking again
                    await asyncio.sleep(1)

                if process_info.is_running:
                    return f"Process still running after {timeout} seconds. Use read-process with wait=false to get current output."

            # Store output
            process_info.output.append(output)

            return output

        except Exception as e:
            logger.error(f"Error reading process output: {str(e)}")
            return f"Error reading process output: {str(e)}"

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "launch_process",
            "description": "Launch a new process with a shell command in the sandbox. The process can be waiting or non-waiting.",
            "parameters": {
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "The shell command to execute."
                    },
                    "cwd": {
                        "type": "string",
                        "description": "Working directory for the command relative to /workspace. If not supplied, uses /workspace."
                    },
                    "wait": {
                        "type": "boolean",
                        "description": "Whether to wait for the command to complete.",
                        "default": True
                    },
                    "max_wait_seconds": {
                        "type": "number",
                        "description": "Number of seconds to wait for the command to complete. Only relevant when wait=true.",
                        "default": 30.0
                    },
                    "session_name": {
                        "type": "string",
                        "description": "Name of the session to use. Sessions maintain state between commands.",
                        "default": "default"
                    }
                },
                "required": ["command"]
            }
        }
    })
    @xml_schema(
        tag_name="launch-process",
        mappings=[
            {"param_name": "command", "node_type": "attribute", "path": "command"},
            {"param_name": "cwd", "node_type": "attribute", "path": "cwd"},
            {"param_name": "wait", "node_type": "attribute", "path": "wait"},
            {"param_name": "max_wait_seconds", "node_type": "attribute", "path": "max_wait_seconds"},
            {"param_name": "session_name", "node_type": "attribute", "path": "session"}
        ],
        example='''
        <!-- Launch a process and wait for it to complete -->
        <launch-process
            command="ls -la"
            wait="true"
            max_wait_seconds="30">
        </launch-process>

        <!-- Launch a background process -->
        <launch-process
            command="python server.py"
            cwd="src"
            wait="false"
            max_wait_seconds="30">
        </launch-process>

        <!-- Launch a process in a specific session -->
        <launch-process
            command="npm start"
            cwd="frontend"
            wait="false"
            session="frontend">
        </launch-process>
        '''
    )
    async def launch_process(
        self,
        command: str,
        cwd: Optional[str] = None,
        wait: bool = True,
        max_wait_seconds: float = 30.0,
        session_name: str = "default"
    ) -> ToolResult:
        """
        Launch a new process with a shell command in the sandbox.

        Args:
            command: The shell command to execute
            cwd: Working directory for the command (relative to /workspace)
            wait: Whether to wait for the command to complete
            max_wait_seconds: Number of seconds to wait for the command to complete
            session_name: Name of the session to use

        Returns:
            ToolResult with process information
        """
        try:
            # Launch the process in the sandbox
            process_info = await self._launch_process(command, cwd, session_name)

            if wait:
                # Wait for the process to complete
                output = await self._read_process_output(
                    process_info,
                    wait=True,
                    timeout=max_wait_seconds
                )

                return self.success_response({
                    'terminal_id': process_info.terminal_id,
                    'command': command,
                    'output': output,
                    'exit_code': process_info.exit_code,
                    'completed': not process_info.is_running,
                    'session_id': process_info.session_id,
                    'window_name': process_info.window_name
                })
            else:
                # Return immediately for non-waiting processes
                return self.success_response({
                    'terminal_id': process_info.terminal_id,
                    'command': command,
                    'message': f"Process launched with terminal ID: {process_info.terminal_id}",
                    'running': True,
                    'session_id': process_info.session_id,
                    'window_name': process_info.window_name
                })

        except Exception as e:
            logger.error(f"Error launching process: {str(e)}")
            return self.fail_response(f"Error launching process: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "kill_process",
            "description": "Kill a process by its terminal ID.",
            "parameters": {
                "type": "object",
                "properties": {
                    "terminal_id": {
                        "type": "integer",
                        "description": "Terminal ID to kill."
                    }
                },
                "required": ["terminal_id"]
            }
        }
    })
    @xml_schema(
        tag_name="kill-process",
        mappings=[
            {"param_name": "terminal_id", "node_type": "attribute", "path": "terminal_id"}
        ],
        example='''
        <kill-process terminal_id="1">
        </kill-process>
        '''
    )
    async def kill_process(self, terminal_id: int) -> ToolResult:
        """
        Kill a process by its terminal ID.

        Args:
            terminal_id: Terminal ID to kill

        Returns:
            ToolResult indicating success or failure
        """
        try:
            await self._ensure_sandbox()  # Ensure sandbox is initialized

            # Check if terminal ID exists
            if terminal_id not in self.processes:
                return self.fail_response(f"Terminal ID {terminal_id} not found")

            process_info = self.processes[terminal_id]

            # Check if process is already completed
            if not process_info.is_running:
                return self.success_response({
                    'message': f"Process with terminal ID {terminal_id} already completed",
                    'exit_code': process_info.exit_code
                })

            # Check if we have session information
            if not process_info.session_id or not process_info.window_name:
                return self.fail_response(f"Process information incomplete. Cannot kill process.")

            # Kill the process using tmux
            try:
                # Kill the tmux window
                result = self.sandbox.commands.run(
                    f"tmux kill-window -t {process_info.session_id}:{process_info.window_name}",
                    timeout=5
                )

                if result.exit_code != 0:
                    return self.fail_response(f"Failed to kill process: {result.stderr}")

                # Clean up the script file if it exists
                if process_info.script_path:
                    self.sandbox.commands.run(f"rm -f {process_info.script_path}", timeout=5)

                # Update process status
                process_info.is_running = False
                process_info.exit_code = -9  # SIGKILL

                return self.success_response({
                    'message': f"Process with terminal ID {terminal_id} killed",
                    'exit_code': process_info.exit_code
                })

            except Exception as e:
                logger.error(f"Error killing process: {str(e)}")
                return self.fail_response(f"Error killing process: {str(e)}")

        except Exception as e:
            logger.error(f"Error killing process: {str(e)}")
            return self.fail_response(f"Error killing process: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "read_process",
            "description": "Read output from a terminal. If wait=true and the process has not yet completed, waits for the terminal to complete up to max_wait_seconds before returning its output.",
            "parameters": {
                "type": "object",
                "properties": {
                    "terminal_id": {
                        "type": "integer",
                        "description": "Terminal ID to read from."
                    },
                    "wait": {
                        "type": "boolean",
                        "description": "Whether to wait for the command to complete.",
                        "default": False
                    },
                    "max_wait_seconds": {
                        "type": "number",
                        "description": "Number of seconds to wait for the command to complete. Only relevant when wait=true.",
                        "default": 10.0
                    }
                },
                "required": ["terminal_id", "wait", "max_wait_seconds"]
            }
        }
    })
    @xml_schema(
        tag_name="read-process",
        mappings=[
            {"param_name": "terminal_id", "node_type": "attribute", "path": "terminal_id"},
            {"param_name": "wait", "node_type": "attribute", "path": "wait"},
            {"param_name": "max_wait_seconds", "node_type": "attribute", "path": "max_wait_seconds"}
        ],
        example='''
        <!-- Read process output without waiting -->
        <read-process
            terminal_id="1"
            wait="false"
            max_wait_seconds="10">
        </read-process>

        <!-- Read process output and wait for completion -->
        <read-process
            terminal_id="1"
            wait="true"
            max_wait_seconds="30">
        </read-process>
        '''
    )
    async def read_process(
        self,
        terminal_id: int,
        wait: bool = False,
        max_wait_seconds: float = 10.0
    ) -> ToolResult:
        """
        Read output from a terminal.

        Args:
            terminal_id: Terminal ID to read from
            wait: Whether to wait for the command to complete
            max_wait_seconds: Number of seconds to wait for the command to complete

        Returns:
            ToolResult with process output
        """
        try:
            # Check if terminal ID exists
            if terminal_id not in self.processes:
                return self.fail_response(f"Terminal ID {terminal_id} not found")

            process_info = self.processes[terminal_id]

            # Read process output
            output = await self._read_process_output(
                process_info,
                wait=wait,
                timeout=max_wait_seconds
            )

            return self.success_response({
                'terminal_id': terminal_id,
                'command': process_info.command,
                'output': output,
                'is_running': process_info.is_running,
                'exit_code': process_info.exit_code
            })

        except Exception as e:
            logger.error(f"Error reading process: {str(e)}")
            return self.fail_response(f"Error reading process: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "write_process",
            "description": "Write input to a terminal.",
            "parameters": {
                "type": "object",
                "properties": {
                    "terminal_id": {
                        "type": "integer",
                        "description": "Terminal ID to write to."
                    },
                    "input_text": {
                        "type": "string",
                        "description": "Text to write to the process's stdin."
                    }
                },
                "required": ["terminal_id", "input_text"]
            }
        }
    })
    @xml_schema(
        tag_name="write-process",
        mappings=[
            {"param_name": "terminal_id", "node_type": "attribute", "path": "terminal_id"},
            {"param_name": "input_text", "node_type": "content", "path": "."}
        ],
        example='''
        <write-process terminal_id="1">
        yes
        </write-process>
        '''
    )
    async def write_process(self, terminal_id: int, input_text: str) -> ToolResult:
        """
        Write input to a terminal.

        Args:
            terminal_id: Terminal ID to write to
            input_text: Text to write to the process's stdin

        Returns:
            ToolResult indicating success or failure
        """
        try:
            await self._ensure_sandbox()  # Ensure sandbox is initialized

            # Check if terminal ID exists
            if terminal_id not in self.processes:
                return self.fail_response(f"Terminal ID {terminal_id} not found")

            process_info = self.processes[terminal_id]

            # Check if process is still running
            if not process_info.is_running:
                return self.fail_response(f"Process with terminal ID {terminal_id} is not running")

            # Check if we have session information
            if not process_info.session_id or not process_info.window_name:
                return self.fail_response(f"Process information incomplete. Cannot write to process.")

            # Ensure input ends with newline
            if not input_text.endswith('\n'):
                input_text += '\n'

            # Escape special characters in the input text
            escaped_input = input_text.replace("'", "'\\''")

            # Write to process using tmux send-keys
            result = self.sandbox.commands.run(
                f"tmux send-keys -t {process_info.session_id}:{process_info.window_name} '{escaped_input}' ENTER",
                timeout=5
            )

            if result.exit_code != 0:
                return self.fail_response(f"Failed to write to process: {result.stderr}")

            return self.success_response({
                'message': f"Input written to process with terminal ID {terminal_id}",
                'input_length': len(input_text)
            })

        except Exception as e:
            logger.error(f"Error writing to process: {str(e)}")
            return self.fail_response(f"Error writing to process: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "list_processes",
            "description": "List all known terminals and their states.",
            "parameters": {
                "type": "object",
                "properties": {}
            }
        }
    })
    @xml_schema(
        tag_name="list-processes",
        mappings=[],
        example='''
        <list-processes>
        </list-processes>
        '''
    )
    async def list_processes(self) -> ToolResult:
        """
        List all known terminals and their states.

        Returns:
            ToolResult with list of processes
        """
        try:
            await self._ensure_sandbox()  # Ensure sandbox is initialized

            # Update process status for all processes
            for process_id, process_info in list(self.processes.items()):
                if process_info.is_running and process_info.session_id and process_info.window_name:
                    try:
                        # Check if the tmux window still exists
                        result = self.sandbox.commands.run(
                            f"tmux list-windows -t {process_info.session_id} | grep {process_info.window_name}",
                            timeout=5
                        )

                        if result.exit_code != 0:
                            # Window doesn't exist anymore
                            process_info.is_running = False
                            process_info.exit_code = -1
                            continue

                        # Check if the process has completed by looking for EXIT_CODE
                        output_result = self.sandbox.commands.run(
                            f"tmux capture-pane -p -t {process_info.session_id}:{process_info.window_name}",
                            timeout=5
                        )

                        if "EXIT_CODE=" in output_result.stdout:
                            # Extract exit code
                            exit_code_line = [line for line in output_result.stdout.split('\n') if "EXIT_CODE=" in line][-1]
                            exit_code = int(exit_code_line.split("=")[1])

                            # Update process status
                            process_info.is_running = False
                            process_info.exit_code = exit_code
                    except Exception as e:
                        logger.warning(f"Error checking process status: {str(e)}")

            # Format process information
            process_list = [
                process_info.to_dict()
                for _, process_info in sorted(self.processes.items())
            ]

            return self.success_response({
                'processes': process_list,
                'total': len(process_list),
                'running': sum(1 for p in process_list if p['is_running'])
            })

        except Exception as e:
            logger.error(f"Error listing processes: {str(e)}")
            return self.fail_response(f"Error listing processes: {str(e)}")