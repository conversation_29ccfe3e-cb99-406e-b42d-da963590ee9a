'use client'

import { useEffect, useState } from 'react'
import { useParams, usePathname } from 'next/navigation'
import { ProjectSidebarLeft } from '@/components/sidebar/project-sidebar-left'
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar'
import { getProject } from '@/lib/api'
import { CallProvider } from '@/components/project/chat/contexts/CallContext'

export default function ProjectLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const params = useParams()
  const pathname = usePathname()
  const projectId = params.projectId as string
  const [activeSection, setActiveSection] = useState<'chat' | 'team' | 'tools' | 'settings'>('chat')
  const [project, setProject] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchProject = async () => {
      try {
        setLoading(true)
        const projectData = await getProject(projectId)
        setProject(projectData)
      } catch (err) {
        console.error('Error fetching project:', err)
      } finally {
        setLoading(false)
      }
    }

    if (projectId) {
      fetchProject()
    }
  }, [projectId])

  // Determine active section based on pathname
  useEffect(() => {
    if (pathname.includes('/chat') || pathname.includes('/threads')) {
      setActiveSection('chat')
    } else if (pathname.includes('/team')) {
      setActiveSection('team')
    } else if (pathname.includes('/tools') || pathname.includes('/integrations')) {
      setActiveSection('tools')
    } else if (pathname.includes('/settings')) {
      setActiveSection('settings')
    } else {
      setActiveSection('chat')
    }
  }, [pathname])

  // Determine if sidebar should be collapsed by default (only for chat page)
  const isOnChatPage = pathname.includes('/chat') || pathname.includes('/threads') || activeSection === 'chat'

  // Dispatch a custom event when the section changes and handle URL routing
  const handleSectionChange = (section: 'chat' | 'team' | 'tools' | 'settings') => {
    setActiveSection(section)

    // Update URL to match the section
    let targetPath = '';
    switch (section) {
      case 'chat':
        targetPath = `/project/${projectId}/chat`;
        break;
      case 'team':
        targetPath = `/project/${projectId}/team`;
        break;
      case 'tools':
        targetPath = `/project/${projectId}/tools`;
        break;
      case 'settings':
        targetPath = `/project/${projectId}/settings`;
        break;
      default:
        targetPath = `/project/${projectId}/chat`;
    }

    // Update URL without page reload
    window.history.pushState({}, '', targetPath);

    window.dispatchEvent(
      new CustomEvent('sidebar-section-change', {
        detail: { section },
      })
    )
  }

  return (
    <CallProvider>
      <SidebarProvider defaultOpen={!isOnChatPage}>
        <ProjectSidebarLeft
          activeSection={activeSection}
          onSectionChange={handleSectionChange}
        />
        <SidebarInset>
          <div className="bg-background">
            <main>{children}</main>
          </div>
        </SidebarInset>
      </SidebarProvider>
    </CallProvider>
  )
}
