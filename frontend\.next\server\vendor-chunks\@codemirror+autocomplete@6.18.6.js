"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+autocomplete@6.18.6";
exports.ids = ["vendor-chunks/@codemirror+autocomplete@6.18.6"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+autocomplete@6.18.6/node_modules/@codemirror/autocomplete/dist/index.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+autocomplete@6.18.6/node_modules/@codemirror/autocomplete/dist/index.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompletionContext: () => (/* binding */ CompletionContext),\n/* harmony export */   acceptCompletion: () => (/* binding */ acceptCompletion),\n/* harmony export */   autocompletion: () => (/* binding */ autocompletion),\n/* harmony export */   clearSnippet: () => (/* binding */ clearSnippet),\n/* harmony export */   closeBrackets: () => (/* binding */ closeBrackets),\n/* harmony export */   closeBracketsKeymap: () => (/* binding */ closeBracketsKeymap),\n/* harmony export */   closeCompletion: () => (/* binding */ closeCompletion),\n/* harmony export */   completeAnyWord: () => (/* binding */ completeAnyWord),\n/* harmony export */   completeFromList: () => (/* binding */ completeFromList),\n/* harmony export */   completionKeymap: () => (/* binding */ completionKeymap),\n/* harmony export */   completionStatus: () => (/* binding */ completionStatus),\n/* harmony export */   currentCompletions: () => (/* binding */ currentCompletions),\n/* harmony export */   deleteBracketPair: () => (/* binding */ deleteBracketPair),\n/* harmony export */   hasNextSnippetField: () => (/* binding */ hasNextSnippetField),\n/* harmony export */   hasPrevSnippetField: () => (/* binding */ hasPrevSnippetField),\n/* harmony export */   ifIn: () => (/* binding */ ifIn),\n/* harmony export */   ifNotIn: () => (/* binding */ ifNotIn),\n/* harmony export */   insertBracket: () => (/* binding */ insertBracket),\n/* harmony export */   insertCompletionText: () => (/* binding */ insertCompletionText),\n/* harmony export */   moveCompletionSelection: () => (/* binding */ moveCompletionSelection),\n/* harmony export */   nextSnippetField: () => (/* binding */ nextSnippetField),\n/* harmony export */   pickedCompletion: () => (/* binding */ pickedCompletion),\n/* harmony export */   prevSnippetField: () => (/* binding */ prevSnippetField),\n/* harmony export */   selectedCompletion: () => (/* binding */ selectedCompletion),\n/* harmony export */   selectedCompletionIndex: () => (/* binding */ selectedCompletionIndex),\n/* harmony export */   setSelectedCompletion: () => (/* binding */ setSelectedCompletion),\n/* harmony export */   snippet: () => (/* binding */ snippet),\n/* harmony export */   snippetCompletion: () => (/* binding */ snippetCompletion),\n/* harmony export */   snippetKeymap: () => (/* binding */ snippetKeymap),\n/* harmony export */   startCompletion: () => (/* binding */ startCompletion)\n/* harmony export */ });\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n\n\n\n\n/**\nAn instance of this is passed to completion source functions.\n*/\nclass CompletionContext {\n    /**\n    Create a new completion context. (Mostly useful for testing\n    completion sources—in the editor, the extension will create\n    these for you.)\n    */\n    constructor(\n    /**\n    The editor state that the completion happens in.\n    */\n    state, \n    /**\n    The position at which the completion is happening.\n    */\n    pos, \n    /**\n    Indicates whether completion was activated explicitly, or\n    implicitly by typing. The usual way to respond to this is to\n    only return completions when either there is part of a\n    completable entity before the cursor, or `explicit` is true.\n    */\n    explicit, \n    /**\n    The editor view. May be undefined if the context was created\n    in a situation where there is no such view available, such as\n    in synchronous updates via\n    [`CompletionResult.update`](https://codemirror.net/6/docs/ref/#autocomplete.CompletionResult.update)\n    or when called by test code.\n    */\n    view) {\n        this.state = state;\n        this.pos = pos;\n        this.explicit = explicit;\n        this.view = view;\n        /**\n        @internal\n        */\n        this.abortListeners = [];\n        /**\n        @internal\n        */\n        this.abortOnDocChange = false;\n    }\n    /**\n    Get the extent, content, and (if there is a token) type of the\n    token before `this.pos`.\n    */\n    tokenBefore(types) {\n        let token = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.syntaxTree)(this.state).resolveInner(this.pos, -1);\n        while (token && types.indexOf(token.name) < 0)\n            token = token.parent;\n        return token ? { from: token.from, to: this.pos,\n            text: this.state.sliceDoc(token.from, this.pos),\n            type: token.type } : null;\n    }\n    /**\n    Get the match of the given expression directly before the\n    cursor.\n    */\n    matchBefore(expr) {\n        let line = this.state.doc.lineAt(this.pos);\n        let start = Math.max(line.from, this.pos - 250);\n        let str = line.text.slice(start - line.from, this.pos - line.from);\n        let found = str.search(ensureAnchor(expr, false));\n        return found < 0 ? null : { from: start + found, to: this.pos, text: str.slice(found) };\n    }\n    /**\n    Yields true when the query has been aborted. Can be useful in\n    asynchronous queries to avoid doing work that will be ignored.\n    */\n    get aborted() { return this.abortListeners == null; }\n    /**\n    Allows you to register abort handlers, which will be called when\n    the query is\n    [aborted](https://codemirror.net/6/docs/ref/#autocomplete.CompletionContext.aborted).\n    \n    By default, running queries will not be aborted for regular\n    typing or backspacing, on the assumption that they are likely to\n    return a result with a\n    [`validFor`](https://codemirror.net/6/docs/ref/#autocomplete.CompletionResult.validFor) field that\n    allows the result to be used after all. Passing `onDocChange:\n    true` will cause this query to be aborted for any document\n    change.\n    */\n    addEventListener(type, listener, options) {\n        if (type == \"abort\" && this.abortListeners) {\n            this.abortListeners.push(listener);\n            if (options && options.onDocChange)\n                this.abortOnDocChange = true;\n        }\n    }\n}\nfunction toSet(chars) {\n    let flat = Object.keys(chars).join(\"\");\n    let words = /\\w/.test(flat);\n    if (words)\n        flat = flat.replace(/\\w/g, \"\");\n    return `[${words ? \"\\\\w\" : \"\"}${flat.replace(/[^\\w\\s]/g, \"\\\\$&\")}]`;\n}\nfunction prefixMatch(options) {\n    let first = Object.create(null), rest = Object.create(null);\n    for (let { label } of options) {\n        first[label[0]] = true;\n        for (let i = 1; i < label.length; i++)\n            rest[label[i]] = true;\n    }\n    let source = toSet(first) + toSet(rest) + \"*$\";\n    return [new RegExp(\"^\" + source), new RegExp(source)];\n}\n/**\nGiven a a fixed array of options, return an autocompleter that\ncompletes them.\n*/\nfunction completeFromList(list) {\n    let options = list.map(o => typeof o == \"string\" ? { label: o } : o);\n    let [validFor, match] = options.every(o => /^\\w+$/.test(o.label)) ? [/\\w*$/, /\\w+$/] : prefixMatch(options);\n    return (context) => {\n        let token = context.matchBefore(match);\n        return token || context.explicit ? { from: token ? token.from : context.pos, options, validFor } : null;\n    };\n}\n/**\nWrap the given completion source so that it will only fire when the\ncursor is in a syntax node with one of the given names.\n*/\nfunction ifIn(nodes, source) {\n    return (context) => {\n        for (let pos = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.syntaxTree)(context.state).resolveInner(context.pos, -1); pos; pos = pos.parent) {\n            if (nodes.indexOf(pos.name) > -1)\n                return source(context);\n            if (pos.type.isTop)\n                break;\n        }\n        return null;\n    };\n}\n/**\nWrap the given completion source so that it will not fire when the\ncursor is in a syntax node with one of the given names.\n*/\nfunction ifNotIn(nodes, source) {\n    return (context) => {\n        for (let pos = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.syntaxTree)(context.state).resolveInner(context.pos, -1); pos; pos = pos.parent) {\n            if (nodes.indexOf(pos.name) > -1)\n                return null;\n            if (pos.type.isTop)\n                break;\n        }\n        return source(context);\n    };\n}\nclass Option {\n    constructor(completion, source, match, score) {\n        this.completion = completion;\n        this.source = source;\n        this.match = match;\n        this.score = score;\n    }\n}\nfunction cur(state) { return state.selection.main.from; }\n// Make sure the given regexp has a $ at its end and, if `start` is\n// true, a ^ at its start.\nfunction ensureAnchor(expr, start) {\n    var _a;\n    let { source } = expr;\n    let addStart = start && source[0] != \"^\", addEnd = source[source.length - 1] != \"$\";\n    if (!addStart && !addEnd)\n        return expr;\n    return new RegExp(`${addStart ? \"^\" : \"\"}(?:${source})${addEnd ? \"$\" : \"\"}`, (_a = expr.flags) !== null && _a !== void 0 ? _a : (expr.ignoreCase ? \"i\" : \"\"));\n}\n/**\nThis annotation is added to transactions that are produced by\npicking a completion.\n*/\nconst pickedCompletion = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Annotation.define();\n/**\nHelper function that returns a transaction spec which inserts a\ncompletion's text in the main selection range, and any other\nselection range that has the same text in front of it.\n*/\nfunction insertCompletionText(state, text, from, to) {\n    let { main } = state.selection, fromOff = from - main.from, toOff = to - main.from;\n    return Object.assign(Object.assign({}, state.changeByRange(range => {\n        if (range != main && from != to &&\n            state.sliceDoc(range.from + fromOff, range.from + toOff) != state.sliceDoc(from, to))\n            return { range };\n        let lines = state.toText(text);\n        return {\n            changes: { from: range.from + fromOff, to: to == main.from ? range.to : range.from + toOff, insert: lines },\n            range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(range.from + fromOff + lines.length)\n        };\n    })), { scrollIntoView: true, userEvent: \"input.complete\" });\n}\nconst SourceCache = /*@__PURE__*/new WeakMap();\nfunction asSource(source) {\n    if (!Array.isArray(source))\n        return source;\n    let known = SourceCache.get(source);\n    if (!known)\n        SourceCache.set(source, known = completeFromList(source));\n    return known;\n}\nconst startCompletionEffect = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define();\nconst closeCompletionEffect = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define();\n\n// A pattern matcher for fuzzy completion matching. Create an instance\n// once for a pattern, and then use that to match any number of\n// completions.\nclass FuzzyMatcher {\n    constructor(pattern) {\n        this.pattern = pattern;\n        this.chars = [];\n        this.folded = [];\n        // Buffers reused by calls to `match` to track matched character\n        // positions.\n        this.any = [];\n        this.precise = [];\n        this.byWord = [];\n        this.score = 0;\n        this.matched = [];\n        for (let p = 0; p < pattern.length;) {\n            let char = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointAt)(pattern, p), size = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointSize)(char);\n            this.chars.push(char);\n            let part = pattern.slice(p, p + size), upper = part.toUpperCase();\n            this.folded.push((0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointAt)(upper == part ? part.toLowerCase() : upper, 0));\n            p += size;\n        }\n        this.astral = pattern.length != this.chars.length;\n    }\n    ret(score, matched) {\n        this.score = score;\n        this.matched = matched;\n        return this;\n    }\n    // Matches a given word (completion) against the pattern (input).\n    // Will return a boolean indicating whether there was a match and,\n    // on success, set `this.score` to the score, `this.matched` to an\n    // array of `from, to` pairs indicating the matched parts of `word`.\n    //\n    // The score is a number that is more negative the worse the match\n    // is. See `Penalty` above.\n    match(word) {\n        if (this.pattern.length == 0)\n            return this.ret(-100 /* Penalty.NotFull */, []);\n        if (word.length < this.pattern.length)\n            return null;\n        let { chars, folded, any, precise, byWord } = this;\n        // For single-character queries, only match when they occur right\n        // at the start\n        if (chars.length == 1) {\n            let first = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointAt)(word, 0), firstSize = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointSize)(first);\n            let score = firstSize == word.length ? 0 : -100 /* Penalty.NotFull */;\n            if (first == chars[0]) ;\n            else if (first == folded[0])\n                score += -200 /* Penalty.CaseFold */;\n            else\n                return null;\n            return this.ret(score, [0, firstSize]);\n        }\n        let direct = word.indexOf(this.pattern);\n        if (direct == 0)\n            return this.ret(word.length == this.pattern.length ? 0 : -100 /* Penalty.NotFull */, [0, this.pattern.length]);\n        let len = chars.length, anyTo = 0;\n        if (direct < 0) {\n            for (let i = 0, e = Math.min(word.length, 200); i < e && anyTo < len;) {\n                let next = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointAt)(word, i);\n                if (next == chars[anyTo] || next == folded[anyTo])\n                    any[anyTo++] = i;\n                i += (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointSize)(next);\n            }\n            // No match, exit immediately\n            if (anyTo < len)\n                return null;\n        }\n        // This tracks the extent of the precise (non-folded, not\n        // necessarily adjacent) match\n        let preciseTo = 0;\n        // Tracks whether there is a match that hits only characters that\n        // appear to be starting words. `byWordFolded` is set to true when\n        // a case folded character is encountered in such a match\n        let byWordTo = 0, byWordFolded = false;\n        // If we've found a partial adjacent match, these track its state\n        let adjacentTo = 0, adjacentStart = -1, adjacentEnd = -1;\n        let hasLower = /[a-z]/.test(word), wordAdjacent = true;\n        // Go over the option's text, scanning for the various kinds of matches\n        for (let i = 0, e = Math.min(word.length, 200), prevType = 0 /* Tp.NonWord */; i < e && byWordTo < len;) {\n            let next = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointAt)(word, i);\n            if (direct < 0) {\n                if (preciseTo < len && next == chars[preciseTo])\n                    precise[preciseTo++] = i;\n                if (adjacentTo < len) {\n                    if (next == chars[adjacentTo] || next == folded[adjacentTo]) {\n                        if (adjacentTo == 0)\n                            adjacentStart = i;\n                        adjacentEnd = i + 1;\n                        adjacentTo++;\n                    }\n                    else {\n                        adjacentTo = 0;\n                    }\n                }\n            }\n            let ch, type = next < 0xff\n                ? (next >= 48 && next <= 57 || next >= 97 && next <= 122 ? 2 /* Tp.Lower */ : next >= 65 && next <= 90 ? 1 /* Tp.Upper */ : 0 /* Tp.NonWord */)\n                : ((ch = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.fromCodePoint)(next)) != ch.toLowerCase() ? 1 /* Tp.Upper */ : ch != ch.toUpperCase() ? 2 /* Tp.Lower */ : 0 /* Tp.NonWord */);\n            if (!i || type == 1 /* Tp.Upper */ && hasLower || prevType == 0 /* Tp.NonWord */ && type != 0 /* Tp.NonWord */) {\n                if (chars[byWordTo] == next || (folded[byWordTo] == next && (byWordFolded = true)))\n                    byWord[byWordTo++] = i;\n                else if (byWord.length)\n                    wordAdjacent = false;\n            }\n            prevType = type;\n            i += (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointSize)(next);\n        }\n        if (byWordTo == len && byWord[0] == 0 && wordAdjacent)\n            return this.result(-100 /* Penalty.ByWord */ + (byWordFolded ? -200 /* Penalty.CaseFold */ : 0), byWord, word);\n        if (adjacentTo == len && adjacentStart == 0)\n            return this.ret(-200 /* Penalty.CaseFold */ - word.length + (adjacentEnd == word.length ? 0 : -100 /* Penalty.NotFull */), [0, adjacentEnd]);\n        if (direct > -1)\n            return this.ret(-700 /* Penalty.NotStart */ - word.length, [direct, direct + this.pattern.length]);\n        if (adjacentTo == len)\n            return this.ret(-200 /* Penalty.CaseFold */ + -700 /* Penalty.NotStart */ - word.length, [adjacentStart, adjacentEnd]);\n        if (byWordTo == len)\n            return this.result(-100 /* Penalty.ByWord */ + (byWordFolded ? -200 /* Penalty.CaseFold */ : 0) + -700 /* Penalty.NotStart */ +\n                (wordAdjacent ? 0 : -1100 /* Penalty.Gap */), byWord, word);\n        return chars.length == 2 ? null\n            : this.result((any[0] ? -700 /* Penalty.NotStart */ : 0) + -200 /* Penalty.CaseFold */ + -1100 /* Penalty.Gap */, any, word);\n    }\n    result(score, positions, word) {\n        let result = [], i = 0;\n        for (let pos of positions) {\n            let to = pos + (this.astral ? (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointSize)((0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointAt)(word, pos)) : 1);\n            if (i && result[i - 1] == pos)\n                result[i - 1] = to;\n            else {\n                result[i++] = pos;\n                result[i++] = to;\n            }\n        }\n        return this.ret(score - word.length, result);\n    }\n}\nclass StrictMatcher {\n    constructor(pattern) {\n        this.pattern = pattern;\n        this.matched = [];\n        this.score = 0;\n        this.folded = pattern.toLowerCase();\n    }\n    match(word) {\n        if (word.length < this.pattern.length)\n            return null;\n        let start = word.slice(0, this.pattern.length);\n        let match = start == this.pattern ? 0 : start.toLowerCase() == this.folded ? -200 /* Penalty.CaseFold */ : null;\n        if (match == null)\n            return null;\n        this.matched = [0, start.length];\n        this.score = match + (word.length == this.pattern.length ? 0 : -100 /* Penalty.NotFull */);\n        return this;\n    }\n}\n\nconst completionConfig = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Facet.define({\n    combine(configs) {\n        return (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.combineConfig)(configs, {\n            activateOnTyping: true,\n            activateOnCompletion: () => false,\n            activateOnTypingDelay: 100,\n            selectOnOpen: true,\n            override: null,\n            closeOnBlur: true,\n            maxRenderedOptions: 100,\n            defaultKeymap: true,\n            tooltipClass: () => \"\",\n            optionClass: () => \"\",\n            aboveCursor: false,\n            icons: true,\n            addToOptions: [],\n            positionInfo: defaultPositionInfo,\n            filterStrict: false,\n            compareCompletions: (a, b) => a.label.localeCompare(b.label),\n            interactionDelay: 75,\n            updateSyncTime: 100\n        }, {\n            defaultKeymap: (a, b) => a && b,\n            closeOnBlur: (a, b) => a && b,\n            icons: (a, b) => a && b,\n            tooltipClass: (a, b) => c => joinClass(a(c), b(c)),\n            optionClass: (a, b) => c => joinClass(a(c), b(c)),\n            addToOptions: (a, b) => a.concat(b),\n            filterStrict: (a, b) => a || b,\n        });\n    }\n});\nfunction joinClass(a, b) {\n    return a ? b ? a + \" \" + b : a : b;\n}\nfunction defaultPositionInfo(view, list, option, info, space, tooltip) {\n    let rtl = view.textDirection == _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Direction.RTL, left = rtl, narrow = false;\n    let side = \"top\", offset, maxWidth;\n    let spaceLeft = list.left - space.left, spaceRight = space.right - list.right;\n    let infoWidth = info.right - info.left, infoHeight = info.bottom - info.top;\n    if (left && spaceLeft < Math.min(infoWidth, spaceRight))\n        left = false;\n    else if (!left && spaceRight < Math.min(infoWidth, spaceLeft))\n        left = true;\n    if (infoWidth <= (left ? spaceLeft : spaceRight)) {\n        offset = Math.max(space.top, Math.min(option.top, space.bottom - infoHeight)) - list.top;\n        maxWidth = Math.min(400 /* Info.Width */, left ? spaceLeft : spaceRight);\n    }\n    else {\n        narrow = true;\n        maxWidth = Math.min(400 /* Info.Width */, (rtl ? list.right : space.right - list.left) - 30 /* Info.Margin */);\n        let spaceBelow = space.bottom - list.bottom;\n        if (spaceBelow >= infoHeight || spaceBelow > list.top) { // Below the completion\n            offset = option.bottom - list.top;\n        }\n        else { // Above it\n            side = \"bottom\";\n            offset = list.bottom - option.top;\n        }\n    }\n    let scaleY = (list.bottom - list.top) / tooltip.offsetHeight;\n    let scaleX = (list.right - list.left) / tooltip.offsetWidth;\n    return {\n        style: `${side}: ${offset / scaleY}px; max-width: ${maxWidth / scaleX}px`,\n        class: \"cm-completionInfo-\" + (narrow ? (rtl ? \"left-narrow\" : \"right-narrow\") : left ? \"left\" : \"right\")\n    };\n}\n\nfunction optionContent(config) {\n    let content = config.addToOptions.slice();\n    if (config.icons)\n        content.push({\n            render(completion) {\n                let icon = document.createElement(\"div\");\n                icon.classList.add(\"cm-completionIcon\");\n                if (completion.type)\n                    icon.classList.add(...completion.type.split(/\\s+/g).map(cls => \"cm-completionIcon-\" + cls));\n                icon.setAttribute(\"aria-hidden\", \"true\");\n                return icon;\n            },\n            position: 20\n        });\n    content.push({\n        render(completion, _s, _v, match) {\n            let labelElt = document.createElement(\"span\");\n            labelElt.className = \"cm-completionLabel\";\n            let label = completion.displayLabel || completion.label, off = 0;\n            for (let j = 0; j < match.length;) {\n                let from = match[j++], to = match[j++];\n                if (from > off)\n                    labelElt.appendChild(document.createTextNode(label.slice(off, from)));\n                let span = labelElt.appendChild(document.createElement(\"span\"));\n                span.appendChild(document.createTextNode(label.slice(from, to)));\n                span.className = \"cm-completionMatchedText\";\n                off = to;\n            }\n            if (off < label.length)\n                labelElt.appendChild(document.createTextNode(label.slice(off)));\n            return labelElt;\n        },\n        position: 50\n    }, {\n        render(completion) {\n            if (!completion.detail)\n                return null;\n            let detailElt = document.createElement(\"span\");\n            detailElt.className = \"cm-completionDetail\";\n            detailElt.textContent = completion.detail;\n            return detailElt;\n        },\n        position: 80\n    });\n    return content.sort((a, b) => a.position - b.position).map(a => a.render);\n}\nfunction rangeAroundSelected(total, selected, max) {\n    if (total <= max)\n        return { from: 0, to: total };\n    if (selected < 0)\n        selected = 0;\n    if (selected <= (total >> 1)) {\n        let off = Math.floor(selected / max);\n        return { from: off * max, to: (off + 1) * max };\n    }\n    let off = Math.floor((total - selected) / max);\n    return { from: total - (off + 1) * max, to: total - off * max };\n}\nclass CompletionTooltip {\n    constructor(view, stateField, applyCompletion) {\n        this.view = view;\n        this.stateField = stateField;\n        this.applyCompletion = applyCompletion;\n        this.info = null;\n        this.infoDestroy = null;\n        this.placeInfoReq = {\n            read: () => this.measureInfo(),\n            write: (pos) => this.placeInfo(pos),\n            key: this\n        };\n        this.space = null;\n        this.currentClass = \"\";\n        let cState = view.state.field(stateField);\n        let { options, selected } = cState.open;\n        let config = view.state.facet(completionConfig);\n        this.optionContent = optionContent(config);\n        this.optionClass = config.optionClass;\n        this.tooltipClass = config.tooltipClass;\n        this.range = rangeAroundSelected(options.length, selected, config.maxRenderedOptions);\n        this.dom = document.createElement(\"div\");\n        this.dom.className = \"cm-tooltip-autocomplete\";\n        this.updateTooltipClass(view.state);\n        this.dom.addEventListener(\"mousedown\", (e) => {\n            let { options } = view.state.field(stateField).open;\n            for (let dom = e.target, match; dom && dom != this.dom; dom = dom.parentNode) {\n                if (dom.nodeName == \"LI\" && (match = /-(\\d+)$/.exec(dom.id)) && +match[1] < options.length) {\n                    this.applyCompletion(view, options[+match[1]]);\n                    e.preventDefault();\n                    return;\n                }\n            }\n        });\n        this.dom.addEventListener(\"focusout\", (e) => {\n            let state = view.state.field(this.stateField, false);\n            if (state && state.tooltip && view.state.facet(completionConfig).closeOnBlur &&\n                e.relatedTarget != view.contentDOM)\n                view.dispatch({ effects: closeCompletionEffect.of(null) });\n        });\n        this.showOptions(options, cState.id);\n    }\n    mount() { this.updateSel(); }\n    showOptions(options, id) {\n        if (this.list)\n            this.list.remove();\n        this.list = this.dom.appendChild(this.createListBox(options, id, this.range));\n        this.list.addEventListener(\"scroll\", () => {\n            if (this.info)\n                this.view.requestMeasure(this.placeInfoReq);\n        });\n    }\n    update(update) {\n        var _a;\n        let cState = update.state.field(this.stateField);\n        let prevState = update.startState.field(this.stateField);\n        this.updateTooltipClass(update.state);\n        if (cState != prevState) {\n            let { options, selected, disabled } = cState.open;\n            if (!prevState.open || prevState.open.options != options) {\n                this.range = rangeAroundSelected(options.length, selected, update.state.facet(completionConfig).maxRenderedOptions);\n                this.showOptions(options, cState.id);\n            }\n            this.updateSel();\n            if (disabled != ((_a = prevState.open) === null || _a === void 0 ? void 0 : _a.disabled))\n                this.dom.classList.toggle(\"cm-tooltip-autocomplete-disabled\", !!disabled);\n        }\n    }\n    updateTooltipClass(state) {\n        let cls = this.tooltipClass(state);\n        if (cls != this.currentClass) {\n            for (let c of this.currentClass.split(\" \"))\n                if (c)\n                    this.dom.classList.remove(c);\n            for (let c of cls.split(\" \"))\n                if (c)\n                    this.dom.classList.add(c);\n            this.currentClass = cls;\n        }\n    }\n    positioned(space) {\n        this.space = space;\n        if (this.info)\n            this.view.requestMeasure(this.placeInfoReq);\n    }\n    updateSel() {\n        let cState = this.view.state.field(this.stateField), open = cState.open;\n        if (open.selected > -1 && open.selected < this.range.from || open.selected >= this.range.to) {\n            this.range = rangeAroundSelected(open.options.length, open.selected, this.view.state.facet(completionConfig).maxRenderedOptions);\n            this.showOptions(open.options, cState.id);\n        }\n        if (this.updateSelectedOption(open.selected)) {\n            this.destroyInfo();\n            let { completion } = open.options[open.selected];\n            let { info } = completion;\n            if (!info)\n                return;\n            let infoResult = typeof info === \"string\" ? document.createTextNode(info) : info(completion);\n            if (!infoResult)\n                return;\n            if (\"then\" in infoResult) {\n                infoResult.then(obj => {\n                    if (obj && this.view.state.field(this.stateField, false) == cState)\n                        this.addInfoPane(obj, completion);\n                }).catch(e => (0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.logException)(this.view.state, e, \"completion info\"));\n            }\n            else {\n                this.addInfoPane(infoResult, completion);\n            }\n        }\n    }\n    addInfoPane(content, completion) {\n        this.destroyInfo();\n        let wrap = this.info = document.createElement(\"div\");\n        wrap.className = \"cm-tooltip cm-completionInfo\";\n        if (content.nodeType != null) {\n            wrap.appendChild(content);\n            this.infoDestroy = null;\n        }\n        else {\n            let { dom, destroy } = content;\n            wrap.appendChild(dom);\n            this.infoDestroy = destroy || null;\n        }\n        this.dom.appendChild(wrap);\n        this.view.requestMeasure(this.placeInfoReq);\n    }\n    updateSelectedOption(selected) {\n        let set = null;\n        for (let opt = this.list.firstChild, i = this.range.from; opt; opt = opt.nextSibling, i++) {\n            if (opt.nodeName != \"LI\" || !opt.id) {\n                i--; // A section header\n            }\n            else if (i == selected) {\n                if (!opt.hasAttribute(\"aria-selected\")) {\n                    opt.setAttribute(\"aria-selected\", \"true\");\n                    set = opt;\n                }\n            }\n            else {\n                if (opt.hasAttribute(\"aria-selected\"))\n                    opt.removeAttribute(\"aria-selected\");\n            }\n        }\n        if (set)\n            scrollIntoView(this.list, set);\n        return set;\n    }\n    measureInfo() {\n        let sel = this.dom.querySelector(\"[aria-selected]\");\n        if (!sel || !this.info)\n            return null;\n        let listRect = this.dom.getBoundingClientRect();\n        let infoRect = this.info.getBoundingClientRect();\n        let selRect = sel.getBoundingClientRect();\n        let space = this.space;\n        if (!space) {\n            let docElt = this.dom.ownerDocument.documentElement;\n            space = { left: 0, top: 0, right: docElt.clientWidth, bottom: docElt.clientHeight };\n        }\n        if (selRect.top > Math.min(space.bottom, listRect.bottom) - 10 ||\n            selRect.bottom < Math.max(space.top, listRect.top) + 10)\n            return null;\n        return this.view.state.facet(completionConfig).positionInfo(this.view, listRect, selRect, infoRect, space, this.dom);\n    }\n    placeInfo(pos) {\n        if (this.info) {\n            if (pos) {\n                if (pos.style)\n                    this.info.style.cssText = pos.style;\n                this.info.className = \"cm-tooltip cm-completionInfo \" + (pos.class || \"\");\n            }\n            else {\n                this.info.style.cssText = \"top: -1e6px\";\n            }\n        }\n    }\n    createListBox(options, id, range) {\n        const ul = document.createElement(\"ul\");\n        ul.id = id;\n        ul.setAttribute(\"role\", \"listbox\");\n        ul.setAttribute(\"aria-expanded\", \"true\");\n        ul.setAttribute(\"aria-label\", this.view.state.phrase(\"Completions\"));\n        ul.addEventListener(\"mousedown\", e => {\n            // Prevent focus change when clicking the scrollbar\n            if (e.target == ul)\n                e.preventDefault();\n        });\n        let curSection = null;\n        for (let i = range.from; i < range.to; i++) {\n            let { completion, match } = options[i], { section } = completion;\n            if (section) {\n                let name = typeof section == \"string\" ? section : section.name;\n                if (name != curSection && (i > range.from || range.from == 0)) {\n                    curSection = name;\n                    if (typeof section != \"string\" && section.header) {\n                        ul.appendChild(section.header(section));\n                    }\n                    else {\n                        let header = ul.appendChild(document.createElement(\"completion-section\"));\n                        header.textContent = name;\n                    }\n                }\n            }\n            const li = ul.appendChild(document.createElement(\"li\"));\n            li.id = id + \"-\" + i;\n            li.setAttribute(\"role\", \"option\");\n            let cls = this.optionClass(completion);\n            if (cls)\n                li.className = cls;\n            for (let source of this.optionContent) {\n                let node = source(completion, this.view.state, this.view, match);\n                if (node)\n                    li.appendChild(node);\n            }\n        }\n        if (range.from)\n            ul.classList.add(\"cm-completionListIncompleteTop\");\n        if (range.to < options.length)\n            ul.classList.add(\"cm-completionListIncompleteBottom\");\n        return ul;\n    }\n    destroyInfo() {\n        if (this.info) {\n            if (this.infoDestroy)\n                this.infoDestroy();\n            this.info.remove();\n            this.info = null;\n        }\n    }\n    destroy() {\n        this.destroyInfo();\n    }\n}\nfunction completionTooltip(stateField, applyCompletion) {\n    return (view) => new CompletionTooltip(view, stateField, applyCompletion);\n}\nfunction scrollIntoView(container, element) {\n    let parent = container.getBoundingClientRect();\n    let self = element.getBoundingClientRect();\n    let scaleY = parent.height / container.offsetHeight;\n    if (self.top < parent.top)\n        container.scrollTop -= (parent.top - self.top) / scaleY;\n    else if (self.bottom > parent.bottom)\n        container.scrollTop += (self.bottom - parent.bottom) / scaleY;\n}\n\n// Used to pick a preferred option when two options with the same\n// label occur in the result.\nfunction score(option) {\n    return (option.boost || 0) * 100 + (option.apply ? 10 : 0) + (option.info ? 5 : 0) +\n        (option.type ? 1 : 0);\n}\nfunction sortOptions(active, state) {\n    let options = [];\n    let sections = null;\n    let addOption = (option) => {\n        options.push(option);\n        let { section } = option.completion;\n        if (section) {\n            if (!sections)\n                sections = [];\n            let name = typeof section == \"string\" ? section : section.name;\n            if (!sections.some(s => s.name == name))\n                sections.push(typeof section == \"string\" ? { name } : section);\n        }\n    };\n    let conf = state.facet(completionConfig);\n    for (let a of active)\n        if (a.hasResult()) {\n            let getMatch = a.result.getMatch;\n            if (a.result.filter === false) {\n                for (let option of a.result.options) {\n                    addOption(new Option(option, a.source, getMatch ? getMatch(option) : [], 1e9 - options.length));\n                }\n            }\n            else {\n                let pattern = state.sliceDoc(a.from, a.to), match;\n                let matcher = conf.filterStrict ? new StrictMatcher(pattern) : new FuzzyMatcher(pattern);\n                for (let option of a.result.options)\n                    if (match = matcher.match(option.label)) {\n                        let matched = !option.displayLabel ? match.matched : getMatch ? getMatch(option, match.matched) : [];\n                        addOption(new Option(option, a.source, matched, match.score + (option.boost || 0)));\n                    }\n            }\n        }\n    if (sections) {\n        let sectionOrder = Object.create(null), pos = 0;\n        let cmp = (a, b) => { var _a, _b; return ((_a = a.rank) !== null && _a !== void 0 ? _a : 1e9) - ((_b = b.rank) !== null && _b !== void 0 ? _b : 1e9) || (a.name < b.name ? -1 : 1); };\n        for (let s of sections.sort(cmp)) {\n            pos -= 1e5;\n            sectionOrder[s.name] = pos;\n        }\n        for (let option of options) {\n            let { section } = option.completion;\n            if (section)\n                option.score += sectionOrder[typeof section == \"string\" ? section : section.name];\n        }\n    }\n    let result = [], prev = null;\n    let compare = conf.compareCompletions;\n    for (let opt of options.sort((a, b) => (b.score - a.score) || compare(a.completion, b.completion))) {\n        let cur = opt.completion;\n        if (!prev || prev.label != cur.label || prev.detail != cur.detail ||\n            (prev.type != null && cur.type != null && prev.type != cur.type) ||\n            prev.apply != cur.apply || prev.boost != cur.boost)\n            result.push(opt);\n        else if (score(opt.completion) > score(prev))\n            result[result.length - 1] = opt;\n        prev = opt.completion;\n    }\n    return result;\n}\nclass CompletionDialog {\n    constructor(options, attrs, tooltip, timestamp, selected, disabled) {\n        this.options = options;\n        this.attrs = attrs;\n        this.tooltip = tooltip;\n        this.timestamp = timestamp;\n        this.selected = selected;\n        this.disabled = disabled;\n    }\n    setSelected(selected, id) {\n        return selected == this.selected || selected >= this.options.length ? this\n            : new CompletionDialog(this.options, makeAttrs(id, selected), this.tooltip, this.timestamp, selected, this.disabled);\n    }\n    static build(active, state, id, prev, conf, didSetActive) {\n        if (prev && !didSetActive && active.some(s => s.isPending))\n            return prev.setDisabled();\n        let options = sortOptions(active, state);\n        if (!options.length)\n            return prev && active.some(a => a.isPending) ? prev.setDisabled() : null;\n        let selected = state.facet(completionConfig).selectOnOpen ? 0 : -1;\n        if (prev && prev.selected != selected && prev.selected != -1) {\n            let selectedValue = prev.options[prev.selected].completion;\n            for (let i = 0; i < options.length; i++)\n                if (options[i].completion == selectedValue) {\n                    selected = i;\n                    break;\n                }\n        }\n        return new CompletionDialog(options, makeAttrs(id, selected), {\n            pos: active.reduce((a, b) => b.hasResult() ? Math.min(a, b.from) : a, 1e8),\n            create: createTooltip,\n            above: conf.aboveCursor,\n        }, prev ? prev.timestamp : Date.now(), selected, false);\n    }\n    map(changes) {\n        return new CompletionDialog(this.options, this.attrs, Object.assign(Object.assign({}, this.tooltip), { pos: changes.mapPos(this.tooltip.pos) }), this.timestamp, this.selected, this.disabled);\n    }\n    setDisabled() {\n        return new CompletionDialog(this.options, this.attrs, this.tooltip, this.timestamp, this.selected, true);\n    }\n}\nclass CompletionState {\n    constructor(active, id, open) {\n        this.active = active;\n        this.id = id;\n        this.open = open;\n    }\n    static start() {\n        return new CompletionState(none, \"cm-ac-\" + Math.floor(Math.random() * 2e6).toString(36), null);\n    }\n    update(tr) {\n        let { state } = tr, conf = state.facet(completionConfig);\n        let sources = conf.override ||\n            state.languageDataAt(\"autocomplete\", cur(state)).map(asSource);\n        let active = sources.map(source => {\n            let value = this.active.find(s => s.source == source) ||\n                new ActiveSource(source, this.active.some(a => a.state != 0 /* State.Inactive */) ? 1 /* State.Pending */ : 0 /* State.Inactive */);\n            return value.update(tr, conf);\n        });\n        if (active.length == this.active.length && active.every((a, i) => a == this.active[i]))\n            active = this.active;\n        let open = this.open, didSet = tr.effects.some(e => e.is(setActiveEffect));\n        if (open && tr.docChanged)\n            open = open.map(tr.changes);\n        if (tr.selection || active.some(a => a.hasResult() && tr.changes.touchesRange(a.from, a.to)) ||\n            !sameResults(active, this.active) || didSet)\n            open = CompletionDialog.build(active, state, this.id, open, conf, didSet);\n        else if (open && open.disabled && !active.some(a => a.isPending))\n            open = null;\n        if (!open && active.every(a => !a.isPending) && active.some(a => a.hasResult()))\n            active = active.map(a => a.hasResult() ? new ActiveSource(a.source, 0 /* State.Inactive */) : a);\n        for (let effect of tr.effects)\n            if (effect.is(setSelectedEffect))\n                open = open && open.setSelected(effect.value, this.id);\n        return active == this.active && open == this.open ? this : new CompletionState(active, this.id, open);\n    }\n    get tooltip() { return this.open ? this.open.tooltip : null; }\n    get attrs() { return this.open ? this.open.attrs : this.active.length ? baseAttrs : noAttrs; }\n}\nfunction sameResults(a, b) {\n    if (a == b)\n        return true;\n    for (let iA = 0, iB = 0;;) {\n        while (iA < a.length && !a[iA].hasResult())\n            iA++;\n        while (iB < b.length && !b[iB].hasResult())\n            iB++;\n        let endA = iA == a.length, endB = iB == b.length;\n        if (endA || endB)\n            return endA == endB;\n        if (a[iA++].result != b[iB++].result)\n            return false;\n    }\n}\nconst baseAttrs = {\n    \"aria-autocomplete\": \"list\"\n};\nconst noAttrs = {};\nfunction makeAttrs(id, selected) {\n    let result = {\n        \"aria-autocomplete\": \"list\",\n        \"aria-haspopup\": \"listbox\",\n        \"aria-controls\": id\n    };\n    if (selected > -1)\n        result[\"aria-activedescendant\"] = id + \"-\" + selected;\n    return result;\n}\nconst none = [];\nfunction getUpdateType(tr, conf) {\n    if (tr.isUserEvent(\"input.complete\")) {\n        let completion = tr.annotation(pickedCompletion);\n        if (completion && conf.activateOnCompletion(completion))\n            return 4 /* UpdateType.Activate */ | 8 /* UpdateType.Reset */;\n    }\n    let typing = tr.isUserEvent(\"input.type\");\n    return typing && conf.activateOnTyping ? 4 /* UpdateType.Activate */ | 1 /* UpdateType.Typing */\n        : typing ? 1 /* UpdateType.Typing */\n            : tr.isUserEvent(\"delete.backward\") ? 2 /* UpdateType.Backspacing */\n                : tr.selection ? 8 /* UpdateType.Reset */\n                    : tr.docChanged ? 16 /* UpdateType.ResetIfTouching */ : 0 /* UpdateType.None */;\n}\nclass ActiveSource {\n    constructor(source, state, explicit = false) {\n        this.source = source;\n        this.state = state;\n        this.explicit = explicit;\n    }\n    hasResult() { return false; }\n    get isPending() { return this.state == 1 /* State.Pending */; }\n    update(tr, conf) {\n        let type = getUpdateType(tr, conf), value = this;\n        if ((type & 8 /* UpdateType.Reset */) || (type & 16 /* UpdateType.ResetIfTouching */) && this.touches(tr))\n            value = new ActiveSource(value.source, 0 /* State.Inactive */);\n        if ((type & 4 /* UpdateType.Activate */) && value.state == 0 /* State.Inactive */)\n            value = new ActiveSource(this.source, 1 /* State.Pending */);\n        value = value.updateFor(tr, type);\n        for (let effect of tr.effects) {\n            if (effect.is(startCompletionEffect))\n                value = new ActiveSource(value.source, 1 /* State.Pending */, effect.value);\n            else if (effect.is(closeCompletionEffect))\n                value = new ActiveSource(value.source, 0 /* State.Inactive */);\n            else if (effect.is(setActiveEffect))\n                for (let active of effect.value)\n                    if (active.source == value.source)\n                        value = active;\n        }\n        return value;\n    }\n    updateFor(tr, type) { return this.map(tr.changes); }\n    map(changes) { return this; }\n    touches(tr) {\n        return tr.changes.touchesRange(cur(tr.state));\n    }\n}\nclass ActiveResult extends ActiveSource {\n    constructor(source, explicit, limit, result, from, to) {\n        super(source, 3 /* State.Result */, explicit);\n        this.limit = limit;\n        this.result = result;\n        this.from = from;\n        this.to = to;\n    }\n    hasResult() { return true; }\n    updateFor(tr, type) {\n        var _a;\n        if (!(type & 3 /* UpdateType.SimpleInteraction */))\n            return this.map(tr.changes);\n        let result = this.result;\n        if (result.map && !tr.changes.empty)\n            result = result.map(result, tr.changes);\n        let from = tr.changes.mapPos(this.from), to = tr.changes.mapPos(this.to, 1);\n        let pos = cur(tr.state);\n        if (pos > to || !result ||\n            (type & 2 /* UpdateType.Backspacing */) && (cur(tr.startState) == this.from || pos < this.limit))\n            return new ActiveSource(this.source, type & 4 /* UpdateType.Activate */ ? 1 /* State.Pending */ : 0 /* State.Inactive */);\n        let limit = tr.changes.mapPos(this.limit);\n        if (checkValid(result.validFor, tr.state, from, to))\n            return new ActiveResult(this.source, this.explicit, limit, result, from, to);\n        if (result.update &&\n            (result = result.update(result, from, to, new CompletionContext(tr.state, pos, false))))\n            return new ActiveResult(this.source, this.explicit, limit, result, result.from, (_a = result.to) !== null && _a !== void 0 ? _a : cur(tr.state));\n        return new ActiveSource(this.source, 1 /* State.Pending */, this.explicit);\n    }\n    map(mapping) {\n        if (mapping.empty)\n            return this;\n        let result = this.result.map ? this.result.map(this.result, mapping) : this.result;\n        if (!result)\n            return new ActiveSource(this.source, 0 /* State.Inactive */);\n        return new ActiveResult(this.source, this.explicit, mapping.mapPos(this.limit), this.result, mapping.mapPos(this.from), mapping.mapPos(this.to, 1));\n    }\n    touches(tr) {\n        return tr.changes.touchesRange(this.from, this.to);\n    }\n}\nfunction checkValid(validFor, state, from, to) {\n    if (!validFor)\n        return false;\n    let text = state.sliceDoc(from, to);\n    return typeof validFor == \"function\" ? validFor(text, from, to, state) : ensureAnchor(validFor, true).test(text);\n}\nconst setActiveEffect = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define({\n    map(sources, mapping) { return sources.map(s => s.map(mapping)); }\n});\nconst setSelectedEffect = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define();\nconst completionState = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateField.define({\n    create() { return CompletionState.start(); },\n    update(value, tr) { return value.update(tr); },\n    provide: f => [\n        _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.showTooltip.from(f, val => val.tooltip),\n        _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.contentAttributes.from(f, state => state.attrs)\n    ]\n});\nfunction applyCompletion(view, option) {\n    const apply = option.completion.apply || option.completion.label;\n    let result = view.state.field(completionState).active.find(a => a.source == option.source);\n    if (!(result instanceof ActiveResult))\n        return false;\n    if (typeof apply == \"string\")\n        view.dispatch(Object.assign(Object.assign({}, insertCompletionText(view.state, apply, result.from, result.to)), { annotations: pickedCompletion.of(option.completion) }));\n    else\n        apply(view, option.completion, result.from, result.to);\n    return true;\n}\nconst createTooltip = /*@__PURE__*/completionTooltip(completionState, applyCompletion);\n\n/**\nReturns a command that moves the completion selection forward or\nbackward by the given amount.\n*/\nfunction moveCompletionSelection(forward, by = \"option\") {\n    return (view) => {\n        let cState = view.state.field(completionState, false);\n        if (!cState || !cState.open || cState.open.disabled ||\n            Date.now() - cState.open.timestamp < view.state.facet(completionConfig).interactionDelay)\n            return false;\n        let step = 1, tooltip;\n        if (by == \"page\" && (tooltip = (0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.getTooltip)(view, cState.open.tooltip)))\n            step = Math.max(2, Math.floor(tooltip.dom.offsetHeight /\n                tooltip.dom.querySelector(\"li\").offsetHeight) - 1);\n        let { length } = cState.open.options;\n        let selected = cState.open.selected > -1 ? cState.open.selected + step * (forward ? 1 : -1) : forward ? 0 : length - 1;\n        if (selected < 0)\n            selected = by == \"page\" ? 0 : length - 1;\n        else if (selected >= length)\n            selected = by == \"page\" ? length - 1 : 0;\n        view.dispatch({ effects: setSelectedEffect.of(selected) });\n        return true;\n    };\n}\n/**\nAccept the current completion.\n*/\nconst acceptCompletion = (view) => {\n    let cState = view.state.field(completionState, false);\n    if (view.state.readOnly || !cState || !cState.open || cState.open.selected < 0 || cState.open.disabled ||\n        Date.now() - cState.open.timestamp < view.state.facet(completionConfig).interactionDelay)\n        return false;\n    return applyCompletion(view, cState.open.options[cState.open.selected]);\n};\n/**\nExplicitly start autocompletion.\n*/\nconst startCompletion = (view) => {\n    let cState = view.state.field(completionState, false);\n    if (!cState)\n        return false;\n    view.dispatch({ effects: startCompletionEffect.of(true) });\n    return true;\n};\n/**\nClose the currently active completion.\n*/\nconst closeCompletion = (view) => {\n    let cState = view.state.field(completionState, false);\n    if (!cState || !cState.active.some(a => a.state != 0 /* State.Inactive */))\n        return false;\n    view.dispatch({ effects: closeCompletionEffect.of(null) });\n    return true;\n};\nclass RunningQuery {\n    constructor(active, context) {\n        this.active = active;\n        this.context = context;\n        this.time = Date.now();\n        this.updates = [];\n        // Note that 'undefined' means 'not done yet', whereas 'null' means\n        // 'query returned null'.\n        this.done = undefined;\n    }\n}\nconst MaxUpdateCount = 50, MinAbortTime = 1000;\nconst completionPlugin = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.view = view;\n        this.debounceUpdate = -1;\n        this.running = [];\n        this.debounceAccept = -1;\n        this.pendingStart = false;\n        this.composing = 0 /* CompositionState.None */;\n        for (let active of view.state.field(completionState).active)\n            if (active.isPending)\n                this.startQuery(active);\n    }\n    update(update) {\n        let cState = update.state.field(completionState);\n        let conf = update.state.facet(completionConfig);\n        if (!update.selectionSet && !update.docChanged && update.startState.field(completionState) == cState)\n            return;\n        let doesReset = update.transactions.some(tr => {\n            let type = getUpdateType(tr, conf);\n            return (type & 8 /* UpdateType.Reset */) || (tr.selection || tr.docChanged) && !(type & 3 /* UpdateType.SimpleInteraction */);\n        });\n        for (let i = 0; i < this.running.length; i++) {\n            let query = this.running[i];\n            if (doesReset ||\n                query.context.abortOnDocChange && update.docChanged ||\n                query.updates.length + update.transactions.length > MaxUpdateCount && Date.now() - query.time > MinAbortTime) {\n                for (let handler of query.context.abortListeners) {\n                    try {\n                        handler();\n                    }\n                    catch (e) {\n                        (0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.logException)(this.view.state, e);\n                    }\n                }\n                query.context.abortListeners = null;\n                this.running.splice(i--, 1);\n            }\n            else {\n                query.updates.push(...update.transactions);\n            }\n        }\n        if (this.debounceUpdate > -1)\n            clearTimeout(this.debounceUpdate);\n        if (update.transactions.some(tr => tr.effects.some(e => e.is(startCompletionEffect))))\n            this.pendingStart = true;\n        let delay = this.pendingStart ? 50 : conf.activateOnTypingDelay;\n        this.debounceUpdate = cState.active.some(a => a.isPending && !this.running.some(q => q.active.source == a.source))\n            ? setTimeout(() => this.startUpdate(), delay) : -1;\n        if (this.composing != 0 /* CompositionState.None */)\n            for (let tr of update.transactions) {\n                if (tr.isUserEvent(\"input.type\"))\n                    this.composing = 2 /* CompositionState.Changed */;\n                else if (this.composing == 2 /* CompositionState.Changed */ && tr.selection)\n                    this.composing = 3 /* CompositionState.ChangedAndMoved */;\n            }\n    }\n    startUpdate() {\n        this.debounceUpdate = -1;\n        this.pendingStart = false;\n        let { state } = this.view, cState = state.field(completionState);\n        for (let active of cState.active) {\n            if (active.isPending && !this.running.some(r => r.active.source == active.source))\n                this.startQuery(active);\n        }\n        if (this.running.length && cState.open && cState.open.disabled)\n            this.debounceAccept = setTimeout(() => this.accept(), this.view.state.facet(completionConfig).updateSyncTime);\n    }\n    startQuery(active) {\n        let { state } = this.view, pos = cur(state);\n        let context = new CompletionContext(state, pos, active.explicit, this.view);\n        let pending = new RunningQuery(active, context);\n        this.running.push(pending);\n        Promise.resolve(active.source(context)).then(result => {\n            if (!pending.context.aborted) {\n                pending.done = result || null;\n                this.scheduleAccept();\n            }\n        }, err => {\n            this.view.dispatch({ effects: closeCompletionEffect.of(null) });\n            (0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.logException)(this.view.state, err);\n        });\n    }\n    scheduleAccept() {\n        if (this.running.every(q => q.done !== undefined))\n            this.accept();\n        else if (this.debounceAccept < 0)\n            this.debounceAccept = setTimeout(() => this.accept(), this.view.state.facet(completionConfig).updateSyncTime);\n    }\n    // For each finished query in this.running, try to create a result\n    // or, if appropriate, restart the query.\n    accept() {\n        var _a;\n        if (this.debounceAccept > -1)\n            clearTimeout(this.debounceAccept);\n        this.debounceAccept = -1;\n        let updated = [];\n        let conf = this.view.state.facet(completionConfig), cState = this.view.state.field(completionState);\n        for (let i = 0; i < this.running.length; i++) {\n            let query = this.running[i];\n            if (query.done === undefined)\n                continue;\n            this.running.splice(i--, 1);\n            if (query.done) {\n                let pos = cur(query.updates.length ? query.updates[0].startState : this.view.state);\n                let limit = Math.min(pos, query.done.from + (query.active.explicit ? 0 : 1));\n                let active = new ActiveResult(query.active.source, query.active.explicit, limit, query.done, query.done.from, (_a = query.done.to) !== null && _a !== void 0 ? _a : pos);\n                // Replay the transactions that happened since the start of\n                // the request and see if that preserves the result\n                for (let tr of query.updates)\n                    active = active.update(tr, conf);\n                if (active.hasResult()) {\n                    updated.push(active);\n                    continue;\n                }\n            }\n            let current = cState.active.find(a => a.source == query.active.source);\n            if (current && current.isPending) {\n                if (query.done == null) {\n                    // Explicitly failed. Should clear the pending status if it\n                    // hasn't been re-set in the meantime.\n                    let active = new ActiveSource(query.active.source, 0 /* State.Inactive */);\n                    for (let tr of query.updates)\n                        active = active.update(tr, conf);\n                    if (!active.isPending)\n                        updated.push(active);\n                }\n                else {\n                    // Cleared by subsequent transactions. Restart.\n                    this.startQuery(current);\n                }\n            }\n        }\n        if (updated.length || cState.open && cState.open.disabled)\n            this.view.dispatch({ effects: setActiveEffect.of(updated) });\n    }\n}, {\n    eventHandlers: {\n        blur(event) {\n            let state = this.view.state.field(completionState, false);\n            if (state && state.tooltip && this.view.state.facet(completionConfig).closeOnBlur) {\n                let dialog = state.open && (0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.getTooltip)(this.view, state.open.tooltip);\n                if (!dialog || !dialog.dom.contains(event.relatedTarget))\n                    setTimeout(() => this.view.dispatch({ effects: closeCompletionEffect.of(null) }), 10);\n            }\n        },\n        compositionstart() {\n            this.composing = 1 /* CompositionState.Started */;\n        },\n        compositionend() {\n            if (this.composing == 3 /* CompositionState.ChangedAndMoved */) {\n                // Safari fires compositionend events synchronously, possibly\n                // from inside an update, so dispatch asynchronously to avoid reentrancy\n                setTimeout(() => this.view.dispatch({ effects: startCompletionEffect.of(false) }), 20);\n            }\n            this.composing = 0 /* CompositionState.None */;\n        }\n    }\n});\nconst windows = typeof navigator == \"object\" && /*@__PURE__*//Win/.test(navigator.platform);\nconst commitCharacters = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Prec.highest(/*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.domEventHandlers({\n    keydown(event, view) {\n        let field = view.state.field(completionState, false);\n        if (!field || !field.open || field.open.disabled || field.open.selected < 0 ||\n            event.key.length > 1 || event.ctrlKey && !(windows && event.altKey) || event.metaKey)\n            return false;\n        let option = field.open.options[field.open.selected];\n        let result = field.active.find(a => a.source == option.source);\n        let commitChars = option.completion.commitCharacters || result.result.commitCharacters;\n        if (commitChars && commitChars.indexOf(event.key) > -1)\n            applyCompletion(view, option);\n        return false;\n    }\n}));\n\nconst baseTheme = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.baseTheme({\n    \".cm-tooltip.cm-tooltip-autocomplete\": {\n        \"& > ul\": {\n            fontFamily: \"monospace\",\n            whiteSpace: \"nowrap\",\n            overflow: \"hidden auto\",\n            maxWidth_fallback: \"700px\",\n            maxWidth: \"min(700px, 95vw)\",\n            minWidth: \"250px\",\n            maxHeight: \"10em\",\n            height: \"100%\",\n            listStyle: \"none\",\n            margin: 0,\n            padding: 0,\n            \"& > li, & > completion-section\": {\n                padding: \"1px 3px\",\n                lineHeight: 1.2\n            },\n            \"& > li\": {\n                overflowX: \"hidden\",\n                textOverflow: \"ellipsis\",\n                cursor: \"pointer\"\n            },\n            \"& > completion-section\": {\n                display: \"list-item\",\n                borderBottom: \"1px solid silver\",\n                paddingLeft: \"0.5em\",\n                opacity: 0.7\n            }\n        }\n    },\n    \"&light .cm-tooltip-autocomplete ul li[aria-selected]\": {\n        background: \"#17c\",\n        color: \"white\",\n    },\n    \"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]\": {\n        background: \"#777\",\n    },\n    \"&dark .cm-tooltip-autocomplete ul li[aria-selected]\": {\n        background: \"#347\",\n        color: \"white\",\n    },\n    \"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]\": {\n        background: \"#444\",\n    },\n    \".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after\": {\n        content: '\"···\"',\n        opacity: 0.5,\n        display: \"block\",\n        textAlign: \"center\"\n    },\n    \".cm-tooltip.cm-completionInfo\": {\n        position: \"absolute\",\n        padding: \"3px 9px\",\n        width: \"max-content\",\n        maxWidth: `${400 /* Info.Width */}px`,\n        boxSizing: \"border-box\",\n        whiteSpace: \"pre-line\"\n    },\n    \".cm-completionInfo.cm-completionInfo-left\": { right: \"100%\" },\n    \".cm-completionInfo.cm-completionInfo-right\": { left: \"100%\" },\n    \".cm-completionInfo.cm-completionInfo-left-narrow\": { right: `${30 /* Info.Margin */}px` },\n    \".cm-completionInfo.cm-completionInfo-right-narrow\": { left: `${30 /* Info.Margin */}px` },\n    \"&light .cm-snippetField\": { backgroundColor: \"#00000022\" },\n    \"&dark .cm-snippetField\": { backgroundColor: \"#ffffff22\" },\n    \".cm-snippetFieldPosition\": {\n        verticalAlign: \"text-top\",\n        width: 0,\n        height: \"1.15em\",\n        display: \"inline-block\",\n        margin: \"0 -0.7px -.7em\",\n        borderLeft: \"1.4px dotted #888\"\n    },\n    \".cm-completionMatchedText\": {\n        textDecoration: \"underline\"\n    },\n    \".cm-completionDetail\": {\n        marginLeft: \"0.5em\",\n        fontStyle: \"italic\"\n    },\n    \".cm-completionIcon\": {\n        fontSize: \"90%\",\n        width: \".8em\",\n        display: \"inline-block\",\n        textAlign: \"center\",\n        paddingRight: \".6em\",\n        opacity: \"0.6\",\n        boxSizing: \"content-box\"\n    },\n    \".cm-completionIcon-function, .cm-completionIcon-method\": {\n        \"&:after\": { content: \"'ƒ'\" }\n    },\n    \".cm-completionIcon-class\": {\n        \"&:after\": { content: \"'○'\" }\n    },\n    \".cm-completionIcon-interface\": {\n        \"&:after\": { content: \"'◌'\" }\n    },\n    \".cm-completionIcon-variable\": {\n        \"&:after\": { content: \"'𝑥'\" }\n    },\n    \".cm-completionIcon-constant\": {\n        \"&:after\": { content: \"'𝐶'\" }\n    },\n    \".cm-completionIcon-type\": {\n        \"&:after\": { content: \"'𝑡'\" }\n    },\n    \".cm-completionIcon-enum\": {\n        \"&:after\": { content: \"'∪'\" }\n    },\n    \".cm-completionIcon-property\": {\n        \"&:after\": { content: \"'□'\" }\n    },\n    \".cm-completionIcon-keyword\": {\n        \"&:after\": { content: \"'🔑\\uFE0E'\" } // Disable emoji rendering\n    },\n    \".cm-completionIcon-namespace\": {\n        \"&:after\": { content: \"'▢'\" }\n    },\n    \".cm-completionIcon-text\": {\n        \"&:after\": { content: \"'abc'\", fontSize: \"50%\", verticalAlign: \"middle\" }\n    }\n});\n\nclass FieldPos {\n    constructor(field, line, from, to) {\n        this.field = field;\n        this.line = line;\n        this.from = from;\n        this.to = to;\n    }\n}\nclass FieldRange {\n    constructor(field, from, to) {\n        this.field = field;\n        this.from = from;\n        this.to = to;\n    }\n    map(changes) {\n        let from = changes.mapPos(this.from, -1, _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.MapMode.TrackDel);\n        let to = changes.mapPos(this.to, 1, _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.MapMode.TrackDel);\n        return from == null || to == null ? null : new FieldRange(this.field, from, to);\n    }\n}\nclass Snippet {\n    constructor(lines, fieldPositions) {\n        this.lines = lines;\n        this.fieldPositions = fieldPositions;\n    }\n    instantiate(state, pos) {\n        let text = [], lineStart = [pos];\n        let lineObj = state.doc.lineAt(pos), baseIndent = /^\\s*/.exec(lineObj.text)[0];\n        for (let line of this.lines) {\n            if (text.length) {\n                let indent = baseIndent, tabs = /^\\t*/.exec(line)[0].length;\n                for (let i = 0; i < tabs; i++)\n                    indent += state.facet(_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.indentUnit);\n                lineStart.push(pos + indent.length - tabs);\n                line = indent + line.slice(tabs);\n            }\n            text.push(line);\n            pos += line.length + 1;\n        }\n        let ranges = this.fieldPositions.map(pos => new FieldRange(pos.field, lineStart[pos.line] + pos.from, lineStart[pos.line] + pos.to));\n        return { text, ranges };\n    }\n    static parse(template) {\n        let fields = [];\n        let lines = [], positions = [], m;\n        for (let line of template.split(/\\r\\n?|\\n/)) {\n            while (m = /[#$]\\{(?:(\\d+)(?::([^}]*))?|((?:\\\\[{}]|[^}])*))\\}/.exec(line)) {\n                let seq = m[1] ? +m[1] : null, rawName = m[2] || m[3] || \"\", found = -1;\n                let name = rawName.replace(/\\\\[{}]/g, m => m[1]);\n                for (let i = 0; i < fields.length; i++) {\n                    if (seq != null ? fields[i].seq == seq : name ? fields[i].name == name : false)\n                        found = i;\n                }\n                if (found < 0) {\n                    let i = 0;\n                    while (i < fields.length && (seq == null || (fields[i].seq != null && fields[i].seq < seq)))\n                        i++;\n                    fields.splice(i, 0, { seq, name });\n                    found = i;\n                    for (let pos of positions)\n                        if (pos.field >= found)\n                            pos.field++;\n                }\n                positions.push(new FieldPos(found, lines.length, m.index, m.index + name.length));\n                line = line.slice(0, m.index) + rawName + line.slice(m.index + m[0].length);\n            }\n            line = line.replace(/\\\\([{}])/g, (_, brace, index) => {\n                for (let pos of positions)\n                    if (pos.line == lines.length && pos.from > index) {\n                        pos.from--;\n                        pos.to--;\n                    }\n                return brace;\n            });\n            lines.push(line);\n        }\n        return new Snippet(lines, positions);\n    }\n}\nlet fieldMarker = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.widget({ widget: /*@__PURE__*/new class extends _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.WidgetType {\n        toDOM() {\n            let span = document.createElement(\"span\");\n            span.className = \"cm-snippetFieldPosition\";\n            return span;\n        }\n        ignoreEvent() { return false; }\n    } });\nlet fieldRange = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.mark({ class: \"cm-snippetField\" });\nclass ActiveSnippet {\n    constructor(ranges, active) {\n        this.ranges = ranges;\n        this.active = active;\n        this.deco = _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.set(ranges.map(r => (r.from == r.to ? fieldMarker : fieldRange).range(r.from, r.to)));\n    }\n    map(changes) {\n        let ranges = [];\n        for (let r of this.ranges) {\n            let mapped = r.map(changes);\n            if (!mapped)\n                return null;\n            ranges.push(mapped);\n        }\n        return new ActiveSnippet(ranges, this.active);\n    }\n    selectionInsideField(sel) {\n        return sel.ranges.every(range => this.ranges.some(r => r.field == this.active && r.from <= range.from && r.to >= range.to));\n    }\n}\nconst setActive = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define({\n    map(value, changes) { return value && value.map(changes); }\n});\nconst moveToField = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define();\nconst snippetState = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateField.define({\n    create() { return null; },\n    update(value, tr) {\n        for (let effect of tr.effects) {\n            if (effect.is(setActive))\n                return effect.value;\n            if (effect.is(moveToField) && value)\n                return new ActiveSnippet(value.ranges, effect.value);\n        }\n        if (value && tr.docChanged)\n            value = value.map(tr.changes);\n        if (value && tr.selection && !value.selectionInsideField(tr.selection))\n            value = null;\n        return value;\n    },\n    provide: f => _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.decorations.from(f, val => val ? val.deco : _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.none)\n});\nfunction fieldSelection(ranges, field) {\n    return _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.create(ranges.filter(r => r.field == field).map(r => _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.range(r.from, r.to)));\n}\n/**\nConvert a snippet template to a function that can\n[apply](https://codemirror.net/6/docs/ref/#autocomplete.Completion.apply) it. Snippets are written\nusing syntax like this:\n\n    \"for (let ${index} = 0; ${index} < ${end}; ${index}++) {\\n\\t${}\\n}\"\n\nEach `${}` placeholder (you may also use `#{}`) indicates a field\nthat the user can fill in. Its name, if any, will be the default\ncontent for the field.\n\nWhen the snippet is activated by calling the returned function,\nthe code is inserted at the given position. Newlines in the\ntemplate are indented by the indentation of the start line, plus\none [indent unit](https://codemirror.net/6/docs/ref/#language.indentUnit) per tab character after\nthe newline.\n\nOn activation, (all instances of) the first field are selected.\nThe user can move between fields with Tab and Shift-Tab as long as\nthe fields are active. Moving to the last field or moving the\ncursor out of the current field deactivates the fields.\n\nThe order of fields defaults to textual order, but you can add\nnumbers to placeholders (`${1}` or `${1:defaultText}`) to provide\na custom order.\n\nTo include a literal `{` or `}` in your template, put a backslash\nin front of it. This will be removed and the brace will not be\ninterpreted as indicating a placeholder.\n*/\nfunction snippet(template) {\n    let snippet = Snippet.parse(template);\n    return (editor, completion, from, to) => {\n        let { text, ranges } = snippet.instantiate(editor.state, from);\n        let { main } = editor.state.selection;\n        let spec = {\n            changes: { from, to: to == main.from ? main.to : to, insert: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Text.of(text) },\n            scrollIntoView: true,\n            annotations: completion ? [pickedCompletion.of(completion), _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Transaction.userEvent.of(\"input.complete\")] : undefined\n        };\n        if (ranges.length)\n            spec.selection = fieldSelection(ranges, 0);\n        if (ranges.some(r => r.field > 0)) {\n            let active = new ActiveSnippet(ranges, 0);\n            let effects = spec.effects = [setActive.of(active)];\n            if (editor.state.field(snippetState, false) === undefined)\n                effects.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.appendConfig.of([snippetState, addSnippetKeymap, snippetPointerHandler, baseTheme]));\n        }\n        editor.dispatch(editor.state.update(spec));\n    };\n}\nfunction moveField(dir) {\n    return ({ state, dispatch }) => {\n        let active = state.field(snippetState, false);\n        if (!active || dir < 0 && active.active == 0)\n            return false;\n        let next = active.active + dir, last = dir > 0 && !active.ranges.some(r => r.field == next + dir);\n        dispatch(state.update({\n            selection: fieldSelection(active.ranges, next),\n            effects: setActive.of(last ? null : new ActiveSnippet(active.ranges, next)),\n            scrollIntoView: true\n        }));\n        return true;\n    };\n}\n/**\nA command that clears the active snippet, if any.\n*/\nconst clearSnippet = ({ state, dispatch }) => {\n    let active = state.field(snippetState, false);\n    if (!active)\n        return false;\n    dispatch(state.update({ effects: setActive.of(null) }));\n    return true;\n};\n/**\nMove to the next snippet field, if available.\n*/\nconst nextSnippetField = /*@__PURE__*/moveField(1);\n/**\nMove to the previous snippet field, if available.\n*/\nconst prevSnippetField = /*@__PURE__*/moveField(-1);\n/**\nCheck if there is an active snippet with a next field for\n`nextSnippetField` to move to.\n*/\nfunction hasNextSnippetField(state) {\n    let active = state.field(snippetState, false);\n    return !!(active && active.ranges.some(r => r.field == active.active + 1));\n}\n/**\nReturns true if there is an active snippet and a previous field\nfor `prevSnippetField` to move to.\n*/\nfunction hasPrevSnippetField(state) {\n    let active = state.field(snippetState, false);\n    return !!(active && active.active > 0);\n}\nconst defaultSnippetKeymap = [\n    { key: \"Tab\", run: nextSnippetField, shift: prevSnippetField },\n    { key: \"Escape\", run: clearSnippet }\n];\n/**\nA facet that can be used to configure the key bindings used by\nsnippets. The default binds Tab to\n[`nextSnippetField`](https://codemirror.net/6/docs/ref/#autocomplete.nextSnippetField), Shift-Tab to\n[`prevSnippetField`](https://codemirror.net/6/docs/ref/#autocomplete.prevSnippetField), and Escape\nto [`clearSnippet`](https://codemirror.net/6/docs/ref/#autocomplete.clearSnippet).\n*/\nconst snippetKeymap = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Facet.define({\n    combine(maps) { return maps.length ? maps[0] : defaultSnippetKeymap; }\n});\nconst addSnippetKeymap = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Prec.highest(/*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.keymap.compute([snippetKeymap], state => state.facet(snippetKeymap)));\n/**\nCreate a completion from a snippet. Returns an object with the\nproperties from `completion`, plus an `apply` function that\napplies the snippet.\n*/\nfunction snippetCompletion(template, completion) {\n    return Object.assign(Object.assign({}, completion), { apply: snippet(template) });\n}\nconst snippetPointerHandler = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.domEventHandlers({\n    mousedown(event, view) {\n        let active = view.state.field(snippetState, false), pos;\n        if (!active || (pos = view.posAtCoords({ x: event.clientX, y: event.clientY })) == null)\n            return false;\n        let match = active.ranges.find(r => r.from <= pos && r.to >= pos);\n        if (!match || match.field == active.active)\n            return false;\n        view.dispatch({\n            selection: fieldSelection(active.ranges, match.field),\n            effects: setActive.of(active.ranges.some(r => r.field > match.field)\n                ? new ActiveSnippet(active.ranges, match.field) : null),\n            scrollIntoView: true\n        });\n        return true;\n    }\n});\n\nfunction wordRE(wordChars) {\n    let escaped = wordChars.replace(/[\\]\\-\\\\]/g, \"\\\\$&\");\n    try {\n        return new RegExp(`[\\\\p{Alphabetic}\\\\p{Number}_${escaped}]+`, \"ug\");\n    }\n    catch (_a) {\n        return new RegExp(`[\\w${escaped}]`, \"g\");\n    }\n}\nfunction mapRE(re, f) {\n    return new RegExp(f(re.source), re.unicode ? \"u\" : \"\");\n}\nconst wordCaches = /*@__PURE__*/Object.create(null);\nfunction wordCache(wordChars) {\n    return wordCaches[wordChars] || (wordCaches[wordChars] = new WeakMap);\n}\nfunction storeWords(doc, wordRE, result, seen, ignoreAt) {\n    for (let lines = doc.iterLines(), pos = 0; !lines.next().done;) {\n        let { value } = lines, m;\n        wordRE.lastIndex = 0;\n        while (m = wordRE.exec(value)) {\n            if (!seen[m[0]] && pos + m.index != ignoreAt) {\n                result.push({ type: \"text\", label: m[0] });\n                seen[m[0]] = true;\n                if (result.length >= 2000 /* C.MaxList */)\n                    return;\n            }\n        }\n        pos += value.length + 1;\n    }\n}\nfunction collectWords(doc, cache, wordRE, to, ignoreAt) {\n    let big = doc.length >= 1000 /* C.MinCacheLen */;\n    let cached = big && cache.get(doc);\n    if (cached)\n        return cached;\n    let result = [], seen = Object.create(null);\n    if (doc.children) {\n        let pos = 0;\n        for (let ch of doc.children) {\n            if (ch.length >= 1000 /* C.MinCacheLen */) {\n                for (let c of collectWords(ch, cache, wordRE, to - pos, ignoreAt - pos)) {\n                    if (!seen[c.label]) {\n                        seen[c.label] = true;\n                        result.push(c);\n                    }\n                }\n            }\n            else {\n                storeWords(ch, wordRE, result, seen, ignoreAt - pos);\n            }\n            pos += ch.length + 1;\n        }\n    }\n    else {\n        storeWords(doc, wordRE, result, seen, ignoreAt);\n    }\n    if (big && result.length < 2000 /* C.MaxList */)\n        cache.set(doc, result);\n    return result;\n}\n/**\nA completion source that will scan the document for words (using a\n[character categorizer](https://codemirror.net/6/docs/ref/#state.EditorState.charCategorizer)), and\nreturn those as completions.\n*/\nconst completeAnyWord = context => {\n    let wordChars = context.state.languageDataAt(\"wordChars\", context.pos).join(\"\");\n    let re = wordRE(wordChars);\n    let token = context.matchBefore(mapRE(re, s => s + \"$\"));\n    if (!token && !context.explicit)\n        return null;\n    let from = token ? token.from : context.pos;\n    let options = collectWords(context.state.doc, wordCache(wordChars), re, 50000 /* C.Range */, from);\n    return { from, options, validFor: mapRE(re, s => \"^\" + s) };\n};\n\nconst defaults = {\n    brackets: [\"(\", \"[\", \"{\", \"'\", '\"'],\n    before: \")]}:;>\",\n    stringPrefixes: []\n};\nconst closeBracketEffect = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define({\n    map(value, mapping) {\n        let mapped = mapping.mapPos(value, -1, _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.MapMode.TrackAfter);\n        return mapped == null ? undefined : mapped;\n    }\n});\nconst closedBracket = /*@__PURE__*/new class extends _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.RangeValue {\n};\nclosedBracket.startSide = 1;\nclosedBracket.endSide = -1;\nconst bracketState = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateField.define({\n    create() { return _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.RangeSet.empty; },\n    update(value, tr) {\n        value = value.map(tr.changes);\n        if (tr.selection) {\n            let line = tr.state.doc.lineAt(tr.selection.main.head);\n            value = value.update({ filter: from => from >= line.from && from <= line.to });\n        }\n        for (let effect of tr.effects)\n            if (effect.is(closeBracketEffect))\n                value = value.update({ add: [closedBracket.range(effect.value, effect.value + 1)] });\n        return value;\n    }\n});\n/**\nExtension to enable bracket-closing behavior. When a closeable\nbracket is typed, its closing bracket is immediately inserted\nafter the cursor. When closing a bracket directly in front of a\nclosing bracket inserted by the extension, the cursor moves over\nthat bracket.\n*/\nfunction closeBrackets() {\n    return [inputHandler, bracketState];\n}\nconst definedClosing = \"()[]{}<>«»»«［］｛｝\";\nfunction closing(ch) {\n    for (let i = 0; i < definedClosing.length; i += 2)\n        if (definedClosing.charCodeAt(i) == ch)\n            return definedClosing.charAt(i + 1);\n    return (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.fromCodePoint)(ch < 128 ? ch : ch + 1);\n}\nfunction config(state, pos) {\n    return state.languageDataAt(\"closeBrackets\", pos)[0] || defaults;\n}\nconst android = typeof navigator == \"object\" && /*@__PURE__*//Android\\b/.test(navigator.userAgent);\nconst inputHandler = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.inputHandler.of((view, from, to, insert) => {\n    if ((android ? view.composing : view.compositionStarted) || view.state.readOnly)\n        return false;\n    let sel = view.state.selection.main;\n    if (insert.length > 2 || insert.length == 2 && (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointSize)((0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointAt)(insert, 0)) == 1 ||\n        from != sel.from || to != sel.to)\n        return false;\n    let tr = insertBracket(view.state, insert);\n    if (!tr)\n        return false;\n    view.dispatch(tr);\n    return true;\n});\n/**\nCommand that implements deleting a pair of matching brackets when\nthe cursor is between them.\n*/\nconst deleteBracketPair = ({ state, dispatch }) => {\n    if (state.readOnly)\n        return false;\n    let conf = config(state, state.selection.main.head);\n    let tokens = conf.brackets || defaults.brackets;\n    let dont = null, changes = state.changeByRange(range => {\n        if (range.empty) {\n            let before = prevChar(state.doc, range.head);\n            for (let token of tokens) {\n                if (token == before && nextChar(state.doc, range.head) == closing((0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointAt)(token, 0)))\n                    return { changes: { from: range.head - token.length, to: range.head + token.length },\n                        range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(range.head - token.length) };\n            }\n        }\n        return { range: dont = range };\n    });\n    if (!dont)\n        dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"delete.backward\" }));\n    return !dont;\n};\n/**\nClose-brackets related key bindings. Binds Backspace to\n[`deleteBracketPair`](https://codemirror.net/6/docs/ref/#autocomplete.deleteBracketPair).\n*/\nconst closeBracketsKeymap = [\n    { key: \"Backspace\", run: deleteBracketPair }\n];\n/**\nImplements the extension's behavior on text insertion. If the\ngiven string counts as a bracket in the language around the\nselection, and replacing the selection with it requires custom\nbehavior (inserting a closing version or skipping past a\npreviously-closed bracket), this function returns a transaction\nrepresenting that custom behavior. (You only need this if you want\nto programmatically insert brackets—the\n[`closeBrackets`](https://codemirror.net/6/docs/ref/#autocomplete.closeBrackets) extension will\ntake care of running this for user input.)\n*/\nfunction insertBracket(state, bracket) {\n    let conf = config(state, state.selection.main.head);\n    let tokens = conf.brackets || defaults.brackets;\n    for (let tok of tokens) {\n        let closed = closing((0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointAt)(tok, 0));\n        if (bracket == tok)\n            return closed == tok ? handleSame(state, tok, tokens.indexOf(tok + tok + tok) > -1, conf)\n                : handleOpen(state, tok, closed, conf.before || defaults.before);\n        if (bracket == closed && closedBracketAt(state, state.selection.main.from))\n            return handleClose(state, tok, closed);\n    }\n    return null;\n}\nfunction closedBracketAt(state, pos) {\n    let found = false;\n    state.field(bracketState).between(0, state.doc.length, from => {\n        if (from == pos)\n            found = true;\n    });\n    return found;\n}\nfunction nextChar(doc, pos) {\n    let next = doc.sliceString(pos, pos + 2);\n    return next.slice(0, (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointSize)((0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointAt)(next, 0)));\n}\nfunction prevChar(doc, pos) {\n    let prev = doc.sliceString(pos - 2, pos);\n    return (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointSize)((0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointAt)(prev, 0)) == prev.length ? prev : prev.slice(1);\n}\nfunction handleOpen(state, open, close, closeBefore) {\n    let dont = null, changes = state.changeByRange(range => {\n        if (!range.empty)\n            return { changes: [{ insert: open, from: range.from }, { insert: close, from: range.to }],\n                effects: closeBracketEffect.of(range.to + open.length),\n                range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.range(range.anchor + open.length, range.head + open.length) };\n        let next = nextChar(state.doc, range.head);\n        if (!next || /\\s/.test(next) || closeBefore.indexOf(next) > -1)\n            return { changes: { insert: open + close, from: range.head },\n                effects: closeBracketEffect.of(range.head + open.length),\n                range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(range.head + open.length) };\n        return { range: dont = range };\n    });\n    return dont ? null : state.update(changes, {\n        scrollIntoView: true,\n        userEvent: \"input.type\"\n    });\n}\nfunction handleClose(state, _open, close) {\n    let dont = null, changes = state.changeByRange(range => {\n        if (range.empty && nextChar(state.doc, range.head) == close)\n            return { changes: { from: range.head, to: range.head + close.length, insert: close },\n                range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(range.head + close.length) };\n        return dont = { range };\n    });\n    return dont ? null : state.update(changes, {\n        scrollIntoView: true,\n        userEvent: \"input.type\"\n    });\n}\n// Handles cases where the open and close token are the same, and\n// possibly triple quotes (as in `\"\"\"abc\"\"\"`-style quoting).\nfunction handleSame(state, token, allowTriple, config) {\n    let stringPrefixes = config.stringPrefixes || defaults.stringPrefixes;\n    let dont = null, changes = state.changeByRange(range => {\n        if (!range.empty)\n            return { changes: [{ insert: token, from: range.from }, { insert: token, from: range.to }],\n                effects: closeBracketEffect.of(range.to + token.length),\n                range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.range(range.anchor + token.length, range.head + token.length) };\n        let pos = range.head, next = nextChar(state.doc, pos), start;\n        if (next == token) {\n            if (nodeStart(state, pos)) {\n                return { changes: { insert: token + token, from: pos },\n                    effects: closeBracketEffect.of(pos + token.length),\n                    range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(pos + token.length) };\n            }\n            else if (closedBracketAt(state, pos)) {\n                let isTriple = allowTriple && state.sliceDoc(pos, pos + token.length * 3) == token + token + token;\n                let content = isTriple ? token + token + token : token;\n                return { changes: { from: pos, to: pos + content.length, insert: content },\n                    range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(pos + content.length) };\n            }\n        }\n        else if (allowTriple && state.sliceDoc(pos - 2 * token.length, pos) == token + token &&\n            (start = canStartStringAt(state, pos - 2 * token.length, stringPrefixes)) > -1 &&\n            nodeStart(state, start)) {\n            return { changes: { insert: token + token + token + token, from: pos },\n                effects: closeBracketEffect.of(pos + token.length),\n                range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(pos + token.length) };\n        }\n        else if (state.charCategorizer(pos)(next) != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word) {\n            if (canStartStringAt(state, pos, stringPrefixes) > -1 && !probablyInString(state, pos, token, stringPrefixes))\n                return { changes: { insert: token + token, from: pos },\n                    effects: closeBracketEffect.of(pos + token.length),\n                    range: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(pos + token.length) };\n        }\n        return { range: dont = range };\n    });\n    return dont ? null : state.update(changes, {\n        scrollIntoView: true,\n        userEvent: \"input.type\"\n    });\n}\nfunction nodeStart(state, pos) {\n    let tree = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.syntaxTree)(state).resolveInner(pos + 1);\n    return tree.parent && tree.from == pos;\n}\nfunction probablyInString(state, pos, quoteToken, prefixes) {\n    let node = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_0__.syntaxTree)(state).resolveInner(pos, -1);\n    let maxPrefix = prefixes.reduce((m, p) => Math.max(m, p.length), 0);\n    for (let i = 0; i < 5; i++) {\n        let start = state.sliceDoc(node.from, Math.min(node.to, node.from + quoteToken.length + maxPrefix));\n        let quotePos = start.indexOf(quoteToken);\n        if (!quotePos || quotePos > -1 && prefixes.indexOf(start.slice(0, quotePos)) > -1) {\n            let first = node.firstChild;\n            while (first && first.from == node.from && first.to - first.from > quoteToken.length + quotePos) {\n                if (state.sliceDoc(first.to - quoteToken.length, first.to) == quoteToken)\n                    return false;\n                first = first.firstChild;\n            }\n            return true;\n        }\n        let parent = node.to == pos && node.parent;\n        if (!parent)\n            break;\n        node = parent;\n    }\n    return false;\n}\nfunction canStartStringAt(state, pos, prefixes) {\n    let charCat = state.charCategorizer(pos);\n    if (charCat(state.sliceDoc(pos - 1, pos)) != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word)\n        return pos;\n    for (let prefix of prefixes) {\n        let start = pos - prefix.length;\n        if (state.sliceDoc(start, pos) == prefix && charCat(state.sliceDoc(start - 1, start)) != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word)\n            return start;\n    }\n    return -1;\n}\n\n/**\nReturns an extension that enables autocompletion.\n*/\nfunction autocompletion(config = {}) {\n    return [\n        commitCharacters,\n        completionState,\n        completionConfig.of(config),\n        completionPlugin,\n        completionKeymapExt,\n        baseTheme\n    ];\n}\n/**\nBasic keybindings for autocompletion.\n\n - Ctrl-Space (and Alt-\\` on macOS): [`startCompletion`](https://codemirror.net/6/docs/ref/#autocomplete.startCompletion)\n - Escape: [`closeCompletion`](https://codemirror.net/6/docs/ref/#autocomplete.closeCompletion)\n - ArrowDown: [`moveCompletionSelection`](https://codemirror.net/6/docs/ref/#autocomplete.moveCompletionSelection)`(true)`\n - ArrowUp: [`moveCompletionSelection`](https://codemirror.net/6/docs/ref/#autocomplete.moveCompletionSelection)`(false)`\n - PageDown: [`moveCompletionSelection`](https://codemirror.net/6/docs/ref/#autocomplete.moveCompletionSelection)`(true, \"page\")`\n - PageDown: [`moveCompletionSelection`](https://codemirror.net/6/docs/ref/#autocomplete.moveCompletionSelection)`(true, \"page\")`\n - Enter: [`acceptCompletion`](https://codemirror.net/6/docs/ref/#autocomplete.acceptCompletion)\n*/\nconst completionKeymap = [\n    { key: \"Ctrl-Space\", run: startCompletion },\n    { mac: \"Alt-`\", run: startCompletion },\n    { key: \"Escape\", run: closeCompletion },\n    { key: \"ArrowDown\", run: /*@__PURE__*/moveCompletionSelection(true) },\n    { key: \"ArrowUp\", run: /*@__PURE__*/moveCompletionSelection(false) },\n    { key: \"PageDown\", run: /*@__PURE__*/moveCompletionSelection(true, \"page\") },\n    { key: \"PageUp\", run: /*@__PURE__*/moveCompletionSelection(false, \"page\") },\n    { key: \"Enter\", run: acceptCompletion }\n];\nconst completionKeymapExt = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Prec.highest(/*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.keymap.computeN([completionConfig], state => state.facet(completionConfig).defaultKeymap ? [completionKeymap] : []));\n/**\nGet the current completion status. When completions are available,\nthis will return `\"active\"`. When completions are pending (in the\nprocess of being queried), this returns `\"pending\"`. Otherwise, it\nreturns `null`.\n*/\nfunction completionStatus(state) {\n    let cState = state.field(completionState, false);\n    return cState && cState.active.some(a => a.isPending) ? \"pending\"\n        : cState && cState.active.some(a => a.state != 0 /* State.Inactive */) ? \"active\" : null;\n}\nconst completionArrayCache = /*@__PURE__*/new WeakMap;\n/**\nReturns the available completions as an array.\n*/\nfunction currentCompletions(state) {\n    var _a;\n    let open = (_a = state.field(completionState, false)) === null || _a === void 0 ? void 0 : _a.open;\n    if (!open || open.disabled)\n        return [];\n    let completions = completionArrayCache.get(open.options);\n    if (!completions)\n        completionArrayCache.set(open.options, completions = open.options.map(o => o.completion));\n    return completions;\n}\n/**\nReturn the currently selected completion, if any.\n*/\nfunction selectedCompletion(state) {\n    var _a;\n    let open = (_a = state.field(completionState, false)) === null || _a === void 0 ? void 0 : _a.open;\n    return open && !open.disabled && open.selected >= 0 ? open.options[open.selected].completion : null;\n}\n/**\nReturns the currently selected position in the active completion\nlist, or null if no completions are active.\n*/\nfunction selectedCompletionIndex(state) {\n    var _a;\n    let open = (_a = state.field(completionState, false)) === null || _a === void 0 ? void 0 : _a.open;\n    return open && !open.disabled && open.selected >= 0 ? open.selected : null;\n}\n/**\nCreate an effect that can be attached to a transaction to change\nthe currently selected completion.\n*/\nfunction setSelectedCompletion(index) {\n    return setSelectedEffect.of(index);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+autocomplete@6.18.6/node_modules/@codemirror/autocomplete/dist/index.js\n");

/***/ })

};
;