"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-json@6.0.1";
exports.ids = ["vendor-chunks/@codemirror+lang-json@6.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-json@6.0.1/node_modules/@codemirror/lang-json/dist/index.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-json@6.0.1/node_modules/@codemirror/lang-json/dist/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   json: () => (/* binding */ json),\n/* harmony export */   jsonLanguage: () => (/* binding */ jsonLanguage),\n/* harmony export */   jsonParseLinter: () => (/* binding */ jsonParseLinter)\n/* harmony export */ });\n/* harmony import */ var _lezer_json__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/json */ \"(ssr)/./node_modules/.pnpm/@lezer+json@1.0.3/node_modules/@lezer/json/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n\n\n\n/**\nCalls\n[`JSON.parse`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse)\non the document and, if that throws an error, reports it as a\nsingle diagnostic.\n*/\nconst jsonParseLinter = () => (view) => {\n    try {\n        JSON.parse(view.state.doc.toString());\n    }\n    catch (e) {\n        if (!(e instanceof SyntaxError))\n            throw e;\n        const pos = getErrorPosition(e, view.state.doc);\n        return [{\n                from: pos,\n                message: e.message,\n                severity: 'error',\n                to: pos\n            }];\n    }\n    return [];\n};\nfunction getErrorPosition(error, doc) {\n    let m;\n    if (m = error.message.match(/at position (\\d+)/))\n        return Math.min(+m[1], doc.length);\n    if (m = error.message.match(/at line (\\d+) column (\\d+)/))\n        return Math.min(doc.line(+m[1]).from + (+m[2]) - 1, doc.length);\n    return 0;\n}\n\n/**\nA language provider that provides JSON parsing.\n*/\nconst jsonLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LRLanguage.define({\n    name: \"json\",\n    parser: /*@__PURE__*/_lezer_json__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.indentNodeProp.add({\n                Object: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.continuedIndent)({ except: /^\\s*\\}/ }),\n                Array: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.continuedIndent)({ except: /^\\s*\\]/ })\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldNodeProp.add({\n                \"Object Array\": _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldInside\n            })\n        ]\n    }),\n    languageData: {\n        closeBrackets: { brackets: [\"[\", \"{\", '\"'] },\n        indentOnInput: /^\\s*[\\}\\]]$/\n    }\n});\n/**\nJSON language support.\n*/\nfunction json() {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LanguageSupport(jsonLanguage);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-json@6.0.1/node_modules/@codemirror/lang-json/dist/index.js\n");

/***/ })

};
;