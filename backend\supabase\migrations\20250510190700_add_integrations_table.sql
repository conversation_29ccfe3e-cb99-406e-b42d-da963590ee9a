-- Create integrations table
CREATE TABLE IF NOT EXISTS public.integrations (
    integration_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    integration_type TEXT NOT NULL,
    account_id UUID NOT NULL REFERENCES public.accounts(id) ON DELETE CASCADE,
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    status TEXT NOT NULL DEFAULT 'pending',
    last_error TEXT,
    last_sync TIMESTAMPTZ,
    config JSONB NOT NULL DEFAULT '{}'::JSONB,
    integration_mode TEXT NOT NULL DEFAULT 'company',
    UNIQUE (account_id, integration_type, integration_mode)
);

-- Add RLS policies for integrations table
ALTER TABLE public.integrations ENABLE ROW LEVEL SECURITY;

-- Allow users to view integrations for accounts they belong to
CREATE POLICY "Users can view integrations for their accounts" ON public.integrations
    FOR SELECT
    USING (
        account_id IN (
            SELECT account_id FROM public.account_user
            WHERE user_id = auth.uid()
        )
    );

-- Allow users to insert integrations for accounts they belong to
CREATE POLICY "Users can insert integrations for their accounts" ON public.integrations
    FOR INSERT
    WITH CHECK (
        account_id IN (
            SELECT account_id FROM public.account_user
            WHERE user_id = auth.uid()
        )
    );

-- Allow users to update integrations for accounts they belong to
CREATE POLICY "Users can update integrations for their accounts" ON public.integrations
    FOR UPDATE
    USING (
        account_id IN (
            SELECT account_id FROM public.account_user
            WHERE user_id = auth.uid()
        )
    );

-- Allow users to delete integrations for accounts they belong to
CREATE POLICY "Users can delete integrations for their accounts" ON public.integrations
    FOR DELETE
    USING (
        account_id IN (
            SELECT account_id FROM public.account_user
            WHERE user_id = auth.uid()
        )
    );

-- Create function to get integrations for an account
CREATE OR REPLACE FUNCTION public.get_account_integrations(p_account_id UUID)
RETURNS SETOF public.integrations
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT *
    FROM public.integrations
    WHERE account_id = p_account_id;
END;
$$;

-- Create function to get integration by ID
CREATE OR REPLACE FUNCTION public.get_integration(p_integration_id UUID)
RETURNS public.integrations
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_integration public.integrations;
BEGIN
    SELECT *
    INTO v_integration
    FROM public.integrations
    WHERE integration_id = p_integration_id;
    
    RETURN v_integration;
END;
$$;

-- Create function to create a new integration
CREATE OR REPLACE FUNCTION public.create_integration(
    p_integration_type TEXT,
    p_account_id UUID,
    p_config JSONB,
    p_integration_mode TEXT DEFAULT 'company'
)
RETURNS UUID
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_integration_id UUID;
BEGIN
    INSERT INTO public.integrations (
        integration_type,
        account_id,
        created_by,
        config,
        integration_mode
    )
    VALUES (
        p_integration_type,
        p_account_id,
        auth.uid(),
        p_config,
        p_integration_mode
    )
    RETURNING integration_id INTO v_integration_id;
    
    RETURN v_integration_id;
END;
$$;

-- Create function to update an integration
CREATE OR REPLACE FUNCTION public.update_integration(
    p_integration_id UUID,
    p_config JSONB
)
RETURNS BOOLEAN
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.integrations
    SET 
        config = p_config,
        updated_at = NOW()
    WHERE integration_id = p_integration_id
    AND account_id IN (
        SELECT account_id FROM public.account_user
        WHERE user_id = auth.uid()
    );
    
    RETURN FOUND;
END;
$$;

-- Create function to delete an integration
CREATE OR REPLACE FUNCTION public.delete_integration(
    p_integration_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
    DELETE FROM public.integrations
    WHERE integration_id = p_integration_id
    AND account_id IN (
        SELECT account_id FROM public.account_user
        WHERE user_id = auth.uid()
    );
    
    RETURN FOUND;
END;
$$;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_integrations_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

CREATE TRIGGER update_integrations_updated_at
BEFORE UPDATE ON public.integrations
FOR EACH ROW
EXECUTE FUNCTION public.update_integrations_updated_at();