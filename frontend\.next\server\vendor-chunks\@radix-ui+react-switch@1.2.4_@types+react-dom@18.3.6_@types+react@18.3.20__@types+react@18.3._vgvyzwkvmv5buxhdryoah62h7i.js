"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-switch@1.2.4_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18.3._vgvyzwkvmv5buxhdryoah62h7i";
exports.ids = ["vendor-chunks/@radix-ui+react-switch@1.2.4_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18.3._vgvyzwkvmv5buxhdryoah62h7i"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-switch@1.2.4_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18.3._vgvyzwkvmv5buxhdryoah62h7i/node_modules/@radix-ui/react-switch/dist/index.mjs":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-switch@1.2.4_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18.3._vgvyzwkvmv5buxhdryoah62h7i/node_modules/@radix-ui/react-switch/dist/index.mjs ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Switch: () => (/* binding */ Switch),\n/* harmony export */   SwitchThumb: () => (/* binding */ SwitchThumb),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   createSwitchScope: () => (/* binding */ createSwitchScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.20_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.20_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.2.2_@types+react@18.3.20_react@18.3.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previous@1.1.1_@types+react@18.3.20_react@18.3.1/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1.1.1_@types+react@18.3.20_react@18.3.1/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.2_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18_gpjlxogsj3bv2vu6dvbkul76le/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Root,Switch,SwitchThumb,Thumb,createSwitchScope auto */ // src/switch.tsx\n\n\n\n\n\n\n\n\n\nvar SWITCH_NAME = \"Switch\";\nvar [createSwitchContext, createSwitchScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SWITCH_NAME);\nvar [SwitchProvider, useSwitchContext] = createSwitchContext(SWITCH_NAME);\nvar Switch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSwitch, name, checked: checkedProp, defaultChecked, required, disabled, value = \"on\", onCheckedChange, form, ...switchProps } = props;\n    const [button, setButton] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"Switch.useComposedRefs[composedRefs]\": (node)=>setButton(node)\n    }[\"Switch.useComposedRefs[composedRefs]\"]);\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    const [checked, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: checkedProp,\n        defaultProp: defaultChecked ?? false,\n        onChange: onCheckedChange,\n        caller: SWITCH_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(SwitchProvider, {\n        scope: __scopeSwitch,\n        checked,\n        disabled,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n                type: \"button\",\n                role: \"switch\",\n                \"aria-checked\": checked,\n                \"aria-required\": required,\n                \"data-state\": getState(checked),\n                \"data-disabled\": disabled ? \"\" : void 0,\n                disabled,\n                value,\n                ...switchProps,\n                ref: composedRefs,\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, (event)=>{\n                    setChecked((prevChecked)=>!prevChecked);\n                    if (isFormControl) {\n                        hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                        if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n                    }\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SwitchBubbleInput, {\n                control: button,\n                bubbles: !hasConsumerStoppedPropagationRef.current,\n                name,\n                value,\n                checked,\n                required,\n                disabled,\n                form,\n                style: {\n                    transform: \"translateX(-100%)\"\n                }\n            })\n        ]\n    });\n});\nSwitch.displayName = SWITCH_NAME;\nvar THUMB_NAME = \"SwitchThumb\";\nvar SwitchThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span, {\n        \"data-state\": getState(context.checked),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...thumbProps,\n        ref: forwardedRef\n    });\n});\nSwitchThumb.displayName = THUMB_NAME;\nvar BUBBLE_INPUT_NAME = \"SwitchBubbleInput\";\nvar SwitchBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeSwitch, control, checked, bubbles = true, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(ref, forwardedRef);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"SwitchBubbleInput.useEffect\": ()=>{\n            const input = ref.current;\n            if (!input) return;\n            const inputProto = window.HTMLInputElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n            const setChecked = descriptor.set;\n            if (prevChecked !== checked && setChecked) {\n                const event = new Event(\"click\", {\n                    bubbles\n                });\n                setChecked.call(input, checked);\n                input.dispatchEvent(event);\n            }\n        }\n    }[\"SwitchBubbleInput.useEffect\"], [\n        prevChecked,\n        checked,\n        bubbles\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"input\", {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: checked,\n        ...props,\n        tabIndex: -1,\n        ref: composedRefs,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0\n        }\n    });\n});\nSwitchBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction getState(checked) {\n    return checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Switch;\nvar Thumb = SwitchThumb;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-switch@1.2.4_@types+react-dom@18.3.6_@types+react@18.3.20__@types+react@18.3._vgvyzwkvmv5buxhdryoah62h7i/node_modules/@radix-ui/react-switch/dist/index.mjs\n");

/***/ })

};
;