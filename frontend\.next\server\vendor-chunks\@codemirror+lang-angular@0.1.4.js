"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-angular@0.1.4";
exports.ids = ["vendor-chunks/@codemirror+lang-angular@0.1.4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-angular@0.1.4/node_modules/@codemirror/lang-angular/dist/index.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-angular@0.1.4/node_modules/@codemirror/lang-angular/dist/index.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   angular: () => (/* binding */ angular),\n/* harmony export */   angularLanguage: () => (/* binding */ angularLanguage)\n/* harmony export */ });\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _codemirror_lang_html__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/lang-html */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-html@6.4.9/node_modules/@codemirror/lang-html/dist/index.js\");\n/* harmony import */ var _codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/lang-javascript */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-javascript@6.2.3/node_modules/@codemirror/lang-javascript/dist/index.js\");\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js\");\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n/* harmony import */ var _lezer_lr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lezer/lr */ \"(ssr)/./node_modules/.pnpm/@lezer+lr@1.4.2/node_modules/@lezer/lr/dist/index.js\");\n\n\n\n\n\n\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst Text = 1,\n  attributeContentSingle = 33,\n  attributeContentDouble = 34,\n  scriptAttributeContentSingle = 35,\n  scriptAttributeContentDouble = 36;\n\nconst text = /*@__PURE__*/new _lezer_lr__WEBPACK_IMPORTED_MODULE_2__.ExternalTokenizer(input => {\n    let start = input.pos;\n    for (;;) {\n        if (input.next == 10 /* Ch.Newline */) {\n            input.advance();\n            break;\n        }\n        else if (input.next == 123 /* Ch.BraceL */ && input.peek(1) == 123 /* Ch.BraceL */ || input.next < 0) {\n            break;\n        }\n        input.advance();\n    }\n    if (input.pos > start)\n        input.acceptToken(Text);\n});\nfunction attrContent(quote, token, script) {\n    return new _lezer_lr__WEBPACK_IMPORTED_MODULE_2__.ExternalTokenizer(input => {\n        let start = input.pos;\n        while (input.next != quote && input.next >= 0 &&\n            (script || input.next != 38 /* Ch.Ampersand */ && (input.next != 123 /* Ch.BraceL */ || input.peek(1) != 123 /* Ch.BraceL */)))\n            input.advance();\n        if (input.pos > start)\n            input.acceptToken(token);\n    });\n}\nconst attrSingle = /*@__PURE__*/attrContent(39 /* Ch.SingleQuote */, attributeContentSingle, false);\nconst attrDouble = /*@__PURE__*/attrContent(34 /* Ch.DoubleQuote */, attributeContentDouble, false);\nconst scriptAttrSingle = /*@__PURE__*/attrContent(39 /* Ch.SingleQuote */, scriptAttributeContentSingle, true);\nconst scriptAttrDouble = /*@__PURE__*/attrContent(34 /* Ch.DoubleQuote */, scriptAttributeContentDouble, true);\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst parser = /*@__PURE__*/_lezer_lr__WEBPACK_IMPORTED_MODULE_2__.LRParser.deserialize({\n  version: 14,\n  states: \"(jOVOqOOOeQpOOOvO!bO'#CaOOOP'#Cx'#CxQVOqOOO!OQpO'#CfO!WQpO'#ClO!]QpO'#CrO!bQpO'#CsOOQO'#Cv'#CvQ!gQpOOQ!lQpOOQ!qQpOOOOOV,58{,58{O!vOpO,58{OOOP-E6v-E6vO!{QpO,59QO#TQpO,59QOOQO,59W,59WO#YQpO,59^OOQO,59_,59_O#_QpOOO#_QpOOO#gQpOOOOOV1G.g1G.gO#oQpO'#CyO#tQpO1G.lOOQO1G.l1G.lO#|QpO1G.lOOQO1G.x1G.xO$UO`O'#DUO$ZOWO'#DUOOQO'#Co'#CoQOQpOOOOQO'#Cu'#CuO$`OtO'#CwO$qOrO'#CwOOQO,59e,59eOOQO-E6w-E6wOOQO7+$W7+$WO%SQpO7+$WO%[QpO7+$WOOOO'#Cp'#CpO%aOpO,59pOOOO'#Cq'#CqO%fOpO,59pOOOS'#Cz'#CzO%kOtO,59cOOQO,59c,59cOOOQ'#C{'#C{O%|OrO,59cO&_QpO<<GrOOQO<<Gr<<GrOOQO1G/[1G/[OOOS-E6x-E6xOOQO1G.}1G.}OOOQ-E6y-E6yOOQOAN=^AN=^\",\n  stateData: \"&d~OvOS~OPROSQOVROWRO~OZTO[XO^VOaUOhWO~OR]OU^O~O[`O^aO~O[bO~O[cO~O[dO~ObeO~ObfO~ObgO~ORhO~O]kOwiO~O[lO~O_mO~OynOzoO~OysOztO~O[uO~O]wOwiO~O_yOwiO~OtzO~Os|O~OSQOV!OOW!OOr!OOy!QO~OSQOV!ROW!ROq!ROz!QO~O_!TOwiO~O]!UO~Oy!VO~Oz!VO~OSQOV!OOW!OOr!OOy!XO~OSQOV!ROW!ROq!ROz!XO~O]!ZO~O\",\n  goto: \"#dyPPPPPzPPPP!WPPPPP!WPP!Z!^!a!d!dP!g!j!m!p!v#Q#WPPPPPPPP#^SROSS!Os!PT!Rt!SRYPRqeR{nR}oRZPRqfR[PRqgQSOR_SQj`SvjxRxlQ!PsR!W!PQ!StR!Y!SQpeRrf\",\n  nodeNames: \"⚠ Text Content }} {{ Interpolation InterpolationContent Entity InvalidEntity Attribute BoundAttributeName [ Identifier ] ( ) ReferenceName # Is ExpressionAttributeValue AttributeInterpolation AttributeInterpolation EventName DirectiveName * StatementAttributeValue AttributeName AttributeValue\",\n  maxTerm: 42,\n  nodeProps: [\n    [\"openedBy\", 3,\"{{\",15,\"(\"],\n    [\"closedBy\", 4,\"}}\",14,\")\"],\n    [\"isolate\", -4,5,19,25,27,\"\"]\n  ],\n  skippedNodes: [0],\n  repeatNodeCount: 4,\n  tokenData: \"0r~RyOX#rXY$mYZ$mZ]#r]^$m^p#rpq$mqr#rrs%jst&Qtv#rvw&hwx)zxy*byz*xz{+`{}#r}!O+v!O!P-]!P!Q#r!Q![+v![!]+v!]!_#r!_!`-s!`!c#r!c!}+v!}#O.Z#O#P#r#P#Q.q#Q#R#r#R#S+v#S#T#r#T#o+v#o#p/X#p#q#r#q#r0Z#r%W#r%W;'S+v;'S;:j-V;:j;=`$g<%lO+vQ#wTUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rQ$ZSO#q#r#r;'S#r;'S;=`$g<%lO#rQ$jP;=`<%l#rR$t[UQvPOX#rXY$mYZ$mZ]#r]^$m^p#rpq$mq#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR%qTyPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR&XTaPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR&oXUQWPOp'[pq#rq!]'[!]!^#r!^#q'[#q#r(d#r;'S'[;'S;=`)t<%lO'[R'aXUQOp'[pq#rq!]'[!]!^'|!^#q'[#q#r(d#r;'S'[;'S;=`)t<%lO'[R(TTVPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR(gXOp'[pq#rq!]'[!]!^'|!^#q'[#q#r)S#r;'S'[;'S;=`)t<%lO'[P)VUOp)Sq!])S!]!^)i!^;'S)S;'S;=`)n<%lO)SP)nOVPP)qP;=`<%l)SR)wP;=`<%l'[R*RTzPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR*iT^PUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR+PT_PUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR+gThPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR+}b[PUQO}#r}!O+v!O!Q#r!Q![+v![!]+v!]!c#r!c!}+v!}#R#r#R#S+v#S#T#r#T#o+v#o#q#r#q#r$W#r%W#r%W;'S+v;'S;:j-V;:j;=`$g<%lO+vR-YP;=`<%l+vR-dTwPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR-zTUQbPO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR.bTZPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR.xT]PUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR/^VUQO#o#r#o#p/s#p#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#rR/zTSPUQO#q#r#q#r$W#r;'S#r;'S;=`$g<%lO#r~0^TO#q#r#q#r0m#r;'S#r;'S;=`$g<%lO#r~0rOR~\",\n  tokenizers: [text, attrSingle, attrDouble, scriptAttrSingle, scriptAttrDouble, 0, 1],\n  topRules: {\"Content\":[0,2],\"Attribute\":[1,9]},\n  tokenPrec: 0\n});\n\nconst exprParser = /*@__PURE__*/_codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_3__.javascriptLanguage.parser.configure({\n    top: \"SingleExpression\"\n});\nconst baseParser = /*@__PURE__*/parser.configure({\n    props: [\n        /*@__PURE__*/(0,_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.styleTags)({\n            Text: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.content,\n            Is: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.definitionOperator,\n            AttributeName: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.attributeName,\n            \"AttributeValue ExpressionAttributeValue StatementAttributeValue\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.attributeValue,\n            Entity: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.character,\n            InvalidEntity: _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.invalid,\n            \"BoundAttributeName/Identifier\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.attributeName,\n            \"EventName/Identifier\": /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.attributeName),\n            \"ReferenceName/Identifier\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.variableName,\n            \"DirectiveName/Identifier\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.keyword,\n            \"{{ }}\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.brace,\n            \"( )\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.paren,\n            \"[ ]\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.bracket,\n            \"# '*'\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_0__.tags.punctuation\n        })\n    ]\n});\nconst exprMixed = { parser: exprParser }, statementMixed = { parser: _codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_3__.javascriptLanguage.parser };\nconst textParser = /*@__PURE__*/baseParser.configure({\n    wrap: /*@__PURE__*/(0,_lezer_common__WEBPACK_IMPORTED_MODULE_1__.parseMixed)((node, input) => node.name == \"InterpolationContent\" ? exprMixed : null),\n});\nconst attrParser = /*@__PURE__*/baseParser.configure({\n    wrap: /*@__PURE__*/(0,_lezer_common__WEBPACK_IMPORTED_MODULE_1__.parseMixed)((node, input) => {\n        var _a;\n        return node.name == \"InterpolationContent\" ? exprMixed\n            : node.name != \"AttributeInterpolation\" ? null\n                : ((_a = node.node.parent) === null || _a === void 0 ? void 0 : _a.name) == \"StatementAttributeValue\" ? statementMixed : exprMixed;\n    }),\n    top: \"Attribute\"\n});\nconst textMixed = { parser: textParser }, attrMixed = { parser: attrParser };\nconst baseHTML = /*@__PURE__*/(0,_codemirror_lang_html__WEBPACK_IMPORTED_MODULE_4__.html)({ selfClosingTags: true });\nfunction mkAngular(language) {\n    return language.configure({ wrap: (0,_lezer_common__WEBPACK_IMPORTED_MODULE_1__.parseMixed)(mixAngular) }, \"angular\");\n}\n/**\nA language provider for Angular Templates.\n*/\nconst angularLanguage = /*@__PURE__*/mkAngular(baseHTML.language);\nfunction mixAngular(node, input) {\n    switch (node.name) {\n        case \"Attribute\":\n            return /^[*#(\\[]|\\{\\{/.test(input.read(node.from, node.to)) ? attrMixed : null;\n        case \"Text\":\n            return textMixed;\n    }\n    return null;\n}\n/**\nAngular Template language support.\n*/\nfunction angular(config = {}) {\n    let base = baseHTML;\n    if (config.base) {\n        if (config.base.language.name != \"html\" || !(config.base.language instanceof _codemirror_language__WEBPACK_IMPORTED_MODULE_5__.LRLanguage))\n            throw new RangeError(\"The base option must be the result of calling html(...)\");\n        base = config.base;\n    }\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_5__.LanguageSupport(base.language == baseHTML.language ? angularLanguage : mkAngular(base.language), [base.support, base.language.data.of({\n            closeBrackets: { brackets: [\"[\", \"{\", '\"'] },\n            indentOnInput: /^\\s*[\\}\\]]$/\n        })]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-angular@0.1.4/node_modules/@codemirror/lang-angular/dist/index.js\n");

/***/ })

};
;