"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_codemirror_legacy-modes_6_5_1_node_modules_codemirror_le-4e28a2"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/rpm.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/rpm.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rpmChanges: () => (/* binding */ rpmChanges),\n/* harmony export */   rpmSpec: () => (/* binding */ rpmSpec)\n/* harmony export */ });\nvar headerSeparator = /^-+$/;\nvar headerLine = /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)  ?\\d{1,2} \\d{2}:\\d{2}(:\\d{2})? [A-Z]{3,4} \\d{4} - /;\nvar simpleEmail = /^[\\w+.-]+@[\\w.-]+/;\n\nconst rpmChanges = {\n  name: \"rpmchanges\",\n  token: function(stream) {\n    if (stream.sol()) {\n      if (stream.match(headerSeparator)) { return 'tag'; }\n      if (stream.match(headerLine)) { return 'tag'; }\n    }\n    if (stream.match(simpleEmail)) { return 'string'; }\n    stream.next();\n    return null;\n  }\n}\n\n// Quick and dirty spec file highlighting\n\nvar arch = /^(i386|i586|i686|x86_64|ppc64le|ppc64|ppc|ia64|s390x|s390|sparc64|sparcv9|sparc|noarch|alphaev6|alpha|hppa|mipsel)/;\n\nvar preamble = /^[a-zA-Z0-9()]+:/;\nvar section = /^%(debug_package|package|description|prep|build|install|files|clean|changelog|preinstall|preun|postinstall|postun|pretrans|posttrans|pre|post|triggerin|triggerun|verifyscript|check|triggerpostun|triggerprein|trigger)/;\nvar control_flow_complex = /^%(ifnarch|ifarch|if)/; // rpm control flow macros\nvar control_flow_simple = /^%(else|endif)/; // rpm control flow macros\nvar operators = /^(\\!|\\?|\\<\\=|\\<|\\>\\=|\\>|\\=\\=|\\&\\&|\\|\\|)/; // operators in control flow macros\n\nconst rpmSpec = {\n  name: \"rpmspec\",\n  startState: function () {\n    return {\n      controlFlow: false,\n      macroParameters: false,\n      section: false\n    };\n  },\n  token: function (stream, state) {\n    var ch = stream.peek();\n    if (ch == \"#\") { stream.skipToEnd(); return \"comment\"; }\n\n    if (stream.sol()) {\n      if (stream.match(preamble)) { return \"header\"; }\n      if (stream.match(section)) { return \"atom\"; }\n    }\n\n    if (stream.match(/^\\$\\w+/)) { return \"def\"; } // Variables like '$RPM_BUILD_ROOT'\n    if (stream.match(/^\\$\\{\\w+\\}/)) { return \"def\"; } // Variables like '${RPM_BUILD_ROOT}'\n\n    if (stream.match(control_flow_simple)) { return \"keyword\"; }\n    if (stream.match(control_flow_complex)) {\n      state.controlFlow = true;\n      return \"keyword\";\n    }\n    if (state.controlFlow) {\n      if (stream.match(operators)) { return \"operator\"; }\n      if (stream.match(/^(\\d+)/)) { return \"number\"; }\n      if (stream.eol()) { state.controlFlow = false; }\n    }\n\n    if (stream.match(arch)) {\n      if (stream.eol()) { state.controlFlow = false; }\n      return \"number\";\n    }\n\n    // Macros like '%make_install' or '%attr(0775,root,root)'\n    if (stream.match(/^%[\\w]+/)) {\n      if (stream.match('(')) { state.macroParameters = true; }\n      return \"keyword\";\n    }\n    if (state.macroParameters) {\n      if (stream.match(/^\\d+/)) { return \"number\";}\n      if (stream.match(')')) {\n        state.macroParameters = false;\n        return \"keyword\";\n      }\n    }\n\n    // Macros like '%{defined fedora}'\n    if (stream.match(/^%\\{\\??[\\w \\-\\:\\!]+\\}/)) {\n      if (stream.eol()) { state.controlFlow = false; }\n      return \"def\";\n    }\n\n    stream.next();\n    return null;\n  }\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/rpm.js\n"));

/***/ })

}]);