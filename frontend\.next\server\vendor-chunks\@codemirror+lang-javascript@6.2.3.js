"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-javascript@6.2.3";
exports.ids = ["vendor-chunks/@codemirror+lang-javascript@6.2.3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-javascript@6.2.3/node_modules/@codemirror/lang-javascript/dist/index.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-javascript@6.2.3/node_modules/@codemirror/lang-javascript/dist/index.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autoCloseTags: () => (/* binding */ autoCloseTags),\n/* harmony export */   completionPath: () => (/* binding */ completionPath),\n/* harmony export */   esLint: () => (/* binding */ esLint),\n/* harmony export */   javascript: () => (/* binding */ javascript),\n/* harmony export */   javascriptLanguage: () => (/* binding */ javascriptLanguage),\n/* harmony export */   jsxLanguage: () => (/* binding */ jsxLanguage),\n/* harmony export */   localCompletionSource: () => (/* binding */ localCompletionSource),\n/* harmony export */   scopeCompletionSource: () => (/* binding */ scopeCompletionSource),\n/* harmony export */   snippets: () => (/* binding */ snippets),\n/* harmony export */   tsxLanguage: () => (/* binding */ tsxLanguage),\n/* harmony export */   typescriptLanguage: () => (/* binding */ typescriptLanguage),\n/* harmony export */   typescriptSnippets: () => (/* binding */ typescriptSnippets)\n/* harmony export */ });\n/* harmony import */ var _lezer_javascript__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/javascript */ \"(ssr)/./node_modules/.pnpm/@lezer+javascript@1.5.1/node_modules/@lezer/javascript/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/autocomplete */ \"(ssr)/./node_modules/.pnpm/@codemirror+autocomplete@6.18.6/node_modules/@codemirror/autocomplete/dist/index.js\");\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n\n\n\n\n\n\n\n/**\nA collection of JavaScript-related\n[snippets](https://codemirror.net/6/docs/ref/#autocomplete.snippet).\n*/\nconst snippets = [\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"function ${name}(${params}) {\\n\\t${}\\n}\", {\n        label: \"function\",\n        detail: \"definition\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"for (let ${index} = 0; ${index} < ${bound}; ${index}++) {\\n\\t${}\\n}\", {\n        label: \"for\",\n        detail: \"loop\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"for (let ${name} of ${collection}) {\\n\\t${}\\n}\", {\n        label: \"for\",\n        detail: \"of loop\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"do {\\n\\t${}\\n} while (${})\", {\n        label: \"do\",\n        detail: \"loop\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"while (${}) {\\n\\t${}\\n}\", {\n        label: \"while\",\n        detail: \"loop\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"try {\\n\\t${}\\n} catch (${error}) {\\n\\t${}\\n}\", {\n        label: \"try\",\n        detail: \"/ catch block\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"if (${}) {\\n\\t${}\\n}\", {\n        label: \"if\",\n        detail: \"block\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"if (${}) {\\n\\t${}\\n} else {\\n\\t${}\\n}\", {\n        label: \"if\",\n        detail: \"/ else block\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"class ${name} {\\n\\tconstructor(${params}) {\\n\\t\\t${}\\n\\t}\\n}\", {\n        label: \"class\",\n        detail: \"definition\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"import {${names}} from \\\"${module}\\\"\\n${}\", {\n        label: \"import\",\n        detail: \"named\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"import ${name} from \\\"${module}\\\"\\n${}\", {\n        label: \"import\",\n        detail: \"default\",\n        type: \"keyword\"\n    })\n];\n/**\nA collection of snippet completions for TypeScript. Includes the\nJavaScript [snippets](https://codemirror.net/6/docs/ref/#lang-javascript.snippets).\n*/\nconst typescriptSnippets = /*@__PURE__*/snippets.concat([\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"interface ${name} {\\n\\t${}\\n}\", {\n        label: \"interface\",\n        detail: \"definition\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"type ${name} = ${type}\", {\n        label: \"type\",\n        detail: \"definition\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.snippetCompletion)(\"enum ${name} {\\n\\t${}\\n}\", {\n        label: \"enum\",\n        detail: \"definition\",\n        type: \"keyword\"\n    })\n]);\n\nconst cache = /*@__PURE__*/new _lezer_common__WEBPACK_IMPORTED_MODULE_1__.NodeWeakMap();\nconst ScopeNodes = /*@__PURE__*/new Set([\n    \"Script\", \"Block\",\n    \"FunctionExpression\", \"FunctionDeclaration\", \"ArrowFunction\", \"MethodDeclaration\",\n    \"ForStatement\"\n]);\nfunction defID(type) {\n    return (node, def) => {\n        let id = node.node.getChild(\"VariableDefinition\");\n        if (id)\n            def(id, type);\n        return true;\n    };\n}\nconst functionContext = [\"FunctionDeclaration\"];\nconst gatherCompletions = {\n    FunctionDeclaration: /*@__PURE__*/defID(\"function\"),\n    ClassDeclaration: /*@__PURE__*/defID(\"class\"),\n    ClassExpression: () => true,\n    EnumDeclaration: /*@__PURE__*/defID(\"constant\"),\n    TypeAliasDeclaration: /*@__PURE__*/defID(\"type\"),\n    NamespaceDeclaration: /*@__PURE__*/defID(\"namespace\"),\n    VariableDefinition(node, def) { if (!node.matchContext(functionContext))\n        def(node, \"variable\"); },\n    TypeDefinition(node, def) { def(node, \"type\"); },\n    __proto__: null\n};\nfunction getScope(doc, node) {\n    let cached = cache.get(node);\n    if (cached)\n        return cached;\n    let completions = [], top = true;\n    function def(node, type) {\n        let name = doc.sliceString(node.from, node.to);\n        completions.push({ label: name, type });\n    }\n    node.cursor(_lezer_common__WEBPACK_IMPORTED_MODULE_1__.IterMode.IncludeAnonymous).iterate(node => {\n        if (top) {\n            top = false;\n        }\n        else if (node.name) {\n            let gather = gatherCompletions[node.name];\n            if (gather && gather(node, def) || ScopeNodes.has(node.name))\n                return false;\n        }\n        else if (node.to - node.from > 8192) {\n            // Allow caching for bigger internal nodes\n            for (let c of getScope(doc, node.node))\n                completions.push(c);\n            return false;\n        }\n    });\n    cache.set(node, completions);\n    return completions;\n}\nconst Identifier = /^[\\w$\\xa1-\\uffff][\\w$\\d\\xa1-\\uffff]*$/;\nconst dontComplete = [\n    \"TemplateString\", \"String\", \"RegExp\",\n    \"LineComment\", \"BlockComment\",\n    \"VariableDefinition\", \"TypeDefinition\", \"Label\",\n    \"PropertyDefinition\", \"PropertyName\",\n    \"PrivatePropertyDefinition\", \"PrivatePropertyName\",\n    \"JSXText\", \"JSXAttributeValue\", \"JSXOpenTag\", \"JSXCloseTag\", \"JSXSelfClosingTag\",\n    \".\", \"?.\"\n];\n/**\nCompletion source that looks up locally defined names in\nJavaScript code.\n*/\nfunction localCompletionSource(context) {\n    let inner = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxTree)(context.state).resolveInner(context.pos, -1);\n    if (dontComplete.indexOf(inner.name) > -1)\n        return null;\n    let isWord = inner.name == \"VariableName\" ||\n        inner.to - inner.from < 20 && Identifier.test(context.state.sliceDoc(inner.from, inner.to));\n    if (!isWord && !context.explicit)\n        return null;\n    let options = [];\n    for (let pos = inner; pos; pos = pos.parent) {\n        if (ScopeNodes.has(pos.name))\n            options = options.concat(getScope(context.state.doc, pos));\n    }\n    return {\n        options,\n        from: isWord ? inner.from : context.pos,\n        validFor: Identifier\n    };\n}\nfunction pathFor(read, member, name) {\n    var _a;\n    let path = [];\n    for (;;) {\n        let obj = member.firstChild, prop;\n        if ((obj === null || obj === void 0 ? void 0 : obj.name) == \"VariableName\") {\n            path.push(read(obj));\n            return { path: path.reverse(), name };\n        }\n        else if ((obj === null || obj === void 0 ? void 0 : obj.name) == \"MemberExpression\" && ((_a = (prop = obj.lastChild)) === null || _a === void 0 ? void 0 : _a.name) == \"PropertyName\") {\n            path.push(read(prop));\n            member = obj;\n        }\n        else {\n            return null;\n        }\n    }\n}\n/**\nHelper function for defining JavaScript completion sources. It\nreturns the completable name and object path for a completion\ncontext, or null if no name/property completion should happen at\nthat position. For example, when completing after `a.b.c` it will\nreturn `{path: [\"a\", \"b\"], name: \"c\"}`. When completing after `x`\nit will return `{path: [], name: \"x\"}`. When not in a property or\nname, it will return null if `context.explicit` is false, and\n`{path: [], name: \"\"}` otherwise.\n*/\nfunction completionPath(context) {\n    let read = (node) => context.state.doc.sliceString(node.from, node.to);\n    let inner = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxTree)(context.state).resolveInner(context.pos, -1);\n    if (inner.name == \"PropertyName\") {\n        return pathFor(read, inner.parent, read(inner));\n    }\n    else if ((inner.name == \".\" || inner.name == \"?.\") && inner.parent.name == \"MemberExpression\") {\n        return pathFor(read, inner.parent, \"\");\n    }\n    else if (dontComplete.indexOf(inner.name) > -1) {\n        return null;\n    }\n    else if (inner.name == \"VariableName\" || inner.to - inner.from < 20 && Identifier.test(read(inner))) {\n        return { path: [], name: read(inner) };\n    }\n    else if (inner.name == \"MemberExpression\") {\n        return pathFor(read, inner, \"\");\n    }\n    else {\n        return context.explicit ? { path: [], name: \"\" } : null;\n    }\n}\nfunction enumeratePropertyCompletions(obj, top) {\n    let options = [], seen = new Set;\n    for (let depth = 0;; depth++) {\n        for (let name of (Object.getOwnPropertyNames || Object.keys)(obj)) {\n            if (!/^[a-zA-Z_$\\xaa-\\uffdc][\\w$\\xaa-\\uffdc]*$/.test(name) || seen.has(name))\n                continue;\n            seen.add(name);\n            let value;\n            try {\n                value = obj[name];\n            }\n            catch (_) {\n                continue;\n            }\n            options.push({\n                label: name,\n                type: typeof value == \"function\" ? (/^[A-Z]/.test(name) ? \"class\" : top ? \"function\" : \"method\")\n                    : top ? \"variable\" : \"property\",\n                boost: -depth\n            });\n        }\n        let next = Object.getPrototypeOf(obj);\n        if (!next)\n            return options;\n        obj = next;\n    }\n}\n/**\nDefines a [completion source](https://codemirror.net/6/docs/ref/#autocomplete.CompletionSource) that\ncompletes from the given scope object (for example `globalThis`).\nWill enter properties of the object when completing properties on\na directly-named path.\n*/\nfunction scopeCompletionSource(scope) {\n    let cache = new Map;\n    return (context) => {\n        let path = completionPath(context);\n        if (!path)\n            return null;\n        let target = scope;\n        for (let step of path.path) {\n            target = target[step];\n            if (!target)\n                return null;\n        }\n        let options = cache.get(target);\n        if (!options)\n            cache.set(target, options = enumeratePropertyCompletions(target, !path.path.length));\n        return {\n            from: context.pos - path.name.length,\n            options,\n            validFor: Identifier\n        };\n    };\n}\n\n/**\nA language provider based on the [Lezer JavaScript\nparser](https://github.com/lezer-parser/javascript), extended with\nhighlighting and indentation information.\n*/\nconst javascriptLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.LRLanguage.define({\n    name: \"javascript\",\n    parser: /*@__PURE__*/_lezer_javascript__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.indentNodeProp.add({\n                IfStatement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.continuedIndent)({ except: /^\\s*({|else\\b)/ }),\n                TryStatement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.continuedIndent)({ except: /^\\s*({|catch\\b|finally\\b)/ }),\n                LabeledStatement: _codemirror_language__WEBPACK_IMPORTED_MODULE_3__.flatIndent,\n                SwitchBody: context => {\n                    let after = context.textAfter, closed = /^\\s*\\}/.test(after), isCase = /^\\s*(case|default)\\b/.test(after);\n                    return context.baseIndent + (closed ? 0 : isCase ? 1 : 2) * context.unit;\n                },\n                Block: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.delimitedIndent)({ closing: \"}\" }),\n                ArrowFunction: cx => cx.baseIndent + cx.unit,\n                \"TemplateString BlockComment\": () => null,\n                \"Statement Property\": /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.continuedIndent)({ except: /^{/ }),\n                JSXElement(context) {\n                    let closed = /^\\s*<\\//.test(context.textAfter);\n                    return context.lineIndent(context.node.from) + (closed ? 0 : context.unit);\n                },\n                JSXEscape(context) {\n                    let closed = /\\s*\\}/.test(context.textAfter);\n                    return context.lineIndent(context.node.from) + (closed ? 0 : context.unit);\n                },\n                \"JSXOpenTag JSXSelfClosingTag\"(context) {\n                    return context.column(context.node.from) + context.unit;\n                }\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.foldNodeProp.add({\n                \"Block ClassBody SwitchBody EnumBody ObjectExpression ArrayExpression ObjectType\": _codemirror_language__WEBPACK_IMPORTED_MODULE_3__.foldInside,\n                BlockComment(tree) { return { from: tree.from + 2, to: tree.to - 2 }; }\n            })\n        ]\n    }),\n    languageData: {\n        closeBrackets: { brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"] },\n        commentTokens: { line: \"//\", block: { open: \"/*\", close: \"*/\" } },\n        indentOnInput: /^\\s*(?:case |default:|\\{|\\}|<\\/)$/,\n        wordChars: \"$\"\n    }\n});\nconst jsxSublanguage = {\n    test: node => /^JSX/.test(node.name),\n    facet: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.defineLanguageFacet)({ commentTokens: { block: { open: \"{/*\", close: \"*/}\" } } })\n};\n/**\nA language provider for TypeScript.\n*/\nconst typescriptLanguage = /*@__PURE__*/javascriptLanguage.configure({ dialect: \"ts\" }, \"typescript\");\n/**\nLanguage provider for JSX.\n*/\nconst jsxLanguage = /*@__PURE__*/javascriptLanguage.configure({\n    dialect: \"jsx\",\n    props: [/*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.sublanguageProp.add(n => n.isTop ? [jsxSublanguage] : undefined)]\n});\n/**\nLanguage provider for JSX + TypeScript.\n*/\nconst tsxLanguage = /*@__PURE__*/javascriptLanguage.configure({\n    dialect: \"jsx ts\",\n    props: [/*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.sublanguageProp.add(n => n.isTop ? [jsxSublanguage] : undefined)]\n}, \"typescript\");\nlet kwCompletion = (name) => ({ label: name, type: \"keyword\" });\nconst keywords = /*@__PURE__*/\"break case const continue default delete export extends false finally in instanceof let new return static super switch this throw true typeof var yield\".split(\" \").map(kwCompletion);\nconst typescriptKeywords = /*@__PURE__*/keywords.concat(/*@__PURE__*/[\"declare\", \"implements\", \"private\", \"protected\", \"public\"].map(kwCompletion));\n/**\nJavaScript support. Includes [snippet](https://codemirror.net/6/docs/ref/#lang-javascript.snippets)\nand local variable completion.\n*/\nfunction javascript(config = {}) {\n    let lang = config.jsx ? (config.typescript ? tsxLanguage : jsxLanguage)\n        : config.typescript ? typescriptLanguage : javascriptLanguage;\n    let completions = config.typescript ? typescriptSnippets.concat(typescriptKeywords) : snippets.concat(keywords);\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_3__.LanguageSupport(lang, [\n        javascriptLanguage.data.of({\n            autocomplete: (0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.ifNotIn)(dontComplete, (0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_2__.completeFromList)(completions))\n        }),\n        javascriptLanguage.data.of({\n            autocomplete: localCompletionSource\n        }),\n        config.jsx ? autoCloseTags : [],\n    ]);\n}\nfunction findOpenTag(node) {\n    for (;;) {\n        if (node.name == \"JSXOpenTag\" || node.name == \"JSXSelfClosingTag\" || node.name == \"JSXFragmentTag\")\n            return node;\n        if (node.name == \"JSXEscape\" || !node.parent)\n            return null;\n        node = node.parent;\n    }\n}\nfunction elementName(doc, tree, max = doc.length) {\n    for (let ch = tree === null || tree === void 0 ? void 0 : tree.firstChild; ch; ch = ch.nextSibling) {\n        if (ch.name == \"JSXIdentifier\" || ch.name == \"JSXBuiltin\" || ch.name == \"JSXNamespacedName\" ||\n            ch.name == \"JSXMemberExpression\")\n            return doc.sliceString(ch.from, Math.min(ch.to, max));\n    }\n    return \"\";\n}\nconst android = typeof navigator == \"object\" && /*@__PURE__*//Android\\b/.test(navigator.userAgent);\n/**\nExtension that will automatically insert JSX close tags when a `>` or\n`/` is typed.\n*/\nconst autoCloseTags = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.inputHandler.of((view, from, to, text, defaultInsert) => {\n    if ((android ? view.composing : view.compositionStarted) || view.state.readOnly ||\n        from != to || (text != \">\" && text != \"/\") ||\n        !javascriptLanguage.isActiveAt(view.state, from, -1))\n        return false;\n    let base = defaultInsert(), { state } = base;\n    let closeTags = state.changeByRange(range => {\n        var _a;\n        let { head } = range, around = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_3__.syntaxTree)(state).resolveInner(head - 1, -1), name;\n        if (around.name == \"JSXStartTag\")\n            around = around.parent;\n        if (state.doc.sliceString(head - 1, head) != text || around.name == \"JSXAttributeValue\" && around.to > head) ;\n        else if (text == \">\" && around.name == \"JSXFragmentTag\") {\n            return { range, changes: { from: head, insert: `</>` } };\n        }\n        else if (text == \"/\" && around.name == \"JSXStartCloseTag\") {\n            let empty = around.parent, base = empty.parent;\n            if (base && empty.from == head - 2 &&\n                ((name = elementName(state.doc, base.firstChild, head)) || ((_a = base.firstChild) === null || _a === void 0 ? void 0 : _a.name) == \"JSXFragmentTag\")) {\n                let insert = `${name}>`;\n                return { range: _codemirror_state__WEBPACK_IMPORTED_MODULE_5__.EditorSelection.cursor(head + insert.length, -1), changes: { from: head, insert } };\n            }\n        }\n        else if (text == \">\") {\n            let openTag = findOpenTag(around);\n            if (openTag && openTag.name == \"JSXOpenTag\" &&\n                !/^\\/?>|^<\\//.test(state.doc.sliceString(head, head + 2)) &&\n                (name = elementName(state.doc, openTag, head)))\n                return { range, changes: { from: head, insert: `</${name}>` } };\n        }\n        return { range };\n    });\n    if (closeTags.changes.empty)\n        return false;\n    view.dispatch([\n        base,\n        state.update(closeTags, { userEvent: \"input.complete\", scrollIntoView: true })\n    ]);\n    return true;\n});\n\n/**\nConnects an [ESLint](https://eslint.org/) linter to CodeMirror's\n[lint](https://codemirror.net/6/docs/ref/#lint) integration. `eslint` should be an instance of the\n[`Linter`](https://eslint.org/docs/developer-guide/nodejs-api#linter)\nclass, and `config` an optional ESLint configuration. The return\nvalue of this function can be passed to [`linter`](https://codemirror.net/6/docs/ref/#lint.linter)\nto create a JavaScript linting extension.\n\nNote that ESLint targets node, and is tricky to run in the\nbrowser. The\n[eslint-linter-browserify](https://github.com/UziTech/eslint-linter-browserify)\npackage may help with that (see\n[example](https://github.com/UziTech/eslint-linter-browserify/blob/master/example/script.js)).\n*/\nfunction esLint(eslint, config) {\n    if (!config) {\n        config = {\n            parserOptions: { ecmaVersion: 2019, sourceType: \"module\" },\n            env: { browser: true, node: true, es6: true, es2015: true, es2017: true, es2020: true },\n            rules: {}\n        };\n        eslint.getRules().forEach((desc, name) => {\n            if (desc.meta.docs.recommended)\n                config.rules[name] = 2;\n        });\n    }\n    return (view) => {\n        let { state } = view, found = [];\n        for (let { from, to } of javascriptLanguage.findRegions(state)) {\n            let fromLine = state.doc.lineAt(from), offset = { line: fromLine.number - 1, col: from - fromLine.from, pos: from };\n            for (let d of eslint.verify(state.sliceDoc(from, to), config))\n                found.push(translateDiagnostic(d, state.doc, offset));\n        }\n        return found;\n    };\n}\nfunction mapPos(line, col, doc, offset) {\n    return doc.line(line + offset.line).from + col + (line == 1 ? offset.col - 1 : -1);\n}\nfunction translateDiagnostic(input, doc, offset) {\n    let start = mapPos(input.line, input.column, doc, offset);\n    let result = {\n        from: start,\n        to: input.endLine != null && input.endColumn != 1 ? mapPos(input.endLine, input.endColumn, doc, offset) : start,\n        message: input.message,\n        source: input.ruleId ? \"eslint:\" + input.ruleId : \"eslint\",\n        severity: input.severity == 1 ? \"warning\" : \"error\",\n    };\n    if (input.fix) {\n        let { range, text } = input.fix, from = range[0] + offset.pos - start, to = range[1] + offset.pos - start;\n        result.actions = [{\n                name: \"fix\",\n                apply(view, start) {\n                    view.dispatch({ changes: { from: start + from, to: start + to, insert: text }, scrollIntoView: true });\n                }\n            }];\n    }\n    return result;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-javascript@6.2.3/node_modules/@codemirror/lang-javascript/dist/index.js\n");

/***/ })

};
;