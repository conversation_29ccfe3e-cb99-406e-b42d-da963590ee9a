"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Users } from "lucide-react";

interface ProjectsSearchAndFilterProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  activeFilter: string;
  onFilterChange: (filter: string) => void;
}

export function ProjectsSearchAndFilter({
  searchQuery,
  onSearchChange,
  activeFilter,
  onFilterChange,
}: ProjectsSearchAndFilterProps) {
  return (
    <div className="mb-6 flex flex-wrap justify-between items-center gap-3">
      <div className="flex items-center gap-2 max-w-md w-full md:w-auto md:flex-1">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search projects..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>

        <Button variant="outline" size="icon" className="h-9 w-9 flex-shrink-0">
          <Users className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex items-center gap-2 ml-auto">
        <Button
          variant="outline"
          size="sm"
          className={`h-9 px-4 ${activeFilter === 'all' ? 'bg-accent/10 border-accent/30' : ''}`}
          onClick={() => onFilterChange('all')}
        >
          All
        </Button>
        <Button
          variant="outline"
          size="sm"
          className={`h-9 px-4 ${activeFilter === 'active' ? 'bg-accent/10 border-accent/30' : ''}`}
          onClick={() => onFilterChange('active')}
        >
          Active
        </Button>
        <Button
          variant="outline"
          size="sm"
          className={`h-9 px-4 ${activeFilter === 'archived' ? 'bg-accent/10 border-accent/30' : ''}`}
          onClick={() => onFilterChange('archived')}
        >
          Archived
        </Button>
      </div>
    </div>
  );
}
