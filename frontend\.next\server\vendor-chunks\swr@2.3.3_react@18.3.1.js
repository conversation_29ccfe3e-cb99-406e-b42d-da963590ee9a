"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/swr@2.3.3_react@18.3.1";
exports.ids = ["vendor-chunks/swr@2.3.3_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A: () => (/* binding */ noop),\n/* harmony export */   B: () => (/* binding */ isPromiseLike),\n/* harmony export */   I: () => (/* binding */ IS_REACT_LEGACY),\n/* harmony export */   O: () => (/* binding */ OBJECT),\n/* harmony export */   S: () => (/* binding */ SWRConfigContext),\n/* harmony export */   U: () => (/* binding */ UNDEFINED),\n/* harmony export */   a: () => (/* binding */ isFunction),\n/* harmony export */   b: () => (/* binding */ SWRGlobalState),\n/* harmony export */   c: () => (/* binding */ cache),\n/* harmony export */   d: () => (/* binding */ defaultConfig),\n/* harmony export */   e: () => (/* binding */ isUndefined),\n/* harmony export */   f: () => (/* binding */ mergeConfigs),\n/* harmony export */   g: () => (/* binding */ SWRConfig),\n/* harmony export */   h: () => (/* binding */ initCache),\n/* harmony export */   i: () => (/* binding */ isWindowDefined),\n/* harmony export */   j: () => (/* binding */ mutate),\n/* harmony export */   k: () => (/* binding */ compare),\n/* harmony export */   l: () => (/* binding */ stableHash),\n/* harmony export */   m: () => (/* binding */ mergeObjects),\n/* harmony export */   n: () => (/* binding */ internalMutate),\n/* harmony export */   o: () => (/* binding */ getTimestamp),\n/* harmony export */   p: () => (/* binding */ preset),\n/* harmony export */   q: () => (/* binding */ defaultConfigOptions),\n/* harmony export */   r: () => (/* binding */ IS_SERVER),\n/* harmony export */   s: () => (/* binding */ serialize),\n/* harmony export */   t: () => (/* binding */ rAF),\n/* harmony export */   u: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   v: () => (/* binding */ slowConnection),\n/* harmony export */   w: () => (/* binding */ isDocumentDefined),\n/* harmony export */   x: () => (/* binding */ isLegacyDeno),\n/* harmony export */   y: () => (/* binding */ hasRequestAnimationFrame),\n/* harmony export */   z: () => (/* binding */ createCacheHelper)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _events_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./events.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var dequal_lite__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dequal/lite */ \"(ssr)/./node_modules/.pnpm/dequal@2.0.3/node_modules/dequal/lite/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ A,B,I,O,S,U,a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z auto */ \n\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = \"undefined\" != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst isLegacyDeno = isWindowDefined && 'Deno' in window;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener('visibilitychange', callback);\n    }\n    onWindowEvent('focus', callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener('visibilitychange', callback);\n        }\n        offWindowEvent('focus', callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent('online', onOnline);\n    onWindowEvent('offline', onOffline);\n    return ()=>{\n        offWindowEvent('online', onOnline);\n        offWindowEvent('offline', onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\nconst IS_REACT_LEGACY = !react__WEBPACK_IMPORTED_MODULE_0__.useId;\nconst IS_SERVER = !isWindowDefined || isLegacyDeno;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? react__WEBPACK_IMPORTED_MODULE_0__.useEffect : react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    'slow-2g',\n    '2g'\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === 'boolean' ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (!/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const startRevalidate = ()=>{\n            const revalidators = EVENT_REVALIDATORS[key];\n            const revalidate = isFunction(options.revalidate) ? options.revalidate(get().data, _k) : options.revalidate !== false;\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](_events_mjs__WEBPACK_IMPORTED_MODULE_2__.MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (error) throw error;\n                return data;\n            } else if (error && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data: committedData,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!error) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    const populateCachedData = populateCache(data, committedData);\n                    set({\n                        data: populateCachedData,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                } else {\n                    // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                    set({\n                        data,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                }\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        Promise.resolve(startRevalidate()).then(()=>{\n            // The mutation and revalidation are ended, we can clear it since the data is\n            // not an optimistic value anymore.\n            set({\n                _c: UNDEFINED\n            });\n        });\n        // Throw error or return data\n        if (error) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return data;\n    }\n}\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = Object.create(null);\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = Object.create(null);\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    Object.create(null),\n                    Object.create(null),\n                    Object.create(null),\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, _events_mjs__WEBPACK_IMPORTED_MODULE_2__.FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, _events_mjs__WEBPACK_IMPORTED_MODULE_2__.RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = dequal_lite__WEBPACK_IMPORTED_MODULE_1__.dequal;\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, preset);\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1, fallback: f1 } = a;\n        const { use: u2, fallback: f2 } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\nconst SWRConfigContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({});\nconst SWRConfig = (props)=>{\n    const { value } = props;\n    const parentConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"SWRConfig.useMemo[config]\": ()=>isFunctionalConfig ? value(parentConfig) : value\n    }[\"SWRConfig.useMemo[config]\"], [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"SWRConfig.useMemo[extendedConfig]\": ()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config)\n    }[\"SWRConfig.useMemo[extendedConfig]\"], [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect({\n        \"SWRConfig.useIsomorphicLayoutEffect\": ()=>{\n            if (cacheContext) {\n                cacheContext[2] && cacheContext[2]();\n                return cacheContext[3];\n            }\n        }\n    }[\"SWRConfig.useIsomorphicLayoutEffect\"], []);\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/constants.mjs":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/constants.mjs ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITE_PREFIX: () => (/* binding */ INFINITE_PREFIX)\n/* harmony export */ });\nconst INFINITE_PREFIX = '$inf$';\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vc3dyQDIuMy4zX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvc3dyL2Rpc3QvX2ludGVybmFsL2NvbnN0YW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBOztBQUUyQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbmRyZVxcT25lRHJpdmVcXERlc2t0b3BcXEdpdGh1YiBSZXBvc2l0b3JpZXNcXGRlbW9cXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxzd3JAMi4zLjNfcmVhY3RAMTguMy4xXFxub2RlX21vZHVsZXNcXHN3clxcZGlzdFxcX2ludGVybmFsXFxjb25zdGFudHMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IElORklOSVRFX1BSRUZJWCA9ICckaW5mJCc7XG5cbmV4cG9ydCB7IElORklOSVRFX1BSRUZJWCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/constants.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/events.mjs":
/*!**********************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/events.mjs ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ERROR_REVALIDATE_EVENT: () => (/* binding */ ERROR_REVALIDATE_EVENT),\n/* harmony export */   FOCUS_EVENT: () => (/* binding */ FOCUS_EVENT),\n/* harmony export */   MUTATE_EVENT: () => (/* binding */ MUTATE_EVENT),\n/* harmony export */   RECONNECT_EVENT: () => (/* binding */ RECONNECT_EVENT)\n/* harmony export */ });\nconst FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vc3dyQDIuMy4zX3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvc3dyL2Rpc3QvX2ludGVybmFsL2V2ZW50cy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUU4RSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbmRyZVxcT25lRHJpdmVcXERlc2t0b3BcXEdpdGh1YiBSZXBvc2l0b3JpZXNcXGRlbW9cXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxzd3JAMi4zLjNfcmVhY3RAMTguMy4xXFxub2RlX21vZHVsZXNcXHN3clxcZGlzdFxcX2ludGVybmFsXFxldmVudHMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IEZPQ1VTX0VWRU5UID0gMDtcbmNvbnN0IFJFQ09OTkVDVF9FVkVOVCA9IDE7XG5jb25zdCBNVVRBVEVfRVZFTlQgPSAyO1xuY29uc3QgRVJST1JfUkVWQUxJREFURV9FVkVOVCA9IDM7XG5cbmV4cG9ydCB7IEVSUk9SX1JFVkFMSURBVEVfRVZFTlQsIEZPQ1VTX0VWRU5ULCBNVVRBVEVfRVZFTlQsIFJFQ09OTkVDVF9FVkVOVCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/events.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/index.mjs":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/index.mjs ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   INFINITE_PREFIX: () => (/* reexport safe */ _constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX),\n/* harmony export */   IS_REACT_LEGACY: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.I),\n/* harmony export */   IS_SERVER: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.r),\n/* harmony export */   OBJECT: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.O),\n/* harmony export */   SWRConfig: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.g),\n/* harmony export */   SWRGlobalState: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.b),\n/* harmony export */   UNDEFINED: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.U),\n/* harmony export */   cache: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.c),\n/* harmony export */   compare: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.k),\n/* harmony export */   createCacheHelper: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.z),\n/* harmony export */   defaultConfig: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.d),\n/* harmony export */   defaultConfigOptions: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.q),\n/* harmony export */   getTimestamp: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.o),\n/* harmony export */   hasRequestAnimationFrame: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.y),\n/* harmony export */   initCache: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.h),\n/* harmony export */   internalMutate: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.n),\n/* harmony export */   isDocumentDefined: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.w),\n/* harmony export */   isFunction: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.a),\n/* harmony export */   isLegacyDeno: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.x),\n/* harmony export */   isPromiseLike: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.B),\n/* harmony export */   isUndefined: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.e),\n/* harmony export */   isWindowDefined: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.i),\n/* harmony export */   mergeConfigs: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.f),\n/* harmony export */   mergeObjects: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.m),\n/* harmony export */   mutate: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.j),\n/* harmony export */   noop: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.A),\n/* harmony export */   normalize: () => (/* binding */ normalize),\n/* harmony export */   preload: () => (/* binding */ preload),\n/* harmony export */   preset: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.p),\n/* harmony export */   rAF: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.t),\n/* harmony export */   revalidateEvents: () => (/* reexport module object */ _events_mjs__WEBPACK_IMPORTED_MODULE_1__),\n/* harmony export */   serialize: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.s),\n/* harmony export */   slowConnection: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.v),\n/* harmony export */   stableHash: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.l),\n/* harmony export */   subscribeCallback: () => (/* binding */ subscribeCallback),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* reexport safe */ _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.u),\n/* harmony export */   useSWRConfig: () => (/* binding */ useSWRConfig),\n/* harmony export */   withArgs: () => (/* binding */ withArgs),\n/* harmony export */   withMiddleware: () => (/* binding */ withMiddleware)\n/* harmony export */ });\n/* harmony import */ var _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./config-context-client-v7VOFo66.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs\");\n/* harmony import */ var _events_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./events.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var _constants_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/constants.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\n\n\n\n// @ts-expect-error\nconst enableDevtools = _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.i && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = react__WEBPACK_IMPORTED_MODULE_3__;\n    }\n};\n\nconst normalize = (args)=>{\n    return (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.a)(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    return (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.m)(_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.d, (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.S));\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(key_);\n    const [, , , PRELOAD] = _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.b.get(_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.c);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.s)(key_);\n            const [, , , PRELOAD] = _config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.b.get(_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.c);\n            if (key.startsWith(_constants_mjs__WEBPACK_IMPORTED_MODULE_2__.INFINITE_PREFIX)) {\n                // we want the infinite fetcher to be called.\n                // handling of the PRELOAD cache happens there.\n                return fetcher_(...args);\n            }\n            const req = PRELOAD[key];\n            if ((0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.e)(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = (0,_config_context_client_v7VOFo66_mjs__WEBPACK_IMPORTED_MODULE_0__.f)(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/index/index.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/index/index.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SWRConfig: () => (/* binding */ SWRConfig),\n/* harmony export */   \"default\": () => (/* binding */ useSWR),\n/* harmony export */   mutate: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.j),\n/* harmony export */   preload: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.preload),\n/* harmony export */   unstable_serialize: () => (/* binding */ unstable_serialize),\n/* harmony export */   useSWRConfig: () => (/* reexport safe */ _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.useSWRConfig)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/.pnpm/use-sync-external-store@1.5.0_react@18.3.1/node_modules/use-sync-external-store/shim/index.js\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/config-context-client-v7VOFo66.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/events.mjs\");\n/* harmony import */ var _internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../_internal/index.mjs */ \"(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/_internal/index.mjs\");\n\n\n\n\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\nconst isObjectType = (value, type)=>OBJECT.prototype.toString.call(value) === `[object ${type}]`;\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const isDate = isObjectType(arg, 'Date');\n    const isRegex = isObjectType(arg, 'RegExp');\n    const isPlainObject = isObjectType(arg, 'Object');\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && !isRegex) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (Array.isArray(arg)) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (isPlainObject) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\nconst unstable_serialize = (key)=>serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = react__WEBPACK_IMPORTED_MODULE_0__.use || // This extra generic is to avoid TypeScript mixing up the generic and JSX sytax\n// and emitting an error.\n// We assume that this is only for the `use(thenable)` case, not `use(context)`.\n// https://github.com/facebook/react/blob/aed00dacfb79d17c53218404c52b1c7aa59c4a89/packages/react-server/src/ReactFizzThenable.js#L45\n((thenable)=>{\n    switch(thenable.status){\n        case 'pending':\n            throw thenable;\n        case 'fulfilled':\n            return thenable.value;\n        case 'rejected':\n            throw thenable.reason;\n        default:\n            thenable.status = 'pending';\n            thenable.then((v)=>{\n                thenable.status = 'fulfilled';\n                thenable.value = v;\n            }, (e)=>{\n                thenable.status = 'rejected';\n                thenable.reason = e;\n            });\n            throw thenable;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.b.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.s)(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Refs to keep the key and config.\n    const keyRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(key);\n    const fetcherRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fetcher);\n    const configRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.z)(cache, key);\n    const stateDependencies = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({}).current;\n    // Resolve the fallback data from either the inline option, or the global provider.\n    // If it's a promise, we simply let React suspend and resolve it for us.\n    const fallback = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(fallbackData) ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(config.fallback) ? _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U : config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            return revalidateIfStale !== false;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.m)(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)((0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData) ? fallback && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.B)(fallback) ? use(fallback) : fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(data);\n    const returnedData = keepPreviousData ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData) ? (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(laggyDataRef.current) ? data : laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.I) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.o)()\n                ];\n            }\n            // Wait until the ongoing request is done. Deduplication is also\n            // considered here.\n            ;\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.a)(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](_internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    const boundMutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.n)(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.U, WITH_DEDUPE);\n        let nextFocusRevalidatedAt = 0;\n        if (getConfig().revalidateOnFocus) {\n            const initNow = Date.now();\n            nextFocusRevalidatedAt = initNow + getConfig().focusThrottleInterval;\n        }\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        const onRevalidate = (type, opts = {})=>{\n            if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == _internal_index_mjs__WEBPACK_IMPORTED_MODULE_4__.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.subscribeCallback)(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) || _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.r) {\n                // Revalidate immediately.\n                softRevalidate();\n            } else {\n                // Delay the revalidate if we have data to return so we won't block\n                // rendering.\n                (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.t)(softRevalidate);\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.u)(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.a)(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any fallback data. This causes hydration errors. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.I && _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.r) {\n            throw new Error('Fallback data is required when using Suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if ((0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!(0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.e)(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    const swrResponse = {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n    return swrResponse;\n};\nconst SWRConfig = _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.O.defineProperty(_internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.g, 'defaultValue', {\n    value: _internal_index_mjs__WEBPACK_IMPORTED_MODULE_2__.d\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error, isLoading } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (isLoading) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = (0,_internal_index_mjs__WEBPACK_IMPORTED_MODULE_3__.withArgs)(useSWRHandler);\n\n// useSWR\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.3.1/node_modules/swr/dist/index/index.mjs\n");

/***/ })

};
;