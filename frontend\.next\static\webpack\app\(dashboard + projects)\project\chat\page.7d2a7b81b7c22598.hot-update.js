"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard + projects)/project/chat/page",{

/***/ "(app-pages-browser)/./src/hooks/use-code-execution.ts":
/*!*****************************************!*\
  !*** ./src/hooks/use-code-execution.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractCodeBlocks: () => (/* binding */ extractCodeBlocks),\n/* harmony export */   generateExecutionKey: () => (/* binding */ generateExecutionKey),\n/* harmony export */   hasExecutableCode: () => (/* binding */ hasExecutableCode),\n/* harmony export */   useCodeExecution: () => (/* binding */ useCodeExecution)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.1_@babel+core@7.26.10_react-dom@18.3.1_react@18.3.1__react@18.3.1_sass@1.87.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.3_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ useCodeExecution,extractCodeBlocks,hasExecutableCode,generateExecutionKey auto */ \n\nfunction useCodeExecution() {\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const executeCode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useCodeExecution.useCallback[executeCode]\": async function(code) {\n            let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'python', timeout = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 30000, resultKey = arguments.length > 3 ? arguments[3] : void 0;\n            const key = resultKey || \"\".concat(Date.now(), \"-\").concat(Math.random());\n            // Set loading state\n            setResults({\n                \"useCodeExecution.useCallback[executeCode]\": (prev)=>({\n                        ...prev,\n                        [key]: {\n                            isLoading: true,\n                            isSuccess: false\n                        }\n                    })\n            }[\"useCodeExecution.useCallback[executeCode]\"]);\n            try {\n                const response = await fetch('http://127.0.0.1:8002/api/sandbox/execute', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        code,\n                        language,\n                        timeout\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"HTTP error! status: \".concat(response.status));\n                }\n                const data = await response.json();\n                const result = {\n                    output: data.output || '',\n                    error: data.error || null,\n                    images: data.images || [],\n                    isLoading: false,\n                    isSuccess: !data.error && data.exitCode === 0,\n                    executionTime: data.execution_time\n                };\n                // Update results\n                setResults({\n                    \"useCodeExecution.useCallback[executeCode]\": (prev)=>({\n                            ...prev,\n                            [key]: result\n                        })\n                }[\"useCodeExecution.useCallback[executeCode]\"]);\n                // Show toast notification\n                if (result.isSuccess) {\n                    var _result_images;\n                    sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"Code executed successfully\", {\n                        description: ((_result_images = result.images) === null || _result_images === void 0 ? void 0 : _result_images.length) ? \"Generated \".concat(result.images.length, \" visualization(s)\") : \"Code ran without errors\"\n                    });\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Code execution failed\", {\n                        description: result.error || \"Unknown error occurred\"\n                    });\n                }\n                return result;\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n                const result = {\n                    output: '',\n                    error: errorMessage,\n                    images: [],\n                    isLoading: false,\n                    isSuccess: false\n                };\n                setResults({\n                    \"useCodeExecution.useCallback[executeCode]\": (prev)=>({\n                            ...prev,\n                            [key]: result\n                        })\n                }[\"useCodeExecution.useCallback[executeCode]\"]);\n                sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Execution failed\", {\n                    description: errorMessage\n                });\n                return result;\n            }\n        }\n    }[\"useCodeExecution.useCallback[executeCode]\"], [\n        sonner__WEBPACK_IMPORTED_MODULE_1__.toast\n    ]);\n    const getResult = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useCodeExecution.useCallback[getResult]\": (key)=>{\n            return results[key];\n        }\n    }[\"useCodeExecution.useCallback[getResult]\"], [\n        results\n    ]);\n    const clearResult = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useCodeExecution.useCallback[clearResult]\": (key)=>{\n            setResults({\n                \"useCodeExecution.useCallback[clearResult]\": (prev)=>{\n                    const newResults = {\n                        ...prev\n                    };\n                    delete newResults[key];\n                    return newResults;\n                }\n            }[\"useCodeExecution.useCallback[clearResult]\"]);\n        }\n    }[\"useCodeExecution.useCallback[clearResult]\"], []);\n    const clearAllResults = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useCodeExecution.useCallback[clearAllResults]\": ()=>{\n            setResults({});\n        }\n    }[\"useCodeExecution.useCallback[clearAllResults]\"], []);\n    return {\n        executeCode,\n        getResult,\n        clearResult,\n        clearAllResults,\n        results\n    };\n}\n// Utility function to extract code blocks from markdown\nfunction extractCodeBlocks(content) {\n    const codeBlockRegex = /```(\\w+)?\\n([\\s\\S]*?)```/g;\n    const blocks = [];\n    let match;\n    while((match = codeBlockRegex.exec(content)) !== null){\n        blocks.push({\n            code: match[2].trim(),\n            language: match[1] || 'text',\n            startIndex: match.index,\n            endIndex: match.index + match[0].length\n        });\n    }\n    return blocks;\n}\n// Utility function to check if content contains executable code\nfunction hasExecutableCode(content) {\n    const codeBlocks = extractCodeBlocks(content);\n    return codeBlocks.some((block)=>[\n            'python',\n            'py',\n            'javascript',\n            'js',\n            'typescript',\n            'ts'\n        ].includes(block.language.toLowerCase()));\n}\n// Utility function to generate a unique key for code execution\nfunction generateExecutionKey(code, language) {\n    // Create a simple hash of the code for consistent keys\n    let hash = 0;\n    const str = code + language;\n    for(let i = 0; i < str.length; i++){\n        const char = str.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // Convert to 32bit integer\n    }\n    return \"exec_\".concat(Math.abs(hash), \"_\").concat(Date.now());\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/use-code-execution.ts\n"));

/***/ })

});