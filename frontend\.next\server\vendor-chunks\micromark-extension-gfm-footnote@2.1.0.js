"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-footnote@2.1.0";
exports.ids = ["vendor-chunks/micromark-extension-gfm-footnote@2.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/micromark-extension-gfm-footnote@2.1.0/node_modules/micromark-extension-gfm-footnote/dev/lib/html.js":
/*!*********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-extension-gfm-footnote@2.1.0/node_modules/micromark-extension-gfm-footnote/dev/lib/html.js ***!
  \*********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultBackLabel: () => (/* binding */ defaultBackLabel),\n/* harmony export */   gfmFootnoteHtml: () => (/* binding */ gfmFootnoteHtml)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/.pnpm/devlop@1.1.0/node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/.pnpm/micromark-util-normalize-identifier@2.0.1/node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-sanitize-uri */ \"(ssr)/./node_modules/.pnpm/micromark-util-sanitize-uri@2.0.1/node_modules/micromark-util-sanitize-uri/dev/index.js\");\n/**\n * @import {HtmlOptions as Options} from 'micromark-extension-gfm-footnote'\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Default label.\n */\nfunction defaultBackLabel(referenceIndex, rereferenceIndex) {\n  return (\n    'Back to reference ' +\n    (referenceIndex + 1) +\n    (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n  )\n}\n\n/**\n * Create an extension for `micromark` to support GFM footnotes when\n * serializing to HTML.\n *\n * @param {Options | null | undefined} [options={}]\n *   Configuration (optional).\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GFM footnotes when serializing to HTML.\n */\nfunction gfmFootnoteHtml(options) {\n  const config = options || emptyOptions\n  const label = config.label || 'Footnotes'\n  const labelTagName = config.labelTagName || 'h2'\n  const labelAttributes =\n    config.labelAttributes === null || config.labelAttributes === undefined\n      ? 'class=\"sr-only\"'\n      : config.labelAttributes\n  const backLabel = config.backLabel || defaultBackLabel\n  const clobberPrefix =\n    config.clobberPrefix === null || config.clobberPrefix === undefined\n      ? 'user-content-'\n      : config.clobberPrefix\n  return {\n    enter: {\n      gfmFootnoteDefinition() {\n        const stack = this.getData('tightStack')\n        stack.push(false)\n      },\n      gfmFootnoteDefinitionLabelString() {\n        this.buffer()\n      },\n      gfmFootnoteCallString() {\n        this.buffer()\n      }\n    },\n    exit: {\n      gfmFootnoteDefinition() {\n        let definitions = this.getData('gfmFootnoteDefinitions')\n        const footnoteStack = this.getData('gfmFootnoteDefinitionStack')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(footnoteStack, 'expected `footnoteStack`')\n        const tightStack = this.getData('tightStack')\n        const current = footnoteStack.pop()\n        const value = this.resume()\n\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(current, 'expected to be in a footnote')\n\n        if (!definitions) {\n          this.setData('gfmFootnoteDefinitions', (definitions = {}))\n        }\n\n        if (!own.call(definitions, current)) definitions[current] = value\n\n        tightStack.pop()\n        this.setData('slurpOneLineEnding', true)\n        // “Hack” to prevent a line ending from showing up if we’re in a definition in\n        // an empty list item.\n        this.setData('lastWasTag')\n      },\n      gfmFootnoteDefinitionLabelString(token) {\n        let footnoteStack = this.getData('gfmFootnoteDefinitionStack')\n\n        if (!footnoteStack) {\n          this.setData('gfmFootnoteDefinitionStack', (footnoteStack = []))\n        }\n\n        footnoteStack.push((0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(this.sliceSerialize(token)))\n        this.resume() // Drop the label.\n        this.buffer() // Get ready for a value.\n      },\n      gfmFootnoteCallString(token) {\n        let calls = this.getData('gfmFootnoteCallOrder')\n        let counts = this.getData('gfmFootnoteCallCounts')\n        const id = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_1__.normalizeIdentifier)(this.sliceSerialize(token))\n        /** @type {number} */\n        let counter\n\n        this.resume()\n\n        if (!calls) this.setData('gfmFootnoteCallOrder', (calls = []))\n        if (!counts) this.setData('gfmFootnoteCallCounts', (counts = {}))\n\n        const index = calls.indexOf(id)\n        const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_2__.sanitizeUri)(id.toLowerCase())\n\n        if (index === -1) {\n          calls.push(id)\n          counts[id] = 1\n          counter = calls.length\n        } else {\n          counts[id]++\n          counter = index + 1\n        }\n\n        const reuseCounter = counts[id]\n\n        this.tag(\n          '<sup><a href=\"#' +\n            clobberPrefix +\n            'fn-' +\n            safeId +\n            '\" id=\"' +\n            clobberPrefix +\n            'fnref-' +\n            safeId +\n            (reuseCounter > 1 ? '-' + reuseCounter : '') +\n            '\" data-footnote-ref=\"\" aria-describedby=\"footnote-label\">' +\n            String(counter) +\n            '</a></sup>'\n        )\n      },\n      null() {\n        const calls = this.getData('gfmFootnoteCallOrder') || []\n        const counts = this.getData('gfmFootnoteCallCounts') || {}\n        const definitions = this.getData('gfmFootnoteDefinitions') || {}\n        let index = -1\n\n        if (calls.length > 0) {\n          this.lineEndingIfNeeded()\n          this.tag(\n            '<section data-footnotes=\"\" class=\"footnotes\"><' +\n              labelTagName +\n              ' id=\"footnote-label\"' +\n              (labelAttributes ? ' ' + labelAttributes : '') +\n              '>'\n          )\n          this.raw(this.encode(label))\n          this.tag('</' + labelTagName + '>')\n          this.lineEndingIfNeeded()\n          this.tag('<ol>')\n        }\n\n        while (++index < calls.length) {\n          // Called definitions are always defined.\n          const id = calls[index]\n          const safeId = (0,micromark_util_sanitize_uri__WEBPACK_IMPORTED_MODULE_2__.sanitizeUri)(id.toLowerCase())\n          let referenceIndex = 0\n          /** @type {Array<string>} */\n          const references = []\n\n          while (++referenceIndex <= counts[id]) {\n            references.push(\n              '<a href=\"#' +\n                clobberPrefix +\n                'fnref-' +\n                safeId +\n                (referenceIndex > 1 ? '-' + referenceIndex : '') +\n                '\" data-footnote-backref=\"\" aria-label=\"' +\n                this.encode(\n                  typeof backLabel === 'string'\n                    ? backLabel\n                    : backLabel(index, referenceIndex)\n                ) +\n                '\" class=\"data-footnote-backref\">↩' +\n                (referenceIndex > 1\n                  ? '<sup>' + referenceIndex + '</sup>'\n                  : '') +\n                '</a>'\n            )\n          }\n\n          const reference = references.join(' ')\n          let injected = false\n\n          this.lineEndingIfNeeded()\n          this.tag('<li id=\"' + clobberPrefix + 'fn-' + safeId + '\">')\n          this.lineEndingIfNeeded()\n          this.tag(\n            definitions[id].replace(/<\\/p>(?:\\r?\\n|\\r)?$/, function ($0) {\n              injected = true\n              return ' ' + reference + $0\n            })\n          )\n\n          if (!injected) {\n            this.lineEndingIfNeeded()\n            this.tag(reference)\n          }\n\n          this.lineEndingIfNeeded()\n          this.tag('</li>')\n        }\n\n        if (calls.length > 0) {\n          this.lineEndingIfNeeded()\n          this.tag('</ol>')\n          this.lineEndingIfNeeded()\n          this.tag('</section>')\n        }\n      }\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-extension-gfm-footnote@2.1.0/node_modules/micromark-extension-gfm-footnote/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-extension-gfm-footnote@2.1.0/node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-extension-gfm-footnote@2.1.0/node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmFootnote: () => (/* binding */ gfmFootnote)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/.pnpm/devlop@1.1.0/node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-core-commonmark */ \"(ssr)/./node_modules/.pnpm/micromark-core-commonmark@2.0.3/node_modules/micromark-core-commonmark/dev/lib/blank-line.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/.pnpm/micromark-factory-space@2.0.1/node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/.pnpm/micromark-util-character@2.1.1/node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/.pnpm/micromark-util-normalize-identifier@2.0.1/node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/constants.js\");\n/**\n * @import {Event, Exiter, Extension, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n\n\n\n\n\n\n\nconst indent = {tokenize: tokenizeIndent, partial: true}\n\n// To do: micromark should support a `_hiddenGfmFootnoteSupport`, which only\n// affects label start (image).\n// That will let us drop `tokenizePotentialGfmFootnote*`.\n// It currently has a `_hiddenFootnoteSupport`, which affects that and more.\n// That can be removed when `micromark-extension-footnote` is archived.\n\n/**\n * Create an extension for `micromark` to enable GFM footnote syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to\n *   enable GFM footnote syntax.\n */\nfunction gfmFootnote() {\n  /** @type {Extension} */\n  return {\n    document: {\n      [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: {\n        name: 'gfmFootnoteDefinition',\n        tokenize: tokenizeDefinitionStart,\n        continuation: {tokenize: tokenizeDefinitionContinuation},\n        exit: gfmFootnoteDefinitionEnd\n      }\n    },\n    text: {\n      [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket]: {\n        name: 'gfmFootnoteCall',\n        tokenize: tokenizeGfmFootnoteCall\n      },\n      [micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket]: {\n        name: 'gfmPotentialFootnoteCall',\n        add: 'after',\n        tokenize: tokenizePotentialGfmFootnoteCall,\n        resolveTo: resolveToPotentialGfmFootnoteCall\n      }\n    }\n  }\n}\n\n// To do: remove after micromark update.\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizePotentialGfmFootnoteCall(effects, ok, nok) {\n  const self = this\n  let index = self.events.length\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  /** @type {Token} */\n  let labelStart\n\n  // Find an opening.\n  while (index--) {\n    const token = self.events[index][1]\n\n    if (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.labelImage) {\n      labelStart = token\n      break\n    }\n\n    // Exit if we’ve walked far enough.\n    if (\n      token.type === 'gfmFootnoteCall' ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.labelLink ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.label ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.image ||\n      token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.link\n    ) {\n      break\n    }\n  }\n\n  return start\n\n  /**\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket, 'expected `]`')\n\n    if (!labelStart || !labelStart._balanced) {\n      return nok(code)\n    }\n\n    const id = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__.normalizeIdentifier)(\n      self.sliceSerialize({start: labelStart.end, end: self.now()})\n    )\n\n    if (id.codePointAt(0) !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.caret || !defined.includes(id.slice(1))) {\n      return nok(code)\n    }\n\n    effects.enter('gfmFootnoteCallLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallLabelMarker')\n    return ok(code)\n  }\n}\n\n// To do: remove after micromark update.\n/** @type {Resolver} */\nfunction resolveToPotentialGfmFootnoteCall(events, context) {\n  let index = events.length\n  /** @type {Token | undefined} */\n  let labelStart\n\n  // Find an opening.\n  while (index--) {\n    if (\n      events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.labelImage &&\n      events[index][0] === 'enter'\n    ) {\n      labelStart = events[index][1]\n      break\n    }\n  }\n\n  (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(labelStart, 'expected `labelStart` to resolve')\n\n  // Change the `labelImageMarker` to a `data`.\n  events[index + 1][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.data\n  events[index + 3][1].type = 'gfmFootnoteCallLabelMarker'\n\n  // The whole (without `!`):\n  /** @type {Token} */\n  const call = {\n    type: 'gfmFootnoteCall',\n    start: Object.assign({}, events[index + 3][1].start),\n    end: Object.assign({}, events[events.length - 1][1].end)\n  }\n  // The `^` marker\n  /** @type {Token} */\n  const marker = {\n    type: 'gfmFootnoteCallMarker',\n    start: Object.assign({}, events[index + 3][1].end),\n    end: Object.assign({}, events[index + 3][1].end)\n  }\n  // Increment the end 1 character.\n  marker.end.column++\n  marker.end.offset++\n  marker.end._bufferIndex++\n  /** @type {Token} */\n  const string = {\n    type: 'gfmFootnoteCallString',\n    start: Object.assign({}, marker.end),\n    end: Object.assign({}, events[events.length - 1][1].start)\n  }\n  /** @type {Token} */\n  const chunk = {\n    type: micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkString,\n    contentType: 'string',\n    start: Object.assign({}, string.start),\n    end: Object.assign({}, string.end)\n  }\n\n  /** @type {Array<Event>} */\n  const replacement = [\n    // Take the `labelImageMarker` (now `data`, the `!`)\n    events[index + 1],\n    events[index + 2],\n    ['enter', call, context],\n    // The `[`\n    events[index + 3],\n    events[index + 4],\n    // The `^`.\n    ['enter', marker, context],\n    ['exit', marker, context],\n    // Everything in between.\n    ['enter', string, context],\n    ['enter', chunk, context],\n    ['exit', chunk, context],\n    ['exit', string, context],\n    // The ending (`]`, properly parsed and labelled).\n    events[events.length - 2],\n    events[events.length - 1],\n    ['exit', call, context]\n  ]\n\n  events.splice(index, events.length - index + 1, ...replacement)\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeGfmFootnoteCall(effects, ok, nok) {\n  const self = this\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  let size = 0\n  /** @type {boolean} */\n  let data\n\n  // Note: the implementation of `markdown-rs` is different, because it houses\n  // core *and* extensions in one project.\n  // Therefore, it can include footnote logic inside `label-end`.\n  // We can’t do that, but luckily, we can parse footnotes in a simpler way than\n  // needed for labels.\n  return start\n\n  /**\n   * Start of footnote label.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket, 'expected `[`')\n    effects.enter('gfmFootnoteCall')\n    effects.enter('gfmFootnoteCallLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallLabelMarker')\n    return callStart\n  }\n\n  /**\n   * After `[`, at `^`.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callStart(code) {\n    if (code !== micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.caret) return nok(code)\n\n    effects.enter('gfmFootnoteCallMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteCallMarker')\n    effects.enter('gfmFootnoteCallString')\n    effects.enter('chunkString').contentType = 'string'\n    return callData\n  }\n\n  /**\n   * In label.\n   *\n   * ```markdown\n   * > | a [^b] c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callData(code) {\n    if (\n      // Too long.\n      size > micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.linkReferenceSizeMax ||\n      // Closing brace with nothing.\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket && !data) ||\n      // Space or tab is not supported by GFM for some reason.\n      // `\\n` and `[` not being supported makes sense.\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)\n    ) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n      effects.exit('chunkString')\n      const token = effects.exit('gfmFootnoteCallString')\n\n      if (!defined.includes((0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__.normalizeIdentifier)(self.sliceSerialize(token)))) {\n        return nok(code)\n      }\n\n      effects.enter('gfmFootnoteCallLabelMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteCallLabelMarker')\n      effects.exit('gfmFootnoteCall')\n      return ok\n    }\n\n    if (!(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)) {\n      data = true\n    }\n\n    size++\n    effects.consume(code)\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ? callEscape : callData\n  }\n\n  /**\n   * On character after escape.\n   *\n   * ```markdown\n   * > | a [^b\\c] d\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function callEscape(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return callData\n    }\n\n    return callData(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDefinitionStart(effects, ok, nok) {\n  const self = this\n  const defined = self.parser.gfmFootnotes || (self.parser.gfmFootnotes = [])\n  /** @type {string} */\n  let identifier\n  let size = 0\n  /** @type {boolean | undefined} */\n  let data\n\n  return start\n\n  /**\n   * Start of GFM footnote definition.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket, 'expected `[`')\n    effects.enter('gfmFootnoteDefinition')._container = true\n    effects.enter('gfmFootnoteDefinitionLabel')\n    effects.enter('gfmFootnoteDefinitionLabelMarker')\n    effects.consume(code)\n    effects.exit('gfmFootnoteDefinitionLabelMarker')\n    return labelAtMarker\n  }\n\n  /**\n   * In label, at caret.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAtMarker(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.caret) {\n      effects.enter('gfmFootnoteDefinitionMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteDefinitionMarker')\n      effects.enter('gfmFootnoteDefinitionLabelString')\n      effects.enter('chunkString').contentType = 'string'\n      return labelInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In label.\n   *\n   * > 👉 **Note**: `cmark-gfm` prevents whitespace from occurring in footnote\n   * > definition labels.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelInside(code) {\n    if (\n      // Too long.\n      size > micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.linkReferenceSizeMax ||\n      // Closing brace with nothing.\n      (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket && !data) ||\n      // Space or tab is not supported by GFM for some reason.\n      // `\\n` and `[` not being supported makes sense.\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)\n    ) {\n      return nok(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket) {\n      effects.exit('chunkString')\n      const token = effects.exit('gfmFootnoteDefinitionLabelString')\n      identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_3__.normalizeIdentifier)(self.sliceSerialize(token))\n      effects.enter('gfmFootnoteDefinitionLabelMarker')\n      effects.consume(code)\n      effects.exit('gfmFootnoteDefinitionLabelMarker')\n      effects.exit('gfmFootnoteDefinitionLabel')\n      return labelAfter\n    }\n\n    if (!(0,micromark_util_character__WEBPACK_IMPORTED_MODULE_5__.markdownLineEndingOrSpace)(code)) {\n      data = true\n    }\n\n    size++\n    effects.consume(code)\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ? labelEscape : labelInside\n  }\n\n  /**\n   * After `\\`, at a special character.\n   *\n   * > 👉 **Note**: `cmark-gfm` currently does not support escaped brackets:\n   * > <https://github.com/github/cmark-gfm/issues/240>\n   *\n   * ```markdown\n   * > | [^a\\*b]: c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEscape(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.leftSquareBracket ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.backslash ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.rightSquareBracket\n    ) {\n      effects.consume(code)\n      size++\n      return labelInside\n    }\n\n    return labelInside(code)\n  }\n\n  /**\n   * After definition label.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.codes.colon) {\n      effects.enter('definitionMarker')\n      effects.consume(code)\n      effects.exit('definitionMarker')\n\n      if (!defined.includes(identifier)) {\n        defined.push(identifier)\n      }\n\n      // Any whitespace after the marker is eaten, forming indented code\n      // is not possible.\n      // No space is also fine, just like a block quote marker.\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(\n        effects,\n        whitespaceAfter,\n        'gfmFootnoteDefinitionWhitespace'\n      )\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After definition prefix.\n   *\n   * ```markdown\n   * > | [^a]: b\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function whitespaceAfter(code) {\n    // `markdown-rs` has a wrapping token for the prefix that is closed here.\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeDefinitionContinuation(effects, ok, nok) {\n  /// Start of footnote definition continuation.\n  ///\n  /// ```markdown\n  ///   | [^a]: b\n  /// > |     c\n  ///     ^\n  /// ```\n  //\n  // Either a blank line, which is okay, or an indented thing.\n  return effects.check(micromark_core_commonmark__WEBPACK_IMPORTED_MODULE_7__.blankLine, ok, effects.attempt(indent, ok, nok))\n}\n\n/** @type {Exiter} */\nfunction gfmFootnoteDefinitionEnd(effects) {\n  effects.exit('gfmFootnoteDefinition')\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeIndent(effects, ok, nok) {\n  const self = this\n\n  return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_6__.factorySpace)(\n    effects,\n    afterPrefix,\n    'gfmFootnoteDefinitionIndent',\n    micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize + 1\n  )\n\n  /**\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === 'gfmFootnoteDefinitionIndent' &&\n      tail[2].sliceSerialize(tail[1], true).length === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.constants.tabSize\n      ? ok(code)\n      : nok(code)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWljcm9tYXJrLWV4dGVuc2lvbi1nZm0tZm9vdG5vdGVAMi4xLjAvbm9kZV9tb2R1bGVzL21pY3JvbWFyay1leHRlbnNpb24tZ2ZtLWZvb3Rub3RlL2Rldi9saWIvc3ludGF4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0EsWUFBWSw4RUFBOEU7QUFDMUY7O0FBRW1DO0FBQ2dCO0FBQ0M7QUFDYztBQUNLO0FBQ1Y7O0FBRTdELGdCQUFnQjs7QUFFaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ087QUFDUCxhQUFhLFdBQVc7QUFDeEI7QUFDQTtBQUNBLE9BQU8sd0RBQUs7QUFDWjtBQUNBO0FBQ0EsdUJBQXVCLHlDQUF5QztBQUNoRTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsT0FBTyx3REFBSztBQUNaO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsT0FBTyx3REFBSztBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFVBQVU7QUFDVixVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsT0FBTztBQUNwQjs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsdUJBQXVCLHdEQUFLO0FBQzVCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsd0RBQUs7QUFDMUIscUJBQXFCLHdEQUFLO0FBQzFCLHFCQUFxQix3REFBSztBQUMxQixxQkFBcUIsd0RBQUs7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLElBQUksMkNBQU0sVUFBVSx3REFBSzs7QUFFekI7QUFDQTtBQUNBOztBQUVBLGVBQWUsd0ZBQW1CO0FBQ2xDLDJCQUEyQix1Q0FBdUM7QUFDbEU7O0FBRUEsOEJBQThCLHdEQUFLO0FBQ25DO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsV0FBVyxVQUFVO0FBQ3JCO0FBQ0E7QUFDQSxhQUFhLG1CQUFtQjtBQUNoQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0Msd0RBQUs7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLEVBQUUsMENBQU07O0FBRVI7QUFDQSw4QkFBOEIsd0RBQUs7QUFDbkM7O0FBRUE7QUFDQSxhQUFhLE9BQU87QUFDcEI7QUFDQTtBQUNBLDJCQUEyQjtBQUMzQix5QkFBeUI7QUFDekI7QUFDQTtBQUNBLGFBQWEsT0FBTztBQUNwQjtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxPQUFPO0FBQ3BCO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0IseUJBQXlCO0FBQ3pCO0FBQ0EsYUFBYSxPQUFPO0FBQ3BCO0FBQ0EsVUFBVSx3REFBSztBQUNmO0FBQ0EsMkJBQTJCO0FBQzNCLHlCQUF5QjtBQUN6Qjs7QUFFQSxhQUFhLGNBQWM7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxVQUFVO0FBQ1YsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLFNBQVM7QUFDdEI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLElBQUksMkNBQU0sVUFBVSx3REFBSztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsaUJBQWlCLHdEQUFLOztBQUV0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSw0REFBUztBQUN0QjtBQUNBLGdCQUFnQix3REFBSztBQUNyQjtBQUNBO0FBQ0EsZUFBZSx3REFBSztBQUNwQixlQUFlLHdEQUFLO0FBQ3BCLE1BQU0sbUZBQXlCO0FBQy9CO0FBQ0E7QUFDQTs7QUFFQSxpQkFBaUIsd0RBQUs7QUFDdEI7QUFDQTs7QUFFQSw0QkFBNEIsd0ZBQW1CO0FBQy9DO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLFNBQVMsbUZBQXlCO0FBQ2xDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLG9CQUFvQix3REFBSztBQUN6Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBLGVBQWUsd0RBQUs7QUFDcEIsZUFBZSx3REFBSztBQUNwQixlQUFlLHdEQUFLO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsVUFBVTtBQUNWLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0EsYUFBYSxxQkFBcUI7QUFDbEM7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsSUFBSSwyQ0FBTSxVQUFVLHdEQUFLO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLGlCQUFpQix3REFBSztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLDREQUFTO0FBQ3RCO0FBQ0EsZ0JBQWdCLHdEQUFLO0FBQ3JCO0FBQ0E7QUFDQSxlQUFlLHdEQUFLO0FBQ3BCLGVBQWUsd0RBQUs7QUFDcEIsTUFBTSxtRkFBeUI7QUFDL0I7QUFDQTtBQUNBOztBQUVBLGlCQUFpQix3REFBSztBQUN0QjtBQUNBO0FBQ0EsbUJBQW1CLHdGQUFtQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsU0FBUyxtRkFBeUI7QUFDbEM7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esb0JBQW9CLHdEQUFLO0FBQ3pCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsZUFBZSx3REFBSztBQUNwQixlQUFlLHdEQUFLO0FBQ3BCLGVBQWUsd0RBQUs7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBLGlCQUFpQix3REFBSztBQUN0QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGFBQWEscUVBQVk7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsVUFBVTtBQUNWLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGdFQUFTO0FBQ2hDOztBQUVBLFdBQVcsUUFBUTtBQUNuQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxVQUFVO0FBQ1YsVUFBVTtBQUNWO0FBQ0E7QUFDQTs7QUFFQSxTQUFTLHFFQUFZO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBLElBQUksNERBQVM7QUFDYjs7QUFFQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdURBQXVELDREQUFTO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFuZHJlXFxPbmVEcml2ZVxcRGVza3RvcFxcR2l0aHViIFJlcG9zaXRvcmllc1xcZGVtb1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG1pY3JvbWFyay1leHRlbnNpb24tZ2ZtLWZvb3Rub3RlQDIuMS4wXFxub2RlX21vZHVsZXNcXG1pY3JvbWFyay1leHRlbnNpb24tZ2ZtLWZvb3Rub3RlXFxkZXZcXGxpYlxcc3ludGF4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7RXZlbnQsIEV4aXRlciwgRXh0ZW5zaW9uLCBSZXNvbHZlciwgU3RhdGUsIFRva2VuLCBUb2tlbml6ZUNvbnRleHQsIFRva2VuaXplcn0gZnJvbSAnbWljcm9tYXJrLXV0aWwtdHlwZXMnXG4gKi9cblxuaW1wb3J0IHtvayBhcyBhc3NlcnR9IGZyb20gJ2RldmxvcCdcbmltcG9ydCB7YmxhbmtMaW5lfSBmcm9tICdtaWNyb21hcmstY29yZS1jb21tb25tYXJrJ1xuaW1wb3J0IHtmYWN0b3J5U3BhY2V9IGZyb20gJ21pY3JvbWFyay1mYWN0b3J5LXNwYWNlJ1xuaW1wb3J0IHttYXJrZG93bkxpbmVFbmRpbmdPclNwYWNlfSBmcm9tICdtaWNyb21hcmstdXRpbC1jaGFyYWN0ZXInXG5pbXBvcnQge25vcm1hbGl6ZUlkZW50aWZpZXJ9IGZyb20gJ21pY3JvbWFyay11dGlsLW5vcm1hbGl6ZS1pZGVudGlmaWVyJ1xuaW1wb3J0IHtjb2RlcywgY29uc3RhbnRzLCB0eXBlc30gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3ltYm9sJ1xuXG5jb25zdCBpbmRlbnQgPSB7dG9rZW5pemU6IHRva2VuaXplSW5kZW50LCBwYXJ0aWFsOiB0cnVlfVxuXG4vLyBUbyBkbzogbWljcm9tYXJrIHNob3VsZCBzdXBwb3J0IGEgYF9oaWRkZW5HZm1Gb290bm90ZVN1cHBvcnRgLCB3aGljaCBvbmx5XG4vLyBhZmZlY3RzIGxhYmVsIHN0YXJ0IChpbWFnZSkuXG4vLyBUaGF0IHdpbGwgbGV0IHVzIGRyb3AgYHRva2VuaXplUG90ZW50aWFsR2ZtRm9vdG5vdGUqYC5cbi8vIEl0IGN1cnJlbnRseSBoYXMgYSBgX2hpZGRlbkZvb3Rub3RlU3VwcG9ydGAsIHdoaWNoIGFmZmVjdHMgdGhhdCBhbmQgbW9yZS5cbi8vIFRoYXQgY2FuIGJlIHJlbW92ZWQgd2hlbiBgbWljcm9tYXJrLWV4dGVuc2lvbi1mb290bm90ZWAgaXMgYXJjaGl2ZWQuXG5cbi8qKlxuICogQ3JlYXRlIGFuIGV4dGVuc2lvbiBmb3IgYG1pY3JvbWFya2AgdG8gZW5hYmxlIEdGTSBmb290bm90ZSBzeW50YXguXG4gKlxuICogQHJldHVybnMge0V4dGVuc2lvbn1cbiAqICAgRXh0ZW5zaW9uIGZvciBgbWljcm9tYXJrYCB0aGF0IGNhbiBiZSBwYXNzZWQgaW4gYGV4dGVuc2lvbnNgIHRvXG4gKiAgIGVuYWJsZSBHRk0gZm9vdG5vdGUgc3ludGF4LlxuICovXG5leHBvcnQgZnVuY3Rpb24gZ2ZtRm9vdG5vdGUoKSB7XG4gIC8qKiBAdHlwZSB7RXh0ZW5zaW9ufSAqL1xuICByZXR1cm4ge1xuICAgIGRvY3VtZW50OiB7XG4gICAgICBbY29kZXMubGVmdFNxdWFyZUJyYWNrZXRdOiB7XG4gICAgICAgIG5hbWU6ICdnZm1Gb290bm90ZURlZmluaXRpb24nLFxuICAgICAgICB0b2tlbml6ZTogdG9rZW5pemVEZWZpbml0aW9uU3RhcnQsXG4gICAgICAgIGNvbnRpbnVhdGlvbjoge3Rva2VuaXplOiB0b2tlbml6ZURlZmluaXRpb25Db250aW51YXRpb259LFxuICAgICAgICBleGl0OiBnZm1Gb290bm90ZURlZmluaXRpb25FbmRcbiAgICAgIH1cbiAgICB9LFxuICAgIHRleHQ6IHtcbiAgICAgIFtjb2Rlcy5sZWZ0U3F1YXJlQnJhY2tldF06IHtcbiAgICAgICAgbmFtZTogJ2dmbUZvb3Rub3RlQ2FsbCcsXG4gICAgICAgIHRva2VuaXplOiB0b2tlbml6ZUdmbUZvb3Rub3RlQ2FsbFxuICAgICAgfSxcbiAgICAgIFtjb2Rlcy5yaWdodFNxdWFyZUJyYWNrZXRdOiB7XG4gICAgICAgIG5hbWU6ICdnZm1Qb3RlbnRpYWxGb290bm90ZUNhbGwnLFxuICAgICAgICBhZGQ6ICdhZnRlcicsXG4gICAgICAgIHRva2VuaXplOiB0b2tlbml6ZVBvdGVudGlhbEdmbUZvb3Rub3RlQ2FsbCxcbiAgICAgICAgcmVzb2x2ZVRvOiByZXNvbHZlVG9Qb3RlbnRpYWxHZm1Gb290bm90ZUNhbGxcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLy8gVG8gZG86IHJlbW92ZSBhZnRlciBtaWNyb21hcmsgdXBkYXRlLlxuLyoqXG4gKiBAdGhpcyB7VG9rZW5pemVDb250ZXh0fVxuICogQHR5cGUge1Rva2VuaXplcn1cbiAqL1xuZnVuY3Rpb24gdG9rZW5pemVQb3RlbnRpYWxHZm1Gb290bm90ZUNhbGwoZWZmZWN0cywgb2ssIG5vaykge1xuICBjb25zdCBzZWxmID0gdGhpc1xuICBsZXQgaW5kZXggPSBzZWxmLmV2ZW50cy5sZW5ndGhcbiAgY29uc3QgZGVmaW5lZCA9IHNlbGYucGFyc2VyLmdmbUZvb3Rub3RlcyB8fCAoc2VsZi5wYXJzZXIuZ2ZtRm9vdG5vdGVzID0gW10pXG4gIC8qKiBAdHlwZSB7VG9rZW59ICovXG4gIGxldCBsYWJlbFN0YXJ0XG5cbiAgLy8gRmluZCBhbiBvcGVuaW5nLlxuICB3aGlsZSAoaW5kZXgtLSkge1xuICAgIGNvbnN0IHRva2VuID0gc2VsZi5ldmVudHNbaW5kZXhdWzFdXG5cbiAgICBpZiAodG9rZW4udHlwZSA9PT0gdHlwZXMubGFiZWxJbWFnZSkge1xuICAgICAgbGFiZWxTdGFydCA9IHRva2VuXG4gICAgICBicmVha1xuICAgIH1cblxuICAgIC8vIEV4aXQgaWYgd2XigJl2ZSB3YWxrZWQgZmFyIGVub3VnaC5cbiAgICBpZiAoXG4gICAgICB0b2tlbi50eXBlID09PSAnZ2ZtRm9vdG5vdGVDYWxsJyB8fFxuICAgICAgdG9rZW4udHlwZSA9PT0gdHlwZXMubGFiZWxMaW5rIHx8XG4gICAgICB0b2tlbi50eXBlID09PSB0eXBlcy5sYWJlbCB8fFxuICAgICAgdG9rZW4udHlwZSA9PT0gdHlwZXMuaW1hZ2UgfHxcbiAgICAgIHRva2VuLnR5cGUgPT09IHR5cGVzLmxpbmtcbiAgICApIHtcbiAgICAgIGJyZWFrXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHN0YXJ0XG5cbiAgLyoqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIHN0YXJ0KGNvZGUpIHtcbiAgICBhc3NlcnQoY29kZSA9PT0gY29kZXMucmlnaHRTcXVhcmVCcmFja2V0LCAnZXhwZWN0ZWQgYF1gJylcblxuICAgIGlmICghbGFiZWxTdGFydCB8fCAhbGFiZWxTdGFydC5fYmFsYW5jZWQpIHtcbiAgICAgIHJldHVybiBub2soY29kZSlcbiAgICB9XG5cbiAgICBjb25zdCBpZCA9IG5vcm1hbGl6ZUlkZW50aWZpZXIoXG4gICAgICBzZWxmLnNsaWNlU2VyaWFsaXplKHtzdGFydDogbGFiZWxTdGFydC5lbmQsIGVuZDogc2VsZi5ub3coKX0pXG4gICAgKVxuXG4gICAgaWYgKGlkLmNvZGVQb2ludEF0KDApICE9PSBjb2Rlcy5jYXJldCB8fCAhZGVmaW5lZC5pbmNsdWRlcyhpZC5zbGljZSgxKSkpIHtcbiAgICAgIHJldHVybiBub2soY29kZSlcbiAgICB9XG5cbiAgICBlZmZlY3RzLmVudGVyKCdnZm1Gb290bm90ZUNhbGxMYWJlbE1hcmtlcicpXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgZWZmZWN0cy5leGl0KCdnZm1Gb290bm90ZUNhbGxMYWJlbE1hcmtlcicpXG4gICAgcmV0dXJuIG9rKGNvZGUpXG4gIH1cbn1cblxuLy8gVG8gZG86IHJlbW92ZSBhZnRlciBtaWNyb21hcmsgdXBkYXRlLlxuLyoqIEB0eXBlIHtSZXNvbHZlcn0gKi9cbmZ1bmN0aW9uIHJlc29sdmVUb1BvdGVudGlhbEdmbUZvb3Rub3RlQ2FsbChldmVudHMsIGNvbnRleHQpIHtcbiAgbGV0IGluZGV4ID0gZXZlbnRzLmxlbmd0aFxuICAvKiogQHR5cGUge1Rva2VuIHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgbGFiZWxTdGFydFxuXG4gIC8vIEZpbmQgYW4gb3BlbmluZy5cbiAgd2hpbGUgKGluZGV4LS0pIHtcbiAgICBpZiAoXG4gICAgICBldmVudHNbaW5kZXhdWzFdLnR5cGUgPT09IHR5cGVzLmxhYmVsSW1hZ2UgJiZcbiAgICAgIGV2ZW50c1tpbmRleF1bMF0gPT09ICdlbnRlcidcbiAgICApIHtcbiAgICAgIGxhYmVsU3RhcnQgPSBldmVudHNbaW5kZXhdWzFdXG4gICAgICBicmVha1xuICAgIH1cbiAgfVxuXG4gIGFzc2VydChsYWJlbFN0YXJ0LCAnZXhwZWN0ZWQgYGxhYmVsU3RhcnRgIHRvIHJlc29sdmUnKVxuXG4gIC8vIENoYW5nZSB0aGUgYGxhYmVsSW1hZ2VNYXJrZXJgIHRvIGEgYGRhdGFgLlxuICBldmVudHNbaW5kZXggKyAxXVsxXS50eXBlID0gdHlwZXMuZGF0YVxuICBldmVudHNbaW5kZXggKyAzXVsxXS50eXBlID0gJ2dmbUZvb3Rub3RlQ2FsbExhYmVsTWFya2VyJ1xuXG4gIC8vIFRoZSB3aG9sZSAod2l0aG91dCBgIWApOlxuICAvKiogQHR5cGUge1Rva2VufSAqL1xuICBjb25zdCBjYWxsID0ge1xuICAgIHR5cGU6ICdnZm1Gb290bm90ZUNhbGwnLFxuICAgIHN0YXJ0OiBPYmplY3QuYXNzaWduKHt9LCBldmVudHNbaW5kZXggKyAzXVsxXS5zdGFydCksXG4gICAgZW5kOiBPYmplY3QuYXNzaWduKHt9LCBldmVudHNbZXZlbnRzLmxlbmd0aCAtIDFdWzFdLmVuZClcbiAgfVxuICAvLyBUaGUgYF5gIG1hcmtlclxuICAvKiogQHR5cGUge1Rva2VufSAqL1xuICBjb25zdCBtYXJrZXIgPSB7XG4gICAgdHlwZTogJ2dmbUZvb3Rub3RlQ2FsbE1hcmtlcicsXG4gICAgc3RhcnQ6IE9iamVjdC5hc3NpZ24oe30sIGV2ZW50c1tpbmRleCArIDNdWzFdLmVuZCksXG4gICAgZW5kOiBPYmplY3QuYXNzaWduKHt9LCBldmVudHNbaW5kZXggKyAzXVsxXS5lbmQpXG4gIH1cbiAgLy8gSW5jcmVtZW50IHRoZSBlbmQgMSBjaGFyYWN0ZXIuXG4gIG1hcmtlci5lbmQuY29sdW1uKytcbiAgbWFya2VyLmVuZC5vZmZzZXQrK1xuICBtYXJrZXIuZW5kLl9idWZmZXJJbmRleCsrXG4gIC8qKiBAdHlwZSB7VG9rZW59ICovXG4gIGNvbnN0IHN0cmluZyA9IHtcbiAgICB0eXBlOiAnZ2ZtRm9vdG5vdGVDYWxsU3RyaW5nJyxcbiAgICBzdGFydDogT2JqZWN0LmFzc2lnbih7fSwgbWFya2VyLmVuZCksXG4gICAgZW5kOiBPYmplY3QuYXNzaWduKHt9LCBldmVudHNbZXZlbnRzLmxlbmd0aCAtIDFdWzFdLnN0YXJ0KVxuICB9XG4gIC8qKiBAdHlwZSB7VG9rZW59ICovXG4gIGNvbnN0IGNodW5rID0ge1xuICAgIHR5cGU6IHR5cGVzLmNodW5rU3RyaW5nLFxuICAgIGNvbnRlbnRUeXBlOiAnc3RyaW5nJyxcbiAgICBzdGFydDogT2JqZWN0LmFzc2lnbih7fSwgc3RyaW5nLnN0YXJ0KSxcbiAgICBlbmQ6IE9iamVjdC5hc3NpZ24oe30sIHN0cmluZy5lbmQpXG4gIH1cblxuICAvKiogQHR5cGUge0FycmF5PEV2ZW50Pn0gKi9cbiAgY29uc3QgcmVwbGFjZW1lbnQgPSBbXG4gICAgLy8gVGFrZSB0aGUgYGxhYmVsSW1hZ2VNYXJrZXJgIChub3cgYGRhdGFgLCB0aGUgYCFgKVxuICAgIGV2ZW50c1tpbmRleCArIDFdLFxuICAgIGV2ZW50c1tpbmRleCArIDJdLFxuICAgIFsnZW50ZXInLCBjYWxsLCBjb250ZXh0XSxcbiAgICAvLyBUaGUgYFtgXG4gICAgZXZlbnRzW2luZGV4ICsgM10sXG4gICAgZXZlbnRzW2luZGV4ICsgNF0sXG4gICAgLy8gVGhlIGBeYC5cbiAgICBbJ2VudGVyJywgbWFya2VyLCBjb250ZXh0XSxcbiAgICBbJ2V4aXQnLCBtYXJrZXIsIGNvbnRleHRdLFxuICAgIC8vIEV2ZXJ5dGhpbmcgaW4gYmV0d2Vlbi5cbiAgICBbJ2VudGVyJywgc3RyaW5nLCBjb250ZXh0XSxcbiAgICBbJ2VudGVyJywgY2h1bmssIGNvbnRleHRdLFxuICAgIFsnZXhpdCcsIGNodW5rLCBjb250ZXh0XSxcbiAgICBbJ2V4aXQnLCBzdHJpbmcsIGNvbnRleHRdLFxuICAgIC8vIFRoZSBlbmRpbmcgKGBdYCwgcHJvcGVybHkgcGFyc2VkIGFuZCBsYWJlbGxlZCkuXG4gICAgZXZlbnRzW2V2ZW50cy5sZW5ndGggLSAyXSxcbiAgICBldmVudHNbZXZlbnRzLmxlbmd0aCAtIDFdLFxuICAgIFsnZXhpdCcsIGNhbGwsIGNvbnRleHRdXG4gIF1cblxuICBldmVudHMuc3BsaWNlKGluZGV4LCBldmVudHMubGVuZ3RoIC0gaW5kZXggKyAxLCAuLi5yZXBsYWNlbWVudClcblxuICByZXR1cm4gZXZlbnRzXG59XG5cbi8qKlxuICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAqIEB0eXBlIHtUb2tlbml6ZXJ9XG4gKi9cbmZ1bmN0aW9uIHRva2VuaXplR2ZtRm9vdG5vdGVDYWxsKGVmZmVjdHMsIG9rLCBub2spIHtcbiAgY29uc3Qgc2VsZiA9IHRoaXNcbiAgY29uc3QgZGVmaW5lZCA9IHNlbGYucGFyc2VyLmdmbUZvb3Rub3RlcyB8fCAoc2VsZi5wYXJzZXIuZ2ZtRm9vdG5vdGVzID0gW10pXG4gIGxldCBzaXplID0gMFxuICAvKiogQHR5cGUge2Jvb2xlYW59ICovXG4gIGxldCBkYXRhXG5cbiAgLy8gTm90ZTogdGhlIGltcGxlbWVudGF0aW9uIG9mIGBtYXJrZG93bi1yc2AgaXMgZGlmZmVyZW50LCBiZWNhdXNlIGl0IGhvdXNlc1xuICAvLyBjb3JlICphbmQqIGV4dGVuc2lvbnMgaW4gb25lIHByb2plY3QuXG4gIC8vIFRoZXJlZm9yZSwgaXQgY2FuIGluY2x1ZGUgZm9vdG5vdGUgbG9naWMgaW5zaWRlIGBsYWJlbC1lbmRgLlxuICAvLyBXZSBjYW7igJl0IGRvIHRoYXQsIGJ1dCBsdWNraWx5LCB3ZSBjYW4gcGFyc2UgZm9vdG5vdGVzIGluIGEgc2ltcGxlciB3YXkgdGhhblxuICAvLyBuZWVkZWQgZm9yIGxhYmVscy5cbiAgcmV0dXJuIHN0YXJ0XG5cbiAgLyoqXG4gICAqIFN0YXJ0IG9mIGZvb3Rub3RlIGxhYmVsLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgYSBbXmJdIGNcbiAgICogICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gc3RhcnQoY29kZSkge1xuICAgIGFzc2VydChjb2RlID09PSBjb2Rlcy5sZWZ0U3F1YXJlQnJhY2tldCwgJ2V4cGVjdGVkIGBbYCcpXG4gICAgZWZmZWN0cy5lbnRlcignZ2ZtRm9vdG5vdGVDYWxsJylcbiAgICBlZmZlY3RzLmVudGVyKCdnZm1Gb290bm90ZUNhbGxMYWJlbE1hcmtlcicpXG4gICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgZWZmZWN0cy5leGl0KCdnZm1Gb290bm90ZUNhbGxMYWJlbE1hcmtlcicpXG4gICAgcmV0dXJuIGNhbGxTdGFydFxuICB9XG5cbiAgLyoqXG4gICAqIEFmdGVyIGBbYCwgYXQgYF5gLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgYSBbXmJdIGNcbiAgICogICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIGNhbGxTdGFydChjb2RlKSB7XG4gICAgaWYgKGNvZGUgIT09IGNvZGVzLmNhcmV0KSByZXR1cm4gbm9rKGNvZGUpXG5cbiAgICBlZmZlY3RzLmVudGVyKCdnZm1Gb290bm90ZUNhbGxNYXJrZXInKVxuICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgIGVmZmVjdHMuZXhpdCgnZ2ZtRm9vdG5vdGVDYWxsTWFya2VyJylcbiAgICBlZmZlY3RzLmVudGVyKCdnZm1Gb290bm90ZUNhbGxTdHJpbmcnKVxuICAgIGVmZmVjdHMuZW50ZXIoJ2NodW5rU3RyaW5nJykuY29udGVudFR5cGUgPSAnc3RyaW5nJ1xuICAgIHJldHVybiBjYWxsRGF0YVxuICB9XG5cbiAgLyoqXG4gICAqIEluIGxhYmVsLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgYSBbXmJdIGNcbiAgICogICAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBjYWxsRGF0YShjb2RlKSB7XG4gICAgaWYgKFxuICAgICAgLy8gVG9vIGxvbmcuXG4gICAgICBzaXplID4gY29uc3RhbnRzLmxpbmtSZWZlcmVuY2VTaXplTWF4IHx8XG4gICAgICAvLyBDbG9zaW5nIGJyYWNlIHdpdGggbm90aGluZy5cbiAgICAgIChjb2RlID09PSBjb2Rlcy5yaWdodFNxdWFyZUJyYWNrZXQgJiYgIWRhdGEpIHx8XG4gICAgICAvLyBTcGFjZSBvciB0YWIgaXMgbm90IHN1cHBvcnRlZCBieSBHRk0gZm9yIHNvbWUgcmVhc29uLlxuICAgICAgLy8gYFxcbmAgYW5kIGBbYCBub3QgYmVpbmcgc3VwcG9ydGVkIG1ha2VzIHNlbnNlLlxuICAgICAgY29kZSA9PT0gY29kZXMuZW9mIHx8XG4gICAgICBjb2RlID09PSBjb2Rlcy5sZWZ0U3F1YXJlQnJhY2tldCB8fFxuICAgICAgbWFya2Rvd25MaW5lRW5kaW5nT3JTcGFjZShjb2RlKVxuICAgICkge1xuICAgICAgcmV0dXJuIG5vayhjb2RlKVxuICAgIH1cblxuICAgIGlmIChjb2RlID09PSBjb2Rlcy5yaWdodFNxdWFyZUJyYWNrZXQpIHtcbiAgICAgIGVmZmVjdHMuZXhpdCgnY2h1bmtTdHJpbmcnKVxuICAgICAgY29uc3QgdG9rZW4gPSBlZmZlY3RzLmV4aXQoJ2dmbUZvb3Rub3RlQ2FsbFN0cmluZycpXG5cbiAgICAgIGlmICghZGVmaW5lZC5pbmNsdWRlcyhub3JtYWxpemVJZGVudGlmaWVyKHNlbGYuc2xpY2VTZXJpYWxpemUodG9rZW4pKSkpIHtcbiAgICAgICAgcmV0dXJuIG5vayhjb2RlKVxuICAgICAgfVxuXG4gICAgICBlZmZlY3RzLmVudGVyKCdnZm1Gb290bm90ZUNhbGxMYWJlbE1hcmtlcicpXG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIGVmZmVjdHMuZXhpdCgnZ2ZtRm9vdG5vdGVDYWxsTGFiZWxNYXJrZXInKVxuICAgICAgZWZmZWN0cy5leGl0KCdnZm1Gb290bm90ZUNhbGwnKVxuICAgICAgcmV0dXJuIG9rXG4gICAgfVxuXG4gICAgaWYgKCFtYXJrZG93bkxpbmVFbmRpbmdPclNwYWNlKGNvZGUpKSB7XG4gICAgICBkYXRhID0gdHJ1ZVxuICAgIH1cblxuICAgIHNpemUrK1xuICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgIHJldHVybiBjb2RlID09PSBjb2Rlcy5iYWNrc2xhc2ggPyBjYWxsRXNjYXBlIDogY2FsbERhdGFcbiAgfVxuXG4gIC8qKlxuICAgKiBPbiBjaGFyYWN0ZXIgYWZ0ZXIgZXNjYXBlLlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgYSBbXmJcXGNdIGRcbiAgICogICAgICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIGNhbGxFc2NhcGUoY29kZSkge1xuICAgIGlmIChcbiAgICAgIGNvZGUgPT09IGNvZGVzLmxlZnRTcXVhcmVCcmFja2V0IHx8XG4gICAgICBjb2RlID09PSBjb2Rlcy5iYWNrc2xhc2ggfHxcbiAgICAgIGNvZGUgPT09IGNvZGVzLnJpZ2h0U3F1YXJlQnJhY2tldFxuICAgICkge1xuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICBzaXplKytcbiAgICAgIHJldHVybiBjYWxsRGF0YVxuICAgIH1cblxuICAgIHJldHVybiBjYWxsRGF0YShjb2RlKVxuICB9XG59XG5cbi8qKlxuICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAqIEB0eXBlIHtUb2tlbml6ZXJ9XG4gKi9cbmZ1bmN0aW9uIHRva2VuaXplRGVmaW5pdGlvblN0YXJ0KGVmZmVjdHMsIG9rLCBub2spIHtcbiAgY29uc3Qgc2VsZiA9IHRoaXNcbiAgY29uc3QgZGVmaW5lZCA9IHNlbGYucGFyc2VyLmdmbUZvb3Rub3RlcyB8fCAoc2VsZi5wYXJzZXIuZ2ZtRm9vdG5vdGVzID0gW10pXG4gIC8qKiBAdHlwZSB7c3RyaW5nfSAqL1xuICBsZXQgaWRlbnRpZmllclxuICBsZXQgc2l6ZSA9IDBcbiAgLyoqIEB0eXBlIHtib29sZWFuIHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgZGF0YVxuXG4gIHJldHVybiBzdGFydFxuXG4gIC8qKlxuICAgKiBTdGFydCBvZiBHRk0gZm9vdG5vdGUgZGVmaW5pdGlvbi5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IFteYV06IGJcbiAgICogICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIHN0YXJ0KGNvZGUpIHtcbiAgICBhc3NlcnQoY29kZSA9PT0gY29kZXMubGVmdFNxdWFyZUJyYWNrZXQsICdleHBlY3RlZCBgW2AnKVxuICAgIGVmZmVjdHMuZW50ZXIoJ2dmbUZvb3Rub3RlRGVmaW5pdGlvbicpLl9jb250YWluZXIgPSB0cnVlXG4gICAgZWZmZWN0cy5lbnRlcignZ2ZtRm9vdG5vdGVEZWZpbml0aW9uTGFiZWwnKVxuICAgIGVmZmVjdHMuZW50ZXIoJ2dmbUZvb3Rub3RlRGVmaW5pdGlvbkxhYmVsTWFya2VyJylcbiAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICBlZmZlY3RzLmV4aXQoJ2dmbUZvb3Rub3RlRGVmaW5pdGlvbkxhYmVsTWFya2VyJylcbiAgICByZXR1cm4gbGFiZWxBdE1hcmtlclxuICB9XG5cbiAgLyoqXG4gICAqIEluIGxhYmVsLCBhdCBjYXJldC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IFteYV06IGJcbiAgICogICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBsYWJlbEF0TWFya2VyKGNvZGUpIHtcbiAgICBpZiAoY29kZSA9PT0gY29kZXMuY2FyZXQpIHtcbiAgICAgIGVmZmVjdHMuZW50ZXIoJ2dmbUZvb3Rub3RlRGVmaW5pdGlvbk1hcmtlcicpXG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIGVmZmVjdHMuZXhpdCgnZ2ZtRm9vdG5vdGVEZWZpbml0aW9uTWFya2VyJylcbiAgICAgIGVmZmVjdHMuZW50ZXIoJ2dmbUZvb3Rub3RlRGVmaW5pdGlvbkxhYmVsU3RyaW5nJylcbiAgICAgIGVmZmVjdHMuZW50ZXIoJ2NodW5rU3RyaW5nJykuY29udGVudFR5cGUgPSAnc3RyaW5nJ1xuICAgICAgcmV0dXJuIGxhYmVsSW5zaWRlXG4gICAgfVxuXG4gICAgcmV0dXJuIG5vayhjb2RlKVxuICB9XG5cbiAgLyoqXG4gICAqIEluIGxhYmVsLlxuICAgKlxuICAgKiA+IPCfkYkgKipOb3RlKio6IGBjbWFyay1nZm1gIHByZXZlbnRzIHdoaXRlc3BhY2UgZnJvbSBvY2N1cnJpbmcgaW4gZm9vdG5vdGVcbiAgICogPiBkZWZpbml0aW9uIGxhYmVscy5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IFteYV06IGJcbiAgICogICAgICAgXlxuICAgKiBgYGBcbiAgICpcbiAgICogQHR5cGUge1N0YXRlfVxuICAgKi9cbiAgZnVuY3Rpb24gbGFiZWxJbnNpZGUoY29kZSkge1xuICAgIGlmIChcbiAgICAgIC8vIFRvbyBsb25nLlxuICAgICAgc2l6ZSA+IGNvbnN0YW50cy5saW5rUmVmZXJlbmNlU2l6ZU1heCB8fFxuICAgICAgLy8gQ2xvc2luZyBicmFjZSB3aXRoIG5vdGhpbmcuXG4gICAgICAoY29kZSA9PT0gY29kZXMucmlnaHRTcXVhcmVCcmFja2V0ICYmICFkYXRhKSB8fFxuICAgICAgLy8gU3BhY2Ugb3IgdGFiIGlzIG5vdCBzdXBwb3J0ZWQgYnkgR0ZNIGZvciBzb21lIHJlYXNvbi5cbiAgICAgIC8vIGBcXG5gIGFuZCBgW2Agbm90IGJlaW5nIHN1cHBvcnRlZCBtYWtlcyBzZW5zZS5cbiAgICAgIGNvZGUgPT09IGNvZGVzLmVvZiB8fFxuICAgICAgY29kZSA9PT0gY29kZXMubGVmdFNxdWFyZUJyYWNrZXQgfHxcbiAgICAgIG1hcmtkb3duTGluZUVuZGluZ09yU3BhY2UoY29kZSlcbiAgICApIHtcbiAgICAgIHJldHVybiBub2soY29kZSlcbiAgICB9XG5cbiAgICBpZiAoY29kZSA9PT0gY29kZXMucmlnaHRTcXVhcmVCcmFja2V0KSB7XG4gICAgICBlZmZlY3RzLmV4aXQoJ2NodW5rU3RyaW5nJylcbiAgICAgIGNvbnN0IHRva2VuID0gZWZmZWN0cy5leGl0KCdnZm1Gb290bm90ZURlZmluaXRpb25MYWJlbFN0cmluZycpXG4gICAgICBpZGVudGlmaWVyID0gbm9ybWFsaXplSWRlbnRpZmllcihzZWxmLnNsaWNlU2VyaWFsaXplKHRva2VuKSlcbiAgICAgIGVmZmVjdHMuZW50ZXIoJ2dmbUZvb3Rub3RlRGVmaW5pdGlvbkxhYmVsTWFya2VyJylcbiAgICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgICAgZWZmZWN0cy5leGl0KCdnZm1Gb290bm90ZURlZmluaXRpb25MYWJlbE1hcmtlcicpXG4gICAgICBlZmZlY3RzLmV4aXQoJ2dmbUZvb3Rub3RlRGVmaW5pdGlvbkxhYmVsJylcbiAgICAgIHJldHVybiBsYWJlbEFmdGVyXG4gICAgfVxuXG4gICAgaWYgKCFtYXJrZG93bkxpbmVFbmRpbmdPclNwYWNlKGNvZGUpKSB7XG4gICAgICBkYXRhID0gdHJ1ZVxuICAgIH1cblxuICAgIHNpemUrK1xuICAgIGVmZmVjdHMuY29uc3VtZShjb2RlKVxuICAgIHJldHVybiBjb2RlID09PSBjb2Rlcy5iYWNrc2xhc2ggPyBsYWJlbEVzY2FwZSA6IGxhYmVsSW5zaWRlXG4gIH1cblxuICAvKipcbiAgICogQWZ0ZXIgYFxcYCwgYXQgYSBzcGVjaWFsIGNoYXJhY3Rlci5cbiAgICpcbiAgICogPiDwn5GJICoqTm90ZSoqOiBgY21hcmstZ2ZtYCBjdXJyZW50bHkgZG9lcyBub3Qgc3VwcG9ydCBlc2NhcGVkIGJyYWNrZXRzOlxuICAgKiA+IDxodHRwczovL2dpdGh1Yi5jb20vZ2l0aHViL2NtYXJrLWdmbS9pc3N1ZXMvMjQwPlxuICAgKlxuICAgKiBgYGBtYXJrZG93blxuICAgKiA+IHwgW15hXFwqYl06IGNcbiAgICogICAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBsYWJlbEVzY2FwZShjb2RlKSB7XG4gICAgaWYgKFxuICAgICAgY29kZSA9PT0gY29kZXMubGVmdFNxdWFyZUJyYWNrZXQgfHxcbiAgICAgIGNvZGUgPT09IGNvZGVzLmJhY2tzbGFzaCB8fFxuICAgICAgY29kZSA9PT0gY29kZXMucmlnaHRTcXVhcmVCcmFja2V0XG4gICAgKSB7XG4gICAgICBlZmZlY3RzLmNvbnN1bWUoY29kZSlcbiAgICAgIHNpemUrK1xuICAgICAgcmV0dXJuIGxhYmVsSW5zaWRlXG4gICAgfVxuXG4gICAgcmV0dXJuIGxhYmVsSW5zaWRlKGNvZGUpXG4gIH1cblxuICAvKipcbiAgICogQWZ0ZXIgZGVmaW5pdGlvbiBsYWJlbC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IFteYV06IGJcbiAgICogICAgICAgICBeXG4gICAqIGBgYFxuICAgKlxuICAgKiBAdHlwZSB7U3RhdGV9XG4gICAqL1xuICBmdW5jdGlvbiBsYWJlbEFmdGVyKGNvZGUpIHtcbiAgICBpZiAoY29kZSA9PT0gY29kZXMuY29sb24pIHtcbiAgICAgIGVmZmVjdHMuZW50ZXIoJ2RlZmluaXRpb25NYXJrZXInKVxuICAgICAgZWZmZWN0cy5jb25zdW1lKGNvZGUpXG4gICAgICBlZmZlY3RzLmV4aXQoJ2RlZmluaXRpb25NYXJrZXInKVxuXG4gICAgICBpZiAoIWRlZmluZWQuaW5jbHVkZXMoaWRlbnRpZmllcikpIHtcbiAgICAgICAgZGVmaW5lZC5wdXNoKGlkZW50aWZpZXIpXG4gICAgICB9XG5cbiAgICAgIC8vIEFueSB3aGl0ZXNwYWNlIGFmdGVyIHRoZSBtYXJrZXIgaXMgZWF0ZW4sIGZvcm1pbmcgaW5kZW50ZWQgY29kZVxuICAgICAgLy8gaXMgbm90IHBvc3NpYmxlLlxuICAgICAgLy8gTm8gc3BhY2UgaXMgYWxzbyBmaW5lLCBqdXN0IGxpa2UgYSBibG9jayBxdW90ZSBtYXJrZXIuXG4gICAgICByZXR1cm4gZmFjdG9yeVNwYWNlKFxuICAgICAgICBlZmZlY3RzLFxuICAgICAgICB3aGl0ZXNwYWNlQWZ0ZXIsXG4gICAgICAgICdnZm1Gb290bm90ZURlZmluaXRpb25XaGl0ZXNwYWNlJ1xuICAgICAgKVxuICAgIH1cblxuICAgIHJldHVybiBub2soY29kZSlcbiAgfVxuXG4gIC8qKlxuICAgKiBBZnRlciBkZWZpbml0aW9uIHByZWZpeC5cbiAgICpcbiAgICogYGBgbWFya2Rvd25cbiAgICogPiB8IFteYV06IGJcbiAgICogICAgICAgICAgIF5cbiAgICogYGBgXG4gICAqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIHdoaXRlc3BhY2VBZnRlcihjb2RlKSB7XG4gICAgLy8gYG1hcmtkb3duLXJzYCBoYXMgYSB3cmFwcGluZyB0b2tlbiBmb3IgdGhlIHByZWZpeCB0aGF0IGlzIGNsb3NlZCBoZXJlLlxuICAgIHJldHVybiBvayhjb2RlKVxuICB9XG59XG5cbi8qKlxuICogQHRoaXMge1Rva2VuaXplQ29udGV4dH1cbiAqIEB0eXBlIHtUb2tlbml6ZXJ9XG4gKi9cbmZ1bmN0aW9uIHRva2VuaXplRGVmaW5pdGlvbkNvbnRpbnVhdGlvbihlZmZlY3RzLCBvaywgbm9rKSB7XG4gIC8vLyBTdGFydCBvZiBmb290bm90ZSBkZWZpbml0aW9uIGNvbnRpbnVhdGlvbi5cbiAgLy8vXG4gIC8vLyBgYGBtYXJrZG93blxuICAvLy8gICB8IFteYV06IGJcbiAgLy8vID4gfCAgICAgY1xuICAvLy8gICAgIF5cbiAgLy8vIGBgYFxuICAvL1xuICAvLyBFaXRoZXIgYSBibGFuayBsaW5lLCB3aGljaCBpcyBva2F5LCBvciBhbiBpbmRlbnRlZCB0aGluZy5cbiAgcmV0dXJuIGVmZmVjdHMuY2hlY2soYmxhbmtMaW5lLCBvaywgZWZmZWN0cy5hdHRlbXB0KGluZGVudCwgb2ssIG5vaykpXG59XG5cbi8qKiBAdHlwZSB7RXhpdGVyfSAqL1xuZnVuY3Rpb24gZ2ZtRm9vdG5vdGVEZWZpbml0aW9uRW5kKGVmZmVjdHMpIHtcbiAgZWZmZWN0cy5leGl0KCdnZm1Gb290bm90ZURlZmluaXRpb24nKVxufVxuXG4vKipcbiAqIEB0aGlzIHtUb2tlbml6ZUNvbnRleHR9XG4gKiBAdHlwZSB7VG9rZW5pemVyfVxuICovXG5mdW5jdGlvbiB0b2tlbml6ZUluZGVudChlZmZlY3RzLCBvaywgbm9rKSB7XG4gIGNvbnN0IHNlbGYgPSB0aGlzXG5cbiAgcmV0dXJuIGZhY3RvcnlTcGFjZShcbiAgICBlZmZlY3RzLFxuICAgIGFmdGVyUHJlZml4LFxuICAgICdnZm1Gb290bm90ZURlZmluaXRpb25JbmRlbnQnLFxuICAgIGNvbnN0YW50cy50YWJTaXplICsgMVxuICApXG5cbiAgLyoqXG4gICAqIEB0eXBlIHtTdGF0ZX1cbiAgICovXG4gIGZ1bmN0aW9uIGFmdGVyUHJlZml4KGNvZGUpIHtcbiAgICBjb25zdCB0YWlsID0gc2VsZi5ldmVudHNbc2VsZi5ldmVudHMubGVuZ3RoIC0gMV1cbiAgICByZXR1cm4gdGFpbCAmJlxuICAgICAgdGFpbFsxXS50eXBlID09PSAnZ2ZtRm9vdG5vdGVEZWZpbml0aW9uSW5kZW50JyAmJlxuICAgICAgdGFpbFsyXS5zbGljZVNlcmlhbGl6ZSh0YWlsWzFdLCB0cnVlKS5sZW5ndGggPT09IGNvbnN0YW50cy50YWJTaXplXG4gICAgICA/IG9rKGNvZGUpXG4gICAgICA6IG5vayhjb2RlKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-extension-gfm-footnote@2.1.0/node_modules/micromark-extension-gfm-footnote/dev/lib/syntax.js\n");

/***/ })

};
;