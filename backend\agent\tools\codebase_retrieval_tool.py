import re
import json
import async<PERSON>
from typing import List, Dict, Any, Optional

from agentpress.tool import Too<PERSON><PERSON><PERSON><PERSON>, openapi_schema, xml_schema
from sandbox.sandbox import SandboxToolsBase
from agentpress.thread_manager import ThreadManager
from utils.logger import logger

class CodebaseRetrievalTool(SandboxToolsBase):
    """Tool for searching and retrieving code from the codebase in the sandbox."""

    def __init__(self, project_id: str, thread_manager: ThreadManager):
        """Initialize the codebase retrieval tool.

        Args:
            project_id: ID of the project
            thread_manager: Thread manager instance
        """
        super().__init__(project_id, thread_manager)
        self.workspace_path = "/workspace"
        self.ignore_patterns = [
            r"\.git/",
            r"\.vscode/",
            r"__pycache__/",
            r"\.pyc$",
            r"\.pyo$",
            r"\.pyd$",
            r"\.so$",
            r"\.dll$",
            r"\.exe$",
            r"\.bin$",
            r"node_modules/",
            r"\.DS_Store$",
            r"\.env$",
            r"\.log$",
            r"\.tmp$",
            r"\.temp$",
            r"\.cache/",
            r"\.idea/",
            r"\.venv/",
            r"env/",
            r"venv/",
            r"dist/",
            r"build/",
        ]

    def _should_ignore(self, path: str) -> bool:
        """Check if a path should be ignored based on ignore patterns.

        Args:
            path: Path to check

        Returns:
            True if the path should be ignored, False otherwise
        """
        for pattern in self.ignore_patterns:
            if re.search(pattern, path):
                return True
        return False

    async def _find_files(self,
                         extensions: Optional[List[str]] = None,
                         exclude_dirs: Optional[List[str]] = None) -> List[str]:
        """Find all files in the sandbox workspace with specified extensions.

        Args:
            extensions: List of file extensions to include (e.g., ['.py', '.js'])
            exclude_dirs: List of directories to exclude

        Returns:
            List of file paths
        """
        await self._ensure_sandbox()  # Ensure sandbox is initialized

        files = []
        exclude_dirs = exclude_dirs or []

        # Build find command
        find_cmd = f"find {self.workspace_path} -type f"

        # Add extension filters
        if extensions:
            ext_pattern = " -o ".join([f"-name '*{ext}'" for ext in extensions])
            find_cmd += f" \\( {ext_pattern} \\)"

        # Add exclude directory filters
        exclude_patterns = []
        for pattern in self.ignore_patterns:
            # Convert regex pattern to find-compatible pattern
            if pattern.startswith(r"\."):
                # Handle hidden files/dirs
                pattern = pattern.replace(r"\.", ".")
            if pattern.endswith("/"):
                # Handle directories
                exclude_patterns.append(f"-path '*{pattern}*' -prune")
            else:
                # Handle files
                exclude_patterns.append(f"-path '*{pattern}' -prune")

        # Add user-specified exclude directories
        for dir_name in exclude_dirs:
            exclude_patterns.append(f"-path '*/{dir_name}/*' -prune")

        if exclude_patterns:
            find_cmd = f"{find_cmd} -not \\( {' -o '.join(exclude_patterns)} \\)"

        # Execute the find command
        try:
            result = self.sandbox.commands.run(find_cmd, timeout=30)

            if result.exit_code == 0 and result.stdout:
                # Process the results
                for file_path in result.stdout.strip().split('\n'):
                    if file_path.strip():
                        # Convert to relative path
                        rel_path = file_path.replace(f"{self.workspace_path}/", "")
                        files.append(rel_path)
            else:
                logger.error(f"Error finding files: {result.stderr}")
        except Exception as e:
            logger.error(f"Error executing find command: {str(e)}")

        return files

    async def _search_file_content(self, file_path: str, query: str, case_sensitive: bool = False) -> Dict[str, Any]:
        """Search for a query in a file and return matching lines with context.

        Args:
            file_path: Path to the file to search
            query: Search query
            case_sensitive: Whether to perform case-sensitive search

        Returns:
            Dictionary with file information and matches
        """
        try:
            await self._ensure_sandbox()  # Ensure sandbox is initialized

            # Clean path to ensure it's relative to /workspace
            clean_path = file_path.lstrip('/')
            sandbox_path = f"{self.workspace_path}/{clean_path}"

            # Read file content from sandbox
            try:
                content = self.sandbox.files.read(sandbox_path)
                lines = content.split('\n')
            except Exception as e:
                logger.error(f"Error reading file {file_path}: {str(e)}")
                return {
                    'file_path': file_path,
                    'error': f"Error reading file: {str(e)}",
                    'matches': [],
                    'match_count': 0
                }

            matches = []

            # Use grep in the sandbox for efficient searching
            grep_cmd = f"grep -n"
            if not case_sensitive:
                grep_cmd += " -i"

            # Escape the query for shell
            escaped_query = query.replace("'", "'\\''")
            grep_cmd += f" '{escaped_query}' {sandbox_path}"

            try:
                result = self.sandbox.commands.run(grep_cmd, timeout=10)

                if result.exit_code == 0 and result.stdout:
                    # Process grep results
                    for line in result.stdout.strip().split('\n'):
                        if ':' in line:
                            line_num_str, _ = line.split(':', 1)
                            try:
                                line_num = int(line_num_str) - 1  # Convert to 0-based index

                                # Get context (lines before and after)
                                start = max(0, line_num - 2)
                                end = min(len(lines), line_num + 3)

                                context_lines = []
                                for j in range(start, end):
                                    context_lines.append({
                                        'line_number': j + 1,
                                        'content': lines[j],
                                        'is_match': j == line_num
                                    })

                                matches.append({
                                    'line_number': line_num + 1,
                                    'context': context_lines
                                })
                            except ValueError:
                                continue
                elif result.exit_code == 1:
                    # No matches found (grep returns 1 when no matches)
                    pass
                else:
                    logger.error(f"Error searching file {file_path}: {result.stderr}")
            except Exception as e:
                logger.error(f"Error executing grep: {str(e)}")

                # Fallback to Python-based search if grep fails
                # Prepare regex pattern
                flags = 0 if case_sensitive else re.IGNORECASE
                pattern = re.compile(re.escape(query), flags)

                # Search for matches
                for i, line in enumerate(lines):
                    if pattern.search(line):
                        # Get context (lines before and after)
                        start = max(0, i - 2)
                        end = min(len(lines), i + 3)

                        context_lines = []
                        for j in range(start, end):
                            context_lines.append({
                                'line_number': j + 1,
                                'content': lines[j],
                                'is_match': j == i
                            })

                        matches.append({
                            'line_number': i + 1,
                            'context': context_lines
                        })

            return {
                'file_path': file_path,
                'matches': matches,
                'match_count': len(matches)
            }
        except Exception as e:
            logger.error(f"Error searching file {file_path}: {str(e)}")
            return {
                'file_path': file_path,
                'error': str(e),
                'matches': [],
                'match_count': 0
            }

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "codebase_retrieval",
            "description": "Search the codebase for files and code matching specific criteria. This tool helps you find relevant code snippets, understand the project structure, and locate specific implementations.",
            "parameters": {
                "type": "object",
                "properties": {
                    "information_request": {
                        "type": "string",
                        "description": "A natural language description of what you're looking for in the codebase. Be specific about the functionality, patterns, or concepts you want to find."
                    },
                    "file_pattern": {
                        "type": "string",
                        "description": "Optional glob pattern to filter files (e.g., '*.py' for Python files, 'src/*.js' for JavaScript files in src directory)"
                    },
                    "extensions": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Optional list of file extensions to search (e.g., ['.py', '.js', '.tsx'])"
                    },
                    "exclude_dirs": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Optional list of directories to exclude from search"
                    },
                    "case_sensitive": {
                        "type": "boolean",
                        "description": "Whether to perform case-sensitive search",
                        "default": False
                    },
                    "max_results": {
                        "type": "integer",
                        "description": "Maximum number of results to return",
                        "default": 10
                    }
                },
                "required": ["information_request"]
            }
        }
    })
    @xml_schema(
        tag_name="codebase-retrieval",
        mappings=[
            {"param_name": "information_request", "node_type": "attribute", "path": "query"},
            {"param_name": "file_pattern", "node_type": "attribute", "path": "file_pattern"},
            {"param_name": "extensions", "node_type": "attribute", "path": "extensions"},
            {"param_name": "exclude_dirs", "node_type": "attribute", "path": "exclude_dirs"},
            {"param_name": "case_sensitive", "node_type": "attribute", "path": "case_sensitive"},
            {"param_name": "max_results", "node_type": "attribute", "path": "max_results"}
        ],
        example='''
        <codebase-retrieval
            query="find all API endpoint definitions"
            file_pattern="*.py"
            extensions=".py,.js"
            exclude_dirs="tests,docs"
            case_sensitive="false"
            max_results="20">
        </codebase-retrieval>
        '''
    )
    async def codebase_retrieval(
        self,
        information_request: str,
        file_pattern: Optional[str] = None,
        extensions: Optional[List[str]] = None,
        exclude_dirs: Optional[List[str]] = None,
        case_sensitive: bool = False,
        max_results: int = 10
    ) -> ToolResult:
        """
        Search the codebase for files and code matching specific criteria.

        Args:
            information_request: Natural language description of what to look for
            file_pattern: Optional glob pattern to filter files
            extensions: Optional list of file extensions to search
            exclude_dirs: Optional list of directories to exclude
            case_sensitive: Whether to perform case-sensitive search
            max_results: Maximum number of results to return

        Returns:
            ToolResult with search results
        """
        try:
            await self._ensure_sandbox()  # Ensure sandbox is initialized

            # Process extensions input
            if extensions and isinstance(extensions, str):
                extensions = [ext.strip() for ext in extensions.split(',')]
                # Ensure extensions start with a dot
                extensions = [ext if ext.startswith('.') else f'.{ext}' for ext in extensions]

            # Process exclude_dirs input
            if exclude_dirs and isinstance(exclude_dirs, str):
                exclude_dirs = [dir.strip() for dir in exclude_dirs.split(',')]

            # Find all files matching criteria
            files = await self._find_files(extensions, exclude_dirs)

            # Apply file pattern if specified
            if file_pattern:
                import fnmatch
                files = [f for f in files if fnmatch.fnmatch(f, file_pattern)]

            # Extract keywords from information_request
            keywords = information_request.lower().split()
            keywords = [k for k in keywords if len(k) > 3 and k not in ['find', 'search', 'look', 'for', 'the', 'and', 'that', 'with', 'have', 'this', 'from']]

            # If no keywords found, use the whole information_request
            if not keywords:
                keywords = [information_request]

            # Search for content matches
            search_tasks = []
            for keyword in keywords:
                for file_path in files:
                    search_tasks.append(self._search_file_content(file_path, keyword, case_sensitive))

            search_results = await asyncio.gather(*search_tasks)

            # Filter out results with no matches
            search_results = [result for result in search_results if result['match_count'] > 0]

            # Sort by match count (most matches first)
            search_results.sort(key=lambda x: x['match_count'], reverse=True)

            # Limit results
            search_results = search_results[:max_results]

            # Format the response
            formatted_results = []
            for result in search_results:
                file_info = {
                    'file_path': result['file_path'],
                    'match_count': result['match_count'],
                    'matches': []
                }

                for match in result['matches']:
                    context_lines = []
                    for line in match['context']:
                        prefix = '> ' if line['is_match'] else '  '
                        context_lines.append(f"{prefix}{line['line_number']}: {line['content']}")

                    file_info['matches'].append({
                        'line_number': match['line_number'],
                        'context': '\n'.join(context_lines)
                    })

                formatted_results.append(file_info)

            return self.success_response({
                'query': information_request,
                'total_files_searched': len(files),
                'total_matches_found': sum(result['match_count'] for result in search_results),
                'results': formatted_results
            })

        except Exception as e:
            logger.error(f"Error in codebase retrieval: {str(e)}")
            return self.fail_response(f"Error searching codebase: {str(e)}")


if __name__ == "__main__":
    import asyncio
    from agentpress.thread_manager import ThreadManager

    async def test_codebase_retrieval():
        """Test function for the codebase retrieval tool"""
        # Create a thread manager
        thread_manager = ThreadManager("test_thread")

        # Create a codebase retrieval tool
        retrieval_tool = CodebaseRetrievalTool("test_project", thread_manager)

        # Test codebase retrieval
        result = await retrieval_tool.codebase_retrieval(
            information_request="find API endpoint definitions",
            extensions=[".py"],
            max_results=5
        )
        print(json.dumps(result.output, indent=2))

    asyncio.run(test_codebase_retrieval())
