"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+search@6.5.10";
exports.ids = ["vendor-chunks/@codemirror+search@6.5.10"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+search@6.5.10/node_modules/@codemirror/search/dist/index.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+search@6.5.10/node_modules/@codemirror/search/dist/index.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RegExpCursor: () => (/* binding */ RegExpCursor),\n/* harmony export */   SearchCursor: () => (/* binding */ SearchCursor),\n/* harmony export */   SearchQuery: () => (/* binding */ SearchQuery),\n/* harmony export */   closeSearchPanel: () => (/* binding */ closeSearchPanel),\n/* harmony export */   findNext: () => (/* binding */ findNext),\n/* harmony export */   findPrevious: () => (/* binding */ findPrevious),\n/* harmony export */   getSearchQuery: () => (/* binding */ getSearchQuery),\n/* harmony export */   gotoLine: () => (/* binding */ gotoLine),\n/* harmony export */   highlightSelectionMatches: () => (/* binding */ highlightSelectionMatches),\n/* harmony export */   openSearchPanel: () => (/* binding */ openSearchPanel),\n/* harmony export */   replaceAll: () => (/* binding */ replaceAll),\n/* harmony export */   replaceNext: () => (/* binding */ replaceNext),\n/* harmony export */   search: () => (/* binding */ search),\n/* harmony export */   searchKeymap: () => (/* binding */ searchKeymap),\n/* harmony export */   searchPanelOpen: () => (/* binding */ searchPanelOpen),\n/* harmony export */   selectMatches: () => (/* binding */ selectMatches),\n/* harmony export */   selectNextOccurrence: () => (/* binding */ selectNextOccurrence),\n/* harmony export */   selectSelectionMatches: () => (/* binding */ selectSelectionMatches),\n/* harmony export */   setSearchQuery: () => (/* binding */ setSearchQuery)\n/* harmony export */ });\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var crelt__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crelt */ \"(ssr)/./node_modules/.pnpm/crelt@1.0.6/node_modules/crelt/index.js\");\n\n\n\n\nconst basicNormalize = typeof String.prototype.normalize == \"function\"\n    ? x => x.normalize(\"NFKD\") : x => x;\n/**\nA search cursor provides an iterator over text matches in a\ndocument.\n*/\nclass SearchCursor {\n    /**\n    Create a text cursor. The query is the search string, `from` to\n    `to` provides the region to search.\n    \n    When `normalize` is given, it will be called, on both the query\n    string and the content it is matched against, before comparing.\n    You can, for example, create a case-insensitive search by\n    passing `s => s.toLowerCase()`.\n    \n    Text is always normalized with\n    [`.normalize(\"NFKD\")`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/normalize)\n    (when supported).\n    */\n    constructor(text, query, from = 0, to = text.length, normalize, test) {\n        this.test = test;\n        /**\n        The current match (only holds a meaningful value after\n        [`next`](https://codemirror.net/6/docs/ref/#search.SearchCursor.next) has been called and when\n        `done` is false).\n        */\n        this.value = { from: 0, to: 0 };\n        /**\n        Whether the end of the iterated region has been reached.\n        */\n        this.done = false;\n        this.matches = [];\n        this.buffer = \"\";\n        this.bufferPos = 0;\n        this.iter = text.iterRange(from, to);\n        this.bufferStart = from;\n        this.normalize = normalize ? x => normalize(basicNormalize(x)) : basicNormalize;\n        this.query = this.normalize(query);\n    }\n    peek() {\n        if (this.bufferPos == this.buffer.length) {\n            this.bufferStart += this.buffer.length;\n            this.iter.next();\n            if (this.iter.done)\n                return -1;\n            this.bufferPos = 0;\n            this.buffer = this.iter.value;\n        }\n        return (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointAt)(this.buffer, this.bufferPos);\n    }\n    /**\n    Look for the next match. Updates the iterator's\n    [`value`](https://codemirror.net/6/docs/ref/#search.SearchCursor.value) and\n    [`done`](https://codemirror.net/6/docs/ref/#search.SearchCursor.done) properties. Should be called\n    at least once before using the cursor.\n    */\n    next() {\n        while (this.matches.length)\n            this.matches.pop();\n        return this.nextOverlapping();\n    }\n    /**\n    The `next` method will ignore matches that partially overlap a\n    previous match. This method behaves like `next`, but includes\n    such matches.\n    */\n    nextOverlapping() {\n        for (;;) {\n            let next = this.peek();\n            if (next < 0) {\n                this.done = true;\n                return this;\n            }\n            let str = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.fromCodePoint)(next), start = this.bufferStart + this.bufferPos;\n            this.bufferPos += (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.codePointSize)(next);\n            let norm = this.normalize(str);\n            if (norm.length)\n                for (let i = 0, pos = start;; i++) {\n                    let code = norm.charCodeAt(i);\n                    let match = this.match(code, pos, this.bufferPos + this.bufferStart);\n                    if (i == norm.length - 1) {\n                        if (match) {\n                            this.value = match;\n                            return this;\n                        }\n                        break;\n                    }\n                    if (pos == start && i < str.length && str.charCodeAt(i) == code)\n                        pos++;\n                }\n        }\n    }\n    match(code, pos, end) {\n        let match = null;\n        for (let i = 0; i < this.matches.length; i += 2) {\n            let index = this.matches[i], keep = false;\n            if (this.query.charCodeAt(index) == code) {\n                if (index == this.query.length - 1) {\n                    match = { from: this.matches[i + 1], to: end };\n                }\n                else {\n                    this.matches[i]++;\n                    keep = true;\n                }\n            }\n            if (!keep) {\n                this.matches.splice(i, 2);\n                i -= 2;\n            }\n        }\n        if (this.query.charCodeAt(0) == code) {\n            if (this.query.length == 1)\n                match = { from: pos, to: end };\n            else\n                this.matches.push(1, pos);\n        }\n        if (match && this.test && !this.test(match.from, match.to, this.buffer, this.bufferStart))\n            match = null;\n        return match;\n    }\n}\nif (typeof Symbol != \"undefined\")\n    SearchCursor.prototype[Symbol.iterator] = function () { return this; };\n\nconst empty = { from: -1, to: -1, match: /*@__PURE__*//.*/.exec(\"\") };\nconst baseFlags = \"gm\" + (/x/.unicode == null ? \"\" : \"u\");\n/**\nThis class is similar to [`SearchCursor`](https://codemirror.net/6/docs/ref/#search.SearchCursor)\nbut searches for a regular expression pattern instead of a plain\nstring.\n*/\nclass RegExpCursor {\n    /**\n    Create a cursor that will search the given range in the given\n    document. `query` should be the raw pattern (as you'd pass it to\n    `new RegExp`).\n    */\n    constructor(text, query, options, from = 0, to = text.length) {\n        this.text = text;\n        this.to = to;\n        this.curLine = \"\";\n        /**\n        Set to `true` when the cursor has reached the end of the search\n        range.\n        */\n        this.done = false;\n        /**\n        Will contain an object with the extent of the match and the\n        match object when [`next`](https://codemirror.net/6/docs/ref/#search.RegExpCursor.next)\n        sucessfully finds a match.\n        */\n        this.value = empty;\n        if (/\\\\[sWDnr]|\\n|\\r|\\[\\^/.test(query))\n            return new MultilineRegExpCursor(text, query, options, from, to);\n        this.re = new RegExp(query, baseFlags + ((options === null || options === void 0 ? void 0 : options.ignoreCase) ? \"i\" : \"\"));\n        this.test = options === null || options === void 0 ? void 0 : options.test;\n        this.iter = text.iter();\n        let startLine = text.lineAt(from);\n        this.curLineStart = startLine.from;\n        this.matchPos = toCharEnd(text, from);\n        this.getLine(this.curLineStart);\n    }\n    getLine(skip) {\n        this.iter.next(skip);\n        if (this.iter.lineBreak) {\n            this.curLine = \"\";\n        }\n        else {\n            this.curLine = this.iter.value;\n            if (this.curLineStart + this.curLine.length > this.to)\n                this.curLine = this.curLine.slice(0, this.to - this.curLineStart);\n            this.iter.next();\n        }\n    }\n    nextLine() {\n        this.curLineStart = this.curLineStart + this.curLine.length + 1;\n        if (this.curLineStart > this.to)\n            this.curLine = \"\";\n        else\n            this.getLine(0);\n    }\n    /**\n    Move to the next match, if there is one.\n    */\n    next() {\n        for (let off = this.matchPos - this.curLineStart;;) {\n            this.re.lastIndex = off;\n            let match = this.matchPos <= this.to && this.re.exec(this.curLine);\n            if (match) {\n                let from = this.curLineStart + match.index, to = from + match[0].length;\n                this.matchPos = toCharEnd(this.text, to + (from == to ? 1 : 0));\n                if (from == this.curLineStart + this.curLine.length)\n                    this.nextLine();\n                if ((from < to || from > this.value.to) && (!this.test || this.test(from, to, match))) {\n                    this.value = { from, to, match };\n                    return this;\n                }\n                off = this.matchPos - this.curLineStart;\n            }\n            else if (this.curLineStart + this.curLine.length < this.to) {\n                this.nextLine();\n                off = 0;\n            }\n            else {\n                this.done = true;\n                return this;\n            }\n        }\n    }\n}\nconst flattened = /*@__PURE__*/new WeakMap();\n// Reusable (partially) flattened document strings\nclass FlattenedDoc {\n    constructor(from, text) {\n        this.from = from;\n        this.text = text;\n    }\n    get to() { return this.from + this.text.length; }\n    static get(doc, from, to) {\n        let cached = flattened.get(doc);\n        if (!cached || cached.from >= to || cached.to <= from) {\n            let flat = new FlattenedDoc(from, doc.sliceString(from, to));\n            flattened.set(doc, flat);\n            return flat;\n        }\n        if (cached.from == from && cached.to == to)\n            return cached;\n        let { text, from: cachedFrom } = cached;\n        if (cachedFrom > from) {\n            text = doc.sliceString(from, cachedFrom) + text;\n            cachedFrom = from;\n        }\n        if (cached.to < to)\n            text += doc.sliceString(cached.to, to);\n        flattened.set(doc, new FlattenedDoc(cachedFrom, text));\n        return new FlattenedDoc(from, text.slice(from - cachedFrom, to - cachedFrom));\n    }\n}\nclass MultilineRegExpCursor {\n    constructor(text, query, options, from, to) {\n        this.text = text;\n        this.to = to;\n        this.done = false;\n        this.value = empty;\n        this.matchPos = toCharEnd(text, from);\n        this.re = new RegExp(query, baseFlags + ((options === null || options === void 0 ? void 0 : options.ignoreCase) ? \"i\" : \"\"));\n        this.test = options === null || options === void 0 ? void 0 : options.test;\n        this.flat = FlattenedDoc.get(text, from, this.chunkEnd(from + 5000 /* Chunk.Base */));\n    }\n    chunkEnd(pos) {\n        return pos >= this.to ? this.to : this.text.lineAt(pos).to;\n    }\n    next() {\n        for (;;) {\n            let off = this.re.lastIndex = this.matchPos - this.flat.from;\n            let match = this.re.exec(this.flat.text);\n            // Skip empty matches directly after the last match\n            if (match && !match[0] && match.index == off) {\n                this.re.lastIndex = off + 1;\n                match = this.re.exec(this.flat.text);\n            }\n            if (match) {\n                let from = this.flat.from + match.index, to = from + match[0].length;\n                // If a match goes almost to the end of a noncomplete chunk, try\n                // again, since it'll likely be able to match more\n                if ((this.flat.to >= this.to || match.index + match[0].length <= this.flat.text.length - 10) &&\n                    (!this.test || this.test(from, to, match))) {\n                    this.value = { from, to, match };\n                    this.matchPos = toCharEnd(this.text, to + (from == to ? 1 : 0));\n                    return this;\n                }\n            }\n            if (this.flat.to == this.to) {\n                this.done = true;\n                return this;\n            }\n            // Grow the flattened doc\n            this.flat = FlattenedDoc.get(this.text, this.flat.from, this.chunkEnd(this.flat.from + this.flat.text.length * 2));\n        }\n    }\n}\nif (typeof Symbol != \"undefined\") {\n    RegExpCursor.prototype[Symbol.iterator] = MultilineRegExpCursor.prototype[Symbol.iterator] =\n        function () { return this; };\n}\nfunction validRegExp(source) {\n    try {\n        new RegExp(source, baseFlags);\n        return true;\n    }\n    catch (_a) {\n        return false;\n    }\n}\nfunction toCharEnd(text, pos) {\n    if (pos >= text.length)\n        return pos;\n    let line = text.lineAt(pos), next;\n    while (pos < line.to && (next = line.text.charCodeAt(pos - line.from)) >= 0xDC00 && next < 0xE000)\n        pos++;\n    return pos;\n}\n\nfunction createLineDialog(view) {\n    let line = String(view.state.doc.lineAt(view.state.selection.main.head).number);\n    let input = (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"input\", { class: \"cm-textfield\", name: \"line\", value: line });\n    let dom = (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"form\", {\n        class: \"cm-gotoLine\",\n        onkeydown: (event) => {\n            if (event.keyCode == 27) { // Escape\n                event.preventDefault();\n                view.dispatch({ effects: dialogEffect.of(false) });\n                view.focus();\n            }\n            else if (event.keyCode == 13) { // Enter\n                event.preventDefault();\n                go();\n            }\n        },\n        onsubmit: (event) => {\n            event.preventDefault();\n            go();\n        }\n    }, (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"label\", view.state.phrase(\"Go to line\"), \": \", input), \" \", (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"button\", { class: \"cm-button\", type: \"submit\" }, view.state.phrase(\"go\")), (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"button\", {\n        name: \"close\",\n        onclick: () => {\n            view.dispatch({ effects: dialogEffect.of(false) });\n            view.focus();\n        },\n        \"aria-label\": view.state.phrase(\"close\"),\n        type: \"button\"\n    }, [\"×\"]));\n    function go() {\n        let match = /^([+-])?(\\d+)?(:\\d+)?(%)?$/.exec(input.value);\n        if (!match)\n            return;\n        let { state } = view, startLine = state.doc.lineAt(state.selection.main.head);\n        let [, sign, ln, cl, percent] = match;\n        let col = cl ? +cl.slice(1) : 0;\n        let line = ln ? +ln : startLine.number;\n        if (ln && percent) {\n            let pc = line / 100;\n            if (sign)\n                pc = pc * (sign == \"-\" ? -1 : 1) + (startLine.number / state.doc.lines);\n            line = Math.round(state.doc.lines * pc);\n        }\n        else if (ln && sign) {\n            line = line * (sign == \"-\" ? -1 : 1) + startLine.number;\n        }\n        let docLine = state.doc.line(Math.max(1, Math.min(state.doc.lines, line)));\n        let selection = _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(docLine.from + Math.max(0, Math.min(col, docLine.length)));\n        view.dispatch({\n            effects: [dialogEffect.of(false), _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.scrollIntoView(selection.from, { y: 'center' })],\n            selection,\n        });\n        view.focus();\n    }\n    return { dom };\n}\nconst dialogEffect = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define();\nconst dialogField = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateField.define({\n    create() { return true; },\n    update(value, tr) {\n        for (let e of tr.effects)\n            if (e.is(dialogEffect))\n                value = e.value;\n        return value;\n    },\n    provide: f => _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.showPanel.from(f, val => val ? createLineDialog : null)\n});\n/**\nCommand that shows a dialog asking the user for a line number, and\nwhen a valid position is provided, moves the cursor to that line.\n\nSupports line numbers, relative line offsets prefixed with `+` or\n`-`, document percentages suffixed with `%`, and an optional\ncolumn position by adding `:` and a second number after the line\nnumber.\n*/\nconst gotoLine = view => {\n    let panel = (0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.getPanel)(view, createLineDialog);\n    if (!panel) {\n        let effects = [dialogEffect.of(true)];\n        if (view.state.field(dialogField, false) == null)\n            effects.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.appendConfig.of([dialogField, baseTheme$1]));\n        view.dispatch({ effects });\n        panel = (0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.getPanel)(view, createLineDialog);\n    }\n    if (panel)\n        panel.dom.querySelector(\"input\").select();\n    return true;\n};\nconst baseTheme$1 = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.baseTheme({\n    \".cm-panel.cm-gotoLine\": {\n        padding: \"2px 6px 4px\",\n        position: \"relative\",\n        \"& label\": { fontSize: \"80%\" },\n        \"& [name=close]\": {\n            position: \"absolute\",\n            top: \"0\", bottom: \"0\",\n            right: \"4px\",\n            backgroundColor: \"inherit\",\n            border: \"none\",\n            font: \"inherit\",\n            padding: \"0\"\n        }\n    }\n});\n\nconst defaultHighlightOptions = {\n    highlightWordAroundCursor: false,\n    minSelectionLength: 1,\n    maxMatches: 100,\n    wholeWords: false\n};\nconst highlightConfig = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Facet.define({\n    combine(options) {\n        return (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.combineConfig)(options, defaultHighlightOptions, {\n            highlightWordAroundCursor: (a, b) => a || b,\n            minSelectionLength: Math.min,\n            maxMatches: Math.min\n        });\n    }\n});\n/**\nThis extension highlights text that matches the selection. It uses\nthe `\"cm-selectionMatch\"` class for the highlighting. When\n`highlightWordAroundCursor` is enabled, the word at the cursor\nitself will be highlighted with `\"cm-selectionMatch-main\"`.\n*/\nfunction highlightSelectionMatches(options) {\n    let ext = [defaultTheme, matchHighlighter];\n    if (options)\n        ext.push(highlightConfig.of(options));\n    return ext;\n}\nconst matchDeco = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.mark({ class: \"cm-selectionMatch\" });\nconst mainMatchDeco = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.mark({ class: \"cm-selectionMatch cm-selectionMatch-main\" });\n// Whether the characters directly outside the given positions are non-word characters\nfunction insideWordBoundaries(check, state, from, to) {\n    return (from == 0 || check(state.sliceDoc(from - 1, from)) != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word) &&\n        (to == state.doc.length || check(state.sliceDoc(to, to + 1)) != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word);\n}\n// Whether the characters directly at the given positions are word characters\nfunction insideWord(check, state, from, to) {\n    return check(state.sliceDoc(from, from + 1)) == _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word\n        && check(state.sliceDoc(to - 1, to)) == _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word;\n}\nconst matchHighlighter = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.decorations = this.getDeco(view);\n    }\n    update(update) {\n        if (update.selectionSet || update.docChanged || update.viewportChanged)\n            this.decorations = this.getDeco(update.view);\n    }\n    getDeco(view) {\n        let conf = view.state.facet(highlightConfig);\n        let { state } = view, sel = state.selection;\n        if (sel.ranges.length > 1)\n            return _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.none;\n        let range = sel.main, query, check = null;\n        if (range.empty) {\n            if (!conf.highlightWordAroundCursor)\n                return _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.none;\n            let word = state.wordAt(range.head);\n            if (!word)\n                return _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.none;\n            check = state.charCategorizer(range.head);\n            query = state.sliceDoc(word.from, word.to);\n        }\n        else {\n            let len = range.to - range.from;\n            if (len < conf.minSelectionLength || len > 200)\n                return _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.none;\n            if (conf.wholeWords) {\n                query = state.sliceDoc(range.from, range.to); // TODO: allow and include leading/trailing space?\n                check = state.charCategorizer(range.head);\n                if (!(insideWordBoundaries(check, state, range.from, range.to) &&\n                    insideWord(check, state, range.from, range.to)))\n                    return _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.none;\n            }\n            else {\n                query = state.sliceDoc(range.from, range.to);\n                if (!query)\n                    return _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.none;\n            }\n        }\n        let deco = [];\n        for (let part of view.visibleRanges) {\n            let cursor = new SearchCursor(state.doc, query, part.from, part.to);\n            while (!cursor.next().done) {\n                let { from, to } = cursor.value;\n                if (!check || insideWordBoundaries(check, state, from, to)) {\n                    if (range.empty && from <= range.from && to >= range.to)\n                        deco.push(mainMatchDeco.range(from, to));\n                    else if (from >= range.to || to <= range.from)\n                        deco.push(matchDeco.range(from, to));\n                    if (deco.length > conf.maxMatches)\n                        return _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.none;\n                }\n            }\n        }\n        return _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.set(deco);\n    }\n}, {\n    decorations: v => v.decorations\n});\nconst defaultTheme = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.baseTheme({\n    \".cm-selectionMatch\": { backgroundColor: \"#99ff7780\" },\n    \".cm-searchMatch .cm-selectionMatch\": { backgroundColor: \"transparent\" }\n});\n// Select the words around the cursors.\nconst selectWord = ({ state, dispatch }) => {\n    let { selection } = state;\n    let newSel = _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.create(selection.ranges.map(range => state.wordAt(range.head) || _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.cursor(range.head)), selection.mainIndex);\n    if (newSel.eq(selection))\n        return false;\n    dispatch(state.update({ selection: newSel }));\n    return true;\n};\n// Find next occurrence of query relative to last cursor. Wrap around\n// the document if there are no more matches.\nfunction findNextOccurrence(state, query) {\n    let { main, ranges } = state.selection;\n    let word = state.wordAt(main.head), fullWord = word && word.from == main.from && word.to == main.to;\n    for (let cycled = false, cursor = new SearchCursor(state.doc, query, ranges[ranges.length - 1].to);;) {\n        cursor.next();\n        if (cursor.done) {\n            if (cycled)\n                return null;\n            cursor = new SearchCursor(state.doc, query, 0, Math.max(0, ranges[ranges.length - 1].from - 1));\n            cycled = true;\n        }\n        else {\n            if (cycled && ranges.some(r => r.from == cursor.value.from))\n                continue;\n            if (fullWord) {\n                let word = state.wordAt(cursor.value.from);\n                if (!word || word.from != cursor.value.from || word.to != cursor.value.to)\n                    continue;\n            }\n            return cursor.value;\n        }\n    }\n}\n/**\nSelect next occurrence of the current selection. Expand selection\nto the surrounding word when the selection is empty.\n*/\nconst selectNextOccurrence = ({ state, dispatch }) => {\n    let { ranges } = state.selection;\n    if (ranges.some(sel => sel.from === sel.to))\n        return selectWord({ state, dispatch });\n    let searchedText = state.sliceDoc(ranges[0].from, ranges[0].to);\n    if (state.selection.ranges.some(r => state.sliceDoc(r.from, r.to) != searchedText))\n        return false;\n    let range = findNextOccurrence(state, searchedText);\n    if (!range)\n        return false;\n    dispatch(state.update({\n        selection: state.selection.addRange(_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.range(range.from, range.to), false),\n        effects: _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.scrollIntoView(range.to)\n    }));\n    return true;\n};\n\nconst searchConfigFacet = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Facet.define({\n    combine(configs) {\n        return (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.combineConfig)(configs, {\n            top: false,\n            caseSensitive: false,\n            literal: false,\n            regexp: false,\n            wholeWord: false,\n            createPanel: view => new SearchPanel(view),\n            scrollToMatch: range => _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.scrollIntoView(range)\n        });\n    }\n});\n/**\nAdd search state to the editor configuration, and optionally\nconfigure the search extension.\n([`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel) will automatically\nenable this if it isn't already on).\n*/\nfunction search(config) {\n    return config ? [searchConfigFacet.of(config), searchExtensions] : searchExtensions;\n}\n/**\nA search query. Part of the editor's search state.\n*/\nclass SearchQuery {\n    /**\n    Create a query object.\n    */\n    constructor(config) {\n        this.search = config.search;\n        this.caseSensitive = !!config.caseSensitive;\n        this.literal = !!config.literal;\n        this.regexp = !!config.regexp;\n        this.replace = config.replace || \"\";\n        this.valid = !!this.search && (!this.regexp || validRegExp(this.search));\n        this.unquoted = this.unquote(this.search);\n        this.wholeWord = !!config.wholeWord;\n    }\n    /**\n    @internal\n    */\n    unquote(text) {\n        return this.literal ? text :\n            text.replace(/\\\\([nrt\\\\])/g, (_, ch) => ch == \"n\" ? \"\\n\" : ch == \"r\" ? \"\\r\" : ch == \"t\" ? \"\\t\" : \"\\\\\");\n    }\n    /**\n    Compare this query to another query.\n    */\n    eq(other) {\n        return this.search == other.search && this.replace == other.replace &&\n            this.caseSensitive == other.caseSensitive && this.regexp == other.regexp &&\n            this.wholeWord == other.wholeWord;\n    }\n    /**\n    @internal\n    */\n    create() {\n        return this.regexp ? new RegExpQuery(this) : new StringQuery(this);\n    }\n    /**\n    Get a search cursor for this query, searching through the given\n    range in the given state.\n    */\n    getCursor(state, from = 0, to) {\n        let st = state.doc ? state : _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorState.create({ doc: state });\n        if (to == null)\n            to = st.doc.length;\n        return this.regexp ? regexpCursor(this, st, from, to) : stringCursor(this, st, from, to);\n    }\n}\nclass QueryType {\n    constructor(spec) {\n        this.spec = spec;\n    }\n}\nfunction stringCursor(spec, state, from, to) {\n    return new SearchCursor(state.doc, spec.unquoted, from, to, spec.caseSensitive ? undefined : x => x.toLowerCase(), spec.wholeWord ? stringWordTest(state.doc, state.charCategorizer(state.selection.main.head)) : undefined);\n}\nfunction stringWordTest(doc, categorizer) {\n    return (from, to, buf, bufPos) => {\n        if (bufPos > from || bufPos + buf.length < to) {\n            bufPos = Math.max(0, from - 2);\n            buf = doc.sliceString(bufPos, Math.min(doc.length, to + 2));\n        }\n        return (categorizer(charBefore(buf, from - bufPos)) != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word ||\n            categorizer(charAfter(buf, from - bufPos)) != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word) &&\n            (categorizer(charAfter(buf, to - bufPos)) != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word ||\n                categorizer(charBefore(buf, to - bufPos)) != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word);\n    };\n}\nclass StringQuery extends QueryType {\n    constructor(spec) {\n        super(spec);\n    }\n    nextMatch(state, curFrom, curTo) {\n        let cursor = stringCursor(this.spec, state, curTo, state.doc.length).nextOverlapping();\n        if (cursor.done) {\n            let end = Math.min(state.doc.length, curFrom + this.spec.unquoted.length);\n            cursor = stringCursor(this.spec, state, 0, end).nextOverlapping();\n        }\n        return cursor.done || cursor.value.from == curFrom && cursor.value.to == curTo ? null : cursor.value;\n    }\n    // Searching in reverse is, rather than implementing an inverted search\n    // cursor, done by scanning chunk after chunk forward.\n    prevMatchInRange(state, from, to) {\n        for (let pos = to;;) {\n            let start = Math.max(from, pos - 10000 /* FindPrev.ChunkSize */ - this.spec.unquoted.length);\n            let cursor = stringCursor(this.spec, state, start, pos), range = null;\n            while (!cursor.nextOverlapping().done)\n                range = cursor.value;\n            if (range)\n                return range;\n            if (start == from)\n                return null;\n            pos -= 10000 /* FindPrev.ChunkSize */;\n        }\n    }\n    prevMatch(state, curFrom, curTo) {\n        let found = this.prevMatchInRange(state, 0, curFrom);\n        if (!found)\n            found = this.prevMatchInRange(state, Math.max(0, curTo - this.spec.unquoted.length), state.doc.length);\n        return found && (found.from != curFrom || found.to != curTo) ? found : null;\n    }\n    getReplacement(_result) { return this.spec.unquote(this.spec.replace); }\n    matchAll(state, limit) {\n        let cursor = stringCursor(this.spec, state, 0, state.doc.length), ranges = [];\n        while (!cursor.next().done) {\n            if (ranges.length >= limit)\n                return null;\n            ranges.push(cursor.value);\n        }\n        return ranges;\n    }\n    highlight(state, from, to, add) {\n        let cursor = stringCursor(this.spec, state, Math.max(0, from - this.spec.unquoted.length), Math.min(to + this.spec.unquoted.length, state.doc.length));\n        while (!cursor.next().done)\n            add(cursor.value.from, cursor.value.to);\n    }\n}\nfunction regexpCursor(spec, state, from, to) {\n    return new RegExpCursor(state.doc, spec.search, {\n        ignoreCase: !spec.caseSensitive,\n        test: spec.wholeWord ? regexpWordTest(state.charCategorizer(state.selection.main.head)) : undefined\n    }, from, to);\n}\nfunction charBefore(str, index) {\n    return str.slice((0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.findClusterBreak)(str, index, false), index);\n}\nfunction charAfter(str, index) {\n    return str.slice(index, (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.findClusterBreak)(str, index));\n}\nfunction regexpWordTest(categorizer) {\n    return (_from, _to, match) => !match[0].length ||\n        (categorizer(charBefore(match.input, match.index)) != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word ||\n            categorizer(charAfter(match.input, match.index)) != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word) &&\n            (categorizer(charAfter(match.input, match.index + match[0].length)) != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word ||\n                categorizer(charBefore(match.input, match.index + match[0].length)) != _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.CharCategory.Word);\n}\nclass RegExpQuery extends QueryType {\n    nextMatch(state, curFrom, curTo) {\n        let cursor = regexpCursor(this.spec, state, curTo, state.doc.length).next();\n        if (cursor.done)\n            cursor = regexpCursor(this.spec, state, 0, curFrom).next();\n        return cursor.done ? null : cursor.value;\n    }\n    prevMatchInRange(state, from, to) {\n        for (let size = 1;; size++) {\n            let start = Math.max(from, to - size * 10000 /* FindPrev.ChunkSize */);\n            let cursor = regexpCursor(this.spec, state, start, to), range = null;\n            while (!cursor.next().done)\n                range = cursor.value;\n            if (range && (start == from || range.from > start + 10))\n                return range;\n            if (start == from)\n                return null;\n        }\n    }\n    prevMatch(state, curFrom, curTo) {\n        return this.prevMatchInRange(state, 0, curFrom) ||\n            this.prevMatchInRange(state, curTo, state.doc.length);\n    }\n    getReplacement(result) {\n        return this.spec.unquote(this.spec.replace).replace(/\\$([$&]|\\d+)/g, (m, i) => {\n            if (i == \"&\")\n                return result.match[0];\n            if (i == \"$\")\n                return \"$\";\n            for (let l = i.length; l > 0; l--) {\n                let n = +i.slice(0, l);\n                if (n > 0 && n < result.match.length)\n                    return result.match[n] + i.slice(l);\n            }\n            return m;\n        });\n    }\n    matchAll(state, limit) {\n        let cursor = regexpCursor(this.spec, state, 0, state.doc.length), ranges = [];\n        while (!cursor.next().done) {\n            if (ranges.length >= limit)\n                return null;\n            ranges.push(cursor.value);\n        }\n        return ranges;\n    }\n    highlight(state, from, to, add) {\n        let cursor = regexpCursor(this.spec, state, Math.max(0, from - 250 /* RegExp.HighlightMargin */), Math.min(to + 250 /* RegExp.HighlightMargin */, state.doc.length));\n        while (!cursor.next().done)\n            add(cursor.value.from, cursor.value.to);\n    }\n}\n/**\nA state effect that updates the current search query. Note that\nthis only has an effect if the search state has been initialized\n(by including [`search`](https://codemirror.net/6/docs/ref/#search.search) in your configuration or\nby running [`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel) at least\nonce).\n*/\nconst setSearchQuery = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define();\nconst togglePanel = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.define();\nconst searchState = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateField.define({\n    create(state) {\n        return new SearchState(defaultQuery(state).create(), null);\n    },\n    update(value, tr) {\n        for (let effect of tr.effects) {\n            if (effect.is(setSearchQuery))\n                value = new SearchState(effect.value.create(), value.panel);\n            else if (effect.is(togglePanel))\n                value = new SearchState(value.query, effect.value ? createSearchPanel : null);\n        }\n        return value;\n    },\n    provide: f => _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.showPanel.from(f, val => val.panel)\n});\n/**\nGet the current search query from an editor state.\n*/\nfunction getSearchQuery(state) {\n    let curState = state.field(searchState, false);\n    return curState ? curState.query.spec : defaultQuery(state);\n}\n/**\nQuery whether the search panel is open in the given editor state.\n*/\nfunction searchPanelOpen(state) {\n    var _a;\n    return ((_a = state.field(searchState, false)) === null || _a === void 0 ? void 0 : _a.panel) != null;\n}\nclass SearchState {\n    constructor(query, panel) {\n        this.query = query;\n        this.panel = panel;\n    }\n}\nconst matchMark = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.mark({ class: \"cm-searchMatch\" }), selectedMatchMark = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.mark({ class: \"cm-searchMatch cm-searchMatch-selected\" });\nconst searchHighlighter = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.view = view;\n        this.decorations = this.highlight(view.state.field(searchState));\n    }\n    update(update) {\n        let state = update.state.field(searchState);\n        if (state != update.startState.field(searchState) || update.docChanged || update.selectionSet || update.viewportChanged)\n            this.decorations = this.highlight(state);\n    }\n    highlight({ query, panel }) {\n        if (!panel || !query.spec.valid)\n            return _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.Decoration.none;\n        let { view } = this;\n        let builder = new _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.RangeSetBuilder();\n        for (let i = 0, ranges = view.visibleRanges, l = ranges.length; i < l; i++) {\n            let { from, to } = ranges[i];\n            while (i < l - 1 && to > ranges[i + 1].from - 2 * 250 /* RegExp.HighlightMargin */)\n                to = ranges[++i].to;\n            query.highlight(view.state, from, to, (from, to) => {\n                let selected = view.state.selection.ranges.some(r => r.from == from && r.to == to);\n                builder.add(from, to, selected ? selectedMatchMark : matchMark);\n            });\n        }\n        return builder.finish();\n    }\n}, {\n    decorations: v => v.decorations\n});\nfunction searchCommand(f) {\n    return view => {\n        let state = view.state.field(searchState, false);\n        return state && state.query.spec.valid ? f(view, state) : openSearchPanel(view);\n    };\n}\n/**\nOpen the search panel if it isn't already open, and move the\nselection to the first match after the current main selection.\nWill wrap around to the start of the document when it reaches the\nend.\n*/\nconst findNext = /*@__PURE__*/searchCommand((view, { query }) => {\n    let { to } = view.state.selection.main;\n    let next = query.nextMatch(view.state, to, to);\n    if (!next)\n        return false;\n    let selection = _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.single(next.from, next.to);\n    let config = view.state.facet(searchConfigFacet);\n    view.dispatch({\n        selection,\n        effects: [announceMatch(view, next), config.scrollToMatch(selection.main, view)],\n        userEvent: \"select.search\"\n    });\n    selectSearchInput(view);\n    return true;\n});\n/**\nMove the selection to the previous instance of the search query,\nbefore the current main selection. Will wrap past the start\nof the document to start searching at the end again.\n*/\nconst findPrevious = /*@__PURE__*/searchCommand((view, { query }) => {\n    let { state } = view, { from } = state.selection.main;\n    let prev = query.prevMatch(state, from, from);\n    if (!prev)\n        return false;\n    let selection = _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.single(prev.from, prev.to);\n    let config = view.state.facet(searchConfigFacet);\n    view.dispatch({\n        selection,\n        effects: [announceMatch(view, prev), config.scrollToMatch(selection.main, view)],\n        userEvent: \"select.search\"\n    });\n    selectSearchInput(view);\n    return true;\n});\n/**\nSelect all instances of the search query.\n*/\nconst selectMatches = /*@__PURE__*/searchCommand((view, { query }) => {\n    let ranges = query.matchAll(view.state, 1000);\n    if (!ranges || !ranges.length)\n        return false;\n    view.dispatch({\n        selection: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.create(ranges.map(r => _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.range(r.from, r.to))),\n        userEvent: \"select.search.matches\"\n    });\n    return true;\n});\n/**\nSelect all instances of the currently selected text.\n*/\nconst selectSelectionMatches = ({ state, dispatch }) => {\n    let sel = state.selection;\n    if (sel.ranges.length > 1 || sel.main.empty)\n        return false;\n    let { from, to } = sel.main;\n    let ranges = [], main = 0;\n    for (let cur = new SearchCursor(state.doc, state.sliceDoc(from, to)); !cur.next().done;) {\n        if (ranges.length > 1000)\n            return false;\n        if (cur.value.from == from)\n            main = ranges.length;\n        ranges.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.range(cur.value.from, cur.value.to));\n    }\n    dispatch(state.update({\n        selection: _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.create(ranges, main),\n        userEvent: \"select.search.matches\"\n    }));\n    return true;\n};\n/**\nReplace the current match of the search query.\n*/\nconst replaceNext = /*@__PURE__*/searchCommand((view, { query }) => {\n    let { state } = view, { from, to } = state.selection.main;\n    if (state.readOnly)\n        return false;\n    let match = query.nextMatch(state, from, from);\n    if (!match)\n        return false;\n    let next = match;\n    let changes = [], selection, replacement;\n    let effects = [];\n    if (next.from == from && next.to == to) {\n        replacement = state.toText(query.getReplacement(next));\n        changes.push({ from: next.from, to: next.to, insert: replacement });\n        next = query.nextMatch(state, next.from, next.to);\n        effects.push(_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.announce.of(state.phrase(\"replaced match on line $\", state.doc.lineAt(from).number) + \".\"));\n    }\n    if (next) {\n        let off = changes.length == 0 || changes[0].from >= match.to ? 0 : match.to - match.from - replacement.length;\n        selection = _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.EditorSelection.single(next.from - off, next.to - off);\n        effects.push(announceMatch(view, next));\n        effects.push(state.facet(searchConfigFacet).scrollToMatch(selection.main, view));\n    }\n    view.dispatch({\n        changes, selection, effects,\n        userEvent: \"input.replace\"\n    });\n    return true;\n});\n/**\nReplace all instances of the search query with the given\nreplacement.\n*/\nconst replaceAll = /*@__PURE__*/searchCommand((view, { query }) => {\n    if (view.state.readOnly)\n        return false;\n    let changes = query.matchAll(view.state, 1e9).map(match => {\n        let { from, to } = match;\n        return { from, to, insert: query.getReplacement(match) };\n    });\n    if (!changes.length)\n        return false;\n    let announceText = view.state.phrase(\"replaced $ matches\", changes.length) + \".\";\n    view.dispatch({\n        changes,\n        effects: _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.announce.of(announceText),\n        userEvent: \"input.replace.all\"\n    });\n    return true;\n});\nfunction createSearchPanel(view) {\n    return view.state.facet(searchConfigFacet).createPanel(view);\n}\nfunction defaultQuery(state, fallback) {\n    var _a, _b, _c, _d, _e;\n    let sel = state.selection.main;\n    let selText = sel.empty || sel.to > sel.from + 100 ? \"\" : state.sliceDoc(sel.from, sel.to);\n    if (fallback && !selText)\n        return fallback;\n    let config = state.facet(searchConfigFacet);\n    return new SearchQuery({\n        search: ((_a = fallback === null || fallback === void 0 ? void 0 : fallback.literal) !== null && _a !== void 0 ? _a : config.literal) ? selText : selText.replace(/\\n/g, \"\\\\n\"),\n        caseSensitive: (_b = fallback === null || fallback === void 0 ? void 0 : fallback.caseSensitive) !== null && _b !== void 0 ? _b : config.caseSensitive,\n        literal: (_c = fallback === null || fallback === void 0 ? void 0 : fallback.literal) !== null && _c !== void 0 ? _c : config.literal,\n        regexp: (_d = fallback === null || fallback === void 0 ? void 0 : fallback.regexp) !== null && _d !== void 0 ? _d : config.regexp,\n        wholeWord: (_e = fallback === null || fallback === void 0 ? void 0 : fallback.wholeWord) !== null && _e !== void 0 ? _e : config.wholeWord\n    });\n}\nfunction getSearchInput(view) {\n    let panel = (0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.getPanel)(view, createSearchPanel);\n    return panel && panel.dom.querySelector(\"[main-field]\");\n}\nfunction selectSearchInput(view) {\n    let input = getSearchInput(view);\n    if (input && input == view.root.activeElement)\n        input.select();\n}\n/**\nMake sure the search panel is open and focused.\n*/\nconst openSearchPanel = view => {\n    let state = view.state.field(searchState, false);\n    if (state && state.panel) {\n        let searchInput = getSearchInput(view);\n        if (searchInput && searchInput != view.root.activeElement) {\n            let query = defaultQuery(view.state, state.query.spec);\n            if (query.valid)\n                view.dispatch({ effects: setSearchQuery.of(query) });\n            searchInput.focus();\n            searchInput.select();\n        }\n    }\n    else {\n        view.dispatch({ effects: [\n                togglePanel.of(true),\n                state ? setSearchQuery.of(defaultQuery(view.state, state.query.spec)) : _codemirror_state__WEBPACK_IMPORTED_MODULE_1__.StateEffect.appendConfig.of(searchExtensions)\n            ] });\n    }\n    return true;\n};\n/**\nClose the search panel.\n*/\nconst closeSearchPanel = view => {\n    let state = view.state.field(searchState, false);\n    if (!state || !state.panel)\n        return false;\n    let panel = (0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.getPanel)(view, createSearchPanel);\n    if (panel && panel.dom.contains(view.root.activeElement))\n        view.focus();\n    view.dispatch({ effects: togglePanel.of(false) });\n    return true;\n};\n/**\nDefault search-related key bindings.\n\n - Mod-f: [`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel)\n - F3, Mod-g: [`findNext`](https://codemirror.net/6/docs/ref/#search.findNext)\n - Shift-F3, Shift-Mod-g: [`findPrevious`](https://codemirror.net/6/docs/ref/#search.findPrevious)\n - Mod-Alt-g: [`gotoLine`](https://codemirror.net/6/docs/ref/#search.gotoLine)\n - Mod-d: [`selectNextOccurrence`](https://codemirror.net/6/docs/ref/#search.selectNextOccurrence)\n*/\nconst searchKeymap = [\n    { key: \"Mod-f\", run: openSearchPanel, scope: \"editor search-panel\" },\n    { key: \"F3\", run: findNext, shift: findPrevious, scope: \"editor search-panel\", preventDefault: true },\n    { key: \"Mod-g\", run: findNext, shift: findPrevious, scope: \"editor search-panel\", preventDefault: true },\n    { key: \"Escape\", run: closeSearchPanel, scope: \"editor search-panel\" },\n    { key: \"Mod-Shift-l\", run: selectSelectionMatches },\n    { key: \"Mod-Alt-g\", run: gotoLine },\n    { key: \"Mod-d\", run: selectNextOccurrence, preventDefault: true },\n];\nclass SearchPanel {\n    constructor(view) {\n        this.view = view;\n        let query = this.query = view.state.field(searchState).query.spec;\n        this.commit = this.commit.bind(this);\n        this.searchField = (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"input\", {\n            value: query.search,\n            placeholder: phrase(view, \"Find\"),\n            \"aria-label\": phrase(view, \"Find\"),\n            class: \"cm-textfield\",\n            name: \"search\",\n            form: \"\",\n            \"main-field\": \"true\",\n            onchange: this.commit,\n            onkeyup: this.commit\n        });\n        this.replaceField = (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"input\", {\n            value: query.replace,\n            placeholder: phrase(view, \"Replace\"),\n            \"aria-label\": phrase(view, \"Replace\"),\n            class: \"cm-textfield\",\n            name: \"replace\",\n            form: \"\",\n            onchange: this.commit,\n            onkeyup: this.commit\n        });\n        this.caseField = (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"input\", {\n            type: \"checkbox\",\n            name: \"case\",\n            form: \"\",\n            checked: query.caseSensitive,\n            onchange: this.commit\n        });\n        this.reField = (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"input\", {\n            type: \"checkbox\",\n            name: \"re\",\n            form: \"\",\n            checked: query.regexp,\n            onchange: this.commit\n        });\n        this.wordField = (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"input\", {\n            type: \"checkbox\",\n            name: \"word\",\n            form: \"\",\n            checked: query.wholeWord,\n            onchange: this.commit\n        });\n        function button(name, onclick, content) {\n            return (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"button\", { class: \"cm-button\", name, onclick, type: \"button\" }, content);\n        }\n        this.dom = (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"div\", { onkeydown: (e) => this.keydown(e), class: \"cm-search\" }, [\n            this.searchField,\n            button(\"next\", () => findNext(view), [phrase(view, \"next\")]),\n            button(\"prev\", () => findPrevious(view), [phrase(view, \"previous\")]),\n            button(\"select\", () => selectMatches(view), [phrase(view, \"all\")]),\n            (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"label\", null, [this.caseField, phrase(view, \"match case\")]),\n            (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"label\", null, [this.reField, phrase(view, \"regexp\")]),\n            (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"label\", null, [this.wordField, phrase(view, \"by word\")]),\n            ...view.state.readOnly ? [] : [\n                (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"br\"),\n                this.replaceField,\n                button(\"replace\", () => replaceNext(view), [phrase(view, \"replace\")]),\n                button(\"replaceAll\", () => replaceAll(view), [phrase(view, \"replace all\")])\n            ],\n            (0,crelt__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"button\", {\n                name: \"close\",\n                onclick: () => closeSearchPanel(view),\n                \"aria-label\": phrase(view, \"close\"),\n                type: \"button\"\n            }, [\"×\"])\n        ]);\n    }\n    commit() {\n        let query = new SearchQuery({\n            search: this.searchField.value,\n            caseSensitive: this.caseField.checked,\n            regexp: this.reField.checked,\n            wholeWord: this.wordField.checked,\n            replace: this.replaceField.value,\n        });\n        if (!query.eq(this.query)) {\n            this.query = query;\n            this.view.dispatch({ effects: setSearchQuery.of(query) });\n        }\n    }\n    keydown(e) {\n        if ((0,_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.runScopeHandlers)(this.view, e, \"search-panel\")) {\n            e.preventDefault();\n        }\n        else if (e.keyCode == 13 && e.target == this.searchField) {\n            e.preventDefault();\n            (e.shiftKey ? findPrevious : findNext)(this.view);\n        }\n        else if (e.keyCode == 13 && e.target == this.replaceField) {\n            e.preventDefault();\n            replaceNext(this.view);\n        }\n    }\n    update(update) {\n        for (let tr of update.transactions)\n            for (let effect of tr.effects) {\n                if (effect.is(setSearchQuery) && !effect.value.eq(this.query))\n                    this.setQuery(effect.value);\n            }\n    }\n    setQuery(query) {\n        this.query = query;\n        this.searchField.value = query.search;\n        this.replaceField.value = query.replace;\n        this.caseField.checked = query.caseSensitive;\n        this.reField.checked = query.regexp;\n        this.wordField.checked = query.wholeWord;\n    }\n    mount() {\n        this.searchField.select();\n    }\n    get pos() { return 80; }\n    get top() { return this.view.state.facet(searchConfigFacet).top; }\n}\nfunction phrase(view, phrase) { return view.state.phrase(phrase); }\nconst AnnounceMargin = 30;\nconst Break = /[\\s\\.,:;?!]/;\nfunction announceMatch(view, { from, to }) {\n    let line = view.state.doc.lineAt(from), lineEnd = view.state.doc.lineAt(to).to;\n    let start = Math.max(line.from, from - AnnounceMargin), end = Math.min(lineEnd, to + AnnounceMargin);\n    let text = view.state.sliceDoc(start, end);\n    if (start != line.from) {\n        for (let i = 0; i < AnnounceMargin; i++)\n            if (!Break.test(text[i + 1]) && Break.test(text[i])) {\n                text = text.slice(i);\n                break;\n            }\n    }\n    if (end != lineEnd) {\n        for (let i = text.length - 1; i > text.length - AnnounceMargin; i--)\n            if (!Break.test(text[i - 1]) && Break.test(text[i])) {\n                text = text.slice(0, i);\n                break;\n            }\n    }\n    return _codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.announce.of(`${view.state.phrase(\"current match\")}. ${text} ${view.state.phrase(\"on line\")} ${line.number}.`);\n}\nconst baseTheme = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_2__.EditorView.baseTheme({\n    \".cm-panel.cm-search\": {\n        padding: \"2px 6px 4px\",\n        position: \"relative\",\n        \"& [name=close]\": {\n            position: \"absolute\",\n            top: \"0\",\n            right: \"4px\",\n            backgroundColor: \"inherit\",\n            border: \"none\",\n            font: \"inherit\",\n            padding: 0,\n            margin: 0\n        },\n        \"& input, & button, & label\": {\n            margin: \".2em .6em .2em 0\"\n        },\n        \"& input[type=checkbox]\": {\n            marginRight: \".2em\"\n        },\n        \"& label\": {\n            fontSize: \"80%\",\n            whiteSpace: \"pre\"\n        }\n    },\n    \"&light .cm-searchMatch\": { backgroundColor: \"#ffff0054\" },\n    \"&dark .cm-searchMatch\": { backgroundColor: \"#00ffff8a\" },\n    \"&light .cm-searchMatch-selected\": { backgroundColor: \"#ff6a0054\" },\n    \"&dark .cm-searchMatch-selected\": { backgroundColor: \"#ff00ff8a\" }\n});\nconst searchExtensions = [\n    searchState,\n    /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_1__.Prec.low(searchHighlighter),\n    baseTheme\n];\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+search@6.5.10/node_modules/@codemirror/search/dist/index.js\n");

/***/ })

};
;