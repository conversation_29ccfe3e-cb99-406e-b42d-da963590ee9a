"use client";

import React, { useState } from "react";
import { FileMessageRenderer } from "./file-message-renderer";
import { AgentDesktopView } from "./agent-desktop-view";
import { Project } from "@/lib/api";

interface FileMessageProps {
  fileName: string;
  content: string;
  binaryUrl?: string | null;
  sandboxId?: string;
  project?: Project;
  className?: string;
}

export function FileMessage({
  fileName,
  content,
  binaryUrl = null,
  sandboxId,
  project,
  className
}: FileMessageProps) {
  const [isDesktopViewOpen, setIsDesktopViewOpen] = useState(false);
  
  const handleViewFull = () => {
    if (sandboxId) {
      setIsDesktopViewOpen(true);
    }
  };
  
  return (
    <>
      <FileMessageRenderer
        fileName={fileName}
        content={content}
        binaryUrl={binaryUrl}
        className={className}
        onViewFull={sandboxId ? handleViewFull : undefined}
        project={project}
      />
      
      {sandboxId && (
        <AgentDesktopView
          open={isDesktopViewOpen}
          onOpenChange={setIsDesktopViewOpen}
          sandboxId={sandboxId}
          project={project}
          agentName={project?.name || "Agent"}
        />
      )}
    </>
  );
}
