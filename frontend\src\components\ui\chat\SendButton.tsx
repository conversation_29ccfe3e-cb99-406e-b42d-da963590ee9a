import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Send, Smile } from "lucide-react";
import { cn } from "@/lib/utils";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface SendButtonGroupProps {
  onSend?: (...args: any[]) => void;
  onEmojiSelect?: (emoji: string) => void;
  disabled?: boolean;
  showEmojiPicker?: boolean;
  className?: string;
}

export function SendButton({
  onSend,
  onEmojiSelect,
  disabled = false,
  showEmojiPicker = true,
  className,
}: SendButtonGroupProps) {
  const emojis = [
    "😊", "👍", "🎉", "🔥", "❤️", "😂", "🙌", "👏",
    "🤔", "👀", "✅", "⭐", "🚀", "💯", "👋", "🙏",
    "😎", "🤩", "😍", "🤗", "🤝", "💪", "👌", "🙂"
  ];

  return (
    <div className={cn("flex border border-[#333333] rounded-md overflow-hidden h-10", className)}>
      {showEmojiPicker && (
        <Popover>
          <PopoverTrigger asChild>
            <Button 
              type="button" 
              size="icon" 
              variant="ghost" 
              className="w-10 flex items-center justify-center text-[#999999] hover:text-white"
            >
              <Smile className="h-5 w-5" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-64 p-2 bg-[#222222] border-[#333333]" align="end" side="top">
            <div className="grid grid-cols-8 gap-1">
              {emojis.map(emoji => (
                <button
                  key={emoji}
                  className="h-8 w-8 flex items-center justify-center hover:bg-[#333333] rounded-md"
                  onClick={(e) => {
                    e.preventDefault();
                    if (onEmojiSelect) onEmojiSelect(emoji);
                  }}
                  type="button"
                >
                  {emoji}
                </button>
              ))}
            </div>
          </PopoverContent>
        </Popover>
      )}
      
      <Button
        type="submit"
        size="icon"
        onClick={onSend}
        disabled={disabled}
        className="w-10 flex items-center justify-center bg-[#222222] hover:bg-[#222222] text-[#999999] hover:text-white disabled:opacity-50"
      >
        <Send className="h-5 w-5" />
      </Button>
    </div>
  );
}
