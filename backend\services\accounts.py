"""
Account management service for Siden AI.
Handles account creation, management, and team operations.
"""

from typing import Optional, List, Dict, Any
from uuid import UUID
from services.supabase import DBConnection
from utils.logger import logger


class AccountService:
    """Service for managing accounts and account memberships."""
    
    def __init__(self):
        self.db = DBConnection()
    
    async def get_user_accounts(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all accounts a user has access to."""
        client = await self.db.client
        
        # Get accounts where user is the owner
        owned_accounts = await client.from_("accounts").select("*").eq("user_id", user_id).execute()
        
        # Get accounts where user is a member
        member_accounts = await client.from_("account_members").select("""
            account_id,
            role,
            accounts!inner(*)
        """).eq("user_id", user_id).execute()
        
        accounts = []
        
        # Add owned accounts
        if owned_accounts.data:
            for account in owned_accounts.data:
                account["user_role"] = "owner"
                accounts.append(account)
        
        # Add member accounts
        if member_accounts.data:
            for membership in member_accounts.data:
                account = membership["accounts"]
                account["user_role"] = membership["role"]
                accounts.append(account)
        
        return accounts
    
    async def get_account_by_id(self, account_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get account by ID if user has access."""
        client = await self.db.client
        
        # Check if user owns the account
        response = await client.from_("accounts").select("*").eq("id", account_id).eq("user_id", user_id).execute()
        
        if response.data:
            account = response.data[0]
            account["user_role"] = "owner"
            return account
        
        # Check if user is a member
        response = await client.from_("account_members").select("""
            role,
            accounts!inner(*)
        """).eq("account_id", account_id).eq("user_id", user_id).execute()
        
        if response.data:
            membership = response.data[0]
            account = membership["accounts"]
            account["user_role"] = membership["role"]
            return account
        
        return None
    
    async def create_personal_account(self, user_id: str, name: str) -> Dict[str, Any]:
        """Create a personal account for a user."""
        client = await self.db.client
        
        # Check if user already has a personal account
        existing = await client.from_("accounts").select("id").eq("user_id", user_id).eq("type", "personal").execute()
        
        if existing.data:
            raise ValueError("User already has a personal account")
        
        # Create the account
        response = await client.from_("accounts").insert({
            "user_id": user_id,
            "name": name,
            "type": "personal",
            "settings": {}
        }).execute()
        
        if not response.data:
            raise RuntimeError("Failed to create personal account")
        
        account = response.data[0]
        account["user_role"] = "owner"
        logger.info(f"Created personal account {account['id']} for user {user_id}")
        return account
    
    async def create_team_account(self, user_id: str, name: str) -> Dict[str, Any]:
        """Create a team account."""
        client = await self.db.client
        
        # Create the team account
        response = await client.from_("accounts").insert({
            "user_id": user_id,
            "name": name,
            "type": "team",
            "settings": {}
        }).execute()
        
        if not response.data:
            raise RuntimeError("Failed to create team account")
        
        account = response.data[0]
        
        # Add the creator as an owner member
        await client.from_("account_members").insert({
            "account_id": account["id"],
            "user_id": user_id,
            "role": "owner"
        }).execute()
        
        account["user_role"] = "owner"
        logger.info(f"Created team account {account['id']} for user {user_id}")
        return account
    
    async def add_member(self, account_id: str, user_id: str, member_user_id: str, role: str = "member") -> Dict[str, Any]:
        """Add a member to a team account."""
        client = await self.db.client
        
        # Verify the user has permission to add members
        account = await self.get_account_by_id(account_id, user_id)
        if not account or account["user_role"] not in ["owner", "admin"]:
            raise PermissionError("User does not have permission to add members")
        
        if account["type"] != "team":
            raise ValueError("Can only add members to team accounts")
        
        # Check if member already exists
        existing = await client.from_("account_members").select("id").eq("account_id", account_id).eq("user_id", member_user_id).execute()
        
        if existing.data:
            raise ValueError("User is already a member of this account")
        
        # Add the member
        response = await client.from_("account_members").insert({
            "account_id": account_id,
            "user_id": member_user_id,
            "role": role,
            "invited_by": user_id
        }).execute()
        
        if not response.data:
            raise RuntimeError("Failed to add member")
        
        logger.info(f"Added user {member_user_id} as {role} to account {account_id}")
        return response.data[0]
    
    async def remove_member(self, account_id: str, user_id: str, member_user_id: str) -> bool:
        """Remove a member from a team account."""
        client = await self.db.client
        
        # Verify the user has permission to remove members
        account = await self.get_account_by_id(account_id, user_id)
        if not account or account["user_role"] not in ["owner", "admin"]:
            raise PermissionError("User does not have permission to remove members")
        
        # Remove the member
        response = await client.from_("account_members").delete().eq("account_id", account_id).eq("user_id", member_user_id).execute()
        
        logger.info(f"Removed user {member_user_id} from account {account_id}")
        return True
    
    async def update_member_role(self, account_id: str, user_id: str, member_user_id: str, new_role: str) -> Dict[str, Any]:
        """Update a member's role in a team account."""
        client = await self.db.client
        
        # Verify the user has permission to update roles
        account = await self.get_account_by_id(account_id, user_id)
        if not account or account["user_role"] != "owner":
            raise PermissionError("Only account owners can update member roles")
        
        # Update the role
        response = await client.from_("account_members").update({
            "role": new_role
        }).eq("account_id", account_id).eq("user_id", member_user_id).execute()
        
        if not response.data:
            raise RuntimeError("Failed to update member role")
        
        logger.info(f"Updated user {member_user_id} role to {new_role} in account {account_id}")
        return response.data[0]
    
    async def get_account_members(self, account_id: str, user_id: str) -> List[Dict[str, Any]]:
        """Get all members of an account."""
        client = await self.db.client
        
        # Verify user has access to the account
        account = await self.get_account_by_id(account_id, user_id)
        if not account:
            raise PermissionError("User does not have access to this account")
        
        # Get account owner
        members = []
        members.append({
            "user_id": account["user_id"],
            "role": "owner",
            "joined_at": account["created_at"],
            "is_owner": True
        })
        
        # Get team members if it's a team account
        if account["type"] == "team":
            response = await client.from_("account_members").select("*").eq("account_id", account_id).execute()
            
            if response.data:
                for member in response.data:
                    member["is_owner"] = False
                    members.append(member)
        
        return members
