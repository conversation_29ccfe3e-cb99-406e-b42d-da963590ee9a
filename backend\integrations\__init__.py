from typing import Dict, Any, Optional, List, Union

class IntegrationRegistry:
    _instance = None
    _integrations = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(IntegrationRegistry, cls).__new__(cls)
        return cls._instance
    
    def register_integration(self, name: str, integration_class):
        self._integrations[name] = integration_class
        
    def get_integration(self, name: str):
        return self._integrations.get(name)
    
    def get_all_integrations(self):
        return self._integrations
        
registry = IntegrationRegistry()
