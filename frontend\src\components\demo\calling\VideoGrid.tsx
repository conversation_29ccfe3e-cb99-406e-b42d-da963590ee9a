"use client";

import React, { ReactNode } from 'react';
import { cn } from '@/lib/utils';
import { Participant } from './types';
import { ParticipantVideo } from './ParticipantVideo';

interface VideoGridProps {
  participants: Participant[];
  userVideo: ReactNode;
  isSidePanelOpen: boolean;
}

export function VideoGrid({
  participants,
  userVideo,
  isSidePanelOpen
}: VideoGridProps) {
  // Get grid layout class based on number of participants
  const getGridClass = () => {
    const totalParticipants = participants.length + 1; // +1 for user
    if (totalParticipants <= 1) return "grid-cols-1";
    if (totalParticipants === 2) return "grid-cols-1 md:grid-cols-2";
    if (totalParticipants <= 4) return "grid-cols-2";
    if (totalParticipants <= 6) return "grid-cols-3";
    if (totalParticipants <= 9) return "grid-cols-3";
    return "grid-cols-4";
  };
  
  // Calculate grid container styles based on side panels
  const getGridContainerStyles = () => {
    if (isSidePanelOpen) {
      return "flex-1 p-2 overflow-auto flex items-center justify-center";
    } else {
      return "w-full p-3 overflow-auto flex items-center justify-center";
    }
  };
  
  // Calculate grid styles based on number of participants
  const getGridStyles = () => {
    const baseStyles = "grid gap-2 place-items-center";
    
    if (isSidePanelOpen) {
      // When side panel is open, maintain standard grid with consistent spacing
      return `${baseStyles} w-full auto-rows-fr max-w-6xl mx-auto`;
    } else {
      // When no side panel, ensure videos take up more space with consistent edge spacing
      return `${baseStyles} w-full mx-auto auto-rows-fr max-w-7xl`;
    }
  };

  return (
    <div className={cn(getGridContainerStyles(), "h-full")}>
      <div className={cn(
        getGridStyles(),
        getGridClass()
      )}>
        {/* User's video */}
        {userVideo}

        {/* Participant videos */}
        {participants.map(participant => (
          <ParticipantVideo
            key={participant.id}
            participant={participant}
          />
        ))}
      </div>
    </div>
  );
}