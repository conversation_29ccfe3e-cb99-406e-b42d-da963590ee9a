'use client';

import React, { useState } from 'react';
import { CodeArtifact } from '@/components/ui/code-artifact';
import { useCodeExecution } from '@/hooks/use-code-execution';
import { detectCodeGeneration } from '@/utils/code-detection';
import { MessageRenderer } from '@/components/project/chat/MessageRenderer';

const samplePythonCode = `import matplotlib.pyplot as plt
import numpy as np

# Create sample data
x = np.linspace(0, 10, 100)
y1 = np.sin(x)
y2 = np.cos(x)

# Create the plot
plt.figure(figsize=(10, 6))
plt.plot(x, y1, 'b-', linewidth=2, label='sin(x)')
plt.plot(x, y2, 'r--', linewidth=2, label='cos(x)')
plt.xlabel('x')
plt.ylabel('y')
plt.title('Sine and Cosine Functions')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

print("Matplotlib plot generated successfully!")`;

const sampleDataAnalysisCode = `import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# Create sample dataset
np.random.seed(42)
data = {
    'sales': np.random.normal(1000, 200, 100),
    'marketing_spend': np.random.normal(500, 100, 100),
    'month': np.repeat(['Jan', 'Feb', 'Mar', 'Apr'], 25)
}

df = pd.DataFrame(data)

# Basic statistics
print("Dataset Overview:")
print(df.describe())
print("\\nSales by Month:")
print(df.groupby('month')['sales'].mean())

# Create visualization
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
plt.scatter(df['marketing_spend'], df['sales'], alpha=0.6)
plt.xlabel('Marketing Spend')
plt.ylabel('Sales')
plt.title('Sales vs Marketing Spend')

plt.subplot(1, 2, 2)
df.groupby('month')['sales'].mean().plot(kind='bar')
plt.title('Average Sales by Month')
plt.xticks(rotation=45)

plt.tight_layout()
plt.show()`;

const sampleMessage = `Here's a Python script that creates a beautiful data visualization:

\`\`\`python
import matplotlib.pyplot as plt
import numpy as np

# Generate sample data
x = np.linspace(0, 4*np.pi, 100)
y1 = np.sin(x)
y2 = np.cos(x)
y3 = np.sin(x) * np.cos(x)

# Create the plot
plt.figure(figsize=(12, 8))
plt.plot(x, y1, 'b-', linewidth=2, label='sin(x)')
plt.plot(x, y2, 'r--', linewidth=2, label='cos(x)')
plt.plot(x, y3, 'g:', linewidth=3, label='sin(x) * cos(x)')

plt.xlabel('x')
plt.ylabel('y')
plt.title('Trigonometric Functions Comparison')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

print("Beautiful trigonometric plot created!")
\`\`\`

This code demonstrates how to create multiple function plots on the same graph with different line styles and colors.`;

export default function TestCodeArtifacts() {
  const { executeCode, results } = useCodeExecution();
  const [executionKey1, setExecutionKey1] = useState<string | null>(null);
  const [executionKey2, setExecutionKey2] = useState<string | null>(null);

  const handleExecute1 = async (code: string, language: string) => {
    const key = `test1_${Date.now()}`;
    setExecutionKey1(key);
    await executeCode(code, language, 30000, key);
  };

  const handleExecute2 = async (code: string, language: string) => {
    const key = `test2_${Date.now()}`;
    setExecutionKey2(key);
    await executeCode(code, language, 30000, key);
  };

  const executionResult1 = executionKey1 ? results[executionKey1] : undefined;
  const executionResult2 = executionKey2 ? results[executionKey2] : undefined;

  // Test code generation detection
  const codeDetection = detectCodeGeneration(sampleMessage);

  return (
    <div className="min-h-screen bg-[#0a0a0a] text-white p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Code Artifacts Test Page</h1>
          <p className="text-[#cccccc]">Testing the new ChatGPT-like code artifact functionality with E2B execution</p>
        </div>

        {/* Test 1: Basic Code Artifact */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Test 1: Basic Matplotlib Plot</h2>
          <CodeArtifact
            code={samplePythonCode}
            language="python"
            title="Sine and Cosine Functions"
            description="A simple matplotlib example showing trigonometric functions"
            onExecute={handleExecute1}
            executionResult={executionResult1}
          />
        </div>

        {/* Test 2: Data Analysis Code */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Test 2: Data Analysis with Pandas</h2>
          <CodeArtifact
            code={sampleDataAnalysisCode}
            language="python"
            title="Sales Data Analysis"
            description="Data analysis example with pandas and matplotlib"
            onExecute={handleExecute2}
            executionResult={executionResult2}
          />
        </div>

        {/* Test 3: Message Renderer with Code Detection */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Test 3: Message Renderer with Auto-Detection</h2>
          <div className="bg-[#1a1a1a] border border-[#333333] rounded-lg p-4">
            <h3 className="text-sm font-medium text-[#999999] mb-2">Code Detection Results:</h3>
            <div className="text-sm space-y-1 mb-4">
              <div>Is Code Generation: <span className={codeDetection.isCodeGeneration ? 'text-green-400' : 'text-red-400'}>{codeDetection.isCodeGeneration ? 'Yes' : 'No'}</span></div>
              <div>Confidence: <span className="text-blue-400">{(codeDetection.confidence * 100).toFixed(1)}%</span></div>
              <div>Has Executable Code: <span className={codeDetection.hasExecutableCode ? 'text-green-400' : 'text-red-400'}>{codeDetection.hasExecutableCode ? 'Yes' : 'No'}</span></div>
              <div>Primary Language: <span className="text-yellow-400">{codeDetection.primaryLanguage || 'None'}</span></div>
              <div>Code Blocks Found: <span className="text-purple-400">{codeDetection.codeBlocks.length}</span></div>
            </div>
            
            <MessageRenderer
              message={{
                id: 'test-message',
                sender: 'developer',
                content: sampleMessage,
                timestamp: new Date().toLocaleTimeString(),
                role: 'assistant'
              }}
              onFileClick={() => {}}
            />
          </div>
        </div>

        {/* Test 4: Simple Code Examples */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Test 4: Simple Code Examples</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <CodeArtifact
              code={`print("Hello, World!")
for i in range(5):
    print(f"Count: {i}")
    
result = sum(range(10))
print(f"Sum of 0-9: {result}")`}
              language="python"
              title="Hello World Example"
              description="Basic Python example"
              showExecuteButton={true}
            />
            
            <CodeArtifact
              code={`const greeting = "Hello, JavaScript!";
console.log(greeting);

const numbers = [1, 2, 3, 4, 5];
const doubled = numbers.map(n => n * 2);
console.log("Doubled:", doubled);

const sum = numbers.reduce((a, b) => a + b, 0);
console.log("Sum:", sum);`}
              language="javascript"
              title="JavaScript Example"
              description="Basic JavaScript example"
              showExecuteButton={false}
            />
          </div>
        </div>

        {/* Status Information */}
        <div className="bg-[#1a1a1a] border border-[#333333] rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-2">System Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <div className="text-[#999999]">Frontend Server:</div>
              <div className="text-green-400">✅ Running on localhost:3001</div>
            </div>
            <div>
              <div className="text-[#999999]">Backend Test Server:</div>
              <div className="text-green-400">✅ Running on localhost:8002</div>
            </div>
            <div>
              <div className="text-[#999999]">E2B Code Execution:</div>
              <div className="text-green-400">✅ Ready</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
