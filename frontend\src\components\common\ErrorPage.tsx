"use client";

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface ErrorPageProps {
  error?: Error & { digest?: string };
  reset?: () => void;
  title?: string;
  description?: string;
  showReload?: boolean;
  isGlobal?: boolean;
}

export function ErrorPage({
  error,
  reset,
  title = "Something went wrong",
  description,
  showReload = true,
  isGlobal = false
}: ErrorPageProps) {
  useEffect(() => {
    if (error) {
      // Log the error to an error reporting service
      console.error(isGlobal ? 'Global error:' : 'Application error:', error);
    }
  }, [error, isGlobal]);

  const handleReload = () => {
    // Clear cache and reload the page
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  };

  const content = (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 text-center">
      <h1 className="text-4xl font-bold mb-4">{title}</h1>
      <p className="text-lg mb-8">
        {description || error?.message || 'An unexpected error occurred'}
      </p>
      <div className="flex gap-4">
        {reset && (
          <Button onClick={reset} variant="outline">
            Try again
          </Button>
        )}
        {showReload && (
          <Button onClick={handleReload}>
            Reload page
          </Button>
        )}
      </div>
    </div>
  );

  if (isGlobal) {
    return (
      <html>
        <body>
          {content}
        </body>
      </html>
    );
  }

  return content;
}
