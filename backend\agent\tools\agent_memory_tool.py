"""
Agent Memory Tool for accessing and managing agent memories.

This tool provides functions for storing, retrieving, and searching memories,
enabling agents to maintain persistent knowledge across conversations.
"""

import json
from typing import Dict, List, Any, Optional
from datetime import datetime

from agentpress.tool import Tool, ToolResult, openapi_schema, xml_schema
from agentpress.agent_memory import Agent<PERSON>emoryManager, MemoryType, MemoryPriority
from utils.logger import logger


class AgentMemoryTool(Tool):
    """Tool for accessing and managing agent memories."""
    
    def __init__(self, agent_id: str, **kwargs):
        """Initialize the AgentMemoryTool.
        
        Args:
            agent_id: ID of the agent using this tool
            **kwargs: Additional arguments
        """
        super().__init__()
        self.agent_id = agent_id
        self.memory_manager = AgentMemoryManager()
        logger.debug(f"Initialized AgentMemoryTool for agent {agent_id}")
    
    @openapi_schema({
        "name": "store_memory",
        "description": "Store a new memory for later recall",
        "parameters": {
            "type": "object",
            "properties": {
                "content": {
                    "type": "string",
                    "description": "Content of the memory to store"
                },
                "memory_type": {
                    "type": "string",
                    "enum": ["fact", "interaction", "preference", "skill", "feedback", "goal", "plan"],
                    "description": "Type of memory to store"
                },
                "priority": {
                    "type": "string",
                    "enum": ["low", "medium", "high", "critical"],
                    "description": "Priority of the memory",
                    "default": "medium"
                },
                "context": {
                    "type": "object",
                    "description": "Additional context for the memory",
                    "default": {}
                },
                "metadata": {
                    "type": "object",
                    "description": "Additional metadata for the memory",
                    "default": {}
                }
            },
            "required": ["content", "memory_type"]
        }
    })
    @xml_schema(
        tag_name="store-memory",
        mappings=[
            {"param_name": "content", "node_type": "element", "path": "content"},
            {"param_name": "memory_type", "node_type": "attribute", "path": "."},
            {"param_name": "priority", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "context", "node_type": "element", "path": "context", "required": False},
            {"param_name": "metadata", "node_type": "element", "path": "metadata", "required": False}
        ],
        example='''
        <store-memory memory_type="fact" priority="high">
            <content>The user prefers dark mode in all applications.</content>
            <context>{"source": "user_preferences", "conversation_id": "12345"}</context>
        </store-memory>
        '''
    )
    async def store_memory(
        self,
        content: str,
        memory_type: str,
        priority: str = "medium",
        context: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ToolResult:
        """Store a new memory for later recall.
        
        Args:
            content: Content of the memory to store
            memory_type: Type of memory to store
            priority: Priority of the memory
            context: Additional context for the memory
            metadata: Additional metadata for the memory
            
        Returns:
            ToolResult with success status and memory ID
        """
        try:
            # Convert string types to enum values
            memory_type_enum = MemoryType(memory_type)
            priority_enum = MemoryPriority(priority)
            
            # Store the memory
            memory_id = await self.memory_manager.store_memory(
                agent_id=self.agent_id,
                content=content,
                memory_type=memory_type_enum,
                priority=priority_enum,
                context=context or {},
                metadata=metadata or {}
            )
            
            return self.success_response(f"Memory stored successfully with ID: {memory_id}")
            
        except Exception as e:
            logger.error(f"Error storing memory: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to store memory: {str(e)}")
    
    @openapi_schema({
        "name": "retrieve_memory",
        "description": "Retrieve a specific memory by ID",
        "parameters": {
            "type": "object",
            "properties": {
                "memory_id": {
                    "type": "string",
                    "description": "ID of the memory to retrieve"
                }
            },
            "required": ["memory_id"]
        }
    })
    @xml_schema(
        tag_name="retrieve-memory",
        mappings=[
            {"param_name": "memory_id", "node_type": "attribute", "path": "."}
        ],
        example='''
        <retrieve-memory memory_id="550e8400-e29b-41d4-a716-446655440000" />
        '''
    )
    async def retrieve_memory(self, memory_id: str) -> ToolResult:
        """Retrieve a specific memory by ID.
        
        Args:
            memory_id: ID of the memory to retrieve
            
        Returns:
            ToolResult with success status and memory content
        """
        try:
            memory = await self.memory_manager.retrieve_memory(memory_id)
            
            if memory:
                return self.success_response(json.dumps(memory, indent=2))
            else:
                return self.fail_response(f"Memory with ID {memory_id} not found")
                
        except Exception as e:
            logger.error(f"Error retrieving memory: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to retrieve memory: {str(e)}")
    
    @openapi_schema({
        "name": "search_memories",
        "description": "Search for memories based on content and criteria",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query"
                },
                "memory_type": {
                    "type": "string",
                    "enum": ["fact", "interaction", "preference", "skill", "feedback", "goal", "plan"],
                    "description": "Type of memories to search for",
                    "default": None
                },
                "min_priority": {
                    "type": "string",
                    "enum": ["low", "medium", "high", "critical"],
                    "description": "Minimum priority level",
                    "default": None
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of memories to return",
                    "default": 10
                }
            },
            "required": ["query"]
        }
    })
    @xml_schema(
        tag_name="search-memories",
        mappings=[
            {"param_name": "query", "node_type": "element", "path": "query"},
            {"param_name": "memory_type", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "min_priority", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "limit", "node_type": "attribute", "path": ".", "required": False}
        ],
        example='''
        <search-memories memory_type="fact" min_priority="medium" limit="5">
            <query>user preferences</query>
        </search-memories>
        '''
    )
    async def search_memories(
        self,
        query: str,
        memory_type: Optional[str] = None,
        min_priority: Optional[str] = None,
        limit: int = 10
    ) -> ToolResult:
        """Search for memories based on content and criteria.
        
        Args:
            query: Search query
            memory_type: Optional type of memories to search for
            min_priority: Optional minimum priority level
            limit: Maximum number of memories to return
            
        Returns:
            ToolResult with success status and matching memories
        """
        try:
            # Convert string types to enum values if provided
            memory_type_enum = MemoryType(memory_type) if memory_type else None
            min_priority_enum = MemoryPriority(min_priority) if min_priority else None
            
            # Search for memories
            memories = await self.memory_manager.search_memories(
                agent_id=self.agent_id,
                query=query,
                memory_type=memory_type_enum,
                min_priority=min_priority_enum,
                limit=limit
            )
            
            if memories:
                return self.success_response(json.dumps(memories, indent=2))
            else:
                return self.success_response("No matching memories found")
                
        except Exception as e:
            logger.error(f"Error searching memories: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to search memories: {str(e)}")
    
    @openapi_schema({
        "name": "get_recent_memories",
        "description": "Get recent memories for the agent",
        "parameters": {
            "type": "object",
            "properties": {
                "memory_type": {
                    "type": "string",
                    "enum": ["fact", "interaction", "preference", "skill", "feedback", "goal", "plan"],
                    "description": "Type of memories to get",
                    "default": None
                },
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of memories to return",
                    "default": 10
                }
            },
            "required": []
        }
    })
    @xml_schema(
        tag_name="get-recent-memories",
        mappings=[
            {"param_name": "memory_type", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "limit", "node_type": "attribute", "path": ".", "required": False}
        ],
        example='''
        <get-recent-memories memory_type="interaction" limit="5" />
        '''
    )
    async def get_recent_memories(
        self,
        memory_type: Optional[str] = None,
        limit: int = 10
    ) -> ToolResult:
        """Get recent memories for the agent.
        
        Args:
            memory_type: Optional type of memories to get
            limit: Maximum number of memories to return
            
        Returns:
            ToolResult with success status and recent memories
        """
        try:
            # Convert string type to enum value if provided
            memory_type_enum = MemoryType(memory_type) if memory_type else None
            
            # Get recent memories
            memories = await self.memory_manager.get_recent_memories(
                agent_id=self.agent_id,
                memory_type=memory_type_enum,
                limit=limit
            )
            
            if memories:
                return self.success_response(json.dumps(memories, indent=2))
            else:
                return self.success_response("No recent memories found")
                
        except Exception as e:
            logger.error(f"Error getting recent memories: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to get recent memories: {str(e)}")
    
    @openapi_schema({
        "name": "get_important_memories",
        "description": "Get important memories for the agent based on priority",
        "parameters": {
            "type": "object",
            "properties": {
                "limit": {
                    "type": "integer",
                    "description": "Maximum number of memories to return",
                    "default": 10
                }
            },
            "required": []
        }
    })
    @xml_schema(
        tag_name="get-important-memories",
        mappings=[
            {"param_name": "limit", "node_type": "attribute", "path": ".", "required": False}
        ],
        example='''
        <get-important-memories limit="5" />
        '''
    )
    async def get_important_memories(self, limit: int = 10) -> ToolResult:
        """Get important memories for the agent based on priority.
        
        Args:
            limit: Maximum number of memories to return
            
        Returns:
            ToolResult with success status and important memories
        """
        try:
            # Get important memories
            memories = await self.memory_manager.get_important_memories(
                agent_id=self.agent_id,
                limit=limit
            )
            
            if memories:
                return self.success_response(json.dumps(memories, indent=2))
            else:
                return self.success_response("No important memories found")
                
        except Exception as e:
            logger.error(f"Error getting important memories: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to get important memories: {str(e)}")
    
    @openapi_schema({
        "name": "update_memory",
        "description": "Update an existing memory",
        "parameters": {
            "type": "object",
            "properties": {
                "memory_id": {
                    "type": "string",
                    "description": "ID of the memory to update"
                },
                "content": {
                    "type": "string",
                    "description": "New content for the memory",
                    "default": None
                },
                "priority": {
                    "type": "string",
                    "enum": ["low", "medium", "high", "critical"],
                    "description": "New priority for the memory",
                    "default": None
                },
                "context": {
                    "type": "object",
                    "description": "New context for the memory",
                    "default": None
                },
                "metadata": {
                    "type": "object",
                    "description": "New metadata for the memory",
                    "default": None
                }
            },
            "required": ["memory_id"]
        }
    })
    @xml_schema(
        tag_name="update-memory",
        mappings=[
            {"param_name": "memory_id", "node_type": "attribute", "path": "."},
            {"param_name": "content", "node_type": "element", "path": "content", "required": False},
            {"param_name": "priority", "node_type": "attribute", "path": ".", "required": False},
            {"param_name": "context", "node_type": "element", "path": "context", "required": False},
            {"param_name": "metadata", "node_type": "element", "path": "metadata", "required": False}
        ],
        example='''
        <update-memory memory_id="550e8400-e29b-41d4-a716-446655440000" priority="high">
            <content>Updated memory content with new information.</content>
        </update-memory>
        '''
    )
    async def update_memory(
        self,
        memory_id: str,
        content: Optional[str] = None,
        priority: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> ToolResult:
        """Update an existing memory.
        
        Args:
            memory_id: ID of the memory to update
            content: Optional new content
            priority: Optional new priority
            context: Optional new context
            metadata: Optional new metadata
            
        Returns:
            ToolResult with success status
        """
        try:
            # Convert string priority to enum value if provided
            priority_enum = MemoryPriority(priority) if priority else None
            
            # Update the memory
            success = await self.memory_manager.update_memory(
                memory_id=memory_id,
                content=content,
                priority=priority_enum,
                context=context,
                metadata=metadata
            )
            
            if success:
                return self.success_response(f"Memory {memory_id} updated successfully")
            else:
                return self.fail_response(f"Failed to update memory {memory_id}")
                
        except Exception as e:
            logger.error(f"Error updating memory: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to update memory: {str(e)}")
    
    @openapi_schema({
        "name": "delete_memory",
        "description": "Delete a memory",
        "parameters": {
            "type": "object",
            "properties": {
                "memory_id": {
                    "type": "string",
                    "description": "ID of the memory to delete"
                }
            },
            "required": ["memory_id"]
        }
    })
    @xml_schema(
        tag_name="delete-memory",
        mappings=[
            {"param_name": "memory_id", "node_type": "attribute", "path": "."}
        ],
        example='''
        <delete-memory memory_id="550e8400-e29b-41d4-a716-446655440000" />
        '''
    )
    async def delete_memory(self, memory_id: str) -> ToolResult:
        """Delete a memory.
        
        Args:
            memory_id: ID of the memory to delete
            
        Returns:
            ToolResult with success status
        """
        try:
            # Delete the memory
            success = await self.memory_manager.delete_memory(memory_id)
            
            if success:
                return self.success_response(f"Memory {memory_id} deleted successfully")
            else:
                return self.fail_response(f"Failed to delete memory {memory_id}")
                
        except Exception as e:
            logger.error(f"Error deleting memory: {str(e)}", exc_info=True)
            return self.fail_response(f"Failed to delete memory: {str(e)}")
