"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@nextjournal+lang-clojure@1.0.0";
exports.ids = ["vendor-chunks/@nextjournal+lang-clojure@1.0.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@nextjournal+lang-clojure@1.0.0/node_modules/@nextjournal/lang-clojure/dist/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@nextjournal+lang-clojure@1.0.0/node_modules/@nextjournal/lang-clojure/dist/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clojure: () => (/* binding */ clojure),\n/* harmony export */   clojureLanguage: () => (/* binding */ clojureLanguage)\n/* harmony export */ });\n/* harmony import */ var _nextjournal_lezer_clojure__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nextjournal/lezer-clojure */ \"(ssr)/./node_modules/.pnpm/@nextjournal+lezer-clojure@1.0.0/node_modules/@nextjournal/lezer-clojure/dist/index.es.js\");\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n\n\n\n\n/**\nA language provider based on the [Lezer Clojure](https://github.com/nextjournal/lezer-clojure), extended with\nhighlighting and indentation information.\n*/\nconst { coll } = _nextjournal_lezer_clojure__WEBPACK_IMPORTED_MODULE_0__.props;\n// debug\n// const nodeText = (state, node: SyntaxNode) => { return state.doc.sliceString(node.from, node.to) }\nconst clojureLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LRLanguage.define({\n    parser: /*@__PURE__*/_nextjournal_lezer_clojure__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [/*@__PURE__*/(0,_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.styleTags)({ NS: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.keyword,\n                DefLike: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.keyword,\n                \"Operator/Symbol\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.keyword,\n                \"VarName/Symbol\": /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.definition(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName),\n                // Symbol: tags.keyword,\n                // \"'\": tags.keyword, // quote\n                Boolean: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.atom,\n                \"DocString/...\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.emphasis,\n                \"Discard!\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.comment,\n                Number: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.number,\n                StringContent: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string,\n                \"\\\"\\\\\\\"\\\"\": _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string,\n                Keyword: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.atom,\n                Nil: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.null,\n                LineComment: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.lineComment,\n                RegExp: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.regexp }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.indentNodeProp.add((nodeType) => {\n                return (context) => {\n                    let { pos, unit, node, state, baseIndent, textAfter } = context;\n                    if (nodeType.prop(coll)) {\n                        // same behaviour as in clojure-mode: args after operator are always 2-units indented\n                        let parentBase = context.column(node.firstChild.to); // column at the right of parent opening-(\n                        if (\"List\" == nodeType.name && [\"NS\", \"DefLike\", \"Operator\"].includes(node.firstChild.nextSibling.type.name)) {\n                            return parentBase + 1;\n                        }\n                        else {\n                            return parentBase;\n                        }\n                    }\n                    else {\n                        return 0;\n                    }\n                };\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldNodeProp.add({ [\"Vector Map List\"]: _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldInside })]\n    }),\n    languageData: { commentTokens: { line: \";;\" } }\n});\nfunction clojure() {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LanguageSupport(clojureLanguage);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@nextjournal+lang-clojure@1.0.0/node_modules/@nextjournal/lang-clojure/dist/index.js\n");

/***/ })

};
;