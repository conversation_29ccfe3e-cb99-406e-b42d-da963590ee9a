"use client";

import Link from "next/link";
import Image from "next/image";
import { SubmitButton } from "@/components/ui/submit-button";
import { Input } from "@/components/ui/input";
import GoogleSignIn from "@/components/GoogleSignIn";
import { AlertCircle } from "lucide-react";

interface AuthFormProps {
  isSignUp: boolean;
  returnUrl?: string;
  message?: string;
  onSignIn: (prevState: any, formData: FormData) => Promise<any>;
  onSignUp: (prevState: any, formData: FormData) => Promise<any>;
  onForgotPassword: () => void;
}

export function AuthForm({ 
  isSignUp, 
  returnUrl, 
  message, 
  onSignIn, 
  onSignUp, 
  onForgotPassword 
}: AuthFormProps) {
  const isSuccessMessage = message && (
    message.includes("Check your email") ||
    message.includes("Account created") ||
    message.includes("success")
  );

  return (
    <div className="flex min-h-screen w-full bg-[#1a1a1a]">
      {/* Left side - Auth form */}
      <div className="w-full md:w-1/2 flex items-center justify-center p-4 md:p-8">
        <div className="w-full max-w-md">
          <div className="mb-8">
            <Image
              src="/sidenlogomark-white.svg"
              alt="Siden AI"
              width={40}
              height={40}
              className="mb-8"
            />

            <h1 className="text-2xl font-bold text-white mb-2">
              {isSignUp ? "Sign up to Siden" : "Sign in to Siden"}
            </h1>
            <p className="text-gray-400 text-sm">
              {isSignUp ? "Create your account to get started" : "Enter your details to access your account"}
            </p>
          </div>

          {/* Non-registration related messages */}
          {message && !isSuccessMessage && (
            <div className="mb-6 p-4 rounded-md flex items-center gap-3 bg-red-900/20 border border-red-900/30 text-red-400">
              <AlertCircle className="h-5 w-5 flex-shrink-0" />
              <span className="text-sm">{message}</span>
            </div>
          )}

          {/* Email/Password Form */}
          <form className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-400 mb-1">Email ID</label>
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                className="h-12 rounded-md bg-[#222222] border-gray-700 text-white w-full"
                required
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-400 mb-1">Password</label>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="••••••••"
                className="h-12 rounded-md bg-[#222222] border-gray-700 text-white w-full"
                required
              />
            </div>

            {isSignUp && (
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-400 mb-1">Confirm password</label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  placeholder="••••••••"
                  className="h-12 rounded-md bg-[#222222] border-gray-700 text-white w-full"
                  required
                />
              </div>
            )}

            {!isSignUp && (
              <div className="flex items-center justify-between mt-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="remember"
                    className="h-4 w-4 rounded-sm border-gray-700 bg-[#222222]"
                  />
                  <label htmlFor="remember" className="ml-2 text-sm text-gray-400">
                    Remember me
                  </label>
                </div>
                <button
                  type="button"
                  onClick={onForgotPassword}
                  className="text-sm text-blue-500 hover:text-blue-400"
                >
                  Forgot password?
                </button>
              </div>
            )}

            <div className="pt-2">
              {!isSignUp ? (
                <SubmitButton
                  formAction={onSignIn}
                  className="w-full h-12 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-all"
                  pendingText="Signing in..."
                >
                  Login
                </SubmitButton>
              ) : (
                <SubmitButton
                  formAction={onSignUp}
                  className="w-full h-12 rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-all"
                  pendingText="Creating account..."
                >
                  Sign up
                </SubmitButton>
              )}
            </div>
          </form>

          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-700"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-[#1a1a1a] text-gray-400">
                OR
              </span>
            </div>
          </div>

          {/* Google Sign In */}
          <div className="mb-6">
            <GoogleSignIn returnUrl={returnUrl || undefined} />
          </div>

          <div className="mt-8 text-center text-xs text-gray-500">
            {!isSignUp ? (
              <p>
                Don't have an account?{' '}
                <Link href={`/auth?mode=signup${returnUrl ? `&returnUrl=${returnUrl}` : ''}`} className="text-blue-500 hover:text-blue-400">
                  Sign up
                </Link>
              </p>
            ) : (
              <p>
                Already have an account?{' '}
                <Link href={`/auth${returnUrl ? `?returnUrl=${returnUrl}` : ''}`} className="text-blue-500 hover:text-blue-400">
                  Sign in
                </Link>
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Right side - Branding */}
      <div className="hidden md:flex md:w-1/2 p-8 items-center justify-center">
        <div className="w-[90%] h-[90%] rounded-[20px] bg-gradient-to-br from-blue-600 to-blue-800 relative overflow-hidden">
          <div className="absolute inset-0 bg-[url('/logoicons/holographic-pattern.svg')] opacity-10"></div>
          <div className="flex flex-col justify-center h-full px-12 py-10 relative z-10">
            <h2 className="text-4xl font-bold text-white mb-6">Siden</h2>
            <p className="text-xl text-white/90 mb-8 max-w-md">
              Create teams of role-based AI agents that work autonomously with approval options
            </p>

            <div className="absolute bottom-0 right-0 translate-y-[10%] translate-x-[10%]">
              <Image
                src="/Dark.png"
                alt="Siden AI Dashboard"
                width={1125}
                height={675}
                className="rounded-t-lg shadow-2xl border border-white/10"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
