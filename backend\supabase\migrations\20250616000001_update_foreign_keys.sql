-- Simple migration to update only the tables that actually exist
-- Based on the actual database schema: projects and threads

-- First, let's check what account_id columns exist in projects and threads
DO $$
BEGIN
    -- Update projects table if it has account_id column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' AND column_name = 'account_id'
    ) THEN
        -- Add temporary column
        ALTER TABLE projects ADD COLUMN IF NOT EXISTS new_account_id UUID;
        
        -- Migrate data: For each project, find the basejump account owner and create/find personal account
        INSERT INTO public.accounts (user_id, name, type, settings)
        SELECT DISTINCT 
            ba.primary_owner_user_id,
            'Personal Account',
            'personal',
            '{}'::jsonb
        FROM projects p
        JOIN basejump.accounts ba ON p.account_id = ba.id
        WHERE ba.primary_owner_user_id IS NOT NULL
        ON CONFLICT (user_id, type) DO NOTHING;
        
        -- Update projects with new account_id where basejump mapping exists
        UPDATE projects 
        SET new_account_id = pa.id
        FROM basejump.accounts ba
        JOIN public.accounts pa ON ba.primary_owner_user_id = pa.user_id AND pa.type = 'personal'
        WHERE projects.account_id = ba.id;
        
        -- Handle projects with NULL or unmappable account_id
        -- Delete orphaned projects (safest approach)
        DELETE FROM projects WHERE new_account_id IS NULL;
        
        -- Drop existing RLS policies that depend on account_id column
        DROP POLICY IF EXISTS "Users can view their own projects" ON projects;
        DROP POLICY IF EXISTS "Users can insert their own projects" ON projects;
        DROP POLICY IF EXISTS "Users can update their own projects" ON projects;
        DROP POLICY IF EXISTS "Users can delete their own projects" ON projects;
        DROP POLICY IF EXISTS project_select_policy ON projects;
        DROP POLICY IF EXISTS project_insert_policy ON projects;
        DROP POLICY IF EXISTS project_update_policy ON projects;
        DROP POLICY IF EXISTS project_delete_policy ON projects;
        
        -- Drop old constraint and column, rename new column
        ALTER TABLE projects DROP CONSTRAINT IF EXISTS projects_account_id_fkey;
        ALTER TABLE projects DROP COLUMN account_id;
        ALTER TABLE projects RENAME COLUMN new_account_id TO account_id;
        
        -- Add new foreign key constraint
        ALTER TABLE projects 
        ADD CONSTRAINT projects_account_id_fkey 
        FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
        
        -- Make account_id NOT NULL (should work now)
        ALTER TABLE projects ALTER COLUMN account_id SET NOT NULL;
        
        RAISE NOTICE 'Updated projects table to use new accounts system';
    ELSE
        RAISE NOTICE 'Projects table does not have account_id column, skipping';
    END IF;
END $$;

DO $$
BEGIN
    -- Update threads table if it has account_id column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'threads' AND column_name = 'account_id'
    ) THEN
        -- Add temporary column
        ALTER TABLE threads ADD COLUMN IF NOT EXISTS new_account_id UUID;
        
        -- Migrate data for threads where basejump mapping exists
        UPDATE threads 
        SET new_account_id = pa.id
        FROM basejump.accounts ba
        JOIN public.accounts pa ON ba.primary_owner_user_id = pa.user_id AND pa.type = 'personal'
        WHERE threads.account_id = ba.id;
        
        -- Handle threads with NULL or unmappable account_id
        -- Delete orphaned threads (safest approach)
        DELETE FROM threads WHERE new_account_id IS NULL;
        
        -- Drop existing RLS policies that depend on account_id column
        DROP POLICY IF EXISTS "Users can view their own threads" ON threads;
        DROP POLICY IF EXISTS "Users can insert their own threads" ON threads;
        DROP POLICY IF EXISTS "Users can update their own threads" ON threads;
        DROP POLICY IF EXISTS "Users can delete their own threads" ON threads;
        DROP POLICY IF EXISTS thread_select_policy ON threads;
        DROP POLICY IF EXISTS thread_insert_policy ON threads;
        DROP POLICY IF EXISTS thread_update_policy ON threads;
        DROP POLICY IF EXISTS thread_delete_policy ON threads;
        
        -- Drop policies on related tables that depend on threads.account_id
        DROP POLICY IF EXISTS "Users can view messages in their threads" ON messages;
        DROP POLICY IF EXISTS "Users can insert messages in their threads" ON messages;
        DROP POLICY IF EXISTS "Users can update messages in their threads" ON messages;
        DROP POLICY IF EXISTS "Users can delete messages in their threads" ON messages;
        DROP POLICY IF EXISTS "Users can view agent runs for their threads" ON agent_runs;
        DROP POLICY IF EXISTS "Users can insert agent runs for their threads" ON agent_runs;
        DROP POLICY IF EXISTS "Users can update agent runs for their threads" ON agent_runs;
        DROP POLICY IF EXISTS "Users can delete agent runs for their threads" ON agent_runs;
        
        -- Drop old constraint and column, rename new column
        ALTER TABLE threads DROP CONSTRAINT IF EXISTS threads_account_id_fkey;
        ALTER TABLE threads DROP COLUMN account_id;
        ALTER TABLE threads RENAME COLUMN new_account_id TO account_id;
        
        -- Add new foreign key constraint
        ALTER TABLE threads 
        ADD CONSTRAINT threads_account_id_fkey 
        FOREIGN KEY (account_id) REFERENCES public.accounts(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Updated threads table to use new accounts system';
    ELSE
        RAISE NOTICE 'Threads table does not have account_id column, skipping';
    END IF;
END $$;

-- Update RLS policies for projects (if they exist)
DO $$
BEGIN
    -- Drop old policies if they exist
    DROP POLICY IF EXISTS project_select_policy ON projects;
    DROP POLICY IF EXISTS project_insert_policy ON projects;
    DROP POLICY IF EXISTS project_update_policy ON projects;
    
    -- Create new policies for projects
    CREATE POLICY "project_select_policy" ON projects
        FOR SELECT
        USING (
            is_public = true OR
            account_id IN (
                SELECT id FROM public.accounts WHERE user_id = auth.uid()
            ) OR
            account_id IN (
                SELECT account_id FROM public.account_members WHERE user_id = auth.uid()
            )
        );

    CREATE POLICY "project_insert_policy" ON projects
        FOR INSERT
        WITH CHECK (
            account_id IN (
                SELECT id FROM public.accounts WHERE user_id = auth.uid()
            ) OR
            account_id IN (
                SELECT account_id FROM public.account_members 
                WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
            )
        );

    CREATE POLICY "project_update_policy" ON projects
        FOR UPDATE
        USING (
            account_id IN (
                SELECT id FROM public.accounts WHERE user_id = auth.uid()
            ) OR
            account_id IN (
                SELECT account_id FROM public.account_members 
                WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
            )
        );
        
    RAISE NOTICE 'Updated RLS policies for projects';
EXCEPTION
    WHEN undefined_table THEN
        RAISE NOTICE 'Projects table does not exist, skipping RLS policies';
    WHEN OTHERS THEN
        RAISE NOTICE 'Error updating RLS policies for projects: %', SQLERRM;
END $$;

-- Update RLS policies for threads (if they exist)
DO $$
BEGIN
    -- Drop old policies if they exist
    DROP POLICY IF EXISTS thread_select_policy ON threads;
    DROP POLICY IF EXISTS thread_insert_policy ON threads;
    DROP POLICY IF EXISTS thread_update_policy ON threads;
    
    -- Create new policies for threads
    CREATE POLICY "thread_select_policy" ON threads
        FOR SELECT
        USING (
            is_public = true OR
            account_id IN (
                SELECT id FROM public.accounts WHERE user_id = auth.uid()
            ) OR
            account_id IN (
                SELECT account_id FROM public.account_members WHERE user_id = auth.uid()
            )
        );

    CREATE POLICY "thread_insert_policy" ON threads
        FOR INSERT
        WITH CHECK (
            account_id IN (
                SELECT id FROM public.accounts WHERE user_id = auth.uid()
            ) OR
            account_id IN (
                SELECT account_id FROM public.account_members WHERE user_id = auth.uid()
            )
        );

    CREATE POLICY "thread_update_policy" ON threads
        FOR UPDATE
        USING (
            account_id IN (
                SELECT id FROM public.accounts WHERE user_id = auth.uid()
            ) OR
            account_id IN (
                SELECT account_id FROM public.account_members WHERE user_id = auth.uid()
            )
        );
        
    RAISE NOTICE 'Updated RLS policies for threads';
EXCEPTION
    WHEN undefined_table THEN
        RAISE NOTICE 'Threads table does not exist, skipping RLS policies';
    WHEN OTHERS THEN
        RAISE NOTICE 'Error updating RLS policies for threads: %', SQLERRM;
END $$;
