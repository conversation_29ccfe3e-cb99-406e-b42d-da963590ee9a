"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON><PERSON><PERSON>, ArrowRight, Folder, <PERSON>, Settings, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ProjectDetails } from './project-details';
import { AgentRoleSelection } from './agent-roles';
import { ConfigureTeam } from './configure-team';
import { createProject } from '@/lib/api';
import { toast } from 'sonner';
import Link from 'next/link';

// Import agent roles data
import { agentRoles } from './agent-roles-data';

// Steps in the project creation process
const steps = [
  "Project Details",
  "Select Agent Roles",
  "Configure Team"
];

export default function NewProjectPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Helper function to get breadcrumb text based on current step
  const getBreadcrumb = () => {
    return steps[currentStep];
  };
  
  // Helper function to get the appropriate icon and text for the current step
  const getStepInfo = () => {
    switch (currentStep) {
      case 0:
        return {
          icon: <FileText className="h-8 w-8 text-primary" />,
          title: "Project Details",
          description: "Set Up Your Project Details So Your AI Team Can Understand What You're Building"
        };
      case 1:
        return {
          icon: <Users className="h-8 w-8 text-primary" />,
          title: "Select Agent Roles",
          description: "Choose Which AI Agents You'd Like On Your Team"
        };
      case 2:
        return {
          icon: <Settings className="h-8 w-8 text-primary" />,
          title: "Configure Team",
          description: "Set Up How Your AI Team Will Work Together"
        };
      default:
        return {
          icon: <Folder className="h-8 w-8 text-primary" />,
          title: "Create Project",
          description: "Set Up Your Project Details So Your AI Team Can Understand What You're Building"
        };
    }
  };

  // Project data state
  const [projectData, setProjectData] = useState({
    name: '',
    description: '',
    companyInfo: '',
    templateId: '',
    selectedAgents: [],
    integrations: [],
    settings: {
      knowledgeAccess: true,
      memoryPersistence: true,
      autonomousMode: false,
      sandboxMode: false
    }
  });

  const updateProjectData = (data: Partial<typeof projectData>) => {
    setProjectData(prev => ({ ...prev, ...data }));
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      window.scrollTo(0, 0);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      window.scrollTo(0, 0);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!projectData.name) {
        toast.error('Project name is required');
        setIsSubmitting(false);
        return;
      }

      // Get the agent names from the selected agent IDs
      const selectedAgentNames = projectData.selectedAgents.map(agentId => {
        const agent = agentRoles.find(a => a.id === agentId);
        return agent ? agent.name : '';
      }).filter(name => name); // Filter out any empty names

      console.log('Selected agent names:', selectedAgentNames);
      console.log('Creating project with data:', {
        name: projectData.name,
        description: projectData.description,
        companyInfo: projectData.companyInfo,
        selectedAgents: projectData.selectedAgents,
        settings: projectData.settings
      });

      // Create the project with all settings
      const newProject = await createProject({
        name: projectData.name,
        description: projectData.description,
        companyInfo: projectData.companyInfo,
        selectedAgents: projectData.selectedAgents,
        settings: projectData.settings
      });

      console.log('Project created successfully:', newProject);
      console.log('Selected agents stored in DB:', selectedAgentNames);

      // Redirect directly to the chat page - no need for URL parameters
      // as the agents are already stored in the project data
      const chatUrl = `/project/${newProject.id}/chat`;

      toast.success('Project created successfully');
      router.push(chatUrl);
    } catch (error: any) {
      console.error('Error creating project:', error);

      // Provide more specific error messages
      if (error.message) {
        toast.error(`Failed to create project: ${error.message}`);
      } else if (error.error_description) {
        toast.error(`Failed to create project: ${error.error_description}`);
      } else {
        toast.error('Failed to create project. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <ProjectDetails
            data={projectData}
            updateData={updateProjectData}
          />
        );
      case 1:
        return (
          <AgentRoleSelection
            data={projectData}
            updateData={updateProjectData}
          />
        );
      case 2:
        return (
          <ConfigureTeam
            data={projectData}
            updateData={updateProjectData}
          />
        );
      default:
        return null;
    }
  };

  const isLastStep = currentStep === steps.length - 1;

  return (
    <div className="w-full min-h-screen bg-background pb-24">
      {/* Breadcrumb navigation */}
      <div className="py-5 px-6 border-b border-border">
        <div className="container mx-auto max-w-7xl">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Link href="/" className="hover:text-white transition-colors">Home</Link>
            <span>/</span>
            <Link href="/project/new" className="hover:text-white transition-colors">Create Project</Link>
            <span>/</span>
            <span className="text-white">{getBreadcrumb()}</span>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto py-12 max-w-7xl">
        {/* Project header with icon in a container with rounded corners */}
        <div className="mb-8 border border-border rounded-md p-6">
          <div className="flex gap-4">
            <div>
              <div className="bg-[#222222] p-4 rounded-md mb-4">
                {getStepInfo().icon}
              </div>
            </div>
            <div>
              <h1 className="text-2xl font-bold mb-2">{getStepInfo().title}</h1>
              <p className="text-muted-foreground">
                {getStepInfo().description}
              </p>
            </div>
          </div>
          
          {/* Progress indicator */}
          <div className="mt-6">
            <div className="flex items-center">
              <div className="flex items-center">
                <div className="h-8 w-8 rounded-full bg-primary/20 flex items-center justify-center">
                  <div className="h-6 w-6 rounded-full border-2 border-primary flex items-center justify-center">
                    <span className="text-primary text-sm">{currentStep + 1}</span>
                  </div>
                </div>
                <div className="text-primary font-medium ml-2">Step {currentStep + 1} / {steps.length}</div>
              </div>
            </div>
          </div>
        </div>

        <div className="mb-6">
          {renderStep()}
        </div>

        <div className="flex justify-between mt-8 fixed bottom-0  bg-background py-6 container mx-auto max-w-7xl">
          <Button
            variant="outline"
            onClick={handleBack}
            disabled={currentStep === 0}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>

          {isLastStep ? (
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Creating...' : 'Create Project'}
              {!isSubmitting && <ArrowRight className="ml-2 h-4 w-4" />}
            </Button>
          ) : (
            <Button
              onClick={handleNext}
            >
              Continue
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
