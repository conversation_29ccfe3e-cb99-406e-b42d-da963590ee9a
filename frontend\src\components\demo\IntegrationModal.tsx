'use client';

import React, { useState } from 'react';
import { X, Eye, EyeOff } from 'lucide-react';

interface IntegrationModalProps {
  isOpen: boolean;
  onClose: () => void;
  integration: {
    id: string;
    name: string;
    description: string;
    iconSrc: string;
    category: string;
  } | null;
}

export function IntegrationModal({ isOpen, onClose, integration }: IntegrationModalProps) {
  const [isConnecting, setIsConnecting] = useState(false);
  const [showManualLogin, setShowManualLogin] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  if (!isOpen || !integration) return null;

  const handleAllow = async () => {
    setIsConnecting(true);
    // Simulate OAuth flow
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsConnecting(false);
    onClose();
  };

  const handleManualConnect = async () => {
    if (!email || !password) return;
    setIsConnecting(true);
    // Simulate manual login
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsConnecting(false);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-4">
      <div className="bg-card border border-border rounded-md max-w-md w-full shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-lg font-medium text-foreground">
            Connect {integration.name}
          </h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-accent rounded-md transition-colors"
          >
            <X className="h-4 w-4 text-muted-foreground" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Icons */}
          <div className="flex items-center justify-center gap-4 mb-6">
            <div className="w-14 h-14 rounded-lg bg-muted border border-border flex items-center justify-center">
              <img src={integration.iconSrc} alt={integration.name} className="w-7 h-7" />
            </div>
            <div className="w-6 h-0.5 bg-border"></div>
            <div className="w-14 h-14 rounded-lg bg-muted border border-border flex items-center justify-center">
              <svg width="28" height="32" viewBox="0 0 400 468" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M182.465 3.40986C217.468 -6.39618 248.999 5.17278 269.532 35.2138C286.115 59.4753 297.523 86.5768 311.126 112.469C334.55 157.054 358.043 201.606 381.023 246.418C391.633 267.107 399.712 289.074 399.99 312.524C400.56 360.666 376.6 396.008 338.151 422.204C306.613 443.692 270.492 451.59 232.919 453.849C231.992 453.905 231.029 453.347 229.583 452.932C230.768 445.359 236.523 440.606 240.252 434.821C271.351 386.563 290.902 334.678 288.263 276.431C286.823 244.65 278.324 214.574 257.761 189.363C232.179 158 188.25 161.104 167.694 196.073C149.415 227.168 150.137 259.003 168.762 289.894C175.062 300.345 182.587 310.139 190.242 319.679C212.827 347.824 219.438 378.782 204.782 412.149C190.143 445.48 163.203 462.994 127.154 467.228C63.364 474.721 12.5693 426.56 2.23663 371.241C-3.75703 339.153 2.78958 309.076 15.9814 280.052C40.3641 226.405 70.7511 175.829 96.7986 123.024C110.649 94.9454 124.599 66.9049 140.422 39.8515C150.158 23.2061 162.798 9.89958 182.465 3.40986Z" fill="currentColor"/>
              </svg>
            </div>
          </div>

          {/* Title and Description */}
          <div className="text-center mb-6">
            <h3 className="text-lg font-medium text-foreground mb-2">
              Connect {integration.name} to your workspace
            </h3>
            <p className="text-muted-foreground text-sm">
              Choose how you'd like to connect your {integration.name} account
            </p>
          </div>

          {!showManualLogin ? (
            /* OAuth Flow */
            <div className="space-y-4">
              <button
                onClick={() => setShowManualLogin(true)}
                className="w-full py-3 px-4 border border-border text-muted-foreground rounded-md font-medium hover:bg-accent hover:text-foreground transition-colors"
              >
                Manual Login
              </button>

              <button
                onClick={handleAllow}
                disabled={isConnecting}
                className="w-full py-3 px-4 bg-primary text-primary-foreground rounded-md font-medium hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isConnecting ? 'Connecting...' : 'Allow'}
              </button>

              <p className="text-xs text-muted-foreground text-center mt-4">
                By clicking Allow, you'll be redirected to {integration.name} to authorize the connection.
              </p>
            </div>
          ) : (
            /* Manual Login Form */
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Email address
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full px-3 py-2.5 border border-border rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Password
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="••••••••"
                    className="w-full px-3 py-2.5 pr-10 border border-border rounded-md bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 p-1 text-muted-foreground hover:text-foreground"
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </button>
                </div>
              </div>

              <div className="flex gap-3 pt-2">
                <button
                  onClick={handleManualConnect}
                  disabled={!email || !password || isConnecting}
                  className="flex-1 py-3 px-4 bg-primary text-primary-foreground rounded-md font-medium hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isConnecting ? 'Connecting...' : 'Connect'}
                </button>
                <button
                  onClick={() => setShowManualLogin(false)}
                  className="px-4 py-3 text-muted-foreground hover:text-foreground font-medium transition-colors"
                >
                  Back
                </button>
              </div>

              <p className="text-xs text-muted-foreground text-center mt-4">
                Your credentials are encrypted and stored securely.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
