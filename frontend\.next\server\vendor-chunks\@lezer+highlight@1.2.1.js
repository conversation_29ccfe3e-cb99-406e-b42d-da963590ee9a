"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lezer+highlight@1.2.1";
exports.ids = ["vendor-chunks/@lezer+highlight@1.2.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tag: () => (/* binding */ Tag),\n/* harmony export */   classHighlighter: () => (/* binding */ classHighlighter),\n/* harmony export */   getStyleTags: () => (/* binding */ getStyleTags),\n/* harmony export */   highlightCode: () => (/* binding */ highlightCode),\n/* harmony export */   highlightTree: () => (/* binding */ highlightTree),\n/* harmony export */   styleTags: () => (/* binding */ styleTags),\n/* harmony export */   tagHighlighter: () => (/* binding */ tagHighlighter),\n/* harmony export */   tags: () => (/* binding */ tags)\n/* harmony export */ });\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n\n\nlet nextTagID = 0;\n/**\nHighlighting tags are markers that denote a highlighting category.\nThey are [associated](#highlight.styleTags) with parts of a syntax\ntree by a language mode, and then mapped to an actual CSS style by\na [highlighter](#highlight.Highlighter).\n\nBecause syntax tree node types and highlight styles have to be\nable to talk the same language, CodeMirror uses a mostly _closed_\n[vocabulary](#highlight.tags) of syntax tags (as opposed to\ntraditional open string-based systems, which make it hard for\nhighlighting themes to cover all the tokens produced by the\nvarious languages).\n\nIt _is_ possible to [define](#highlight.Tag^define) your own\nhighlighting tags for system-internal use (where you control both\nthe language package and the highlighter), but such tags will not\nbe picked up by regular highlighters (though you can derive them\nfrom standard tags to allow highlighters to fall back to those).\n*/\nclass Tag {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The optional name of the base tag @internal\n    */\n    name, \n    /**\n    The set of this tag and all its parent tags, starting with\n    this one itself and sorted in order of decreasing specificity.\n    */\n    set, \n    /**\n    The base unmodified tag that this one is based on, if it's\n    modified @internal\n    */\n    base, \n    /**\n    The modifiers applied to this.base @internal\n    */\n    modified) {\n        this.name = name;\n        this.set = set;\n        this.base = base;\n        this.modified = modified;\n        /**\n        @internal\n        */\n        this.id = nextTagID++;\n    }\n    toString() {\n        let { name } = this;\n        for (let mod of this.modified)\n            if (mod.name)\n                name = `${mod.name}(${name})`;\n        return name;\n    }\n    static define(nameOrParent, parent) {\n        let name = typeof nameOrParent == \"string\" ? nameOrParent : \"?\";\n        if (nameOrParent instanceof Tag)\n            parent = nameOrParent;\n        if (parent === null || parent === void 0 ? void 0 : parent.base)\n            throw new Error(\"Can not derive from a modified tag\");\n        let tag = new Tag(name, [], null, []);\n        tag.set.push(tag);\n        if (parent)\n            for (let t of parent.set)\n                tag.set.push(t);\n        return tag;\n    }\n    /**\n    Define a tag _modifier_, which is a function that, given a tag,\n    will return a tag that is a subtag of the original. Applying the\n    same modifier to a twice tag will return the same value (`m1(t1)\n    == m1(t1)`) and applying multiple modifiers will, regardless or\n    order, produce the same tag (`m1(m2(t1)) == m2(m1(t1))`).\n    \n    When multiple modifiers are applied to a given base tag, each\n    smaller set of modifiers is registered as a parent, so that for\n    example `m1(m2(m3(t1)))` is a subtype of `m1(m2(t1))`,\n    `m1(m3(t1)`, and so on.\n    */\n    static defineModifier(name) {\n        let mod = new Modifier(name);\n        return (tag) => {\n            if (tag.modified.indexOf(mod) > -1)\n                return tag;\n            return Modifier.get(tag.base || tag, tag.modified.concat(mod).sort((a, b) => a.id - b.id));\n        };\n    }\n}\nlet nextModifierID = 0;\nclass Modifier {\n    constructor(name) {\n        this.name = name;\n        this.instances = [];\n        this.id = nextModifierID++;\n    }\n    static get(base, mods) {\n        if (!mods.length)\n            return base;\n        let exists = mods[0].instances.find(t => t.base == base && sameArray(mods, t.modified));\n        if (exists)\n            return exists;\n        let set = [], tag = new Tag(base.name, set, base, mods);\n        for (let m of mods)\n            m.instances.push(tag);\n        let configs = powerSet(mods);\n        for (let parent of base.set)\n            if (!parent.modified.length)\n                for (let config of configs)\n                    set.push(Modifier.get(parent, config));\n        return tag;\n    }\n}\nfunction sameArray(a, b) {\n    return a.length == b.length && a.every((x, i) => x == b[i]);\n}\nfunction powerSet(array) {\n    let sets = [[]];\n    for (let i = 0; i < array.length; i++) {\n        for (let j = 0, e = sets.length; j < e; j++) {\n            sets.push(sets[j].concat(array[i]));\n        }\n    }\n    return sets.sort((a, b) => b.length - a.length);\n}\n/**\nThis function is used to add a set of tags to a language syntax\nvia [`NodeSet.extend`](#common.NodeSet.extend) or\n[`LRParser.configure`](#lr.LRParser.configure).\n\nThe argument object maps node selectors to [highlighting\ntags](#highlight.Tag) or arrays of tags.\n\nNode selectors may hold one or more (space-separated) node paths.\nSuch a path can be a [node name](#common.NodeType.name), or\nmultiple node names (or `*` wildcards) separated by slash\ncharacters, as in `\"Block/Declaration/VariableName\"`. Such a path\nmatches the final node but only if its direct parent nodes are the\nother nodes mentioned. A `*` in such a path matches any parent,\nbut only a single level—wildcards that match multiple parents\naren't supported, both for efficiency reasons and because Lezer\ntrees make it rather hard to reason about what they would match.)\n\nA path can be ended with `/...` to indicate that the tag assigned\nto the node should also apply to all child nodes, even if they\nmatch their own style (by default, only the innermost style is\nused).\n\nWhen a path ends in `!`, as in `Attribute!`, no further matching\nhappens for the node's child nodes, and the entire node gets the\ngiven style.\n\nIn this notation, node names that contain `/`, `!`, `*`, or `...`\nmust be quoted as JSON strings.\n\nFor example:\n\n```javascript\nparser.withProps(\n  styleTags({\n    // Style Number and BigNumber nodes\n    \"Number BigNumber\": tags.number,\n    // Style Escape nodes whose parent is String\n    \"String/Escape\": tags.escape,\n    // Style anything inside Attributes nodes\n    \"Attributes!\": tags.meta,\n    // Add a style to all content inside Italic nodes\n    \"Italic/...\": tags.emphasis,\n    // Style InvalidString nodes as both `string` and `invalid`\n    \"InvalidString\": [tags.string, tags.invalid],\n    // Style the node named \"/\" as punctuation\n    '\"/\"': tags.punctuation\n  })\n)\n```\n*/\nfunction styleTags(spec) {\n    let byName = Object.create(null);\n    for (let prop in spec) {\n        let tags = spec[prop];\n        if (!Array.isArray(tags))\n            tags = [tags];\n        for (let part of prop.split(\" \"))\n            if (part) {\n                let pieces = [], mode = 2 /* Mode.Normal */, rest = part;\n                for (let pos = 0;;) {\n                    if (rest == \"...\" && pos > 0 && pos + 3 == part.length) {\n                        mode = 1 /* Mode.Inherit */;\n                        break;\n                    }\n                    let m = /^\"(?:[^\"\\\\]|\\\\.)*?\"|[^\\/!]+/.exec(rest);\n                    if (!m)\n                        throw new RangeError(\"Invalid path: \" + part);\n                    pieces.push(m[0] == \"*\" ? \"\" : m[0][0] == '\"' ? JSON.parse(m[0]) : m[0]);\n                    pos += m[0].length;\n                    if (pos == part.length)\n                        break;\n                    let next = part[pos++];\n                    if (pos == part.length && next == \"!\") {\n                        mode = 0 /* Mode.Opaque */;\n                        break;\n                    }\n                    if (next != \"/\")\n                        throw new RangeError(\"Invalid path: \" + part);\n                    rest = part.slice(pos);\n                }\n                let last = pieces.length - 1, inner = pieces[last];\n                if (!inner)\n                    throw new RangeError(\"Invalid path: \" + part);\n                let rule = new Rule(tags, mode, last > 0 ? pieces.slice(0, last) : null);\n                byName[inner] = rule.sort(byName[inner]);\n            }\n    }\n    return ruleNodeProp.add(byName);\n}\nconst ruleNodeProp = new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp();\nclass Rule {\n    constructor(tags, mode, context, next) {\n        this.tags = tags;\n        this.mode = mode;\n        this.context = context;\n        this.next = next;\n    }\n    get opaque() { return this.mode == 0 /* Mode.Opaque */; }\n    get inherit() { return this.mode == 1 /* Mode.Inherit */; }\n    sort(other) {\n        if (!other || other.depth < this.depth) {\n            this.next = other;\n            return this;\n        }\n        other.next = this.sort(other.next);\n        return other;\n    }\n    get depth() { return this.context ? this.context.length : 0; }\n}\nRule.empty = new Rule([], 2 /* Mode.Normal */, null);\n/**\nDefine a [highlighter](#highlight.Highlighter) from an array of\ntag/class pairs. Classes associated with more specific tags will\ntake precedence.\n*/\nfunction tagHighlighter(tags, options) {\n    let map = Object.create(null);\n    for (let style of tags) {\n        if (!Array.isArray(style.tag))\n            map[style.tag.id] = style.class;\n        else\n            for (let tag of style.tag)\n                map[tag.id] = style.class;\n    }\n    let { scope, all = null } = options || {};\n    return {\n        style: (tags) => {\n            let cls = all;\n            for (let tag of tags) {\n                for (let sub of tag.set) {\n                    let tagClass = map[sub.id];\n                    if (tagClass) {\n                        cls = cls ? cls + \" \" + tagClass : tagClass;\n                        break;\n                    }\n                }\n            }\n            return cls;\n        },\n        scope\n    };\n}\nfunction highlightTags(highlighters, tags) {\n    let result = null;\n    for (let highlighter of highlighters) {\n        let value = highlighter.style(tags);\n        if (value)\n            result = result ? result + \" \" + value : value;\n    }\n    return result;\n}\n/**\nHighlight the given [tree](#common.Tree) with the given\n[highlighter](#highlight.Highlighter). Often, the higher-level\n[`highlightCode`](#highlight.highlightCode) function is easier to\nuse.\n*/\nfunction highlightTree(tree, highlighter, \n/**\nAssign styling to a region of the text. Will be called, in order\nof position, for any ranges where more than zero classes apply.\n`classes` is a space separated string of CSS classes.\n*/\nputStyle, \n/**\nThe start of the range to highlight.\n*/\nfrom = 0, \n/**\nThe end of the range.\n*/\nto = tree.length) {\n    let builder = new HighlightBuilder(from, Array.isArray(highlighter) ? highlighter : [highlighter], putStyle);\n    builder.highlightRange(tree.cursor(), from, to, \"\", builder.highlighters);\n    builder.flush(to);\n}\n/**\nHighlight the given tree with the given highlighter, calling\n`putText` for every piece of text, either with a set of classes or\nwith the empty string when unstyled, and `putBreak` for every line\nbreak.\n*/\nfunction highlightCode(code, tree, highlighter, putText, putBreak, from = 0, to = code.length) {\n    let pos = from;\n    function writeTo(p, classes) {\n        if (p <= pos)\n            return;\n        for (let text = code.slice(pos, p), i = 0;;) {\n            let nextBreak = text.indexOf(\"\\n\", i);\n            let upto = nextBreak < 0 ? text.length : nextBreak;\n            if (upto > i)\n                putText(text.slice(i, upto), classes);\n            if (nextBreak < 0)\n                break;\n            putBreak();\n            i = nextBreak + 1;\n        }\n        pos = p;\n    }\n    highlightTree(tree, highlighter, (from, to, classes) => {\n        writeTo(from, \"\");\n        writeTo(to, classes);\n    }, from, to);\n    writeTo(to, \"\");\n}\nclass HighlightBuilder {\n    constructor(at, highlighters, span) {\n        this.at = at;\n        this.highlighters = highlighters;\n        this.span = span;\n        this.class = \"\";\n    }\n    startSpan(at, cls) {\n        if (cls != this.class) {\n            this.flush(at);\n            if (at > this.at)\n                this.at = at;\n            this.class = cls;\n        }\n    }\n    flush(to) {\n        if (to > this.at && this.class)\n            this.span(this.at, to, this.class);\n    }\n    highlightRange(cursor, from, to, inheritedClass, highlighters) {\n        let { type, from: start, to: end } = cursor;\n        if (start >= to || end <= from)\n            return;\n        if (type.isTop)\n            highlighters = this.highlighters.filter(h => !h.scope || h.scope(type));\n        let cls = inheritedClass;\n        let rule = getStyleTags(cursor) || Rule.empty;\n        let tagCls = highlightTags(highlighters, rule.tags);\n        if (tagCls) {\n            if (cls)\n                cls += \" \";\n            cls += tagCls;\n            if (rule.mode == 1 /* Mode.Inherit */)\n                inheritedClass += (inheritedClass ? \" \" : \"\") + tagCls;\n        }\n        this.startSpan(Math.max(from, start), cls);\n        if (rule.opaque)\n            return;\n        let mounted = cursor.tree && cursor.tree.prop(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.mounted);\n        if (mounted && mounted.overlay) {\n            let inner = cursor.node.enter(mounted.overlay[0].from + start, 1);\n            let innerHighlighters = this.highlighters.filter(h => !h.scope || h.scope(mounted.tree.type));\n            let hasChild = cursor.firstChild();\n            for (let i = 0, pos = start;; i++) {\n                let next = i < mounted.overlay.length ? mounted.overlay[i] : null;\n                let nextPos = next ? next.from + start : end;\n                let rangeFrom = Math.max(from, pos), rangeTo = Math.min(to, nextPos);\n                if (rangeFrom < rangeTo && hasChild) {\n                    while (cursor.from < rangeTo) {\n                        this.highlightRange(cursor, rangeFrom, rangeTo, inheritedClass, highlighters);\n                        this.startSpan(Math.min(rangeTo, cursor.to), cls);\n                        if (cursor.to >= nextPos || !cursor.nextSibling())\n                            break;\n                    }\n                }\n                if (!next || nextPos > to)\n                    break;\n                pos = next.to + start;\n                if (pos > from) {\n                    this.highlightRange(inner.cursor(), Math.max(from, next.from + start), Math.min(to, pos), \"\", innerHighlighters);\n                    this.startSpan(Math.min(to, pos), cls);\n                }\n            }\n            if (hasChild)\n                cursor.parent();\n        }\n        else if (cursor.firstChild()) {\n            if (mounted)\n                inheritedClass = \"\";\n            do {\n                if (cursor.to <= from)\n                    continue;\n                if (cursor.from >= to)\n                    break;\n                this.highlightRange(cursor, from, to, inheritedClass, highlighters);\n                this.startSpan(Math.min(to, cursor.to), cls);\n            } while (cursor.nextSibling());\n            cursor.parent();\n        }\n    }\n}\n/**\nMatch a syntax node's [highlight rules](#highlight.styleTags). If\nthere's a match, return its set of tags, and whether it is\nopaque (uses a `!`) or applies to all child nodes (`/...`).\n*/\nfunction getStyleTags(node) {\n    let rule = node.type.prop(ruleNodeProp);\n    while (rule && rule.context && !node.matchContext(rule.context))\n        rule = rule.next;\n    return rule || null;\n}\nconst t = Tag.define;\nconst comment = t(), name = t(), typeName = t(name), propertyName = t(name), literal = t(), string = t(literal), number = t(literal), content = t(), heading = t(content), keyword = t(), operator = t(), punctuation = t(), bracket = t(punctuation), meta = t();\n/**\nThe default set of highlighting [tags](#highlight.Tag).\n\nThis collection is heavily biased towards programming languages,\nand necessarily incomplete. A full ontology of syntactic\nconstructs would fill a stack of books, and be impractical to\nwrite themes for. So try to make do with this set. If all else\nfails, [open an\nissue](https://github.com/codemirror/codemirror.next) to propose a\nnew tag, or [define](#highlight.Tag^define) a local custom tag for\nyour use case.\n\nNote that it is not obligatory to always attach the most specific\ntag possible to an element—if your grammar can't easily\ndistinguish a certain type of element (such as a local variable),\nit is okay to style it as its more general variant (a variable).\n\nFor tags that extend some parent tag, the documentation links to\nthe parent.\n*/\nconst tags = {\n    /**\n    A comment.\n    */\n    comment,\n    /**\n    A line [comment](#highlight.tags.comment).\n    */\n    lineComment: t(comment),\n    /**\n    A block [comment](#highlight.tags.comment).\n    */\n    blockComment: t(comment),\n    /**\n    A documentation [comment](#highlight.tags.comment).\n    */\n    docComment: t(comment),\n    /**\n    Any kind of identifier.\n    */\n    name,\n    /**\n    The [name](#highlight.tags.name) of a variable.\n    */\n    variableName: t(name),\n    /**\n    A type [name](#highlight.tags.name).\n    */\n    typeName: typeName,\n    /**\n    A tag name (subtag of [`typeName`](#highlight.tags.typeName)).\n    */\n    tagName: t(typeName),\n    /**\n    A property or field [name](#highlight.tags.name).\n    */\n    propertyName: propertyName,\n    /**\n    An attribute name (subtag of [`propertyName`](#highlight.tags.propertyName)).\n    */\n    attributeName: t(propertyName),\n    /**\n    The [name](#highlight.tags.name) of a class.\n    */\n    className: t(name),\n    /**\n    A label [name](#highlight.tags.name).\n    */\n    labelName: t(name),\n    /**\n    A namespace [name](#highlight.tags.name).\n    */\n    namespace: t(name),\n    /**\n    The [name](#highlight.tags.name) of a macro.\n    */\n    macroName: t(name),\n    /**\n    A literal value.\n    */\n    literal,\n    /**\n    A string [literal](#highlight.tags.literal).\n    */\n    string,\n    /**\n    A documentation [string](#highlight.tags.string).\n    */\n    docString: t(string),\n    /**\n    A character literal (subtag of [string](#highlight.tags.string)).\n    */\n    character: t(string),\n    /**\n    An attribute value (subtag of [string](#highlight.tags.string)).\n    */\n    attributeValue: t(string),\n    /**\n    A number [literal](#highlight.tags.literal).\n    */\n    number,\n    /**\n    An integer [number](#highlight.tags.number) literal.\n    */\n    integer: t(number),\n    /**\n    A floating-point [number](#highlight.tags.number) literal.\n    */\n    float: t(number),\n    /**\n    A boolean [literal](#highlight.tags.literal).\n    */\n    bool: t(literal),\n    /**\n    Regular expression [literal](#highlight.tags.literal).\n    */\n    regexp: t(literal),\n    /**\n    An escape [literal](#highlight.tags.literal), for example a\n    backslash escape in a string.\n    */\n    escape: t(literal),\n    /**\n    A color [literal](#highlight.tags.literal).\n    */\n    color: t(literal),\n    /**\n    A URL [literal](#highlight.tags.literal).\n    */\n    url: t(literal),\n    /**\n    A language keyword.\n    */\n    keyword,\n    /**\n    The [keyword](#highlight.tags.keyword) for the self or this\n    object.\n    */\n    self: t(keyword),\n    /**\n    The [keyword](#highlight.tags.keyword) for null.\n    */\n    null: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) denoting some atomic value.\n    */\n    atom: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) that represents a unit.\n    */\n    unit: t(keyword),\n    /**\n    A modifier [keyword](#highlight.tags.keyword).\n    */\n    modifier: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) that acts as an operator.\n    */\n    operatorKeyword: t(keyword),\n    /**\n    A control-flow related [keyword](#highlight.tags.keyword).\n    */\n    controlKeyword: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) that defines something.\n    */\n    definitionKeyword: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) related to defining or\n    interfacing with modules.\n    */\n    moduleKeyword: t(keyword),\n    /**\n    An operator.\n    */\n    operator,\n    /**\n    An [operator](#highlight.tags.operator) that dereferences something.\n    */\n    derefOperator: t(operator),\n    /**\n    Arithmetic-related [operator](#highlight.tags.operator).\n    */\n    arithmeticOperator: t(operator),\n    /**\n    Logical [operator](#highlight.tags.operator).\n    */\n    logicOperator: t(operator),\n    /**\n    Bit [operator](#highlight.tags.operator).\n    */\n    bitwiseOperator: t(operator),\n    /**\n    Comparison [operator](#highlight.tags.operator).\n    */\n    compareOperator: t(operator),\n    /**\n    [Operator](#highlight.tags.operator) that updates its operand.\n    */\n    updateOperator: t(operator),\n    /**\n    [Operator](#highlight.tags.operator) that defines something.\n    */\n    definitionOperator: t(operator),\n    /**\n    Type-related [operator](#highlight.tags.operator).\n    */\n    typeOperator: t(operator),\n    /**\n    Control-flow [operator](#highlight.tags.operator).\n    */\n    controlOperator: t(operator),\n    /**\n    Program or markup punctuation.\n    */\n    punctuation,\n    /**\n    [Punctuation](#highlight.tags.punctuation) that separates\n    things.\n    */\n    separator: t(punctuation),\n    /**\n    Bracket-style [punctuation](#highlight.tags.punctuation).\n    */\n    bracket,\n    /**\n    Angle [brackets](#highlight.tags.bracket) (usually `<` and `>`\n    tokens).\n    */\n    angleBracket: t(bracket),\n    /**\n    Square [brackets](#highlight.tags.bracket) (usually `[` and `]`\n    tokens).\n    */\n    squareBracket: t(bracket),\n    /**\n    Parentheses (usually `(` and `)` tokens). Subtag of\n    [bracket](#highlight.tags.bracket).\n    */\n    paren: t(bracket),\n    /**\n    Braces (usually `{` and `}` tokens). Subtag of\n    [bracket](#highlight.tags.bracket).\n    */\n    brace: t(bracket),\n    /**\n    Content, for example plain text in XML or markup documents.\n    */\n    content,\n    /**\n    [Content](#highlight.tags.content) that represents a heading.\n    */\n    heading,\n    /**\n    A level 1 [heading](#highlight.tags.heading).\n    */\n    heading1: t(heading),\n    /**\n    A level 2 [heading](#highlight.tags.heading).\n    */\n    heading2: t(heading),\n    /**\n    A level 3 [heading](#highlight.tags.heading).\n    */\n    heading3: t(heading),\n    /**\n    A level 4 [heading](#highlight.tags.heading).\n    */\n    heading4: t(heading),\n    /**\n    A level 5 [heading](#highlight.tags.heading).\n    */\n    heading5: t(heading),\n    /**\n    A level 6 [heading](#highlight.tags.heading).\n    */\n    heading6: t(heading),\n    /**\n    A prose [content](#highlight.tags.content) separator (such as a horizontal rule).\n    */\n    contentSeparator: t(content),\n    /**\n    [Content](#highlight.tags.content) that represents a list.\n    */\n    list: t(content),\n    /**\n    [Content](#highlight.tags.content) that represents a quote.\n    */\n    quote: t(content),\n    /**\n    [Content](#highlight.tags.content) that is emphasized.\n    */\n    emphasis: t(content),\n    /**\n    [Content](#highlight.tags.content) that is styled strong.\n    */\n    strong: t(content),\n    /**\n    [Content](#highlight.tags.content) that is part of a link.\n    */\n    link: t(content),\n    /**\n    [Content](#highlight.tags.content) that is styled as code or\n    monospace.\n    */\n    monospace: t(content),\n    /**\n    [Content](#highlight.tags.content) that has a strike-through\n    style.\n    */\n    strikethrough: t(content),\n    /**\n    Inserted text in a change-tracking format.\n    */\n    inserted: t(),\n    /**\n    Deleted text.\n    */\n    deleted: t(),\n    /**\n    Changed text.\n    */\n    changed: t(),\n    /**\n    An invalid or unsyntactic element.\n    */\n    invalid: t(),\n    /**\n    Metadata or meta-instruction.\n    */\n    meta,\n    /**\n    [Metadata](#highlight.tags.meta) that applies to the entire\n    document.\n    */\n    documentMeta: t(meta),\n    /**\n    [Metadata](#highlight.tags.meta) that annotates or adds\n    attributes to a given syntactic element.\n    */\n    annotation: t(meta),\n    /**\n    Processing instruction or preprocessor directive. Subtag of\n    [meta](#highlight.tags.meta).\n    */\n    processingInstruction: t(meta),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) that indicates that a\n    given element is being defined. Expected to be used with the\n    various [name](#highlight.tags.name) tags.\n    */\n    definition: Tag.defineModifier(\"definition\"),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) that indicates that\n    something is constant. Mostly expected to be used with\n    [variable names](#highlight.tags.variableName).\n    */\n    constant: Tag.defineModifier(\"constant\"),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) used to indicate that\n    a [variable](#highlight.tags.variableName) or [property\n    name](#highlight.tags.propertyName) is being called or defined\n    as a function.\n    */\n    function: Tag.defineModifier(\"function\"),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) that can be applied to\n    [names](#highlight.tags.name) to indicate that they belong to\n    the language's standard environment.\n    */\n    standard: Tag.defineModifier(\"standard\"),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) that indicates a given\n    [names](#highlight.tags.name) is local to some scope.\n    */\n    local: Tag.defineModifier(\"local\"),\n    /**\n    A generic variant [modifier](#highlight.Tag^defineModifier) that\n    can be used to tag language-specific alternative variants of\n    some common tag. It is recommended for themes to define special\n    forms of at least the [string](#highlight.tags.string) and\n    [variable name](#highlight.tags.variableName) tags, since those\n    come up a lot.\n    */\n    special: Tag.defineModifier(\"special\")\n};\nfor (let name in tags) {\n    let val = tags[name];\n    if (val instanceof Tag)\n        val.name = name;\n}\n/**\nThis is a highlighter that adds stable, predictable classes to\ntokens, for styling with external CSS.\n\nThe following tags are mapped to their name prefixed with `\"tok-\"`\n(for example `\"tok-comment\"`):\n\n* [`link`](#highlight.tags.link)\n* [`heading`](#highlight.tags.heading)\n* [`emphasis`](#highlight.tags.emphasis)\n* [`strong`](#highlight.tags.strong)\n* [`keyword`](#highlight.tags.keyword)\n* [`atom`](#highlight.tags.atom)\n* [`bool`](#highlight.tags.bool)\n* [`url`](#highlight.tags.url)\n* [`labelName`](#highlight.tags.labelName)\n* [`inserted`](#highlight.tags.inserted)\n* [`deleted`](#highlight.tags.deleted)\n* [`literal`](#highlight.tags.literal)\n* [`string`](#highlight.tags.string)\n* [`number`](#highlight.tags.number)\n* [`variableName`](#highlight.tags.variableName)\n* [`typeName`](#highlight.tags.typeName)\n* [`namespace`](#highlight.tags.namespace)\n* [`className`](#highlight.tags.className)\n* [`macroName`](#highlight.tags.macroName)\n* [`propertyName`](#highlight.tags.propertyName)\n* [`operator`](#highlight.tags.operator)\n* [`comment`](#highlight.tags.comment)\n* [`meta`](#highlight.tags.meta)\n* [`punctuation`](#highlight.tags.punctuation)\n* [`invalid`](#highlight.tags.invalid)\n\nIn addition, these mappings are provided:\n\n* [`regexp`](#highlight.tags.regexp),\n  [`escape`](#highlight.tags.escape), and\n  [`special`](#highlight.tags.special)[`(string)`](#highlight.tags.string)\n  are mapped to `\"tok-string2\"`\n* [`special`](#highlight.tags.special)[`(variableName)`](#highlight.tags.variableName)\n  to `\"tok-variableName2\"`\n* [`local`](#highlight.tags.local)[`(variableName)`](#highlight.tags.variableName)\n  to `\"tok-variableName tok-local\"`\n* [`definition`](#highlight.tags.definition)[`(variableName)`](#highlight.tags.variableName)\n  to `\"tok-variableName tok-definition\"`\n* [`definition`](#highlight.tags.definition)[`(propertyName)`](#highlight.tags.propertyName)\n  to `\"tok-propertyName tok-definition\"`\n*/\nconst classHighlighter = tagHighlighter([\n    { tag: tags.link, class: \"tok-link\" },\n    { tag: tags.heading, class: \"tok-heading\" },\n    { tag: tags.emphasis, class: \"tok-emphasis\" },\n    { tag: tags.strong, class: \"tok-strong\" },\n    { tag: tags.keyword, class: \"tok-keyword\" },\n    { tag: tags.atom, class: \"tok-atom\" },\n    { tag: tags.bool, class: \"tok-bool\" },\n    { tag: tags.url, class: \"tok-url\" },\n    { tag: tags.labelName, class: \"tok-labelName\" },\n    { tag: tags.inserted, class: \"tok-inserted\" },\n    { tag: tags.deleted, class: \"tok-deleted\" },\n    { tag: tags.literal, class: \"tok-literal\" },\n    { tag: tags.string, class: \"tok-string\" },\n    { tag: tags.number, class: \"tok-number\" },\n    { tag: [tags.regexp, tags.escape, tags.special(tags.string)], class: \"tok-string2\" },\n    { tag: tags.variableName, class: \"tok-variableName\" },\n    { tag: tags.local(tags.variableName), class: \"tok-variableName tok-local\" },\n    { tag: tags.definition(tags.variableName), class: \"tok-variableName tok-definition\" },\n    { tag: tags.special(tags.variableName), class: \"tok-variableName2\" },\n    { tag: tags.definition(tags.propertyName), class: \"tok-propertyName tok-definition\" },\n    { tag: tags.typeName, class: \"tok-typeName\" },\n    { tag: tags.namespace, class: \"tok-namespace\" },\n    { tag: tags.className, class: \"tok-className\" },\n    { tag: tags.macroName, class: \"tok-macroName\" },\n    { tag: tags.propertyName, class: \"tok-propertyName\" },\n    { tag: tags.operator, class: \"tok-operator\" },\n    { tag: tags.comment, class: \"tok-comment\" },\n    { tag: tags.meta, class: \"tok-meta\" },\n    { tag: tags.invalid, class: \"tok-invalid\" },\n    { tag: tags.punctuation, class: \"tok-punctuation\" }\n]);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js\n");

/***/ })

};
;