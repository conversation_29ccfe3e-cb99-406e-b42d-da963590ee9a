import { Suspense } from 'react'
import { notFound } from 'next/navigation'
import { Metadata } from 'next'

import IntegrationsManager from '@/components/project/IntegrationsManager'
import LoadingSpinner from '@/components/ui/loading-spinner'

export const metadata: Metadata = {
  title: 'Integrations | Siden',
  description: 'Manage external integrations for your AI agents',
}

export default async function IntegrationsPage({
  params,
}: {
  params: Promise<{ projectId: string }>
}) {
  const { projectId } = await params;

  if (!projectId) {
    return notFound()
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Integrations</h1>
        <p className="text-muted-foreground">
          Connect your AI agents with external services to enhance their capabilities
        </p>
      </div>

      <Suspense fallback={<LoadingSpinner />}>
        <IntegrationsManager projectId={projectId} />
      </Suspense>
    </div>
  )
}
