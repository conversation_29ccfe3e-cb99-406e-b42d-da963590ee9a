import { useState, useEffect } from 'react';
import { generateSmartSuggestions } from '@/lib/api';
import { Message, CustomGroup } from '../types';

export function useSuggestions(
  activeChat: string,
  customGroups: CustomGroup[],
  getCurrentMessages: () => Message[],
  getCurrentThreadId: () => string | null,
  loading: boolean
) {
  // AI-generated smart suggestions state
  const [suggestedResponses, setSuggestedResponses] = useState<string[]>([]);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);
  const [lastProcessedMessageId, setLastProcessedMessageId] = useState<string | null>(null);
  const [lastProcessedContent, setLastProcessedContent] = useState<string>('');

  // Generate AI-powered suggestions based on conversation context
  const generateAISuggestions = async () => {
    // Don't generate suggestions if main chat is still loading
    if (loading) return;

    // Get current messages for the active chat
    const currentMessages = getCurrentMessages();

    // Only generate suggestions if we have messages and the last message is from assistant
    if (currentMessages.length === 0) {
      setSuggestedResponses([]);
      setLoadingSuggestions(false);
      return;
    }

    const lastMessage = currentMessages[currentMessages.length - 1];

    // Only generate suggestions if the last message is from an assistant (ceo, developer, system) and not thinking
    if (lastMessage?.sender === 'user' || lastMessage?.type === 'thinking') {
      setSuggestedResponses([]);
      setLoadingSuggestions(false);
      return;
    }

    // Check if it's an assistant message (ceo, developer, system)
    if (!['ceo', 'developer', 'system'].includes(lastMessage?.sender || '')) {
      setSuggestedResponses([]);
      setLoadingSuggestions(false);
      return;
    }

    // Check if we've already processed this exact message content to avoid duplicates
    const messageContent = lastMessage?.content || '';
    const messageId = lastMessage?.id || lastMessage?.timestamp;

    // Skip if we've already processed this exact content
    if (messageContent && messageContent === lastProcessedContent) {
      setLoadingSuggestions(false);
      return;
    }

    // Skip if content is too short (likely still streaming)
    if (messageContent.length < 10) {
      setLoadingSuggestions(false);
      return;
    }

    // Clear existing suggestions and start generating new ones
    setSuggestedResponses([]);
    setLoadingSuggestions(true);
    setLastProcessedMessageId(messageId);
    setLastProcessedContent(messageContent);

    try {

      // Get the last assistant message content
      const finalMessageContent = lastMessage?.content || '';

      // Determine chat type for context
      let chatType = activeChat;
      if (activeChat === 'group') {
        chatType = 'group';
      } else if (customGroups.find(group => group.id === activeChat)) {
        chatType = 'group';
      }

      // Get current thread ID for context
      const threadId = getCurrentThreadId();

      // Generate AI suggestions in background
      const aiSuggestions = await generateSmartSuggestions(
        finalMessageContent,
        chatType,
        threadId || undefined
      );

      // Only show suggestions once they're fully generated
      setSuggestedResponses(aiSuggestions);
    } catch (error) {
      console.error('Error generating AI suggestions:', error);

      // Don't show any suggestions if AI fails - let the backend handle all fallbacks
      // This ensures all suggestions are AI-generated and contextually relevant
      setSuggestedResponses([]);
    } finally {
      setLoadingSuggestions(false);
    }
  };

  // Effect to generate suggestions when messages change
  useEffect(() => {
    // Small delay to ensure message is fully rendered before generating suggestions
    const timer = setTimeout(() => {
      generateAISuggestions();
    }, 500);

    return () => clearTimeout(timer);
  }, [getCurrentMessages(), activeChat, loading]);

  return {
    suggestedResponses,
    setSuggestedResponses,
    loadingSuggestions,
    setLoadingSuggestions
  };
}
