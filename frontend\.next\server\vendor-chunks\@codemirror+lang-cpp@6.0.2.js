"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-cpp@6.0.2";
exports.ids = ["vendor-chunks/@codemirror+lang-cpp@6.0.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-cpp@6.0.2/node_modules/@codemirror/lang-cpp/dist/index.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-cpp@6.0.2/node_modules/@codemirror/lang-cpp/dist/index.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cpp: () => (/* binding */ cpp),\n/* harmony export */   cppLanguage: () => (/* binding */ cppLanguage)\n/* harmony export */ });\n/* harmony import */ var _lezer_cpp__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/cpp */ \"(ssr)/./node_modules/.pnpm/@lezer+cpp@1.1.3/node_modules/@lezer/cpp/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n\n\n\n/**\nA language provider based on the [Lezer C++\nparser](https://github.com/lezer-parser/cpp), extended with\nhighlighting and indentation information.\n*/\nconst cppLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LRLanguage.define({\n    name: \"cpp\",\n    parser: /*@__PURE__*/_lezer_cpp__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.indentNodeProp.add({\n                IfStatement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.continuedIndent)({ except: /^\\s*({|else\\b)/ }),\n                TryStatement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.continuedIndent)({ except: /^\\s*({|catch)\\b/ }),\n                LabeledStatement: _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.flatIndent,\n                CaseStatement: context => context.baseIndent + context.unit,\n                BlockComment: () => null,\n                CompoundStatement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.delimitedIndent)({ closing: \"}\" }),\n                Statement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.continuedIndent)({ except: /^{/ })\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldNodeProp.add({\n                \"DeclarationList CompoundStatement EnumeratorList FieldDeclarationList InitializerList\": _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldInside,\n                BlockComment(tree) { return { from: tree.from + 2, to: tree.to - 2 }; }\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { line: \"//\", block: { open: \"/*\", close: \"*/\" } },\n        indentOnInput: /^\\s*(?:case |default:|\\{|\\})$/,\n        closeBrackets: { stringPrefixes: [\"L\", \"u\", \"U\", \"u8\", \"LR\", \"UR\", \"uR\", \"u8R\", \"R\"] }\n    }\n});\n/**\nLanguage support for C++.\n*/\nfunction cpp() {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LanguageSupport(cppLanguage);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-cpp@6.0.2/node_modules/@codemirror/lang-cpp/dist/index.js\n");

/***/ })

};
;