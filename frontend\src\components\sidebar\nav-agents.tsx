"use client"

import { useEffect, useState } from "react"
import {
  ArrowUpRight,
  Link as LinkIcon,
  MoreHorizontal,
  Trash2,
  Plus,
  FolderKanban,
  Loader2,
  Users,
  Settings,
  PlayCircle,
} from "lucide-react"
import { toast } from "sonner"
import { usePathname, useRouter } from "next/navigation"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from "@/components/ui/tooltip"
import { getProjects, Project } from "@/lib/api"
import Link from "next/link"

export function NavAgents() {
  const { isMobile, state } = useSidebar()
  const [projects, setProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [loadingProjectId, setLoadingProjectId] = useState<string | null>(null)
  const pathname = usePathname()
  const router = useRouter()

  // Helper to sort projects by updated_at (most recent first)
  const sortProjects = (projectsList: Project[]): Project[] => {
    return [...projectsList].sort((a, b) => {
      const dateA = a.updated_at ? new Date(a.updated_at).getTime() : 0;
      const dateB = b.updated_at ? new Date(b.updated_at).getTime() : 0;
      return dateB - dateA;
    });
  };

  // Function to load projects data
  const loadProjects = async (showLoading = true) => {
    try {
      if (showLoading) {
        setIsLoading(true)
      }

      // Get all projects
      const projectsData = await getProjects() as Project[]

      // If no projects are found, the user might not be logged in
      if (projectsData.length === 0) {
        setProjects([])
        return
      }

      // Set projects, ensuring consistent sort order
      setProjects(sortProjects(projectsData))
    } catch (err) {
      console.error("Error loading projects:", err)
      // Set empty projects array on error
      setProjects([])
    } finally {
      if (showLoading) {
        setIsLoading(false)
      }
    }
  }

  // Load projects dynamically from the API on initial load
  useEffect(() => {
    loadProjects(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Listen for project-updated events to update the sidebar without full reload
  useEffect(() => {
    const handleProjectUpdate = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail) {
        const { projectId, updatedData } = customEvent.detail;

        // Update just the name for the project with the matching ID
        setProjects(prevProjects => {
          const updatedProjects = prevProjects.map(project =>
            project.id === projectId
              ? {
                  ...project,
                  name: updatedData.name,
                }
              : project
          );

          // Return the projects without re-sorting immediately
          return updatedProjects;
        });

        // Silently refresh in background to fetch updated timestamp and re-sort
        setTimeout(() => loadProjects(false), 1000);
      }
    }

    // Add event listener
    window.addEventListener('project-updated', handleProjectUpdate as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('project-updated', handleProjectUpdate as EventListener);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reset loading state when navigation completes (pathname changes)
  useEffect(() => {
    setLoadingProjectId(null)
  }, [pathname])

  // Function to handle project click with loading state
  const handleProjectClick = (e: React.MouseEvent<HTMLAnchorElement>, projectId: string, url: string) => {
    e.preventDefault()
    setLoadingProjectId(projectId)
    router.push(url)
  }

  return (
    <>
      <SidebarGroup>
        <div className="flex justify-between items-center">
          <SidebarGroupLabel>Projects</SidebarGroupLabel>
          {state !== "collapsed" ? (
            <Tooltip>
              <TooltipTrigger asChild>
                <Link
                  href="/project/new"
                  className="text-muted-foreground hover:text-foreground h-8 w-8 flex items-center justify-center rounded-md"
                >
                  <Plus className="h-4 w-4" />
                  <span className="sr-only">New Project</span>
                </Link>
              </TooltipTrigger>
              <TooltipContent>New Project</TooltipContent>
            </Tooltip>
          ) : null}
        </div>

      <SidebarMenu className="overflow-y-auto max-h-[calc(100vh-200px)] [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']">
        {state === "collapsed" && (
          <SidebarMenuItem>
            <Tooltip>
              <TooltipTrigger asChild>
                <SidebarMenuButton asChild>
                  <Link href="/project/new" className="flex items-center">
                    <Plus className="h-4 w-4" />
                    <span>New Project</span>
                  </Link>
                </SidebarMenuButton>
              </TooltipTrigger>
              <TooltipContent>New Project</TooltipContent>
            </Tooltip>
          </SidebarMenuItem>
        )}

        {isLoading ? (
          // Show skeleton loaders while loading
          Array.from({length: 3}).map((_, index) => (
            <SidebarMenuItem key={`skeleton-${index}`}>
              <SidebarMenuButton>
                <div className="h-4 w-4 bg-sidebar-foreground/10 rounded-md animate-pulse"></div>
                <div className="h-3 bg-sidebar-foreground/10 rounded w-3/4 animate-pulse"></div>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))
        ) : projects.length > 0 ? (
          // Show all projects
          <>
            {projects.map((project) => {
              // Check if this project is currently active
              const isActive = pathname?.includes(`/project/${project.id}`) || false;
              const isProjectLoading = loadingProjectId === project.id;
              const projectUrl = `/project/${project.id}`;

              return (
                <SidebarMenuItem key={`project-${project.id}`}>
                  {state === "collapsed" ? (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <SidebarMenuButton asChild className={isActive ? "bg-accent text-accent-foreground" : ""}>
                          <Link href={projectUrl} onClick={(e) => handleProjectClick(e, project.id, projectUrl)}>
                            {isProjectLoading ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <FolderKanban className="h-4 w-4" />
                            )}
                            <span>{project.name || 'Unnamed Project'}</span>
                          </Link>
                        </SidebarMenuButton>
                      </TooltipTrigger>
                      <TooltipContent>{project.name || 'Unnamed Project'}</TooltipContent>
                    </Tooltip>
                  ) : (
                    <SidebarMenuButton asChild className={isActive ? "bg-accent text-accent-foreground font-medium" : ""}>
                      <Link href={projectUrl} onClick={(e) => handleProjectClick(e, project.id, projectUrl)}>
                        {isProjectLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <FolderKanban className="h-4 w-4" />
                        )}
                        <span>{project.name || 'Unnamed Project'}</span>
                      </Link>
                    </SidebarMenuButton>
                  )}
                  {state !== "collapsed" && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <SidebarMenuAction showOnHover>
                          <MoreHorizontal />
                          <span className="sr-only">More</span>
                        </SidebarMenuAction>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent
                        className="w-56 rounded-lg"
                        side={isMobile ? "bottom" : "right"}
                        align={isMobile ? "end" : "start"}
                      >
                        <DropdownMenuItem asChild>
                          <Link href={`${projectUrl}/threads`}>
                            <Users className="text-muted-foreground" />
                            <span>View Conversations</span>
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`${projectUrl}/integrations`}>
                            <Settings className="text-muted-foreground" />
                            <span>Manage Integrations</span>
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => {
                          navigator.clipboard.writeText(window.location.origin + projectUrl)
                          toast.success("Link copied to clipboard")
                        }}>
                          <LinkIcon className="text-muted-foreground" />
                          <span>Copy Link</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <a href={projectUrl} target="_blank" rel="noopener noreferrer">
                            <ArrowUpRight className="text-muted-foreground" />
                            <span>Open in New Tab</span>
                          </a>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Trash2 className="text-muted-foreground" />
                          <span>Delete Project</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </SidebarMenuItem>
              );
            })}
          </>
        ) : (
          // Empty state
          <SidebarMenuItem>
            <SidebarMenuButton className="text-sidebar-foreground/70">
              <FolderKanban className="h-4 w-4" />
              <span>No Projects yet</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        )}
      </SidebarMenu>
    </SidebarGroup>
    </>
  )
}
