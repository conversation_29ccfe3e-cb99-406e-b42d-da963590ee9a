"""
Agent Capabilities and Task Delegation System

This module defines the capabilities for each agent role and implements
the task delegation protocol for agent handoffs.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone
from utils.logger import logger

from agentpress.agent_communication import AgentCommunicationManager, MessageType
from agent_collaboration import AgentCollaborationManager

# Define agent role capabilities
AGENT_CAPABILITIES = {
    "ceo": {
        "name": "<PERSON><PERSON>",
        "role": "CEO",
        "capabilities": {
            "strategic_planning": 0.9,
            "decision_making": 0.9,
            "leadership": 0.9,
            "business_analysis": 0.8,
            "resource_allocation": 0.8,
            "risk_management": 0.8,
            "stakeholder_management": 0.8,
            "vision_setting": 0.9,
            "executive_communication": 0.9,
            "team_coordination": 0.8
        },
        "can_delegate_to": ["developer", "marketing", "product", "design", "finance", "research", "sales"],
        "description": "Strategic leadership and executive decision-making"
    },
    "developer": {
        "name": "<PERSON>",
        "role": "Developer",
        "capabilities": {
            "software_development": 0.9,
            "system_architecture": 0.9,
            "code_review": 0.9,
            "debugging": 0.9,
            "api_design": 0.8,
            "database_management": 0.8,
            "testing": 0.8,
            "devops": 0.7,
            "security": 0.7,
            "technical_documentation": 0.8
        },
        "can_delegate_to": ["design", "product", "research"],
        "description": "Software development and technical implementation"
    },
    "marketing": {
        "name": "Chloe",
        "role": "Marketing",
        "capabilities": {
            "content_creation": 0.9,
            "campaign_planning": 0.9,
            "brand_management": 0.8,
            "market_research": 0.8,
            "social_media_strategy": 0.8,
            "copywriting": 0.9,
            "analytics": 0.7,
            "audience_targeting": 0.8,
            "seo": 0.7,
            "public_relations": 0.7
        },
        "can_delegate_to": ["design", "sales", "research"],
        "description": "Marketing strategy and content creation"
    },
    "product": {
        "name": "Mark",
        "role": "Product",
        "capabilities": {
            "product_strategy": 0.9,
            "user_experience": 0.8,
            "feature_prioritization": 0.9,
            "roadmap_planning": 0.9,
            "user_research": 0.8,
            "requirements_gathering": 0.9,
            "product_analytics": 0.8,
            "market_analysis": 0.7,
            "stakeholder_management": 0.8,
            "product_lifecycle": 0.8
        },
        "can_delegate_to": ["developer", "design", "marketing", "research"],
        "description": "Product management and user experience"
    },
    "design": {
        "name": "Diana",
        "role": "Design",
        "capabilities": {
            "ui_design": 0.9,
            "ux_design": 0.9,
            "visual_design": 0.9,
            "prototyping": 0.8,
            "user_research": 0.7,
            "design_systems": 0.8,
            "accessibility": 0.8,
            "interaction_design": 0.9,
            "branding": 0.8,
            "design_thinking": 0.8
        },
        "can_delegate_to": ["product", "developer"],
        "description": "User interface and experience design"
    },
    "finance": {
        "name": "Frank",
        "role": "Finance",
        "capabilities": {
            "financial_analysis": 0.9,
            "budgeting": 0.9,
            "forecasting": 0.9,
            "investment_analysis": 0.8,
            "risk_assessment": 0.8,
            "financial_reporting": 0.9,
            "cost_management": 0.8,
            "pricing_strategy": 0.7,
            "financial_planning": 0.9,
            "compliance": 0.8
        },
        "can_delegate_to": ["ceo", "sales"],
        "description": "Financial planning and analysis"
    },
    "research": {
        "name": "Rachel",
        "role": "Research",
        "capabilities": {
            "data_analysis": 0.9,
            "market_research": 0.9,
            "competitive_analysis": 0.8,
            "trend_identification": 0.8,
            "user_research": 0.8,
            "literature_review": 0.9,
            "research_methodology": 0.9,
            "statistical_analysis": 0.8,
            "report_writing": 0.8,
            "information_synthesis": 0.9
        },
        "can_delegate_to": ["product", "marketing", "developer"],
        "description": "Research and data analysis"
    },
    "sales": {
        "name": "Sam",
        "role": "Sales",
        "capabilities": {
            "sales_strategy": 0.9,
            "client_relationship": 0.9,
            "negotiation": 0.9,
            "pipeline_management": 0.8,
            "sales_forecasting": 0.8,
            "customer_needs_analysis": 0.8,
            "proposal_development": 0.8,
            "closing_techniques": 0.9,
            "account_management": 0.8,
            "sales_analytics": 0.7
        },
        "can_delegate_to": ["marketing", "product", "finance"],
        "description": "Sales strategy and client relationships"
    }
}

class AgentCapabilityManager:
    """Manages agent capabilities and task delegation."""
    
    def __init__(self, comm_manager: AgentCommunicationManager):
        """Initialize the capability manager.
        
        Args:
            comm_manager: The communication manager for agent interactions
        """
        self.comm_manager = comm_manager
        # Use the collaboration manager if it's an instance of AgentCollaborationManager
        self.collab_manager = comm_manager if isinstance(comm_manager, AgentCollaborationManager) else None
        
    async def register_all_agent_capabilities(self) -> bool:
        """Register capabilities for all agent roles.
        
        Returns:
            True if all registrations succeeded, False otherwise
        """
        success = True
        for agent_type, capabilities in AGENT_CAPABILITIES.items():
            agent_id = capabilities["name"].lower()
            result = await self.comm_manager.register_agent_capabilities(
                agent_id=agent_id,
                capabilities=capabilities
            )
            if not result:
                logger.error(f"Failed to register capabilities for agent {agent_id}")
                success = False
        return success
    
    async def find_best_agent_for_task(self, task_description: str, required_capabilities: List[str]) -> Optional[str]:
        """Find the best agent for a given task based on required capabilities.
        
        Args:
            task_description: Description of the task
            required_capabilities: List of capabilities required for the task
            
        Returns:
            Agent ID of the best agent for the task, or None if no suitable agent found
        """
        best_agent = None
        best_score = 0.0
        
        for agent_type, agent_info in AGENT_CAPABILITIES.items():
            agent_id = agent_info["name"].lower()
            agent_capabilities = agent_info["capabilities"]
            
            # Calculate capability match score
            score = 0.0
            for capability in required_capabilities:
                if capability in agent_capabilities:
                    score += agent_capabilities[capability]
            
            # Normalize score by number of required capabilities
            if required_capabilities:
                score /= len(required_capabilities)
            
            if score > best_score:
                best_score = score
                best_agent = agent_id
        
        return best_agent
    
    async def delegate_task(
        self, 
        from_agent_id: str, 
        task_description: str, 
        required_capabilities: List[str] = None,
        to_agent_id: str = None,
        thread_id: str = None,
        metadata: Dict[str, Any] = None
    ) -> Tuple[bool, Optional[str], Optional[str]]:
        """Delegate a task from one agent to another.
        
        Args:
            from_agent_id: ID of the agent delegating the task
            task_description: Description of the task to delegate
            required_capabilities: List of capabilities required for the task
            to_agent_id: ID of the agent to delegate to (optional)
            thread_id: Thread ID for the conversation
            metadata: Additional metadata for the task
            
        Returns:
            Tuple of (success, to_agent_id, message_id)
        """
        # If no specific agent is provided, find the best agent for the task
        if not to_agent_id and required_capabilities:
            to_agent_id = await self.find_best_agent_for_task(task_description, required_capabilities)
            
        if not to_agent_id:
            logger.error(f"No suitable agent found for task: {task_description}")
            return False, None, None
            
        # Check if the delegating agent can delegate to the target agent
        from_agent_type = None
        for agent_type, info in AGENT_CAPABILITIES.items():
            if info["name"].lower() == from_agent_id:
                from_agent_type = agent_type
                break
                
        if from_agent_type:
            can_delegate_to = AGENT_CAPABILITIES[from_agent_type]["can_delegate_to"]
            to_agent_type = None
            
            for agent_type, info in AGENT_CAPABILITIES.items():
                if info["name"].lower() == to_agent_id:
                    to_agent_type = agent_type
                    break
                    
            if to_agent_type not in can_delegate_to:
                logger.warning(f"Agent {from_agent_id} cannot delegate to {to_agent_id}")
                return False, None, None
        
        # Create task delegation message
        message = {
            "type": MessageType.TASK_DELEGATION,
            "content": task_description,
            "from_agent_id": from_agent_id,
            "to_agent_id": to_agent_id,
            "thread_id": thread_id,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "metadata": metadata or {},
            "required_capabilities": required_capabilities or []
        }
        
        # Use collaboration manager if available for reliable message delivery
        if self.collab_manager:
            message_id = await self.collab_manager.send_message_with_queuing(
                from_agent_id=from_agent_id,
                to_agent_id=to_agent_id,
                message=message,
                thread_id=thread_id
            )
            success = message_id is not None
        else:
            # Fall back to regular communication manager
            success = await self.comm_manager.send_message(
                from_agent_id=from_agent_id,
                to_agent_id=to_agent_id,
                message=message,
                thread_id=thread_id
            )
            message_id = None  # Regular comm manager doesn't return message_id
            
        return success, to_agent_id, message_id
        
    async def accept_task(
        self,
        agent_id: str,
        task_id: str,
        thread_id: str = None,
        metadata: Dict[str, Any] = None
    ) -> bool:
        """Accept a delegated task.
        
        Args:
            agent_id: ID of the agent accepting the task
            task_id: ID of the task being accepted
            thread_id: Thread ID for the conversation
            metadata: Additional metadata for the task acceptance
            
        Returns:
            True if task acceptance was successful, False otherwise
        """
        # Get the task details
        task = await self.comm_manager.get_message(task_id)
        if not task:
            logger.error(f"Task {task_id} not found")
            return False
            
        from_agent_id = task.get("to_agent_id")  # The agent who received the task
        to_agent_id = task.get("from_agent_id")  # The agent who delegated the task
        
        if not from_agent_id or not to_agent_id:
            logger.error(f"Invalid task delegation: missing agent IDs")
            return False
            
        # Create task acceptance message
        message = {
            "type": MessageType.TASK_ACCEPTANCE,
            "content": f"Task accepted: {task.get('content', 'Unknown task')}",
            "from_agent_id": agent_id,
            "to_agent_id": to_agent_id,
            "thread_id": thread_id or task.get("thread_id"),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "metadata": metadata or {},
            "task_id": task_id
        }
        
        # Use collaboration manager if available for reliable message delivery
        if self.collab_manager:
            message_id = await self.collab_manager.send_message_with_queuing(
                from_agent_id=agent_id,
                to_agent_id=to_agent_id,
                message=message,
                thread_id=thread_id or task.get("thread_id")
            )
            success = message_id is not None
        else:
            # Fall back to regular communication manager
            success = await self.comm_manager.send_message(
                from_agent_id=agent_id,
                to_agent_id=to_agent_id,
                message=message,
                thread_id=thread_id or task.get("thread_id")
            )
            
        return success
        
    async def complete_task(
        self,
        agent_id: str,
        task_id: str,
        result: str,
        thread_id: str = None,
        metadata: Dict[str, Any] = None
    ) -> bool:
        """Mark a task as completed and send results back to the delegating agent.
        
        Args:
            agent_id: ID of the agent completing the task
            task_id: ID of the task being completed
            result: Result of the completed task
            thread_id: Thread ID for the conversation
            metadata: Additional metadata for the task completion
            
        Returns:
            True if task completion was successful, False otherwise
        """
        # Get the task details
        task = await self.comm_manager.get_message(task_id)
        if not task:
            logger.error(f"Task {task_id} not found")
            return False
            
        from_agent_id = task.get("to_agent_id")  # The agent who received the task
        to_agent_id = task.get("from_agent_id")  # The agent who delegated the task
        
        if not from_agent_id or not to_agent_id:
            logger.error(f"Invalid task delegation: missing agent IDs")
            return False
            
        # Create task completion message
        message = {
            "type": MessageType.TASK_COMPLETION,
            "content": result,
            "from_agent_id": agent_id,
            "to_agent_id": to_agent_id,
            "thread_id": thread_id or task.get("thread_id"),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "metadata": metadata or {},
            "task_id": task_id
        }
        
        # Use collaboration manager if available for reliable message delivery
        if self.collab_manager:
            message_id = await self.collab_manager.send_message_with_queuing(
                from_agent_id=agent_id,
                to_agent_id=to_agent_id,
                message=message,
                thread_id=thread_id or task.get("thread_id")
            )
            success = message_id is not None
        else:
            # Fall back to regular communication manager
            success = await self.comm_manager.send_message(
                from_agent_id=agent_id,
                to_agent_id=to_agent_id,
                message=message,
                thread_id=thread_id or task.get("thread_id")
            )
            
        return success
        
    async def get_agent_capabilities(self, agent_id: str) -> Dict[str, Any]:
        """Get the capabilities of a specific agent.
        
        Args:
            agent_id: ID of the agent
            
        Returns:
            Dictionary of agent capabilities
        """
        # First try to get from database
        capabilities = await self.comm_manager.get_agent_capabilities(agent_id)
        
        # If not found in database, use the predefined capabilities
        if not capabilities:
            for agent_type, info in AGENT_CAPABILITIES.items():
                if info["name"].lower() == agent_id:
                    capabilities = info
                    break
                    
        return capabilities or {}
        
    async def get_all_agent_capabilities(self) -> Dict[str, Dict[str, Any]]:
        """Get capabilities of all agents.
        
        Returns:
            Dictionary mapping agent IDs to their capabilities
        """
        return AGENT_CAPABILITIES
