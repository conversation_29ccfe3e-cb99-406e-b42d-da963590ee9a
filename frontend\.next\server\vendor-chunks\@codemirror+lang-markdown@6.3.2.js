"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-markdown@6.3.2";
exports.ids = ["vendor-chunks/@codemirror+lang-markdown@6.3.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-markdown@6.3.2/node_modules/@codemirror/lang-markdown/dist/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-markdown@6.3.2/node_modules/@codemirror/lang-markdown/dist/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   commonmarkLanguage: () => (/* binding */ commonmarkLanguage),\n/* harmony export */   deleteMarkupBackward: () => (/* binding */ deleteMarkupBackward),\n/* harmony export */   insertNewlineContinueMarkup: () => (/* binding */ insertNewlineContinueMarkup),\n/* harmony export */   markdown: () => (/* binding */ markdown),\n/* harmony export */   markdownKeymap: () => (/* binding */ markdownKeymap),\n/* harmony export */   markdownLanguage: () => (/* binding */ markdownLanguage)\n/* harmony export */ });\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @codemirror/autocomplete */ \"(ssr)/./node_modules/.pnpm/@codemirror+autocomplete@6.18.6/node_modules/@codemirror/autocomplete/dist/index.js\");\n/* harmony import */ var _lezer_markdown__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/markdown */ \"(ssr)/./node_modules/.pnpm/@lezer+markdown@1.4.3/node_modules/@lezer/markdown/dist/index.js\");\n/* harmony import */ var _codemirror_lang_html__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/lang-html */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-html@6.4.9/node_modules/@codemirror/lang-html/dist/index.js\");\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n\n\n\n\n\n\n\n\nconst data = /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.defineLanguageFacet)({ commentTokens: { block: { open: \"<!--\", close: \"-->\" } } });\nconst headingProp = /*@__PURE__*/new _lezer_common__WEBPACK_IMPORTED_MODULE_1__.NodeProp();\nconst commonmark = /*@__PURE__*/_lezer_markdown__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n    props: [\n        /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldNodeProp.add(type => {\n            return !type.is(\"Block\") || type.is(\"Document\") || isHeading(type) != null || isList(type) ? undefined\n                : (tree, state) => ({ from: state.doc.lineAt(tree.from).to, to: tree.to });\n        }),\n        /*@__PURE__*/headingProp.add(isHeading),\n        /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.indentNodeProp.add({\n            Document: () => null\n        }),\n        /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.languageDataProp.add({\n            Document: data\n        })\n    ]\n});\nfunction isHeading(type) {\n    let match = /^(?:ATX|Setext)Heading(\\d)$/.exec(type.name);\n    return match ? +match[1] : undefined;\n}\nfunction isList(type) {\n    return type.name == \"OrderedList\" || type.name == \"BulletList\";\n}\nfunction findSectionEnd(headerNode, level) {\n    let last = headerNode;\n    for (;;) {\n        let next = last.nextSibling, heading;\n        if (!next || (heading = isHeading(next.type)) != null && heading <= level)\n            break;\n        last = next;\n    }\n    return last.to;\n}\nconst headerIndent = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldService.of((state, start, end) => {\n    for (let node = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.syntaxTree)(state).resolveInner(end, -1); node; node = node.parent) {\n        if (node.from < start)\n            break;\n        let heading = node.type.prop(headingProp);\n        if (heading == null)\n            continue;\n        let upto = findSectionEnd(node, heading);\n        if (upto > end)\n            return { from: end, to: upto };\n    }\n    return null;\n});\nfunction mkLang(parser) {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.Language(data, parser, [headerIndent], \"markdown\");\n}\n/**\nLanguage support for strict CommonMark.\n*/\nconst commonmarkLanguage = /*@__PURE__*/mkLang(commonmark);\nconst extended = /*@__PURE__*/commonmark.configure([_lezer_markdown__WEBPACK_IMPORTED_MODULE_0__.GFM, _lezer_markdown__WEBPACK_IMPORTED_MODULE_0__.Subscript, _lezer_markdown__WEBPACK_IMPORTED_MODULE_0__.Superscript, _lezer_markdown__WEBPACK_IMPORTED_MODULE_0__.Emoji, {\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldNodeProp.add({\n                Table: (tree, state) => ({ from: state.doc.lineAt(tree.from).to, to: tree.to })\n            })\n        ]\n    }]);\n/**\nLanguage support for [GFM](https://github.github.com/gfm/) plus\nsubscript, superscript, and emoji syntax.\n*/\nconst markdownLanguage = /*@__PURE__*/mkLang(extended);\nfunction getCodeParser(languages, defaultLanguage) {\n    return (info) => {\n        if (info && languages) {\n            let found = null;\n            // Strip anything after whitespace\n            info = /\\S*/.exec(info)[0];\n            if (typeof languages == \"function\")\n                found = languages(info);\n            else\n                found = _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LanguageDescription.matchLanguageName(languages, info, true);\n            if (found instanceof _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LanguageDescription)\n                return found.support ? found.support.language.parser : _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.ParseContext.getSkippingParser(found.load());\n            else if (found)\n                return found.parser;\n        }\n        return defaultLanguage ? defaultLanguage.parser : null;\n    };\n}\n\nclass Context {\n    constructor(node, from, to, spaceBefore, spaceAfter, type, item) {\n        this.node = node;\n        this.from = from;\n        this.to = to;\n        this.spaceBefore = spaceBefore;\n        this.spaceAfter = spaceAfter;\n        this.type = type;\n        this.item = item;\n    }\n    blank(maxWidth, trailing = true) {\n        let result = this.spaceBefore + (this.node.name == \"Blockquote\" ? \">\" : \"\");\n        if (maxWidth != null) {\n            while (result.length < maxWidth)\n                result += \" \";\n            return result;\n        }\n        else {\n            for (let i = this.to - this.from - result.length - this.spaceAfter.length; i > 0; i--)\n                result += \" \";\n            return result + (trailing ? this.spaceAfter : \"\");\n        }\n    }\n    marker(doc, add) {\n        let number = this.node.name == \"OrderedList\" ? String((+itemNumber(this.item, doc)[2] + add)) : \"\";\n        return this.spaceBefore + number + this.type + this.spaceAfter;\n    }\n}\nfunction getContext(node, doc) {\n    let nodes = [], context = [];\n    for (let cur = node; cur; cur = cur.parent) {\n        if (cur.name == \"FencedCode\")\n            return context;\n        if (cur.name == \"ListItem\" || cur.name == \"Blockquote\")\n            nodes.push(cur);\n    }\n    for (let i = nodes.length - 1; i >= 0; i--) {\n        let node = nodes[i], match;\n        let line = doc.lineAt(node.from), startPos = node.from - line.from;\n        if (node.name == \"Blockquote\" && (match = /^ *>( ?)/.exec(line.text.slice(startPos)))) {\n            context.push(new Context(node, startPos, startPos + match[0].length, \"\", match[1], \">\", null));\n        }\n        else if (node.name == \"ListItem\" && node.parent.name == \"OrderedList\" &&\n            (match = /^( *)\\d+([.)])( *)/.exec(line.text.slice(startPos)))) {\n            let after = match[3], len = match[0].length;\n            if (after.length >= 4) {\n                after = after.slice(0, after.length - 4);\n                len -= 4;\n            }\n            context.push(new Context(node.parent, startPos, startPos + len, match[1], after, match[2], node));\n        }\n        else if (node.name == \"ListItem\" && node.parent.name == \"BulletList\" &&\n            (match = /^( *)([-+*])( {1,4}\\[[ xX]\\])?( +)/.exec(line.text.slice(startPos)))) {\n            let after = match[4], len = match[0].length;\n            if (after.length > 4) {\n                after = after.slice(0, after.length - 4);\n                len -= 4;\n            }\n            let type = match[2];\n            if (match[3])\n                type += match[3].replace(/[xX]/, ' ');\n            context.push(new Context(node.parent, startPos, startPos + len, match[1], after, type, node));\n        }\n    }\n    return context;\n}\nfunction itemNumber(item, doc) {\n    return /^(\\s*)(\\d+)(?=[.)])/.exec(doc.sliceString(item.from, item.from + 10));\n}\nfunction renumberList(after, doc, changes, offset = 0) {\n    for (let prev = -1, node = after;;) {\n        if (node.name == \"ListItem\") {\n            let m = itemNumber(node, doc);\n            let number = +m[2];\n            if (prev >= 0) {\n                if (number != prev + 1)\n                    return;\n                changes.push({ from: node.from + m[1].length, to: node.from + m[0].length, insert: String(prev + 2 + offset) });\n            }\n            prev = number;\n        }\n        let next = node.nextSibling;\n        if (!next)\n            break;\n        node = next;\n    }\n}\nfunction normalizeIndent(content, state) {\n    let blank = /^[ \\t]*/.exec(content)[0].length;\n    if (!blank || state.facet(_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.indentUnit) != \"\\t\")\n        return content;\n    let col = (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.countColumn)(content, 4, blank);\n    let space = \"\";\n    for (let i = col; i > 0;) {\n        if (i >= 4) {\n            space += \"\\t\";\n            i -= 4;\n        }\n        else {\n            space += \" \";\n            i--;\n        }\n    }\n    return space + content.slice(blank);\n}\n/**\nThis command, when invoked in Markdown context with cursor\nselection(s), will create a new line with the markup for\nblockquotes and lists that were active on the old line. If the\ncursor was directly after the end of the markup for the old line,\ntrailing whitespace and list markers are removed from that line.\n\nThe command does nothing in non-Markdown context, so it should\nnot be used as the only binding for Enter (even in a Markdown\ndocument, HTML and code regions might use a different language).\n*/\nconst insertNewlineContinueMarkup = ({ state, dispatch }) => {\n    let tree = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.syntaxTree)(state), { doc } = state;\n    let dont = null, changes = state.changeByRange(range => {\n        if (!range.empty || !markdownLanguage.isActiveAt(state, range.from, 0))\n            return dont = { range };\n        let pos = range.from, line = doc.lineAt(pos);\n        let context = getContext(tree.resolveInner(pos, -1), doc);\n        while (context.length && context[context.length - 1].from > pos - line.from)\n            context.pop();\n        if (!context.length)\n            return dont = { range };\n        let inner = context[context.length - 1];\n        if (inner.to - inner.spaceAfter.length > pos - line.from)\n            return dont = { range };\n        let emptyLine = pos >= (inner.to - inner.spaceAfter.length) && !/\\S/.test(line.text.slice(inner.to));\n        // Empty line in list\n        if (inner.item && emptyLine) {\n            let first = inner.node.firstChild, second = inner.node.getChild(\"ListItem\", \"ListItem\");\n            // Not second item or blank line before: delete a level of markup\n            if (first.to >= pos || second && second.to < pos ||\n                line.from > 0 && !/[^\\s>]/.test(doc.lineAt(line.from - 1).text)) {\n                let next = context.length > 1 ? context[context.length - 2] : null;\n                let delTo, insert = \"\";\n                if (next && next.item) { // Re-add marker for the list at the next level\n                    delTo = line.from + next.from;\n                    insert = next.marker(doc, 1);\n                }\n                else {\n                    delTo = line.from + (next ? next.to : 0);\n                }\n                let changes = [{ from: delTo, to: pos, insert }];\n                if (inner.node.name == \"OrderedList\")\n                    renumberList(inner.item, doc, changes, -2);\n                if (next && next.node.name == \"OrderedList\")\n                    renumberList(next.item, doc, changes);\n                return { range: _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorSelection.cursor(delTo + insert.length), changes };\n            }\n            else { // Move second item down, making tight two-item list non-tight\n                let insert = blankLine(context, state, line);\n                return { range: _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorSelection.cursor(pos + insert.length + 1),\n                    changes: { from: line.from, insert: insert + state.lineBreak } };\n            }\n        }\n        if (inner.node.name == \"Blockquote\" && emptyLine && line.from) {\n            let prevLine = doc.lineAt(line.from - 1), quoted = />\\s*$/.exec(prevLine.text);\n            // Two aligned empty quoted lines in a row\n            if (quoted && quoted.index == inner.from) {\n                let changes = state.changes([{ from: prevLine.from + quoted.index, to: prevLine.to },\n                    { from: line.from + inner.from, to: line.to }]);\n                return { range: range.map(changes), changes };\n            }\n        }\n        let changes = [];\n        if (inner.node.name == \"OrderedList\")\n            renumberList(inner.item, doc, changes);\n        let continued = inner.item && inner.item.from < line.from;\n        let insert = \"\";\n        // If not dedented\n        if (!continued || /^[\\s\\d.)\\-+*>]*/.exec(line.text)[0].length >= inner.to) {\n            for (let i = 0, e = context.length - 1; i <= e; i++) {\n                insert += i == e && !continued ? context[i].marker(doc, 1)\n                    : context[i].blank(i < e ? (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.countColumn)(line.text, 4, context[i + 1].from) - insert.length : null);\n            }\n        }\n        let from = pos;\n        while (from > line.from && /\\s/.test(line.text.charAt(from - line.from - 1)))\n            from--;\n        insert = normalizeIndent(insert, state);\n        if (nonTightList(inner.node, state.doc))\n            insert = blankLine(context, state, line) + state.lineBreak + insert;\n        changes.push({ from, to: pos, insert: state.lineBreak + insert });\n        return { range: _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorSelection.cursor(from + insert.length + 1), changes };\n    });\n    if (dont)\n        return false;\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"input\" }));\n    return true;\n};\nfunction isMark(node) {\n    return node.name == \"QuoteMark\" || node.name == \"ListMark\";\n}\nfunction nonTightList(node, doc) {\n    if (node.name != \"OrderedList\" && node.name != \"BulletList\")\n        return false;\n    let first = node.firstChild, second = node.getChild(\"ListItem\", \"ListItem\");\n    if (!second)\n        return false;\n    let line1 = doc.lineAt(first.to), line2 = doc.lineAt(second.from);\n    let empty = /^[\\s>]*$/.test(line1.text);\n    return line1.number + (empty ? 0 : 1) < line2.number;\n}\nfunction blankLine(context, state, line) {\n    let insert = \"\";\n    for (let i = 0, e = context.length - 2; i <= e; i++) {\n        insert += context[i].blank(i < e\n            ? (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.countColumn)(line.text, 4, Math.min(line.text.length, context[i + 1].from)) - insert.length\n            : null, i < e);\n    }\n    return normalizeIndent(insert, state);\n}\nfunction contextNodeForDelete(tree, pos) {\n    let node = tree.resolveInner(pos, -1), scan = pos;\n    if (isMark(node)) {\n        scan = node.from;\n        node = node.parent;\n    }\n    for (let prev; prev = node.childBefore(scan);) {\n        if (isMark(prev)) {\n            scan = prev.from;\n        }\n        else if (prev.name == \"OrderedList\" || prev.name == \"BulletList\") {\n            node = prev.lastChild;\n            scan = node.to;\n        }\n        else {\n            break;\n        }\n    }\n    return node;\n}\n/**\nThis command will, when invoked in a Markdown context with the\ncursor directly after list or blockquote markup, delete one level\nof markup. When the markup is for a list, it will be replaced by\nspaces on the first invocation (a further invocation will delete\nthe spaces), to make it easy to continue a list.\n\nWhen not after Markdown block markup, this command will return\nfalse, so it is intended to be bound alongside other deletion\ncommands, with a higher precedence than the more generic commands.\n*/\nconst deleteMarkupBackward = ({ state, dispatch }) => {\n    let tree = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.syntaxTree)(state);\n    let dont = null, changes = state.changeByRange(range => {\n        let pos = range.from, { doc } = state;\n        if (range.empty && markdownLanguage.isActiveAt(state, range.from)) {\n            let line = doc.lineAt(pos);\n            let context = getContext(contextNodeForDelete(tree, pos), doc);\n            if (context.length) {\n                let inner = context[context.length - 1];\n                let spaceEnd = inner.to - inner.spaceAfter.length + (inner.spaceAfter ? 1 : 0);\n                // Delete extra trailing space after markup\n                if (pos - line.from > spaceEnd && !/\\S/.test(line.text.slice(spaceEnd, pos - line.from)))\n                    return { range: _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorSelection.cursor(line.from + spaceEnd),\n                        changes: { from: line.from + spaceEnd, to: pos } };\n                if (pos - line.from == spaceEnd &&\n                    // Only apply this if we're on the line that has the\n                    // construct's syntax, or there's only indentation in the\n                    // target range\n                    (!inner.item || line.from <= inner.item.from || !/\\S/.test(line.text.slice(0, inner.to)))) {\n                    let start = line.from + inner.from;\n                    // Replace a list item marker with blank space\n                    if (inner.item && inner.node.from < inner.item.from && /\\S/.test(line.text.slice(inner.from, inner.to))) {\n                        let insert = inner.blank((0,_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.countColumn)(line.text, 4, inner.to) - (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.countColumn)(line.text, 4, inner.from));\n                        if (start == line.from)\n                            insert = normalizeIndent(insert, state);\n                        return { range: _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorSelection.cursor(start + insert.length),\n                            changes: { from: start, to: line.from + inner.to, insert } };\n                    }\n                    // Delete one level of indentation\n                    if (start < pos)\n                        return { range: _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorSelection.cursor(start), changes: { from: start, to: pos } };\n                }\n            }\n        }\n        return dont = { range };\n    });\n    if (dont)\n        return false;\n    dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"delete\" }));\n    return true;\n};\n\n/**\nA small keymap with Markdown-specific bindings. Binds Enter to\n[`insertNewlineContinueMarkup`](https://codemirror.net/6/docs/ref/#lang-markdown.insertNewlineContinueMarkup)\nand Backspace to\n[`deleteMarkupBackward`](https://codemirror.net/6/docs/ref/#lang-markdown.deleteMarkupBackward).\n*/\nconst markdownKeymap = [\n    { key: \"Enter\", run: insertNewlineContinueMarkup },\n    { key: \"Backspace\", run: deleteMarkupBackward }\n];\nconst htmlNoMatch = /*@__PURE__*/(0,_codemirror_lang_html__WEBPACK_IMPORTED_MODULE_4__.html)({ matchClosingTags: false });\n/**\nMarkdown language support.\n*/\nfunction markdown(config = {}) {\n    let { codeLanguages, defaultCodeLanguage, addKeymap = true, base: { parser } = commonmarkLanguage, completeHTMLTags = true, htmlTagLanguage = htmlNoMatch } = config;\n    if (!(parser instanceof _lezer_markdown__WEBPACK_IMPORTED_MODULE_0__.MarkdownParser))\n        throw new RangeError(\"Base parser provided to `markdown` should be a Markdown parser\");\n    let extensions = config.extensions ? [config.extensions] : [];\n    let support = [htmlTagLanguage.support], defaultCode;\n    if (defaultCodeLanguage instanceof _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LanguageSupport) {\n        support.push(defaultCodeLanguage.support);\n        defaultCode = defaultCodeLanguage.language;\n    }\n    else if (defaultCodeLanguage) {\n        defaultCode = defaultCodeLanguage;\n    }\n    let codeParser = codeLanguages || defaultCode ? getCodeParser(codeLanguages, defaultCode) : undefined;\n    extensions.push((0,_lezer_markdown__WEBPACK_IMPORTED_MODULE_0__.parseCode)({ codeParser, htmlParser: htmlTagLanguage.language.parser }));\n    if (addKeymap)\n        support.push(_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Prec.high(_codemirror_view__WEBPACK_IMPORTED_MODULE_5__.keymap.of(markdownKeymap)));\n    let lang = mkLang(parser.configure(extensions));\n    if (completeHTMLTags)\n        support.push(lang.data.of({ autocomplete: htmlTagCompletion }));\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LanguageSupport(lang, support);\n}\nfunction htmlTagCompletion(context) {\n    let { state, pos } = context, m = /<[:\\-\\.\\w\\u00b7-\\uffff]*$/.exec(state.sliceDoc(pos - 25, pos));\n    if (!m)\n        return null;\n    let tree = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.syntaxTree)(state).resolveInner(pos, -1);\n    while (tree && !tree.type.isTop) {\n        if (tree.name == \"CodeBlock\" || tree.name == \"FencedCode\" || tree.name == \"ProcessingInstructionBlock\" ||\n            tree.name == \"CommentBlock\" || tree.name == \"Link\" || tree.name == \"Image\")\n            return null;\n        tree = tree.parent;\n    }\n    return {\n        from: pos - m[0].length, to: pos,\n        options: htmlTagCompletions(),\n        validFor: /^<[:\\-\\.\\w\\u00b7-\\uffff]*$/\n    };\n}\nlet _tagCompletions = null;\nfunction htmlTagCompletions() {\n    if (_tagCompletions)\n        return _tagCompletions;\n    let result = (0,_codemirror_lang_html__WEBPACK_IMPORTED_MODULE_4__.htmlCompletionSource)(new _codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_6__.CompletionContext(_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState.create({ extensions: htmlNoMatch }), 0, true));\n    return _tagCompletions = result ? result.options : [];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-markdown@6.3.2/node_modules/@codemirror/lang-markdown/dist/index.js\n");

/***/ })

};
;