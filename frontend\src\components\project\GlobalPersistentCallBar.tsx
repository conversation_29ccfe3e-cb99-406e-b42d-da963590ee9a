'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { PersistentCallBar } from './chat/PersistentCallBar';
import { useCallContext } from './chat/contexts/CallContext';

export function GlobalPersistentCallBar() {
  const pathname = usePathname();
  const callContext = useCallContext();

  // Only show the persistent call bar when call is active and minimized
  const shouldShowCallBar = callContext.isCallActive && callContext.isCallMinimized;

  // Determine if we're on chat page to adjust positioning
  const isOnChatPage = pathname.includes('/chat') || pathname.includes('/threads');

  // Add CSS class to document root when call bar is visible
  React.useEffect(() => {
    if (shouldShowCallBar) {
      document.documentElement.classList.add('has-persistent-call-bar');
    } else {
      document.documentElement.classList.remove('has-persistent-call-bar');
    }

    // Cleanup on unmount
    return () => {
      document.documentElement.classList.remove('has-persistent-call-bar');
    };
  }, [shouldShowCallBar]);

  if (!shouldShowCallBar) {
    return null;
  }

  return (
    <PersistentCallBar
      isCallActive={callContext.isCallActive}
      callType={callContext.callType}
      callParticipants={callContext.callParticipants}
      callStartTime={callContext.callStartTime}
      onMaximize={callContext.maximizeCall}
      onEndCall={callContext.endCall}
      className="z-5"
    />
  );
}
