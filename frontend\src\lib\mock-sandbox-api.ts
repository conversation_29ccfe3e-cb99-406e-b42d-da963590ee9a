import { FileInfo } from './api';

// Sample code files for the mock sandbox
const SAMPLE_FILES: Record<string, { content: string; is_dir: boolean }> = {
  // Root directory files
  '/workspace/README.md': {
    content: `# Agent Workspace

This is the main workspace for the agent. Here you'll find all the files and directories related to the current task.

## Project Structure

- \`src/\`: Source code files
- \`docs/\`: Documentation files
- \`tests/\`: Test files
- \`data/\`: Data files

## Current Task

The agent is currently working on implementing a new feature for the user dashboard.`,
    is_dir: false
  },
  '/workspace/package.json': {
    content: `{
  "name": "user-dashboard",
  "version": "1.0.0",
  "description": "User dashboard application",
  "main": "src/index.js",
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build",
    "test": "react-scripts test",
    "eject": "react-scripts eject"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-scripts": "5.0.1",
    "axios": "^1.3.4",
    "tailwindcss": "^3.3.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.28",
    "@types/react-dom": "^18.0.11",
    "typescript": "^5.0.2"
  }
}`,
    is_dir: false
  },
  
  // Source directory
  '/workspace/src': { content: '', is_dir: true },
  '/workspace/src/index.tsx': {
    content: `import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`,
    is_dir: false
  },
  '/workspace/src/App.tsx': {
    content: `import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Dashboard from './components/Dashboard';
import Login from './components/Login';
import Navbar from './components/Navbar';
import './App.css';

function App() {
  return (
    <Router>
      <div className="App">
        <Navbar />
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/login" element={<Login />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;`,
    is_dir: false
  },
  '/workspace/src/components': { content: '', is_dir: true },
  '/workspace/src/components/Dashboard.tsx': {
    content: `import React, { useState, useEffect } from 'react';
import { fetchUserData } from '../api/users';
import UserMetrics from './UserMetrics';
import ActivityFeed from './ActivityFeed';
import './Dashboard.css';

interface UserData {
  id: string;
  name: string;
  email: string;
  metrics: {
    totalActivities: number;
    completionRate: number;
    averageScore: number;
  };
  activities: {
    id: string;
    type: string;
    description: string;
    timestamp: string;
    completed: boolean;
    score?: number;
  }[];
}

function Dashboard() {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadUserData = async () => {
      try {
        setLoading(true);
        const data = await fetchUserData('current-user');
        setUserData(data);
        setError(null);
      } catch (err) {
        setError('Failed to load user data');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    loadUserData();
  }, []);

  if (loading) return <div className="loading">Loading...</div>;
  if (error) return <div className="error">{error}</div>;
  if (!userData) return <div className="error">No user data available</div>;

  return (
    <div className="dashboard">
      <h1>Welcome, {userData.name}</h1>
      <UserMetrics metrics={userData.metrics} />
      <ActivityFeed activities={userData.activities} />
    </div>
  );
}

export default Dashboard;`,
    is_dir: false
  },
  '/workspace/src/components/UserMetrics.tsx': {
    content: `import React from 'react';
import './UserMetrics.css';

interface Metrics {
  totalActivities: number;
  completionRate: number;
  averageScore: number;
}

interface UserMetricsProps {
  metrics: Metrics;
}

function UserMetrics({ metrics }: UserMetricsProps) {
  return (
    <div className="user-metrics">
      <h2>Your Performance</h2>
      <div className="metrics-grid">
        <div className="metric-card">
          <h3>Total Activities</h3>
          <p className="metric-value">{metrics.totalActivities}</p>
        </div>
        <div className="metric-card">
          <h3>Completion Rate</h3>
          <p className="metric-value">{metrics.completionRate}%</p>
        </div>
        <div className="metric-card">
          <h3>Average Score</h3>
          <p className="metric-value">{metrics.averageScore}</p>
        </div>
      </div>
    </div>
  );
}

export default UserMetrics;`,
    is_dir: false
  },
  
  // Docs directory
  '/workspace/docs': { content: '', is_dir: true },
  '/workspace/docs/api.md': {
    content: `# API Documentation

## User API

### GET /api/users/:id

Retrieves user data by ID.

**Parameters:**
- \`id\`: User ID or 'current-user' for the currently authenticated user

**Response:**
\`\`\`json
{
  "id": "user123",
  "name": "John Doe",
  "email": "<EMAIL>",
  "metrics": {
    "totalActivities": 42,
    "completionRate": 78.5,
    "averageScore": 85.2
  },
  "activities": [
    {
      "id": "act1",
      "type": "task",
      "description": "Complete project proposal",
      "timestamp": "2023-04-15T14:30:00Z",
      "completed": true,
      "score": 92
    }
  ]
}
\`\`\``,
    is_dir: false
  },
  
  // Tests directory
  '/workspace/tests': { content: '', is_dir: true },
  '/workspace/tests/Dashboard.test.tsx': {
    content: `import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import Dashboard from '../src/components/Dashboard';
import { fetchUserData } from '../src/api/users';

// Mock the API module
jest.mock('../src/api/users');

describe('Dashboard Component', () => {
  const mockUserData = {
    id: 'test-user',
    name: 'Test User',
    email: '<EMAIL>',
    metrics: {
      totalActivities: 10,
      completionRate: 80,
      averageScore: 75
    },
    activities: [
      {
        id: 'act1',
        type: 'task',
        description: 'Test activity',
        timestamp: '2023-04-15T14:30:00Z',
        completed: true,
        score: 85
      }
    ]
  };

  beforeEach(() => {
    // Reset all mocks
    jest.resetAllMocks();
  });

  test('renders loading state initially', () => {
    (fetchUserData as jest.Mock).mockReturnValue(new Promise(() => {}));
    
    render(<Dashboard />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('renders user data when loaded successfully', async () => {
    (fetchUserData as jest.Mock).mockResolvedValue(mockUserData);
    
    render(<Dashboard />);
    
    await waitFor(() => {
      expect(screen.getByText(\`Welcome, \${mockUserData.name}\`)).toBeInTheDocument();
    });
    
    expect(screen.getByText('Your Performance')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument(); // totalActivities
    expect(screen.getByText('80%')).toBeInTheDocument(); // completionRate
    expect(screen.getByText('75')).toBeInTheDocument(); // averageScore
  });

  test('renders error state when API call fails', async () => {
    (fetchUserData as jest.Mock).mockRejectedValue(new Error('API error'));
    
    render(<Dashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Failed to load user data')).toBeInTheDocument();
    });
  });
});`,
    is_dir: false
  }
};

// Mock implementation of listSandboxFiles
export const mockListSandboxFiles = async (sandboxId: string, path: string): Promise<FileInfo[]> => {
  console.log(`[MOCK] Listing files in ${path}`);
  
  // Normalize path
  const normalizedPath = path.endsWith('/') ? path : `${path}/`;
  
  // Get all files that are direct children of the specified path
  const files: FileInfo[] = [];
  
  // Track directories to avoid duplicates
  const directories = new Set<string>();
  
  Object.keys(SAMPLE_FILES).forEach(filePath => {
    // Skip files that don't start with the normalized path
    if (!filePath.startsWith(path)) return;
    
    // Skip the path itself
    if (filePath === path) return;
    
    // Get the relative path from the specified path
    const relativePath = filePath.slice(path.length);
    
    // Skip files that are in subdirectories
    if (relativePath.includes('/') && path !== '/workspace/') {
      // Extract the top-level directory
      const dirName = relativePath.split('/')[0];
      const dirPath = `${path}${dirName}`;
      
      // Add the directory if we haven't seen it yet
      if (!directories.has(dirPath)) {
        directories.add(dirPath);
        files.push({
          name: dirName,
          path: dirPath,
          is_dir: true,
          size: 0,
          mod_time: new Date().toISOString()
        });
      }
      
      return;
    }
    
    // Add the file
    const fileInfo = SAMPLE_FILES[filePath];
    const fileName = filePath.split('/').pop() || '';
    
    files.push({
      name: fileName,
      path: filePath,
      is_dir: fileInfo.is_dir,
      size: fileInfo.content.length,
      mod_time: new Date().toISOString()
    });
  });
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return files;
};

// Mock implementation of getSandboxFileContent
export const mockGetSandboxFileContent = async (sandboxId: string, path: string): Promise<string | Blob> => {
  console.log(`[MOCK] Getting content of ${path}`);
  
  // Check if the file exists
  if (!SAMPLE_FILES[path]) {
    throw new Error(`File not found: ${path}`);
  }
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 700));
  
  // Return the file content
  return SAMPLE_FILES[path].content;
};
