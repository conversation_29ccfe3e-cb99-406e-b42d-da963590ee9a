import React, { useState, useRef } from 'react';
import { cn } from '@/lib/utils';

interface CallTooltipProps {
  content: string;
  children: React.ReactNode;
  side?: 'top' | 'bottom';
}

export function CallTooltip({ content, children, side = 'top' }: CallTooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(true);
  };

  const handleMouseLeave = () => {
    setIsVisible(false);
  };

  return (
    <div 
      className="relative inline-block"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {isVisible && (
        <div 
          className={cn(
            "absolute z-50 px-3 py-2 text-sm font-medium text-white bg-primary rounded-md whitespace-nowrap",
            side === 'top' ? "bottom-full mb-2 left-1/2 transform -translate-x-1/2" : "top-full mt-2 left-1/2 transform -translate-x-1/2"
          )}
        >
          {content}
          <div 
            className={cn(
              "absolute w-2 h-2 bg-primary transform rotate-45",
              side === 'top' ? "top-full -mt-1 left-1/2 -translate-x-1/2" : "bottom-full -mb-1 left-1/2 -translate-x-1/2"
            )}
          />
        </div>
      )}
      {children}
    </div>
  );
}