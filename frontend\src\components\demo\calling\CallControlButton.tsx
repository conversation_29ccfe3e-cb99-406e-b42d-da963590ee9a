"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { CallTooltip } from "./CallTooltip";
import { LucideIcon } from 'lucide-react';

interface CallControlButtonProps {
  icon: LucideIcon;
  tooltip: string;
  onClick: () => void;
  isActive?: boolean;
  isRed?: boolean;
  onContextMenu?: (e: React.MouseEvent) => void;
  className?: string;
}

export function CallControlButton({
  icon: Icon,
  tooltip,
  onClick,
  isActive = false,
  isRed = false,
  onContextMenu,
  className
}: CallControlButtonProps) {
  return (
    <CallTooltip content={tooltip}>
      <Button 
        size="icon" 
        onClick={onClick}
        onContextMenu={onContextMenu}
        className={`bg-[#222222] ${isRed ? "text-[var(--call-red)]" : isActive ? "text-white" : "text-[#999999]"} hover:text-white hover:bg-[#333333] rounded-md border border-[#333333] ${className || ""}`}
      >
        <Icon className="h-5 w-5" />
      </Button>
    </CallTooltip>
  );
}
