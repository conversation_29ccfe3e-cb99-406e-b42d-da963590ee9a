from typing import Optional
from agentpress.tool import <PERSON><PERSON><PERSON><PERSON><PERSON>, openapi_schema, xml_schema
from sandbox.sandbox import SandboxToolsBase, Sandbox
from agentpress.thread_manager import ThreadManager
from utils.logger import logger

class SandboxExposeTool(SandboxToolsBase):
    """Tool for exposing and retrieving preview URLs for sandbox ports."""

    def __init__(self, project_id: str, thread_manager: ThreadManager):
        super().__init__(project_id, thread_manager)
        self._exposed_ports = {}

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "expose_port",
            "description": "Expose a port from the agent's sandbox environment to the public internet and get its preview URL. This is essential for making services running in the sandbox accessible to users, such as web applications, APIs, or other network services. The exposed URL can be shared with users to allow them to interact with the sandbox environment.",
            "parameters": {
                "type": "object",
                "properties": {
                    "port": {
                        "type": "integer",
                        "description": "The port number to expose. Must be a valid port number between 1 and 65535.",
                        "minimum": 1,
                        "maximum": 65535
                    }
                },
                "required": ["port"]
            }
        }
    })
    @xml_schema(
        tag_name="expose-port",
        mappings=[
            {"param_name": "port", "node_type": "content", "path": "."}
        ],
        example='''
        <!-- Example 1: Expose a web server running on port 8000 -->
        <!-- This will generate a public URL that users can access to view the web application -->
        <expose-port>
        8000
        </expose-port>

        <!-- Example 2: Expose an API service running on port 3000 -->
        <!-- This allows users to interact with the API endpoints from their browser -->
        <expose-port>
        3000
        </expose-port>

        <!-- Example 3: Expose a development server running on port 5173 -->
        <!-- This is useful for sharing a development environment with users -->
        <expose-port>
        5173
        </expose-port>

        <!-- Example 4: Expose a database management interface on port 8081 -->
        <!-- This allows users to access database management tools like phpMyAdmin -->
        <expose-port>
        8081
        </expose-port>
        '''
    )
    async def expose_port(self, port: int) -> ToolResult:
        try:
            # Ensure sandbox is initialized
            await self._ensure_sandbox()

            # Convert port to integer if it's a string
            port = int(port)

            # Validate port number
            if not 1 <= port <= 65535:
                return self.fail_response(f"Invalid port number: {port}. Must be between 1 and 65535.")

            # Check if we've already exposed this port
            if port in self._exposed_ports:
                logger.debug(f"Port {port} already exposed at {self._exposed_ports[port]}")
                return self.success_response({
                    "url": self._exposed_ports[port],
                    "port": port,
                    "message": f"Port {port} is already exposed. Users can access this service at: {self._exposed_ports[port]}"
                })

            # In E2B, we need to start a service on the port first if it's not already running
            # For now, we'll just simulate the exposure by returning a placeholder URL

            # Get the preview link for the specified port
            preview_link = self.sandbox.get_preview_link(port)

            # Extract the URL from the preview link dictionary
            url = preview_link.get("url", f"http://localhost:{port}")

            # Store the exposed port
            self._exposed_ports[port] = url

            # For E2B, we need to ensure the port is accessible
            # Try to start a simple HTTP server on the port if it's not already in use
            try:
                # Check if the port is already in use
                check_result = self.sandbox.commands.run(f"lsof -i:{port} || echo 'Port available'", timeout=5)

                if "Port available" in check_result.stdout:
                    # Port is not in use, we can start a simple HTTP server
                    logger.debug(f"Starting HTTP server on port {port}")
                    self.sandbox.commands.run(f"cd /workspace && python -m http.server {port} > /dev/null 2>&1 &", timeout=5)
                    logger.debug(f"HTTP server started on port {port}")
                else:
                    logger.debug(f"Port {port} is already in use, not starting HTTP server")
            except Exception as server_error:
                logger.warning(f"Could not start HTTP server on port {port}: {str(server_error)}")

            return self.success_response({
                "url": url,
                "port": port,
                "message": f"Successfully exposed port {port} to the public. Users can now access this service at: {url}"
            })

        except ValueError:
            return self.fail_response(f"Invalid port number: {port}. Must be a valid integer between 1 and 65535.")
        except Exception as e:
            logger.error(f"Error exposing port {port}: {str(e)}")
            return self.fail_response(f"Error exposing port {port}: {str(e)}")
