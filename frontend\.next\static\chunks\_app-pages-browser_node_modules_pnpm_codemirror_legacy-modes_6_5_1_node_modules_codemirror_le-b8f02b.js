"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_codemirror_legacy-modes_6_5_1_node_modules_codemirror_le-b8f02b"],{

/***/ "(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/mllike.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/mllike.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fSharp: () => (/* binding */ fSharp),\n/* harmony export */   oCaml: () => (/* binding */ oCaml),\n/* harmony export */   sml: () => (/* binding */ sml)\n/* harmony export */ });\nfunction mlLike(parserConfig) {\n  var words = {\n    'as': 'keyword',\n    'do': 'keyword',\n    'else': 'keyword',\n    'end': 'keyword',\n    'exception': 'keyword',\n    'fun': 'keyword',\n    'functor': 'keyword',\n    'if': 'keyword',\n    'in': 'keyword',\n    'include': 'keyword',\n    'let': 'keyword',\n    'of': 'keyword',\n    'open': 'keyword',\n    'rec': 'keyword',\n    'struct': 'keyword',\n    'then': 'keyword',\n    'type': 'keyword',\n    'val': 'keyword',\n    'while': 'keyword',\n    'with': 'keyword'\n  };\n\n  var extraWords = parserConfig.extraWords || {};\n  for (var prop in extraWords) {\n    if (extraWords.hasOwnProperty(prop)) {\n      words[prop] = parserConfig.extraWords[prop];\n    }\n  }\n  var hintWords = [];\n  for (var k in words) { hintWords.push(k); }\n\n  function tokenBase(stream, state) {\n    var ch = stream.next();\n\n    if (ch === '\"') {\n      state.tokenize = tokenString;\n      return state.tokenize(stream, state);\n    }\n    if (ch === '{') {\n      if (stream.eat('|')) {\n        state.longString = true;\n        state.tokenize = tokenLongString;\n        return state.tokenize(stream, state);\n      }\n    }\n    if (ch === '(') {\n      if (stream.match(/^\\*(?!\\))/)) {\n        state.commentLevel++;\n        state.tokenize = tokenComment;\n        return state.tokenize(stream, state);\n      }\n    }\n    if (ch === '~' || ch === '?') {\n      stream.eatWhile(/\\w/);\n      return 'variableName.special';\n    }\n    if (ch === '`') {\n      stream.eatWhile(/\\w/);\n      return 'quote';\n    }\n    if (ch === '/' && parserConfig.slashComments && stream.eat('/')) {\n      stream.skipToEnd();\n      return 'comment';\n    }\n    if (/\\d/.test(ch)) {\n      if (ch === '0' && stream.eat(/[bB]/)) {\n        stream.eatWhile(/[01]/);\n      } if (ch === '0' && stream.eat(/[xX]/)) {\n        stream.eatWhile(/[0-9a-fA-F]/)\n      } if (ch === '0' && stream.eat(/[oO]/)) {\n        stream.eatWhile(/[0-7]/);\n      } else {\n        stream.eatWhile(/[\\d_]/);\n        if (stream.eat('.')) {\n          stream.eatWhile(/[\\d]/);\n        }\n        if (stream.eat(/[eE]/)) {\n          stream.eatWhile(/[\\d\\-+]/);\n        }\n      }\n      return 'number';\n    }\n    if ( /[+\\-*&%=<>!?|@\\.~:]/.test(ch)) {\n      return 'operator';\n    }\n    if (/[\\w\\xa1-\\uffff]/.test(ch)) {\n      stream.eatWhile(/[\\w\\xa1-\\uffff]/);\n      var cur = stream.current();\n      return words.hasOwnProperty(cur) ? words[cur] : 'variable';\n    }\n    return null\n  }\n\n  function tokenString(stream, state) {\n    var next, end = false, escaped = false;\n    while ((next = stream.next()) != null) {\n      if (next === '\"' && !escaped) {\n        end = true;\n        break;\n      }\n      escaped = !escaped && next === '\\\\';\n    }\n    if (end && !escaped) {\n      state.tokenize = tokenBase;\n    }\n    return 'string';\n  };\n\n  function tokenComment(stream, state) {\n    var prev, next;\n    while(state.commentLevel > 0 && (next = stream.next()) != null) {\n      if (prev === '(' && next === '*') state.commentLevel++;\n      if (prev === '*' && next === ')') state.commentLevel--;\n      prev = next;\n    }\n    if (state.commentLevel <= 0) {\n      state.tokenize = tokenBase;\n    }\n    return 'comment';\n  }\n\n  function tokenLongString(stream, state) {\n    var prev, next;\n    while (state.longString && (next = stream.next()) != null) {\n      if (prev === '|' && next === '}') state.longString = false;\n      prev = next;\n    }\n    if (!state.longString) {\n      state.tokenize = tokenBase;\n    }\n    return 'string';\n  }\n\n  return {\n    startState: function() {return {tokenize: tokenBase, commentLevel: 0, longString: false};},\n    token: function(stream, state) {\n      if (stream.eatSpace()) return null;\n      return state.tokenize(stream, state);\n    },\n\n    languageData: {\n      autocomplete: hintWords,\n      commentTokens: {\n        line: parserConfig.slashComments ? \"//\" : undefined,\n        block: {open: \"(*\", close: \"*)\"}\n      }\n    }\n  };\n};\n\nconst oCaml = mlLike({\n  name: \"ocaml\",\n  extraWords: {\n    'and': 'keyword',\n    'assert': 'keyword',\n    'begin': 'keyword',\n    'class': 'keyword',\n    'constraint': 'keyword',\n    'done': 'keyword',\n    'downto': 'keyword',\n    'external': 'keyword',\n    'function': 'keyword',\n    'initializer': 'keyword',\n    'lazy': 'keyword',\n    'match': 'keyword',\n    'method': 'keyword',\n    'module': 'keyword',\n    'mutable': 'keyword',\n    'new': 'keyword',\n    'nonrec': 'keyword',\n    'object': 'keyword',\n    'private': 'keyword',\n    'sig': 'keyword',\n    'to': 'keyword',\n    'try': 'keyword',\n    'value': 'keyword',\n    'virtual': 'keyword',\n    'when': 'keyword',\n\n    // builtins\n    'raise': 'builtin',\n    'failwith': 'builtin',\n    'true': 'builtin',\n    'false': 'builtin',\n\n    // Pervasives builtins\n    'asr': 'builtin',\n    'land': 'builtin',\n    'lor': 'builtin',\n    'lsl': 'builtin',\n    'lsr': 'builtin',\n    'lxor': 'builtin',\n    'mod': 'builtin',\n    'or': 'builtin',\n\n    // More Pervasives\n    'raise_notrace': 'builtin',\n    'trace': 'builtin',\n    'exit': 'builtin',\n    'print_string': 'builtin',\n    'print_endline': 'builtin',\n\n     'int': 'type',\n     'float': 'type',\n     'bool': 'type',\n     'char': 'type',\n     'string': 'type',\n     'unit': 'type',\n\n     // Modules\n     'List': 'builtin'\n  }\n});\n\nconst fSharp = mlLike({\n  name: \"fsharp\",\n  extraWords: {\n    'abstract': 'keyword',\n    'assert': 'keyword',\n    'base': 'keyword',\n    'begin': 'keyword',\n    'class': 'keyword',\n    'default': 'keyword',\n    'delegate': 'keyword',\n    'do!': 'keyword',\n    'done': 'keyword',\n    'downcast': 'keyword',\n    'downto': 'keyword',\n    'elif': 'keyword',\n    'extern': 'keyword',\n    'finally': 'keyword',\n    'for': 'keyword',\n    'function': 'keyword',\n    'global': 'keyword',\n    'inherit': 'keyword',\n    'inline': 'keyword',\n    'interface': 'keyword',\n    'internal': 'keyword',\n    'lazy': 'keyword',\n    'let!': 'keyword',\n    'match': 'keyword',\n    'member': 'keyword',\n    'module': 'keyword',\n    'mutable': 'keyword',\n    'namespace': 'keyword',\n    'new': 'keyword',\n    'null': 'keyword',\n    'override': 'keyword',\n    'private': 'keyword',\n    'public': 'keyword',\n    'return!': 'keyword',\n    'return': 'keyword',\n    'select': 'keyword',\n    'static': 'keyword',\n    'to': 'keyword',\n    'try': 'keyword',\n    'upcast': 'keyword',\n    'use!': 'keyword',\n    'use': 'keyword',\n    'void': 'keyword',\n    'when': 'keyword',\n    'yield!': 'keyword',\n    'yield': 'keyword',\n\n    // Reserved words\n    'atomic': 'keyword',\n    'break': 'keyword',\n    'checked': 'keyword',\n    'component': 'keyword',\n    'const': 'keyword',\n    'constraint': 'keyword',\n    'constructor': 'keyword',\n    'continue': 'keyword',\n    'eager': 'keyword',\n    'event': 'keyword',\n    'external': 'keyword',\n    'fixed': 'keyword',\n    'method': 'keyword',\n    'mixin': 'keyword',\n    'object': 'keyword',\n    'parallel': 'keyword',\n    'process': 'keyword',\n    'protected': 'keyword',\n    'pure': 'keyword',\n    'sealed': 'keyword',\n    'tailcall': 'keyword',\n    'trait': 'keyword',\n    'virtual': 'keyword',\n    'volatile': 'keyword',\n\n    // builtins\n    'List': 'builtin',\n    'Seq': 'builtin',\n    'Map': 'builtin',\n    'Set': 'builtin',\n    'Option': 'builtin',\n    'int': 'builtin',\n    'string': 'builtin',\n    'not': 'builtin',\n    'true': 'builtin',\n    'false': 'builtin',\n\n    'raise': 'builtin',\n    'failwith': 'builtin'\n  },\n  slashComments: true\n});\n\nconst sml = mlLike({\n  name: \"sml\",\n  extraWords: {\n    'abstype': 'keyword',\n    'and': 'keyword',\n    'andalso': 'keyword',\n    'case': 'keyword',\n    'datatype': 'keyword',\n    'fn': 'keyword',\n    'handle': 'keyword',\n    'infix': 'keyword',\n    'infixr': 'keyword',\n    'local': 'keyword',\n    'nonfix': 'keyword',\n    'op': 'keyword',\n    'orelse': 'keyword',\n    'raise': 'keyword',\n    'withtype': 'keyword',\n    'eqtype': 'keyword',\n    'sharing': 'keyword',\n    'sig': 'keyword',\n    'signature': 'keyword',\n    'structure': 'keyword',\n    'where': 'keyword',\n    'true': 'keyword',\n    'false': 'keyword',\n\n    // types\n    'int': 'builtin',\n    'real': 'builtin',\n    'string': 'builtin',\n    'char': 'builtin',\n    'bool': 'builtin'\n  },\n  slashComments: true\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/mllike.js\n"));

/***/ })

}]);