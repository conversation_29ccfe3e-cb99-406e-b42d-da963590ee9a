import React from "react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

interface MessageInputProps {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  placeholder?: string;
  className?: string;
}

export function MessageInput({
  value,
  onChange,
  onKeyDown,
  placeholder = "Type a message...",
  className,
}: MessageInputProps) {
  return (
    <div className="flex-1 relative">
      <Input
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onKeyDown={onKeyDown}
        className={cn(
          "w-full h-10 bg-[#222222] border-[#333333] text-white placeholder:text-[#999999] focus-visible:ring-0 focus-visible:ring-offset-0",
          className
        )}
      />
    </div>
  );
}
