"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-python@6.2.0";
exports.ids = ["vendor-chunks/@codemirror+lang-python@6.2.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-python@6.2.0/node_modules/@codemirror/lang-python/dist/index.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-python@6.2.0/node_modules/@codemirror/lang-python/dist/index.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   globalCompletion: () => (/* binding */ globalCompletion),\n/* harmony export */   localCompletionSource: () => (/* binding */ localCompletionSource),\n/* harmony export */   python: () => (/* binding */ python),\n/* harmony export */   pythonLanguage: () => (/* binding */ pythonLanguage)\n/* harmony export */ });\n/* harmony import */ var _lezer_python__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/python */ \"(ssr)/./node_modules/.pnpm/@lezer+python@1.1.18/node_modules/@lezer/python/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n/* harmony import */ var _codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/autocomplete */ \"(ssr)/./node_modules/.pnpm/@codemirror+autocomplete@6.18.6/node_modules/@codemirror/autocomplete/dist/index.js\");\n\n\n\n\n\nconst cache = /*@__PURE__*/new _lezer_common__WEBPACK_IMPORTED_MODULE_1__.NodeWeakMap();\nconst ScopeNodes = /*@__PURE__*/new Set([\n    \"Script\", \"Body\",\n    \"FunctionDefinition\", \"ClassDefinition\", \"LambdaExpression\",\n    \"ForStatement\", \"MatchClause\"\n]);\nfunction defID(type) {\n    return (node, def, outer) => {\n        if (outer)\n            return false;\n        let id = node.node.getChild(\"VariableName\");\n        if (id)\n            def(id, type);\n        return true;\n    };\n}\nconst gatherCompletions = {\n    FunctionDefinition: /*@__PURE__*/defID(\"function\"),\n    ClassDefinition: /*@__PURE__*/defID(\"class\"),\n    ForStatement(node, def, outer) {\n        if (outer)\n            for (let child = node.node.firstChild; child; child = child.nextSibling) {\n                if (child.name == \"VariableName\")\n                    def(child, \"variable\");\n                else if (child.name == \"in\")\n                    break;\n            }\n    },\n    ImportStatement(_node, def) {\n        var _a, _b;\n        let { node } = _node;\n        let isFrom = ((_a = node.firstChild) === null || _a === void 0 ? void 0 : _a.name) == \"from\";\n        for (let ch = node.getChild(\"import\"); ch; ch = ch.nextSibling) {\n            if (ch.name == \"VariableName\" && ((_b = ch.nextSibling) === null || _b === void 0 ? void 0 : _b.name) != \"as\")\n                def(ch, isFrom ? \"variable\" : \"namespace\");\n        }\n    },\n    AssignStatement(node, def) {\n        for (let child = node.node.firstChild; child; child = child.nextSibling) {\n            if (child.name == \"VariableName\")\n                def(child, \"variable\");\n            else if (child.name == \":\" || child.name == \"AssignOp\")\n                break;\n        }\n    },\n    ParamList(node, def) {\n        for (let prev = null, child = node.node.firstChild; child; child = child.nextSibling) {\n            if (child.name == \"VariableName\" && (!prev || !/\\*|AssignOp/.test(prev.name)))\n                def(child, \"variable\");\n            prev = child;\n        }\n    },\n    CapturePattern: /*@__PURE__*/defID(\"variable\"),\n    AsPattern: /*@__PURE__*/defID(\"variable\"),\n    __proto__: null\n};\nfunction getScope(doc, node) {\n    let cached = cache.get(node);\n    if (cached)\n        return cached;\n    let completions = [], top = true;\n    function def(node, type) {\n        let name = doc.sliceString(node.from, node.to);\n        completions.push({ label: name, type });\n    }\n    node.cursor(_lezer_common__WEBPACK_IMPORTED_MODULE_1__.IterMode.IncludeAnonymous).iterate(node => {\n        if (node.name) {\n            let gather = gatherCompletions[node.name];\n            if (gather && gather(node, def, top) || !top && ScopeNodes.has(node.name))\n                return false;\n            top = false;\n        }\n        else if (node.to - node.from > 8192) {\n            // Allow caching for bigger internal nodes\n            for (let c of getScope(doc, node.node))\n                completions.push(c);\n            return false;\n        }\n    });\n    cache.set(node, completions);\n    return completions;\n}\nconst Identifier = /^[\\w\\xa1-\\uffff][\\w\\d\\xa1-\\uffff]*$/;\nconst dontComplete = [\"String\", \"FormatString\", \"Comment\", \"PropertyName\"];\n/**\nCompletion source that looks up locally defined names in\nPython code.\n*/\nfunction localCompletionSource(context) {\n    let inner = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.syntaxTree)(context.state).resolveInner(context.pos, -1);\n    if (dontComplete.indexOf(inner.name) > -1)\n        return null;\n    let isWord = inner.name == \"VariableName\" ||\n        inner.to - inner.from < 20 && Identifier.test(context.state.sliceDoc(inner.from, inner.to));\n    if (!isWord && !context.explicit)\n        return null;\n    let options = [];\n    for (let pos = inner; pos; pos = pos.parent) {\n        if (ScopeNodes.has(pos.name))\n            options = options.concat(getScope(context.state.doc, pos));\n    }\n    return {\n        options,\n        from: isWord ? inner.from : context.pos,\n        validFor: Identifier\n    };\n}\nconst globals = /*@__PURE__*/[\n    \"__annotations__\", \"__builtins__\", \"__debug__\", \"__doc__\", \"__import__\", \"__name__\",\n    \"__loader__\", \"__package__\", \"__spec__\",\n    \"False\", \"None\", \"True\"\n].map(n => ({ label: n, type: \"constant\" })).concat(/*@__PURE__*/[\n    \"ArithmeticError\", \"AssertionError\", \"AttributeError\", \"BaseException\", \"BlockingIOError\",\n    \"BrokenPipeError\", \"BufferError\", \"BytesWarning\", \"ChildProcessError\", \"ConnectionAbortedError\",\n    \"ConnectionError\", \"ConnectionRefusedError\", \"ConnectionResetError\", \"DeprecationWarning\",\n    \"EOFError\", \"Ellipsis\", \"EncodingWarning\", \"EnvironmentError\", \"Exception\", \"FileExistsError\",\n    \"FileNotFoundError\", \"FloatingPointError\", \"FutureWarning\", \"GeneratorExit\", \"IOError\",\n    \"ImportError\", \"ImportWarning\", \"IndentationError\", \"IndexError\", \"InterruptedError\",\n    \"IsADirectoryError\", \"KeyError\", \"KeyboardInterrupt\", \"LookupError\", \"MemoryError\",\n    \"ModuleNotFoundError\", \"NameError\", \"NotADirectoryError\", \"NotImplemented\", \"NotImplementedError\",\n    \"OSError\", \"OverflowError\", \"PendingDeprecationWarning\", \"PermissionError\", \"ProcessLookupError\",\n    \"RecursionError\", \"ReferenceError\", \"ResourceWarning\", \"RuntimeError\", \"RuntimeWarning\",\n    \"StopAsyncIteration\", \"StopIteration\", \"SyntaxError\", \"SyntaxWarning\", \"SystemError\",\n    \"SystemExit\", \"TabError\", \"TimeoutError\", \"TypeError\", \"UnboundLocalError\", \"UnicodeDecodeError\",\n    \"UnicodeEncodeError\", \"UnicodeError\", \"UnicodeTranslateError\", \"UnicodeWarning\", \"UserWarning\",\n    \"ValueError\", \"Warning\", \"ZeroDivisionError\"\n].map(n => ({ label: n, type: \"type\" }))).concat(/*@__PURE__*/[\n    \"bool\", \"bytearray\", \"bytes\", \"classmethod\", \"complex\", \"float\", \"frozenset\", \"int\", \"list\",\n    \"map\", \"memoryview\", \"object\", \"range\", \"set\", \"staticmethod\", \"str\", \"super\", \"tuple\", \"type\"\n].map(n => ({ label: n, type: \"class\" }))).concat(/*@__PURE__*/[\n    \"abs\", \"aiter\", \"all\", \"anext\", \"any\", \"ascii\", \"bin\", \"breakpoint\", \"callable\", \"chr\",\n    \"compile\", \"delattr\", \"dict\", \"dir\", \"divmod\", \"enumerate\", \"eval\", \"exec\", \"exit\", \"filter\",\n    \"format\", \"getattr\", \"globals\", \"hasattr\", \"hash\", \"help\", \"hex\", \"id\", \"input\", \"isinstance\",\n    \"issubclass\", \"iter\", \"len\", \"license\", \"locals\", \"max\", \"min\", \"next\", \"oct\", \"open\",\n    \"ord\", \"pow\", \"print\", \"property\", \"quit\", \"repr\", \"reversed\", \"round\", \"setattr\", \"slice\",\n    \"sorted\", \"sum\", \"vars\", \"zip\"\n].map(n => ({ label: n, type: \"function\" })));\nconst snippets = [\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.snippetCompletion)(\"def ${name}(${params}):\\n\\t${}\", {\n        label: \"def\",\n        detail: \"function\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.snippetCompletion)(\"for ${name} in ${collection}:\\n\\t${}\", {\n        label: \"for\",\n        detail: \"loop\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.snippetCompletion)(\"while ${}:\\n\\t${}\", {\n        label: \"while\",\n        detail: \"loop\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.snippetCompletion)(\"try:\\n\\t${}\\nexcept ${error}:\\n\\t${}\", {\n        label: \"try\",\n        detail: \"/ except block\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.snippetCompletion)(\"if ${}:\\n\\t\\n\", {\n        label: \"if\",\n        detail: \"block\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.snippetCompletion)(\"if ${}:\\n\\t${}\\nelse:\\n\\t${}\", {\n        label: \"if\",\n        detail: \"/ else block\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.snippetCompletion)(\"class ${name}:\\n\\tdef __init__(self, ${params}):\\n\\t\\t\\t${}\", {\n        label: \"class\",\n        detail: \"definition\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.snippetCompletion)(\"import ${module}\", {\n        label: \"import\",\n        detail: \"statement\",\n        type: \"keyword\"\n    }),\n    /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.snippetCompletion)(\"from ${module} import ${names}\", {\n        label: \"from\",\n        detail: \"import\",\n        type: \"keyword\"\n    })\n];\n/**\nAutocompletion for built-in Python globals and keywords.\n*/\nconst globalCompletion = /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.ifNotIn)(dontComplete, /*@__PURE__*/(0,_codemirror_autocomplete__WEBPACK_IMPORTED_MODULE_3__.completeFromList)(/*@__PURE__*/globals.concat(snippets)));\n\nfunction innerBody(context) {\n    let { node, pos } = context;\n    let lineIndent = context.lineIndent(pos, -1);\n    let found = null;\n    for (;;) {\n        let before = node.childBefore(pos);\n        if (!before) {\n            break;\n        }\n        else if (before.name == \"Comment\") {\n            pos = before.from;\n        }\n        else if (before.name == \"Body\" || before.name == \"MatchBody\") {\n            if (context.baseIndentFor(before) + context.unit <= lineIndent)\n                found = before;\n            node = before;\n        }\n        else if (before.name == \"MatchClause\") {\n            node = before;\n        }\n        else if (before.type.is(\"Statement\")) {\n            node = before;\n        }\n        else {\n            break;\n        }\n    }\n    return found;\n}\nfunction indentBody(context, node) {\n    let base = context.baseIndentFor(node);\n    let line = context.lineAt(context.pos, -1), to = line.from + line.text.length;\n    // Don't consider blank, deindented lines at the end of the\n    // block part of the block\n    if (/^\\s*($|#)/.test(line.text) &&\n        context.node.to < to + 100 &&\n        !/\\S/.test(context.state.sliceDoc(to, context.node.to)) &&\n        context.lineIndent(context.pos, -1) <= base)\n        return null;\n    // A normally deindenting keyword that appears at a higher\n    // indentation than the block should probably be handled by the next\n    // level\n    if (/^\\s*(else:|elif |except |finally:|case\\s+[^=:]+:)/.test(context.textAfter) && context.lineIndent(context.pos, -1) > base)\n        return null;\n    return base + context.unit;\n}\n/**\nA language provider based on the [Lezer Python\nparser](https://github.com/lezer-parser/python), extended with\nhighlighting and indentation information.\n*/\nconst pythonLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LRLanguage.define({\n    name: \"python\",\n    parser: /*@__PURE__*/_lezer_python__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.indentNodeProp.add({\n                Body: context => {\n                    var _a;\n                    let inner = innerBody(context);\n                    return (_a = indentBody(context, inner || context.node)) !== null && _a !== void 0 ? _a : context.continue();\n                },\n                MatchBody: context => {\n                    var _a;\n                    let inner = innerBody(context);\n                    return (_a = indentBody(context, inner || context.node)) !== null && _a !== void 0 ? _a : context.continue();\n                },\n                IfStatement: cx => /^\\s*(else:|elif )/.test(cx.textAfter) ? cx.baseIndent : cx.continue(),\n                \"ForStatement WhileStatement\": cx => /^\\s*else:/.test(cx.textAfter) ? cx.baseIndent : cx.continue(),\n                TryStatement: cx => /^\\s*(except |finally:|else:)/.test(cx.textAfter) ? cx.baseIndent : cx.continue(),\n                MatchStatement: cx => {\n                    if (/^\\s*case /.test(cx.textAfter))\n                        return cx.baseIndent + cx.unit;\n                    return cx.continue();\n                },\n                \"TupleExpression ComprehensionExpression ParamList ArgList ParenthesizedExpression\": /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.delimitedIndent)({ closing: \")\" }),\n                \"DictionaryExpression DictionaryComprehensionExpression SetExpression SetComprehensionExpression\": /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.delimitedIndent)({ closing: \"}\" }),\n                \"ArrayExpression ArrayComprehensionExpression\": /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.delimitedIndent)({ closing: \"]\" }),\n                MemberExpression: cx => cx.baseIndent + cx.unit,\n                \"String FormatString\": () => null,\n                Script: context => {\n                    var _a;\n                    let inner = innerBody(context);\n                    return (_a = (inner && indentBody(context, inner))) !== null && _a !== void 0 ? _a : context.continue();\n                },\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldNodeProp.add({\n                \"ArrayExpression DictionaryExpression SetExpression TupleExpression\": _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.foldInside,\n                Body: (node, state) => ({ from: node.from + 1, to: node.to - (node.to == state.doc.length ? 0 : 1) }),\n                \"String FormatString\": (node, state) => ({ from: state.doc.lineAt(node.from).to, to: node.to })\n            })\n        ],\n    }),\n    languageData: {\n        closeBrackets: {\n            brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"'''\", '\"\"\"'],\n            stringPrefixes: [\"f\", \"fr\", \"rf\", \"r\", \"u\", \"b\", \"br\", \"rb\",\n                \"F\", \"FR\", \"RF\", \"R\", \"U\", \"B\", \"BR\", \"RB\"]\n        },\n        commentTokens: { line: \"#\" },\n        // Indent logic logic are triggered upon below input patterns\n        indentOnInput: /^\\s*([\\}\\]\\)]|else:|elif |except |finally:|case\\s+[^:]*:?)$/,\n    }\n});\n/**\nPython language support.\n*/\nfunction python() {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_2__.LanguageSupport(pythonLanguage, [\n        pythonLanguage.data.of({ autocomplete: localCompletionSource }),\n        pythonLanguage.data.of({ autocomplete: globalCompletion }),\n    ]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-python@6.2.0/node_modules/@codemirror/lang-python/dist/index.js\n");

/***/ })

};
;