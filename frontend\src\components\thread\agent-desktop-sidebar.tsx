"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import {
  Terminal,
  FileText,
  Folder,
  ChevronDown,
  ChevronRight,
  RefreshCw,
  Monitor,
  CircleDot,
  ChevronUp
} from "lucide-react";
import { cn } from "@/lib/utils";
import { type FileInfo, Project } from "@/lib/api";
import { mockListSandboxFiles, mockGetSandboxFileContent } from "@/lib/mock-sandbox-api";
import { FileRenderer } from "@/components/file-renderers";

interface AgentDesktopSidebarProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  sandboxId: string;
  project?: Project;
  agentName?: string;
}

export function AgentDesktopSidebar({
  open,
  onOpenChange,
  sandboxId,
  project,
  agentName = "Agent"
}: AgentDesktopSidebarProps) {
  // State for sidebar size
  const [isExpanded, setIsExpanded] = useState(true);

  // Active tab state
  const [activeTab, setActiveTab] = useState("terminal");

  // Terminal output state
  const [terminalOutput, setTerminalOutput] = useState<string[]>([
    "$ cd /home/<USER>",
    "$ cd /home/<USER>/doc_project",
    "$ ls -la",
    "total 60",
    "drwxr-xr-x  9 <USER> <GROUP> 4096 Apr 15 12:34 .",
    "drwxr-xr-x 16 <USER> <GROUP> 4096 Apr 15 12:30 ..",
    "drwxr-xr-x  8 <USER> <GROUP> 4096 Apr 15 12:34 .git",
    "-rw-r--r--  1 <USER> <GROUP>  551 Apr 15 12:34 .gitignore",
    "drwxr-xr-x  2 <USER> <GROUP> 4096 Apr 15 12:34 public",
    "drwxr-xr-x  6 <USER> <GROUP> 4096 Apr 15 12:34 src",
    "-rw-r--r--  1 <USER> <GROUP> 2581 Apr 15 12:34 package.json",
    "-rw-r--r--  1 <USER> <GROUP>  982 Apr 15 12:34 README.md",
    "-rw-r--r--  1 <USER> <GROUP>  731 Apr 15 12:34 tsconfig.json",
    "$ rm -r doc_website_nextjs",
    "$ mkdir doc_website_nextjs",
    "$ cd doc_website_nextjs",
    "$ mkdir -p doc_website_nextjs/node_modules",
    "$ mkdir -p doc_website_nextjs/public",
    "$ mkdir -p doc_website_nextjs/src/app/root/",
    "$ npm init -y",
    "Wrote to /home/<USER>/doc_project/doc_website_nextjs/package.json:",
    "{",
    "  \"name\": \"doc_website_nextjs\",",
    "  \"version\": \"1.0.0\",",
    "  \"description\": \"\",",
    "  \"main\": \"index.js\",",
    "  \"scripts\": {",
    "    \"dev\": \"next dev\",",
    "    \"build\": \"next build\",",
    "    \"start\": \"next start\",",
    "    \"lint\": \"next lint\"",
    "  },",
    "  \"keywords\": [],",
    "  \"author\": \"\",",
    "  \"license\": \"ISC\"",
    "}",
    "$ npm install next react react-dom",
    "added 300 packages, and audited 301 packages in 8s",
    "",
    "150 packages are looking for funding",
    "  run `npm fund` for details",
    "",
    "found 0 vulnerabilities",
    "$ touch src/app/page.tsx",
    "$ touch src/app/layout.tsx",
    "$ touch src/app/globals.css"
  ]);

  // File system state
  const [currentPath, setCurrentPath] = useState("/workspace");
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [isLoadingFiles, setIsLoadingFiles] = useState(false);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(["/workspace"]));

  // Task progress state
  const [tasks, setTasks] = useState([
    { id: 1, text: "Set up Next.js app route project", completed: true },
    { id: 2, text: "Convert markdown documentation into Next.js pages", completed: true },
    { id: 3, text: "Apply basic styling for readability", completed: true },
    { id: 4, text: "Test the website locally", completed: true },
    { id: 5, text: "Package Next.js project for Vercel deployment excluding node_modules", completed: true },
    { id: 6, text: "Provide zip file and GitHub/Vercel deployment instructions to user", completed: true }
  ]);
  const [isTasksExpanded, setIsTasksExpanded] = useState(false);

  // Refs
  const terminalRef = useRef<HTMLDivElement>(null);

  // Load files when component mounts or path changes
  const loadFiles = useCallback(async () => {
    if (!sandboxId) return;

    setIsLoadingFiles(true);
    try {
      // Use mock API for demo purposes
      const filesData = await mockListSandboxFiles(sandboxId, currentPath);
      setFiles(filesData);

      // Add terminal output for directory listing
      setTerminalOutput(prev => [
        ...prev,
        `$ ls -la ${currentPath}`,
        ...filesData.map(file =>
          `${file.is_dir ? 'd' : '-'}rwxr-xr-x  1 ubuntu ubuntu ${file.size.toString().padStart(6)} Apr 15 12:34 ${file.name}`
        )
      ]);
    } catch (error) {
      console.error("Failed to load files:", error);
      setFiles([]);
    } finally {
      setIsLoadingFiles(false);
    }
  }, [sandboxId, currentPath]);

  // Effect to load files when component mounts
  useEffect(() => {
    if (open) {
      loadFiles();
    }
  }, [open, loadFiles]);

  // Effect to scroll terminal to bottom when output changes
  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [terminalOutput]);

  // Toggle folder expansion
  const toggleFolder = (path: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(path)) {
        newSet.delete(path);
      } else {
        newSet.add(path);
      }
      return newSet;
    });
  };

  // Add a new terminal command
  const addTerminalCommand = (command: string, output: string[] = []) => {
    setTerminalOutput(prev => [
      ...prev,
      `$ ${command}`,
      ...output
    ]);
  };

  // Get file icon based on file extension
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();

    // Return appropriate icon based on file extension
    switch (extension) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return <FileText className="h-4 w-4 mr-2 text-primary" />;
      case 'html':
        return <FileText className="h-4 w-4 mr-2 text-primary" />;
      case 'css':
      case 'scss':
      case 'sass':
        return <FileText className="h-4 w-4 mr-2 text-primary" />;
      case 'json':
        return <FileText className="h-4 w-4 mr-2 text-primary" />;
      case 'md':
        return <FileText className="h-4 w-4 mr-2 text-primary" />;
      case 'py':
        return <FileText className="h-4 w-4 mr-2 text-primary" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'svg':
        return <FileText className="h-4 w-4 mr-2 text-primary" />;
      case 'pdf':
        return <FileText className="h-4 w-4 mr-2 text-primary" />;
      default:
        return <FileText className="h-4 w-4 mr-2 text-muted-foreground" />;
    }
  };

  // Render file tree recursively
  const renderFileTree = (files: FileInfo[], level = 0) => {
    return files.map(file => (
      <div key={file.path} style={{ paddingLeft: `${level * 16}px` }}>
        <div
          className="flex items-center py-1.5 px-2 hover:bg-muted rounded cursor-pointer text-sm"
          onClick={() => {
            if (file.is_dir) {
              toggleFolder(file.path);
              if (!expandedFolders.has(file.path)) {
                setCurrentPath(file.path);
                addTerminalCommand(`cd ${file.path}`);
              }
            } else {
              // View file content
              addTerminalCommand(`cat ${file.path}`, [`Viewing file: ${file.name}`]);
            }
          }}
        >
          {file.is_dir ? (
            expandedFolders.has(file.path) ? (
              <ChevronDown className="h-4 w-4 mr-1 flex-shrink-0 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-4 w-4 mr-1 flex-shrink-0 text-muted-foreground" />
            )
          ) : (
            <div className="w-4 mr-1" />
          )}
          {file.is_dir ? (
            <Folder className="h-4 w-4 mr-2 text-primary" />
          ) : (
            getFileIcon(file.name)
          )}
          <span className="truncate">{file.name}</span>
        </div>
        {file.is_dir && expandedFolders.has(file.path) && (
          <div>
            {/* Render children recursively */}
            {renderFileTree(
              files.filter(f =>
                f.path.startsWith(file.path + '/') &&
                f.path.split('/').length === file.path.split('/').length + 1
              ),
              level + 1
            )}
          </div>
        )}
      </div>
    ));
  };

  if (!open) return null;

  return (
    <div
      className={cn(
        "fixed top-0 right-0 h-full bg-background border-l border-border z-50 flex flex-col transition-all duration-300",
        isExpanded ? "w-[500px]" : "w-[350px]"
      )}
    >
      {/* Chat header - Professional design with subtle elements */}
      <div className="bg-sidebar-accent h-[60px] px-4 flex items-center justify-between border-b border-border">
        <div className="flex items-center">
          <div className="flex-shrink-0 h-8 w-8 rounded-md overflow-hidden mr-3 bg-[#333333] flex items-center justify-center">
            <Monitor className="h-4 w-4 text-white" />
          </div>
          <div>
            <h2 className="text-white font-medium">
              {agentName}'s Computer
            </h2>
            <div className="flex items-center mt-0.5">
              <CircleDot className="h-3 w-3 text-green-500 mr-1.5" />
              <p className="text-xs text-[#999999]">
                Active
              </p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div className="flex border border-[#333333] rounded-md overflow-hidden">
            <button
              className={`h-8 w-8 flex items-center justify-center ${
                activeTab === "terminal"
                  ? 'bg-[#333333] text-white'
                  : 'bg-[#222222] text-[#999999] hover:bg-[#333333] hover:text-white'
              }`}
              onClick={() => setActiveTab("terminal")}
            >
              <Terminal className="h-4 w-4" />
            </button>
            <button
              className={`h-8 w-8 flex items-center justify-center ${
                activeTab === "files"
                  ? 'bg-[#333333] text-white'
                  : 'bg-[#222222] text-[#999999] hover:bg-[#333333] hover:text-white'
              }`}
              onClick={() => setActiveTab("files")}
            >
              <Folder className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Tabs container - No visible tabs, just content */}
      <Tabs defaultValue="terminal" value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="hidden">
          {/* Hidden tab triggers for accessibility */}
          <button role="tab" aria-selected={activeTab === "terminal"} />
          <button role="tab" aria-selected={activeTab === "files"} />
        </div>

        {/* Terminal Tab */}
        <TabsContent value="terminal" className="flex-1 overflow-hidden p-0">
          <div className="bg-[#1a1a1a] text-[#cccccc] font-mono text-sm">
            <div className="h-[49px] p-4 border-b border-[#333333] flex items-center">
              <div className="flex items-center">
                <span className="text-green-500">ubuntu@sandbox</span>:<span className="text-blue-500">~</span>$ cd /home/<USER>/doc_project
              </div>
            </div>
            <ScrollArea className="h-[300px]" ref={terminalRef}>
              <div className="p-4 whitespace-pre-wrap">
                {terminalOutput.map((line, index) => {
                  if (line.startsWith('$')) {
                    // Command line
                    return (
                      <div key={index} className="text-white">
                        {line}
                      </div>
                    );
                  } else if (line.includes('drwx')) {
                    // Directory listing
                    return (
                      <div key={index}>
                        <span className="text-blue-400">{line.substring(0, 10)}</span>
                        <span>{line.substring(10)}</span>
                      </div>
                    );
                  } else if (line.includes('-rw-')) {
                    // File listing
                    return (
                      <div key={index}>
                        <span className="text-muted-foreground">{line.substring(0, 10)}</span>
                        <span>{line.substring(10)}</span>
                      </div>
                    );
                  } else if (line.includes('{') || line.includes('}') || line.includes('\"')) {
                    // JSON content
                    return (
                      <div key={index} className="text-yellow-300">
                        {line}
                      </div>
                    );
                  } else if (line.includes('added') || line.includes('packages')) {
                    // npm output
                    return (
                      <div key={index} className="text-cyan-300">
                        {line}
                      </div>
                    );
                  } else if (line.includes('vulnerabilities') || line.includes('funding')) {
                    // npm warnings/info
                    return (
                      <div key={index} className="text-yellow-200">
                        {line}
                      </div>
                    );
                  } else {
                    // Default output
                    return (
                      <div key={index}>
                        {line}
                      </div>
                    );
                  }
                })}
              </div>
            </ScrollArea>
          </div>
        </TabsContent>

        {/* Files Tab */}
        <TabsContent value="files" className="flex-1 overflow-hidden p-0">
          <div className="bg-[#1a1a1a] text-[#cccccc]">
            <div className="h-[49px] p-4 border-b border-[#333333] flex items-center justify-between">
              <div className="text-sm font-medium">Workspace Files</div>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-[#999999] hover:text-white hover:bg-[#333333]"
                onClick={loadFiles}
              >
                <RefreshCw className="h-3.5 w-3.5 mr-1" />
                Refresh
              </Button>
            </div>
            <ScrollArea className="h-[300px]">
              <div className="p-4">
                {isLoadingFiles ? (
                  <div className="flex items-center justify-center h-20">
                    <RefreshCw className="h-5 w-5 animate-spin text-[#999999]" />
                  </div>
                ) : (
                  <div className="space-y-0.5">
                    {renderFileTree(files.filter(file => file.path.split('/').length === currentPath.split('/').length + 1))}
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </TabsContent>
      </Tabs>

      {/* Task Progress */}
      <div className="border-t border-border h-[60px] px-4 flex items-center justify-between bg-sidebar-accent">
        <div className="flex items-center">
          <h3 className="text-sm font-medium text-white">Task progress</h3>
          <div className="text-xs text-[#999999] ml-2">
            {tasks.filter(t => t.completed).length}/{tasks.length}
          </div>
        </div>
        <div
          className="text-[#999999] cursor-pointer hover:text-white"
          onClick={() => setIsTasksExpanded(!isTasksExpanded)}
        >
          {isTasksExpanded ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </div>
      </div>

      {isTasksExpanded && (
        <div className="border-t border-border bg-[#1a1a1a]">
          <div className="py-2 space-y-1">
            {tasks.map(task => (
              <div
                key={task.id}
                className="flex items-center px-4 py-2 hover:bg-[#222222] rounded-sm transition-colors duration-150"
              >
                <div className="mr-3 flex-shrink-0">
                  <svg className="w-4 h-4 text-green-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12L10 17L20 7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <div className="text-sm text-white">
                  {task.text}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
