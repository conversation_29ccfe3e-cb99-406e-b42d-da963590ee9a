export interface Participant {
  id: string;
  name: string;
  avatar: string;
  role: string;
}

export interface Message {
  id: string;
  sender: {
    id: string;
    name: string;
    avatar: string;
  };
  content: string;
  timestamp: string;
}

export type CallType = 'one-on-one' | 'group';

export interface VideoDevice {
  deviceId: string;
  label: string;
}

export interface CallInterfaceProps {
  callType: CallType;
  callName: string;
  participants: Participant[];
  onEndCall: () => void;
  onMinimize?: () => void;
}