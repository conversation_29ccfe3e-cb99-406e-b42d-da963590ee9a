'use client';

import React from 'react';
import { Code, Image as ImageIcon, CheckCircle, XCircle } from 'lucide-react';
import { ToolViewProps } from './types';
import { getToolTitle } from './utils';

export function CodeExecutionToolView({ 
  name = "execute-python-code",
  assistant<PERSON><PERSON><PERSON>, 
  toolContent,
  assistantTimestamp,
  toolTimestamp,
  isSuccess = true,
  isStreaming = false
}: ToolViewProps) {
  
  // Extract code execution results
  const extractCodeExecutionData = (content: string | null) => {
    if (!content) return null;
    
    try {
      // Look for structured code execution result
      const resultMatch = content.match(/__CODE_EXECUTION_RESULT__\n([\s\S]*?)\n__CODE_EXECUTION_RESULT_END__/);
      if (resultMatch) {
        const resultData = JSON.parse(resultMatch[1]);
        return {
          textOutput: resultData.text_output || '',
          images: resultData.images || [],
          imageCount: resultData.image_count || 0
        };
      }
      
      // Fallback: look for ToolResult pattern
      const toolResultMatch = content.match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);
      if (toolResultMatch) {
        const output = toolResultMatch[1];
        
        // Check if output mentions generated visualizations
        const imageMatch = output.match(/🖼️ Generated (\d+) visualization\(s\)/);
        if (imageMatch) {
          return {
            textOutput: output.replace(/🖼️ Generated \d+ visualization\(s\)/, '').trim(),
            images: [],
            imageCount: parseInt(imageMatch[1])
          };
        }
        
        return {
          textOutput: output,
          images: [],
          imageCount: 0
        };
      }
      
      return {
        textOutput: content,
        images: [],
        imageCount: 0
      };
    } catch (e) {
      console.error('Error parsing code execution data:', e);
      return {
        textOutput: content || '',
        images: [],
        imageCount: 0
      };
    }
  };

  // Extract the Python code from assistant content
  const extractPythonCode = (content: string | null) => {
    if (!content) return null;
    
    try {
      // Look for code blocks in markdown
      const codeBlockMatch = content.match(/```python\n([\s\S]*?)\n```/);
      if (codeBlockMatch) {
        return codeBlockMatch[1];
      }
      
      // Look for execute-python-code XML tags
      const xmlMatch = content.match(/<execute-python-code[^>]*>([\s\S]*?)<\/execute-python-code>/);
      if (xmlMatch) {
        return xmlMatch[1].trim();
      }
      
      return null;
    } catch (e) {
      console.error('Error extracting Python code:', e);
      return null;
    }
  };

  const codeExecutionData = extractCodeExecutionData(toolContent);
  const pythonCode = extractPythonCode(assistantContent);
  const toolTitle = getToolTitle(name);

  if (!codeExecutionData && !pythonCode) {
    return null;
  }

  return (
    <div className="space-y-3">
      {/* Tool Header */}
      <div className="flex items-center gap-2 text-sm">
        <div className={`p-1.5 rounded ${isSuccess ? 'bg-green-500/20' : 'bg-red-500/20'}`}>
          <Code className={`h-4 w-4 ${isSuccess ? 'text-green-400' : 'text-red-400'}`} />
        </div>
        <span className="font-medium text-white">{toolTitle}</span>
        <div className="flex items-center gap-1">
          {isSuccess ? (
            <CheckCircle className="h-3 w-3 text-green-400" />
          ) : (
            <XCircle className="h-3 w-3 text-red-400" />
          )}
          <span className={`text-xs ${isSuccess ? 'text-green-400' : 'text-red-400'}`}>
            {isSuccess ? 'Success' : 'Failed'}
          </span>
        </div>
        {toolTimestamp && (
          <span className="text-xs text-zinc-400 ml-auto">{toolTimestamp}</span>
        )}
      </div>

      {/* Python Code Display */}
      {pythonCode && (
        <div className="bg-[#1a1a1a] border border-[#333333] rounded-md">
          <div className="flex items-center gap-2 px-3 py-2 border-b border-[#333333] bg-[#222222]">
            <Code className="h-4 w-4 text-blue-400" />
            <span className="text-sm font-medium text-white">Python Code</span>
          </div>
          <div className="p-3">
            <pre className="text-sm text-[#cccccc] font-mono whitespace-pre-wrap overflow-x-auto">
              {pythonCode}
            </pre>
          </div>
        </div>
      )}

      {/* Execution Results */}
      {codeExecutionData && (
        <div className="bg-[#1a1a1a] border border-[#333333] rounded-md">
          <div className="flex items-center gap-2 px-3 py-2 border-b border-[#333333] bg-[#222222]">
            <div className={`p-1 rounded ${isSuccess ? 'bg-green-500/20' : 'bg-red-500/20'}`}>
              {isSuccess ? (
                <CheckCircle className="h-3 w-3 text-green-400" />
              ) : (
                <XCircle className="h-3 w-3 text-red-400" />
              )}
            </div>
            <span className="text-sm font-medium text-white">Execution Output</span>
            {codeExecutionData.imageCount > 0 && (
              <div className="flex items-center gap-1 ml-auto">
                <ImageIcon className="h-3 w-3 text-blue-400" />
                <span className="text-xs text-blue-400">
                  {codeExecutionData.imageCount} image{codeExecutionData.imageCount !== 1 ? 's' : ''}
                </span>
              </div>
            )}
          </div>
          
          <div className="p-3 space-y-3">
            {/* Text Output */}
            {codeExecutionData.textOutput && (
              <div>
                <pre className="text-sm text-[#cccccc] font-mono whitespace-pre-wrap bg-[#0a0a0a] p-2 rounded border border-[#333333]">
                  {codeExecutionData.textOutput}
                </pre>
              </div>
            )}
            
            {/* Generated Images */}
            {codeExecutionData.images && codeExecutionData.images.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <ImageIcon className="h-4 w-4 text-blue-400" />
                  <span className="text-sm font-medium text-white">Generated Visualizations</span>
                </div>
                <div className="grid gap-3">
                  {codeExecutionData.images.map((imageBase64: string, index: number) => (
                    <div key={index} className="bg-[#0a0a0a] border border-[#333333] rounded-md p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs text-zinc-400">Plot {index + 1}</span>
                        <button 
                          onClick={() => {
                            // Open image in new tab
                            const newWindow = window.open();
                            if (newWindow) {
                              newWindow.document.write(`
                                <html>
                                  <head><title>Plot ${index + 1}</title></head>
                                  <body style="margin:0;padding:20px;background:#000;display:flex;justify-content:center;align-items:center;min-height:100vh;">
                                    <img src="data:image/png;base64,${imageBase64}" style="max-width:100%;max-height:100%;object-fit:contain;" />
                                  </body>
                                </html>
                              `);
                            }
                          }}
                          className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          Open Full Size
                        </button>
                      </div>
                      <div className="flex justify-center">
                        <img 
                          src={`data:image/png;base64,${imageBase64}`}
                          alt={`Generated plot ${index + 1}`}
                          className="max-w-full h-auto rounded border border-[#444444]"
                          style={{ maxHeight: '400px' }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Placeholder for images that were generated but not captured */}
            {codeExecutionData.imageCount > 0 && (!codeExecutionData.images || codeExecutionData.images.length === 0) && (
              <div className="bg-[#0a0a0a] border border-[#333333] rounded-md p-3">
                <div className="flex items-center gap-2 text-blue-400">
                  <ImageIcon className="h-4 w-4" />
                  <span className="text-sm">
                    {codeExecutionData.imageCount} visualization{codeExecutionData.imageCount !== 1 ? 's' : ''} generated
                  </span>
                </div>
                <p className="text-xs text-zinc-400 mt-1">
                  Images were generated but are not yet displayed in the chat interface.
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
