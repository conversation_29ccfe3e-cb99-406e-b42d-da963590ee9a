-- AGENT MEMORY SCHEMA

-- Create agent_memories table to store agent memories
CREATE TABLE agent_memories (
    memory_id UUID PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(agent_id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    memory_type TEXT NOT NULL,
    priority TEXT NOT NULL,
    context JSONB DEFAULT '{}'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    last_accessed TIMESTAMP WITH TIME ZONE,
    access_count INTEGER DEFAULT 0,
    expiration TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create index on agent_id for faster memory retrieval
CREATE INDEX agent_memories_agent_id_idx ON agent_memories(agent_id);

-- Create index on memory_type for faster filtering
CREATE INDEX agent_memories_memory_type_idx ON agent_memories(memory_type);

-- Create index on priority for faster filtering
CREATE INDEX agent_memories_priority_idx ON agent_memories(priority);

-- Create index on access_count for faster retrieval of frequently accessed memories
CREATE INDEX agent_memories_access_count_idx ON agent_memories(access_count DESC);

-- Create index on last_accessed for faster retrieval of recently accessed memories
CREATE INDEX agent_memories_last_accessed_idx ON agent_memories(last_accessed DESC);

-- Create index on created_at for faster retrieval of recent memories
CREATE INDEX agent_memories_created_at_idx ON agent_memories(created_at DESC);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_agent_memories_updated_at
BEFORE UPDATE ON agent_memories
FOR EACH ROW
EXECUTE FUNCTION update_agent_updated_at();

-- Create function to search memories by content
CREATE OR REPLACE FUNCTION search_agent_memories(
    p_agent_id UUID,
    p_query TEXT,
    p_memory_type TEXT DEFAULT NULL,
    p_min_priority TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 10
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    memories_array JSONB;
BEGIN
    -- Build query based on parameters
    WITH filtered_memories AS (
        SELECT *
        FROM agent_memories
        WHERE agent_id = p_agent_id
        AND (p_memory_type IS NULL OR memory_type = p_memory_type)
        AND (p_min_priority IS NULL OR priority = p_min_priority)
        AND content ILIKE '%' || p_query || '%'
        ORDER BY 
            CASE 
                WHEN priority = 'critical' THEN 4
                WHEN priority = 'high' THEN 3
                WHEN priority = 'medium' THEN 2
                WHEN priority = 'low' THEN 1
                ELSE 0
            END DESC,
            created_at DESC
        LIMIT p_limit
    )
    SELECT JSONB_AGG(row_to_json(filtered_memories))
    INTO memories_array
    FROM filtered_memories;
    
    -- Handle the case when no memories are found
    IF memories_array IS NULL THEN
        RETURN '[]'::JSONB;
    END IF;
    
    RETURN memories_array;
END;
$$;

-- Create function to get recent memories
CREATE OR REPLACE FUNCTION get_recent_agent_memories(
    p_agent_id UUID,
    p_memory_type TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 10
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    memories_array JSONB;
BEGIN
    -- Build query based on parameters
    WITH filtered_memories AS (
        SELECT *
        FROM agent_memories
        WHERE agent_id = p_agent_id
        AND (p_memory_type IS NULL OR memory_type = p_memory_type)
        ORDER BY created_at DESC
        LIMIT p_limit
    )
    SELECT JSONB_AGG(row_to_json(filtered_memories))
    INTO memories_array
    FROM filtered_memories;
    
    -- Handle the case when no memories are found
    IF memories_array IS NULL THEN
        RETURN '[]'::JSONB;
    END IF;
    
    RETURN memories_array;
END;
$$;

-- Create function to get important memories
CREATE OR REPLACE FUNCTION get_important_agent_memories(
    p_agent_id UUID,
    p_limit INTEGER DEFAULT 10
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    memories_array JSONB;
BEGIN
    -- Get high and critical priority memories
    WITH important_memories AS (
        SELECT *
        FROM agent_memories
        WHERE agent_id = p_agent_id
        AND priority IN ('high', 'critical')
        ORDER BY 
            CASE 
                WHEN priority = 'critical' THEN 2
                WHEN priority = 'high' THEN 1
                ELSE 0
            END DESC,
            created_at DESC
        LIMIT p_limit
    )
    SELECT JSONB_AGG(row_to_json(important_memories))
    INTO memories_array
    FROM important_memories;
    
    -- Handle the case when no memories are found
    IF memories_array IS NULL THEN
        RETURN '[]'::JSONB;
    END IF;
    
    RETURN memories_array;
END;
$$;

-- Create function to get frequently accessed memories
CREATE OR REPLACE FUNCTION get_frequently_accessed_agent_memories(
    p_agent_id UUID,
    p_limit INTEGER DEFAULT 10
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    memories_array JSONB;
BEGIN
    -- Get memories with high access count
    WITH frequent_memories AS (
        SELECT *
        FROM agent_memories
        WHERE agent_id = p_agent_id
        AND access_count > 0
        ORDER BY access_count DESC, last_accessed DESC
        LIMIT p_limit
    )
    SELECT JSONB_AGG(row_to_json(frequent_memories))
    INTO memories_array
    FROM frequent_memories;
    
    -- Handle the case when no memories are found
    IF memories_array IS NULL THEN
        RETURN '[]'::JSONB;
    END IF;
    
    RETURN memories_array;
END;
$$;
