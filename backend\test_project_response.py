#!/usr/bin/env python3
"""
Test script to show what the project API response looks like
"""

# Simulate what gets stored in database
database_project = {
    "project_id": "test-123",
    "name": "Test Project",
    "description": "Test Description",
    "account_id": "user-456",
    "sandbox": {
        "knowledgeAccess": False,
        "memoryPersistence": False,
        "autonomousMode": False,
        "sandboxMode": True,
        "integrations": [],
        "selectedAgents": ["<PERSON>ard", "<PERSON>", "<PERSON>"],
        "agent_names": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>"],
        "companyInfo": "My Company",
        "settings": {"theme": "dark"},
        "metadata": {"custom": "data"}
    },
    "is_public": False
}

# Simulate what gets returned to frontend (the extraction logic)
def process_project_for_frontend(project):
    # Add id field for frontend compatibility
    project["id"] = project["project_id"]
    
    # Extract agent data from sandbox for frontend compatibility
    if project.get("sandbox"):
        sandbox = project["sandbox"]
        project["selectedAgents"] = sandbox.get("selectedAgents", ["Kenard"])
        project["agent_names"] = sandbox.get("agent_names", ["Kenard"])
        project["companyInfo"] = sandbox.get("companyInfo")
        project["settings"] = sandbox.get("settings", {})
        project["metadata"] = sandbox.get("metadata", {})
    
    return project

# Test the transformation
frontend_project = process_project_for_frontend(database_project.copy())

print("=== WHAT'S STORED IN DATABASE ===")
print(f"selectedAgents in sandbox: {database_project['sandbox']['selectedAgents']}")
print(f"agent_names in sandbox: {database_project['sandbox']['agent_names']}")

print("\n=== WHAT FRONTEND RECEIVES ===")
print(f"project.id: {frontend_project.get('id')}")
print(f"project.selectedAgents: {frontend_project.get('selectedAgents')}")
print(f"project.agent_names: {frontend_project.get('agent_names')}")
print(f"project.companyInfo: {frontend_project.get('companyInfo')}")
print(f"project.settings: {frontend_project.get('settings')}")

print("\n=== FRONTEND CAN ACCESS AGENTS LIKE BEFORE ===")
print("✅ Your chat interface can still do: project.selectedAgents")
print("✅ Your chat interface can still do: project.agent_names")
print("✅ All agent selection functionality preserved!")
