
import { siteConfig } from "@/lib/site";
import type { Metada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";
import { Toaster } from "@/components/ui/sonner";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const viewport: Viewport = {
  themeColor: "black",
};

export const metadata: Metadata = {
  metadataBase: new URL(siteConfig.url),
  title: {
    default: siteConfig.name,
    template: `%s - ${siteConfig.name}`,
  },
  description: "Siden.ai creates teams of specialized AI agents that work together to solve complex business problems. Our agents collaborate autonomously while maintaining persistent memory.",
  keywords: ["AI agents", "artificial intelligence", "business automation", "collaborative AI", "agent teams", "autonomous agents", "AI tools", "developer agent", "marketing agent", "research agent"],
  authors: [{ name: "Siden AI Team", url: "https://siden.ai" }],
  creator: "Siden",
  publisher: "Siden",
  category: "Technology",
  applicationName: "Siden",
  formatDetection: {
    telephone: false,
    email: false,
    address: false,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  },
  openGraph: {
    title: "Siden AI - Teams of Collaborative AI Agents",
    description: "Siden.ai creates teams of specialized AI agents that work together to solve complex business problems. Our agents collaborate autonomously while maintaining persistent memory.",
    url: siteConfig.url,
    siteName: "Siden AI",
    images: [{
      url: "/banner.png",
      width: 1200,
      height: 630,
      alt: "Siden AI - Teams of Collaborative AI Agents",
      type: "image/png",
    }],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Siden AI - Teams of Collaborative AI Agents",
    description: "Siden.ai creates teams of specialized AI agents that work together to solve complex business problems. Our agents collaborate autonomously while maintaining persistent memory.",
    creator: "@sidenai",
    site: "@sidenai",
    images: [{
      url: "/banner.png",
      width: 1200,
      height: 630,
      alt: "Siden AI - Teams of Collaborative AI Agents",
    }],
  },
  icons: {
    icon: [
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
      { url: "/android-chrome-192x192.png", sizes: "192x192", type: "image/png" },
      { url: "/android-chrome-512x512.png", sizes: "512x512", type: "image/png" },
    ],
    shortcut: "/favicon.ico",
    apple: { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" },
  },
  manifest: "/site.webmanifest",
  alternates: {
    canonical: siteConfig.url,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Google Tag Manager */}
      </head>

      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased font-sans bg-background`}
      >

          <Providers>
            {children}
            <Toaster />
          </Providers>
      </body>
    </html>
  );
}
