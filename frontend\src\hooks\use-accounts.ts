import useSWR, {SWRConfiguration} from "swr";
import { createClient } from "@/lib/supabase/client";
import { GetAccountsResponse } from "@usebasejump/shared";

// Mock data for development
const mockAccounts: GetAccountsResponse = [
    {
        account_id: "mock-personal-account-id",
        account_role: "owner",
        is_primary_owner: true,
        name: "Personal Account",
        slug: "personal",
        personal_account: true,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
    },
    {
        account_id: "mock-team-account-id",
        account_role: "owner",
        is_primary_owner: true,
        name: "Team Account",
        slug: "team",
        personal_account: false,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z"
    }
];

export const useAccounts = (options?: SWRConfiguration) => {
    const supabaseClient = createClient();

    // For now, return mock data to avoid API calls
    return useSWR<GetAccountsResponse>(
        ["accounts"],
        async () => {
            // Return mock data immediately
            return mockAccounts;

            // Commented out real API call for now:
            // const {data, error} = await supabaseClient.rpc("get_accounts");
            // if (error) {
            //     throw new Error(error.message);
            // }
            // return data;
        },
        options
    );
};