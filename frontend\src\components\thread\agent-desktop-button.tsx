"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Monitor } from "lucide-react";
import { AgentDesktopView } from "./agent-desktop-view";
import { Project } from "@/lib/api";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface AgentDesktopButtonProps {
  sandboxId: string;
  project?: Project;
  variant?: "icon" | "default";
  size?: "sm" | "default";
  className?: string;
}

export function AgentDesktopButton({
  sandboxId,
  project,
  variant = "default",
  size = "default",
  className
}: AgentDesktopButtonProps) {
  const [isDesktopViewOpen, setIsDesktopViewOpen] = useState(false);
  
  const handleOpenDesktop = () => {
    setIsDesktopViewOpen(true);
  };
  
  return (
    <>
      {variant === "icon" ? (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={handleOpenDesktop}
                className={className}
              >
                <Monitor className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>View Agent's Computer</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ) : (
        <Button
          variant="outline"
          size={size}
          onClick={handleOpenDesktop}
          className={className}
        >
          <Monitor className="h-4 w-4 mr-2" />
          View Computer
        </Button>
      )}
      
      <AgentDesktopView
        open={isDesktopViewOpen}
        onOpenChange={setIsDesktopViewOpen}
        sandboxId={sandboxId}
        project={project}
        agentName={project?.name || "Agent"}
      />
    </>
  );
}
