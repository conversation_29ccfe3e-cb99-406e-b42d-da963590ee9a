"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+language@6.11.0";
exports.ids = ["vendor-chunks/@codemirror+language@6.11.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocInput: () => (/* binding */ DocInput),\n/* harmony export */   HighlightStyle: () => (/* binding */ HighlightStyle),\n/* harmony export */   IndentContext: () => (/* binding */ IndentContext),\n/* harmony export */   LRLanguage: () => (/* binding */ LRLanguage),\n/* harmony export */   Language: () => (/* binding */ Language),\n/* harmony export */   LanguageDescription: () => (/* binding */ LanguageDescription),\n/* harmony export */   LanguageSupport: () => (/* binding */ LanguageSupport),\n/* harmony export */   ParseContext: () => (/* binding */ ParseContext),\n/* harmony export */   StreamLanguage: () => (/* binding */ StreamLanguage),\n/* harmony export */   StringStream: () => (/* binding */ StringStream),\n/* harmony export */   TreeIndentContext: () => (/* binding */ TreeIndentContext),\n/* harmony export */   bidiIsolates: () => (/* binding */ bidiIsolates),\n/* harmony export */   bracketMatching: () => (/* binding */ bracketMatching),\n/* harmony export */   bracketMatchingHandle: () => (/* binding */ bracketMatchingHandle),\n/* harmony export */   codeFolding: () => (/* binding */ codeFolding),\n/* harmony export */   continuedIndent: () => (/* binding */ continuedIndent),\n/* harmony export */   defaultHighlightStyle: () => (/* binding */ defaultHighlightStyle),\n/* harmony export */   defineLanguageFacet: () => (/* binding */ defineLanguageFacet),\n/* harmony export */   delimitedIndent: () => (/* binding */ delimitedIndent),\n/* harmony export */   ensureSyntaxTree: () => (/* binding */ ensureSyntaxTree),\n/* harmony export */   flatIndent: () => (/* binding */ flatIndent),\n/* harmony export */   foldAll: () => (/* binding */ foldAll),\n/* harmony export */   foldCode: () => (/* binding */ foldCode),\n/* harmony export */   foldEffect: () => (/* binding */ foldEffect),\n/* harmony export */   foldGutter: () => (/* binding */ foldGutter),\n/* harmony export */   foldInside: () => (/* binding */ foldInside),\n/* harmony export */   foldKeymap: () => (/* binding */ foldKeymap),\n/* harmony export */   foldNodeProp: () => (/* binding */ foldNodeProp),\n/* harmony export */   foldService: () => (/* binding */ foldService),\n/* harmony export */   foldState: () => (/* binding */ foldState),\n/* harmony export */   foldable: () => (/* binding */ foldable),\n/* harmony export */   foldedRanges: () => (/* binding */ foldedRanges),\n/* harmony export */   forceParsing: () => (/* binding */ forceParsing),\n/* harmony export */   getIndentUnit: () => (/* binding */ getIndentUnit),\n/* harmony export */   getIndentation: () => (/* binding */ getIndentation),\n/* harmony export */   highlightingFor: () => (/* binding */ highlightingFor),\n/* harmony export */   indentNodeProp: () => (/* binding */ indentNodeProp),\n/* harmony export */   indentOnInput: () => (/* binding */ indentOnInput),\n/* harmony export */   indentRange: () => (/* binding */ indentRange),\n/* harmony export */   indentService: () => (/* binding */ indentService),\n/* harmony export */   indentString: () => (/* binding */ indentString),\n/* harmony export */   indentUnit: () => (/* binding */ indentUnit),\n/* harmony export */   language: () => (/* binding */ language),\n/* harmony export */   languageDataProp: () => (/* binding */ languageDataProp),\n/* harmony export */   matchBrackets: () => (/* binding */ matchBrackets),\n/* harmony export */   sublanguageProp: () => (/* binding */ sublanguageProp),\n/* harmony export */   syntaxHighlighting: () => (/* binding */ syntaxHighlighting),\n/* harmony export */   syntaxParserRunning: () => (/* binding */ syntaxParserRunning),\n/* harmony export */   syntaxTree: () => (/* binding */ syntaxTree),\n/* harmony export */   syntaxTreeAvailable: () => (/* binding */ syntaxTreeAvailable),\n/* harmony export */   toggleFold: () => (/* binding */ toggleFold),\n/* harmony export */   unfoldAll: () => (/* binding */ unfoldAll),\n/* harmony export */   unfoldCode: () => (/* binding */ unfoldCode),\n/* harmony export */   unfoldEffect: () => (/* binding */ unfoldEffect)\n/* harmony export */ });\n/* harmony import */ var _lezer_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/common */ \"(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lezer/highlight */ \"(ssr)/./node_modules/.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js\");\n/* harmony import */ var style_mod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! style-mod */ \"(ssr)/./node_modules/.pnpm/style-mod@4.1.2/node_modules/style-mod/src/style-mod.js\");\n\n\n\n\n\n\nvar _a;\n/**\nNode prop stored in a parser's top syntax node to provide the\nfacet that stores language-specific data for that language.\n*/\nconst languageDataProp = /*@__PURE__*/new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp();\n/**\nHelper function to define a facet (to be added to the top syntax\nnode(s) for a language via\n[`languageDataProp`](https://codemirror.net/6/docs/ref/#language.languageDataProp)), that will be\nused to associate language data with the language. You\nprobably only need this when subclassing\n[`Language`](https://codemirror.net/6/docs/ref/#language.Language).\n*/\nfunction defineLanguageFacet(baseData) {\n    return _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Facet.define({\n        combine: baseData ? values => values.concat(baseData) : undefined\n    });\n}\n/**\nSyntax node prop used to register sublanguages. Should be added to\nthe top level node type for the language.\n*/\nconst sublanguageProp = /*@__PURE__*/new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp();\n/**\nA language object manages parsing and per-language\n[metadata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt). Parse data is\nmanaged as a [Lezer](https://lezer.codemirror.net) tree. The class\ncan be used directly, via the [`LRLanguage`](https://codemirror.net/6/docs/ref/#language.LRLanguage)\nsubclass for [Lezer](https://lezer.codemirror.net/) LR parsers, or\nvia the [`StreamLanguage`](https://codemirror.net/6/docs/ref/#language.StreamLanguage) subclass\nfor stream parsers.\n*/\nclass Language {\n    /**\n    Construct a language object. If you need to invoke this\n    directly, first define a data facet with\n    [`defineLanguageFacet`](https://codemirror.net/6/docs/ref/#language.defineLanguageFacet), and then\n    configure your parser to [attach](https://codemirror.net/6/docs/ref/#language.languageDataProp) it\n    to the language's outer syntax node.\n    */\n    constructor(\n    /**\n    The [language data](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt) facet\n    used for this language.\n    */\n    data, parser, extraExtensions = [], \n    /**\n    A language name.\n    */\n    name = \"\") {\n        this.data = data;\n        this.name = name;\n        // Kludge to define EditorState.tree as a debugging helper,\n        // without the EditorState package actually knowing about\n        // languages and lezer trees.\n        if (!_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState.prototype.hasOwnProperty(\"tree\"))\n            Object.defineProperty(_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState.prototype, \"tree\", { get() { return syntaxTree(this); } });\n        this.parser = parser;\n        this.extension = [\n            language.of(this),\n            _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState.languageData.of((state, pos, side) => {\n                let top = topNodeAt(state, pos, side), data = top.type.prop(languageDataProp);\n                if (!data)\n                    return [];\n                let base = state.facet(data), sub = top.type.prop(sublanguageProp);\n                if (sub) {\n                    let innerNode = top.resolve(pos - top.from, side);\n                    for (let sublang of sub)\n                        if (sublang.test(innerNode, state)) {\n                            let data = state.facet(sublang.facet);\n                            return sublang.type == \"replace\" ? data : data.concat(base);\n                        }\n                }\n                return base;\n            })\n        ].concat(extraExtensions);\n    }\n    /**\n    Query whether this language is active at the given position.\n    */\n    isActiveAt(state, pos, side = -1) {\n        return topNodeAt(state, pos, side).type.prop(languageDataProp) == this.data;\n    }\n    /**\n    Find the document regions that were parsed using this language.\n    The returned regions will _include_ any nested languages rooted\n    in this language, when those exist.\n    */\n    findRegions(state) {\n        let lang = state.facet(language);\n        if ((lang === null || lang === void 0 ? void 0 : lang.data) == this.data)\n            return [{ from: 0, to: state.doc.length }];\n        if (!lang || !lang.allowsNesting)\n            return [];\n        let result = [];\n        let explore = (tree, from) => {\n            if (tree.prop(languageDataProp) == this.data) {\n                result.push({ from, to: from + tree.length });\n                return;\n            }\n            let mount = tree.prop(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.mounted);\n            if (mount) {\n                if (mount.tree.prop(languageDataProp) == this.data) {\n                    if (mount.overlay)\n                        for (let r of mount.overlay)\n                            result.push({ from: r.from + from, to: r.to + from });\n                    else\n                        result.push({ from: from, to: from + tree.length });\n                    return;\n                }\n                else if (mount.overlay) {\n                    let size = result.length;\n                    explore(mount.tree, mount.overlay[0].from + from);\n                    if (result.length > size)\n                        return;\n                }\n            }\n            for (let i = 0; i < tree.children.length; i++) {\n                let ch = tree.children[i];\n                if (ch instanceof _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree)\n                    explore(ch, tree.positions[i] + from);\n            }\n        };\n        explore(syntaxTree(state), 0);\n        return result;\n    }\n    /**\n    Indicates whether this language allows nested languages. The\n    default implementation returns true.\n    */\n    get allowsNesting() { return true; }\n}\n/**\n@internal\n*/\nLanguage.setState = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.StateEffect.define();\nfunction topNodeAt(state, pos, side) {\n    let topLang = state.facet(language), tree = syntaxTree(state).topNode;\n    if (!topLang || topLang.allowsNesting) {\n        for (let node = tree; node; node = node.enter(pos, side, _lezer_common__WEBPACK_IMPORTED_MODULE_0__.IterMode.ExcludeBuffers))\n            if (node.type.isTop)\n                tree = node;\n    }\n    return tree;\n}\n/**\nA subclass of [`Language`](https://codemirror.net/6/docs/ref/#language.Language) for use with Lezer\n[LR parsers](https://lezer.codemirror.net/docs/ref#lr.LRParser)\nparsers.\n*/\nclass LRLanguage extends Language {\n    constructor(data, parser, name) {\n        super(data, parser, [], name);\n        this.parser = parser;\n    }\n    /**\n    Define a language from a parser.\n    */\n    static define(spec) {\n        let data = defineLanguageFacet(spec.languageData);\n        return new LRLanguage(data, spec.parser.configure({\n            props: [languageDataProp.add(type => type.isTop ? data : undefined)]\n        }), spec.name);\n    }\n    /**\n    Create a new instance of this language with a reconfigured\n    version of its parser and optionally a new name.\n    */\n    configure(options, name) {\n        return new LRLanguage(this.data, this.parser.configure(options), name || this.name);\n    }\n    get allowsNesting() { return this.parser.hasWrappers(); }\n}\n/**\nGet the syntax tree for a state, which is the current (possibly\nincomplete) parse tree of the active\n[language](https://codemirror.net/6/docs/ref/#language.Language), or the empty tree if there is no\nlanguage available.\n*/\nfunction syntaxTree(state) {\n    let field = state.field(Language.state, false);\n    return field ? field.tree : _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree.empty;\n}\n/**\nTry to get a parse tree that spans at least up to `upto`. The\nmethod will do at most `timeout` milliseconds of work to parse\nup to that point if the tree isn't already available.\n*/\nfunction ensureSyntaxTree(state, upto, timeout = 50) {\n    var _a;\n    let parse = (_a = state.field(Language.state, false)) === null || _a === void 0 ? void 0 : _a.context;\n    if (!parse)\n        return null;\n    let oldVieport = parse.viewport;\n    parse.updateViewport({ from: 0, to: upto });\n    let result = parse.isDone(upto) || parse.work(timeout, upto) ? parse.tree : null;\n    parse.updateViewport(oldVieport);\n    return result;\n}\n/**\nQueries whether there is a full syntax tree available up to the\ngiven document position. If there isn't, the background parse\nprocess _might_ still be working and update the tree further, but\nthere is no guarantee of that—the parser will [stop\nworking](https://codemirror.net/6/docs/ref/#language.syntaxParserRunning) when it has spent a\ncertain amount of time or has moved beyond the visible viewport.\nAlways returns false if no language has been enabled.\n*/\nfunction syntaxTreeAvailable(state, upto = state.doc.length) {\n    var _a;\n    return ((_a = state.field(Language.state, false)) === null || _a === void 0 ? void 0 : _a.context.isDone(upto)) || false;\n}\n/**\nMove parsing forward, and update the editor state afterwards to\nreflect the new tree. Will work for at most `timeout`\nmilliseconds. Returns true if the parser managed get to the given\nposition in that time.\n*/\nfunction forceParsing(view, upto = view.viewport.to, timeout = 100) {\n    let success = ensureSyntaxTree(view.state, upto, timeout);\n    if (success != syntaxTree(view.state))\n        view.dispatch({});\n    return !!success;\n}\n/**\nTells you whether the language parser is planning to do more\nparsing work (in a `requestIdleCallback` pseudo-thread) or has\nstopped running, either because it parsed the entire document,\nbecause it spent too much time and was cut off, or because there\nis no language parser enabled.\n*/\nfunction syntaxParserRunning(view) {\n    var _a;\n    return ((_a = view.plugin(parseWorker)) === null || _a === void 0 ? void 0 : _a.isWorking()) || false;\n}\n/**\nLezer-style\n[`Input`](https://lezer.codemirror.net/docs/ref#common.Input)\nobject for a [`Text`](https://codemirror.net/6/docs/ref/#state.Text) object.\n*/\nclass DocInput {\n    /**\n    Create an input object for the given document.\n    */\n    constructor(doc) {\n        this.doc = doc;\n        this.cursorPos = 0;\n        this.string = \"\";\n        this.cursor = doc.iter();\n    }\n    get length() { return this.doc.length; }\n    syncTo(pos) {\n        this.string = this.cursor.next(pos - this.cursorPos).value;\n        this.cursorPos = pos + this.string.length;\n        return this.cursorPos - this.string.length;\n    }\n    chunk(pos) {\n        this.syncTo(pos);\n        return this.string;\n    }\n    get lineChunks() { return true; }\n    read(from, to) {\n        let stringStart = this.cursorPos - this.string.length;\n        if (from < stringStart || to >= this.cursorPos)\n            return this.doc.sliceString(from, to);\n        else\n            return this.string.slice(from - stringStart, to - stringStart);\n    }\n}\nlet currentContext = null;\n/**\nA parse context provided to parsers working on the editor content.\n*/\nclass ParseContext {\n    constructor(parser, \n    /**\n    The current editor state.\n    */\n    state, \n    /**\n    Tree fragments that can be reused by incremental re-parses.\n    */\n    fragments = [], \n    /**\n    @internal\n    */\n    tree, \n    /**\n    @internal\n    */\n    treeLen, \n    /**\n    The current editor viewport (or some overapproximation\n    thereof). Intended to be used for opportunistically avoiding\n    work (in which case\n    [`skipUntilInView`](https://codemirror.net/6/docs/ref/#language.ParseContext.skipUntilInView)\n    should be called to make sure the parser is restarted when the\n    skipped region becomes visible).\n    */\n    viewport, \n    /**\n    @internal\n    */\n    skipped, \n    /**\n    This is where skipping parsers can register a promise that,\n    when resolved, will schedule a new parse. It is cleared when\n    the parse worker picks up the promise. @internal\n    */\n    scheduleOn) {\n        this.parser = parser;\n        this.state = state;\n        this.fragments = fragments;\n        this.tree = tree;\n        this.treeLen = treeLen;\n        this.viewport = viewport;\n        this.skipped = skipped;\n        this.scheduleOn = scheduleOn;\n        this.parse = null;\n        /**\n        @internal\n        */\n        this.tempSkipped = [];\n    }\n    /**\n    @internal\n    */\n    static create(parser, state, viewport) {\n        return new ParseContext(parser, state, [], _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree.empty, 0, viewport, [], null);\n    }\n    startParse() {\n        return this.parser.startParse(new DocInput(this.state.doc), this.fragments);\n    }\n    /**\n    @internal\n    */\n    work(until, upto) {\n        if (upto != null && upto >= this.state.doc.length)\n            upto = undefined;\n        if (this.tree != _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree.empty && this.isDone(upto !== null && upto !== void 0 ? upto : this.state.doc.length)) {\n            this.takeTree();\n            return true;\n        }\n        return this.withContext(() => {\n            var _a;\n            if (typeof until == \"number\") {\n                let endTime = Date.now() + until;\n                until = () => Date.now() > endTime;\n            }\n            if (!this.parse)\n                this.parse = this.startParse();\n            if (upto != null && (this.parse.stoppedAt == null || this.parse.stoppedAt > upto) &&\n                upto < this.state.doc.length)\n                this.parse.stopAt(upto);\n            for (;;) {\n                let done = this.parse.advance();\n                if (done) {\n                    this.fragments = this.withoutTempSkipped(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.TreeFragment.addTree(done, this.fragments, this.parse.stoppedAt != null));\n                    this.treeLen = (_a = this.parse.stoppedAt) !== null && _a !== void 0 ? _a : this.state.doc.length;\n                    this.tree = done;\n                    this.parse = null;\n                    if (this.treeLen < (upto !== null && upto !== void 0 ? upto : this.state.doc.length))\n                        this.parse = this.startParse();\n                    else\n                        return true;\n                }\n                if (until())\n                    return false;\n            }\n        });\n    }\n    /**\n    @internal\n    */\n    takeTree() {\n        let pos, tree;\n        if (this.parse && (pos = this.parse.parsedPos) >= this.treeLen) {\n            if (this.parse.stoppedAt == null || this.parse.stoppedAt > pos)\n                this.parse.stopAt(pos);\n            this.withContext(() => { while (!(tree = this.parse.advance())) { } });\n            this.treeLen = pos;\n            this.tree = tree;\n            this.fragments = this.withoutTempSkipped(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.TreeFragment.addTree(this.tree, this.fragments, true));\n            this.parse = null;\n        }\n    }\n    withContext(f) {\n        let prev = currentContext;\n        currentContext = this;\n        try {\n            return f();\n        }\n        finally {\n            currentContext = prev;\n        }\n    }\n    withoutTempSkipped(fragments) {\n        for (let r; r = this.tempSkipped.pop();)\n            fragments = cutFragments(fragments, r.from, r.to);\n        return fragments;\n    }\n    /**\n    @internal\n    */\n    changes(changes, newState) {\n        let { fragments, tree, treeLen, viewport, skipped } = this;\n        this.takeTree();\n        if (!changes.empty) {\n            let ranges = [];\n            changes.iterChangedRanges((fromA, toA, fromB, toB) => ranges.push({ fromA, toA, fromB, toB }));\n            fragments = _lezer_common__WEBPACK_IMPORTED_MODULE_0__.TreeFragment.applyChanges(fragments, ranges);\n            tree = _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree.empty;\n            treeLen = 0;\n            viewport = { from: changes.mapPos(viewport.from, -1), to: changes.mapPos(viewport.to, 1) };\n            if (this.skipped.length) {\n                skipped = [];\n                for (let r of this.skipped) {\n                    let from = changes.mapPos(r.from, 1), to = changes.mapPos(r.to, -1);\n                    if (from < to)\n                        skipped.push({ from, to });\n                }\n            }\n        }\n        return new ParseContext(this.parser, newState, fragments, tree, treeLen, viewport, skipped, this.scheduleOn);\n    }\n    /**\n    @internal\n    */\n    updateViewport(viewport) {\n        if (this.viewport.from == viewport.from && this.viewport.to == viewport.to)\n            return false;\n        this.viewport = viewport;\n        let startLen = this.skipped.length;\n        for (let i = 0; i < this.skipped.length; i++) {\n            let { from, to } = this.skipped[i];\n            if (from < viewport.to && to > viewport.from) {\n                this.fragments = cutFragments(this.fragments, from, to);\n                this.skipped.splice(i--, 1);\n            }\n        }\n        if (this.skipped.length >= startLen)\n            return false;\n        this.reset();\n        return true;\n    }\n    /**\n    @internal\n    */\n    reset() {\n        if (this.parse) {\n            this.takeTree();\n            this.parse = null;\n        }\n    }\n    /**\n    Notify the parse scheduler that the given region was skipped\n    because it wasn't in view, and the parse should be restarted\n    when it comes into view.\n    */\n    skipUntilInView(from, to) {\n        this.skipped.push({ from, to });\n    }\n    /**\n    Returns a parser intended to be used as placeholder when\n    asynchronously loading a nested parser. It'll skip its input and\n    mark it as not-really-parsed, so that the next update will parse\n    it again.\n    \n    When `until` is given, a reparse will be scheduled when that\n    promise resolves.\n    */\n    static getSkippingParser(until) {\n        return new class extends _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Parser {\n            createParse(input, fragments, ranges) {\n                let from = ranges[0].from, to = ranges[ranges.length - 1].to;\n                let parser = {\n                    parsedPos: from,\n                    advance() {\n                        let cx = currentContext;\n                        if (cx) {\n                            for (let r of ranges)\n                                cx.tempSkipped.push(r);\n                            if (until)\n                                cx.scheduleOn = cx.scheduleOn ? Promise.all([cx.scheduleOn, until]) : until;\n                        }\n                        this.parsedPos = to;\n                        return new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeType.none, [], [], to - from);\n                    },\n                    stoppedAt: null,\n                    stopAt() { }\n                };\n                return parser;\n            }\n        };\n    }\n    /**\n    @internal\n    */\n    isDone(upto) {\n        upto = Math.min(upto, this.state.doc.length);\n        let frags = this.fragments;\n        return this.treeLen >= upto && frags.length && frags[0].from == 0 && frags[0].to >= upto;\n    }\n    /**\n    Get the context for the current parse, or `null` if no editor\n    parse is in progress.\n    */\n    static get() { return currentContext; }\n}\nfunction cutFragments(fragments, from, to) {\n    return _lezer_common__WEBPACK_IMPORTED_MODULE_0__.TreeFragment.applyChanges(fragments, [{ fromA: from, toA: to, fromB: from, toB: to }]);\n}\nclass LanguageState {\n    constructor(\n    // A mutable parse state that is used to preserve work done during\n    // the lifetime of a state when moving to the next state.\n    context) {\n        this.context = context;\n        this.tree = context.tree;\n    }\n    apply(tr) {\n        if (!tr.docChanged && this.tree == this.context.tree)\n            return this;\n        let newCx = this.context.changes(tr.changes, tr.state);\n        // If the previous parse wasn't done, go forward only up to its\n        // end position or the end of the viewport, to avoid slowing down\n        // state updates with parse work beyond the viewport.\n        let upto = this.context.treeLen == tr.startState.doc.length ? undefined\n            : Math.max(tr.changes.mapPos(this.context.treeLen), newCx.viewport.to);\n        if (!newCx.work(20 /* Work.Apply */, upto))\n            newCx.takeTree();\n        return new LanguageState(newCx);\n    }\n    static init(state) {\n        let vpTo = Math.min(3000 /* Work.InitViewport */, state.doc.length);\n        let parseState = ParseContext.create(state.facet(language).parser, state, { from: 0, to: vpTo });\n        if (!parseState.work(20 /* Work.Apply */, vpTo))\n            parseState.takeTree();\n        return new LanguageState(parseState);\n    }\n}\nLanguage.state = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.StateField.define({\n    create: LanguageState.init,\n    update(value, tr) {\n        for (let e of tr.effects)\n            if (e.is(Language.setState))\n                return e.value;\n        if (tr.startState.facet(language) != tr.state.facet(language))\n            return LanguageState.init(tr.state);\n        return value.apply(tr);\n    }\n});\nlet requestIdle = (callback) => {\n    let timeout = setTimeout(() => callback(), 500 /* Work.MaxPause */);\n    return () => clearTimeout(timeout);\n};\nif (typeof requestIdleCallback != \"undefined\")\n    requestIdle = (callback) => {\n        let idle = -1, timeout = setTimeout(() => {\n            idle = requestIdleCallback(callback, { timeout: 500 /* Work.MaxPause */ - 100 /* Work.MinPause */ });\n        }, 100 /* Work.MinPause */);\n        return () => idle < 0 ? clearTimeout(timeout) : cancelIdleCallback(idle);\n    };\nconst isInputPending = typeof navigator != \"undefined\" && ((_a = navigator.scheduling) === null || _a === void 0 ? void 0 : _a.isInputPending)\n    ? () => navigator.scheduling.isInputPending() : null;\nconst parseWorker = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.ViewPlugin.fromClass(class ParseWorker {\n    constructor(view) {\n        this.view = view;\n        this.working = null;\n        this.workScheduled = 0;\n        // End of the current time chunk\n        this.chunkEnd = -1;\n        // Milliseconds of budget left for this chunk\n        this.chunkBudget = -1;\n        this.work = this.work.bind(this);\n        this.scheduleWork();\n    }\n    update(update) {\n        let cx = this.view.state.field(Language.state).context;\n        if (cx.updateViewport(update.view.viewport) || this.view.viewport.to > cx.treeLen)\n            this.scheduleWork();\n        if (update.docChanged || update.selectionSet) {\n            if (this.view.hasFocus)\n                this.chunkBudget += 50 /* Work.ChangeBonus */;\n            this.scheduleWork();\n        }\n        this.checkAsyncSchedule(cx);\n    }\n    scheduleWork() {\n        if (this.working)\n            return;\n        let { state } = this.view, field = state.field(Language.state);\n        if (field.tree != field.context.tree || !field.context.isDone(state.doc.length))\n            this.working = requestIdle(this.work);\n    }\n    work(deadline) {\n        this.working = null;\n        let now = Date.now();\n        if (this.chunkEnd < now && (this.chunkEnd < 0 || this.view.hasFocus)) { // Start a new chunk\n            this.chunkEnd = now + 30000 /* Work.ChunkTime */;\n            this.chunkBudget = 3000 /* Work.ChunkBudget */;\n        }\n        if (this.chunkBudget <= 0)\n            return; // No more budget\n        let { state, viewport: { to: vpTo } } = this.view, field = state.field(Language.state);\n        if (field.tree == field.context.tree && field.context.isDone(vpTo + 100000 /* Work.MaxParseAhead */))\n            return;\n        let endTime = Date.now() + Math.min(this.chunkBudget, 100 /* Work.Slice */, deadline && !isInputPending ? Math.max(25 /* Work.MinSlice */, deadline.timeRemaining() - 5) : 1e9);\n        let viewportFirst = field.context.treeLen < vpTo && state.doc.length > vpTo + 1000;\n        let done = field.context.work(() => {\n            return isInputPending && isInputPending() || Date.now() > endTime;\n        }, vpTo + (viewportFirst ? 0 : 100000 /* Work.MaxParseAhead */));\n        this.chunkBudget -= Date.now() - now;\n        if (done || this.chunkBudget <= 0) {\n            field.context.takeTree();\n            this.view.dispatch({ effects: Language.setState.of(new LanguageState(field.context)) });\n        }\n        if (this.chunkBudget > 0 && !(done && !viewportFirst))\n            this.scheduleWork();\n        this.checkAsyncSchedule(field.context);\n    }\n    checkAsyncSchedule(cx) {\n        if (cx.scheduleOn) {\n            this.workScheduled++;\n            cx.scheduleOn\n                .then(() => this.scheduleWork())\n                .catch(err => (0,_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.logException)(this.view.state, err))\n                .then(() => this.workScheduled--);\n            cx.scheduleOn = null;\n        }\n    }\n    destroy() {\n        if (this.working)\n            this.working();\n    }\n    isWorking() {\n        return !!(this.working || this.workScheduled > 0);\n    }\n}, {\n    eventHandlers: { focus() { this.scheduleWork(); } }\n});\n/**\nThe facet used to associate a language with an editor state. Used\nby `Language` object's `extension` property (so you don't need to\nmanually wrap your languages in this). Can be used to access the\ncurrent language on a state.\n*/\nconst language = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Facet.define({\n    combine(languages) { return languages.length ? languages[0] : null; },\n    enables: language => [\n        Language.state,\n        parseWorker,\n        _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.contentAttributes.compute([language], state => {\n            let lang = state.facet(language);\n            return lang && lang.name ? { \"data-language\": lang.name } : {};\n        })\n    ]\n});\n/**\nThis class bundles a [language](https://codemirror.net/6/docs/ref/#language.Language) with an\noptional set of supporting extensions. Language packages are\nencouraged to export a function that optionally takes a\nconfiguration object and returns a `LanguageSupport` instance, as\nthe main way for client code to use the package.\n*/\nclass LanguageSupport {\n    /**\n    Create a language support object.\n    */\n    constructor(\n    /**\n    The language object.\n    */\n    language, \n    /**\n    An optional set of supporting extensions. When nesting a\n    language in another language, the outer language is encouraged\n    to include the supporting extensions for its inner languages\n    in its own set of support extensions.\n    */\n    support = []) {\n        this.language = language;\n        this.support = support;\n        this.extension = [language, support];\n    }\n}\n/**\nLanguage descriptions are used to store metadata about languages\nand to dynamically load them. Their main role is finding the\nappropriate language for a filename or dynamically loading nested\nparsers.\n*/\nclass LanguageDescription {\n    constructor(\n    /**\n    The name of this language.\n    */\n    name, \n    /**\n    Alternative names for the mode (lowercased, includes `this.name`).\n    */\n    alias, \n    /**\n    File extensions associated with this language.\n    */\n    extensions, \n    /**\n    Optional filename pattern that should be associated with this\n    language.\n    */\n    filename, loadFunc, \n    /**\n    If the language has been loaded, this will hold its value.\n    */\n    support = undefined) {\n        this.name = name;\n        this.alias = alias;\n        this.extensions = extensions;\n        this.filename = filename;\n        this.loadFunc = loadFunc;\n        this.support = support;\n        this.loading = null;\n    }\n    /**\n    Start loading the the language. Will return a promise that\n    resolves to a [`LanguageSupport`](https://codemirror.net/6/docs/ref/#language.LanguageSupport)\n    object when the language successfully loads.\n    */\n    load() {\n        return this.loading || (this.loading = this.loadFunc().then(support => this.support = support, err => { this.loading = null; throw err; }));\n    }\n    /**\n    Create a language description.\n    */\n    static of(spec) {\n        let { load, support } = spec;\n        if (!load) {\n            if (!support)\n                throw new RangeError(\"Must pass either 'load' or 'support' to LanguageDescription.of\");\n            load = () => Promise.resolve(support);\n        }\n        return new LanguageDescription(spec.name, (spec.alias || []).concat(spec.name).map(s => s.toLowerCase()), spec.extensions || [], spec.filename, load, support);\n    }\n    /**\n    Look for a language in the given array of descriptions that\n    matches the filename. Will first match\n    [`filename`](https://codemirror.net/6/docs/ref/#language.LanguageDescription.filename) patterns,\n    and then [extensions](https://codemirror.net/6/docs/ref/#language.LanguageDescription.extensions),\n    and return the first language that matches.\n    */\n    static matchFilename(descs, filename) {\n        for (let d of descs)\n            if (d.filename && d.filename.test(filename))\n                return d;\n        let ext = /\\.([^.]+)$/.exec(filename);\n        if (ext)\n            for (let d of descs)\n                if (d.extensions.indexOf(ext[1]) > -1)\n                    return d;\n        return null;\n    }\n    /**\n    Look for a language whose name or alias matches the the given\n    name (case-insensitively). If `fuzzy` is true, and no direct\n    matchs is found, this'll also search for a language whose name\n    or alias occurs in the string (for names shorter than three\n    characters, only when surrounded by non-word characters).\n    */\n    static matchLanguageName(descs, name, fuzzy = true) {\n        name = name.toLowerCase();\n        for (let d of descs)\n            if (d.alias.some(a => a == name))\n                return d;\n        if (fuzzy)\n            for (let d of descs)\n                for (let a of d.alias) {\n                    let found = name.indexOf(a);\n                    if (found > -1 && (a.length > 2 || !/\\w/.test(name[found - 1]) && !/\\w/.test(name[found + a.length])))\n                        return d;\n                }\n        return null;\n    }\n}\n\n/**\nFacet that defines a way to provide a function that computes the\nappropriate indentation depth, as a column number (see\n[`indentString`](https://codemirror.net/6/docs/ref/#language.indentString)), at the start of a given\nline. A return value of `null` indicates no indentation can be\ndetermined, and the line should inherit the indentation of the one\nabove it. A return value of `undefined` defers to the next indent\nservice.\n*/\nconst indentService = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Facet.define();\n/**\nFacet for overriding the unit by which indentation happens. Should\nbe a string consisting either entirely of the same whitespace\ncharacter. When not set, this defaults to 2 spaces.\n*/\nconst indentUnit = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Facet.define({\n    combine: values => {\n        if (!values.length)\n            return \"  \";\n        let unit = values[0];\n        if (!unit || /\\S/.test(unit) || Array.from(unit).some(e => e != unit[0]))\n            throw new Error(\"Invalid indent unit: \" + JSON.stringify(values[0]));\n        return unit;\n    }\n});\n/**\nReturn the _column width_ of an indent unit in the state.\nDetermined by the [`indentUnit`](https://codemirror.net/6/docs/ref/#language.indentUnit)\nfacet, and [`tabSize`](https://codemirror.net/6/docs/ref/#state.EditorState^tabSize) when that\ncontains tabs.\n*/\nfunction getIndentUnit(state) {\n    let unit = state.facet(indentUnit);\n    return unit.charCodeAt(0) == 9 ? state.tabSize * unit.length : unit.length;\n}\n/**\nCreate an indentation string that covers columns 0 to `cols`.\nWill use tabs for as much of the columns as possible when the\n[`indentUnit`](https://codemirror.net/6/docs/ref/#language.indentUnit) facet contains\ntabs.\n*/\nfunction indentString(state, cols) {\n    let result = \"\", ts = state.tabSize, ch = state.facet(indentUnit)[0];\n    if (ch == \"\\t\") {\n        while (cols >= ts) {\n            result += \"\\t\";\n            cols -= ts;\n        }\n        ch = \" \";\n    }\n    for (let i = 0; i < cols; i++)\n        result += ch;\n    return result;\n}\n/**\nGet the indentation, as a column number, at the given position.\nWill first consult any [indent services](https://codemirror.net/6/docs/ref/#language.indentService)\nthat are registered, and if none of those return an indentation,\nthis will check the syntax tree for the [indent node\nprop](https://codemirror.net/6/docs/ref/#language.indentNodeProp) and use that if found. Returns a\nnumber when an indentation could be determined, and null\notherwise.\n*/\nfunction getIndentation(context, pos) {\n    if (context instanceof _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState)\n        context = new IndentContext(context);\n    for (let service of context.state.facet(indentService)) {\n        let result = service(context, pos);\n        if (result !== undefined)\n            return result;\n    }\n    let tree = syntaxTree(context.state);\n    return tree.length >= pos ? syntaxIndentation(context, tree, pos) : null;\n}\n/**\nCreate a change set that auto-indents all lines touched by the\ngiven document range.\n*/\nfunction indentRange(state, from, to) {\n    let updated = Object.create(null);\n    let context = new IndentContext(state, { overrideIndentation: start => { var _a; return (_a = updated[start]) !== null && _a !== void 0 ? _a : -1; } });\n    let changes = [];\n    for (let pos = from; pos <= to;) {\n        let line = state.doc.lineAt(pos);\n        pos = line.to + 1;\n        let indent = getIndentation(context, line.from);\n        if (indent == null)\n            continue;\n        if (!/\\S/.test(line.text))\n            indent = 0;\n        let cur = /^\\s*/.exec(line.text)[0];\n        let norm = indentString(state, indent);\n        if (cur != norm) {\n            updated[line.from] = indent;\n            changes.push({ from: line.from, to: line.from + cur.length, insert: norm });\n        }\n    }\n    return state.changes(changes);\n}\n/**\nIndentation contexts are used when calling [indentation\nservices](https://codemirror.net/6/docs/ref/#language.indentService). They provide helper utilities\nuseful in indentation logic, and can selectively override the\nindentation reported for some lines.\n*/\nclass IndentContext {\n    /**\n    Create an indent context.\n    */\n    constructor(\n    /**\n    The editor state.\n    */\n    state, \n    /**\n    @internal\n    */\n    options = {}) {\n        this.state = state;\n        this.options = options;\n        this.unit = getIndentUnit(state);\n    }\n    /**\n    Get a description of the line at the given position, taking\n    [simulated line\n    breaks](https://codemirror.net/6/docs/ref/#language.IndentContext.constructor^options.simulateBreak)\n    into account. If there is such a break at `pos`, the `bias`\n    argument determines whether the part of the line line before or\n    after the break is used.\n    */\n    lineAt(pos, bias = 1) {\n        let line = this.state.doc.lineAt(pos);\n        let { simulateBreak, simulateDoubleBreak } = this.options;\n        if (simulateBreak != null && simulateBreak >= line.from && simulateBreak <= line.to) {\n            if (simulateDoubleBreak && simulateBreak == pos)\n                return { text: \"\", from: pos };\n            else if (bias < 0 ? simulateBreak < pos : simulateBreak <= pos)\n                return { text: line.text.slice(simulateBreak - line.from), from: simulateBreak };\n            else\n                return { text: line.text.slice(0, simulateBreak - line.from), from: line.from };\n        }\n        return line;\n    }\n    /**\n    Get the text directly after `pos`, either the entire line\n    or the next 100 characters, whichever is shorter.\n    */\n    textAfterPos(pos, bias = 1) {\n        if (this.options.simulateDoubleBreak && pos == this.options.simulateBreak)\n            return \"\";\n        let { text, from } = this.lineAt(pos, bias);\n        return text.slice(pos - from, Math.min(text.length, pos + 100 - from));\n    }\n    /**\n    Find the column for the given position.\n    */\n    column(pos, bias = 1) {\n        let { text, from } = this.lineAt(pos, bias);\n        let result = this.countColumn(text, pos - from);\n        let override = this.options.overrideIndentation ? this.options.overrideIndentation(from) : -1;\n        if (override > -1)\n            result += override - this.countColumn(text, text.search(/\\S|$/));\n        return result;\n    }\n    /**\n    Find the column position (taking tabs into account) of the given\n    position in the given string.\n    */\n    countColumn(line, pos = line.length) {\n        return (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.countColumn)(line, this.state.tabSize, pos);\n    }\n    /**\n    Find the indentation column of the line at the given point.\n    */\n    lineIndent(pos, bias = 1) {\n        let { text, from } = this.lineAt(pos, bias);\n        let override = this.options.overrideIndentation;\n        if (override) {\n            let overriden = override(from);\n            if (overriden > -1)\n                return overriden;\n        }\n        return this.countColumn(text, text.search(/\\S|$/));\n    }\n    /**\n    Returns the [simulated line\n    break](https://codemirror.net/6/docs/ref/#language.IndentContext.constructor^options.simulateBreak)\n    for this context, if any.\n    */\n    get simulatedBreak() {\n        return this.options.simulateBreak || null;\n    }\n}\n/**\nA syntax tree node prop used to associate indentation strategies\nwith node types. Such a strategy is a function from an indentation\ncontext to a column number (see also\n[`indentString`](https://codemirror.net/6/docs/ref/#language.indentString)) or null, where null\nindicates that no definitive indentation can be determined.\n*/\nconst indentNodeProp = /*@__PURE__*/new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp();\n// Compute the indentation for a given position from the syntax tree.\nfunction syntaxIndentation(cx, ast, pos) {\n    let stack = ast.resolveStack(pos);\n    let inner = ast.resolveInner(pos, -1).resolve(pos, 0).enterUnfinishedNodesBefore(pos);\n    if (inner != stack.node) {\n        let add = [];\n        for (let cur = inner; cur && !(cur.from == stack.node.from && cur.type == stack.node.type); cur = cur.parent)\n            add.push(cur);\n        for (let i = add.length - 1; i >= 0; i--)\n            stack = { node: add[i], next: stack };\n    }\n    return indentFor(stack, cx, pos);\n}\nfunction indentFor(stack, cx, pos) {\n    for (let cur = stack; cur; cur = cur.next) {\n        let strategy = indentStrategy(cur.node);\n        if (strategy)\n            return strategy(TreeIndentContext.create(cx, pos, cur));\n    }\n    return 0;\n}\nfunction ignoreClosed(cx) {\n    return cx.pos == cx.options.simulateBreak && cx.options.simulateDoubleBreak;\n}\nfunction indentStrategy(tree) {\n    let strategy = tree.type.prop(indentNodeProp);\n    if (strategy)\n        return strategy;\n    let first = tree.firstChild, close;\n    if (first && (close = first.type.prop(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.closedBy))) {\n        let last = tree.lastChild, closed = last && close.indexOf(last.name) > -1;\n        return cx => delimitedStrategy(cx, true, 1, undefined, closed && !ignoreClosed(cx) ? last.from : undefined);\n    }\n    return tree.parent == null ? topIndent : null;\n}\nfunction topIndent() { return 0; }\n/**\nObjects of this type provide context information and helper\nmethods to indentation functions registered on syntax nodes.\n*/\nclass TreeIndentContext extends IndentContext {\n    constructor(base, \n    /**\n    The position at which indentation is being computed.\n    */\n    pos, \n    /**\n    @internal\n    */\n    context) {\n        super(base.state, base.options);\n        this.base = base;\n        this.pos = pos;\n        this.context = context;\n    }\n    /**\n    The syntax tree node to which the indentation strategy\n    applies.\n    */\n    get node() { return this.context.node; }\n    /**\n    @internal\n    */\n    static create(base, pos, context) {\n        return new TreeIndentContext(base, pos, context);\n    }\n    /**\n    Get the text directly after `this.pos`, either the entire line\n    or the next 100 characters, whichever is shorter.\n    */\n    get textAfter() {\n        return this.textAfterPos(this.pos);\n    }\n    /**\n    Get the indentation at the reference line for `this.node`, which\n    is the line on which it starts, unless there is a node that is\n    _not_ a parent of this node covering the start of that line. If\n    so, the line at the start of that node is tried, again skipping\n    on if it is covered by another such node.\n    */\n    get baseIndent() {\n        return this.baseIndentFor(this.node);\n    }\n    /**\n    Get the indentation for the reference line of the given node\n    (see [`baseIndent`](https://codemirror.net/6/docs/ref/#language.TreeIndentContext.baseIndent)).\n    */\n    baseIndentFor(node) {\n        let line = this.state.doc.lineAt(node.from);\n        // Skip line starts that are covered by a sibling (or cousin, etc)\n        for (;;) {\n            let atBreak = node.resolve(line.from);\n            while (atBreak.parent && atBreak.parent.from == atBreak.from)\n                atBreak = atBreak.parent;\n            if (isParent(atBreak, node))\n                break;\n            line = this.state.doc.lineAt(atBreak.from);\n        }\n        return this.lineIndent(line.from);\n    }\n    /**\n    Continue looking for indentations in the node's parent nodes,\n    and return the result of that.\n    */\n    continue() {\n        return indentFor(this.context.next, this.base, this.pos);\n    }\n}\nfunction isParent(parent, of) {\n    for (let cur = of; cur; cur = cur.parent)\n        if (parent == cur)\n            return true;\n    return false;\n}\n// Check whether a delimited node is aligned (meaning there are\n// non-skipped nodes on the same line as the opening delimiter). And\n// if so, return the opening token.\nfunction bracketedAligned(context) {\n    let tree = context.node;\n    let openToken = tree.childAfter(tree.from), last = tree.lastChild;\n    if (!openToken)\n        return null;\n    let sim = context.options.simulateBreak;\n    let openLine = context.state.doc.lineAt(openToken.from);\n    let lineEnd = sim == null || sim <= openLine.from ? openLine.to : Math.min(openLine.to, sim);\n    for (let pos = openToken.to;;) {\n        let next = tree.childAfter(pos);\n        if (!next || next == last)\n            return null;\n        if (!next.type.isSkipped) {\n            if (next.from >= lineEnd)\n                return null;\n            let space = /^ */.exec(openLine.text.slice(openToken.to - openLine.from))[0].length;\n            return { from: openToken.from, to: openToken.to + space };\n        }\n        pos = next.to;\n    }\n}\n/**\nAn indentation strategy for delimited (usually bracketed) nodes.\nWill, by default, indent one unit more than the parent's base\nindent unless the line starts with a closing token. When `align`\nis true and there are non-skipped nodes on the node's opening\nline, the content of the node will be aligned with the end of the\nopening node, like this:\n\n    foo(bar,\n        baz)\n*/\nfunction delimitedIndent({ closing, align = true, units = 1 }) {\n    return (context) => delimitedStrategy(context, align, units, closing);\n}\nfunction delimitedStrategy(context, align, units, closing, closedAt) {\n    let after = context.textAfter, space = after.match(/^\\s*/)[0].length;\n    let closed = closing && after.slice(space, space + closing.length) == closing || closedAt == context.pos + space;\n    let aligned = align ? bracketedAligned(context) : null;\n    if (aligned)\n        return closed ? context.column(aligned.from) : context.column(aligned.to);\n    return context.baseIndent + (closed ? 0 : context.unit * units);\n}\n/**\nAn indentation strategy that aligns a node's content to its base\nindentation.\n*/\nconst flatIndent = (context) => context.baseIndent;\n/**\nCreates an indentation strategy that, by default, indents\ncontinued lines one unit more than the node's base indentation.\nYou can provide `except` to prevent indentation of lines that\nmatch a pattern (for example `/^else\\b/` in `if`/`else`\nconstructs), and you can change the amount of units used with the\n`units` option.\n*/\nfunction continuedIndent({ except, units = 1 } = {}) {\n    return (context) => {\n        let matchExcept = except && except.test(context.textAfter);\n        return context.baseIndent + (matchExcept ? 0 : units * context.unit);\n    };\n}\nconst DontIndentBeyond = 200;\n/**\nEnables reindentation on input. When a language defines an\n`indentOnInput` field in its [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt), which must hold a regular\nexpression, the line at the cursor will be reindented whenever new\ntext is typed and the input from the start of the line up to the\ncursor matches that regexp.\n\nTo avoid unneccesary reindents, it is recommended to start the\nregexp with `^` (usually followed by `\\s*`), and end it with `$`.\nFor example, `/^\\s*\\}$/` will reindent when a closing brace is\nadded at the start of a line.\n*/\nfunction indentOnInput() {\n    return _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.EditorState.transactionFilter.of(tr => {\n        if (!tr.docChanged || !tr.isUserEvent(\"input.type\") && !tr.isUserEvent(\"input.complete\"))\n            return tr;\n        let rules = tr.startState.languageDataAt(\"indentOnInput\", tr.startState.selection.main.head);\n        if (!rules.length)\n            return tr;\n        let doc = tr.newDoc, { head } = tr.newSelection.main, line = doc.lineAt(head);\n        if (head > line.from + DontIndentBeyond)\n            return tr;\n        let lineStart = doc.sliceString(line.from, head);\n        if (!rules.some(r => r.test(lineStart)))\n            return tr;\n        let { state } = tr, last = -1, changes = [];\n        for (let { head } of state.selection.ranges) {\n            let line = state.doc.lineAt(head);\n            if (line.from == last)\n                continue;\n            last = line.from;\n            let indent = getIndentation(state, line.from);\n            if (indent == null)\n                continue;\n            let cur = /^\\s*/.exec(line.text)[0];\n            let norm = indentString(state, indent);\n            if (cur != norm)\n                changes.push({ from: line.from, to: line.from + cur.length, insert: norm });\n        }\n        return changes.length ? [tr, { changes, sequential: true }] : tr;\n    });\n}\n\n/**\nA facet that registers a code folding service. When called with\nthe extent of a line, such a function should return a foldable\nrange that starts on that line (but continues beyond it), if one\ncan be found.\n*/\nconst foldService = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Facet.define();\n/**\nThis node prop is used to associate folding information with\nsyntax node types. Given a syntax node, it should check whether\nthat tree is foldable and return the range that can be collapsed\nwhen it is.\n*/\nconst foldNodeProp = /*@__PURE__*/new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp();\n/**\n[Fold](https://codemirror.net/6/docs/ref/#language.foldNodeProp) function that folds everything but\nthe first and the last child of a syntax node. Useful for nodes\nthat start and end with delimiters.\n*/\nfunction foldInside(node) {\n    let first = node.firstChild, last = node.lastChild;\n    return first && first.to < last.from ? { from: first.to, to: last.type.isError ? node.to : last.from } : null;\n}\nfunction syntaxFolding(state, start, end) {\n    let tree = syntaxTree(state);\n    if (tree.length < end)\n        return null;\n    let stack = tree.resolveStack(end, 1);\n    let found = null;\n    for (let iter = stack; iter; iter = iter.next) {\n        let cur = iter.node;\n        if (cur.to <= end || cur.from > end)\n            continue;\n        if (found && cur.from < start)\n            break;\n        let prop = cur.type.prop(foldNodeProp);\n        if (prop && (cur.to < tree.length - 50 || tree.length == state.doc.length || !isUnfinished(cur))) {\n            let value = prop(cur, state);\n            if (value && value.from <= end && value.from >= start && value.to > end)\n                found = value;\n        }\n    }\n    return found;\n}\nfunction isUnfinished(node) {\n    let ch = node.lastChild;\n    return ch && ch.to == node.to && ch.type.isError;\n}\n/**\nCheck whether the given line is foldable. First asks any fold\nservices registered through\n[`foldService`](https://codemirror.net/6/docs/ref/#language.foldService), and if none of them return\na result, tries to query the [fold node\nprop](https://codemirror.net/6/docs/ref/#language.foldNodeProp) of syntax nodes that cover the end\nof the line.\n*/\nfunction foldable(state, lineStart, lineEnd) {\n    for (let service of state.facet(foldService)) {\n        let result = service(state, lineStart, lineEnd);\n        if (result)\n            return result;\n    }\n    return syntaxFolding(state, lineStart, lineEnd);\n}\nfunction mapRange(range, mapping) {\n    let from = mapping.mapPos(range.from, 1), to = mapping.mapPos(range.to, -1);\n    return from >= to ? undefined : { from, to };\n}\n/**\nState effect that can be attached to a transaction to fold the\ngiven range. (You probably only need this in exceptional\ncircumstances—usually you'll just want to let\n[`foldCode`](https://codemirror.net/6/docs/ref/#language.foldCode) and the [fold\ngutter](https://codemirror.net/6/docs/ref/#language.foldGutter) create the transactions.)\n*/\nconst foldEffect = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.StateEffect.define({ map: mapRange });\n/**\nState effect that unfolds the given range (if it was folded).\n*/\nconst unfoldEffect = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.StateEffect.define({ map: mapRange });\nfunction selectedLines(view) {\n    let lines = [];\n    for (let { head } of view.state.selection.ranges) {\n        if (lines.some(l => l.from <= head && l.to >= head))\n            continue;\n        lines.push(view.lineBlockAt(head));\n    }\n    return lines;\n}\n/**\nThe state field that stores the folded ranges (as a [decoration\nset](https://codemirror.net/6/docs/ref/#view.DecorationSet)). Can be passed to\n[`EditorState.toJSON`](https://codemirror.net/6/docs/ref/#state.EditorState.toJSON) and\n[`fromJSON`](https://codemirror.net/6/docs/ref/#state.EditorState^fromJSON) to serialize the fold\nstate.\n*/\nconst foldState = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.StateField.define({\n    create() {\n        return _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.none;\n    },\n    update(folded, tr) {\n        folded = folded.map(tr.changes);\n        for (let e of tr.effects) {\n            if (e.is(foldEffect) && !foldExists(folded, e.value.from, e.value.to)) {\n                let { preparePlaceholder } = tr.state.facet(foldConfig);\n                let widget = !preparePlaceholder ? foldWidget :\n                    _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.replace({ widget: new PreparedFoldWidget(preparePlaceholder(tr.state, e.value)) });\n                folded = folded.update({ add: [widget.range(e.value.from, e.value.to)] });\n            }\n            else if (e.is(unfoldEffect)) {\n                folded = folded.update({ filter: (from, to) => e.value.from != from || e.value.to != to,\n                    filterFrom: e.value.from, filterTo: e.value.to });\n            }\n        }\n        // Clear folded ranges that cover the selection head\n        if (tr.selection) {\n            let onSelection = false, { head } = tr.selection.main;\n            folded.between(head, head, (a, b) => { if (a < head && b > head)\n                onSelection = true; });\n            if (onSelection)\n                folded = folded.update({\n                    filterFrom: head,\n                    filterTo: head,\n                    filter: (a, b) => b <= head || a >= head\n                });\n        }\n        return folded;\n    },\n    provide: f => _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.decorations.from(f),\n    toJSON(folded, state) {\n        let ranges = [];\n        folded.between(0, state.doc.length, (from, to) => { ranges.push(from, to); });\n        return ranges;\n    },\n    fromJSON(value) {\n        if (!Array.isArray(value) || value.length % 2)\n            throw new RangeError(\"Invalid JSON for fold state\");\n        let ranges = [];\n        for (let i = 0; i < value.length;) {\n            let from = value[i++], to = value[i++];\n            if (typeof from != \"number\" || typeof to != \"number\")\n                throw new RangeError(\"Invalid JSON for fold state\");\n            ranges.push(foldWidget.range(from, to));\n        }\n        return _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.set(ranges, true);\n    }\n});\n/**\nGet a [range set](https://codemirror.net/6/docs/ref/#state.RangeSet) containing the folded ranges\nin the given state.\n*/\nfunction foldedRanges(state) {\n    return state.field(foldState, false) || _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.RangeSet.empty;\n}\nfunction findFold(state, from, to) {\n    var _a;\n    let found = null;\n    (_a = state.field(foldState, false)) === null || _a === void 0 ? void 0 : _a.between(from, to, (from, to) => {\n        if (!found || found.from > from)\n            found = { from, to };\n    });\n    return found;\n}\nfunction foldExists(folded, from, to) {\n    let found = false;\n    folded.between(from, from, (a, b) => { if (a == from && b == to)\n        found = true; });\n    return found;\n}\nfunction maybeEnable(state, other) {\n    return state.field(foldState, false) ? other : other.concat(_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.StateEffect.appendConfig.of(codeFolding()));\n}\n/**\nFold the lines that are selected, if possible.\n*/\nconst foldCode = view => {\n    for (let line of selectedLines(view)) {\n        let range = foldable(view.state, line.from, line.to);\n        if (range) {\n            view.dispatch({ effects: maybeEnable(view.state, [foldEffect.of(range), announceFold(view, range)]) });\n            return true;\n        }\n    }\n    return false;\n};\n/**\nUnfold folded ranges on selected lines.\n*/\nconst unfoldCode = view => {\n    if (!view.state.field(foldState, false))\n        return false;\n    let effects = [];\n    for (let line of selectedLines(view)) {\n        let folded = findFold(view.state, line.from, line.to);\n        if (folded)\n            effects.push(unfoldEffect.of(folded), announceFold(view, folded, false));\n    }\n    if (effects.length)\n        view.dispatch({ effects });\n    return effects.length > 0;\n};\nfunction announceFold(view, range, fold = true) {\n    let lineFrom = view.state.doc.lineAt(range.from).number, lineTo = view.state.doc.lineAt(range.to).number;\n    return _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.announce.of(`${view.state.phrase(fold ? \"Folded lines\" : \"Unfolded lines\")} ${lineFrom} ${view.state.phrase(\"to\")} ${lineTo}.`);\n}\n/**\nFold all top-level foldable ranges. Note that, in most cases,\nfolding information will depend on the [syntax\ntree](https://codemirror.net/6/docs/ref/#language.syntaxTree), and folding everything may not work\nreliably when the document hasn't been fully parsed (either\nbecause the editor state was only just initialized, or because the\ndocument is so big that the parser decided not to parse it\nentirely).\n*/\nconst foldAll = view => {\n    let { state } = view, effects = [];\n    for (let pos = 0; pos < state.doc.length;) {\n        let line = view.lineBlockAt(pos), range = foldable(state, line.from, line.to);\n        if (range)\n            effects.push(foldEffect.of(range));\n        pos = (range ? view.lineBlockAt(range.to) : line).to + 1;\n    }\n    if (effects.length)\n        view.dispatch({ effects: maybeEnable(view.state, effects) });\n    return !!effects.length;\n};\n/**\nUnfold all folded code.\n*/\nconst unfoldAll = view => {\n    let field = view.state.field(foldState, false);\n    if (!field || !field.size)\n        return false;\n    let effects = [];\n    field.between(0, view.state.doc.length, (from, to) => { effects.push(unfoldEffect.of({ from, to })); });\n    view.dispatch({ effects });\n    return true;\n};\n// Find the foldable region containing the given line, if one exists\nfunction foldableContainer(view, lineBlock) {\n    // Look backwards through line blocks until we find a foldable region that\n    // intersects with the line\n    for (let line = lineBlock;;) {\n        let foldableRegion = foldable(view.state, line.from, line.to);\n        if (foldableRegion && foldableRegion.to > lineBlock.from)\n            return foldableRegion;\n        if (!line.from)\n            return null;\n        line = view.lineBlockAt(line.from - 1);\n    }\n}\n/**\nToggle folding at cursors. Unfolds if there is an existing fold\nstarting in that line, tries to find a foldable range around it\notherwise.\n*/\nconst toggleFold = (view) => {\n    let effects = [];\n    for (let line of selectedLines(view)) {\n        let folded = findFold(view.state, line.from, line.to);\n        if (folded) {\n            effects.push(unfoldEffect.of(folded), announceFold(view, folded, false));\n        }\n        else {\n            let foldRange = foldableContainer(view, line);\n            if (foldRange)\n                effects.push(foldEffect.of(foldRange), announceFold(view, foldRange));\n        }\n    }\n    if (effects.length > 0)\n        view.dispatch({ effects: maybeEnable(view.state, effects) });\n    return !!effects.length;\n};\n/**\nDefault fold-related key bindings.\n\n - Ctrl-Shift-[ (Cmd-Alt-[ on macOS): [`foldCode`](https://codemirror.net/6/docs/ref/#language.foldCode).\n - Ctrl-Shift-] (Cmd-Alt-] on macOS): [`unfoldCode`](https://codemirror.net/6/docs/ref/#language.unfoldCode).\n - Ctrl-Alt-[: [`foldAll`](https://codemirror.net/6/docs/ref/#language.foldAll).\n - Ctrl-Alt-]: [`unfoldAll`](https://codemirror.net/6/docs/ref/#language.unfoldAll).\n*/\nconst foldKeymap = [\n    { key: \"Ctrl-Shift-[\", mac: \"Cmd-Alt-[\", run: foldCode },\n    { key: \"Ctrl-Shift-]\", mac: \"Cmd-Alt-]\", run: unfoldCode },\n    { key: \"Ctrl-Alt-[\", run: foldAll },\n    { key: \"Ctrl-Alt-]\", run: unfoldAll }\n];\nconst defaultConfig = {\n    placeholderDOM: null,\n    preparePlaceholder: null,\n    placeholderText: \"…\"\n};\nconst foldConfig = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Facet.define({\n    combine(values) { return (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.combineConfig)(values, defaultConfig); }\n});\n/**\nCreate an extension that configures code folding.\n*/\nfunction codeFolding(config) {\n    let result = [foldState, baseTheme$1];\n    if (config)\n        result.push(foldConfig.of(config));\n    return result;\n}\nfunction widgetToDOM(view, prepared) {\n    let { state } = view, conf = state.facet(foldConfig);\n    let onclick = (event) => {\n        let line = view.lineBlockAt(view.posAtDOM(event.target));\n        let folded = findFold(view.state, line.from, line.to);\n        if (folded)\n            view.dispatch({ effects: unfoldEffect.of(folded) });\n        event.preventDefault();\n    };\n    if (conf.placeholderDOM)\n        return conf.placeholderDOM(view, onclick, prepared);\n    let element = document.createElement(\"span\");\n    element.textContent = conf.placeholderText;\n    element.setAttribute(\"aria-label\", state.phrase(\"folded code\"));\n    element.title = state.phrase(\"unfold\");\n    element.className = \"cm-foldPlaceholder\";\n    element.onclick = onclick;\n    return element;\n}\nconst foldWidget = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.replace({ widget: /*@__PURE__*/new class extends _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.WidgetType {\n        toDOM(view) { return widgetToDOM(view, null); }\n    } });\nclass PreparedFoldWidget extends _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.WidgetType {\n    constructor(value) {\n        super();\n        this.value = value;\n    }\n    eq(other) { return this.value == other.value; }\n    toDOM(view) { return widgetToDOM(view, this.value); }\n}\nconst foldGutterDefaults = {\n    openText: \"⌄\",\n    closedText: \"›\",\n    markerDOM: null,\n    domEventHandlers: {},\n    foldingChanged: () => false\n};\nclass FoldMarker extends _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.GutterMarker {\n    constructor(config, open) {\n        super();\n        this.config = config;\n        this.open = open;\n    }\n    eq(other) { return this.config == other.config && this.open == other.open; }\n    toDOM(view) {\n        if (this.config.markerDOM)\n            return this.config.markerDOM(this.open);\n        let span = document.createElement(\"span\");\n        span.textContent = this.open ? this.config.openText : this.config.closedText;\n        span.title = view.state.phrase(this.open ? \"Fold line\" : \"Unfold line\");\n        return span;\n    }\n}\n/**\nCreate an extension that registers a fold gutter, which shows a\nfold status indicator before foldable lines (which can be clicked\nto fold or unfold the line).\n*/\nfunction foldGutter(config = {}) {\n    let fullConfig = Object.assign(Object.assign({}, foldGutterDefaults), config);\n    let canFold = new FoldMarker(fullConfig, true), canUnfold = new FoldMarker(fullConfig, false);\n    let markers = _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.ViewPlugin.fromClass(class {\n        constructor(view) {\n            this.from = view.viewport.from;\n            this.markers = this.buildMarkers(view);\n        }\n        update(update) {\n            if (update.docChanged || update.viewportChanged ||\n                update.startState.facet(language) != update.state.facet(language) ||\n                update.startState.field(foldState, false) != update.state.field(foldState, false) ||\n                syntaxTree(update.startState) != syntaxTree(update.state) ||\n                fullConfig.foldingChanged(update))\n                this.markers = this.buildMarkers(update.view);\n        }\n        buildMarkers(view) {\n            let builder = new _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.RangeSetBuilder();\n            for (let line of view.viewportLineBlocks) {\n                let mark = findFold(view.state, line.from, line.to) ? canUnfold\n                    : foldable(view.state, line.from, line.to) ? canFold : null;\n                if (mark)\n                    builder.add(line.from, line.from, mark);\n            }\n            return builder.finish();\n        }\n    });\n    let { domEventHandlers } = fullConfig;\n    return [\n        markers,\n        (0,_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.gutter)({\n            class: \"cm-foldGutter\",\n            markers(view) { var _a; return ((_a = view.plugin(markers)) === null || _a === void 0 ? void 0 : _a.markers) || _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.RangeSet.empty; },\n            initialSpacer() {\n                return new FoldMarker(fullConfig, false);\n            },\n            domEventHandlers: Object.assign(Object.assign({}, domEventHandlers), { click: (view, line, event) => {\n                    if (domEventHandlers.click && domEventHandlers.click(view, line, event))\n                        return true;\n                    let folded = findFold(view.state, line.from, line.to);\n                    if (folded) {\n                        view.dispatch({ effects: unfoldEffect.of(folded) });\n                        return true;\n                    }\n                    let range = foldable(view.state, line.from, line.to);\n                    if (range) {\n                        view.dispatch({ effects: foldEffect.of(range) });\n                        return true;\n                    }\n                    return false;\n                } })\n        }),\n        codeFolding()\n    ];\n}\nconst baseTheme$1 = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.baseTheme({\n    \".cm-foldPlaceholder\": {\n        backgroundColor: \"#eee\",\n        border: \"1px solid #ddd\",\n        color: \"#888\",\n        borderRadius: \".2em\",\n        margin: \"0 1px\",\n        padding: \"0 1px\",\n        cursor: \"pointer\"\n    },\n    \".cm-foldGutter span\": {\n        padding: \"0 1px\",\n        cursor: \"pointer\"\n    }\n});\n\n/**\nA highlight style associates CSS styles with higlighting\n[tags](https://lezer.codemirror.net/docs/ref#highlight.Tag).\n*/\nclass HighlightStyle {\n    constructor(\n    /**\n    The tag styles used to create this highlight style.\n    */\n    specs, options) {\n        this.specs = specs;\n        let modSpec;\n        function def(spec) {\n            let cls = style_mod__WEBPACK_IMPORTED_MODULE_2__.StyleModule.newName();\n            (modSpec || (modSpec = Object.create(null)))[\".\" + cls] = spec;\n            return cls;\n        }\n        const all = typeof options.all == \"string\" ? options.all : options.all ? def(options.all) : undefined;\n        const scopeOpt = options.scope;\n        this.scope = scopeOpt instanceof Language ? (type) => type.prop(languageDataProp) == scopeOpt.data\n            : scopeOpt ? (type) => type == scopeOpt : undefined;\n        this.style = (0,_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tagHighlighter)(specs.map(style => ({\n            tag: style.tag,\n            class: style.class || def(Object.assign({}, style, { tag: null }))\n        })), {\n            all,\n        }).style;\n        this.module = modSpec ? new style_mod__WEBPACK_IMPORTED_MODULE_2__.StyleModule(modSpec) : null;\n        this.themeType = options.themeType;\n    }\n    /**\n    Create a highlighter style that associates the given styles to\n    the given tags. The specs must be objects that hold a style tag\n    or array of tags in their `tag` property, and either a single\n    `class` property providing a static CSS class (for highlighter\n    that rely on external styling), or a\n    [`style-mod`](https://github.com/marijnh/style-mod#documentation)-style\n    set of CSS properties (which define the styling for those tags).\n    \n    The CSS rules created for a highlighter will be emitted in the\n    order of the spec's properties. That means that for elements that\n    have multiple tags associated with them, styles defined further\n    down in the list will have a higher CSS precedence than styles\n    defined earlier.\n    */\n    static define(specs, options) {\n        return new HighlightStyle(specs, options || {});\n    }\n}\nconst highlighterFacet = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Facet.define();\nconst fallbackHighlighter = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Facet.define({\n    combine(values) { return values.length ? [values[0]] : null; }\n});\nfunction getHighlighters(state) {\n    let main = state.facet(highlighterFacet);\n    return main.length ? main : state.facet(fallbackHighlighter);\n}\n/**\nWrap a highlighter in an editor extension that uses it to apply\nsyntax highlighting to the editor content.\n\nWhen multiple (non-fallback) styles are provided, the styling\napplied is the union of the classes they emit.\n*/\nfunction syntaxHighlighting(highlighter, options) {\n    let ext = [treeHighlighter], themeType;\n    if (highlighter instanceof HighlightStyle) {\n        if (highlighter.module)\n            ext.push(_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.styleModule.of(highlighter.module));\n        themeType = highlighter.themeType;\n    }\n    if (options === null || options === void 0 ? void 0 : options.fallback)\n        ext.push(fallbackHighlighter.of(highlighter));\n    else if (themeType)\n        ext.push(highlighterFacet.computeN([_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.darkTheme], state => {\n            return state.facet(_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.darkTheme) == (themeType == \"dark\") ? [highlighter] : [];\n        }));\n    else\n        ext.push(highlighterFacet.of(highlighter));\n    return ext;\n}\n/**\nReturns the CSS classes (if any) that the highlighters active in\nthe state would assign to the given style\n[tags](https://lezer.codemirror.net/docs/ref#highlight.Tag) and\n(optional) language\n[scope](https://codemirror.net/6/docs/ref/#language.HighlightStyle^define^options.scope).\n*/\nfunction highlightingFor(state, tags, scope) {\n    let highlighters = getHighlighters(state);\n    let result = null;\n    if (highlighters)\n        for (let highlighter of highlighters) {\n            if (!highlighter.scope || scope && highlighter.scope(scope)) {\n                let cls = highlighter.style(tags);\n                if (cls)\n                    result = result ? result + \" \" + cls : cls;\n            }\n        }\n    return result;\n}\nclass TreeHighlighter {\n    constructor(view) {\n        this.markCache = Object.create(null);\n        this.tree = syntaxTree(view.state);\n        this.decorations = this.buildDeco(view, getHighlighters(view.state));\n        this.decoratedTo = view.viewport.to;\n    }\n    update(update) {\n        let tree = syntaxTree(update.state), highlighters = getHighlighters(update.state);\n        let styleChange = highlighters != getHighlighters(update.startState);\n        let { viewport } = update.view, decoratedToMapped = update.changes.mapPos(this.decoratedTo, 1);\n        if (tree.length < viewport.to && !styleChange && tree.type == this.tree.type && decoratedToMapped >= viewport.to) {\n            this.decorations = this.decorations.map(update.changes);\n            this.decoratedTo = decoratedToMapped;\n        }\n        else if (tree != this.tree || update.viewportChanged || styleChange) {\n            this.tree = tree;\n            this.decorations = this.buildDeco(update.view, highlighters);\n            this.decoratedTo = viewport.to;\n        }\n    }\n    buildDeco(view, highlighters) {\n        if (!highlighters || !this.tree.length)\n            return _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.none;\n        let builder = new _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.RangeSetBuilder();\n        for (let { from, to } of view.visibleRanges) {\n            (0,_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.highlightTree)(this.tree, highlighters, (from, to, style) => {\n                builder.add(from, to, this.markCache[style] || (this.markCache[style] = _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.mark({ class: style })));\n            }, from, to);\n        }\n        return builder.finish();\n    }\n}\nconst treeHighlighter = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Prec.high(/*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.ViewPlugin.fromClass(TreeHighlighter, {\n    decorations: v => v.decorations\n}));\n/**\nA default highlight style (works well with light themes).\n*/\nconst defaultHighlightStyle = /*@__PURE__*/HighlightStyle.define([\n    { tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.meta,\n        color: \"#404740\" },\n    { tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.link,\n        textDecoration: \"underline\" },\n    { tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.heading,\n        textDecoration: \"underline\",\n        fontWeight: \"bold\" },\n    { tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.emphasis,\n        fontStyle: \"italic\" },\n    { tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.strong,\n        fontWeight: \"bold\" },\n    { tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.strikethrough,\n        textDecoration: \"line-through\" },\n    { tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.keyword,\n        color: \"#708\" },\n    { tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.atom, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.bool, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.url, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.contentSeparator, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.labelName],\n        color: \"#219\" },\n    { tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.literal, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.inserted],\n        color: \"#164\" },\n    { tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.deleted],\n        color: \"#a11\" },\n    { tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.regexp, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.escape, /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.string)],\n        color: \"#e40\" },\n    { tag: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.definition(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName),\n        color: \"#00f\" },\n    { tag: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.local(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName),\n        color: \"#30a\" },\n    { tag: [_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.typeName, _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.namespace],\n        color: \"#085\" },\n    { tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.className,\n        color: \"#167\" },\n    { tag: [/*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.special(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.variableName), _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.macroName],\n        color: \"#256\" },\n    { tag: /*@__PURE__*/_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.definition(_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.propertyName),\n        color: \"#00c\" },\n    { tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.comment,\n        color: \"#940\" },\n    { tag: _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags.invalid,\n        color: \"#f00\" }\n]);\n\nconst baseTheme = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.baseTheme({\n    \"&.cm-focused .cm-matchingBracket\": { backgroundColor: \"#328c8252\" },\n    \"&.cm-focused .cm-nonmatchingBracket\": { backgroundColor: \"#bb555544\" }\n});\nconst DefaultScanDist = 10000, DefaultBrackets = \"()[]{}\";\nconst bracketMatchingConfig = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Facet.define({\n    combine(configs) {\n        return (0,_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.combineConfig)(configs, {\n            afterCursor: true,\n            brackets: DefaultBrackets,\n            maxScanDistance: DefaultScanDist,\n            renderMatch: defaultRenderMatch\n        });\n    }\n});\nconst matchingMark = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.mark({ class: \"cm-matchingBracket\" }), nonmatchingMark = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.mark({ class: \"cm-nonmatchingBracket\" });\nfunction defaultRenderMatch(match) {\n    let decorations = [];\n    let mark = match.matched ? matchingMark : nonmatchingMark;\n    decorations.push(mark.range(match.start.from, match.start.to));\n    if (match.end)\n        decorations.push(mark.range(match.end.from, match.end.to));\n    return decorations;\n}\nconst bracketMatchingState = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.StateField.define({\n    create() { return _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.none; },\n    update(deco, tr) {\n        if (!tr.docChanged && !tr.selection)\n            return deco;\n        let decorations = [];\n        let config = tr.state.facet(bracketMatchingConfig);\n        for (let range of tr.state.selection.ranges) {\n            if (!range.empty)\n                continue;\n            let match = matchBrackets(tr.state, range.head, -1, config)\n                || (range.head > 0 && matchBrackets(tr.state, range.head - 1, 1, config))\n                || (config.afterCursor &&\n                    (matchBrackets(tr.state, range.head, 1, config) ||\n                        (range.head < tr.state.doc.length && matchBrackets(tr.state, range.head + 1, -1, config))));\n            if (match)\n                decorations = decorations.concat(config.renderMatch(match, tr.state));\n        }\n        return _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.set(decorations, true);\n    },\n    provide: f => _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.decorations.from(f)\n});\nconst bracketMatchingUnique = [\n    bracketMatchingState,\n    baseTheme\n];\n/**\nCreate an extension that enables bracket matching. Whenever the\ncursor is next to a bracket, that bracket and the one it matches\nare highlighted. Or, when no matching bracket is found, another\nhighlighting style is used to indicate this.\n*/\nfunction bracketMatching(config = {}) {\n    return [bracketMatchingConfig.of(config), bracketMatchingUnique];\n}\n/**\nWhen larger syntax nodes, such as HTML tags, are marked as\nopening/closing, it can be a bit messy to treat the whole node as\na matchable bracket. This node prop allows you to define, for such\na node, a ‘handle’—the part of the node that is highlighted, and\nthat the cursor must be on to activate highlighting in the first\nplace.\n*/\nconst bracketMatchingHandle = /*@__PURE__*/new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp();\nfunction matchingNodes(node, dir, brackets) {\n    let byProp = node.prop(dir < 0 ? _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.openedBy : _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.closedBy);\n    if (byProp)\n        return byProp;\n    if (node.name.length == 1) {\n        let index = brackets.indexOf(node.name);\n        if (index > -1 && index % 2 == (dir < 0 ? 1 : 0))\n            return [brackets[index + dir]];\n    }\n    return null;\n}\nfunction findHandle(node) {\n    let hasHandle = node.type.prop(bracketMatchingHandle);\n    return hasHandle ? hasHandle(node.node) : node;\n}\n/**\nFind the matching bracket for the token at `pos`, scanning\ndirection `dir`. Only the `brackets` and `maxScanDistance`\nproperties are used from `config`, if given. Returns null if no\nbracket was found at `pos`, or a match result otherwise.\n*/\nfunction matchBrackets(state, pos, dir, config = {}) {\n    let maxScanDistance = config.maxScanDistance || DefaultScanDist, brackets = config.brackets || DefaultBrackets;\n    let tree = syntaxTree(state), node = tree.resolveInner(pos, dir);\n    for (let cur = node; cur; cur = cur.parent) {\n        let matches = matchingNodes(cur.type, dir, brackets);\n        if (matches && cur.from < cur.to) {\n            let handle = findHandle(cur);\n            if (handle && (dir > 0 ? pos >= handle.from && pos < handle.to : pos > handle.from && pos <= handle.to))\n                return matchMarkedBrackets(state, pos, dir, cur, handle, matches, brackets);\n        }\n    }\n    return matchPlainBrackets(state, pos, dir, tree, node.type, maxScanDistance, brackets);\n}\nfunction matchMarkedBrackets(_state, _pos, dir, token, handle, matching, brackets) {\n    let parent = token.parent, firstToken = { from: handle.from, to: handle.to };\n    let depth = 0, cursor = parent === null || parent === void 0 ? void 0 : parent.cursor();\n    if (cursor && (dir < 0 ? cursor.childBefore(token.from) : cursor.childAfter(token.to)))\n        do {\n            if (dir < 0 ? cursor.to <= token.from : cursor.from >= token.to) {\n                if (depth == 0 && matching.indexOf(cursor.type.name) > -1 && cursor.from < cursor.to) {\n                    let endHandle = findHandle(cursor);\n                    return { start: firstToken, end: endHandle ? { from: endHandle.from, to: endHandle.to } : undefined, matched: true };\n                }\n                else if (matchingNodes(cursor.type, dir, brackets)) {\n                    depth++;\n                }\n                else if (matchingNodes(cursor.type, -dir, brackets)) {\n                    if (depth == 0) {\n                        let endHandle = findHandle(cursor);\n                        return {\n                            start: firstToken,\n                            end: endHandle && endHandle.from < endHandle.to ? { from: endHandle.from, to: endHandle.to } : undefined,\n                            matched: false\n                        };\n                    }\n                    depth--;\n                }\n            }\n        } while (dir < 0 ? cursor.prevSibling() : cursor.nextSibling());\n    return { start: firstToken, matched: false };\n}\nfunction matchPlainBrackets(state, pos, dir, tree, tokenType, maxScanDistance, brackets) {\n    let startCh = dir < 0 ? state.sliceDoc(pos - 1, pos) : state.sliceDoc(pos, pos + 1);\n    let bracket = brackets.indexOf(startCh);\n    if (bracket < 0 || (bracket % 2 == 0) != (dir > 0))\n        return null;\n    let startToken = { from: dir < 0 ? pos - 1 : pos, to: dir > 0 ? pos + 1 : pos };\n    let iter = state.doc.iterRange(pos, dir > 0 ? state.doc.length : 0), depth = 0;\n    for (let distance = 0; !(iter.next()).done && distance <= maxScanDistance;) {\n        let text = iter.value;\n        if (dir < 0)\n            distance += text.length;\n        let basePos = pos + distance * dir;\n        for (let pos = dir > 0 ? 0 : text.length - 1, end = dir > 0 ? text.length : -1; pos != end; pos += dir) {\n            let found = brackets.indexOf(text[pos]);\n            if (found < 0 || tree.resolveInner(basePos + pos, 1).type != tokenType)\n                continue;\n            if ((found % 2 == 0) == (dir > 0)) {\n                depth++;\n            }\n            else if (depth == 1) { // Closing\n                return { start: startToken, end: { from: basePos + pos, to: basePos + pos + 1 }, matched: (found >> 1) == (bracket >> 1) };\n            }\n            else {\n                depth--;\n            }\n        }\n        if (dir > 0)\n            distance += text.length;\n    }\n    return iter.done ? { start: startToken, matched: false } : null;\n}\n\n// Counts the column offset in a string, taking tabs into account.\n// Used mostly to find indentation.\nfunction countCol(string, end, tabSize, startIndex = 0, startValue = 0) {\n    if (end == null) {\n        end = string.search(/[^\\s\\u00a0]/);\n        if (end == -1)\n            end = string.length;\n    }\n    let n = startValue;\n    for (let i = startIndex; i < end; i++) {\n        if (string.charCodeAt(i) == 9)\n            n += tabSize - (n % tabSize);\n        else\n            n++;\n    }\n    return n;\n}\n/**\nEncapsulates a single line of input. Given to stream syntax code,\nwhich uses it to tokenize the content.\n*/\nclass StringStream {\n    /**\n    Create a stream.\n    */\n    constructor(\n    /**\n    The line.\n    */\n    string, tabSize, \n    /**\n    The current indent unit size.\n    */\n    indentUnit, overrideIndent) {\n        this.string = string;\n        this.tabSize = tabSize;\n        this.indentUnit = indentUnit;\n        this.overrideIndent = overrideIndent;\n        /**\n        The current position on the line.\n        */\n        this.pos = 0;\n        /**\n        The start position of the current token.\n        */\n        this.start = 0;\n        this.lastColumnPos = 0;\n        this.lastColumnValue = 0;\n    }\n    /**\n    True if we are at the end of the line.\n    */\n    eol() { return this.pos >= this.string.length; }\n    /**\n    True if we are at the start of the line.\n    */\n    sol() { return this.pos == 0; }\n    /**\n    Get the next code unit after the current position, or undefined\n    if we're at the end of the line.\n    */\n    peek() { return this.string.charAt(this.pos) || undefined; }\n    /**\n    Read the next code unit and advance `this.pos`.\n    */\n    next() {\n        if (this.pos < this.string.length)\n            return this.string.charAt(this.pos++);\n    }\n    /**\n    Match the next character against the given string, regular\n    expression, or predicate. Consume and return it if it matches.\n    */\n    eat(match) {\n        let ch = this.string.charAt(this.pos);\n        let ok;\n        if (typeof match == \"string\")\n            ok = ch == match;\n        else\n            ok = ch && (match instanceof RegExp ? match.test(ch) : match(ch));\n        if (ok) {\n            ++this.pos;\n            return ch;\n        }\n    }\n    /**\n    Continue matching characters that match the given string,\n    regular expression, or predicate function. Return true if any\n    characters were consumed.\n    */\n    eatWhile(match) {\n        let start = this.pos;\n        while (this.eat(match)) { }\n        return this.pos > start;\n    }\n    /**\n    Consume whitespace ahead of `this.pos`. Return true if any was\n    found.\n    */\n    eatSpace() {\n        let start = this.pos;\n        while (/[\\s\\u00a0]/.test(this.string.charAt(this.pos)))\n            ++this.pos;\n        return this.pos > start;\n    }\n    /**\n    Move to the end of the line.\n    */\n    skipToEnd() { this.pos = this.string.length; }\n    /**\n    Move to directly before the given character, if found on the\n    current line.\n    */\n    skipTo(ch) {\n        let found = this.string.indexOf(ch, this.pos);\n        if (found > -1) {\n            this.pos = found;\n            return true;\n        }\n    }\n    /**\n    Move back `n` characters.\n    */\n    backUp(n) { this.pos -= n; }\n    /**\n    Get the column position at `this.pos`.\n    */\n    column() {\n        if (this.lastColumnPos < this.start) {\n            this.lastColumnValue = countCol(this.string, this.start, this.tabSize, this.lastColumnPos, this.lastColumnValue);\n            this.lastColumnPos = this.start;\n        }\n        return this.lastColumnValue;\n    }\n    /**\n    Get the indentation column of the current line.\n    */\n    indentation() {\n        var _a;\n        return (_a = this.overrideIndent) !== null && _a !== void 0 ? _a : countCol(this.string, null, this.tabSize);\n    }\n    /**\n    Match the input against the given string or regular expression\n    (which should start with a `^`). Return true or the regexp match\n    if it matches.\n    \n    Unless `consume` is set to `false`, this will move `this.pos`\n    past the matched text.\n    \n    When matching a string `caseInsensitive` can be set to true to\n    make the match case-insensitive.\n    */\n    match(pattern, consume, caseInsensitive) {\n        if (typeof pattern == \"string\") {\n            let cased = (str) => caseInsensitive ? str.toLowerCase() : str;\n            let substr = this.string.substr(this.pos, pattern.length);\n            if (cased(substr) == cased(pattern)) {\n                if (consume !== false)\n                    this.pos += pattern.length;\n                return true;\n            }\n            else\n                return null;\n        }\n        else {\n            let match = this.string.slice(this.pos).match(pattern);\n            if (match && match.index > 0)\n                return null;\n            if (match && consume !== false)\n                this.pos += match[0].length;\n            return match;\n        }\n    }\n    /**\n    Get the current token.\n    */\n    current() { return this.string.slice(this.start, this.pos); }\n}\n\nfunction fullParser(spec) {\n    return {\n        name: spec.name || \"\",\n        token: spec.token,\n        blankLine: spec.blankLine || (() => { }),\n        startState: spec.startState || (() => true),\n        copyState: spec.copyState || defaultCopyState,\n        indent: spec.indent || (() => null),\n        languageData: spec.languageData || {},\n        tokenTable: spec.tokenTable || noTokens,\n        mergeTokens: spec.mergeTokens !== false\n    };\n}\nfunction defaultCopyState(state) {\n    if (typeof state != \"object\")\n        return state;\n    let newState = {};\n    for (let prop in state) {\n        let val = state[prop];\n        newState[prop] = (val instanceof Array ? val.slice() : val);\n    }\n    return newState;\n}\nconst IndentedFrom = /*@__PURE__*/new WeakMap();\n/**\nA [language](https://codemirror.net/6/docs/ref/#language.Language) class based on a CodeMirror\n5-style [streaming parser](https://codemirror.net/6/docs/ref/#language.StreamParser).\n*/\nclass StreamLanguage extends Language {\n    constructor(parser) {\n        let data = defineLanguageFacet(parser.languageData);\n        let p = fullParser(parser), self;\n        let impl = new class extends _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Parser {\n            createParse(input, fragments, ranges) {\n                return new Parse(self, input, fragments, ranges);\n            }\n        };\n        super(data, impl, [], parser.name);\n        this.topNode = docID(data, this);\n        self = this;\n        this.streamParser = p;\n        this.stateAfter = new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp({ perNode: true });\n        this.tokenTable = parser.tokenTable ? new TokenTable(p.tokenTable) : defaultTokenTable;\n    }\n    /**\n    Define a stream language.\n    */\n    static define(spec) { return new StreamLanguage(spec); }\n    /**\n    @internal\n    */\n    getIndent(cx) {\n        let from = undefined;\n        let { overrideIndentation } = cx.options;\n        if (overrideIndentation) {\n            from = IndentedFrom.get(cx.state);\n            if (from != null && from < cx.pos - 1e4)\n                from = undefined;\n        }\n        let start = findState(this, cx.node.tree, cx.node.from, cx.node.from, from !== null && from !== void 0 ? from : cx.pos), statePos, state;\n        if (start) {\n            state = start.state;\n            statePos = start.pos + 1;\n        }\n        else {\n            state = this.streamParser.startState(cx.unit);\n            statePos = cx.node.from;\n        }\n        if (cx.pos - statePos > 10000 /* C.MaxIndentScanDist */)\n            return null;\n        while (statePos < cx.pos) {\n            let line = cx.state.doc.lineAt(statePos), end = Math.min(cx.pos, line.to);\n            if (line.length) {\n                let indentation = overrideIndentation ? overrideIndentation(line.from) : -1;\n                let stream = new StringStream(line.text, cx.state.tabSize, cx.unit, indentation < 0 ? undefined : indentation);\n                while (stream.pos < end - line.from)\n                    readToken(this.streamParser.token, stream, state);\n            }\n            else {\n                this.streamParser.blankLine(state, cx.unit);\n            }\n            if (end == cx.pos)\n                break;\n            statePos = line.to + 1;\n        }\n        let line = cx.lineAt(cx.pos);\n        if (overrideIndentation && from == null)\n            IndentedFrom.set(cx.state, line.from);\n        return this.streamParser.indent(state, /^\\s*(.*)/.exec(line.text)[1], cx);\n    }\n    get allowsNesting() { return false; }\n}\nfunction findState(lang, tree, off, startPos, before) {\n    let state = off >= startPos && off + tree.length <= before && tree.prop(lang.stateAfter);\n    if (state)\n        return { state: lang.streamParser.copyState(state), pos: off + tree.length };\n    for (let i = tree.children.length - 1; i >= 0; i--) {\n        let child = tree.children[i], pos = off + tree.positions[i];\n        let found = child instanceof _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree && pos < before && findState(lang, child, pos, startPos, before);\n        if (found)\n            return found;\n    }\n    return null;\n}\nfunction cutTree(lang, tree, from, to, inside) {\n    if (inside && from <= 0 && to >= tree.length)\n        return tree;\n    if (!inside && from == 0 && tree.type == lang.topNode)\n        inside = true;\n    for (let i = tree.children.length - 1; i >= 0; i--) {\n        let pos = tree.positions[i], child = tree.children[i], inner;\n        if (pos < to && child instanceof _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree) {\n            if (!(inner = cutTree(lang, child, from - pos, to - pos, inside)))\n                break;\n            return !inside ? inner\n                : new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree(tree.type, tree.children.slice(0, i).concat(inner), tree.positions.slice(0, i + 1), pos + inner.length);\n        }\n    }\n    return null;\n}\nfunction findStartInFragments(lang, fragments, startPos, endPos, editorState) {\n    for (let f of fragments) {\n        let from = f.from + (f.openStart ? 25 : 0), to = f.to - (f.openEnd ? 25 : 0);\n        let found = from <= startPos && to > startPos && findState(lang, f.tree, 0 - f.offset, startPos, to), tree;\n        if (found && found.pos <= endPos && (tree = cutTree(lang, f.tree, startPos + f.offset, found.pos + f.offset, false)))\n            return { state: found.state, tree };\n    }\n    return { state: lang.streamParser.startState(editorState ? getIndentUnit(editorState) : 4), tree: _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree.empty };\n}\nclass Parse {\n    constructor(lang, input, fragments, ranges) {\n        this.lang = lang;\n        this.input = input;\n        this.fragments = fragments;\n        this.ranges = ranges;\n        this.stoppedAt = null;\n        this.chunks = [];\n        this.chunkPos = [];\n        this.chunk = [];\n        this.chunkReused = undefined;\n        this.rangeIndex = 0;\n        this.to = ranges[ranges.length - 1].to;\n        let context = ParseContext.get(), from = ranges[0].from;\n        let { state, tree } = findStartInFragments(lang, fragments, from, this.to, context === null || context === void 0 ? void 0 : context.state);\n        this.state = state;\n        this.parsedPos = this.chunkStart = from + tree.length;\n        for (let i = 0; i < tree.children.length; i++) {\n            this.chunks.push(tree.children[i]);\n            this.chunkPos.push(tree.positions[i]);\n        }\n        if (context && this.parsedPos < context.viewport.from - 100000 /* C.MaxDistanceBeforeViewport */ &&\n            ranges.some(r => r.from <= context.viewport.from && r.to >= context.viewport.from)) {\n            this.state = this.lang.streamParser.startState(getIndentUnit(context.state));\n            context.skipUntilInView(this.parsedPos, context.viewport.from);\n            this.parsedPos = context.viewport.from;\n        }\n        this.moveRangeIndex();\n    }\n    advance() {\n        let context = ParseContext.get();\n        let parseEnd = this.stoppedAt == null ? this.to : Math.min(this.to, this.stoppedAt);\n        let end = Math.min(parseEnd, this.chunkStart + 2048 /* C.ChunkSize */);\n        if (context)\n            end = Math.min(end, context.viewport.to);\n        while (this.parsedPos < end)\n            this.parseLine(context);\n        if (this.chunkStart < this.parsedPos)\n            this.finishChunk();\n        if (this.parsedPos >= parseEnd)\n            return this.finish();\n        if (context && this.parsedPos >= context.viewport.to) {\n            context.skipUntilInView(this.parsedPos, parseEnd);\n            return this.finish();\n        }\n        return null;\n    }\n    stopAt(pos) {\n        this.stoppedAt = pos;\n    }\n    lineAfter(pos) {\n        let chunk = this.input.chunk(pos);\n        if (!this.input.lineChunks) {\n            let eol = chunk.indexOf(\"\\n\");\n            if (eol > -1)\n                chunk = chunk.slice(0, eol);\n        }\n        else if (chunk == \"\\n\") {\n            chunk = \"\";\n        }\n        return pos + chunk.length <= this.to ? chunk : chunk.slice(0, this.to - pos);\n    }\n    nextLine() {\n        let from = this.parsedPos, line = this.lineAfter(from), end = from + line.length;\n        for (let index = this.rangeIndex;;) {\n            let rangeEnd = this.ranges[index].to;\n            if (rangeEnd >= end)\n                break;\n            line = line.slice(0, rangeEnd - (end - line.length));\n            index++;\n            if (index == this.ranges.length)\n                break;\n            let rangeStart = this.ranges[index].from;\n            let after = this.lineAfter(rangeStart);\n            line += after;\n            end = rangeStart + after.length;\n        }\n        return { line, end };\n    }\n    skipGapsTo(pos, offset, side) {\n        for (;;) {\n            let end = this.ranges[this.rangeIndex].to, offPos = pos + offset;\n            if (side > 0 ? end > offPos : end >= offPos)\n                break;\n            let start = this.ranges[++this.rangeIndex].from;\n            offset += start - end;\n        }\n        return offset;\n    }\n    moveRangeIndex() {\n        while (this.ranges[this.rangeIndex].to < this.parsedPos)\n            this.rangeIndex++;\n    }\n    emitToken(id, from, to, offset) {\n        let size = 4;\n        if (this.ranges.length > 1) {\n            offset = this.skipGapsTo(from, offset, 1);\n            from += offset;\n            let len0 = this.chunk.length;\n            offset = this.skipGapsTo(to, offset, -1);\n            to += offset;\n            size += this.chunk.length - len0;\n        }\n        let last = this.chunk.length - 4;\n        if (this.lang.streamParser.mergeTokens && size == 4 && last >= 0 &&\n            this.chunk[last] == id && this.chunk[last + 2] == from)\n            this.chunk[last + 2] = to;\n        else\n            this.chunk.push(id, from, to, size);\n        return offset;\n    }\n    parseLine(context) {\n        let { line, end } = this.nextLine(), offset = 0, { streamParser } = this.lang;\n        let stream = new StringStream(line, context ? context.state.tabSize : 4, context ? getIndentUnit(context.state) : 2);\n        if (stream.eol()) {\n            streamParser.blankLine(this.state, stream.indentUnit);\n        }\n        else {\n            while (!stream.eol()) {\n                let token = readToken(streamParser.token, stream, this.state);\n                if (token)\n                    offset = this.emitToken(this.lang.tokenTable.resolve(token), this.parsedPos + stream.start, this.parsedPos + stream.pos, offset);\n                if (stream.start > 10000 /* C.MaxLineLength */)\n                    break;\n            }\n        }\n        this.parsedPos = end;\n        this.moveRangeIndex();\n        if (this.parsedPos < this.to)\n            this.parsedPos++;\n    }\n    finishChunk() {\n        let tree = _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree.build({\n            buffer: this.chunk,\n            start: this.chunkStart,\n            length: this.parsedPos - this.chunkStart,\n            nodeSet,\n            topID: 0,\n            maxBufferLength: 2048 /* C.ChunkSize */,\n            reused: this.chunkReused\n        });\n        tree = new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree(tree.type, tree.children, tree.positions, tree.length, [[this.lang.stateAfter, this.lang.streamParser.copyState(this.state)]]);\n        this.chunks.push(tree);\n        this.chunkPos.push(this.chunkStart - this.ranges[0].from);\n        this.chunk = [];\n        this.chunkReused = undefined;\n        this.chunkStart = this.parsedPos;\n    }\n    finish() {\n        return new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.Tree(this.lang.topNode, this.chunks, this.chunkPos, this.parsedPos - this.ranges[0].from).balance();\n    }\n}\nfunction readToken(token, stream, state) {\n    stream.start = stream.pos;\n    for (let i = 0; i < 10; i++) {\n        let result = token(stream, state);\n        if (stream.pos > stream.start)\n            return result;\n    }\n    throw new Error(\"Stream parser failed to advance stream.\");\n}\nconst noTokens = /*@__PURE__*/Object.create(null);\nconst typeArray = [_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeType.none];\nconst nodeSet = /*@__PURE__*/new _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeSet(typeArray);\nconst warned = [];\n// Cache of node types by name and tags\nconst byTag = /*@__PURE__*/Object.create(null);\nconst defaultTable = /*@__PURE__*/Object.create(null);\nfor (let [legacyName, name] of [\n    [\"variable\", \"variableName\"],\n    [\"variable-2\", \"variableName.special\"],\n    [\"string-2\", \"string.special\"],\n    [\"def\", \"variableName.definition\"],\n    [\"tag\", \"tagName\"],\n    [\"attribute\", \"attributeName\"],\n    [\"type\", \"typeName\"],\n    [\"builtin\", \"variableName.standard\"],\n    [\"qualifier\", \"modifier\"],\n    [\"error\", \"invalid\"],\n    [\"header\", \"heading\"],\n    [\"property\", \"propertyName\"]\n])\n    defaultTable[legacyName] = /*@__PURE__*/createTokenType(noTokens, name);\nclass TokenTable {\n    constructor(extra) {\n        this.extra = extra;\n        this.table = Object.assign(Object.create(null), defaultTable);\n    }\n    resolve(tag) {\n        return !tag ? 0 : this.table[tag] || (this.table[tag] = createTokenType(this.extra, tag));\n    }\n}\nconst defaultTokenTable = /*@__PURE__*/new TokenTable(noTokens);\nfunction warnForPart(part, msg) {\n    if (warned.indexOf(part) > -1)\n        return;\n    warned.push(part);\n    console.warn(msg);\n}\nfunction createTokenType(extra, tagStr) {\n    let tags$1 = [];\n    for (let name of tagStr.split(\" \")) {\n        let found = [];\n        for (let part of name.split(\".\")) {\n            let value = (extra[part] || _lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.tags[part]);\n            if (!value) {\n                warnForPart(part, `Unknown highlighting tag ${part}`);\n            }\n            else if (typeof value == \"function\") {\n                if (!found.length)\n                    warnForPart(part, `Modifier ${part} used at start of tag`);\n                else\n                    found = found.map(value);\n            }\n            else {\n                if (found.length)\n                    warnForPart(part, `Tag ${part} used as modifier`);\n                else\n                    found = Array.isArray(value) ? value : [value];\n            }\n        }\n        for (let tag of found)\n            tags$1.push(tag);\n    }\n    if (!tags$1.length)\n        return 0;\n    let name = tagStr.replace(/ /g, \"_\"), key = name + \" \" + tags$1.map(t => t.id);\n    let known = byTag[key];\n    if (known)\n        return known.id;\n    let type = byTag[key] = _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeType.define({\n        id: typeArray.length,\n        name,\n        props: [(0,_lezer_highlight__WEBPACK_IMPORTED_MODULE_1__.styleTags)({ [name]: tags$1 })]\n    });\n    typeArray.push(type);\n    return type.id;\n}\nfunction docID(data, lang) {\n    let type = _lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeType.define({ id: typeArray.length, name: \"Document\", props: [\n            languageDataProp.add(() => data),\n            indentNodeProp.add(() => cx => lang.getIndent(cx))\n        ], top: true });\n    typeArray.push(type);\n    return type;\n}\n\nfunction buildForLine(line) {\n    return line.length <= 4096 && /[\\u0590-\\u05f4\\u0600-\\u06ff\\u0700-\\u08ac\\ufb50-\\ufdff]/.test(line);\n}\nfunction textHasRTL(text) {\n    for (let i = text.iter(); !i.next().done;)\n        if (buildForLine(i.value))\n            return true;\n    return false;\n}\nfunction changeAddsRTL(change) {\n    let added = false;\n    change.iterChanges((fA, tA, fB, tB, ins) => {\n        if (!added && textHasRTL(ins))\n            added = true;\n    });\n    return added;\n}\nconst alwaysIsolate = /*@__PURE__*/_codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Facet.define({ combine: values => values.some(x => x) });\n/**\nMake sure nodes\n[marked](https://lezer.codemirror.net/docs/ref/#common.NodeProp^isolate)\nas isolating for bidirectional text are rendered in a way that\nisolates them from the surrounding text.\n*/\nfunction bidiIsolates(options = {}) {\n    let extensions = [isolateMarks];\n    if (options.alwaysIsolate)\n        extensions.push(alwaysIsolate.of(true));\n    return extensions;\n}\nconst isolateMarks = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.always = view.state.facet(alwaysIsolate) ||\n            view.textDirection != _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Direction.LTR ||\n            view.state.facet(_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.perLineTextDirection);\n        this.hasRTL = !this.always && textHasRTL(view.state.doc);\n        this.tree = syntaxTree(view.state);\n        this.decorations = this.always || this.hasRTL ? buildDeco(view, this.tree, this.always) : _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.none;\n    }\n    update(update) {\n        let always = update.state.facet(alwaysIsolate) ||\n            update.view.textDirection != _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Direction.LTR ||\n            update.state.facet(_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.perLineTextDirection);\n        if (!always && !this.hasRTL && changeAddsRTL(update.changes))\n            this.hasRTL = true;\n        if (!always && !this.hasRTL)\n            return;\n        let tree = syntaxTree(update.state);\n        if (always != this.always || tree != this.tree || update.docChanged || update.viewportChanged) {\n            this.tree = tree;\n            this.always = always;\n            this.decorations = buildDeco(update.view, tree, always);\n        }\n    }\n}, {\n    provide: plugin => {\n        function access(view) {\n            var _a, _b;\n            return (_b = (_a = view.plugin(plugin)) === null || _a === void 0 ? void 0 : _a.decorations) !== null && _b !== void 0 ? _b : _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.none;\n        }\n        return [_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.outerDecorations.of(access),\n            _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.Prec.lowest(_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.bidiIsolatedRanges.of(access))];\n    }\n});\nfunction buildDeco(view, tree, always) {\n    let deco = new _codemirror_state__WEBPACK_IMPORTED_MODULE_3__.RangeSetBuilder();\n    let ranges = view.visibleRanges;\n    if (!always)\n        ranges = clipRTLLines(ranges, view.state.doc);\n    for (let { from, to } of ranges) {\n        tree.iterate({\n            enter: node => {\n                let iso = node.type.prop(_lezer_common__WEBPACK_IMPORTED_MODULE_0__.NodeProp.isolate);\n                if (iso)\n                    deco.add(node.from, node.to, marks[iso]);\n            },\n            from, to\n        });\n    }\n    return deco.finish();\n}\nfunction clipRTLLines(ranges, doc) {\n    let cur = doc.iter(), pos = 0, result = [], last = null;\n    for (let { from, to } of ranges) {\n        if (last && last.to > from) {\n            from = last.to;\n            if (from >= to)\n                continue;\n        }\n        if (pos + cur.value.length < from) {\n            cur.next(from - (pos + cur.value.length));\n            pos = from;\n        }\n        for (;;) {\n            let start = pos, end = pos + cur.value.length;\n            if (!cur.lineBreak && buildForLine(cur.value)) {\n                if (last && last.to > start - 10)\n                    last.to = Math.min(to, end);\n                else\n                    result.push(last = { from: start, to: Math.min(to, end) });\n            }\n            if (end >= to)\n                break;\n            pos = end;\n            cur.next();\n        }\n    }\n    return result;\n}\nconst marks = {\n    rtl: /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.mark({ class: \"cm-iso\", inclusive: true, attributes: { dir: \"rtl\" }, bidiIsolate: _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Direction.RTL }),\n    ltr: /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.mark({ class: \"cm-iso\", inclusive: true, attributes: { dir: \"ltr\" }, bidiIsolate: _codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Direction.LTR }),\n    auto: /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.Decoration.mark({ class: \"cm-iso\", inclusive: true, attributes: { dir: \"auto\" }, bidiIsolate: null })\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\n");

/***/ })

};
;