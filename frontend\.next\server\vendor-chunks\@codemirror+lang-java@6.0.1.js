"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-java@6.0.1";
exports.ids = ["vendor-chunks/@codemirror+lang-java@6.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-java@6.0.1/node_modules/@codemirror/lang-java/dist/index.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-java@6.0.1/node_modules/@codemirror/lang-java/dist/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   java: () => (/* binding */ java),\n/* harmony export */   javaLanguage: () => (/* binding */ javaLanguage)\n/* harmony export */ });\n/* harmony import */ var _lezer_java__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/java */ \"(ssr)/./node_modules/.pnpm/@lezer+java@1.1.3/node_modules/@lezer/java/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n\n\n\n/**\nA language provider based on the [Lezer Java\nparser](https://github.com/lezer-parser/java), extended with\nhighlighting and indentation information.\n*/\nconst javaLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LRLanguage.define({\n    name: \"java\",\n    parser: /*@__PURE__*/_lezer_java__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.indentNodeProp.add({\n                IfStatement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.continuedIndent)({ except: /^\\s*({|else\\b)/ }),\n                TryStatement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.continuedIndent)({ except: /^\\s*({|catch|finally)\\b/ }),\n                LabeledStatement: _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.flatIndent,\n                SwitchBlock: context => {\n                    let after = context.textAfter, closed = /^\\s*\\}/.test(after), isCase = /^\\s*(case|default)\\b/.test(after);\n                    return context.baseIndent + (closed ? 0 : isCase ? 1 : 2) * context.unit;\n                },\n                Block: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.delimitedIndent)({ closing: \"}\" }),\n                BlockComment: () => null,\n                Statement: /*@__PURE__*/(0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.continuedIndent)({ except: /^{/ })\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldNodeProp.add({\n                [\"Block SwitchBlock ClassBody ElementValueArrayInitializer ModuleBody EnumBody \" +\n                    \"ConstructorBody InterfaceBody ArrayInitializer\"]: _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldInside,\n                BlockComment(tree) { return { from: tree.from + 2, to: tree.to - 2 }; }\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { line: \"//\", block: { open: \"/*\", close: \"*/\" } },\n        indentOnInput: /^\\s*(?:case |default:|\\{|\\})$/\n    }\n});\n/**\nJava language support.\n*/\nfunction java() {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LanguageSupport(javaLanguage);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGNvZGVtaXJyb3IrbGFuZy1qYXZhQDYuMC4xL25vZGVfbW9kdWxlcy9AY29kZW1pcnJvci9sYW5nLWphdmEvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXFDO0FBQ3NIOztBQUUzSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLDREQUFVO0FBQzVDO0FBQ0EseUJBQXlCLCtDQUFNO0FBQy9CO0FBQ0EseUJBQXlCLGdFQUFjO0FBQ3ZDLDBDQUEwQyxxRUFBZSxHQUFHLGVBQWUsV0FBVztBQUN0RiwyQ0FBMkMscUVBQWUsR0FBRyxlQUFlLG9CQUFvQjtBQUNoRyxrQ0FBa0MsNERBQVU7QUFDNUM7QUFDQSxtRUFBbUU7QUFDbkU7QUFDQSxpQkFBaUI7QUFDakIsb0NBQW9DLHFFQUFlLEdBQUcsV0FBVyxHQUFHO0FBQ3BFO0FBQ0Esd0NBQXdDLHFFQUFlLEdBQUcsV0FBVyxHQUFHO0FBQ3hFLGFBQWE7QUFDYix5QkFBeUIsOERBQVk7QUFDckM7QUFDQSx1RUFBdUUsNERBQVU7QUFDakYscUNBQXFDLFNBQVM7QUFDOUMsYUFBYTtBQUNiO0FBQ0EsS0FBSztBQUNMO0FBQ0EseUJBQXlCLHFCQUFxQiwyQkFBMkI7QUFDekUsZ0RBQWdELEdBQUc7QUFDbkQ7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGlFQUFlO0FBQzlCOztBQUU4QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbmRyZVxcT25lRHJpdmVcXERlc2t0b3BcXEdpdGh1YiBSZXBvc2l0b3JpZXNcXGRlbW9cXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAY29kZW1pcnJvcitsYW5nLWphdmFANi4wLjFcXG5vZGVfbW9kdWxlc1xcQGNvZGVtaXJyb3JcXGxhbmctamF2YVxcZGlzdFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgcGFyc2VyIH0gZnJvbSAnQGxlemVyL2phdmEnO1xuaW1wb3J0IHsgTFJMYW5ndWFnZSwgaW5kZW50Tm9kZVByb3AsIGNvbnRpbnVlZEluZGVudCwgZmxhdEluZGVudCwgZGVsaW1pdGVkSW5kZW50LCBmb2xkTm9kZVByb3AsIGZvbGRJbnNpZGUsIExhbmd1YWdlU3VwcG9ydCB9IGZyb20gJ0Bjb2RlbWlycm9yL2xhbmd1YWdlJztcblxuLyoqXG5BIGxhbmd1YWdlIHByb3ZpZGVyIGJhc2VkIG9uIHRoZSBbTGV6ZXIgSmF2YVxucGFyc2VyXShodHRwczovL2dpdGh1Yi5jb20vbGV6ZXItcGFyc2VyL2phdmEpLCBleHRlbmRlZCB3aXRoXG5oaWdobGlnaHRpbmcgYW5kIGluZGVudGF0aW9uIGluZm9ybWF0aW9uLlxuKi9cbmNvbnN0IGphdmFMYW5ndWFnZSA9IC8qQF9fUFVSRV9fKi9MUkxhbmd1YWdlLmRlZmluZSh7XG4gICAgbmFtZTogXCJqYXZhXCIsXG4gICAgcGFyc2VyOiAvKkBfX1BVUkVfXyovcGFyc2VyLmNvbmZpZ3VyZSh7XG4gICAgICAgIHByb3BzOiBbXG4gICAgICAgICAgICAvKkBfX1BVUkVfXyovaW5kZW50Tm9kZVByb3AuYWRkKHtcbiAgICAgICAgICAgICAgICBJZlN0YXRlbWVudDogLypAX19QVVJFX18qL2NvbnRpbnVlZEluZGVudCh7IGV4Y2VwdDogL15cXHMqKHt8ZWxzZVxcYikvIH0pLFxuICAgICAgICAgICAgICAgIFRyeVN0YXRlbWVudDogLypAX19QVVJFX18qL2NvbnRpbnVlZEluZGVudCh7IGV4Y2VwdDogL15cXHMqKHt8Y2F0Y2h8ZmluYWxseSlcXGIvIH0pLFxuICAgICAgICAgICAgICAgIExhYmVsZWRTdGF0ZW1lbnQ6IGZsYXRJbmRlbnQsXG4gICAgICAgICAgICAgICAgU3dpdGNoQmxvY2s6IGNvbnRleHQgPT4ge1xuICAgICAgICAgICAgICAgICAgICBsZXQgYWZ0ZXIgPSBjb250ZXh0LnRleHRBZnRlciwgY2xvc2VkID0gL15cXHMqXFx9Ly50ZXN0KGFmdGVyKSwgaXNDYXNlID0gL15cXHMqKGNhc2V8ZGVmYXVsdClcXGIvLnRlc3QoYWZ0ZXIpO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gY29udGV4dC5iYXNlSW5kZW50ICsgKGNsb3NlZCA/IDAgOiBpc0Nhc2UgPyAxIDogMikgKiBjb250ZXh0LnVuaXQ7XG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBCbG9jazogLypAX19QVVJFX18qL2RlbGltaXRlZEluZGVudCh7IGNsb3Npbmc6IFwifVwiIH0pLFxuICAgICAgICAgICAgICAgIEJsb2NrQ29tbWVudDogKCkgPT4gbnVsbCxcbiAgICAgICAgICAgICAgICBTdGF0ZW1lbnQ6IC8qQF9fUFVSRV9fKi9jb250aW51ZWRJbmRlbnQoeyBleGNlcHQ6IC9eey8gfSlcbiAgICAgICAgICAgIH0pLFxuICAgICAgICAgICAgLypAX19QVVJFX18qL2ZvbGROb2RlUHJvcC5hZGQoe1xuICAgICAgICAgICAgICAgIFtcIkJsb2NrIFN3aXRjaEJsb2NrIENsYXNzQm9keSBFbGVtZW50VmFsdWVBcnJheUluaXRpYWxpemVyIE1vZHVsZUJvZHkgRW51bUJvZHkgXCIgK1xuICAgICAgICAgICAgICAgICAgICBcIkNvbnN0cnVjdG9yQm9keSBJbnRlcmZhY2VCb2R5IEFycmF5SW5pdGlhbGl6ZXJcIl06IGZvbGRJbnNpZGUsXG4gICAgICAgICAgICAgICAgQmxvY2tDb21tZW50KHRyZWUpIHsgcmV0dXJuIHsgZnJvbTogdHJlZS5mcm9tICsgMiwgdG86IHRyZWUudG8gLSAyIH07IH1cbiAgICAgICAgICAgIH0pXG4gICAgICAgIF1cbiAgICB9KSxcbiAgICBsYW5ndWFnZURhdGE6IHtcbiAgICAgICAgY29tbWVudFRva2VuczogeyBsaW5lOiBcIi8vXCIsIGJsb2NrOiB7IG9wZW46IFwiLypcIiwgY2xvc2U6IFwiKi9cIiB9IH0sXG4gICAgICAgIGluZGVudE9uSW5wdXQ6IC9eXFxzKig/OmNhc2UgfGRlZmF1bHQ6fFxce3xcXH0pJC9cbiAgICB9XG59KTtcbi8qKlxuSmF2YSBsYW5ndWFnZSBzdXBwb3J0LlxuKi9cbmZ1bmN0aW9uIGphdmEoKSB7XG4gICAgcmV0dXJuIG5ldyBMYW5ndWFnZVN1cHBvcnQoamF2YUxhbmd1YWdlKTtcbn1cblxuZXhwb3J0IHsgamF2YSwgamF2YUxhbmd1YWdlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-java@6.0.1/node_modules/@codemirror/lang-java/dist/index.js\n");

/***/ })

};
;