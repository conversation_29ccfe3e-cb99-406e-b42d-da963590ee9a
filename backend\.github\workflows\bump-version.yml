name: Bump Version

on:
  workflow_dispatch:
    inputs:
      version_part:
        description: 'Part of version to bump (major, minor, patch)'
        required: true
        default: 'patch'
        type: choice
        options:
          - major
          - minor
          - patch

# Add these permissions
permissions:
  contents: write
  pull-requests: write

jobs:
  bump-version:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'
      
      - name: Install Poetry
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
      
      - name: Configure Git
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
      
      - name: Bump version
        run: |
          poetry version ${{ github.event.inputs.version_part }}
          NEW_VERSION=$(poetry version -s)
          echo "NEW_VERSION=$NEW_VERSION" >> $GITHUB_ENV
      
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          commit-message: "chore: bump version to ${{ env.NEW_VERSION }}"
          title: "Bump version to ${{ env.NEW_VERSION }}"
          body: "Automated version bump to ${{ env.NEW_VERSION }}"
          branch: "bump-version-${{ env.NEW_VERSION }}"
          base: "main"