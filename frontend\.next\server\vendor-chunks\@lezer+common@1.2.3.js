"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lezer+common@1.2.3";
exports.ids = ["vendor-chunks/@lezer+common@1.2.3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultBufferLength: () => (/* binding */ DefaultBufferLength),\n/* harmony export */   IterMode: () => (/* binding */ IterMode),\n/* harmony export */   MountedTree: () => (/* binding */ MountedTree),\n/* harmony export */   NodeProp: () => (/* binding */ NodeProp),\n/* harmony export */   NodeSet: () => (/* binding */ NodeSet),\n/* harmony export */   NodeType: () => (/* binding */ NodeType),\n/* harmony export */   NodeWeakMap: () => (/* binding */ NodeWeakMap),\n/* harmony export */   Parser: () => (/* binding */ Parser),\n/* harmony export */   Tree: () => (/* binding */ Tree),\n/* harmony export */   TreeBuffer: () => (/* binding */ TreeBuffer),\n/* harmony export */   TreeCursor: () => (/* binding */ TreeCursor),\n/* harmony export */   TreeFragment: () => (/* binding */ TreeFragment),\n/* harmony export */   parseMixed: () => (/* binding */ parseMixed)\n/* harmony export */ });\n/**\nThe default maximum length of a `TreeBuffer` node.\n*/\nconst DefaultBufferLength = 1024;\nlet nextPropID = 0;\nclass Range {\n    constructor(from, to) {\n        this.from = from;\n        this.to = to;\n    }\n}\n/**\nEach [node type](#common.NodeType) or [individual tree](#common.Tree)\ncan have metadata associated with it in props. Instances of this\nclass represent prop names.\n*/\nclass NodeProp {\n    /**\n    Create a new node prop type.\n    */\n    constructor(config = {}) {\n        this.id = nextPropID++;\n        this.perNode = !!config.perNode;\n        this.deserialize = config.deserialize || (() => {\n            throw new Error(\"This node type doesn't define a deserialize function\");\n        });\n    }\n    /**\n    This is meant to be used with\n    [`NodeSet.extend`](#common.NodeSet.extend) or\n    [`LRParser.configure`](#lr.ParserConfig.props) to compute\n    prop values for each node type in the set. Takes a [match\n    object](#common.NodeType^match) or function that returns undefined\n    if the node type doesn't get this prop, and the prop's value if\n    it does.\n    */\n    add(match) {\n        if (this.perNode)\n            throw new RangeError(\"Can't add per-node props to node types\");\n        if (typeof match != \"function\")\n            match = NodeType.match(match);\n        return (type) => {\n            let result = match(type);\n            return result === undefined ? null : [this, result];\n        };\n    }\n}\n/**\nProp that is used to describe matching delimiters. For opening\ndelimiters, this holds an array of node names (written as a\nspace-separated string when declaring this prop in a grammar)\nfor the node types of closing delimiters that match it.\n*/\nNodeProp.closedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nThe inverse of [`closedBy`](#common.NodeProp^closedBy). This is\nattached to closing delimiters, holding an array of node names\nof types of matching opening delimiters.\n*/\nNodeProp.openedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nUsed to assign node types to groups (for example, all node\ntypes that represent an expression could be tagged with an\n`\"Expression\"` group).\n*/\nNodeProp.group = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nAttached to nodes to indicate these should be\n[displayed](https://codemirror.net/docs/ref/#language.syntaxTree)\nin a bidirectional text isolate, so that direction-neutral\ncharacters on their sides don't incorrectly get associated with\nsurrounding text. You'll generally want to set this for nodes\nthat contain arbitrary text, like strings and comments, and for\nnodes that appear _inside_ arbitrary text, like HTML tags. When\nnot given a value, in a grammar declaration, defaults to\n`\"auto\"`.\n*/\nNodeProp.isolate = new NodeProp({ deserialize: value => {\n        if (value && value != \"rtl\" && value != \"ltr\" && value != \"auto\")\n            throw new RangeError(\"Invalid value for isolate: \" + value);\n        return value || \"auto\";\n    } });\n/**\nThe hash of the [context](#lr.ContextTracker.constructor)\nthat the node was parsed in, if any. Used to limit reuse of\ncontextual nodes.\n*/\nNodeProp.contextHash = new NodeProp({ perNode: true });\n/**\nThe distance beyond the end of the node that the tokenizer\nlooked ahead for any of the tokens inside the node. (The LR\nparser only stores this when it is larger than 25, for\nefficiency reasons.)\n*/\nNodeProp.lookAhead = new NodeProp({ perNode: true });\n/**\nThis per-node prop is used to replace a given node, or part of a\nnode, with another tree. This is useful to include trees from\ndifferent languages in mixed-language parsers.\n*/\nNodeProp.mounted = new NodeProp({ perNode: true });\n/**\nA mounted tree, which can be [stored](#common.NodeProp^mounted) on\na tree node to indicate that parts of its content are\nrepresented by another tree.\n*/\nclass MountedTree {\n    constructor(\n    /**\n    The inner tree.\n    */\n    tree, \n    /**\n    If this is null, this tree replaces the entire node (it will\n    be included in the regular iteration instead of its host\n    node). If not, only the given ranges are considered to be\n    covered by this tree. This is used for trees that are mixed in\n    a way that isn't strictly hierarchical. Such mounted trees are\n    only entered by [`resolveInner`](#common.Tree.resolveInner)\n    and [`enter`](#common.SyntaxNode.enter).\n    */\n    overlay, \n    /**\n    The parser used to create this subtree.\n    */\n    parser) {\n        this.tree = tree;\n        this.overlay = overlay;\n        this.parser = parser;\n    }\n    /**\n    @internal\n    */\n    static get(tree) {\n        return tree && tree.props && tree.props[NodeProp.mounted.id];\n    }\n}\nconst noProps = Object.create(null);\n/**\nEach node in a syntax tree has a node type associated with it.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the node type. Not necessarily unique, but if the\n    grammar was written properly, different node types with the\n    same name within a node set should play the same semantic\n    role.\n    */\n    name, \n    /**\n    @internal\n    */\n    props, \n    /**\n    The id of this node in its set. Corresponds to the term ids\n    used in the parser.\n    */\n    id, \n    /**\n    @internal\n    */\n    flags = 0) {\n        this.name = name;\n        this.props = props;\n        this.id = id;\n        this.flags = flags;\n    }\n    /**\n    Define a node type.\n    */\n    static define(spec) {\n        let props = spec.props && spec.props.length ? Object.create(null) : noProps;\n        let flags = (spec.top ? 1 /* NodeFlag.Top */ : 0) | (spec.skipped ? 2 /* NodeFlag.Skipped */ : 0) |\n            (spec.error ? 4 /* NodeFlag.Error */ : 0) | (spec.name == null ? 8 /* NodeFlag.Anonymous */ : 0);\n        let type = new NodeType(spec.name || \"\", props, spec.id, flags);\n        if (spec.props)\n            for (let src of spec.props) {\n                if (!Array.isArray(src))\n                    src = src(type);\n                if (src) {\n                    if (src[0].perNode)\n                        throw new RangeError(\"Can't store a per-node prop on a node type\");\n                    props[src[0].id] = src[1];\n                }\n            }\n        return type;\n    }\n    /**\n    Retrieves a node prop for this type. Will return `undefined` if\n    the prop isn't present on this node.\n    */\n    prop(prop) { return this.props[prop.id]; }\n    /**\n    True when this is the top node of a grammar.\n    */\n    get isTop() { return (this.flags & 1 /* NodeFlag.Top */) > 0; }\n    /**\n    True when this node is produced by a skip rule.\n    */\n    get isSkipped() { return (this.flags & 2 /* NodeFlag.Skipped */) > 0; }\n    /**\n    Indicates whether this is an error node.\n    */\n    get isError() { return (this.flags & 4 /* NodeFlag.Error */) > 0; }\n    /**\n    When true, this node type doesn't correspond to a user-declared\n    named node, for example because it is used to cache repetition.\n    */\n    get isAnonymous() { return (this.flags & 8 /* NodeFlag.Anonymous */) > 0; }\n    /**\n    Returns true when this node's name or one of its\n    [groups](#common.NodeProp^group) matches the given string.\n    */\n    is(name) {\n        if (typeof name == 'string') {\n            if (this.name == name)\n                return true;\n            let group = this.prop(NodeProp.group);\n            return group ? group.indexOf(name) > -1 : false;\n        }\n        return this.id == name;\n    }\n    /**\n    Create a function from node types to arbitrary values by\n    specifying an object whose property names are node or\n    [group](#common.NodeProp^group) names. Often useful with\n    [`NodeProp.add`](#common.NodeProp.add). You can put multiple\n    names, separated by spaces, in a single property name to map\n    multiple node names to a single value.\n    */\n    static match(map) {\n        let direct = Object.create(null);\n        for (let prop in map)\n            for (let name of prop.split(\" \"))\n                direct[name] = map[prop];\n        return (node) => {\n            for (let groups = node.prop(NodeProp.group), i = -1; i < (groups ? groups.length : 0); i++) {\n                let found = direct[i < 0 ? node.name : groups[i]];\n                if (found)\n                    return found;\n            }\n        };\n    }\n}\n/**\nAn empty dummy node type to use when no actual type is available.\n*/\nNodeType.none = new NodeType(\"\", Object.create(null), 0, 8 /* NodeFlag.Anonymous */);\n/**\nA node set holds a collection of node types. It is used to\ncompactly represent trees by storing their type ids, rather than a\nfull pointer to the type object, in a numeric array. Each parser\n[has](#lr.LRParser.nodeSet) a node set, and [tree\nbuffers](#common.TreeBuffer) can only store collections of nodes\nfrom the same set. A set can have a maximum of 2**16 (65536) node\ntypes in it, so that the ids fit into 16-bit typed array slots.\n*/\nclass NodeSet {\n    /**\n    Create a set with the given types. The `id` property of each\n    type should correspond to its position within the array.\n    */\n    constructor(\n    /**\n    The node types in this set, by id.\n    */\n    types) {\n        this.types = types;\n        for (let i = 0; i < types.length; i++)\n            if (types[i].id != i)\n                throw new RangeError(\"Node type ids should correspond to array positions when creating a node set\");\n    }\n    /**\n    Create a copy of this set with some node properties added. The\n    arguments to this method can be created with\n    [`NodeProp.add`](#common.NodeProp.add).\n    */\n    extend(...props) {\n        let newTypes = [];\n        for (let type of this.types) {\n            let newProps = null;\n            for (let source of props) {\n                let add = source(type);\n                if (add) {\n                    if (!newProps)\n                        newProps = Object.assign({}, type.props);\n                    newProps[add[0].id] = add[1];\n                }\n            }\n            newTypes.push(newProps ? new NodeType(type.name, newProps, type.id, type.flags) : type);\n        }\n        return new NodeSet(newTypes);\n    }\n}\nconst CachedNode = new WeakMap(), CachedInnerNode = new WeakMap();\n/**\nOptions that control iteration. Can be combined with the `|`\noperator to enable multiple ones.\n*/\nvar IterMode;\n(function (IterMode) {\n    /**\n    When enabled, iteration will only visit [`Tree`](#common.Tree)\n    objects, not nodes packed into\n    [`TreeBuffer`](#common.TreeBuffer)s.\n    */\n    IterMode[IterMode[\"ExcludeBuffers\"] = 1] = \"ExcludeBuffers\";\n    /**\n    Enable this to make iteration include anonymous nodes (such as\n    the nodes that wrap repeated grammar constructs into a balanced\n    tree).\n    */\n    IterMode[IterMode[\"IncludeAnonymous\"] = 2] = \"IncludeAnonymous\";\n    /**\n    By default, regular [mounted](#common.NodeProp^mounted) nodes\n    replace their base node in iteration. Enable this to ignore them\n    instead.\n    */\n    IterMode[IterMode[\"IgnoreMounts\"] = 4] = \"IgnoreMounts\";\n    /**\n    This option only applies in\n    [`enter`](#common.SyntaxNode.enter)-style methods. It tells the\n    library to not enter mounted overlays if one covers the given\n    position.\n    */\n    IterMode[IterMode[\"IgnoreOverlays\"] = 8] = \"IgnoreOverlays\";\n})(IterMode || (IterMode = {}));\n/**\nA piece of syntax tree. There are two ways to approach these\ntrees: the way they are actually stored in memory, and the\nconvenient way.\n\nSyntax trees are stored as a tree of `Tree` and `TreeBuffer`\nobjects. By packing detail information into `TreeBuffer` leaf\nnodes, the representation is made a lot more memory-efficient.\n\nHowever, when you want to actually work with tree nodes, this\nrepresentation is very awkward, so most client code will want to\nuse the [`TreeCursor`](#common.TreeCursor) or\n[`SyntaxNode`](#common.SyntaxNode) interface instead, which provides\na view on some part of this data structure, and can be used to\nmove around to adjacent nodes.\n*/\nclass Tree {\n    /**\n    Construct a new tree. See also [`Tree.build`](#common.Tree^build).\n    */\n    constructor(\n    /**\n    The type of the top node.\n    */\n    type, \n    /**\n    This node's child nodes.\n    */\n    children, \n    /**\n    The positions (offsets relative to the start of this tree) of\n    the children.\n    */\n    positions, \n    /**\n    The total length of this tree\n    */\n    length, \n    /**\n    Per-node [node props](#common.NodeProp) to associate with this node.\n    */\n    props) {\n        this.type = type;\n        this.children = children;\n        this.positions = positions;\n        this.length = length;\n        /**\n        @internal\n        */\n        this.props = null;\n        if (props && props.length) {\n            this.props = Object.create(null);\n            for (let [prop, value] of props)\n                this.props[typeof prop == \"number\" ? prop : prop.id] = value;\n        }\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let mounted = MountedTree.get(this);\n        if (mounted && !mounted.overlay)\n            return mounted.tree.toString();\n        let children = \"\";\n        for (let ch of this.children) {\n            let str = ch.toString();\n            if (str) {\n                if (children)\n                    children += \",\";\n                children += str;\n            }\n        }\n        return !this.type.name ? children :\n            (/\\W/.test(this.type.name) && !this.type.isError ? JSON.stringify(this.type.name) : this.type.name) +\n                (children.length ? \"(\" + children + \")\" : \"\");\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) positioned at the top of\n    the tree. Mode can be used to [control](#common.IterMode) which\n    nodes the cursor visits.\n    */\n    cursor(mode = 0) {\n        return new TreeCursor(this.topNode, mode);\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) pointing into this tree\n    at the given position and side (see\n    [`moveTo`](#common.TreeCursor.moveTo).\n    */\n    cursorAt(pos, side = 0, mode = 0) {\n        let scope = CachedNode.get(this) || this.topNode;\n        let cursor = new TreeCursor(scope);\n        cursor.moveTo(pos, side);\n        CachedNode.set(this, cursor._tree);\n        return cursor;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) object for the top of the\n    tree.\n    */\n    get topNode() {\n        return new TreeNode(this, 0, 0, null);\n    }\n    /**\n    Get the [syntax node](#common.SyntaxNode) at the given position.\n    If `side` is -1, this will move into nodes that end at the\n    position. If 1, it'll move into nodes that start at the\n    position. With 0, it'll only enter nodes that cover the position\n    from both sides.\n    \n    Note that this will not enter\n    [overlays](#common.MountedTree.overlay), and you often want\n    [`resolveInner`](#common.Tree.resolveInner) instead.\n    */\n    resolve(pos, side = 0) {\n        let node = resolveNode(CachedNode.get(this) || this.topNode, pos, side, false);\n        CachedNode.set(this, node);\n        return node;\n    }\n    /**\n    Like [`resolve`](#common.Tree.resolve), but will enter\n    [overlaid](#common.MountedTree.overlay) nodes, producing a syntax node\n    pointing into the innermost overlaid tree at the given position\n    (with parent links going through all parent structure, including\n    the host trees).\n    */\n    resolveInner(pos, side = 0) {\n        let node = resolveNode(CachedInnerNode.get(this) || this.topNode, pos, side, true);\n        CachedInnerNode.set(this, node);\n        return node;\n    }\n    /**\n    In some situations, it can be useful to iterate through all\n    nodes around a position, including those in overlays that don't\n    directly cover the position. This method gives you an iterator\n    that will produce all nodes, from small to big, around the given\n    position.\n    */\n    resolveStack(pos, side = 0) {\n        return stackIterator(this, pos, side);\n    }\n    /**\n    Iterate over the tree and its children, calling `enter` for any\n    node that touches the `from`/`to` region (if given) before\n    running over such a node's children, and `leave` (if given) when\n    leaving the node. When `enter` returns `false`, that node will\n    not have its children iterated over (or `leave` called).\n    */\n    iterate(spec) {\n        let { enter, leave, from = 0, to = this.length } = spec;\n        let mode = spec.mode || 0, anon = (mode & IterMode.IncludeAnonymous) > 0;\n        for (let c = this.cursor(mode | IterMode.IncludeAnonymous);;) {\n            let entered = false;\n            if (c.from <= to && c.to >= from && (!anon && c.type.isAnonymous || enter(c) !== false)) {\n                if (c.firstChild())\n                    continue;\n                entered = true;\n            }\n            for (;;) {\n                if (entered && leave && (anon || !c.type.isAnonymous))\n                    leave(c);\n                if (c.nextSibling())\n                    break;\n                if (!c.parent())\n                    return;\n                entered = true;\n            }\n        }\n    }\n    /**\n    Get the value of the given [node prop](#common.NodeProp) for this\n    node. Works with both per-node and per-type props.\n    */\n    prop(prop) {\n        return !prop.perNode ? this.type.prop(prop) : this.props ? this.props[prop.id] : undefined;\n    }\n    /**\n    Returns the node's [per-node props](#common.NodeProp.perNode) in a\n    format that can be passed to the [`Tree`](#common.Tree)\n    constructor.\n    */\n    get propValues() {\n        let result = [];\n        if (this.props)\n            for (let id in this.props)\n                result.push([+id, this.props[id]]);\n        return result;\n    }\n    /**\n    Balance the direct children of this tree, producing a copy of\n    which may have children grouped into subtrees with type\n    [`NodeType.none`](#common.NodeType^none).\n    */\n    balance(config = {}) {\n        return this.children.length <= 8 /* Balance.BranchFactor */ ? this :\n            balanceRange(NodeType.none, this.children, this.positions, 0, this.children.length, 0, this.length, (children, positions, length) => new Tree(this.type, children, positions, length, this.propValues), config.makeTree || ((children, positions, length) => new Tree(NodeType.none, children, positions, length)));\n    }\n    /**\n    Build a tree from a postfix-ordered buffer of node information,\n    or a cursor over such a buffer.\n    */\n    static build(data) { return buildTree(data); }\n}\n/**\nThe empty tree\n*/\nTree.empty = new Tree(NodeType.none, [], [], 0);\nclass FlatBufferCursor {\n    constructor(buffer, index) {\n        this.buffer = buffer;\n        this.index = index;\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    get pos() { return this.index; }\n    next() { this.index -= 4; }\n    fork() { return new FlatBufferCursor(this.buffer, this.index); }\n}\n/**\nTree buffers contain (type, start, end, endIndex) quads for each\nnode. In such a buffer, nodes are stored in prefix order (parents\nbefore children, with the endIndex of the parent indicating which\nchildren belong to it).\n*/\nclass TreeBuffer {\n    /**\n    Create a tree buffer.\n    */\n    constructor(\n    /**\n    The buffer's content.\n    */\n    buffer, \n    /**\n    The total length of the group of nodes in the buffer.\n    */\n    length, \n    /**\n    The node set used in this buffer.\n    */\n    set) {\n        this.buffer = buffer;\n        this.length = length;\n        this.set = set;\n    }\n    /**\n    @internal\n    */\n    get type() { return NodeType.none; }\n    /**\n    @internal\n    */\n    toString() {\n        let result = [];\n        for (let index = 0; index < this.buffer.length;) {\n            result.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result.join(\",\");\n    }\n    /**\n    @internal\n    */\n    childString(index) {\n        let id = this.buffer[index], endIndex = this.buffer[index + 3];\n        let type = this.set.types[id], result = type.name;\n        if (/\\W/.test(result) && !type.isError)\n            result = JSON.stringify(result);\n        index += 4;\n        if (endIndex == index)\n            return result;\n        let children = [];\n        while (index < endIndex) {\n            children.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result + \"(\" + children.join(\",\") + \")\";\n    }\n    /**\n    @internal\n    */\n    findChild(startIndex, endIndex, dir, pos, side) {\n        let { buffer } = this, pick = -1;\n        for (let i = startIndex; i != endIndex; i = buffer[i + 3]) {\n            if (checkSide(side, pos, buffer[i + 1], buffer[i + 2])) {\n                pick = i;\n                if (dir > 0)\n                    break;\n            }\n        }\n        return pick;\n    }\n    /**\n    @internal\n    */\n    slice(startI, endI, from) {\n        let b = this.buffer;\n        let copy = new Uint16Array(endI - startI), len = 0;\n        for (let i = startI, j = 0; i < endI;) {\n            copy[j++] = b[i++];\n            copy[j++] = b[i++] - from;\n            let to = copy[j++] = b[i++] - from;\n            copy[j++] = b[i++] - startI;\n            len = Math.max(len, to);\n        }\n        return new TreeBuffer(copy, len, this.set);\n    }\n}\nfunction checkSide(side, pos, from, to) {\n    switch (side) {\n        case -2 /* Side.Before */: return from < pos;\n        case -1 /* Side.AtOrBefore */: return to >= pos && from < pos;\n        case 0 /* Side.Around */: return from < pos && to > pos;\n        case 1 /* Side.AtOrAfter */: return from <= pos && to > pos;\n        case 2 /* Side.After */: return to > pos;\n        case 4 /* Side.DontCare */: return true;\n    }\n}\nfunction resolveNode(node, pos, side, overlays) {\n    var _a;\n    // Move up to a node that actually holds the position, if possible\n    while (node.from == node.to ||\n        (side < 1 ? node.from >= pos : node.from > pos) ||\n        (side > -1 ? node.to <= pos : node.to < pos)) {\n        let parent = !overlays && node instanceof TreeNode && node.index < 0 ? null : node.parent;\n        if (!parent)\n            return node;\n        node = parent;\n    }\n    let mode = overlays ? 0 : IterMode.IgnoreOverlays;\n    // Must go up out of overlays when those do not overlap with pos\n    if (overlays)\n        for (let scan = node, parent = scan.parent; parent; scan = parent, parent = scan.parent) {\n            if (scan instanceof TreeNode && scan.index < 0 && ((_a = parent.enter(pos, side, mode)) === null || _a === void 0 ? void 0 : _a.from) != scan.from)\n                node = parent;\n        }\n    for (;;) {\n        let inner = node.enter(pos, side, mode);\n        if (!inner)\n            return node;\n        node = inner;\n    }\n}\nclass BaseNode {\n    cursor(mode = 0) { return new TreeCursor(this, mode); }\n    getChild(type, before = null, after = null) {\n        let r = getChildren(this, type, before, after);\n        return r.length ? r[0] : null;\n    }\n    getChildren(type, before = null, after = null) {\n        return getChildren(this, type, before, after);\n    }\n    resolve(pos, side = 0) {\n        return resolveNode(this, pos, side, false);\n    }\n    resolveInner(pos, side = 0) {\n        return resolveNode(this, pos, side, true);\n    }\n    matchContext(context) {\n        return matchNodeContext(this.parent, context);\n    }\n    enterUnfinishedNodesBefore(pos) {\n        let scan = this.childBefore(pos), node = this;\n        while (scan) {\n            let last = scan.lastChild;\n            if (!last || last.to != scan.to)\n                break;\n            if (last.type.isError && last.from == last.to) {\n                node = scan;\n                scan = last.prevSibling;\n            }\n            else {\n                scan = last;\n            }\n        }\n        return node;\n    }\n    get node() { return this; }\n    get next() { return this.parent; }\n}\nclass TreeNode extends BaseNode {\n    constructor(_tree, from, \n    // Index in parent node, set to -1 if the node is not a direct child of _parent.node (overlay)\n    index, _parent) {\n        super();\n        this._tree = _tree;\n        this.from = from;\n        this.index = index;\n        this._parent = _parent;\n    }\n    get type() { return this._tree.type; }\n    get name() { return this._tree.type.name; }\n    get to() { return this.from + this._tree.length; }\n    nextChild(i, dir, pos, side, mode = 0) {\n        for (let parent = this;;) {\n            for (let { children, positions } = parent._tree, e = dir > 0 ? children.length : -1; i != e; i += dir) {\n                let next = children[i], start = positions[i] + parent.from;\n                if (!checkSide(side, pos, start, start + next.length))\n                    continue;\n                if (next instanceof TreeBuffer) {\n                    if (mode & IterMode.ExcludeBuffers)\n                        continue;\n                    let index = next.findChild(0, next.buffer.length, dir, pos - start, side);\n                    if (index > -1)\n                        return new BufferNode(new BufferContext(parent, next, i, start), null, index);\n                }\n                else if ((mode & IterMode.IncludeAnonymous) || (!next.type.isAnonymous || hasChild(next))) {\n                    let mounted;\n                    if (!(mode & IterMode.IgnoreMounts) && (mounted = MountedTree.get(next)) && !mounted.overlay)\n                        return new TreeNode(mounted.tree, start, i, parent);\n                    let inner = new TreeNode(next, start, i, parent);\n                    return (mode & IterMode.IncludeAnonymous) || !inner.type.isAnonymous ? inner\n                        : inner.nextChild(dir < 0 ? next.children.length - 1 : 0, dir, pos, side);\n                }\n            }\n            if ((mode & IterMode.IncludeAnonymous) || !parent.type.isAnonymous)\n                return null;\n            if (parent.index >= 0)\n                i = parent.index + dir;\n            else\n                i = dir < 0 ? -1 : parent._parent._tree.children.length;\n            parent = parent._parent;\n            if (!parent)\n                return null;\n        }\n    }\n    get firstChild() { return this.nextChild(0, 1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.nextChild(this._tree.children.length - 1, -1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.nextChild(0, 1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.nextChild(this._tree.children.length - 1, -1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        let mounted;\n        if (!(mode & IterMode.IgnoreOverlays) && (mounted = MountedTree.get(this._tree)) && mounted.overlay) {\n            let rPos = pos - this.from;\n            for (let { from, to } of mounted.overlay) {\n                if ((side > 0 ? from <= rPos : from < rPos) &&\n                    (side < 0 ? to >= rPos : to > rPos))\n                    return new TreeNode(mounted.tree, mounted.overlay[0].from + this.from, -1, this);\n            }\n        }\n        return this.nextChild(0, 1, pos, side, mode);\n    }\n    nextSignificantParent() {\n        let val = this;\n        while (val.type.isAnonymous && val._parent)\n            val = val._parent;\n        return val;\n    }\n    get parent() {\n        return this._parent ? this._parent.nextSignificantParent() : null;\n    }\n    get nextSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index + 1, 1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get prevSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index - 1, -1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get tree() { return this._tree; }\n    toTree() { return this._tree; }\n    /**\n    @internal\n    */\n    toString() { return this._tree.toString(); }\n}\nfunction getChildren(node, type, before, after) {\n    let cur = node.cursor(), result = [];\n    if (!cur.firstChild())\n        return result;\n    if (before != null)\n        for (let found = false; !found;) {\n            found = cur.type.is(before);\n            if (!cur.nextSibling())\n                return result;\n        }\n    for (;;) {\n        if (after != null && cur.type.is(after))\n            return result;\n        if (cur.type.is(type))\n            result.push(cur.node);\n        if (!cur.nextSibling())\n            return after == null ? result : [];\n    }\n}\nfunction matchNodeContext(node, context, i = context.length - 1) {\n    for (let p = node; i >= 0; p = p.parent) {\n        if (!p)\n            return false;\n        if (!p.type.isAnonymous) {\n            if (context[i] && context[i] != p.name)\n                return false;\n            i--;\n        }\n    }\n    return true;\n}\nclass BufferContext {\n    constructor(parent, buffer, index, start) {\n        this.parent = parent;\n        this.buffer = buffer;\n        this.index = index;\n        this.start = start;\n    }\n}\nclass BufferNode extends BaseNode {\n    get name() { return this.type.name; }\n    get from() { return this.context.start + this.context.buffer.buffer[this.index + 1]; }\n    get to() { return this.context.start + this.context.buffer.buffer[this.index + 2]; }\n    constructor(context, _parent, index) {\n        super();\n        this.context = context;\n        this._parent = _parent;\n        this.index = index;\n        this.type = context.buffer.set.types[context.buffer.buffer[index]];\n    }\n    child(dir, pos, side) {\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get firstChild() { return this.child(1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.child(-1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.child(1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.child(-1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        if (mode & IterMode.ExcludeBuffers)\n            return null;\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], side > 0 ? 1 : -1, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get parent() {\n        return this._parent || this.context.parent.nextSignificantParent();\n    }\n    externalSibling(dir) {\n        return this._parent ? null : this.context.parent.nextChild(this.context.index + dir, dir, 0, 4 /* Side.DontCare */);\n    }\n    get nextSibling() {\n        let { buffer } = this.context;\n        let after = buffer.buffer[this.index + 3];\n        if (after < (this._parent ? buffer.buffer[this._parent.index + 3] : buffer.buffer.length))\n            return new BufferNode(this.context, this._parent, after);\n        return this.externalSibling(1);\n    }\n    get prevSibling() {\n        let { buffer } = this.context;\n        let parentStart = this._parent ? this._parent.index + 4 : 0;\n        if (this.index == parentStart)\n            return this.externalSibling(-1);\n        return new BufferNode(this.context, this._parent, buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n    }\n    get tree() { return null; }\n    toTree() {\n        let children = [], positions = [];\n        let { buffer } = this.context;\n        let startI = this.index + 4, endI = buffer.buffer[this.index + 3];\n        if (endI > startI) {\n            let from = buffer.buffer[this.index + 1];\n            children.push(buffer.slice(startI, endI, from));\n            positions.push(0);\n        }\n        return new Tree(this.type, children, positions, this.to - this.from);\n    }\n    /**\n    @internal\n    */\n    toString() { return this.context.buffer.childString(this.index); }\n}\nfunction iterStack(heads) {\n    if (!heads.length)\n        return null;\n    let pick = 0, picked = heads[0];\n    for (let i = 1; i < heads.length; i++) {\n        let node = heads[i];\n        if (node.from > picked.from || node.to < picked.to) {\n            picked = node;\n            pick = i;\n        }\n    }\n    let next = picked instanceof TreeNode && picked.index < 0 ? null : picked.parent;\n    let newHeads = heads.slice();\n    if (next)\n        newHeads[pick] = next;\n    else\n        newHeads.splice(pick, 1);\n    return new StackIterator(newHeads, picked);\n}\nclass StackIterator {\n    constructor(heads, node) {\n        this.heads = heads;\n        this.node = node;\n    }\n    get next() { return iterStack(this.heads); }\n}\nfunction stackIterator(tree, pos, side) {\n    let inner = tree.resolveInner(pos, side), layers = null;\n    for (let scan = inner instanceof TreeNode ? inner : inner.context.parent; scan; scan = scan.parent) {\n        if (scan.index < 0) { // This is an overlay root\n            let parent = scan.parent;\n            (layers || (layers = [inner])).push(parent.resolve(pos, side));\n            scan = parent;\n        }\n        else {\n            let mount = MountedTree.get(scan.tree);\n            // Relevant overlay branching off\n            if (mount && mount.overlay && mount.overlay[0].from <= pos && mount.overlay[mount.overlay.length - 1].to >= pos) {\n                let root = new TreeNode(mount.tree, mount.overlay[0].from + scan.from, -1, scan);\n                (layers || (layers = [inner])).push(resolveNode(root, pos, side, false));\n            }\n        }\n    }\n    return layers ? iterStack(layers) : inner;\n}\n/**\nA tree cursor object focuses on a given node in a syntax tree, and\nallows you to move to adjacent nodes.\n*/\nclass TreeCursor {\n    /**\n    Shorthand for `.type.name`.\n    */\n    get name() { return this.type.name; }\n    /**\n    @internal\n    */\n    constructor(node, \n    /**\n    @internal\n    */\n    mode = 0) {\n        this.mode = mode;\n        /**\n        @internal\n        */\n        this.buffer = null;\n        this.stack = [];\n        /**\n        @internal\n        */\n        this.index = 0;\n        this.bufferNode = null;\n        if (node instanceof TreeNode) {\n            this.yieldNode(node);\n        }\n        else {\n            this._tree = node.context.parent;\n            this.buffer = node.context;\n            for (let n = node._parent; n; n = n._parent)\n                this.stack.unshift(n.index);\n            this.bufferNode = node;\n            this.yieldBuf(node.index);\n        }\n    }\n    yieldNode(node) {\n        if (!node)\n            return false;\n        this._tree = node;\n        this.type = node.type;\n        this.from = node.from;\n        this.to = node.to;\n        return true;\n    }\n    yieldBuf(index, type) {\n        this.index = index;\n        let { start, buffer } = this.buffer;\n        this.type = type || buffer.set.types[buffer.buffer[index]];\n        this.from = start + buffer.buffer[index + 1];\n        this.to = start + buffer.buffer[index + 2];\n        return true;\n    }\n    /**\n    @internal\n    */\n    yield(node) {\n        if (!node)\n            return false;\n        if (node instanceof TreeNode) {\n            this.buffer = null;\n            return this.yieldNode(node);\n        }\n        this.buffer = node.context;\n        return this.yieldBuf(node.index, node.type);\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.buffer ? this.buffer.buffer.childString(this.index) : this._tree.toString();\n    }\n    /**\n    @internal\n    */\n    enterChild(dir, pos, side) {\n        if (!this.buffer)\n            return this.yield(this._tree.nextChild(dir < 0 ? this._tree._tree.children.length - 1 : 0, dir, pos, side, this.mode));\n        let { buffer } = this.buffer;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.buffer.start, side);\n        if (index < 0)\n            return false;\n        this.stack.push(this.index);\n        return this.yieldBuf(index);\n    }\n    /**\n    Move the cursor to this node's first child. When this returns\n    false, the node has no child, and the cursor has not been moved.\n    */\n    firstChild() { return this.enterChild(1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to this node's last child.\n    */\n    lastChild() { return this.enterChild(-1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to the first child that ends after `pos`.\n    */\n    childAfter(pos) { return this.enterChild(1, pos, 2 /* Side.After */); }\n    /**\n    Move to the last child that starts before `pos`.\n    */\n    childBefore(pos) { return this.enterChild(-1, pos, -2 /* Side.Before */); }\n    /**\n    Move the cursor to the child around `pos`. If side is -1 the\n    child may end at that position, when 1 it may start there. This\n    will also enter [overlaid](#common.MountedTree.overlay)\n    [mounted](#common.NodeProp^mounted) trees unless `overlays` is\n    set to false.\n    */\n    enter(pos, side, mode = this.mode) {\n        if (!this.buffer)\n            return this.yield(this._tree.enter(pos, side, mode));\n        return mode & IterMode.ExcludeBuffers ? false : this.enterChild(1, pos, side);\n    }\n    /**\n    Move to the node's parent node, if this isn't the top node.\n    */\n    parent() {\n        if (!this.buffer)\n            return this.yieldNode((this.mode & IterMode.IncludeAnonymous) ? this._tree._parent : this._tree.parent);\n        if (this.stack.length)\n            return this.yieldBuf(this.stack.pop());\n        let parent = (this.mode & IterMode.IncludeAnonymous) ? this.buffer.parent : this.buffer.parent.nextSignificantParent();\n        this.buffer = null;\n        return this.yieldNode(parent);\n    }\n    /**\n    @internal\n    */\n    sibling(dir) {\n        if (!this.buffer)\n            return !this._tree._parent ? false\n                : this.yield(this._tree.index < 0 ? null\n                    : this._tree._parent.nextChild(this._tree.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode));\n        let { buffer } = this.buffer, d = this.stack.length - 1;\n        if (dir < 0) {\n            let parentStart = d < 0 ? 0 : this.stack[d] + 4;\n            if (this.index != parentStart)\n                return this.yieldBuf(buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n        }\n        else {\n            let after = buffer.buffer[this.index + 3];\n            if (after < (d < 0 ? buffer.buffer.length : buffer.buffer[this.stack[d] + 3]))\n                return this.yieldBuf(after);\n        }\n        return d < 0 ? this.yield(this.buffer.parent.nextChild(this.buffer.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode)) : false;\n    }\n    /**\n    Move to this node's next sibling, if any.\n    */\n    nextSibling() { return this.sibling(1); }\n    /**\n    Move to this node's previous sibling, if any.\n    */\n    prevSibling() { return this.sibling(-1); }\n    atLastNode(dir) {\n        let index, parent, { buffer } = this;\n        if (buffer) {\n            if (dir > 0) {\n                if (this.index < buffer.buffer.buffer.length)\n                    return false;\n            }\n            else {\n                for (let i = 0; i < this.index; i++)\n                    if (buffer.buffer.buffer[i + 3] < this.index)\n                        return false;\n            }\n            ({ index, parent } = buffer);\n        }\n        else {\n            ({ index, _parent: parent } = this._tree);\n        }\n        for (; parent; { index, _parent: parent } = parent) {\n            if (index > -1)\n                for (let i = index + dir, e = dir < 0 ? -1 : parent._tree.children.length; i != e; i += dir) {\n                    let child = parent._tree.children[i];\n                    if ((this.mode & IterMode.IncludeAnonymous) ||\n                        child instanceof TreeBuffer ||\n                        !child.type.isAnonymous ||\n                        hasChild(child))\n                        return false;\n                }\n        }\n        return true;\n    }\n    move(dir, enter) {\n        if (enter && this.enterChild(dir, 0, 4 /* Side.DontCare */))\n            return true;\n        for (;;) {\n            if (this.sibling(dir))\n                return true;\n            if (this.atLastNode(dir) || !this.parent())\n                return false;\n        }\n    }\n    /**\n    Move to the next node in a\n    [pre-order](https://en.wikipedia.org/wiki/Tree_traversal#Pre-order,_NLR)\n    traversal, going from a node to its first child or, if the\n    current node is empty or `enter` is false, its next sibling or\n    the next sibling of the first parent node that has one.\n    */\n    next(enter = true) { return this.move(1, enter); }\n    /**\n    Move to the next node in a last-to-first pre-order traversal. A\n    node is followed by its last child or, if it has none, its\n    previous sibling or the previous sibling of the first parent\n    node that has one.\n    */\n    prev(enter = true) { return this.move(-1, enter); }\n    /**\n    Move the cursor to the innermost node that covers `pos`. If\n    `side` is -1, it will enter nodes that end at `pos`. If it is 1,\n    it will enter nodes that start at `pos`.\n    */\n    moveTo(pos, side = 0) {\n        // Move up to a node that actually holds the position, if possible\n        while (this.from == this.to ||\n            (side < 1 ? this.from >= pos : this.from > pos) ||\n            (side > -1 ? this.to <= pos : this.to < pos))\n            if (!this.parent())\n                break;\n        // Then scan down into child nodes as far as possible\n        while (this.enterChild(1, pos, side)) { }\n        return this;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) at the cursor's current\n    position.\n    */\n    get node() {\n        if (!this.buffer)\n            return this._tree;\n        let cache = this.bufferNode, result = null, depth = 0;\n        if (cache && cache.context == this.buffer) {\n            scan: for (let index = this.index, d = this.stack.length; d >= 0;) {\n                for (let c = cache; c; c = c._parent)\n                    if (c.index == index) {\n                        if (index == this.index)\n                            return c;\n                        result = c;\n                        depth = d + 1;\n                        break scan;\n                    }\n                index = this.stack[--d];\n            }\n        }\n        for (let i = depth; i < this.stack.length; i++)\n            result = new BufferNode(this.buffer, result, this.stack[i]);\n        return this.bufferNode = new BufferNode(this.buffer, result, this.index);\n    }\n    /**\n    Get the [tree](#common.Tree) that represents the current node, if\n    any. Will return null when the node is in a [tree\n    buffer](#common.TreeBuffer).\n    */\n    get tree() {\n        return this.buffer ? null : this._tree._tree;\n    }\n    /**\n    Iterate over the current node and all its descendants, calling\n    `enter` when entering a node and `leave`, if given, when leaving\n    one. When `enter` returns `false`, any children of that node are\n    skipped, and `leave` isn't called for it.\n    */\n    iterate(enter, leave) {\n        for (let depth = 0;;) {\n            let mustLeave = false;\n            if (this.type.isAnonymous || enter(this) !== false) {\n                if (this.firstChild()) {\n                    depth++;\n                    continue;\n                }\n                if (!this.type.isAnonymous)\n                    mustLeave = true;\n            }\n            for (;;) {\n                if (mustLeave && leave)\n                    leave(this);\n                mustLeave = this.type.isAnonymous;\n                if (!depth)\n                    return;\n                if (this.nextSibling())\n                    break;\n                this.parent();\n                depth--;\n                mustLeave = true;\n            }\n        }\n    }\n    /**\n    Test whether the current node matches a given context—a sequence\n    of direct parent node names. Empty strings in the context array\n    are treated as wildcards.\n    */\n    matchContext(context) {\n        if (!this.buffer)\n            return matchNodeContext(this.node.parent, context);\n        let { buffer } = this.buffer, { types } = buffer.set;\n        for (let i = context.length - 1, d = this.stack.length - 1; i >= 0; d--) {\n            if (d < 0)\n                return matchNodeContext(this._tree, context, i);\n            let type = types[buffer.buffer[this.stack[d]]];\n            if (!type.isAnonymous) {\n                if (context[i] && context[i] != type.name)\n                    return false;\n                i--;\n            }\n        }\n        return true;\n    }\n}\nfunction hasChild(tree) {\n    return tree.children.some(ch => ch instanceof TreeBuffer || !ch.type.isAnonymous || hasChild(ch));\n}\nfunction buildTree(data) {\n    var _a;\n    let { buffer, nodeSet, maxBufferLength = DefaultBufferLength, reused = [], minRepeatType = nodeSet.types.length } = data;\n    let cursor = Array.isArray(buffer) ? new FlatBufferCursor(buffer, buffer.length) : buffer;\n    let types = nodeSet.types;\n    let contextHash = 0, lookAhead = 0;\n    function takeNode(parentStart, minPos, children, positions, inRepeat, depth) {\n        let { id, start, end, size } = cursor;\n        let lookAheadAtStart = lookAhead, contextAtStart = contextHash;\n        while (size < 0) {\n            cursor.next();\n            if (size == -1 /* SpecialRecord.Reuse */) {\n                let node = reused[id];\n                children.push(node);\n                positions.push(start - parentStart);\n                return;\n            }\n            else if (size == -3 /* SpecialRecord.ContextChange */) { // Context change\n                contextHash = id;\n                return;\n            }\n            else if (size == -4 /* SpecialRecord.LookAhead */) {\n                lookAhead = id;\n                return;\n            }\n            else {\n                throw new RangeError(`Unrecognized record size: ${size}`);\n            }\n        }\n        let type = types[id], node, buffer;\n        let startPos = start - parentStart;\n        if (end - start <= maxBufferLength && (buffer = findBufferSize(cursor.pos - minPos, inRepeat))) {\n            // Small enough for a buffer, and no reused nodes inside\n            let data = new Uint16Array(buffer.size - buffer.skip);\n            let endPos = cursor.pos - buffer.size, index = data.length;\n            while (cursor.pos > endPos)\n                index = copyToBuffer(buffer.start, data, index);\n            node = new TreeBuffer(data, end - buffer.start, nodeSet);\n            startPos = buffer.start - parentStart;\n        }\n        else { // Make it a node\n            let endPos = cursor.pos - size;\n            cursor.next();\n            let localChildren = [], localPositions = [];\n            let localInRepeat = id >= minRepeatType ? id : -1;\n            let lastGroup = 0, lastEnd = end;\n            while (cursor.pos > endPos) {\n                if (localInRepeat >= 0 && cursor.id == localInRepeat && cursor.size >= 0) {\n                    if (cursor.end <= lastEnd - maxBufferLength) {\n                        makeRepeatLeaf(localChildren, localPositions, start, lastGroup, cursor.end, lastEnd, localInRepeat, lookAheadAtStart, contextAtStart);\n                        lastGroup = localChildren.length;\n                        lastEnd = cursor.end;\n                    }\n                    cursor.next();\n                }\n                else if (depth > 2500 /* CutOff.Depth */) {\n                    takeFlatNode(start, endPos, localChildren, localPositions);\n                }\n                else {\n                    takeNode(start, endPos, localChildren, localPositions, localInRepeat, depth + 1);\n                }\n            }\n            if (localInRepeat >= 0 && lastGroup > 0 && lastGroup < localChildren.length)\n                makeRepeatLeaf(localChildren, localPositions, start, lastGroup, start, lastEnd, localInRepeat, lookAheadAtStart, contextAtStart);\n            localChildren.reverse();\n            localPositions.reverse();\n            if (localInRepeat > -1 && lastGroup > 0) {\n                let make = makeBalanced(type, contextAtStart);\n                node = balanceRange(type, localChildren, localPositions, 0, localChildren.length, 0, end - start, make, make);\n            }\n            else {\n                node = makeTree(type, localChildren, localPositions, end - start, lookAheadAtStart - end, contextAtStart);\n            }\n        }\n        children.push(node);\n        positions.push(startPos);\n    }\n    function takeFlatNode(parentStart, minPos, children, positions) {\n        let nodes = []; // Temporary, inverted array of leaf nodes found, with absolute positions\n        let nodeCount = 0, stopAt = -1;\n        while (cursor.pos > minPos) {\n            let { id, start, end, size } = cursor;\n            if (size > 4) { // Not a leaf\n                cursor.next();\n            }\n            else if (stopAt > -1 && start < stopAt) {\n                break;\n            }\n            else {\n                if (stopAt < 0)\n                    stopAt = end - maxBufferLength;\n                nodes.push(id, start, end);\n                nodeCount++;\n                cursor.next();\n            }\n        }\n        if (nodeCount) {\n            let buffer = new Uint16Array(nodeCount * 4);\n            let start = nodes[nodes.length - 2];\n            for (let i = nodes.length - 3, j = 0; i >= 0; i -= 3) {\n                buffer[j++] = nodes[i];\n                buffer[j++] = nodes[i + 1] - start;\n                buffer[j++] = nodes[i + 2] - start;\n                buffer[j++] = j;\n            }\n            children.push(new TreeBuffer(buffer, nodes[2] - start, nodeSet));\n            positions.push(start - parentStart);\n        }\n    }\n    function makeBalanced(type, contextHash) {\n        return (children, positions, length) => {\n            let lookAhead = 0, lastI = children.length - 1, last, lookAheadProp;\n            if (lastI >= 0 && (last = children[lastI]) instanceof Tree) {\n                if (!lastI && last.type == type && last.length == length)\n                    return last;\n                if (lookAheadProp = last.prop(NodeProp.lookAhead))\n                    lookAhead = positions[lastI] + last.length + lookAheadProp;\n            }\n            return makeTree(type, children, positions, length, lookAhead, contextHash);\n        };\n    }\n    function makeRepeatLeaf(children, positions, base, i, from, to, type, lookAhead, contextHash) {\n        let localChildren = [], localPositions = [];\n        while (children.length > i) {\n            localChildren.push(children.pop());\n            localPositions.push(positions.pop() + base - from);\n        }\n        children.push(makeTree(nodeSet.types[type], localChildren, localPositions, to - from, lookAhead - to, contextHash));\n        positions.push(from - base);\n    }\n    function makeTree(type, children, positions, length, lookAhead, contextHash, props) {\n        if (contextHash) {\n            let pair = [NodeProp.contextHash, contextHash];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        if (lookAhead > 25) {\n            let pair = [NodeProp.lookAhead, lookAhead];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        return new Tree(type, children, positions, length, props);\n    }\n    function findBufferSize(maxSize, inRepeat) {\n        // Scan through the buffer to find previous siblings that fit\n        // together in a TreeBuffer, and don't contain any reused nodes\n        // (which can't be stored in a buffer).\n        // If `inRepeat` is > -1, ignore node boundaries of that type for\n        // nesting, but make sure the end falls either at the start\n        // (`maxSize`) or before such a node.\n        let fork = cursor.fork();\n        let size = 0, start = 0, skip = 0, minStart = fork.end - maxBufferLength;\n        let result = { size: 0, start: 0, skip: 0 };\n        scan: for (let minPos = fork.pos - maxSize; fork.pos > minPos;) {\n            let nodeSize = fork.size;\n            // Pretend nested repeat nodes of the same type don't exist\n            if (fork.id == inRepeat && nodeSize >= 0) {\n                // Except that we store the current state as a valid return\n                // value.\n                result.size = size;\n                result.start = start;\n                result.skip = skip;\n                skip += 4;\n                size += 4;\n                fork.next();\n                continue;\n            }\n            let startPos = fork.pos - nodeSize;\n            if (nodeSize < 0 || startPos < minPos || fork.start < minStart)\n                break;\n            let localSkipped = fork.id >= minRepeatType ? 4 : 0;\n            let nodeStart = fork.start;\n            fork.next();\n            while (fork.pos > startPos) {\n                if (fork.size < 0) {\n                    if (fork.size == -3 /* SpecialRecord.ContextChange */)\n                        localSkipped += 4;\n                    else\n                        break scan;\n                }\n                else if (fork.id >= minRepeatType) {\n                    localSkipped += 4;\n                }\n                fork.next();\n            }\n            start = nodeStart;\n            size += nodeSize;\n            skip += localSkipped;\n        }\n        if (inRepeat < 0 || size == maxSize) {\n            result.size = size;\n            result.start = start;\n            result.skip = skip;\n        }\n        return result.size > 4 ? result : undefined;\n    }\n    function copyToBuffer(bufferStart, buffer, index) {\n        let { id, start, end, size } = cursor;\n        cursor.next();\n        if (size >= 0 && id < minRepeatType) {\n            let startIndex = index;\n            if (size > 4) {\n                let endPos = cursor.pos - (size - 4);\n                while (cursor.pos > endPos)\n                    index = copyToBuffer(bufferStart, buffer, index);\n            }\n            buffer[--index] = startIndex;\n            buffer[--index] = end - bufferStart;\n            buffer[--index] = start - bufferStart;\n            buffer[--index] = id;\n        }\n        else if (size == -3 /* SpecialRecord.ContextChange */) {\n            contextHash = id;\n        }\n        else if (size == -4 /* SpecialRecord.LookAhead */) {\n            lookAhead = id;\n        }\n        return index;\n    }\n    let children = [], positions = [];\n    while (cursor.pos > 0)\n        takeNode(data.start || 0, data.bufferStart || 0, children, positions, -1, 0);\n    let length = (_a = data.length) !== null && _a !== void 0 ? _a : (children.length ? positions[0] + children[0].length : 0);\n    return new Tree(types[data.topID], children.reverse(), positions.reverse(), length);\n}\nconst nodeSizeCache = new WeakMap;\nfunction nodeSize(balanceType, node) {\n    if (!balanceType.isAnonymous || node instanceof TreeBuffer || node.type != balanceType)\n        return 1;\n    let size = nodeSizeCache.get(node);\n    if (size == null) {\n        size = 1;\n        for (let child of node.children) {\n            if (child.type != balanceType || !(child instanceof Tree)) {\n                size = 1;\n                break;\n            }\n            size += nodeSize(balanceType, child);\n        }\n        nodeSizeCache.set(node, size);\n    }\n    return size;\n}\nfunction balanceRange(\n// The type the balanced tree's inner nodes.\nbalanceType, \n// The direct children and their positions\nchildren, positions, \n// The index range in children/positions to use\nfrom, to, \n// The start position of the nodes, relative to their parent.\nstart, \n// Length of the outer node\nlength, \n// Function to build the top node of the balanced tree\nmkTop, \n// Function to build internal nodes for the balanced tree\nmkTree) {\n    let total = 0;\n    for (let i = from; i < to; i++)\n        total += nodeSize(balanceType, children[i]);\n    let maxChild = Math.ceil((total * 1.5) / 8 /* Balance.BranchFactor */);\n    let localChildren = [], localPositions = [];\n    function divide(children, positions, from, to, offset) {\n        for (let i = from; i < to;) {\n            let groupFrom = i, groupStart = positions[i], groupSize = nodeSize(balanceType, children[i]);\n            i++;\n            for (; i < to; i++) {\n                let nextSize = nodeSize(balanceType, children[i]);\n                if (groupSize + nextSize >= maxChild)\n                    break;\n                groupSize += nextSize;\n            }\n            if (i == groupFrom + 1) {\n                if (groupSize > maxChild) {\n                    let only = children[groupFrom]; // Only trees can have a size > 1\n                    divide(only.children, only.positions, 0, only.children.length, positions[groupFrom] + offset);\n                    continue;\n                }\n                localChildren.push(children[groupFrom]);\n            }\n            else {\n                let length = positions[i - 1] + children[i - 1].length - groupStart;\n                localChildren.push(balanceRange(balanceType, children, positions, groupFrom, i, groupStart, length, null, mkTree));\n            }\n            localPositions.push(groupStart + offset - start);\n        }\n    }\n    divide(children, positions, from, to, 0);\n    return (mkTop || mkTree)(localChildren, localPositions, length);\n}\n/**\nProvides a way to associate values with pieces of trees. As long\nas that part of the tree is reused, the associated values can be\nretrieved from an updated tree.\n*/\nclass NodeWeakMap {\n    constructor() {\n        this.map = new WeakMap();\n    }\n    setBuffer(buffer, index, value) {\n        let inner = this.map.get(buffer);\n        if (!inner)\n            this.map.set(buffer, inner = new Map);\n        inner.set(index, value);\n    }\n    getBuffer(buffer, index) {\n        let inner = this.map.get(buffer);\n        return inner && inner.get(index);\n    }\n    /**\n    Set the value for this syntax node.\n    */\n    set(node, value) {\n        if (node instanceof BufferNode)\n            this.setBuffer(node.context.buffer, node.index, value);\n        else if (node instanceof TreeNode)\n            this.map.set(node.tree, value);\n    }\n    /**\n    Retrieve value for this syntax node, if it exists in the map.\n    */\n    get(node) {\n        return node instanceof BufferNode ? this.getBuffer(node.context.buffer, node.index)\n            : node instanceof TreeNode ? this.map.get(node.tree) : undefined;\n    }\n    /**\n    Set the value for the node that a cursor currently points to.\n    */\n    cursorSet(cursor, value) {\n        if (cursor.buffer)\n            this.setBuffer(cursor.buffer.buffer, cursor.index, value);\n        else\n            this.map.set(cursor.tree, value);\n    }\n    /**\n    Retrieve the value for the node that a cursor currently points\n    to.\n    */\n    cursorGet(cursor) {\n        return cursor.buffer ? this.getBuffer(cursor.buffer.buffer, cursor.index) : this.map.get(cursor.tree);\n    }\n}\n\n/**\nTree fragments are used during [incremental\nparsing](#common.Parser.startParse) to track parts of old trees\nthat can be reused in a new parse. An array of fragments is used\nto track regions of an old tree whose nodes might be reused in new\nparses. Use the static\n[`applyChanges`](#common.TreeFragment^applyChanges) method to\nupdate fragments for document changes.\n*/\nclass TreeFragment {\n    /**\n    Construct a tree fragment. You'll usually want to use\n    [`addTree`](#common.TreeFragment^addTree) and\n    [`applyChanges`](#common.TreeFragment^applyChanges) instead of\n    calling this directly.\n    */\n    constructor(\n    /**\n    The start of the unchanged range pointed to by this fragment.\n    This refers to an offset in the _updated_ document (as opposed\n    to the original tree).\n    */\n    from, \n    /**\n    The end of the unchanged range.\n    */\n    to, \n    /**\n    The tree that this fragment is based on.\n    */\n    tree, \n    /**\n    The offset between the fragment's tree and the document that\n    this fragment can be used against. Add this when going from\n    document to tree positions, subtract it to go from tree to\n    document positions.\n    */\n    offset, openStart = false, openEnd = false) {\n        this.from = from;\n        this.to = to;\n        this.tree = tree;\n        this.offset = offset;\n        this.open = (openStart ? 1 /* Open.Start */ : 0) | (openEnd ? 2 /* Open.End */ : 0);\n    }\n    /**\n    Whether the start of the fragment represents the start of a\n    parse, or the end of a change. (In the second case, it may not\n    be safe to reuse some nodes at the start, depending on the\n    parsing algorithm.)\n    */\n    get openStart() { return (this.open & 1 /* Open.Start */) > 0; }\n    /**\n    Whether the end of the fragment represents the end of a\n    full-document parse, or the start of a change.\n    */\n    get openEnd() { return (this.open & 2 /* Open.End */) > 0; }\n    /**\n    Create a set of fragments from a freshly parsed tree, or update\n    an existing set of fragments by replacing the ones that overlap\n    with a tree with content from the new tree. When `partial` is\n    true, the parse is treated as incomplete, and the resulting\n    fragment has [`openEnd`](#common.TreeFragment.openEnd) set to\n    true.\n    */\n    static addTree(tree, fragments = [], partial = false) {\n        let result = [new TreeFragment(0, tree.length, tree, 0, false, partial)];\n        for (let f of fragments)\n            if (f.to > tree.length)\n                result.push(f);\n        return result;\n    }\n    /**\n    Apply a set of edits to an array of fragments, removing or\n    splitting fragments as necessary to remove edited ranges, and\n    adjusting offsets for fragments that moved.\n    */\n    static applyChanges(fragments, changes, minGap = 128) {\n        if (!changes.length)\n            return fragments;\n        let result = [];\n        let fI = 1, nextF = fragments.length ? fragments[0] : null;\n        for (let cI = 0, pos = 0, off = 0;; cI++) {\n            let nextC = cI < changes.length ? changes[cI] : null;\n            let nextPos = nextC ? nextC.fromA : 1e9;\n            if (nextPos - pos >= minGap)\n                while (nextF && nextF.from < nextPos) {\n                    let cut = nextF;\n                    if (pos >= cut.from || nextPos <= cut.to || off) {\n                        let fFrom = Math.max(cut.from, pos) - off, fTo = Math.min(cut.to, nextPos) - off;\n                        cut = fFrom >= fTo ? null : new TreeFragment(fFrom, fTo, cut.tree, cut.offset + off, cI > 0, !!nextC);\n                    }\n                    if (cut)\n                        result.push(cut);\n                    if (nextF.to > nextPos)\n                        break;\n                    nextF = fI < fragments.length ? fragments[fI++] : null;\n                }\n            if (!nextC)\n                break;\n            pos = nextC.toA;\n            off = nextC.toA - nextC.toB;\n        }\n        return result;\n    }\n}\n/**\nA superclass that parsers should extend.\n*/\nclass Parser {\n    /**\n    Start a parse, returning a [partial parse](#common.PartialParse)\n    object. [`fragments`](#common.TreeFragment) can be passed in to\n    make the parse incremental.\n    \n    By default, the entire input is parsed. You can pass `ranges`,\n    which should be a sorted array of non-empty, non-overlapping\n    ranges, to parse only those ranges. The tree returned in that\n    case will start at `ranges[0].from`.\n    */\n    startParse(input, fragments, ranges) {\n        if (typeof input == \"string\")\n            input = new StringInput(input);\n        ranges = !ranges ? [new Range(0, input.length)] : ranges.length ? ranges.map(r => new Range(r.from, r.to)) : [new Range(0, 0)];\n        return this.createParse(input, fragments || [], ranges);\n    }\n    /**\n    Run a full parse, returning the resulting tree.\n    */\n    parse(input, fragments, ranges) {\n        let parse = this.startParse(input, fragments, ranges);\n        for (;;) {\n            let done = parse.advance();\n            if (done)\n                return done;\n        }\n    }\n}\nclass StringInput {\n    constructor(string) {\n        this.string = string;\n    }\n    get length() { return this.string.length; }\n    chunk(from) { return this.string.slice(from); }\n    get lineChunks() { return false; }\n    read(from, to) { return this.string.slice(from, to); }\n}\n\n/**\nCreate a parse wrapper that, after the inner parse completes,\nscans its tree for mixed language regions with the `nest`\nfunction, runs the resulting [inner parses](#common.NestedParse),\nand then [mounts](#common.NodeProp^mounted) their results onto the\ntree.\n*/\nfunction parseMixed(nest) {\n    return (parse, input, fragments, ranges) => new MixedParse(parse, nest, input, fragments, ranges);\n}\nclass InnerParse {\n    constructor(parser, parse, overlay, target, from) {\n        this.parser = parser;\n        this.parse = parse;\n        this.overlay = overlay;\n        this.target = target;\n        this.from = from;\n    }\n}\nfunction checkRanges(ranges) {\n    if (!ranges.length || ranges.some(r => r.from >= r.to))\n        throw new RangeError(\"Invalid inner parse ranges given: \" + JSON.stringify(ranges));\n}\nclass ActiveOverlay {\n    constructor(parser, predicate, mounts, index, start, target, prev) {\n        this.parser = parser;\n        this.predicate = predicate;\n        this.mounts = mounts;\n        this.index = index;\n        this.start = start;\n        this.target = target;\n        this.prev = prev;\n        this.depth = 0;\n        this.ranges = [];\n    }\n}\nconst stoppedInner = new NodeProp({ perNode: true });\nclass MixedParse {\n    constructor(base, nest, input, fragments, ranges) {\n        this.nest = nest;\n        this.input = input;\n        this.fragments = fragments;\n        this.ranges = ranges;\n        this.inner = [];\n        this.innerDone = 0;\n        this.baseTree = null;\n        this.stoppedAt = null;\n        this.baseParse = base;\n    }\n    advance() {\n        if (this.baseParse) {\n            let done = this.baseParse.advance();\n            if (!done)\n                return null;\n            this.baseParse = null;\n            this.baseTree = done;\n            this.startInner();\n            if (this.stoppedAt != null)\n                for (let inner of this.inner)\n                    inner.parse.stopAt(this.stoppedAt);\n        }\n        if (this.innerDone == this.inner.length) {\n            let result = this.baseTree;\n            if (this.stoppedAt != null)\n                result = new Tree(result.type, result.children, result.positions, result.length, result.propValues.concat([[stoppedInner, this.stoppedAt]]));\n            return result;\n        }\n        let inner = this.inner[this.innerDone], done = inner.parse.advance();\n        if (done) {\n            this.innerDone++;\n            // This is a somewhat dodgy but super helpful hack where we\n            // patch up nodes created by the inner parse (and thus\n            // presumably not aliased anywhere else) to hold the information\n            // about the inner parse.\n            let props = Object.assign(Object.create(null), inner.target.props);\n            props[NodeProp.mounted.id] = new MountedTree(done, inner.overlay, inner.parser);\n            inner.target.props = props;\n        }\n        return null;\n    }\n    get parsedPos() {\n        if (this.baseParse)\n            return 0;\n        let pos = this.input.length;\n        for (let i = this.innerDone; i < this.inner.length; i++) {\n            if (this.inner[i].from < pos)\n                pos = Math.min(pos, this.inner[i].parse.parsedPos);\n        }\n        return pos;\n    }\n    stopAt(pos) {\n        this.stoppedAt = pos;\n        if (this.baseParse)\n            this.baseParse.stopAt(pos);\n        else\n            for (let i = this.innerDone; i < this.inner.length; i++)\n                this.inner[i].parse.stopAt(pos);\n    }\n    startInner() {\n        let fragmentCursor = new FragmentCursor(this.fragments);\n        let overlay = null;\n        let covered = null;\n        let cursor = new TreeCursor(new TreeNode(this.baseTree, this.ranges[0].from, 0, null), IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n        scan: for (let nest, isCovered;;) {\n            let enter = true, range;\n            if (this.stoppedAt != null && cursor.from >= this.stoppedAt) {\n                enter = false;\n            }\n            else if (fragmentCursor.hasNode(cursor)) {\n                if (overlay) {\n                    let match = overlay.mounts.find(m => m.frag.from <= cursor.from && m.frag.to >= cursor.to && m.mount.overlay);\n                    if (match)\n                        for (let r of match.mount.overlay) {\n                            let from = r.from + match.pos, to = r.to + match.pos;\n                            if (from >= cursor.from && to <= cursor.to && !overlay.ranges.some(r => r.from < to && r.to > from))\n                                overlay.ranges.push({ from, to });\n                        }\n                }\n                enter = false;\n            }\n            else if (covered && (isCovered = checkCover(covered.ranges, cursor.from, cursor.to))) {\n                enter = isCovered != 2 /* Cover.Full */;\n            }\n            else if (!cursor.type.isAnonymous && (nest = this.nest(cursor, this.input)) &&\n                (cursor.from < cursor.to || !nest.overlay)) {\n                if (!cursor.tree)\n                    materialize(cursor);\n                let oldMounts = fragmentCursor.findMounts(cursor.from, nest.parser);\n                if (typeof nest.overlay == \"function\") {\n                    overlay = new ActiveOverlay(nest.parser, nest.overlay, oldMounts, this.inner.length, cursor.from, cursor.tree, overlay);\n                }\n                else {\n                    let ranges = punchRanges(this.ranges, nest.overlay ||\n                        (cursor.from < cursor.to ? [new Range(cursor.from, cursor.to)] : []));\n                    if (ranges.length)\n                        checkRanges(ranges);\n                    if (ranges.length || !nest.overlay)\n                        this.inner.push(new InnerParse(nest.parser, ranges.length ? nest.parser.startParse(this.input, enterFragments(oldMounts, ranges), ranges)\n                            : nest.parser.startParse(\"\"), nest.overlay ? nest.overlay.map(r => new Range(r.from - cursor.from, r.to - cursor.from)) : null, cursor.tree, ranges.length ? ranges[0].from : cursor.from));\n                    if (!nest.overlay)\n                        enter = false;\n                    else if (ranges.length)\n                        covered = { ranges, depth: 0, prev: covered };\n                }\n            }\n            else if (overlay && (range = overlay.predicate(cursor))) {\n                if (range === true)\n                    range = new Range(cursor.from, cursor.to);\n                if (range.from < range.to) {\n                    let last = overlay.ranges.length - 1;\n                    if (last >= 0 && overlay.ranges[last].to == range.from)\n                        overlay.ranges[last] = { from: overlay.ranges[last].from, to: range.to };\n                    else\n                        overlay.ranges.push(range);\n                }\n            }\n            if (enter && cursor.firstChild()) {\n                if (overlay)\n                    overlay.depth++;\n                if (covered)\n                    covered.depth++;\n            }\n            else {\n                for (;;) {\n                    if (cursor.nextSibling())\n                        break;\n                    if (!cursor.parent())\n                        break scan;\n                    if (overlay && !--overlay.depth) {\n                        let ranges = punchRanges(this.ranges, overlay.ranges);\n                        if (ranges.length) {\n                            checkRanges(ranges);\n                            this.inner.splice(overlay.index, 0, new InnerParse(overlay.parser, overlay.parser.startParse(this.input, enterFragments(overlay.mounts, ranges), ranges), overlay.ranges.map(r => new Range(r.from - overlay.start, r.to - overlay.start)), overlay.target, ranges[0].from));\n                        }\n                        overlay = overlay.prev;\n                    }\n                    if (covered && !--covered.depth)\n                        covered = covered.prev;\n                }\n            }\n        }\n    }\n}\nfunction checkCover(covered, from, to) {\n    for (let range of covered) {\n        if (range.from >= to)\n            break;\n        if (range.to > from)\n            return range.from <= from && range.to >= to ? 2 /* Cover.Full */ : 1 /* Cover.Partial */;\n    }\n    return 0 /* Cover.None */;\n}\n// Take a piece of buffer and convert it into a stand-alone\n// TreeBuffer.\nfunction sliceBuf(buf, startI, endI, nodes, positions, off) {\n    if (startI < endI) {\n        let from = buf.buffer[startI + 1];\n        nodes.push(buf.slice(startI, endI, from));\n        positions.push(from - off);\n    }\n}\n// This function takes a node that's in a buffer, and converts it, and\n// its parent buffer nodes, into a Tree. This is again acting on the\n// assumption that the trees and buffers have been constructed by the\n// parse that was ran via the mix parser, and thus aren't shared with\n// any other code, making violations of the immutability safe.\nfunction materialize(cursor) {\n    let { node } = cursor, stack = [];\n    let buffer = node.context.buffer;\n    // Scan up to the nearest tree\n    do {\n        stack.push(cursor.index);\n        cursor.parent();\n    } while (!cursor.tree);\n    // Find the index of the buffer in that tree\n    let base = cursor.tree, i = base.children.indexOf(buffer);\n    let buf = base.children[i], b = buf.buffer, newStack = [i];\n    // Split a level in the buffer, putting the nodes before and after\n    // the child that contains `node` into new buffers.\n    function split(startI, endI, type, innerOffset, length, stackPos) {\n        let targetI = stack[stackPos];\n        let children = [], positions = [];\n        sliceBuf(buf, startI, targetI, children, positions, innerOffset);\n        let from = b[targetI + 1], to = b[targetI + 2];\n        newStack.push(children.length);\n        let child = stackPos\n            ? split(targetI + 4, b[targetI + 3], buf.set.types[b[targetI]], from, to - from, stackPos - 1)\n            : node.toTree();\n        children.push(child);\n        positions.push(from - innerOffset);\n        sliceBuf(buf, b[targetI + 3], endI, children, positions, innerOffset);\n        return new Tree(type, children, positions, length);\n    }\n    base.children[i] = split(0, b.length, NodeType.none, 0, buf.length, stack.length - 1);\n    // Move the cursor back to the target node\n    for (let index of newStack) {\n        let tree = cursor.tree.children[index], pos = cursor.tree.positions[index];\n        cursor.yield(new TreeNode(tree, pos + cursor.from, index, cursor._tree));\n    }\n}\nclass StructureCursor {\n    constructor(root, offset) {\n        this.offset = offset;\n        this.done = false;\n        this.cursor = root.cursor(IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n    }\n    // Move to the first node (in pre-order) that starts at or after `pos`.\n    moveTo(pos) {\n        let { cursor } = this, p = pos - this.offset;\n        while (!this.done && cursor.from < p) {\n            if (cursor.to >= pos && cursor.enter(p, 1, IterMode.IgnoreOverlays | IterMode.ExcludeBuffers)) ;\n            else if (!cursor.next(false))\n                this.done = true;\n        }\n    }\n    hasNode(cursor) {\n        this.moveTo(cursor.from);\n        if (!this.done && this.cursor.from + this.offset == cursor.from && this.cursor.tree) {\n            for (let tree = this.cursor.tree;;) {\n                if (tree == cursor.tree)\n                    return true;\n                if (tree.children.length && tree.positions[0] == 0 && tree.children[0] instanceof Tree)\n                    tree = tree.children[0];\n                else\n                    break;\n            }\n        }\n        return false;\n    }\n}\nclass FragmentCursor {\n    constructor(fragments) {\n        var _a;\n        this.fragments = fragments;\n        this.curTo = 0;\n        this.fragI = 0;\n        if (fragments.length) {\n            let first = this.curFrag = fragments[0];\n            this.curTo = (_a = first.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : first.to;\n            this.inner = new StructureCursor(first.tree, -first.offset);\n        }\n        else {\n            this.curFrag = this.inner = null;\n        }\n    }\n    hasNode(node) {\n        while (this.curFrag && node.from >= this.curTo)\n            this.nextFrag();\n        return this.curFrag && this.curFrag.from <= node.from && this.curTo >= node.to && this.inner.hasNode(node);\n    }\n    nextFrag() {\n        var _a;\n        this.fragI++;\n        if (this.fragI == this.fragments.length) {\n            this.curFrag = this.inner = null;\n        }\n        else {\n            let frag = this.curFrag = this.fragments[this.fragI];\n            this.curTo = (_a = frag.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : frag.to;\n            this.inner = new StructureCursor(frag.tree, -frag.offset);\n        }\n    }\n    findMounts(pos, parser) {\n        var _a;\n        let result = [];\n        if (this.inner) {\n            this.inner.cursor.moveTo(pos, 1);\n            for (let pos = this.inner.cursor.node; pos; pos = pos.parent) {\n                let mount = (_a = pos.tree) === null || _a === void 0 ? void 0 : _a.prop(NodeProp.mounted);\n                if (mount && mount.parser == parser) {\n                    for (let i = this.fragI; i < this.fragments.length; i++) {\n                        let frag = this.fragments[i];\n                        if (frag.from >= pos.to)\n                            break;\n                        if (frag.tree == this.curFrag.tree)\n                            result.push({\n                                frag,\n                                pos: pos.from - frag.offset,\n                                mount\n                            });\n                    }\n                }\n            }\n        }\n        return result;\n    }\n}\nfunction punchRanges(outer, ranges) {\n    let copy = null, current = ranges;\n    for (let i = 1, j = 0; i < outer.length; i++) {\n        let gapFrom = outer[i - 1].to, gapTo = outer[i].from;\n        for (; j < current.length; j++) {\n            let r = current[j];\n            if (r.from >= gapTo)\n                break;\n            if (r.to <= gapFrom)\n                continue;\n            if (!copy)\n                current = copy = ranges.slice();\n            if (r.from < gapFrom) {\n                copy[j] = new Range(r.from, gapFrom);\n                if (r.to > gapTo)\n                    copy.splice(j + 1, 0, new Range(gapTo, r.to));\n            }\n            else if (r.to > gapTo) {\n                copy[j--] = new Range(gapTo, r.to);\n            }\n            else {\n                copy.splice(j--, 1);\n            }\n        }\n    }\n    return current;\n}\nfunction findCoverChanges(a, b, from, to) {\n    let iA = 0, iB = 0, inA = false, inB = false, pos = -1e9;\n    let result = [];\n    for (;;) {\n        let nextA = iA == a.length ? 1e9 : inA ? a[iA].to : a[iA].from;\n        let nextB = iB == b.length ? 1e9 : inB ? b[iB].to : b[iB].from;\n        if (inA != inB) {\n            let start = Math.max(pos, from), end = Math.min(nextA, nextB, to);\n            if (start < end)\n                result.push(new Range(start, end));\n        }\n        pos = Math.min(nextA, nextB);\n        if (pos == 1e9)\n            break;\n        if (nextA == pos) {\n            if (!inA)\n                inA = true;\n            else {\n                inA = false;\n                iA++;\n            }\n        }\n        if (nextB == pos) {\n            if (!inB)\n                inB = true;\n            else {\n                inB = false;\n                iB++;\n            }\n        }\n    }\n    return result;\n}\n// Given a number of fragments for the outer tree, and a set of ranges\n// to parse, find fragments for inner trees mounted around those\n// ranges, if any.\nfunction enterFragments(mounts, ranges) {\n    let result = [];\n    for (let { pos, mount, frag } of mounts) {\n        let startPos = pos + (mount.overlay ? mount.overlay[0].from : 0), endPos = startPos + mount.tree.length;\n        let from = Math.max(frag.from, startPos), to = Math.min(frag.to, endPos);\n        if (mount.overlay) {\n            let overlay = mount.overlay.map(r => new Range(r.from + pos, r.to + pos));\n            let changes = findCoverChanges(ranges, overlay, from, to);\n            for (let i = 0, pos = from;; i++) {\n                let last = i == changes.length, end = last ? to : changes[i].from;\n                if (end > pos)\n                    result.push(new TreeFragment(pos, end, mount.tree, -startPos, frag.from >= pos || frag.openStart, frag.to <= end || frag.openEnd));\n                if (last)\n                    break;\n                pos = changes[i].to;\n            }\n        }\n        else {\n            result.push(new TreeFragment(from, to, mount.tree, -startPos, frag.from >= startPos || frag.openStart, frag.to <= endPos || frag.openEnd));\n        }\n    }\n    return result;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js\n");

/***/ })

};
;