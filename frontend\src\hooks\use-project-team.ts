'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/lib/supabase/client';

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
  avatar?: string | null;
  status: string;
  joinedAt: string;
  lastActive: string;
}

export interface AgentMember {
  id: string;
  name: string;
  role: string;
  imageSrc: string;
  skills: string[];
  description: string;
  color: string;
  email?: string;
}

export function useProjectTeam(projectId?: string) {
  const [realMembers, setRealMembers] = useState<TeamMember[]>([]);
  const [agentMembers, setAgentMembers] = useState<AgentMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock data for now - in a real app, this would come from the API
  const mockRealMembers: TeamMember[] = [
    {
      id: 'user-1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Admin',
      avatar: null,
      status: 'Active',
      joinedAt: '2024-01-15',
      lastActive: '2024-01-20'
    },
    {
      id: 'user-2',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Member',
      avatar: null,
      status: 'Active',
      joinedAt: '2024-01-18',
      lastActive: '2024-01-19'
    }
  ];

  const mockAgentMembers: AgentMember[] = [
    {
      id: 'ceo',
      name: 'Kenard',
      role: 'CEO',
      imageSrc: '/roles/kenard.png',
      skills: ['Strategic planning', 'Team leadership'],
      description: 'Manages the team and makes strategic decisions',
      color: '#4f6bed',
      email: '<EMAIL>'
    },
    {
      id: 'developer',
      name: 'Alex',
      role: 'Developer',
      imageSrc: '/roles/alex.png',
      skills: ['Full-stack development', 'Code architecture'],
      description: 'Builds and maintains the codebase',
      color: '#5b5fc7',
      email: '<EMAIL>'
    }
  ];

  // All available agent roles
  const availableRoles: AgentMember[] = [
    {
      id: 'ceo',
      name: 'Kenard',
      role: 'CEO',
      imageSrc: '/roles/kenard.png',
      skills: ['Strategic planning', 'Team leadership'],
      description: 'Manages the team and makes strategic decisions',
      color: '#4f6bed',
      email: '<EMAIL>'
    },
    {
      id: 'developer',
      name: 'Alex',
      role: 'Developer',
      imageSrc: '/roles/alex.png',
      skills: ['Full-stack development', 'Code architecture'],
      description: 'Builds and maintains the codebase',
      color: '#5b5fc7',
      email: '<EMAIL>'
    },
    {
      id: 'marketing',
      name: 'Chloe',
      role: 'Marketing Officer',
      imageSrc: '/roles/chloe.png',
      skills: ['Content creation', 'Campaign planning'],
      description: 'Creates and executes marketing strategies',
      color: '#8561c5',
      email: '<EMAIL>'
    },
    {
      id: 'product',
      name: 'Mark',
      role: 'Product Manager',
      imageSrc: '/roles/mark.png',
      skills: ['Feature prioritization', 'User research'],
      description: 'Defines product vision and roadmap',
      color: '#6366f1',
      email: '<EMAIL>'
    },
    {
      id: 'sales',
      name: 'Hannah',
      role: 'Sales Representative',
      imageSrc: '/roles/hannah.png',
      skills: ['Lead qualification', 'Demos and pitches'],
      description: 'Converts leads into customers',
      color: '#8b5cf6',
      email: '<EMAIL>'
    },
    {
      id: 'finance',
      name: 'Jenna',
      role: 'Finance Advisor',
      imageSrc: '/roles/jenna.png',
      skills: ['Budget planning', 'Financial analysis'],
      description: 'Manages budgets and financial planning',
      color: '#a855f7',
      email: '<EMAIL>'
    },
    {
      id: 'designer',
      name: 'Maisie',
      role: 'Designer',
      imageSrc: '/roles/maisie.png',
      skills: ['UI/UX design', 'Brand identity'],
      description: 'Creates visuals and user experiences',
      color: '#c084fc',
      email: '<EMAIL>'
    },
    {
      id: 'research',
      name: 'Garek',
      role: 'Research Analyst',
      imageSrc: '/roles/garek.png',
      skills: ['Competitive analysis', 'Market trends'],
      description: 'Gathers and analyzes market data',
      color: '#d946ef',
      email: '<EMAIL>'
    }
  ];

  useEffect(() => {
    const loadTeamData = async () => {
      try {
        setLoading(true);
        setError(null);

        // For now, use mock data
        // In a real implementation, you would fetch from your API:
        // const supabase = createClient();
        // const { data: members } = await supabase.rpc('get_project_team_members', { project_id: projectId });
        // const { data: agents } = await supabase.rpc('get_project_agents', { project_id: projectId });

        setRealMembers(mockRealMembers);
        setAgentMembers(mockAgentMembers);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load team data');
      } finally {
        setLoading(false);
      }
    };

    loadTeamData();
  }, [projectId]);



  const addAgent = (agentId: string) => {
    // In a real implementation, this would call your agent API
    // const supabase = createClient();
    // await supabase.rpc('add_project_agent', { project_id: projectId, agent_id: agentId });

    console.log('Adding agent:', agentId);
    // For now, just update local state - find the agent and add it
    const agentToAdd = availableRoles.find(role => role.id === agentId);
    if (agentToAdd && !agentMembers.some(member => member.id === agentId)) {
      setAgentMembers(prev => [...prev, agentToAdd]);
    }
  };

  const removeAgent = (agentId: string) => {
    // In a real implementation, this would call your agent API
    // const supabase = createClient();
    // await supabase.rpc('remove_project_agent', { project_id: projectId, agent_id: agentId });

    console.log('Removing agent:', agentId);
    setAgentMembers(prev => prev.filter(agent => agent.id !== agentId));
  };

  const changeTeamMemberRole = (memberId: string, newRole: string) => {
    // In a real implementation, this would call your team API
    // const supabase = createClient();
    // await supabase.rpc('update_team_member_role', { project_id: projectId, member_id: memberId, role: newRole });

    console.log('Changing role for member:', memberId, 'to', newRole);
    setRealMembers(prev =>
      prev.map(member =>
        member.id === memberId ? { ...member, role: newRole } : member
      )
    );
  };

  const removeTeamMember = (memberId: string) => {
    // In a real implementation, this would call your team API
    // const supabase = createClient();
    // await supabase.rpc('remove_team_member', { project_id: projectId, member_id: memberId });

    console.log('Removing team member:', memberId);
    setRealMembers(prev => prev.filter(member => member.id !== memberId));
  };

  return {
    realMembers,
    agentMembers,
    loading,
    error,
    addAgent,
    removeAgent,
    removeTeamMember,
    refetch: () => {
      // Trigger a refetch of the data
      setLoading(true);
      setTimeout(() => {
        setRealMembers(mockRealMembers);
        setAgentMembers(mockAgentMembers);
        setLoading(false);
      }, 500);
    }
  };
}
