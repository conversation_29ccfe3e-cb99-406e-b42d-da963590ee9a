import React, { useState } from 'react';
import { ChatInterface } from './ChatInterface';

interface AgentDemoContainerProps {
  isActive?: boolean;
  project?: any;
}

export function AgentDemoContainer({ isActive = true, project }: AgentDemoContainerProps) {
  const [errorTriggered, setErrorTriggered] = useState(true); // Auto-trigger the demo

  const handleSendMessage = (message: string) => {
    console.log('Message sent:', message);
    // In a real implementation, this would send the message to the agent
  };

  return (
    <ChatInterface
      isActive={isActive}
      errorTriggered={errorTriggered}
      onSendMessage={handleSendMessage}
      project={project}
    />
  );
}
