'use client';

import React, { useRef, forwardRef } from 'react';
import { Message, CustomGroup, SelectedFile } from './types';
import { groupMessagesByTime } from './utils';
import { MessageRenderer } from './MessageRenderer';
import { AGENT_ROLES } from './utils';

interface ChatMessagesProps {
  activeChat: string;
  ceoMessages: Message[];
  developerMessages: Message[];
  groupMessages: Message[];
  customGroups: CustomGroup[];
  dynamicAgentMessages?: Record<string, Message[]>;
  loading: boolean;
  error: string | null;
  onFileClick: (file: SelectedFile) => void;
}

export const ChatMessages = forwardRef<HTMLDivElement, ChatMessagesProps>(({
  activeChat,
  ceoMessages,
  developerMessages,
  groupMessages,
  customGroups,
  dynamicAgentMessages = {},
  loading,
  error,
  onFileClick
}, ref) => {
  const renderEmptyState = (chatName: string, description: string) => (
    <div className="h-full flex flex-col items-center justify-center text-[#999999]">
      <p className="text-center mb-2">No messages yet</p>
      <p className="text-center text-sm">{description}</p>
    </div>
  );

  const renderMessages = (messages: Message[]) => (
    groupMessagesByTime(messages).map((group, groupIndex) => (
      <div key={`group-${groupIndex}`}>
        {group.map((message, messageIndex) => (
          <MessageRenderer
            key={message.id}
            message={message}
            isFirstInGroup={messageIndex === 0}
            isInGroup={group.length > 1}
            onFileClick={onFileClick}
          />
        ))}
      </div>
    ))
  );

  const renderLoadingIndicator = () => (
    <div className="flex justify-start">
      <div className="flex-shrink-0 mr-2">
        <div className={`h-8 w-8 rounded-md overflow-hidden ${
          activeChat === 'ceo' ? 'bg-[#4f6bed]' :
          activeChat === 'developer' ? 'bg-[#5b5fc7]' :
          'bg-[#8561c5]'
        }`}>
          <div className="h-8 w-8 flex items-center justify-center text-white font-medium">
            {activeChat === 'ceo' ? (
              <img src="/roles/kenard.png" alt="Kenard" className="h-full w-full object-cover" />
            ) : activeChat === 'developer' ? (
              <img src="/roles/alex.png" alt="Alex" className="h-full w-full object-cover" />
            ) : (
              <span>TC</span>
            )}
          </div>
        </div>
      </div>
      <div className="bg-[#2a2a2a] text-white border border-[#333333] rounded-md p-2 px-3">
        <div className="flex items-center justify-center space-x-1 min-h-[20px]">
          <div className="w-2 h-2 bg-[#999999] rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-[#999999] rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-[#999999] rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    </div>
  );

  const renderErrorMessage = () => (
    <div className="flex justify-center">
      <div className="bg-red-500/20 border border-red-500/50 text-red-200 rounded-md p-2 px-3">
        <div className="text-sm">{error}</div>
      </div>
    </div>
  );

  // Find the agent name and role based on activeChat
  const getAgentInfo = () => {
    // Check if it's one of the standard chats
    if (activeChat === 'ceo') return { name: 'Kenard', role: 'CEO' };
    if (activeChat === 'developer') return { name: 'Alex', role: 'Developer' };
    if (activeChat === 'group') return { name: 'Team', role: 'Group' };
    
    // Check if it's a custom group
    const customGroup = customGroups.find(group => group.id === activeChat);
    if (customGroup) return { name: customGroup.name, role: 'Group' };
    
    // Check if it's one of the other agent types from AGENT_ROLES
    const agentRole = AGENT_ROLES.find(agent => agent.id === activeChat);
    if (agentRole) return { name: agentRole.name, role: agentRole.role };
    
    // Default fallback
    return { name: 'Agent', role: 'Assistant' };
  };
  
  const agentInfo = getAgentInfo();
  
  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4" ref={ref}>
      {/* Standard agent types with dedicated state variables */}
      {activeChat === 'ceo' && (
        <>
          {ceoMessages.length === 0 ? (
            renderEmptyState('Kenard', 'Your conversation with Kenard will appear here')
          ) : (
            renderMessages(ceoMessages)
          )}
        </>
      )}

      {activeChat === 'developer' && (
        <>
          {developerMessages.length === 0 ? (
            renderEmptyState('Alex', 'Your conversation with Alex will appear here')
          ) : (
            renderMessages(developerMessages)
          )}
        </>
      )}

      {activeChat === 'group' && (
        <>
          {groupMessages.length === 0 ? (
            renderEmptyState('Team', 'Team conversation will appear here')
          ) : (
            renderMessages(groupMessages)
          )}
        </>
      )}

      {/* Custom Group Messages */}
      {(() => {
        const customGroup = customGroups.find(group => group.id === activeChat);
        if (!customGroup) return null;

        return (
          <>
            {customGroup.messages.length === 0 ? (
              renderEmptyState(
                customGroup.name,
                `Start a conversation with ${customGroup.participants.map(p => p.name).join(', ')}`
              )
            ) : (
              groupMessagesByTime(customGroup.messages).map((group, groupIndex) => (
                <div key={`custom-group-${groupIndex}`}>
                  {group.map((message, messageIndex) => (
                    <MessageRenderer
                      key={message.id}
                      message={message}
                      isFirstInGroup={messageIndex === 0}
                      isInGroup={group.length > 1}
                      onFileClick={onFileClick}
                    />
                  ))}
                </div>
              ))
            )}
          </>
        );
      })()}
      
      {/* Dynamic agent types (marketing, product, etc.) */}
      {(() => {
        // Skip rendering for known agent types that are handled above
        if (['ceo', 'developer', 'group'].includes(activeChat)) return null;
        if (customGroups.some(group => group.id === activeChat)) return null;
        
        // Find agent info for the current activeChat
        const agentRole = AGENT_ROLES.find(agent => agent.id === activeChat);
        if (!agentRole) return null;
        
        // Get messages for this dynamic agent
        const messages = dynamicAgentMessages[activeChat] || [];
        console.log(`Rendering messages for ${activeChat}:`, messages);
        
        return (
          <>
            {messages.length === 0 ? (
              renderEmptyState(
                agentRole.name,
                `Your conversation with ${agentRole.name} (${agentRole.role}) will appear here`
              )
            ) : (
              renderMessages(messages)
            )}
          </>
        );
      })()}

      {/* Loading indicator */}
      {loading && renderLoadingIndicator()}

      {/* Error message */}
      {error && renderErrorMessage()}
    </div>
  );
});
