import os
import time
import base64
import aiohttp
import asyncio
import logging
import json
from typing import Optional, Dict, Any, Union, List
from PIL import Image
import numpy as np
from io import BytesIO

from agentpress.tool import Tool, ToolResult, openapi_schema, xml_schema
from sandbox.sandbox import SandboxToolsBase, Sandbox

KEYBOARD_KEYS = [
    'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
    'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
    '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
    'enter', 'esc', 'backspace', 'tab', 'space', 'delete',
    'ctrl', 'alt', 'shift', 'win',
    'up', 'down', 'left', 'right',
    'f1', 'f2', 'f3', 'f4', 'f5', 'f6', 'f7', 'f8', 'f9', 'f10', 'f11', 'f12',
    'ctrl+c', 'ctrl+v', 'ctrl+x', 'ctrl+z', 'ctrl+a', 'ctrl+s',
    'alt+tab', 'alt+f4', 'ctrl+alt+delete'
]

class ComputerUseTool(SandboxToolsBase):
    """Computer automation tool for controlling the sandbox browser and GUI."""

    def __init__(self, sandbox: Sandbox):
        """Initialize automation tool with sandbox connection."""
        super().__init__(sandbox)
        self.session = None
        self.mouse_x = 0  # Track current mouse position
        self.mouse_y = 0
        # Get automation service URL using port 8000
        self.api_base_url = self.sandbox.get_preview_link(8000)
        logging.info(f"Initialized Computer Use Tool with API URL: {self.api_base_url}")

    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session for API requests."""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session

    async def _api_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """Send request to automation service API."""
        try:
            session = await self._get_session()
            url = f"{self.api_base_url}/api{endpoint}"

            logging.debug(f"API request: {method} {url} {data}")

            if method.upper() == "GET":
                async with session.get(url) as response:
                    result = await response.json()
            else:  # POST
                async with session.post(url, json=data) as response:
                    result = await response.json()

            logging.debug(f"API response: {result}")
            return result

        except Exception as e:
            logging.error(f"API request failed: {str(e)}")
            return {"success": False, "error": str(e)}

    async def cleanup(self):
        """Clean up resources."""
        if self.session and not self.session.closed:
            await self.session.close()
            self.session = None

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "move_to",
            "description": "Move cursor to specified position",
            "parameters": {
                "type": "object",
                "properties": {
                    "x": {
                        "type": "number",
                        "description": "X coordinate"
                    },
                    "y": {
                        "type": "number",
                        "description": "Y coordinate"
                    }
                },
                "required": ["x", "y"]
            }
        }
    })
    @xml_schema(
        tag_name="move-to",
        mappings=[
            {"param_name": "x", "node_type": "attribute", "path": "."},
            {"param_name": "y", "node_type": "attribute", "path": "."}
        ],
        example='''
        <move-to x="100" y="200">
        </move-to>
        '''
    )
    async def move_to(self, x: float, y: float) -> ToolResult:
        """Move cursor to specified position."""
        try:
            x_int = int(round(float(x)))
            y_int = int(round(float(y)))

            result = await self._api_request("POST", "/automation/mouse/move", {
                "x": x_int,
                "y": y_int
            })

            if result.get("success", False):
                self.mouse_x = x_int
                self.mouse_y = y_int
                return ToolResult(success=True, output=f"Moved to ({x_int}, {y_int})")
            else:
                return ToolResult(success=False, output=f"Failed to move: {result.get('error', 'Unknown error')}")

        except Exception as e:
            return ToolResult(success=False, output=f"Failed to move: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "click",
            "description": "Click at current or specified position",
            "parameters": {
                "type": "object",
                "properties": {
                    "button": {
                        "type": "string",
                        "description": "Mouse button to click",
                        "enum": ["left", "right", "middle"],
                        "default": "left"
                    },
                    "x": {
                        "type": "number",
                        "description": "Optional X coordinate"
                    },
                    "y": {
                        "type": "number",
                        "description": "Optional Y coordinate"
                    },
                    "num_clicks": {
                        "type": "integer",
                        "description": "Number of clicks",
                        "enum": [1, 2, 3],
                        "default": 1
                    }
                }
            }
        }
    })
    @xml_schema(
        tag_name="click",
        mappings=[
            {"param_name": "x", "node_type": "attribute", "path": "x"},
            {"param_name": "y", "node_type": "attribute", "path": "y"},
            {"param_name": "button", "node_type": "attribute", "path": "button"},
            {"param_name": "num_clicks", "node_type": "attribute", "path": "num_clicks"}
        ],
        example='''
        <click x="100" y="200" button="left" num_clicks="1">
        </click>
        '''
    )
    async def click(self, x: Optional[float] = None, y: Optional[float] = None,
                   button: str = "left", num_clicks: int = 1) -> ToolResult:
        """Click at current or specified position."""
        try:
            x_val = x if x is not None else self.mouse_x
            y_val = y if y is not None else self.mouse_y

            x_int = int(round(float(x_val)))
            y_int = int(round(float(y_val)))
            num_clicks = int(num_clicks)

            result = await self._api_request("POST", "/automation/mouse/click", {
                "x": x_int,
                "y": y_int,
                "clicks": num_clicks,
                "button": button.lower()
            })

            if result.get("success", False):
                self.mouse_x = x_int
                self.mouse_y = y_int
                return ToolResult(success=True,
                                output=f"{num_clicks} {button} click(s) performed at ({x_int}, {y_int})")
            else:
                return ToolResult(success=False, output=f"Failed to click: {result.get('error', 'Unknown error')}")
        except Exception as e:
            return ToolResult(success=False, output=f"Failed to click: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "scroll",
            "description": "Scroll the mouse wheel at current position",
            "parameters": {
                "type": "object",
                "properties": {
                    "amount": {
                        "type": "integer",
                        "description": "Scroll amount (positive for up, negative for down)",
                        "minimum": -10,
                        "maximum": 10
                    }
                },
                "required": ["amount"]
            }
        }
    })
    @xml_schema(
        tag_name="scroll",
        mappings=[
            {"param_name": "amount", "node_type": "attribute", "path": "amount"}
        ],
        example='''
        <scroll amount="-3">
        </scroll>
        '''
    )
    async def scroll(self, amount: int) -> ToolResult:
        """
        Scroll the mouse wheel at current position.
        Positive values scroll up, negative values scroll down.
        """
        try:
            amount = int(float(amount))
            amount = max(-10, min(10, amount))

            result = await self._api_request("POST", "/automation/mouse/scroll", {
                "clicks": amount,
                "x": self.mouse_x,
                "y": self.mouse_y
            })

            if result.get("success", False):
                direction = "up" if amount > 0 else "down"
                steps = abs(amount)
                return ToolResult(success=True,
                                output=f"Scrolled {direction} {steps} step(s) at position ({self.mouse_x}, {self.mouse_y})")
            else:
                return ToolResult(success=False, output=f"Failed to scroll: {result.get('error', 'Unknown error')}")
        except Exception as e:
            return ToolResult(success=False, output=f"Failed to scroll: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "typing",
            "description": "Type specified text",
            "parameters": {
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "Text to type"
                    }
                },
                "required": ["text"]
            }
        }
    })
    @xml_schema(
        tag_name="typing",
        mappings=[
            {"param_name": "text", "node_type": "content", "path": "text"}
        ],
        example='''
        <typing>Hello World!</typing>
        '''
    )
    async def typing(self, text: str) -> ToolResult:
        """Type specified text."""
        try:
            text = str(text)

            result = await self._api_request("POST", "/automation/keyboard/write", {
                "message": text,
                "interval": 0.01
            })

            if result.get("success", False):
                return ToolResult(success=True, output=f"Typed: {text}")
            else:
                return ToolResult(success=False, output=f"Failed to type: {result.get('error', 'Unknown error')}")
        except Exception as e:
            return ToolResult(success=False, output=f"Failed to type: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "press",
            "description": "Press and release a key",
            "parameters": {
                "type": "object",
                "properties": {
                    "key": {
                        "type": "string",
                        "description": "Key to press",
                        "enum": KEYBOARD_KEYS
                    }
                },
                "required": ["key"]
            }
        }
    })
    @xml_schema(
        tag_name="press",
        mappings=[
            {"param_name": "key", "node_type": "attribute", "path": "key"}
        ],
        example='''
        <press key="enter">
        </press>
        '''
    )
    async def press(self, key: str) -> ToolResult:
        """Press and release a key."""
        try:
            key = str(key).lower()

            result = await self._api_request("POST", "/automation/keyboard/press", {
                "keys": key,
                "presses": 1
            })

            if result.get("success", False):
                return ToolResult(success=True, output=f"Pressed key: {key}")
            else:
                return ToolResult(success=False, output=f"Failed to press key: {result.get('error', 'Unknown error')}")
        except Exception as e:
            return ToolResult(success=False, output=f"Failed to press key: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "wait",
            "description": "Wait for specified duration",
            "parameters": {
                "type": "object",
                "properties": {
                    "duration": {
                        "type": "number",
                        "description": "Duration in seconds",
                        "default": 0.5
                    }
                }
            }
        }
    })
    @xml_schema(
        tag_name="wait",
        mappings=[
            {"param_name": "duration", "node_type": "attribute", "path": "duration"}
        ],
        example='''
        <wait duration="1.5">
        </wait>
        '''
    )
    async def wait(self, duration: float = 0.5) -> ToolResult:
        """Wait for specified duration."""
        try:
            duration = float(duration)
            duration = max(0, min(10, duration))
            await asyncio.sleep(duration)
            return ToolResult(success=True, output=f"Waited {duration} seconds")
        except Exception as e:
            return ToolResult(success=False, output=f"Failed to wait: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "mouse_down",
            "description": "Press a mouse button",
            "parameters": {
                "type": "object",
                "properties": {
                    "button": {
                        "type": "string",
                        "description": "Mouse button to press",
                        "enum": ["left", "right", "middle"],
                        "default": "left"
                    }
                }
            }
        }
    })
    @xml_schema(
        tag_name="mouse-down",
        mappings=[
            {"param_name": "button", "node_type": "attribute", "path": "button"}
        ],
        example='''
        <mouse-down button="left">
        </mouse-down>
        '''
    )
    async def mouse_down(self, button: str = "left", x: Optional[float] = None, y: Optional[float] = None) -> ToolResult:
        """Press a mouse button at current or specified position."""
        try:
            x_val = x if x is not None else self.mouse_x
            y_val = y if y is not None else self.mouse_y

            x_int = int(round(float(x_val)))
            y_int = int(round(float(y_val)))

            result = await self._api_request("POST", "/automation/mouse/down", {
                "x": x_int,
                "y": y_int,
                "button": button.lower()
            })

            if result.get("success", False):
                self.mouse_x = x_int
                self.mouse_y = y_int
                return ToolResult(success=True, output=f"{button} button pressed at ({x_int}, {y_int})")
            else:
                return ToolResult(success=False, output=f"Failed to press button: {result.get('error', 'Unknown error')}")
        except Exception as e:
            return ToolResult(success=False, output=f"Failed to press button: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "mouse_up",
            "description": "Release a mouse button",
            "parameters": {
                "type": "object",
                "properties": {
                    "button": {
                        "type": "string",
                        "description": "Mouse button to release",
                        "enum": ["left", "right", "middle"],
                        "default": "left"
                    }
                }
            }
        }
    })
    @xml_schema(
        tag_name="mouse-up",
        mappings=[
            {"param_name": "button", "node_type": "attribute", "path": "button"}
        ],
        example='''
        <mouse-up button="left">
        </mouse-up>
        '''
    )
    async def mouse_up(self, button: str = "left", x: Optional[float] = None, y: Optional[float] = None) -> ToolResult:
        """Release a mouse button at current or specified position."""
        try:
            x_val = x if x is not None else self.mouse_x
            y_val = y if y is not None else self.mouse_y

            x_int = int(round(float(x_val)))
            y_int = int(round(float(y_val)))

            result = await self._api_request("POST", "/automation/mouse/up", {
                "x": x_int,
                "y": y_int,
                "button": button.lower()
            })

            if result.get("success", False):
                self.mouse_x = x_int
                self.mouse_y = y_int
                return ToolResult(success=True, output=f"{button} button released at ({x_int}, {y_int})")
            else:
                return ToolResult(success=False, output=f"Failed to release button: {result.get('error', 'Unknown error')}")
        except Exception as e:
            return ToolResult(success=False, output=f"Failed to release button: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "drag_to",
            "description": "Drag cursor to specified position",
            "parameters": {
                "type": "object",
                "properties": {
                    "x": {
                        "type": "number",
                        "description": "Target X coordinate"
                    },
                    "y": {
                        "type": "number",
                        "description": "Target Y coordinate"
                    }
                },
                "required": ["x", "y"]
            }
        }
    })
    @xml_schema(
        tag_name="drag-to",
        mappings=[
            {"param_name": "x", "node_type": "attribute", "path": "x"},
            {"param_name": "y", "node_type": "attribute", "path": "y"}
        ],
        example='''
        <drag-to x="500" y="50">
        </drag-to>
        '''
    )
    async def drag_to(self, x: float, y: float) -> ToolResult:
        """Click and drag from current position to target position."""
        try:
            target_x = int(round(float(x)))
            target_y = int(round(float(y)))
            start_x = self.mouse_x
            start_y = self.mouse_y

            result = await self._api_request("POST", "/automation/mouse/drag", {
                "x": target_x,
                "y": target_y,
                "duration": 0.3,
                "button": "left"
            })

            if result.get("success", False):
                self.mouse_x = target_x
                self.mouse_y = target_y
                return ToolResult(success=True,
                                output=f"Dragged from ({start_x}, {start_y}) to ({target_x}, {target_y})")
            else:
                return ToolResult(success=False, output=f"Failed to drag: {result.get('error', 'Unknown error')}")
        except Exception as e:
            return ToolResult(success=False, output=f"Failed to drag: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "take_screenshot",
            "description": "Take a screenshot of the entire screen or a specific region",
            "parameters": {
                "type": "object",
                "properties": {
                    "x1": {
                        "type": "number",
                        "description": "Top-left X coordinate of region (optional)"
                    },
                    "y1": {
                        "type": "number",
                        "description": "Top-left Y coordinate of region (optional)"
                    },
                    "x2": {
                        "type": "number",
                        "description": "Bottom-right X coordinate of region (optional)"
                    },
                    "y2": {
                        "type": "number",
                        "description": "Bottom-right Y coordinate of region (optional)"
                    },
                    "save_to_file": {
                        "type": "boolean",
                        "description": "Whether to save the screenshot to a file",
                        "default": True
                    }
                }
            }
        }
    })
    @xml_schema(
        tag_name="take-screenshot",
        mappings=[
            {"param_name": "x1", "node_type": "attribute", "path": "x1"},
            {"param_name": "y1", "node_type": "attribute", "path": "y1"},
            {"param_name": "x2", "node_type": "attribute", "path": "x2"},
            {"param_name": "y2", "node_type": "attribute", "path": "y2"},
            {"param_name": "save_to_file", "node_type": "attribute", "path": "save"}
        ],
        example='''
        <take-screenshot x1="100" y1="200" x2="500" y2="400" save="true">
        </take-screenshot>
        '''
    )
    async def take_screenshot(self, x1: Optional[float] = None, y1: Optional[float] = None,
                            x2: Optional[float] = None, y2: Optional[float] = None,
                            save_to_file: bool = True) -> ToolResult:
        """Take a screenshot of the entire screen or a specific region."""
        try:
            # Prepare region parameters if provided
            region = None
            if all(coord is not None for coord in [x1, y1, x2, y2]):
                region = {
                    "x": int(float(x1)),
                    "y": int(float(y1)),
                    "width": int(float(x2)) - int(float(x1)),
                    "height": int(float(y2)) - int(float(y1))
                }

            # Call screenshot API with region if specified
            params = {}
            if region:
                params["region"] = region

            screenshot_data = await self.get_screenshot_base64(region)

            if screenshot_data:
                region_str = f" of region ({x1}, {y1}, {x2}, {y2})" if region else ""
                file_str = f", saved to {screenshot_data['filename']}" if save_to_file else ""
                return ToolResult(success=True,
                                output=f"Screenshot taken{region_str}{file_str}")
            else:
                return ToolResult(success=False, output="Failed to take screenshot")
        except Exception as e:
            return ToolResult(success=False, output=f"Screenshot failed: {str(e)}")

    async def get_screenshot_base64(self, region: Optional[Dict] = None) -> Optional[dict]:
        """Capture screen and return as base64 encoded image."""
        try:
            params = {}
            if region:
                params["region"] = region

            result = await self._api_request("POST", "/automation/screenshot", params)

            if "image" in result:
                base64_str = result["image"]
                timestamp = time.strftime("%Y%m%d_%H%M%S")

                # Save screenshot to file
                screenshots_dir = "screenshots"
                if not os.path.exists(screenshots_dir):
                    os.makedirs(screenshots_dir)

                region_suffix = ""
                if region:
                    region_suffix = f"_region_{region['x']}_{region['y']}_{region['width']}_{region['height']}"

                timestamped_filename = os.path.join(screenshots_dir, f"screenshot_{timestamp}{region_suffix}.png")
                latest_filename = "latest_screenshot.png"

                # Decode base64 string and save to file
                img_data = base64.b64decode(base64_str)
                with open(timestamped_filename, 'wb') as f:
                    f.write(img_data)

                # Save a copy as the latest screenshot
                with open(latest_filename, 'wb') as f:
                    f.write(img_data)

                return {
                    "content_type": "image/png",
                    "base64": base64_str,
                    "timestamp": timestamp,
                    "filename": timestamped_filename
                }
            else:
                return None

        except Exception as e:
            print(f"[Screenshot] Error during screenshot process: {str(e)}")
            return None

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "hotkey",
            "description": "Press a key combination",
            "parameters": {
                "type": "object",
                "properties": {
                    "keys": {
                        "type": "string",
                        "description": "Key combination to press",
                        "enum": KEYBOARD_KEYS
                    }
                },
                "required": ["keys"]
            }
        }
    })
    @xml_schema(
        tag_name="hotkey",
        mappings=[
            {"param_name": "keys", "node_type": "attribute", "path": "keys"}
        ],
        example='''
        <hotkey keys="ctrl+a">
        </hotkey>
        '''
        )
    async def hotkey(self, keys: str) -> ToolResult:
        """Press a key combination."""
        try:
            keys = str(keys).lower().strip()
            key_sequence = keys.split('+')

            result = await self._api_request("POST", "/automation/keyboard/hotkey", {
                "keys": key_sequence,
                "interval": 0.01
            })

            if result.get("success", False):
                return ToolResult(success=True, output=f"Pressed key combination: {keys}")
            else:
                return ToolResult(success=False, output=f"Failed to press keys: {result.get('error', 'Unknown error')}")
        except Exception as e:
            return ToolResult(success=False, output=f"Failed to press keys: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "ocr_screen",
            "description": "Extract text from the entire screen or a specific region using OCR",
            "parameters": {
                "type": "object",
                "properties": {
                    "x1": {
                        "type": "number",
                        "description": "Top-left X coordinate of region (optional)"
                    },
                    "y1": {
                        "type": "number",
                        "description": "Top-left Y coordinate of region (optional)"
                    },
                    "x2": {
                        "type": "number",
                        "description": "Bottom-right X coordinate of region (optional)"
                    },
                    "y2": {
                        "type": "number",
                        "description": "Bottom-right Y coordinate of region (optional)"
                    }
                }
            }
        }
    })
    @xml_schema(
        tag_name="ocr-screen",
        mappings=[
            {"param_name": "x1", "node_type": "attribute", "path": "x1"},
            {"param_name": "y1", "node_type": "attribute", "path": "y1"},
            {"param_name": "x2", "node_type": "attribute", "path": "x2"},
            {"param_name": "y2", "node_type": "attribute", "path": "y2"}
        ],
        example='''
        <ocr-screen x1="100" y1="200" x2="500" y2="400">
        </ocr-screen>
        '''
    )
    async def ocr_screen(self, x1: Optional[float] = None, y1: Optional[float] = None,
                        x2: Optional[float] = None, y2: Optional[float] = None) -> ToolResult:
        """Extract text from the screen or a specific region using OCR."""
        try:
            # Prepare region parameters if provided
            region = None
            if all(coord is not None for coord in [x1, y1, x2, y2]):
                region = {
                    "x": int(float(x1)),
                    "y": int(float(y1)),
                    "width": int(float(x2)) - int(float(x1)),
                    "height": int(float(y2)) - int(float(y1))
                }

            # Call OCR API endpoint
            result = await self._api_request("POST", "/automation/ocr", {
                "region": region
            })

            if result.get("success", False):
                text = result.get("text", "")
                region_str = f" in region ({x1}, {y1}, {x2}, {y2})" if region else ""
                return ToolResult(success=True,
                                output=f"OCR text{region_str}:\n{text}")
            else:
                return ToolResult(success=False, output=f"OCR failed: {result.get('error', 'Unknown error')}")
        except Exception as e:
            return ToolResult(success=False, output=f"OCR failed: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "find_element",
            "description": "Find UI element on screen by type and/or text content",
            "parameters": {
                "type": "object",
                "properties": {
                    "element_type": {
                        "type": "string",
                        "description": "Type of element to find",
                        "enum": ["button", "input", "checkbox", "radio", "link", "image", "text", "any"],
                        "default": "any"
                    },
                    "text_content": {
                        "type": "string",
                        "description": "Text content to search for (optional)"
                    },
                    "search_region": {
                        "type": "object",
                        "description": "Region to search in (optional)",
                        "properties": {
                            "x1": {"type": "number"},
                            "y1": {"type": "number"},
                            "x2": {"type": "number"},
                            "y2": {"type": "number"}
                        }
                    }
                },
                "required": ["element_type"]
            }
        }
    })
    @xml_schema(
        tag_name="find-element",
        mappings=[
            {"param_name": "element_type", "node_type": "attribute", "path": "type"},
            {"param_name": "text_content", "node_type": "attribute", "path": "text"}
        ],
        example='''
        <find-element type="button" text="Submit">
        </find-element>
        '''
    )
    async def find_element(self, element_type: str = "any", text_content: Optional[str] = None,
                         search_region: Optional[Dict] = None) -> ToolResult:
        """Find UI element on screen by type and/or text content."""
        try:
            # Prepare search parameters
            params = {
                "element_type": element_type,
                "text": text_content
            }

            if search_region:
                params["region"] = {
                    "x": int(float(search_region.get("x1", 0))),
                    "y": int(float(search_region.get("y1", 0))),
                    "width": int(float(search_region.get("x2", 0))) - int(float(search_region.get("x1", 0))),
                    "height": int(float(search_region.get("y2", 0))) - int(float(search_region.get("y1", 0)))
                }

            # Call element detection API endpoint
            result = await self._api_request("POST", "/automation/find_element", params)

            if result.get("success", False) and "elements" in result:
                elements = result["elements"]
                if not elements:
                    return ToolResult(success=True, output=f"No {element_type} elements found")

                output = f"Found {len(elements)} {element_type} element(s):\n"
                for i, elem in enumerate(elements):
                    output += f"{i+1}. {elem.get('type', 'unknown')} at ({elem.get('x', 0)}, {elem.get('y', 0)}) "
                    output += f"size: {elem.get('width', 0)}x{elem.get('height', 0)} "
                    if "text" in elem and elem["text"]:
                        output += f"text: '{elem['text']}'"
                    output += "\n"

                return ToolResult(success=True, output=output)
            else:
                return ToolResult(success=False,
                                output=f"Element detection failed: {result.get('error', 'Unknown error')}")
        except Exception as e:
            return ToolResult(success=False, output=f"Element detection failed: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "click_element",
            "description": "Click on a UI element by type and/or text content",
            "parameters": {
                "type": "object",
                "properties": {
                    "element_type": {
                        "type": "string",
                        "description": "Type of element to click",
                        "enum": ["button", "input", "checkbox", "radio", "link", "image", "text", "any"],
                        "default": "any"
                    },
                    "text_content": {
                        "type": "string",
                        "description": "Text content to search for"
                    },
                    "index": {
                        "type": "integer",
                        "description": "Index of element if multiple matches (0-based)",
                        "default": 0
                    }
                },
                "required": ["text_content"]
            }
        }
    })
    @xml_schema(
        tag_name="click-element",
        mappings=[
            {"param_name": "element_type", "node_type": "attribute", "path": "type"},
            {"param_name": "text_content", "node_type": "attribute", "path": "text"},
            {"param_name": "index", "node_type": "attribute", "path": "index"}
        ],
        example='''
        <click-element type="button" text="Submit" index="0">
        </click-element>
        '''
    )
    async def click_element(self, text_content: str, element_type: str = "any",
                          index: int = 0) -> ToolResult:
        """Click on a UI element by type and/or text content."""
        try:
            # First find the element
            find_result = await self.find_element(element_type, text_content)

            if not find_result.success:
                return ToolResult(success=False,
                                output=f"Failed to find element: {find_result.output}")

            # Parse the output to extract element coordinates
            lines = find_result.output.split('\n')
            if "No elements found" in find_result.output:
                return ToolResult(success=False,
                                output=f"No {element_type} elements with text '{text_content}' found")

            # Extract element info from the output
            for line in lines:
                if line.startswith(f"{index+1}."):
                    # Extract coordinates using regex or string parsing
                    import re
                    coords = re.search(r"at \((\d+), (\d+)\)", line)
                    if coords:
                        x, y = int(coords.group(1)), int(coords.group(2))
                        # Click on the element
                        return await self.click(x=x, y=y)

            return ToolResult(success=False,
                            output=f"Element found but couldn't extract coordinates to click")
        except Exception as e:
            return ToolResult(success=False, output=f"Click element failed: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "describe_screen",
            "description": "Describe what's visible on the screen",
            "parameters": {
                "type": "object",
                "properties": {
                    "detail_level": {
                        "type": "string",
                        "description": "Level of detail in the description",
                        "enum": ["low", "medium", "high"],
                        "default": "medium"
                    }
                }
            }
        }
    })
    @xml_schema(
        tag_name="describe-screen",
        mappings=[
            {"param_name": "detail_level", "node_type": "attribute", "path": "detail"}
        ],
        example='''
        <describe-screen detail="high">
        </describe-screen>
        '''
    )
    async def describe_screen(self, detail_level: str = "medium") -> ToolResult:
        """Describe what's visible on the screen using computer vision."""
        try:
            # Take a screenshot first
            screenshot_data = await self.get_screenshot_base64()
            if not screenshot_data:
                return ToolResult(success=False, output="Failed to capture screenshot for analysis")

            # Call screen description API endpoint
            result = await self._api_request("POST", "/automation/describe_screen", {
                "detail_level": detail_level
            })

            if result.get("success", False):
                description = result.get("description", "No description available")
                elements = result.get("elements", [])

                output = f"Screen description:\n{description}\n\n"

                if elements:
                    output += "Detected elements:\n"
                    for i, elem in enumerate(elements):
                        output += f"{i+1}. {elem.get('type', 'unknown')}"
                        if "text" in elem and elem["text"]:
                            output += f": '{elem['text']}'"
                        output += f" at ({elem.get('x', 0)}, {elem.get('y', 0)})\n"

                return ToolResult(success=True, output=output)
            else:
                return ToolResult(success=False,
                                output=f"Screen description failed: {result.get('error', 'Unknown error')}")
        except Exception as e:
            return ToolResult(success=False, output=f"Screen description failed: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "compare_screenshots",
            "description": "Compare current screen with previous screenshot to detect changes",
            "parameters": {
                "type": "object",
                "properties": {
                    "threshold": {
                        "type": "number",
                        "description": "Similarity threshold (0.0-1.0, lower means more sensitive)",
                        "default": 0.95
                    },
                    "highlight_changes": {
                        "type": "boolean",
                        "description": "Whether to highlight changes in the output",
                        "default": True
                    }
                }
            }
        }
    })
    @xml_schema(
        tag_name="compare-screenshots",
        mappings=[
            {"param_name": "threshold", "node_type": "attribute", "path": "threshold"},
            {"param_name": "highlight_changes", "node_type": "attribute", "path": "highlight"}
        ],
        example='''
        <compare-screenshots threshold="0.9" highlight="true">
        </compare-screenshots>
        '''
    )
    async def compare_screenshots(self, threshold: float = 0.95,
                                highlight_changes: bool = True) -> ToolResult:
        """Compare current screen with previous screenshot to detect changes."""
        try:
            # Call screenshot comparison API endpoint
            result = await self._api_request("POST", "/automation/compare_screenshots", {
                "threshold": float(threshold),
                "highlight": bool(highlight_changes)
            })

            if result.get("success", False):
                similarity = result.get("similarity", 0)
                changes = result.get("changes", [])

                output = f"Screenshot comparison results:\n"
                output += f"Similarity: {similarity:.2%}\n"

                if changes:
                    output += f"Detected {len(changes)} change(s):\n"
                    for i, change in enumerate(changes):
                        output += f"{i+1}. Region: ({change.get('x', 0)}, {change.get('y', 0)}) "
                        output += f"size: {change.get('width', 0)}x{change.get('height', 0)}\n"
                else:
                    output += "No significant changes detected."

                return ToolResult(success=True, output=output)
            else:
                return ToolResult(success=False,
                                output=f"Screenshot comparison failed: {result.get('error', 'Unknown error')}")
        except Exception as e:
            return ToolResult(success=False, output=f"Screenshot comparison failed: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "smart_navigate",
            "description": "Intelligently navigate to a UI element using natural language description",
            "parameters": {
                "type": "object",
                "properties": {
                    "description": {
                        "type": "string",
                        "description": "Natural language description of the element to find (e.g., 'the submit button', 'search box in the top right')"
                    },
                    "action": {
                        "type": "string",
                        "description": "Action to perform on the element",
                        "enum": ["click", "type", "hover", "right_click"],
                        "default": "click"
                    },
                    "text_to_type": {
                        "type": "string",
                        "description": "Text to type if action is 'type'"
                    }
                },
                "required": ["description"]
            }
        }
    })
    @xml_schema(
        tag_name="smart-navigate",
        mappings=[
            {"param_name": "description", "node_type": "attribute", "path": "description"},
            {"param_name": "action", "node_type": "attribute", "path": "action"},
            {"param_name": "text_to_type", "node_type": "content", "path": "text"}
        ],
        example='''
        <smart-navigate description="the login button" action="click">
        </smart-navigate>
        '''
    )
    async def smart_navigate(self, description: str, action: str = "click",
                           text_to_type: Optional[str] = None) -> ToolResult:
        """Intelligently navigate to a UI element using natural language description."""
        try:
            # Call smart navigation API endpoint
            params = {
                "description": description,
                "action": action
            }

            if action == "type" and text_to_type:
                params["text"] = text_to_type

            result = await self._api_request("POST", "/automation/smart_navigate", params)

            if result.get("success", False):
                element = result.get("element", {})

                output = f"Successfully performed '{action}' on element matching '{description}'"
                if action == "type" and text_to_type:
                    output += f" with text '{text_to_type}'"

                if element:
                    output += f"\nElement details: {element.get('type', 'unknown')} "
                    output += f"at ({element.get('x', 0)}, {element.get('y', 0)}) "
                    if "text" in element and element["text"]:
                        output += f"with text '{element['text']}'"

                return ToolResult(success=True, output=output)
            else:
                return ToolResult(success=False,
                                output=f"Smart navigation failed: {result.get('error', 'Unknown error')}")
        except Exception as e:
            return ToolResult(success=False, output=f"Smart navigation failed: {str(e)}")

    @openapi_schema({
        "type": "function",
        "function": {
            "name": "wait_for_element",
            "description": "Wait for a specific element to appear on screen",
            "parameters": {
                "type": "object",
                "properties": {
                    "element_type": {
                        "type": "string",
                        "description": "Type of element to wait for",
                        "enum": ["button", "input", "checkbox", "radio", "link", "image", "text", "any"],
                        "default": "any"
                    },
                    "text_content": {
                        "type": "string",
                        "description": "Text content to search for"
                    },
                    "timeout": {
                        "type": "number",
                        "description": "Maximum time to wait in seconds",
                        "default": 10.0
                    },
                    "check_interval": {
                        "type": "number",
                        "description": "Interval between checks in seconds",
                        "default": 0.5
                    }
                },
                "required": ["text_content"]
            }
        }
    })
    @xml_schema(
        tag_name="wait-for-element",
        mappings=[
            {"param_name": "element_type", "node_type": "attribute", "path": "type"},
            {"param_name": "text_content", "node_type": "attribute", "path": "text"},
            {"param_name": "timeout", "node_type": "attribute", "path": "timeout"},
            {"param_name": "check_interval", "node_type": "attribute", "path": "interval"}
        ],
        example='''
        <wait-for-element type="button" text="Continue" timeout="15" interval="1">
        </wait-for-element>
        '''
    )
    async def wait_for_element(self, text_content: str, element_type: str = "any",
                             timeout: float = 10.0, check_interval: float = 0.5) -> ToolResult:
        """Wait for a specific element to appear on screen."""
        try:
            start_time = time.time()
            timeout = float(timeout)
            check_interval = float(check_interval)

            while time.time() - start_time < timeout:
                # Try to find the element
                find_result = await self.find_element(element_type, text_content)

                # If element found, return success
                if find_result.success and "No elements found" not in find_result.output:
                    elapsed = time.time() - start_time
                    return ToolResult(success=True,
                                    output=f"Element found after {elapsed:.2f} seconds: {find_result.output}")

                # Wait before next check
                await asyncio.sleep(check_interval)

            # Timeout reached
            return ToolResult(success=False,
                            output=f"Timeout after {timeout} seconds waiting for {element_type} with text '{text_content}'")
        except Exception as e:
            return ToolResult(success=False, output=f"Wait for element failed: {str(e)}")

if __name__ == "__main__":
    print("This module should be imported, not run directly.")