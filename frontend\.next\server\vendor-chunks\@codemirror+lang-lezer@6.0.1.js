"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-lezer@6.0.1";
exports.ids = ["vendor-chunks/@codemirror+lang-lezer@6.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-lezer@6.0.1/node_modules/@codemirror/lang-lezer/dist/index.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-lezer@6.0.1/node_modules/@codemirror/lang-lezer/dist/index.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   lezer: () => (/* binding */ lezer),\n/* harmony export */   lezerLanguage: () => (/* binding */ lezerLanguage)\n/* harmony export */ });\n/* harmony import */ var _lezer_lezer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/lezer */ \"(ssr)/./node_modules/.pnpm/@lezer+lezer@1.1.2/node_modules/@lezer/lezer/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n\n\n\n/**\nA language provider based on the [Lezer Lezer\nparser](https://github.com/lezer-parser/lezer-grammar), extended\nwith highlighting and indentation information.\n*/\nconst lezerLanguage = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LRLanguage.define({\n    name: \"lezer\",\n    parser: /*@__PURE__*/_lezer_lezer__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldNodeProp.add({\n                \"Body TokensBody SkipBody PrecedenceBody\": _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldInside\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"/*\", close: \"*/\" }, line: \"//\" },\n        indentOnInput: /^\\s*\\}$/\n    }\n});\n/**\nLanguage support for Lezer grammars.\n*/\nfunction lezer() {\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LanguageSupport(lezerLanguage);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-lezer@6.0.1/node_modules/@codemirror/lang-lezer/dist/index.js\n");

/***/ })

};
;