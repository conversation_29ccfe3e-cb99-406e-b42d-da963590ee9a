"use client";

import Link from "next/link";
import { MailCheck } from "lucide-react";

interface RegistrationSuccessViewProps {
  email: string;
  onBackToSignIn: () => void;
}

export function RegistrationSuccessView({ email, onBackToSignIn }: RegistrationSuccessViewProps) {
  return (
    <main className="flex items-center justify-center min-h-screen w-full bg-[#1a1a1a]">
      <div className="w-full max-w-md bg-[#1a1a1a] rounded-[6px] p-8">
        <div className="flex flex-col items-center text-center">
          <div className="bg-blue-900/20 rounded-full p-4 mb-6">
            <MailCheck className="h-12 w-12 text-blue-500" />
          </div>

          <h1 className="text-2xl font-medium text-white mb-4">
            Check your email
          </h1>

          <p className="text-base text-gray-400 mb-2">
            We've sent a confirmation link to:
          </p>

          <p className="text-lg font-medium mb-6 text-white">
            {email || "your email address"}
          </p>

          <div className="bg-blue-900/20 border border-blue-900/50 rounded-lg p-6 mb-8 w-full">
            <p className="text-sm text-blue-400">
              Click the link in the email to activate your account. If you don't see the email, check your spam folder.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 w-full">
            <Link
              href="/"
              className="flex h-12 items-center justify-center w-full text-center rounded-md border border-gray-700 bg-[#1a1a1a] hover:bg-gray-800 transition-all text-white"
            >
              Return to home
            </Link>
            <button
              onClick={onBackToSignIn}
              className="flex h-12 items-center justify-center w-full text-center rounded-md bg-blue-600 text-white hover:bg-blue-700 transition-all"
            >
              Back to sign in
            </button>
          </div>
        </div>
      </div>
    </main>
  );
}
