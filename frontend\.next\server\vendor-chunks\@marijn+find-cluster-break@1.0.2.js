"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@marijn+find-cluster-break@1.0.2";
exports.ids = ["vendor-chunks/@marijn+find-cluster-break@1.0.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@marijn+find-cluster-break@1.0.2/node_modules/@marijn/find-cluster-break/src/index.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@marijn+find-cluster-break@1.0.2/node_modules/@marijn/find-cluster-break/src/index.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findClusterBreak: () => (/* binding */ findClusterBreak),\n/* harmony export */   isExtendingChar: () => (/* binding */ isExtendingChar)\n/* harmony export */ });\n// These are filled with ranges (rangeFrom[i] up to but not including\n// rangeTo[i]) of code points that count as extending characters.\nlet rangeFrom = [], rangeTo = []\n\n;(() => {\n  // Compressed representation of the Grapheme_Cluster_Break=Extend\n  // information from\n  // http://www.unicode.org/Public/16.0.0/ucd/auxiliary/GraphemeBreakProperty.txt.\n  // Each pair of elements represents a range, as an offet from the\n  // previous range and a length. Numbers are in base-36, with the empty\n  // string being a shorthand for 1.\n  let numbers = \"lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o\".split(\",\").map(s => s ? parseInt(s, 36) : 1)\n  for (let i = 0, n = 0; i < numbers.length; i++)\n    (i % 2 ? rangeTo : rangeFrom).push(n = n + numbers[i])\n})()\n\nfunction isExtendingChar(code) {\n  if (code < 768) return false\n  for (let from = 0, to = rangeFrom.length;;) {\n    let mid = (from + to) >> 1\n    if (code < rangeFrom[mid]) to = mid\n    else if (code >= rangeTo[mid]) from = mid + 1\n    else return true\n    if (from == to) return false\n  }\n}\n\nfunction isRegionalIndicator(code) {\n  return code >= 0x1F1E6 && code <= 0x1F1FF\n}\n\nfunction check(code) {\n  for (let i = 0; i < rangeFrom.length; i++) {\n    if (rangeTo[i] > code) return rangeFrom[i] <= code\n  }\n  return false\n}\n\nconst ZWJ = 0x200d\n\nfunction findClusterBreak(str, pos, forward = true, includeExtending = true) {\n  return (forward ? nextClusterBreak : prevClusterBreak)(str, pos, includeExtending)\n}\n\nfunction nextClusterBreak(str, pos, includeExtending) {\n  if (pos == str.length) return pos\n  // If pos is in the middle of a surrogate pair, move to its start\n  if (pos && surrogateLow(str.charCodeAt(pos)) && surrogateHigh(str.charCodeAt(pos - 1))) pos--\n  let prev = codePointAt(str, pos)\n  pos += codePointSize(prev)\n  while (pos < str.length) {\n    let next = codePointAt(str, pos)\n    if (prev == ZWJ || next == ZWJ || includeExtending && isExtendingChar(next)) {\n      pos += codePointSize(next)\n      prev = next\n    } else if (isRegionalIndicator(next)) {\n      let countBefore = 0, i = pos - 2\n      while (i >= 0 && isRegionalIndicator(codePointAt(str, i))) { countBefore++; i -= 2 }\n      if (countBefore % 2 == 0) break\n      else pos += 2\n    } else {\n      break\n    }\n  }\n  return pos\n}\n\nfunction prevClusterBreak(str, pos, includeExtending) {\n  while (pos > 0) {\n    let found = nextClusterBreak(str, pos - 2, includeExtending)\n    if (found < pos) return found\n    pos--\n  }\n  return 0\n}\n\nfunction codePointAt(str, pos) {\n  let code0 = str.charCodeAt(pos)\n  if (!surrogateHigh(code0) || pos + 1 == str.length) return code0\n  let code1 = str.charCodeAt(pos + 1)\n  if (!surrogateLow(code1)) return code0\n  return ((code0 - 0xd800) << 10) + (code1 - 0xdc00) + 0x10000\n}\n\nfunction surrogateLow(ch) { return ch >= 0xDC00 && ch < 0xE000 }\nfunction surrogateHigh(ch) { return ch >= 0xD800 && ch < 0xDC00 }\nfunction codePointSize(code) { return code < 0x10000 ? 1 : 2 }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@marijn+find-cluster-break@1.0.2/node_modules/@marijn/find-cluster-break/src/index.js\n");

/***/ })

};
;