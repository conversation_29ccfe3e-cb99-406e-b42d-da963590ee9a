"use client";

import { ProjectCard } from "./ProjectCard";
import { Skeleton } from "@/components/ui/skeleton";

interface Project {
  id: string;
  name: string;
  description?: string;
  agentCount?: number;
  lastActivity?: string;
  active?: boolean;
}

interface ProjectsGridProps {
  projects: Project[];
  loading: boolean;
}

export function ProjectsGrid({ projects, loading }: ProjectsGridProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Skeleton key={i} className="h-[180px] w-full rounded-md" />
        ))}
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center border border-dashed rounded-md bg-card/50">
        <h3 className="text-xl font-medium mb-3">No projects yet</h3>
        <p className="text-muted-foreground mb-8 max-w-md">
          Create your first project to get started with your AI team
        </p>
        <ProjectCard isCreateCard />
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <ProjectCard isCreateCard />
      <ProjectCard />
      {projects.map((project) => (
        <ProjectCard key={project.id} project={project} />
      ))}
    </div>
  );
}
