"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@codemirror+lang-html@6.4.9";
exports.ids = ["vendor-chunks/@codemirror+lang-html@6.4.9"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@codemirror+lang-html@6.4.9/node_modules/@codemirror/lang-html/dist/index.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@codemirror+lang-html@6.4.9/node_modules/@codemirror/lang-html/dist/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autoCloseTags: () => (/* binding */ autoCloseTags),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   htmlCompletionSource: () => (/* binding */ htmlCompletionSource),\n/* harmony export */   htmlCompletionSourceWith: () => (/* binding */ htmlCompletionSourceWith),\n/* harmony export */   htmlLanguage: () => (/* binding */ htmlLanguage),\n/* harmony export */   htmlPlain: () => (/* binding */ htmlPlain)\n/* harmony export */ });\n/* harmony import */ var _lezer_html__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lezer/html */ \"(ssr)/./node_modules/.pnpm/@lezer+html@1.3.10/node_modules/@lezer/html/dist/index.js\");\n/* harmony import */ var _codemirror_lang_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @codemirror/lang-css */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-css@6.3.1/node_modules/@codemirror/lang-css/dist/index.js\");\n/* harmony import */ var _codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @codemirror/lang-javascript */ \"(ssr)/./node_modules/.pnpm/@codemirror+lang-javascript@6.2.3/node_modules/@codemirror/lang-javascript/dist/index.js\");\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @codemirror/state */ \"(ssr)/./node_modules/.pnpm/@codemirror+state@6.5.2/node_modules/@codemirror/state/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n\n\n\n\n\n\n\nconst Targets = [\"_blank\", \"_self\", \"_top\", \"_parent\"];\nconst Charsets = [\"ascii\", \"utf-8\", \"utf-16\", \"latin1\", \"latin1\"];\nconst Methods = [\"get\", \"post\", \"put\", \"delete\"];\nconst Encs = [\"application/x-www-form-urlencoded\", \"multipart/form-data\", \"text/plain\"];\nconst Bool = [\"true\", \"false\"];\nconst S = {}; // Empty tag spec\nconst Tags = {\n    a: {\n        attrs: {\n            href: null, ping: null, type: null,\n            media: null,\n            target: Targets,\n            hreflang: null\n        }\n    },\n    abbr: S,\n    address: S,\n    area: {\n        attrs: {\n            alt: null, coords: null, href: null, target: null, ping: null,\n            media: null, hreflang: null, type: null,\n            shape: [\"default\", \"rect\", \"circle\", \"poly\"]\n        }\n    },\n    article: S,\n    aside: S,\n    audio: {\n        attrs: {\n            src: null, mediagroup: null,\n            crossorigin: [\"anonymous\", \"use-credentials\"],\n            preload: [\"none\", \"metadata\", \"auto\"],\n            autoplay: [\"autoplay\"],\n            loop: [\"loop\"],\n            controls: [\"controls\"]\n        }\n    },\n    b: S,\n    base: { attrs: { href: null, target: Targets } },\n    bdi: S,\n    bdo: S,\n    blockquote: { attrs: { cite: null } },\n    body: S,\n    br: S,\n    button: {\n        attrs: {\n            form: null, formaction: null, name: null, value: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"autofocus\"],\n            formenctype: Encs,\n            formmethod: Methods,\n            formnovalidate: [\"novalidate\"],\n            formtarget: Targets,\n            type: [\"submit\", \"reset\", \"button\"]\n        }\n    },\n    canvas: { attrs: { width: null, height: null } },\n    caption: S,\n    center: S,\n    cite: S,\n    code: S,\n    col: { attrs: { span: null } },\n    colgroup: { attrs: { span: null } },\n    command: {\n        attrs: {\n            type: [\"command\", \"checkbox\", \"radio\"],\n            label: null, icon: null, radiogroup: null, command: null, title: null,\n            disabled: [\"disabled\"],\n            checked: [\"checked\"]\n        }\n    },\n    data: { attrs: { value: null } },\n    datagrid: { attrs: { disabled: [\"disabled\"], multiple: [\"multiple\"] } },\n    datalist: { attrs: { data: null } },\n    dd: S,\n    del: { attrs: { cite: null, datetime: null } },\n    details: { attrs: { open: [\"open\"] } },\n    dfn: S,\n    div: S,\n    dl: S,\n    dt: S,\n    em: S,\n    embed: { attrs: { src: null, type: null, width: null, height: null } },\n    eventsource: { attrs: { src: null } },\n    fieldset: { attrs: { disabled: [\"disabled\"], form: null, name: null } },\n    figcaption: S,\n    figure: S,\n    footer: S,\n    form: {\n        attrs: {\n            action: null, name: null,\n            \"accept-charset\": Charsets,\n            autocomplete: [\"on\", \"off\"],\n            enctype: Encs,\n            method: Methods,\n            novalidate: [\"novalidate\"],\n            target: Targets\n        }\n    },\n    h1: S, h2: S, h3: S, h4: S, h5: S, h6: S,\n    head: {\n        children: [\"title\", \"base\", \"link\", \"style\", \"meta\", \"script\", \"noscript\", \"command\"]\n    },\n    header: S,\n    hgroup: S,\n    hr: S,\n    html: {\n        attrs: { manifest: null }\n    },\n    i: S,\n    iframe: {\n        attrs: {\n            src: null, srcdoc: null, name: null, width: null, height: null,\n            sandbox: [\"allow-top-navigation\", \"allow-same-origin\", \"allow-forms\", \"allow-scripts\"],\n            seamless: [\"seamless\"]\n        }\n    },\n    img: {\n        attrs: {\n            alt: null, src: null, ismap: null, usemap: null, width: null, height: null,\n            crossorigin: [\"anonymous\", \"use-credentials\"]\n        }\n    },\n    input: {\n        attrs: {\n            alt: null, dirname: null, form: null, formaction: null,\n            height: null, list: null, max: null, maxlength: null, min: null,\n            name: null, pattern: null, placeholder: null, size: null, src: null,\n            step: null, value: null, width: null,\n            accept: [\"audio/*\", \"video/*\", \"image/*\"],\n            autocomplete: [\"on\", \"off\"],\n            autofocus: [\"autofocus\"],\n            checked: [\"checked\"],\n            disabled: [\"disabled\"],\n            formenctype: Encs,\n            formmethod: Methods,\n            formnovalidate: [\"novalidate\"],\n            formtarget: Targets,\n            multiple: [\"multiple\"],\n            readonly: [\"readonly\"],\n            required: [\"required\"],\n            type: [\"hidden\", \"text\", \"search\", \"tel\", \"url\", \"email\", \"password\", \"datetime\", \"date\", \"month\",\n                \"week\", \"time\", \"datetime-local\", \"number\", \"range\", \"color\", \"checkbox\", \"radio\",\n                \"file\", \"submit\", \"image\", \"reset\", \"button\"]\n        }\n    },\n    ins: { attrs: { cite: null, datetime: null } },\n    kbd: S,\n    keygen: {\n        attrs: {\n            challenge: null, form: null, name: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"disabled\"],\n            keytype: [\"RSA\"]\n        }\n    },\n    label: { attrs: { for: null, form: null } },\n    legend: S,\n    li: { attrs: { value: null } },\n    link: {\n        attrs: {\n            href: null, type: null,\n            hreflang: null,\n            media: null,\n            sizes: [\"all\", \"16x16\", \"16x16 32x32\", \"16x16 32x32 64x64\"]\n        }\n    },\n    map: { attrs: { name: null } },\n    mark: S,\n    menu: { attrs: { label: null, type: [\"list\", \"context\", \"toolbar\"] } },\n    meta: {\n        attrs: {\n            content: null,\n            charset: Charsets,\n            name: [\"viewport\", \"application-name\", \"author\", \"description\", \"generator\", \"keywords\"],\n            \"http-equiv\": [\"content-language\", \"content-type\", \"default-style\", \"refresh\"]\n        }\n    },\n    meter: { attrs: { value: null, min: null, low: null, high: null, max: null, optimum: null } },\n    nav: S,\n    noscript: S,\n    object: {\n        attrs: {\n            data: null, type: null, name: null, usemap: null, form: null, width: null, height: null,\n            typemustmatch: [\"typemustmatch\"]\n        }\n    },\n    ol: { attrs: { reversed: [\"reversed\"], start: null, type: [\"1\", \"a\", \"A\", \"i\", \"I\"] },\n        children: [\"li\", \"script\", \"template\", \"ul\", \"ol\"] },\n    optgroup: { attrs: { disabled: [\"disabled\"], label: null } },\n    option: { attrs: { disabled: [\"disabled\"], label: null, selected: [\"selected\"], value: null } },\n    output: { attrs: { for: null, form: null, name: null } },\n    p: S,\n    param: { attrs: { name: null, value: null } },\n    pre: S,\n    progress: { attrs: { value: null, max: null } },\n    q: { attrs: { cite: null } },\n    rp: S,\n    rt: S,\n    ruby: S,\n    samp: S,\n    script: {\n        attrs: {\n            type: [\"text/javascript\"],\n            src: null,\n            async: [\"async\"],\n            defer: [\"defer\"],\n            charset: Charsets\n        }\n    },\n    section: S,\n    select: {\n        attrs: {\n            form: null, name: null, size: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"disabled\"],\n            multiple: [\"multiple\"]\n        }\n    },\n    slot: { attrs: { name: null } },\n    small: S,\n    source: { attrs: { src: null, type: null, media: null } },\n    span: S,\n    strong: S,\n    style: {\n        attrs: {\n            type: [\"text/css\"],\n            media: null,\n            scoped: null\n        }\n    },\n    sub: S,\n    summary: S,\n    sup: S,\n    table: S,\n    tbody: S,\n    td: { attrs: { colspan: null, rowspan: null, headers: null } },\n    template: S,\n    textarea: {\n        attrs: {\n            dirname: null, form: null, maxlength: null, name: null, placeholder: null,\n            rows: null, cols: null,\n            autofocus: [\"autofocus\"],\n            disabled: [\"disabled\"],\n            readonly: [\"readonly\"],\n            required: [\"required\"],\n            wrap: [\"soft\", \"hard\"]\n        }\n    },\n    tfoot: S,\n    th: { attrs: { colspan: null, rowspan: null, headers: null, scope: [\"row\", \"col\", \"rowgroup\", \"colgroup\"] } },\n    thead: S,\n    time: { attrs: { datetime: null } },\n    title: S,\n    tr: S,\n    track: {\n        attrs: {\n            src: null, label: null, default: null,\n            kind: [\"subtitles\", \"captions\", \"descriptions\", \"chapters\", \"metadata\"],\n            srclang: null\n        }\n    },\n    ul: { children: [\"li\", \"script\", \"template\", \"ul\", \"ol\"] },\n    var: S,\n    video: {\n        attrs: {\n            src: null, poster: null, width: null, height: null,\n            crossorigin: [\"anonymous\", \"use-credentials\"],\n            preload: [\"auto\", \"metadata\", \"none\"],\n            autoplay: [\"autoplay\"],\n            mediagroup: [\"movie\"],\n            muted: [\"muted\"],\n            controls: [\"controls\"]\n        }\n    },\n    wbr: S\n};\nconst GlobalAttrs = {\n    accesskey: null,\n    class: null,\n    contenteditable: Bool,\n    contextmenu: null,\n    dir: [\"ltr\", \"rtl\", \"auto\"],\n    draggable: [\"true\", \"false\", \"auto\"],\n    dropzone: [\"copy\", \"move\", \"link\", \"string:\", \"file:\"],\n    hidden: [\"hidden\"],\n    id: null,\n    inert: [\"inert\"],\n    itemid: null,\n    itemprop: null,\n    itemref: null,\n    itemscope: [\"itemscope\"],\n    itemtype: null,\n    lang: [\"ar\", \"bn\", \"de\", \"en-GB\", \"en-US\", \"es\", \"fr\", \"hi\", \"id\", \"ja\", \"pa\", \"pt\", \"ru\", \"tr\", \"zh\"],\n    spellcheck: Bool,\n    autocorrect: Bool,\n    autocapitalize: Bool,\n    style: null,\n    tabindex: null,\n    title: null,\n    translate: [\"yes\", \"no\"],\n    rel: [\"stylesheet\", \"alternate\", \"author\", \"bookmark\", \"help\", \"license\", \"next\", \"nofollow\", \"noreferrer\", \"prefetch\", \"prev\", \"search\", \"tag\"],\n    role: /*@__PURE__*/\"alert application article banner button cell checkbox complementary contentinfo dialog document feed figure form grid gridcell heading img list listbox listitem main navigation region row rowgroup search switch tab table tabpanel textbox timer\".split(\" \"),\n    \"aria-activedescendant\": null,\n    \"aria-atomic\": Bool,\n    \"aria-autocomplete\": [\"inline\", \"list\", \"both\", \"none\"],\n    \"aria-busy\": Bool,\n    \"aria-checked\": [\"true\", \"false\", \"mixed\", \"undefined\"],\n    \"aria-controls\": null,\n    \"aria-describedby\": null,\n    \"aria-disabled\": Bool,\n    \"aria-dropeffect\": null,\n    \"aria-expanded\": [\"true\", \"false\", \"undefined\"],\n    \"aria-flowto\": null,\n    \"aria-grabbed\": [\"true\", \"false\", \"undefined\"],\n    \"aria-haspopup\": Bool,\n    \"aria-hidden\": Bool,\n    \"aria-invalid\": [\"true\", \"false\", \"grammar\", \"spelling\"],\n    \"aria-label\": null,\n    \"aria-labelledby\": null,\n    \"aria-level\": null,\n    \"aria-live\": [\"off\", \"polite\", \"assertive\"],\n    \"aria-multiline\": Bool,\n    \"aria-multiselectable\": Bool,\n    \"aria-owns\": null,\n    \"aria-posinset\": null,\n    \"aria-pressed\": [\"true\", \"false\", \"mixed\", \"undefined\"],\n    \"aria-readonly\": Bool,\n    \"aria-relevant\": null,\n    \"aria-required\": Bool,\n    \"aria-selected\": [\"true\", \"false\", \"undefined\"],\n    \"aria-setsize\": null,\n    \"aria-sort\": [\"ascending\", \"descending\", \"none\", \"other\"],\n    \"aria-valuemax\": null,\n    \"aria-valuemin\": null,\n    \"aria-valuenow\": null,\n    \"aria-valuetext\": null\n};\nconst eventAttributes = /*@__PURE__*/(\"beforeunload copy cut dragstart dragover dragleave dragenter dragend \" +\n    \"drag paste focus blur change click load mousedown mouseenter mouseleave \" +\n    \"mouseup keydown keyup resize scroll unload\").split(\" \").map(n => \"on\" + n);\nfor (let a of eventAttributes)\n    GlobalAttrs[a] = null;\nclass Schema {\n    constructor(extraTags, extraAttrs) {\n        this.tags = Object.assign(Object.assign({}, Tags), extraTags);\n        this.globalAttrs = Object.assign(Object.assign({}, GlobalAttrs), extraAttrs);\n        this.allTags = Object.keys(this.tags);\n        this.globalAttrNames = Object.keys(this.globalAttrs);\n    }\n}\nSchema.default = /*@__PURE__*/new Schema;\nfunction elementName(doc, tree, max = doc.length) {\n    if (!tree)\n        return \"\";\n    let tag = tree.firstChild;\n    let name = tag && tag.getChild(\"TagName\");\n    return name ? doc.sliceString(name.from, Math.min(name.to, max)) : \"\";\n}\nfunction findParentElement(tree, skip = false) {\n    for (; tree; tree = tree.parent)\n        if (tree.name == \"Element\") {\n            if (skip)\n                skip = false;\n            else\n                return tree;\n        }\n    return null;\n}\nfunction allowedChildren(doc, tree, schema) {\n    let parentInfo = schema.tags[elementName(doc, findParentElement(tree))];\n    return (parentInfo === null || parentInfo === void 0 ? void 0 : parentInfo.children) || schema.allTags;\n}\nfunction openTags(doc, tree) {\n    let open = [];\n    for (let parent = findParentElement(tree); parent && !parent.type.isTop; parent = findParentElement(parent.parent)) {\n        let tagName = elementName(doc, parent);\n        if (tagName && parent.lastChild.name == \"CloseTag\")\n            break;\n        if (tagName && open.indexOf(tagName) < 0 && (tree.name == \"EndTag\" || tree.from >= parent.firstChild.to))\n            open.push(tagName);\n    }\n    return open;\n}\nconst identifier = /^[:\\-\\.\\w\\u00b7-\\uffff]*$/;\nfunction completeTag(state, schema, tree, from, to) {\n    let end = /\\s*>/.test(state.sliceDoc(to, to + 5)) ? \"\" : \">\";\n    let parent = findParentElement(tree, true);\n    return { from, to,\n        options: allowedChildren(state.doc, parent, schema).map(tagName => ({ label: tagName, type: \"type\" })).concat(openTags(state.doc, tree).map((tag, i) => ({ label: \"/\" + tag, apply: \"/\" + tag + end,\n            type: \"type\", boost: 99 - i }))),\n        validFor: /^\\/?[:\\-\\.\\w\\u00b7-\\uffff]*$/ };\n}\nfunction completeCloseTag(state, tree, from, to) {\n    let end = /\\s*>/.test(state.sliceDoc(to, to + 5)) ? \"\" : \">\";\n    return { from, to,\n        options: openTags(state.doc, tree).map((tag, i) => ({ label: tag, apply: tag + end, type: \"type\", boost: 99 - i })),\n        validFor: identifier };\n}\nfunction completeStartTag(state, schema, tree, pos) {\n    let options = [], level = 0;\n    for (let tagName of allowedChildren(state.doc, tree, schema))\n        options.push({ label: \"<\" + tagName, type: \"type\" });\n    for (let open of openTags(state.doc, tree))\n        options.push({ label: \"</\" + open + \">\", type: \"type\", boost: 99 - level++ });\n    return { from: pos, to: pos, options, validFor: /^<\\/?[:\\-\\.\\w\\u00b7-\\uffff]*$/ };\n}\nfunction completeAttrName(state, schema, tree, from, to) {\n    let elt = findParentElement(tree), info = elt ? schema.tags[elementName(state.doc, elt)] : null;\n    let localAttrs = info && info.attrs ? Object.keys(info.attrs) : [];\n    let names = info && info.globalAttrs === false ? localAttrs\n        : localAttrs.length ? localAttrs.concat(schema.globalAttrNames) : schema.globalAttrNames;\n    return { from, to,\n        options: names.map(attrName => ({ label: attrName, type: \"property\" })),\n        validFor: identifier };\n}\nfunction completeAttrValue(state, schema, tree, from, to) {\n    var _a;\n    let nameNode = (_a = tree.parent) === null || _a === void 0 ? void 0 : _a.getChild(\"AttributeName\");\n    let options = [], token = undefined;\n    if (nameNode) {\n        let attrName = state.sliceDoc(nameNode.from, nameNode.to);\n        let attrs = schema.globalAttrs[attrName];\n        if (!attrs) {\n            let elt = findParentElement(tree), info = elt ? schema.tags[elementName(state.doc, elt)] : null;\n            attrs = (info === null || info === void 0 ? void 0 : info.attrs) && info.attrs[attrName];\n        }\n        if (attrs) {\n            let base = state.sliceDoc(from, to).toLowerCase(), quoteStart = '\"', quoteEnd = '\"';\n            if (/^['\"]/.test(base)) {\n                token = base[0] == '\"' ? /^[^\"]*$/ : /^[^']*$/;\n                quoteStart = \"\";\n                quoteEnd = state.sliceDoc(to, to + 1) == base[0] ? \"\" : base[0];\n                base = base.slice(1);\n                from++;\n            }\n            else {\n                token = /^[^\\s<>='\"]*$/;\n            }\n            for (let value of attrs)\n                options.push({ label: value, apply: quoteStart + value + quoteEnd, type: \"constant\" });\n        }\n    }\n    return { from, to, options, validFor: token };\n}\nfunction htmlCompletionFor(schema, context) {\n    let { state, pos } = context, tree = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.syntaxTree)(state).resolveInner(pos, -1), around = tree.resolve(pos);\n    for (let scan = pos, before; around == tree && (before = tree.childBefore(scan));) {\n        let last = before.lastChild;\n        if (!last || !last.type.isError || last.from < last.to)\n            break;\n        around = tree = before;\n        scan = last.from;\n    }\n    if (tree.name == \"TagName\") {\n        return tree.parent && /CloseTag$/.test(tree.parent.name) ? completeCloseTag(state, tree, tree.from, pos)\n            : completeTag(state, schema, tree, tree.from, pos);\n    }\n    else if (tree.name == \"StartTag\") {\n        return completeTag(state, schema, tree, pos, pos);\n    }\n    else if (tree.name == \"StartCloseTag\" || tree.name == \"IncompleteCloseTag\") {\n        return completeCloseTag(state, tree, pos, pos);\n    }\n    else if (tree.name == \"OpenTag\" || tree.name == \"SelfClosingTag\" || tree.name == \"AttributeName\") {\n        return completeAttrName(state, schema, tree, tree.name == \"AttributeName\" ? tree.from : pos, pos);\n    }\n    else if (tree.name == \"Is\" || tree.name == \"AttributeValue\" || tree.name == \"UnquotedAttributeValue\") {\n        return completeAttrValue(state, schema, tree, tree.name == \"Is\" ? pos : tree.from, pos);\n    }\n    else if (context.explicit && (around.name == \"Element\" || around.name == \"Text\" || around.name == \"Document\")) {\n        return completeStartTag(state, schema, tree, pos);\n    }\n    else {\n        return null;\n    }\n}\n/**\nHTML tag completion. Opens and closes tags and attributes in a\ncontext-aware way.\n*/\nfunction htmlCompletionSource(context) {\n    return htmlCompletionFor(Schema.default, context);\n}\n/**\nCreate a completion source for HTML extended with additional tags\nor attributes.\n*/\nfunction htmlCompletionSourceWith(config) {\n    let { extraTags, extraGlobalAttributes: extraAttrs } = config;\n    let schema = extraAttrs || extraTags ? new Schema(extraTags, extraAttrs) : Schema.default;\n    return (context) => htmlCompletionFor(schema, context);\n}\n\nconst jsonParser = /*@__PURE__*/_codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_2__.javascriptLanguage.parser.configure({ top: \"SingleExpression\" });\nconst defaultNesting = [\n    { tag: \"script\",\n        attrs: attrs => attrs.type == \"text/typescript\" || attrs.lang == \"ts\",\n        parser: _codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_2__.typescriptLanguage.parser },\n    { tag: \"script\",\n        attrs: attrs => attrs.type == \"text/babel\" || attrs.type == \"text/jsx\",\n        parser: _codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_2__.jsxLanguage.parser },\n    { tag: \"script\",\n        attrs: attrs => attrs.type == \"text/typescript-jsx\",\n        parser: _codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_2__.tsxLanguage.parser },\n    { tag: \"script\",\n        attrs(attrs) {\n            return /^(importmap|speculationrules|application\\/(.+\\+)?json)$/i.test(attrs.type);\n        },\n        parser: jsonParser },\n    { tag: \"script\",\n        attrs(attrs) {\n            return !attrs.type || /^(?:text|application)\\/(?:x-)?(?:java|ecma)script$|^module$|^$/i.test(attrs.type);\n        },\n        parser: _codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_2__.javascriptLanguage.parser },\n    { tag: \"style\",\n        attrs(attrs) {\n            return (!attrs.lang || attrs.lang == \"css\") && (!attrs.type || /^(text\\/)?(x-)?(stylesheet|css)$/i.test(attrs.type));\n        },\n        parser: _codemirror_lang_css__WEBPACK_IMPORTED_MODULE_3__.cssLanguage.parser }\n];\nconst defaultAttrs = /*@__PURE__*/[\n    { name: \"style\",\n        parser: /*@__PURE__*/_codemirror_lang_css__WEBPACK_IMPORTED_MODULE_3__.cssLanguage.parser.configure({ top: \"Styles\" }) }\n].concat(/*@__PURE__*/eventAttributes.map(name => ({ name, parser: _codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_2__.javascriptLanguage.parser })));\n/**\nA language provider based on the [Lezer HTML\nparser](https://github.com/lezer-parser/html), extended with the\nJavaScript and CSS parsers to parse the content of `<script>` and\n`<style>` tags.\n*/\nconst htmlPlain = /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LRLanguage.define({\n    name: \"html\",\n    parser: /*@__PURE__*/_lezer_html__WEBPACK_IMPORTED_MODULE_0__.parser.configure({\n        props: [\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.indentNodeProp.add({\n                Element(context) {\n                    let after = /^(\\s*)(<\\/)?/.exec(context.textAfter);\n                    if (context.node.to <= context.pos + after[0].length)\n                        return context.continue();\n                    return context.lineIndent(context.node.from) + (after[2] ? 0 : context.unit);\n                },\n                \"OpenTag CloseTag SelfClosingTag\"(context) {\n                    return context.column(context.node.from) + context.unit;\n                },\n                Document(context) {\n                    if (context.pos + /\\s*/.exec(context.textAfter)[0].length < context.node.to)\n                        return context.continue();\n                    let endElt = null, close;\n                    for (let cur = context.node;;) {\n                        let last = cur.lastChild;\n                        if (!last || last.name != \"Element\" || last.to != cur.to)\n                            break;\n                        endElt = cur = last;\n                    }\n                    if (endElt && !((close = endElt.lastChild) && (close.name == \"CloseTag\" || close.name == \"SelfClosingTag\")))\n                        return context.lineIndent(endElt.from) + context.unit;\n                    return null;\n                }\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.foldNodeProp.add({\n                Element(node) {\n                    let first = node.firstChild, last = node.lastChild;\n                    if (!first || first.name != \"OpenTag\")\n                        return null;\n                    return { from: first.to, to: last.name == \"CloseTag\" ? last.from : node.to };\n                }\n            }),\n            /*@__PURE__*/_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.bracketMatchingHandle.add({\n                \"OpenTag CloseTag\": node => node.getChild(\"TagName\")\n            })\n        ]\n    }),\n    languageData: {\n        commentTokens: { block: { open: \"<!--\", close: \"-->\" } },\n        indentOnInput: /^\\s*<\\/\\w+\\W$/,\n        wordChars: \"-._\"\n    }\n});\n/**\nA language provider based on the [Lezer HTML\nparser](https://github.com/lezer-parser/html), extended with the\nJavaScript and CSS parsers to parse the content of `<script>` and\n`<style>` tags.\n*/\nconst htmlLanguage = /*@__PURE__*/htmlPlain.configure({\n    wrap: /*@__PURE__*/(0,_lezer_html__WEBPACK_IMPORTED_MODULE_0__.configureNesting)(defaultNesting, defaultAttrs)\n});\n/**\nLanguage support for HTML, including\n[`htmlCompletion`](https://codemirror.net/6/docs/ref/#lang-html.htmlCompletion) and JavaScript and\nCSS support extensions.\n*/\nfunction html(config = {}) {\n    let dialect = \"\", wrap;\n    if (config.matchClosingTags === false)\n        dialect = \"noMatch\";\n    if (config.selfClosingTags === true)\n        dialect = (dialect ? dialect + \" \" : \"\") + \"selfClosing\";\n    if (config.nestedLanguages && config.nestedLanguages.length ||\n        config.nestedAttributes && config.nestedAttributes.length)\n        wrap = (0,_lezer_html__WEBPACK_IMPORTED_MODULE_0__.configureNesting)((config.nestedLanguages || []).concat(defaultNesting), (config.nestedAttributes || []).concat(defaultAttrs));\n    let lang = wrap ? htmlPlain.configure({ wrap, dialect }) : dialect ? htmlLanguage.configure({ dialect }) : htmlLanguage;\n    return new _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.LanguageSupport(lang, [\n        htmlLanguage.data.of({ autocomplete: htmlCompletionSourceWith(config) }),\n        config.autoCloseTags !== false ? autoCloseTags : [],\n        (0,_codemirror_lang_javascript__WEBPACK_IMPORTED_MODULE_2__.javascript)().support,\n        (0,_codemirror_lang_css__WEBPACK_IMPORTED_MODULE_3__.css)().support\n    ]);\n}\nconst selfClosers = /*@__PURE__*/new Set(/*@__PURE__*/\"area base br col command embed frame hr img input keygen link meta param source track wbr menuitem\".split(\" \"));\n/**\nExtension that will automatically insert close tags when a `>` or\n`/` is typed.\n*/\nconst autoCloseTags = /*@__PURE__*/_codemirror_view__WEBPACK_IMPORTED_MODULE_4__.EditorView.inputHandler.of((view, from, to, text, insertTransaction) => {\n    if (view.composing || view.state.readOnly || from != to || (text != \">\" && text != \"/\") ||\n        !htmlLanguage.isActiveAt(view.state, from, -1))\n        return false;\n    let base = insertTransaction(), { state } = base;\n    let closeTags = state.changeByRange(range => {\n        var _a, _b, _c;\n        let didType = state.doc.sliceString(range.from - 1, range.to) == text;\n        let { head } = range, after = (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.syntaxTree)(state).resolveInner(head, -1), name;\n        if (didType && text == \">\" && after.name == \"EndTag\") {\n            let tag = after.parent;\n            if (((_b = (_a = tag.parent) === null || _a === void 0 ? void 0 : _a.lastChild) === null || _b === void 0 ? void 0 : _b.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, tag.parent, head)) &&\n                !selfClosers.has(name)) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `</${name}>`;\n                return { range, changes: { from: head, to, insert } };\n            }\n        }\n        else if (didType && text == \"/\" && after.name == \"IncompleteCloseTag\") {\n            let tag = after.parent;\n            if (after.from == head - 2 && ((_c = tag.lastChild) === null || _c === void 0 ? void 0 : _c.name) != \"CloseTag\" &&\n                (name = elementName(state.doc, tag, head)) && !selfClosers.has(name)) {\n                let to = head + (state.doc.sliceString(head, head + 1) === \">\" ? 1 : 0);\n                let insert = `${name}>`;\n                return {\n                    range: _codemirror_state__WEBPACK_IMPORTED_MODULE_5__.EditorSelection.cursor(head + insert.length, -1),\n                    changes: { from: head, to, insert }\n                };\n            }\n        }\n        return { range };\n    });\n    if (closeTags.changes.empty)\n        return false;\n    view.dispatch([\n        base,\n        state.update(closeTags, {\n            userEvent: \"input.complete\",\n            scrollIntoView: true\n        })\n    ]);\n    return true;\n});\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@codemirror+lang-html@6.4.9/node_modules/@codemirror/lang-html/dist/index.js\n");

/***/ })

};
;