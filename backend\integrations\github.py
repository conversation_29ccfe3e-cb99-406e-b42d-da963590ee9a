from typing import Dict, Any, Optional, List, Union
import asyncio
import json
import httpx
from datetime import datetime

from utils.logger import logger
from integrations.base import BaseIntegration, IntegrationType, IntegrationStatus
from integrations import registry


class GitHubIntegration(BaseIntegration):
    name = "github"
    display_name = "GitHub"
    description = "Integration with GitHub for repository management and code collaboration"
    
    def __init__(self, 
                 integration_id: str, 
                 account_id: str, 
                 integration_type: IntegrationType = IntegrationType.COMPANY,
                 config: Optional[Dict[str, Any]] = None):
        super().__init__(integration_id, account_id, integration_type, config)
        self.api_base_url = "https://api.github.com"
        self.access_token = self.config.get("access_token")
        self.headers = {
            "Accept": "application/vnd.github.v3+json",
        }
        
        if self.access_token:
            self.headers["Authorization"] = f"token {self.access_token}"
    
    async def initialize(self) -> bool:
        if not self.access_token:
            self.last_error = "GitHub access token is required"
            self.status = IntegrationStatus.ERROR
            return False
            
        try:
            # Validate the token by making a test API call
            valid = await self.validate_credentials()
            if valid:
                self.status = IntegrationStatus.ACTIVE
                self.last_sync = datetime.now().isoformat()
                return True
            else:
                self.status = IntegrationStatus.ERROR
                self.last_error = "Invalid GitHub credentials"
                return False
        except Exception as e:
            self.status = IntegrationStatus.ERROR
            self.last_error = str(e)
            logger.error(f"Error initializing GitHub integration: {str(e)}")
            return False
    
    async def validate_credentials(self) -> bool:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_base_url}/user", 
                    headers=self.headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    return True
                else:
                    self.last_error = f"GitHub API error: {response.status_code} - {response.text}"
                    return False
        except Exception as e:
            self.last_error = f"Error validating GitHub credentials: {str(e)}"
            return False
    
    async def get_capabilities(self) -> List[str]:
        return [
            "list_repositories",
            "get_repository",
            "create_repository",
            "create_issue",
            "list_issues",
            "create_pull_request",
            "list_pull_requests",
            "get_repository_contents",
            "create_webhook"
        ]
    
    async def execute_action(self, action: str, params: Dict[str, Any]) -> Dict[str, Any]:
        if action == "list_repositories":
            return await self._list_repositories(params)
        elif action == "get_repository":
            return await self._get_repository(params)
        elif action == "create_repository":
            return await self._create_repository(params)
        elif action == "create_issue":
            return await self._create_issue(params)
        elif action == "list_issues":
            return await self._list_issues(params)
        elif action == "create_pull_request":
            return await self._create_pull_request(params)
        elif action == "list_pull_requests":
            return await self._list_pull_requests(params)
        elif action == "get_repository_contents":
            return await self._get_repository_contents(params)
        elif action == "create_webhook":
            return await self._create_webhook(params)
        else:
            return {"success": False, "error": f"Action {action} not implemented for GitHub integration"}
    
    async def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None, data: Optional[Dict] = None) -> Dict:
        try:
            url = f"{self.api_base_url}{endpoint}" if endpoint.startswith("/") else f"{self.api_base_url}/{endpoint}"
            
            async with httpx.AsyncClient() as client:
                if method.upper() == "GET":
                    response = await client.get(url, headers=self.headers, params=params, timeout=30)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=self.headers, json=data, timeout=30)
                elif method.upper() == "PATCH":
                    response = await client.patch(url, headers=self.headers, json=data, timeout=30)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=self.headers, timeout=30)
                else:
                    return {"success": False, "error": f"Unsupported method: {method}"}
                
                if response.status_code >= 200 and response.status_code < 300:
                    return {"success": True, "data": response.json() if response.text else {}}
                else:
                    return {
                        "success": False, 
                        "error": f"GitHub API error: {response.status_code}", 
                        "details": response.text
                    }
                
        except Exception as e:
            logger.error(f"GitHub request error: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _list_repositories(self, params: Dict[str, Any]) -> Dict[str, Any]:
        org = params.get("org")
        user = params.get("user")
        per_page = params.get("per_page", 30)
        page = params.get("page", 1)
        
        endpoint = "user/repos"
        if org:
            endpoint = f"orgs/{org}/repos"
        elif user:
            endpoint = f"users/{user}/repos"
            
        query_params = {
            "per_page": per_page,
            "page": page,
            "sort": params.get("sort", "updated"),
            "direction": params.get("direction", "desc")
        }
        
        result = await self._make_request("GET", endpoint, params=query_params)
        
        if result["success"]:
            repos = []
            for repo in result["data"]:
                repos.append({
                    "id": repo.get("id"),
                    "name": repo.get("name"),
                    "full_name": repo.get("full_name"),
                    "description": repo.get("description"),
                    "html_url": repo.get("html_url"),
                    "language": repo.get("language"),
                    "stargazers_count": repo.get("stargazers_count"),
                    "forks_count": repo.get("forks_count"),
                    "open_issues_count": repo.get("open_issues_count"),
                    "created_at": repo.get("created_at"),
                    "updated_at": repo.get("updated_at"),
                    "private": repo.get("private", False),
                    "owner": {
                        "login": repo.get("owner", {}).get("login"),
                        "avatar_url": repo.get("owner", {}).get("avatar_url"),
                        "html_url": repo.get("owner", {}).get("html_url")
                    }
                })
            
            return {"success": True, "repositories": repos}
        else:
            return result
    
    async def _get_repository(self, params: Dict[str, Any]) -> Dict[str, Any]:
        owner = params.get("owner")
        repo = params.get("repo")
        
        if not owner or not repo:
            return {"success": False, "error": "Owner and repo parameters are required"}
            
        endpoint = f"repos/{owner}/{repo}"
        
        return await self._make_request("GET", endpoint)
    
    async def _create_repository(self, params: Dict[str, Any]) -> Dict[str, Any]:
        name = params.get("name")
        
        if not name:
            return {"success": False, "error": "Repository name is required"}
            
        data = {
            "name": name,
            "description": params.get("description", ""),
            "private": params.get("private", False),
            "auto_init": params.get("auto_init", True),
            "gitignore_template": params.get("gitignore_template"),
            "license_template": params.get("license_template")
        }
        
        org = params.get("org")
        endpoint = "user/repos"
        
        if org:
            endpoint = f"orgs/{org}/repos"
            
        return await self._make_request("POST", endpoint, data=data)
    
    async def _create_issue(self, params: Dict[str, Any]) -> Dict[str, Any]:
        owner = params.get("owner")
        repo = params.get("repo")
        title = params.get("title")
        
        if not owner or not repo or not title:
            return {"success": False, "error": "Owner, repo, and title parameters are required"}
            
        data = {
            "title": title,
            "body": params.get("body", ""),
            "assignees": params.get("assignees", []),
            "labels": params.get("labels", [])
        }
        
        endpoint = f"repos/{owner}/{repo}/issues"
        
        return await self._make_request("POST", endpoint, data=data)
    
    async def _list_issues(self, params: Dict[str, Any]) -> Dict[str, Any]:
        owner = params.get("owner")
        repo = params.get("repo")
        
        if not owner or not repo:
            return {"success": False, "error": "Owner and repo parameters are required"}
            
        query_params = {
            "state": params.get("state", "open"),
            "per_page": params.get("per_page", 30),
            "page": params.get("page", 1),
            "sort": params.get("sort", "created"),
            "direction": params.get("direction", "desc")
        }
        
        endpoint = f"repos/{owner}/{repo}/issues"
        
        return await self._make_request("GET", endpoint, params=query_params)
    
    async def _create_pull_request(self, params: Dict[str, Any]) -> Dict[str, Any]:
        owner = params.get("owner")
        repo = params.get("repo")
        title = params.get("title")
        head = params.get("head")
        base = params.get("base")
        
        if not owner or not repo or not title or not head or not base:
            return {"success": False, "error": "Owner, repo, title, head, and base parameters are required"}
            
        data = {
            "title": title,
            "head": head,
            "base": base,
            "body": params.get("body", "")
        }
        
        endpoint = f"repos/{owner}/{repo}/pulls"
        
        return await self._make_request("POST", endpoint, data=data)
    
    async def _list_pull_requests(self, params: Dict[str, Any]) -> Dict[str, Any]:
        owner = params.get("owner")
        repo = params.get("repo")
        
        if not owner or not repo:
            return {"success": False, "error": "Owner and repo parameters are required"}
            
        query_params = {
            "state": params.get("state", "open"),
            "per_page": params.get("per_page", 30),
            "page": params.get("page", 1),
            "sort": params.get("sort", "created"),
            "direction": params.get("direction", "desc")
        }
        
        endpoint = f"repos/{owner}/{repo}/pulls"
        
        return await self._make_request("GET", endpoint, params=query_params)
    
    async def _get_repository_contents(self, params: Dict[str, Any]) -> Dict[str, Any]:
        owner = params.get("owner")
        repo = params.get("repo")
        path = params.get("path", "")
        
        if not owner or not repo:
            return {"success": False, "error": "Owner and repo parameters are required"}
            
        endpoint = f"repos/{owner}/{repo}/contents/{path}"
        
        return await self._make_request("GET", endpoint)
    
    async def _create_webhook(self, params: Dict[str, Any]) -> Dict[str, Any]:
        owner = params.get("owner")
        repo = params.get("repo")
        url = params.get("url")
        secret = params.get("secret")
        
        if not owner or not repo or not url:
            return {"success": False, "error": "Owner, repo, and url parameters are required"}
            
        data = {
            "name": "web",
            "active": True,
            "events": params.get("events", ["push", "pull_request"]),
            "config": {
                "url": url,
                "content_type": "json",
                "insecure_ssl": "0"
            }
        }
        
        if secret:
            data["config"]["secret"] = secret
            
        endpoint = f"repos/{owner}/{repo}/hooks"
        
        return await self._make_request("POST", endpoint, data=data)
    
    async def handle_webhook(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        event_type = payload.get("event_type")
        
        if not event_type:
            return {"success": False, "error": "Event type is required"}
            
        # Process different webhook events
        if event_type == "push":
            return await self._handle_push_event(payload)
        elif event_type == "pull_request":
            return await self._handle_pull_request_event(payload)
        elif event_type == "issues":
            return await self._handle_issues_event(payload)
        else:
            return {"success": True, "message": f"Event type {event_type} not handled"}
    
    async def _handle_push_event(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        # Process push event
        return {"success": True, "message": "Push event processed"}
    
    async def _handle_pull_request_event(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        # Process pull request event
        return {"success": True, "message": "Pull request event processed"}
    
    async def _handle_issues_event(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        # Process issues event
        return {"success": True, "message": "Issues event processed"}


# Register the integration
registry.register_integration("github", GitHubIntegration)
