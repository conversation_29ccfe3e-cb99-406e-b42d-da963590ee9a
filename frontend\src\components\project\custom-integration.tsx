import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';

// Custom Integration Icon component
export function CustomIntegrationIcon({ className = "", size = 40 }: { className?: string, size?: number }) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <rect
        x="2"
        y="2"
        width="9"
        height="9"
        rx="2"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <rect
        x="13"
        y="2"
        width="9"
        height="9"
        rx="2"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <rect
        x="13"
        y="13"
        width="9"
        height="9"
        rx="2"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.5 13V17.5H11"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.5 6.5L17.5 6.51"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.5 17.5L17.5 17.51"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.5 6.5L6.5 6.51"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

interface CustomIntegrationProps {
  onSave: (integrationData: CustomIntegrationData) => void;
  onCancel: () => void;
}

export interface CustomIntegrationData {
  name: string;
  baseUrl: string;
  authType: 'none' | 'api_key' | 'oauth' | 'basic';
  apiKey?: string;
  username?: string;
  password?: string;
  description?: string;
  headers?: { key: string; value: string }[];
  requiresAuthentication: boolean;
}

export function CustomIntegration({ onSave, onCancel }: CustomIntegrationProps) {
  const [integrationData, setIntegrationData] = useState<CustomIntegrationData>({
    name: '',
    baseUrl: '',
    authType: 'none',
    description: '',
    requiresAuthentication: false,
    headers: [{ key: '', value: '' }]
  });

  const handleChange = (field: keyof CustomIntegrationData, value: any) => {
    setIntegrationData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addHeader = () => {
    setIntegrationData(prev => ({
      ...prev,
      headers: [...(prev.headers || []), { key: '', value: '' }]
    }));
  };

  const updateHeader = (index: number, field: 'key' | 'value', value: string) => {
    const updatedHeaders = [...(integrationData.headers || [])];
    updatedHeaders[index] = {
      ...updatedHeaders[index],
      [field]: value
    };

    setIntegrationData(prev => ({
      ...prev,
      headers: updatedHeaders
    }));
  };

  const removeHeader = (index: number) => {
    const updatedHeaders = [...(integrationData.headers || [])];
    updatedHeaders.splice(index, 1);

    setIntegrationData(prev => ({
      ...prev,
      headers: updatedHeaders
    }));
  };

  const handleSubmit = () => {
    // Validate required fields
    if (!integrationData.name || !integrationData.baseUrl) {
      // Show error or handle validation
      return;
    }

    onSave(integrationData);
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-2">Create Custom Integration</h3>
        <p className="text-sm text-muted-foreground">
          Configure a custom API endpoint for your agents to use
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="integration-name">Integration Name</Label>
          <Input
            id="integration-name"
            placeholder="My API Integration"
            value={integrationData.name}
            onChange={(e) => handleChange('name', e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="base-url">Base URL</Label>
          <Input
            id="base-url"
            placeholder="https://api.example.com"
            value={integrationData.baseUrl}
            onChange={(e) => handleChange('baseUrl', e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description (Optional)</Label>
          <Textarea
            id="description"
            placeholder="Describe what this integration does"
            value={integrationData.description}
            onChange={(e) => handleChange('description', e.target.value)}
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="requires-auth"
            checked={integrationData.requiresAuthentication}
            onCheckedChange={(checked) => handleChange('requiresAuthentication', checked)}
          />
          <Label htmlFor="requires-auth">Requires Authentication</Label>
        </div>

        {integrationData.requiresAuthentication && (
          <div className="space-y-4 pl-6 border-l-2 border-muted mt-2">
            <div className="space-y-2">
              <Label htmlFor="auth-type">Authentication Type</Label>
              <Select
                value={integrationData.authType}
                onValueChange={(value) => handleChange('authType', value)}
              >
                <SelectTrigger id="auth-type">
                  <SelectValue placeholder="Select authentication type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="api_key">API Key</SelectItem>
                  <SelectItem value="basic">Basic Auth</SelectItem>
                  <SelectItem value="oauth">OAuth</SelectItem>
                  <SelectItem value="none">None</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {integrationData.authType === 'api_key' && (
              <div className="space-y-2">
                <Label htmlFor="api-key">API Key</Label>
                <Input
                  id="api-key"
                  type="password"
                  placeholder="Enter your API key"
                  value={integrationData.apiKey || ''}
                  onChange={(e) => handleChange('apiKey', e.target.value)}
                />
              </div>
            )}

            {integrationData.authType === 'basic' && (
              <>
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    placeholder="Username"
                    value={integrationData.username || ''}
                    onChange={(e) => handleChange('username', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Password"
                    value={integrationData.password || ''}
                    onChange={(e) => handleChange('password', e.target.value)}
                  />
                </div>
              </>
            )}
          </div>
        )}

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label>Custom Headers (Optional)</Label>
            <Button
              variant="outline"
              size="sm"
              onClick={addHeader}
              type="button"
            >
              Add Header
            </Button>
          </div>

          {integrationData.headers && integrationData.headers.map((header, index) => (
            <div key={index} className="flex gap-2 items-start">
              <Input
                placeholder="Header name"
                value={header.key}
                onChange={(e) => updateHeader(index, 'key', e.target.value)}
                className="flex-1"
              />
              <Input
                placeholder="Value"
                value={header.value}
                onChange={(e) => updateHeader(index, 'value', e.target.value)}
                className="flex-1"
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeHeader(index)}
                type="button"
                className="px-2"
              >
                ×
              </Button>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-end gap-3 pt-2">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSubmit}>
          Create Integration
        </Button>
      </div>
    </div>
  );
}
