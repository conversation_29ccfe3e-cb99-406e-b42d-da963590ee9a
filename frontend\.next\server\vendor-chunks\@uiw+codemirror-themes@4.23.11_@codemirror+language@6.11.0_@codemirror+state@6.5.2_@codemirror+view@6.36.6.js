"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uiw+codemirror-themes@4.23.11_@codemirror+language@6.11.0_@codemirror+state@6.5.2_@codemirror+view@6.36.6";
exports.ids = ["vendor-chunks/@uiw+codemirror-themes@4.23.11_@codemirror+language@6.11.0_@codemirror+state@6.5.2_@codemirror+view@6.36.6"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@uiw+codemirror-themes@4.23.11_@codemirror+language@6.11.0_@codemirror+state@6.5.2_@codemirror+view@6.36.6/node_modules/@uiw/codemirror-themes/esm/index.js":
/*!****************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@uiw+codemirror-themes@4.23.11_@codemirror+language@6.11.0_@codemirror+state@6.5.2_@codemirror+view@6.36.6/node_modules/@uiw/codemirror-themes/esm/index.js ***!
  \****************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTheme: () => (/* binding */ createTheme),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _codemirror_view__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @codemirror/view */ \"(ssr)/./node_modules/.pnpm/@codemirror+view@6.36.6/node_modules/@codemirror/view/dist/index.js\");\n/* harmony import */ var _codemirror_language__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @codemirror/language */ \"(ssr)/./node_modules/.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js\");\n\n\nvar createTheme = _ref => {\n  var {\n    theme,\n    settings = {},\n    styles = []\n  } = _ref;\n  var themeOptions = {\n    '.cm-gutters': {}\n  };\n  var baseStyle = {};\n  if (settings.background) {\n    baseStyle.backgroundColor = settings.background;\n  }\n  if (settings.backgroundImage) {\n    baseStyle.backgroundImage = settings.backgroundImage;\n  }\n  if (settings.foreground) {\n    baseStyle.color = settings.foreground;\n  }\n  if (settings.fontSize) {\n    baseStyle.fontSize = settings.fontSize;\n  }\n  if (settings.background || settings.foreground) {\n    themeOptions['&'] = baseStyle;\n  }\n  if (settings.fontFamily) {\n    themeOptions['&.cm-editor .cm-scroller'] = {\n      fontFamily: settings.fontFamily\n    };\n  }\n  if (settings.gutterBackground) {\n    themeOptions['.cm-gutters'].backgroundColor = settings.gutterBackground;\n  }\n  if (settings.gutterForeground) {\n    themeOptions['.cm-gutters'].color = settings.gutterForeground;\n  }\n  if (settings.gutterBorder) {\n    themeOptions['.cm-gutters'].borderRightColor = settings.gutterBorder;\n  }\n  if (settings.caret) {\n    themeOptions['.cm-content'] = {\n      caretColor: settings.caret\n    };\n    themeOptions['.cm-cursor, .cm-dropCursor'] = {\n      borderLeftColor: settings.caret\n    };\n  }\n  var activeLineGutterStyle = {};\n  if (settings.gutterActiveForeground) {\n    activeLineGutterStyle.color = settings.gutterActiveForeground;\n  }\n  if (settings.lineHighlight) {\n    themeOptions['.cm-activeLine'] = {\n      backgroundColor: settings.lineHighlight\n    };\n    activeLineGutterStyle.backgroundColor = settings.lineHighlight;\n  }\n  themeOptions['.cm-activeLineGutter'] = activeLineGutterStyle;\n  if (settings.selection) {\n    themeOptions['&.cm-focused .cm-selectionBackground, & .cm-line::selection, & .cm-selectionLayer .cm-selectionBackground, .cm-content ::selection'] = {\n      background: settings.selection + ' !important'\n    };\n  }\n  if (settings.selectionMatch) {\n    themeOptions['& .cm-selectionMatch'] = {\n      backgroundColor: settings.selectionMatch\n    };\n  }\n  var themeExtension = _codemirror_view__WEBPACK_IMPORTED_MODULE_0__.EditorView.theme(themeOptions, {\n    dark: theme === 'dark'\n  });\n  var highlightStyle = _codemirror_language__WEBPACK_IMPORTED_MODULE_1__.HighlightStyle.define(styles);\n  var extension = [themeExtension, (0,_codemirror_language__WEBPACK_IMPORTED_MODULE_1__.syntaxHighlighting)(highlightStyle)];\n  return extension;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (createTheme);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@uiw+codemirror-themes@4.23.11_@codemirror+language@6.11.0_@codemirror+state@6.5.2_@codemirror+view@6.36.6/node_modules/@uiw/codemirror-themes/esm/index.js\n");

/***/ })

};
;