"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send, Paperclip, Smile } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ChatInputArea } from '@/components/ui/chat/ChatInputArea';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface ChatPanelProps {
  participants: Array<{
    id: string;
    name: string;
    avatar: string;
    role: string;
  }>;
}

// Demo messages
const initialMessages = [
  {
    id: '1',
    sender: { id: '1', name: '<PERSON> (CEO)', avatar: '/roles/kenard.png' },
    content: 'Hey team, let\'s discuss the new product launch strategy',
    timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
  },
  {
    id: '2',
    sender: { id: '2', name: '<PERSON> (Developer)', avatar: '/roles/alex.png' },
    content: 'I\'ve finished the backend implementation for the new features',
    timestamp: new Date(Date.now() - 1000 * 60 * 10).toISOString(),
  },
  {
    id: '3',
    sender: { id: '3', name: 'Morgan (Marketing)', avatar: '/roles/morgan.png' },
    content: 'The social media campaign is ready to go live next week',
    timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString(),
  },
  {
    id: '4',
    sender: { id: '4', name: 'Taylor (Product)', avatar: '/roles/taylor.png' },
    content: 'User testing results are promising. 90% of participants rated the new UI as "excellent" or "very good"',
    timestamp: new Date(Date.now() - 1000 * 60 * 2).toISOString(),
  },
];

export function ChatPanel({ participants }: ChatPanelProps) {
  const [messages, setMessages] = useState(initialMessages);
  const [newMessage, setNewMessage] = useState('');
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [messages]);

  const handleSendMessage = (messageContent?: string) => {
    const content = messageContent || newMessage;
    if (!content.trim()) return;
    
    const message = {
      id: Date.now().toString(),
      sender: { id: 'user', name: 'You', avatar: '/demo-assets/user-avatar.png' },
      content: content,
      timestamp: new Date().toISOString(),
    };
    
    setMessages([...messages, message]);
    setNewMessage('');
    
    // Simulate a response after a short delay
    setTimeout(() => {
      const randomParticipant = participants[Math.floor(Math.random() * participants.length)];
      
      const responseMessage = {
        id: (Date.now() + 1).toString(),
        sender: { 
          id: randomParticipant.id, 
          name: randomParticipant.name, 
          avatar: randomParticipant.avatar 
        },
        content: getRandomResponse(randomParticipant.role),
        timestamp: new Date().toISOString(),
      };
      
      setMessages(prev => [...prev, responseMessage]);
    }, 1500 + Math.random() * 2000);
  };

  const getRandomResponse = (role: string) => {
    const responses: Record<string, string[]> = {
      'CEO': [
        'Great point! Let\'s prioritize this for Q3.',
        'I think we should focus on user acquisition first.',
        'Let\'s schedule a follow-up meeting to discuss this in detail.',
      ],
      'Developer': [
        'I can implement that feature by next sprint.',
        'We might need to refactor some code before adding that.',
        'The technical debt on that component is pretty high.',
      ],
      'Marketing': [
        'Our social engagement is up 32% this month!',
        'We should highlight this in our next campaign.',
        'I\'ll create some content around this idea.',
      ],
      'Product': [
        'User testing shows this is a high-priority feature.',
        'Let\'s add this to the roadmap for next quarter.',
        'The analytics show users are struggling with this flow.',
      ],
      'Research': [
        'Our research indicates this is a growing market segment.',
        'The data suggests we should pivot our approach here.',
        'I\'ll prepare a detailed analysis on this topic.',
      ],
      'Design': [
        'I can create some mockups for this by tomorrow.',
        'This would require a significant UI change.',
        'Let\'s do a design sprint to explore this concept.',
      ],
    };
    
    const roleResponses = responses[role] || responses['CEO'];
    return roleResponses[Math.floor(Math.random() * roleResponses.length)];
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-2 border-b">
        <h2 className="text-base font-semibold">Chat</h2>
      </div>
      
      <div className="flex-1 overflow-hidden" ref={scrollAreaRef}>
        <ScrollArea className="h-full p-3">
          <div className="space-y-3">
            {messages.map(message => (
              <div key={message.id} className="flex gap-2">
                <Avatar className="h-7 w-7 flex-shrink-0">
                  <AvatarImage src={message.sender.avatar} alt={message.sender.name} />
                  <AvatarFallback>{message.sender.name[0]}</AvatarFallback>
                </Avatar>
                
                <div className="flex-1 space-y-0.5">
                  <div className="flex items-baseline justify-between">
                    <p className="text-xs font-medium">{message.sender.name}</p>
                    <span className="text-[10px] text-muted-foreground">{formatTime(message.timestamp)}</span>
                  </div>
                  <p className="text-xs">{message.content}</p>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* Message input */}
      <ChatInputArea
        onSendMessage={handleSendMessage}
        placeholder="Type a message..."
      />
    </div>
  );
}