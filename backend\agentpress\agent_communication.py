"""
Agent Communication Module for AgentPress.

This module provides functionality for agent-to-agent communication, enabling
teams of AGI agents to collaborate effectively.

Features:
- Message routing between agents
- Agent discovery and capability negotiation
- Structured communication patterns for task delegation and information exchange
- Message history tracking
- Task status tracking
- Role-specific communication guidance
"""

import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from enum import Enum

from utils.logger import logger
from services.supabase import DBConnection


class MessageType(Enum):
    """Types of messages that can be exchanged between agents."""
    QUERY = "query"           # Request for information
    RESPONSE = "response"     # Response to a query
    NOTIFICATION = "notification"  # Informational update
    TASK = "task"             # Task assignment
    RESULT = "result"         # Task result
    STATUS = "status"         # Status update
    ERROR = "error"           # Error notification


class AgentCommunicationManager:
    """Manages communication between agents.

    This class provides methods for sending and receiving messages between
    agents, discovering agent capabilities, and tracking message history.

    Attributes:
        db: Database connection for persisting messages
    """

    def __init__(self, project_id: Optional[str] = None):
        """Initialize the AgentCommunicationManager.

        Args:
            project_id: Optional ID of the project this manager is associated with
        """
        self.db = DBConnection()
        self.project_id = project_id
        logger.debug(f"Initialized AgentCommunicationManager for project {project_id}")

    async def get_project_agents(self, project_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get the list of agents available for a project.

        Args:
            project_id: Optional ID of the project to get agents for.
                        If not provided, uses the project_id from initialization.

        Returns:
            List of agent objects with their details
        """
        try:
            # Use provided project_id or fall back to the one from initialization
            project_id = project_id or self.project_id

            if not project_id:
                logger.warning("No project_id provided for get_project_agents")
                return []

            client = await self.db.client

            # Call the database function to get project agents
            result = await client.rpc(
                'get_project_agents',
                {'p_project_id': project_id}
            ).execute()

            if result.data:
                agents = result.data
                logger.debug(f"Retrieved {len(agents)} agents for project {project_id}")
                return agents
            else:
                logger.warning(f"No agents found for project {project_id}")
                return []

        except Exception as e:
            logger.error(f"Error getting project agents: {str(e)}", exc_info=True)
            return []

    async def can_agents_communicate(
        self,
        from_agent_id: str,
        to_agent_id: str
    ) -> bool:
        """Check if two agents can communicate with each other.

        Agents can communicate if they are both active in the same project.

        Args:
            from_agent_id: ID of the sending agent
            to_agent_id: ID of the receiving agent

        Returns:
            Whether the agents can communicate
        """
        try:
            client = await self.db.client

            # Call the database function to check if agents can communicate
            result = await client.rpc(
                'can_agents_communicate',
                {
                    'p_from_agent_id': from_agent_id,
                    'p_to_agent_id': to_agent_id
                }
            ).execute()

            if result.data:
                can_communicate = result.data
                logger.debug(f"Agents {from_agent_id} and {to_agent_id} can communicate: {can_communicate}")
                return can_communicate
            else:
                logger.warning(f"Could not determine if agents {from_agent_id} and {to_agent_id} can communicate")
                return False

        except Exception as e:
            logger.error(f"Error checking if agents can communicate: {str(e)}", exc_info=True)
            return False

    async def send_message(
        self,
        from_agent_id: str,
        to_agent_id: str,
        message_type: MessageType,
        content: Dict[str, Any],
        thread_id: Optional[str] = None,
        reference_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Send a message from one agent to another.

        Args:
            from_agent_id: ID of the sending agent
            to_agent_id: ID of the receiving agent
            message_type: Type of message being sent
            content: Message content
            thread_id: Optional thread ID for conversation tracking
            reference_id: Optional reference to another message
            metadata: Optional additional metadata

        Returns:
            ID of the created message

        Raises:
            ValueError: If the agents cannot communicate with each other
        """
        try:
            # Check if agents can communicate
            if self.project_id:
                can_communicate = await self.can_agents_communicate(from_agent_id, to_agent_id)
                if not can_communicate:
                    error_msg = f"Agents {from_agent_id} and {to_agent_id} cannot communicate - they are not both active in the same project"
                    logger.error(error_msg)
                    raise ValueError(error_msg)

            # Generate a new message ID
            message_id = str(uuid.uuid4())

            # Create thread ID if not provided
            if not thread_id:
                thread_id = str(uuid.uuid4())

            # Prepare message data
            message_data = {
                "message_id": message_id,
                "thread_id": thread_id,
                "from_agent_id": from_agent_id,
                "to_agent_id": to_agent_id,
                "message_type": message_type.value,
                "content": content,
                "reference_id": reference_id,
                "metadata": metadata or {},
                "created_at": datetime.now(timezone.utc).isoformat(),
                "read": False
            }

            # Save message to database
            client = await self.db.client
            await client.from_("agent_messages").insert(message_data).execute()

            logger.info(f"Sent message from {from_agent_id} to {to_agent_id} with type {message_type.value}")
            return message_id

        except Exception as e:
            logger.error(f"Error sending message: {str(e)}", exc_info=True)
            raise

    async def get_messages(
        self,
        agent_id: str,
        thread_id: Optional[str] = None,
        unread_only: bool = False,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get messages for an agent.

        Args:
            agent_id: ID of the agent to get messages for
            thread_id: Optional thread ID to filter by
            unread_only: Whether to return only unread messages
            limit: Maximum number of messages to return
            offset: Offset for pagination

        Returns:
            List of message objects
        """
        try:
            client = await self.db.client
            query = client.from_("agent_messages").select("*").eq("to_agent_id", agent_id)

            # Apply filters
            if thread_id:
                query = query.eq("thread_id", thread_id)

            if unread_only:
                query = query.eq("read", False)

            # Apply pagination
            query = query.order("created_at", desc=True).limit(limit).offset(offset)

            # Execute query
            result = await query.execute()
            messages = result.data

            logger.debug(f"Retrieved {len(messages)} messages for agent {agent_id}")
            return messages

        except Exception as e:
            logger.error(f"Error getting messages: {str(e)}", exc_info=True)
            return []

    async def mark_as_read(self, message_id: str) -> bool:
        """Mark a message as read.

        Args:
            message_id: ID of the message to mark as read

        Returns:
            Whether the operation was successful
        """
        try:
            client = await self.db.client
            await client.from_("agent_messages").update({"read": True}).eq("message_id", message_id).execute()

            logger.debug(f"Marked message {message_id} as read")
            return True

        except Exception as e:
            logger.error(f"Error marking message as read: {str(e)}", exc_info=True)
            return False

    async def get_agent_capabilities(self, agent_id: str) -> Dict[str, Any]:
        """Get the capabilities of an agent.

        Args:
            agent_id: ID of the agent to get capabilities for

        Returns:
            Dictionary of agent capabilities
        """
        try:
            client = await self.db.client
            result = await client.from_("agents").select("capabilities").eq("agent_id", agent_id).execute()

            if result.data and len(result.data) > 0:
                capabilities = result.data[0].get("capabilities", {})
                logger.debug(f"Retrieved capabilities for agent {agent_id}")
                return capabilities
            else:
                logger.warning(f"No capabilities found for agent {agent_id}")
                return {}

        except Exception as e:
            logger.error(f"Error getting agent capabilities: {str(e)}", exc_info=True)
            return {}

    async def register_agent_capabilities(self, agent_id: str, capabilities: Dict[str, Any]) -> bool:
        """Register or update an agent's capabilities.

        Args:
            agent_id: ID of the agent to register capabilities for
            capabilities: Dictionary of agent capabilities

        Returns:
            Whether the operation was successful
        """
        try:
            client = await self.db.client

            # Check if agent exists
            check_result = await client.from_("agents").select("agent_id").eq("agent_id", agent_id).execute()

            if check_result.data and len(check_result.data) > 0:
                # Update existing agent
                await client.from_("agents").update({"capabilities": capabilities}).eq("agent_id", agent_id).execute()
                logger.info(f"Updated capabilities for agent {agent_id}")
            else:
                # Create new agent
                await client.from_("agents").insert({
                    "agent_id": agent_id,
                    "capabilities": capabilities,
                    "created_at": datetime.now(timezone.utc).isoformat()
                }).execute()
                logger.info(f"Registered new agent {agent_id} with capabilities")

            return True

        except Exception as e:
            logger.error(f"Error registering agent capabilities: {str(e)}", exc_info=True)
            return False

    async def find_agents_by_capability(self, capability: str) -> List[str]:
        """Find agents that have a specific capability.

        Args:
            capability: Capability to search for

        Returns:
            List of agent IDs that have the capability
        """
        try:
            client = await self.db.client

            # This is a simplified query - in a real implementation, you would need
            # to use a more sophisticated query to search within the capabilities JSONB
            result = await client.from_("agents").select("agent_id").execute()

            matching_agents = []
            for agent in result.data:
                agent_id = agent.get("agent_id")
                capabilities = await self.get_agent_capabilities(agent_id)

                # Check if the agent has the requested capability
                if capability in capabilities:
                    matching_agents.append(agent_id)

            logger.debug(f"Found {len(matching_agents)} agents with capability {capability}")
            return matching_agents

        except Exception as e:
            logger.error(f"Error finding agents by capability: {str(e)}", exc_info=True)
            return []

    async def get_conversation_history(
        self,
        agent_id: str,
        other_agent_id: str,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get the conversation history between two agents.

        Args:
            agent_id: ID of the first agent
            other_agent_id: ID of the second agent
            limit: Maximum number of messages to return
            offset: Offset for pagination

        Returns:
            List of message objects in chronological order
        """
        try:
            client = await self.db.client

            # Get messages where either agent is the sender and the other is the receiver
            query = client.from_("agent_messages").select("*").or_(
                f"from_agent_id.eq.{agent_id},to_agent_id.eq.{other_agent_id}",
                f"from_agent_id.eq.{other_agent_id},to_agent_id.eq.{agent_id}"
            )

            # Apply pagination
            query = query.order("created_at").limit(limit).offset(offset)

            # Execute query
            result = await query.execute()
            messages = result.data

            logger.debug(f"Retrieved {len(messages)} messages between agents {agent_id} and {other_agent_id}")
            return messages

        except Exception as e:
            logger.error(f"Error getting conversation history: {str(e)}", exc_info=True)
            return []

    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """Get the status of a task.

        Args:
            task_id: ID of the task to get status for

        Returns:
            Task status information
        """
        try:
            client = await self.db.client

            # Find the latest message related to this task
            query = client.from_("agent_messages").select("*").or_(
                f"content->>'task_id'.eq.{task_id}",
                f"metadata->>'task_id'.eq.{task_id}"
            ).order("created_at", desc=True).limit(1)

            result = await query.execute()

            if result.data and len(result.data) > 0:
                message = result.data[0]
                content = message.get("content", {})

                # Extract task status from the message content
                if "task_response" in content:
                    status = content.get("task_response", {}).get("status")
                    return {
                        "task_id": task_id,
                        "status": status,
                        "last_update": message.get("created_at"),
                        "from_agent_id": message.get("from_agent_id"),
                        "to_agent_id": message.get("to_agent_id"),
                        "message": content.get("task_response", {}).get("message")
                    }
                elif "task" in content:
                    return {
                        "task_id": task_id,
                        "status": content.get("task", {}).get("status"),
                        "last_update": message.get("created_at"),
                        "from_agent_id": message.get("from_agent_id"),
                        "to_agent_id": message.get("to_agent_id"),
                        "title": content.get("task", {}).get("title"),
                        "description": content.get("task", {}).get("description")
                    }

            logger.warning(f"No task found with ID {task_id}")
            return {"task_id": task_id, "status": "unknown"}

        except Exception as e:
            logger.error(f"Error getting task status: {str(e)}", exc_info=True)
            return {"task_id": task_id, "status": "error", "error": str(e)}

    async def get_agent_tasks(
        self,
        agent_id: str,
        status: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get tasks assigned to or created by an agent.

        Args:
            agent_id: ID of the agent
            status: Optional status to filter by
            limit: Maximum number of tasks to return
            offset: Offset for pagination

        Returns:
            List of task objects
        """
        try:
            client = await self.db.client

            # Get messages where the agent is the receiver and the message type is TASK
            query = client.from_("agent_messages").select("*").eq("to_agent_id", agent_id).eq("message_type", "task")

            # Apply status filter if provided
            if status:
                query = query.filter("content->>'status'", "eq", status)

            # Apply pagination
            query = query.order("created_at", desc=True).limit(limit).offset(offset)

            # Execute query
            result = await query.execute()
            task_messages = result.data

            # Extract task information from messages
            tasks = []
            for message in task_messages:
                content = message.get("content", {})
                if "task" in content:
                    task = content.get("task", {})
                    tasks.append({
                        "task_id": message.get("message_id"),
                        "title": task.get("title"),
                        "description": task.get("description"),
                        "status": task.get("status"),
                        "priority": task.get("priority"),
                        "due_date": task.get("due_date"),
                        "from_agent_id": message.get("from_agent_id"),
                        "created_at": message.get("created_at")
                    })

            logger.debug(f"Retrieved {len(tasks)} tasks for agent {agent_id}")
            return tasks

        except Exception as e:
            logger.error(f"Error getting agent tasks: {str(e)}", exc_info=True)
            return []

    async def get_agent_info(self, agent_id: str) -> Dict[str, Any]:
        """Get information about an agent.

        Args:
            agent_id: ID of the agent

        Returns:
            Agent information
        """
        try:
            client = await self.db.client

            # Get agent information
            result = await client.from_("agents").select("*").eq("agent_id", agent_id).execute()

            if result.data and len(result.data) > 0:
                agent_info = result.data[0]

                # Get project information for this agent
                project_agent_result = await client.from_("project_agents").select("*,agent_type_id").eq("agent_id", agent_id).execute()

                if project_agent_result.data and len(project_agent_result.data) > 0:
                    project_agent = project_agent_result.data[0]
                    agent_type_id = project_agent.get("agent_type_id")

                    # Get agent type information
                    agent_type_result = await client.from_("agent_types").select("*").eq("agent_type_id", agent_type_id).execute()

                    if agent_type_result.data and len(agent_type_result.data) > 0:
                        agent_type = agent_type_result.data[0]

                        # Combine information
                        return {
                            "agent_id": agent_id,
                            "name": agent_info.get("name"),
                            "description": agent_info.get("description"),
                            "capabilities": agent_info.get("capabilities", {}),
                            "role": agent_type.get("role"),
                            "project_id": project_agent.get("project_id"),
                            "is_active": project_agent.get("is_active", True),
                            "created_at": agent_info.get("created_at")
                        }

                # Fall back to just agent information if project/type info not available
                return {
                    "agent_id": agent_id,
                    "name": agent_info.get("name"),
                    "description": agent_info.get("description"),
                    "capabilities": agent_info.get("capabilities", {}),
                    "created_at": agent_info.get("created_at")
                }

            logger.warning(f"No agent found with ID {agent_id}")
            return {"agent_id": agent_id, "status": "not_found"}

        except Exception as e:
            logger.error(f"Error getting agent info: {str(e)}", exc_info=True)
            return {"agent_id": agent_id, "status": "error", "error": str(e)}
