"use client";

import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Code, FileText, Users } from 'lucide-react';
import { TemplateCard } from '@/components/project/template-card';

interface ProjectDetailsProps {
  data: {
    name: string;
    description: string;
    companyInfo: string;
    templateId: string;
  };
  updateData: (data: Partial<{
    name: string;
    description: string;
    companyInfo: string;
    templateId: string;
  }>) => void;
}

const templates = [
  {
    id: 'ai-developer',
    name: 'AI Developer Team',
    description: 'Build technical solutions and development projects',
    icon: <Code className="h-6 w-6 text-primary" />,
  },
  {
    id: 'content-creation',
    name: 'Content Creation',
    description: 'Create marketing content and social media campaigns',
    icon: <FileText className="h-6 w-6 text-primary" />,
  },
  {
    id: 'custom-team',
    name: 'Custom Team',
    description: 'Build your project with a fully customized team',
    icon: <Users className="h-6 w-6 text-primary" />,
  },
];

export function ProjectDetails({ data, updateData }: ProjectDetailsProps) {
  const handleSelectTemplate = (templateId: string) => {
    updateData({ templateId });
  };

  return (
    <div className="bg-background">
      {/* Header moved to parent component */}

      <div className="flex flex-col space-y-8">
        <div className="space-y-6 w-full">
          <div>
            <Label htmlFor="projectName" className="text-sm font-medium block mb-2">Project Name</Label>
            <Input
              id="projectName"
              placeholder="e.g., Marketing Campaign Builder"
              value={data.name}
              onChange={(e) => updateData({ name: e.target.value })}
              className="w-full"
            />
          </div>

          <div>
            <Label htmlFor="projectDescription" className="text-sm font-medium block mb-2">Project Goals</Label>
            <Textarea
              id="projectDescription"
              placeholder="Describe what you want to accomplish with this project"
              className="min-h-[120px] w-full"
              value={data.description}
              onChange={(e) => updateData({ description: e.target.value })}
            />
          </div>

          <div>
            <div className="flex items-center gap-1 mb-2">
              <Label htmlFor="companyInfo" className="text-sm font-medium">Company Background</Label>
              <span className="text-xs bg-muted text-muted-foreground px-1.5 py-0.5 rounded-sm">Optional</span>
            </div>
            <Textarea
              id="companyInfo"
              placeholder="Tell us about your company, industry, and target market"
              className="min-h-[120px] w-full"
              value={data.companyInfo}
              onChange={(e) => updateData({ companyInfo: e.target.value })}
            />
          </div>
        </div>

        <div className="mt-8">
          <Label className="text-xl font-semibold block mb-4">Choose a Starting Template</Label>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {templates.map((template) => (
              <TemplateCard
                key={template.id}
                id={template.id}
                name={template.name}
                description={template.description}
                icon={template.icon}
                selected={data.templateId === template.id}
                onClick={() => handleSelectTemplate(template.id)}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
