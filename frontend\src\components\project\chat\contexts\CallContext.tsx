'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { CallParticipant } from '../types';

interface CallContextType {
  // Call state
  isCallActive: boolean;
  callType: 'audio' | 'video';
  callParticipants: CallParticipant[];
  isCallMinimized: boolean;
  callStartTime: Date | null;
  
  // Call actions
  startCall: (type: 'audio' | 'video', participants: CallParticipant[]) => void;
  endCall: () => void;
  minimizeCall: () => void;
  maximizeCall: () => void;
}

const CallContext = createContext<CallContextType | undefined>(undefined);

interface CallProviderProps {
  children: ReactNode;
}

export function CallProvider({ children }: CallProviderProps) {
  const [isCallActive, setIsCallActive] = useState(false);
  const [callType, setCallType] = useState<'audio' | 'video'>('audio');
  const [callParticipants, setCallParticipants] = useState<CallParticipant[]>([]);
  const [isCallMinimized, setIsCallMinimized] = useState(false);
  const [callStartTime, setCallStartTime] = useState<Date | null>(null);

  const startCall = (type: 'audio' | 'video', participants: CallParticipant[]) => {
    setCallType(type);
    setIsCallActive(true);
    setCallParticipants(participants);
    setCallStartTime(new Date());
    setIsCallMinimized(false);
  };

  const endCall = () => {
    setIsCallActive(false);
    setIsCallMinimized(false);
    setCallParticipants([]);
    setCallStartTime(null);
  };

  const minimizeCall = () => {
    setIsCallMinimized(true);
  };

  const maximizeCall = () => {
    setIsCallMinimized(false);
  };

  const value: CallContextType = {
    isCallActive,
    callType,
    callParticipants,
    isCallMinimized,
    callStartTime,
    startCall,
    endCall,
    minimizeCall,
    maximizeCall,
  };

  return (
    <CallContext.Provider value={value}>
      {children}
    </CallContext.Provider>
  );
}

export function useCallContext() {
  const context = useContext(CallContext);
  if (context === undefined) {
    throw new Error('useCallContext must be used within a CallProvider');
  }
  return context;
}
