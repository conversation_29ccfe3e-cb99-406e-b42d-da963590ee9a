'use client';

import React from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface AvailableAgentCardProps {
  agent: {
    id: string;
    name: string;
    role: string;
    imageSrc: string;
    description: string;
    color: string;
    email?: string;
  };
  onAdd: (id: string) => void;
}

export function AvailableAgentCard({ agent, onAdd }: AvailableAgentCardProps) {
  return (
    <div className="p-4 border border-[#333333] rounded-md bg-[#1a1a1a]">
      <div className="flex items-center gap-3 mb-3">
        <div 
          className="w-10 h-10 rounded-md overflow-hidden flex-shrink-0" 
          style={{ backgroundColor: agent.color }}
        >
          <Image
            src={agent.imageSrc}
            alt={agent.name}
            width={40}
            height={40}
            className="w-full h-full object-cover"
          />
        </div>
        <div>
          <div className="flex items-center gap-2">
            <span className="text-white font-medium">{agent.name}</span>
            <Badge variant="secondary" className="bg-[#333333] text-[#999999] text-xs">
              {agent.role}
            </Badge>
          </div>
          {agent.email && (
            <div className="text-[#999999] text-sm">{agent.email}</div>
          )}
        </div>
      </div>
      <p className="text-[#999999] text-sm mb-3">{agent.description}</p>
      <Button
        size="sm"
        className="w-full border-[#2a2a2a] text-[#999999] bg-[#2a2a2a] hover:bg-transparent"
        onClick={() => onAdd(agent.id)}
      >
        Add to Team
      </Button>
    </div>
  );
}
