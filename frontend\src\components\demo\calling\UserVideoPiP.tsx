"use client";

import React, { RefObject } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface UserVideoPiPProps {
  isVideoOn: boolean;
  pipVideoRef: RefObject<HTMLVideoElement>;
}

export function UserVideoPiP({
  isVideoOn,
  pipVideoRef
}: UserVideoPiPProps) {
  return (
    <div className="absolute bottom-20 right-4 z-10 rounded-lg overflow-hidden shadow-lg border-2 border-background" style={{ width: '180px', height: '101px' }}>
      {isVideoOn ? (
        <video 
          ref={pipVideoRef}
          className="w-full h-full object-cover mirror-mode"
          autoPlay
          muted
          playsInline
        />
      ) : (
        <div className="w-full h-full flex items-center justify-center bg-muted">
          <Avatar className="h-12 w-12">
            <AvatarImage src="/demo-assets/user-avatar.png" alt="You" />
            <AvatarFallback>You</AvatarFallback>
          </Avatar>
        </div>
      )}
    </div>
  );
}