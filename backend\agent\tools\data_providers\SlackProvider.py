from typing import Dict

from agent.tools.data_providers.RapidDataProviderBase import RapidDataProviderBase, EndpointSchema


class SlackProvider(RapidDataProviderBase):
    def __init__(self):
        endpoints: Dict[str, EndpointSchema] = {
            "send_message": {
                "route": "/chat.postMessage",
                "method": "POST",
                "name": "Send Message",
                "description": "Send a message to a Slack channel or user.",
                "payload": {
                    "channel": "Channel ID or name to send message to",
                    "text": "Text of the message to send",
                    "thread_ts": "Optional thread timestamp to reply to a thread",
                    "mrkdwn": "Optional boolean to enable or disable Markdown parsing (default: true)"
                }
            },
            "send_blocks": {
                "route": "/chat.postMessage",
                "method": "POST",
                "name": "Send Blocks",
                "description": "Send a message with block elements to a Slack channel or user.",
                "payload": {
                    "channel": "Channel ID or name to send message to",
                    "blocks": "JSON array of block elements",
                    "thread_ts": "Optional thread timestamp to reply to a thread"
                }
            },
            "get_channel_history": {
                "route": "/conversations.history",
                "method": "GET",
                "name": "Channel History",
                "description": "Get message history for a channel.",
                "payload": {
                    "channel": "Channel ID to fetch history for",
                    "limit": "Optional number of messages to return (default: 100)",
                    "latest": "Optional end of time range to include messages",
                    "oldest": "Optional start of time range to include messages",
                    "inclusive": "Optional boolean to include messages with latest or oldest timestamps"
                }
            },
            "get_channel_info": {
                "route": "/conversations.info",
                "method": "GET",
                "name": "Channel Info",
                "description": "Get information about a channel.",
                "payload": {
                    "channel": "Channel ID to get info on",
                    "include_locale": "Optional boolean to receive the locale for this channel",
                    "include_num_members": "Optional boolean to include the member count for the channel"
                }
            },
            "list_channels": {
                "route": "/conversations.list",
                "method": "GET",
                "name": "List Channels",
                "description": "List all channels in a Slack team.",
                "payload": {
                    "exclude_archived": "Optional boolean to exclude archived channels",
                    "types": "Optional comma-separated list of channel types (public_channel, private_channel, mpim, im)",
                    "limit": "Optional maximum number of items to return"
                }
            },
            "get_user_info": {
                "route": "/users.info",
                "method": "GET",
                "name": "User Info",
                "description": "Get information about a user.",
                "payload": {
                    "user": "User ID to get info on",
                    "include_locale": "Optional boolean to receive the locale for this user"
                }
            },
            "list_users": {
                "route": "/users.list",
                "method": "GET",
                "name": "List Users",
                "description": "List all users in a Slack team.",
                "payload": {
                    "limit": "Optional maximum number of items to return",
                    "include_locale": "Optional boolean to receive the locale for users"
                }
            },
            "upload_file": {
                "route": "/files.upload",
                "method": "POST",
                "name": "Upload File",
                "description": "Upload a file to Slack.",
                "payload": {
                    "channels": "Comma-separated list of channel IDs where the file will be shared",
                    "content": "File contents (if not using file)",
                    "filename": "Filename of file",
                    "filetype": "Optional file type identifier",
                    "initial_comment": "Optional message text introducing the file",
                    "thread_ts": "Optional thread timestamp to share the file in a thread"
                }
            },
            "search_messages": {
                "route": "/search.messages",
                "method": "GET",
                "name": "Search Messages",
                "description": "Search for messages matching a query.",
                "payload": {
                    "query": "Search query",
                    "sort": "Optional sort direction (score or timestamp)",
                    "sort_dir": "Optional sort direction (asc or desc)",
                    "count": "Optional number of results to return per page",
                    "page": "Optional page number of results to return"
                }
            },
            "create_channel": {
                "route": "/conversations.create",
                "method": "POST",
                "name": "Create Channel",
                "description": "Create a new channel.",
                "payload": {
                    "name": "Name of the channel to create",
                    "is_private": "Optional boolean to create a private channel"
                }
            }
        }
        base_url = "https://slack.com/api"
        super().__init__(base_url, endpoints)
        
        # Override call_endpoint to handle Slack API authentication
        self.original_call_endpoint = self.call_endpoint
        self.call_endpoint = self.slack_call_endpoint
    
    def slack_call_endpoint(self, route: str, payload: Dict = None):
        """
        Custom call_endpoint method to handle Slack API authentication.
        For Slack, we need to use a different authentication method.
        """
        import os
        import requests
        
        if route.startswith("/"):
            route = route[1:]

        endpoint = self.endpoints.get(route)
        if not endpoint:
            raise ValueError(f"Endpoint {route} not found")
        
        url = f"{self.base_url}{endpoint['route']}"
        
        # Get Slack token from environment
        slack_token = os.getenv("SLACK_API_TOKEN")
        if not slack_token:
            return {"error": "SLACK_API_TOKEN environment variable not set"}
        
        headers = {
            "Authorization": f"Bearer {slack_token}",
            "Content-Type": "application/json; charset=utf-8"
        }

        method = endpoint.get('method', 'GET').upper()
        
        if method == 'GET':
            response = requests.get(url, params=payload, headers=headers)
        elif method == 'POST':
            response = requests.post(url, json=payload, headers=headers)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
            
        return response.json()


if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    tool = SlackProvider()

    # Example for sending a message
    send_message = tool.call_endpoint(
        route="send_message",
        payload={
            "channel": "general",
            "text": "Hello from the Slack Provider tool!"
        }
    )
    print("Send Message:", send_message)
    
    # Example for getting channel history
    channel_history = tool.call_endpoint(
        route="get_channel_history",
        payload={
            "channel": "C04XXXXXXX",
            "limit": 5
        }
    )
    print("Channel History:", channel_history)
    
    # Example for listing channels
    list_channels = tool.call_endpoint(
        route="list_channels",
        payload={
            "exclude_archived": True,
            "types": "public_channel,private_channel"
        }
    )
    print("List Channels:", list_channels)
    
    # Example for getting user info
    user_info = tool.call_endpoint(
        route="get_user_info",
        payload={
            "user": "U04XXXXXXX"
        }
    )
    print("User Info:", user_info)
